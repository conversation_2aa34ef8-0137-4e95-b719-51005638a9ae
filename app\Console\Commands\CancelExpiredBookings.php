<?php

namespace App\Console\Commands;

use App\Models\CourtBooking;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CancelExpiredBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:cancel-expired-bookings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tự động hủy các đơn đặt sân đã quá hạn thanh toán';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = Carbon::now();
        $this->info("Bắt đầu kiểm tra đơn đặt sân quá hạn vào: " . $now->format('Y-m-d H:i:s'));
        Log::info("Bắt đầu kiểm tra đơn đặt sân quá hạn thanh toán", ['time' => $now->format('Y-m-d H:i:s')]);

        try {
            $expiredMainBookings = Booking::where('payment_status', 'unpaid')
                ->whereIn('status', ['pending', 'pending_approval'])
                ->where('payment_deadline', '<', $now)
                ->get();

            $this->info("Tìm thấy " . count($expiredMainBookings) . " Booking chính quá hạn thanh toán");
            Log::info("Tìm thấy " . count($expiredMainBookings) . " Booking chính quá hạn thanh toán");

            $expiredCourtBookingsOnly = CourtBooking::where('payment_status', 'unpaid')
                ->whereIn('status', ['pending', 'pending_approval'])
                ->where('payment_deadline', '<', $now)
                ->whereNotIn('reference_number', $expiredMainBookings->pluck('reference_number'))
                ->get();

            $this->info("Tìm thấy " . count($expiredCourtBookingsOnly) . " CourtBooking riêng lẻ quá hạn thanh toán");
            Log::info("Tìm thấy " . count($expiredCourtBookingsOnly) . " CourtBooking riêng lẻ quá hạn thanh toán");

            $pendingMainBookings = Booking::where('payment_status', 'unpaid')
                ->whereIn('status', ['pending', 'pending_approval'])
                ->count();

            $this->info("Tổng số Booking chính chờ thanh toán: {$pendingMainBookings}");

            $bookingsWithDeadline = Booking::whereNotNull('payment_deadline')
                ->where('payment_status', 'unpaid')
                ->whereIn('status', ['pending', 'pending_approval'])
                ->get();

            $this->info("Booking chính có thời hạn thanh toán: " . count($bookingsWithDeadline));

            foreach ($bookingsWithDeadline as $booking) {
                $this->info("ID: {$booking->id}, Ref: {$booking->reference_number}, Deadline: {$booking->payment_deadline}, Now: {$now}");
            }

            $count = 0;

            foreach ($expiredMainBookings as $mainBooking) {
                $this->info("Đang hủy Booking chính: ID #{$mainBooking->id}, Mã {$mainBooking->reference_number}, Deadline: {$mainBooking->payment_deadline}");
                $mainBooking->status = 'cancelled';
                $mainBooking->payment_status = 'cancelled';
                $mainBooking->cancelled_at = $now;
                $mainBooking->cancellation_reason = 'Tự động hủy - Quá hạn thanh toán';

                $metadata = $mainBooking->metadata ?? [];
                $metadata['cancelled_at'] = $now->toDateTimeString();
                $metadata['cancellation_reason'] = 'Tự động hủy - Quá hạn thanh toán';
                $metadata['cancelled_by_system'] = true;
                $mainBooking->metadata = $metadata;

                $updatedMainBooking = $mainBooking->save() ? 1 : 0;

                $updatedCourtBookings = CourtBooking::where('reference_number', $mainBooking->reference_number)
                    ->whereIn('status', ['pending', 'pending_approval'])
                    ->update([
                        'status' => 'cancelled',
                        'payment_status' => 'cancelled',
                        'cancelled_at' => $now,
                        'cancellation_reason' => 'Tự động hủy - Quá hạn thanh toán'
                    ]);

                $count++;

                $this->info("Đã hủy: {$updatedMainBooking} Booking chính và {$updatedCourtBookings} CourtBooking(s) cho mã {$mainBooking->reference_number}");

                Log::info("Đã hủy Booking chính #{$mainBooking->id} (Mã: {$mainBooking->reference_number}) do quá hạn thanh toán", [
                    'deadline' => $mainBooking->payment_deadline,
                    'cancelled_at' => $now->format('Y-m-d H:i:s'),
                    'main_booking_updated' => $updatedMainBooking,
                    'court_bookings_updated' => $updatedCourtBookings,
                    'affected_models' => ['Booking', 'CourtBooking']
                ]);
            }

            // Xử lý CourtBooking riêng lẻ (không có Booking chính)
            foreach ($expiredCourtBookingsOnly as $courtBooking) {
                $this->info("Đang hủy CourtBooking riêng lẻ: ID #{$courtBooking->id}, Mã {$courtBooking->reference_number}, Deadline: {$courtBooking->payment_deadline}");

                // Update CourtBooking
                $courtBooking->status = 'cancelled';
                $courtBooking->payment_status = 'cancelled';
                $courtBooking->cancelled_at = $now;
                $courtBooking->cancellation_reason = 'Tự động hủy - Quá hạn thanh toán';

                $updatedCourtBooking = $courtBooking->save() ? 1 : 0;
                $count++;

                $this->info("Đã hủy: {$updatedCourtBooking} CourtBooking riêng lẻ cho mã {$courtBooking->reference_number}");

                Log::info("Đã hủy CourtBooking riêng lẻ #{$courtBooking->id} (Mã: {$courtBooking->reference_number}) do quá hạn thanh toán", [
                    'deadline' => $courtBooking->payment_deadline,
                    'cancelled_at' => $now->format('Y-m-d H:i:s'),
                    'court_booking_updated' => $updatedCourtBooking,
                    'affected_models' => ['CourtBooking']
                ]);
            }

            $this->info("Đã hủy {$count} đơn đặt sân quá hạn thanh toán");
            Log::info("Hoàn tất hủy {$count} đơn đặt sân quá hạn thanh toán");

        } catch (\Exception $e) {
            $this->error("Có lỗi xảy ra: " . $e->getMessage());
            Log::error("Lỗi khi hủy đơn đặt sân quá hạn: " . $e->getMessage(), [
                'exception' => $e
            ]);
        }
    }
}
