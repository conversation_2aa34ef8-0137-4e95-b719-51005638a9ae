<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CourtPrice;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CheckSpecialPriceExpired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'court:check-special-price-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update expired special prices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt đầu kiểm tra giá đặc biệt hết hạn...');

        try {
            $today = Carbon::now()->startOfDay();

            // Lấy tất cả giá đặc biệt có ngày nhỏ hơn ngày hiện tại và chưa được đánh dấu là hết hạn
            $expiredPrices = CourtPrice::where('price_type', 'special_date')
                ->where('special_date', '<', $today)
                ->where('status', '!=', 'expired')
                ->get();

            $count = $expiredPrices->count();

            if ($count > 0) {
                foreach ($expiredPrices as $price) {
                    $price->update([
                        'status' => 'expired',
                        'is_active' => false
                    ]);
                }

                $message = "Đã cập nhật {$count} giá đặc biệt hết hạn.";
                $this->info($message);
                Log::info($message);
            } else {
                $message = "Không có giá đặc biệt nào hết hạn.";
                $this->info($message);
                Log::info($message);
            }

            $todayPrices = CourtPrice::where('price_type', 'special_date')
                ->where('special_date', $today)
                ->where('status', '!=', 'active')
                ->get();

            if ($todayPrices->count() > 0) {
                foreach ($todayPrices as $price) {
                    $price->update([
                        'status' => 'active',
                        'is_active' => true
                    ]);
                }

                $message = "Đã kích hoạt " . $todayPrices->count() . " giá đặc biệt cho ngày hôm nay.";
                $this->info($message);
                Log::info($message);
            }

            return 0;
        } catch (\Exception $e) {
            $error = "Lỗi khi kiểm tra giá đặc biệt: " . $e->getMessage();
            $this->error($error);
            Log::error($error);
            return 1;
        }
    }
}