<?php

namespace App\Console\Commands;

use App\Models\Statistic;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupStatistics extends Command
{
    protected $signature = 'statistics:cleanup';
    protected $description = 'Xóa dữ liệu thống kê cũ dựa theo chính sách lưu trữ';

    public function handle()
    {
        $this->info('Đang dọn dẹp dữ liệu thống kê cũ...');

        // Chính sách lưu trữ:
        // - Dữ liệu ngày: lưu trữ 6 tháng gần nhất
        // - Dữ liệu tuần: lưu trữ 2 năm
        // - Dữ liệu tháng: lưu trữ lâu dài
        // - Dữ liệu năm: lưu trữ lâu dài

        // Xóa dữ liệu thống kê hàng ngày cũ hơn 6 tháng
        $sixMonthsAgo = Carbon::now()->subMonths(6)->startOfDay();
        $deletedDaily = Statistic::where('statistics_type', 'daily')
            ->where('statistics_date', '<', $sixMonthsAgo)
            ->delete();

        $this->info("Đã xóa {$deletedDaily} bản ghi thống kê hàng ngày cũ hơn 6 tháng.");

        // Xóa dữ liệu thống kê hàng tuần cũ hơn 2 năm
        $twoYearsAgo = Carbon::now()->subYears(2)->startOfDay();
        $deletedWeekly = Statistic::where('statistics_type', 'weekly')
            ->where('statistics_date', '<', $twoYearsAgo)
            ->delete();

        $this->info("Đã xóa {$deletedWeekly} bản ghi thống kê hàng tuần cũ hơn 2 năm.");

        // Dữ liệu tháng và năm được giữ lại lâu dài, không xóa

        $this->info('Hoàn tất dọn dẹp dữ liệu thống kê.');

        return 0;
    }
}
