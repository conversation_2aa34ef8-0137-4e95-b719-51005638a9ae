<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class FixPermissionCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:fix-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix permission cache issues and ensure booking:edit_branch_profile permission exists';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Clear permission cache
        $this->info('Clearing permission cache...');
        app()->make(PermissionRegistrar::class)->forgetCachedPermissions();

        // Check if permission exists
        $permissionName = 'booking:edit_branch_profile';
        $permission = Permission::where('name', $permissionName)->first();

        if (!$permission) {
            $this->info("Creating missing permission: {$permissionName}");
            // Create permission
            $permission = Permission::create([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);
        } else {
            $this->info("Permission {$permissionName} already exists with ID: {$permission->id}");
        }

        // Verify permission creation
        app()->make(PermissionRegistrar::class)->forgetCachedPermissions();
        $verifyPermission = Permission::where('name', $permissionName)->first();

        if (!$verifyPermission) {
            $this->error("Failed to create permission using Spatie model. Trying direct DB insert...");

            // Try direct DB insert as a fallback
            DB::table('permissions')->insert([
                'name' => $permissionName,
                'guard_name' => 'web',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            // Fetch again
            app()->make(PermissionRegistrar::class)->forgetCachedPermissions();
            $verifyPermission = Permission::where('name', $permissionName)->first();

            if (!$verifyPermission) {
                $this->error("Failed to create permission through direct DB insert. Check database connection.");
                return 1;
            }
        }

        $this->info("Permission verified with ID: {$verifyPermission->id}");

        // List all permissions
        $this->info('Listing all permissions:');
        $allPermissions = Permission::all();
        $headers = ['ID', 'Name', 'Guard'];
        $rows = [];

        foreach ($allPermissions as $p) {
            $rows[] = [$p->id, $p->name, $p->guard_name];
        }

        $this->table($headers, $rows);

        // Assign to main roles
        $this->info('Assigning permission to roles...');
        $roles = ['manager', 'branch_admin', 'business_admin', 'super-admin'];

        foreach ($roles as $roleName) {
            $role = Role::where('name', $roleName)->first();

            if ($role) {
                if (!$role->hasPermissionTo($permissionName)) {
                    $role->givePermissionTo($permissionName);
                    $this->info("Assigned permission to role: {$roleName}");
                } else {
                    $this->info("Role {$roleName} already has the permission");
                }
            } else {
                $this->info("Role {$roleName} not found");
            }
        }

        // Clear cache one more time
        app()->make(PermissionRegistrar::class)->forgetCachedPermissions();

        $this->info('Permission cache fixed successfully!');
        return 0;
    }
}