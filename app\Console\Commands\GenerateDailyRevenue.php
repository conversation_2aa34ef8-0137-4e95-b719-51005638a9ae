<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DailyRevenue;
use App\Models\Business;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\Payment;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class GenerateDailyRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revenue:generate-daily 
                            {--date= : The date to generate revenue for (YYYY-MM-DD). Defaults to yesterday}
                            {--start-date= : Start date for a date range (YYYY-MM-DD)}
                            {--end-date= : End date for a date range (YYYY-MM-DD)}
                            {--business-id= : Generate for a specific business ID}
                            {--branch-id= : Generate for a specific branch ID}
                            {--force : Force regeneration of existing data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate daily revenue data from bookings and payments';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->option('date');
        $startDate = $this->option('start-date');
        $endDate = $this->option('end-date');
        $businessId = $this->option('business-id');
        $branchId = $this->option('branch-id');
        $force = $this->option('force');

        // Determine the date range to process
        if ($startDate && $endDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
            $endDate = Carbon::parse($endDate)->endOfDay();
            $dateRange = CarbonPeriod::create($startDate, $endDate);
            $this->info("Generating revenue data from {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
        } elseif ($date) {
            $processDate = Carbon::parse($date);
            $dateRange = [Carbon::parse($date)];
            $this->info("Generating revenue data for {$processDate->format('Y-m-d')}");
        } else {
            // Default to yesterday
            $processDate = Carbon::yesterday();
            $dateRange = [Carbon::yesterday()];
            $this->info("Generating revenue data for yesterday ({$processDate->format('Y-m-d')})");
        }

        // Get businesses to process
        $businessQuery = Business::query();
        if ($businessId) {
            $businessQuery->where('id', $businessId);
        }
        $businesses = $businessQuery->get();

        if ($businesses->isEmpty()) {
            $this->error('No businesses found with the specified criteria.');
            return 1;
        }

        $this->info("Processing " . $businesses->count() . " businesses");

        $totalRecordsCreated = 0;
        $totalRecordsUpdated = 0;

        // Process each business
        foreach ($businesses as $business) {
            $this->info("Processing business: {$business->name}");

            // Get branches to process
            $branchQuery = Branch::where('business_id', $business->id);
            if ($branchId) {
                $branchQuery->where('id', $branchId);
            }
            $branches = $branchQuery->get();

            if ($branches->isEmpty()) {
                $this->warn("No branches found for business ID {$business->id}");
                continue;
            }

            // Process each branch
            foreach ($branches as $branch) {
                $this->info("- Processing branch: {$branch->name}");

                // Process each date in the range
                foreach ($dateRange as $currentDate) {
                    $dateFormatted = $currentDate->format('Y-m-d');
                    $this->info("  - Processing date: {$dateFormatted}");

                    // Check if data already exists and skip if not forced
                    if (
                        !$force && DailyRevenue::where('business_id', $business->id)
                            ->where('branch_id', $branch->id)
                            ->where('revenue_date', $dateFormatted)
                            ->exists()
                    ) {
                        $this->warn("    Revenue data already exists for this date. Use --force to regenerate.");
                        continue;
                    }

                    // Calculate branch-level metrics
                    $branchMetrics = $this->calculateBranchMetrics($branch->id, $dateFormatted);

                    // Create or update branch-level revenue record
                    $branchRevenue = DailyRevenue::updateOrCreate(
                        [
                            'business_id' => $business->id,
                            'branch_id' => $branch->id,
                            'court_id' => null,
                            'revenue_date' => $dateFormatted,
                        ],
                        [
                            'total_bookings' => $branchMetrics['total_bookings'],
                            'confirmed_bookings' => $branchMetrics['confirmed_bookings'],
                            'cancelled_bookings' => $branchMetrics['cancelled_bookings'],
                            'completed_bookings' => $branchMetrics['completed_bookings'],
                            'gross_revenue' => $branchMetrics['gross_revenue'],
                            'commission_amount' => $branchMetrics['commission_amount'],
                            'net_revenue' => $branchMetrics['net_revenue'],
                            'peak_hour_revenue' => $branchMetrics['peak_hour_revenue'],
                            'discount_amount' => $branchMetrics['discount_amount'],
                            'refund_amount' => $branchMetrics['refund_amount'],
                        ]
                    );

                    if ($branchRevenue->wasRecentlyCreated) {
                        $totalRecordsCreated++;
                    } else {
                        $totalRecordsUpdated++;
                    }

                    // Process each court in the branch
                    $courts = Court::where('branch_id', $branch->id)->get();
                    foreach ($courts as $court) {
                        $this->info("    - Processing court: {$court->name}");

                        // Calculate court-level metrics
                        $courtMetrics = $this->calculateCourtMetrics($court->id, $dateFormatted);

                        if ($courtMetrics['total_bookings'] > 0) {
                            // Create or update court-level revenue record
                            $courtRevenue = DailyRevenue::updateOrCreate(
                                [
                                    'business_id' => $business->id,
                                    'branch_id' => $branch->id,
                                    'court_id' => $court->id,
                                    'revenue_date' => $dateFormatted,
                                ],
                                [
                                    'total_bookings' => $courtMetrics['total_bookings'],
                                    'confirmed_bookings' => $courtMetrics['confirmed_bookings'],
                                    'cancelled_bookings' => $courtMetrics['cancelled_bookings'],
                                    'completed_bookings' => $courtMetrics['completed_bookings'],
                                    'gross_revenue' => $courtMetrics['gross_revenue'],
                                    'commission_amount' => $courtMetrics['commission_amount'],
                                    'net_revenue' => $courtMetrics['net_revenue'],
                                    'peak_hour_revenue' => $courtMetrics['peak_hour_revenue'],
                                    'discount_amount' => $courtMetrics['discount_amount'],
                                    'refund_amount' => $courtMetrics['refund_amount'],
                                ]
                            );

                            if ($courtRevenue->wasRecentlyCreated) {
                                $totalRecordsCreated++;
                            } else {
                                $totalRecordsUpdated++;
                            }
                        } else {
                            $this->info("      No bookings found for this court on {$dateFormatted}");
                        }
                    }
                }
            }
        }

        $this->info("Revenue data generation completed successfully!");
        $this->info("Records created: {$totalRecordsCreated}");
        $this->info("Records updated: {$totalRecordsUpdated}");

        return 0;
    }

    /**
     * Calculate revenue metrics for a branch on a specific date
     *
     * @param int $branchId
     * @param string $date
     * @return array
     */
    private function calculateBranchMetrics(int $branchId, string $date): array
    {
        // Get all court bookings for this branch on this date
        $bookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->where('booking_date', $date)
            ->get();

        // Initialize metrics
        $metrics = [
            'total_bookings' => $bookings->count(),
            'confirmed_bookings' => 0,
            'cancelled_bookings' => 0,
            'completed_bookings' => 0,
            'gross_revenue' => 0,
            'commission_amount' => 0,
            'net_revenue' => 0,
            'peak_hour_revenue' => 0,
            'discount_amount' => 0,
            'refund_amount' => 0,
        ];

        if ($metrics['total_bookings'] == 0) {
            return $metrics;
        }

        // Calculate booking status counts
        $metrics['confirmed_bookings'] = $bookings->where('status', 'confirmed')->count();
        $metrics['cancelled_bookings'] = $bookings->where('status', 'cancelled')->count();
        $metrics['completed_bookings'] = $bookings->where('status', 'completed')->count();

        // Calculate revenue from completed bookings
        $completedBookings = $bookings->whereIn('status', ['completed', 'confirmed']);
        $metrics['gross_revenue'] = $completedBookings->sum('total_price');

        // Calculate commission (assuming 5% commission)
        $metrics['commission_amount'] = $metrics['gross_revenue'] * 0.05;
        $metrics['net_revenue'] = $metrics['gross_revenue'] - $metrics['commission_amount'];

        // Calculate peak hour revenue (bookings between 17:00 and 21:00)
        $peakHourBookings = $completedBookings->filter(function ($booking) {
            $startTime = Carbon::parse($booking->start_time)->format('H:i');
            return $startTime >= '17:00' && $startTime <= '21:00';
        });
        $metrics['peak_hour_revenue'] = $peakHourBookings->sum('total_price');

        // Calculate discounts from booking metadata
        $metrics['discount_amount'] = $completedBookings->reduce(function ($carry, $booking) {
            $metadata = $booking->metadata ?? [];
            $discount = $metadata['discount_amount'] ?? 0;
            return $carry + $discount;
        }, 0);

        // Calculate refunds from payments
        $refundedPayments = Payment::where('status', Payment::STATUS_REFUNDED)
            ->whereIn('court_booking_id', $bookings->pluck('id'))
            ->get();
        $metrics['refund_amount'] = $refundedPayments->sum('amount');

        return $metrics;
    }

    /**
     * Calculate revenue metrics for a court on a specific date
     *
     * @param int $courtId
     * @param string $date
     * @return array
     */
    private function calculateCourtMetrics(int $courtId, string $date): array
    {
        // Get all bookings for this court on this date
        $bookings = CourtBooking::where('court_id', $courtId)
            ->where('booking_date', $date)
            ->get();

        // Initialize metrics
        $metrics = [
            'total_bookings' => $bookings->count(),
            'confirmed_bookings' => 0,
            'cancelled_bookings' => 0,
            'completed_bookings' => 0,
            'gross_revenue' => 0,
            'commission_amount' => 0,
            'net_revenue' => 0,
            'peak_hour_revenue' => 0,
            'discount_amount' => 0,
            'refund_amount' => 0,
        ];

        if ($metrics['total_bookings'] == 0) {
            return $metrics;
        }

        // Calculate booking status counts
        $metrics['confirmed_bookings'] = $bookings->where('status', 'confirmed')->count();
        $metrics['cancelled_bookings'] = $bookings->where('status', 'cancelled')->count();
        $metrics['completed_bookings'] = $bookings->where('status', 'completed')->count();

        // Calculate revenue from completed bookings
        $completedBookings = $bookings->whereIn('status', ['completed', 'confirmed']);
        $metrics['gross_revenue'] = $completedBookings->sum('total_price');

        // Calculate commission (assuming 5% commission)
        $metrics['commission_amount'] = $metrics['gross_revenue'] * 0.05;
        $metrics['net_revenue'] = $metrics['gross_revenue'] - $metrics['commission_amount'];

        // Calculate peak hour revenue (bookings between 17:00 and 21:00)
        $peakHourBookings = $completedBookings->filter(function ($booking) {
            $startTime = Carbon::parse($booking->start_time)->format('H:i');
            return $startTime >= '17:00' && $startTime <= '21:00';
        });
        $metrics['peak_hour_revenue'] = $peakHourBookings->sum('total_price');

        // Calculate discounts from booking metadata
        $metrics['discount_amount'] = $completedBookings->reduce(function ($carry, $booking) {
            $metadata = $booking->metadata ?? [];
            $discount = $metadata['discount_amount'] ?? 0;
            return $carry + $discount;
        }, 0);

        // Calculate refunds from payments
        $refundedPayments = Payment::where('status', Payment::STATUS_REFUNDED)
            ->whereIn('court_booking_id', $bookings->pluck('id'))
            ->get();
        $metrics['refund_amount'] = $refundedPayments->sum('amount');

        return $metrics;
    }
}
