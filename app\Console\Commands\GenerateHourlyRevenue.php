<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\HourlyRevenue;
use App\Models\Business;
use App\Models\Branch;
use App\Models\CourtBooking;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;

class GenerateHourlyRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revenue:generate-hourly 
                            {--date= : The date to generate revenue for (YYYY-MM-DD). Defaults to yesterday}
                            {--start-date= : Start date for a date range (YYYY-MM-DD)}
                            {--end-date= : End date for a date range (YYYY-MM-DD)}
                            {--business-id= : Generate for a specific business ID}
                            {--branch-id= : Generate for a specific branch ID}
                            {--force : Force regeneration of existing data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate hourly revenue data from bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->option('date');
        $startDate = $this->option('start-date');
        $endDate = $this->option('end-date');
        $businessId = $this->option('business-id');
        $branchId = $this->option('branch-id');
        $force = $this->option('force');

        // Determine the date range to process
        if ($startDate && $endDate) {
            $startDate = Carbon::parse($startDate)->startOfDay();
            $endDate = Carbon::parse($endDate)->endOfDay();
            $dateRange = CarbonPeriod::create($startDate, $endDate);
            $this->info("Generating hourly revenue data from {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
        } elseif ($date) {
            $processDate = Carbon::parse($date);
            $dateRange = [Carbon::parse($date)];
            $this->info("Generating hourly revenue data for {$processDate->format('Y-m-d')}");
        } else {
            // Default to yesterday
            $processDate = Carbon::yesterday();
            $dateRange = [Carbon::yesterday()];
            $this->info("Generating hourly revenue data for yesterday ({$processDate->format('Y-m-d')})");
        }

        // Get businesses to process
        $businessQuery = Business::query();
        if ($businessId) {
            $businessQuery->where('id', $businessId);
        }
        $businesses = $businessQuery->get();

        if ($businesses->isEmpty()) {
            $this->error('No businesses found with the specified criteria.');
            return 1;
        }

        $this->info("Processing " . $businesses->count() . " businesses");

        $totalRecordsCreated = 0;
        $totalRecordsUpdated = 0;

        // Process each business
        foreach ($businesses as $business) {
            $this->info("Processing business: {$business->name}");

            // Get branches to process
            $branchQuery = Branch::where('business_id', $business->id);
            if ($branchId) {
                $branchQuery->where('id', $branchId);
            }
            $branches = $branchQuery->get();

            if ($branches->isEmpty()) {
                $this->warn("No branches found for business ID {$business->id}");
                continue;
            }

            // Process each branch
            foreach ($branches as $branch) {
                $this->info("- Processing branch: {$branch->name}");

                // Process each date in the range
                foreach ($dateRange as $currentDate) {
                    $dateFormatted = $currentDate->format('Y-m-d');
                    $this->info("  - Processing date: {$dateFormatted}");

                    // Get all court bookings for this branch on this date
                    $bookings = CourtBooking::whereHas('court', function ($query) use ($branch) {
                        $query->where('branch_id', $branch->id);
                    })
                        ->where('booking_date', $dateFormatted)
                        ->whereIn('status', ['confirmed', 'completed'])
                        ->get();

                    if ($bookings->isEmpty()) {
                        $this->info("    No bookings found for this branch on {$dateFormatted}");
                        continue;
                    }

                    $this->info("    Found {$bookings->count()} bookings");

                    // Group bookings by hour
                    $hourlyData = [];
                    foreach ($bookings as $booking) {
                        $startTime = Carbon::parse($booking->start_time);
                        $endTime = Carbon::parse($booking->end_time);
                        $hourSlot = $startTime->hour;

                        if (!isset($hourlyData[$hourSlot])) {
                            $hourlyData[$hourSlot] = [
                                'total_bookings' => 0,
                                'total_revenue' => 0
                            ];
                        }

                        $hourlyData[$hourSlot]['total_bookings']++;
                        $hourlyData[$hourSlot]['total_revenue'] += (float) $booking->total_price;
                    }

                    // Create or update hourly revenue records
                    foreach ($hourlyData as $hourSlot => $data) {
                        // Check if data already exists and skip if not forced
                        if (
                            !$force && HourlyRevenue::where('business_id', $business->id)
                                ->where('branch_id', $branch->id)
                                ->where('revenue_date', $dateFormatted)
                                ->where('hour_slot', $hourSlot)
                                ->exists()
                        ) {
                            $this->warn("    Revenue data already exists for hour {$hourSlot}. Use --force to regenerate.");
                            continue;
                        }

                        $hourlyRevenue = HourlyRevenue::updateOrCreate(
                            [
                                'business_id' => $business->id,
                                'branch_id' => $branch->id,
                                'revenue_date' => $dateFormatted,
                                'hour_slot' => $hourSlot
                            ],
                            [
                                'total_bookings' => $data['total_bookings'],
                                'total_revenue' => $data['total_revenue']
                            ]
                        );

                        if ($hourlyRevenue->wasRecentlyCreated) {
                            $totalRecordsCreated++;
                        } else {
                            $totalRecordsUpdated++;
                        }

                        $this->info("    Hour {$hourSlot}: {$data['total_bookings']} bookings, " .
                            number_format($data['total_revenue'], 2) . " revenue");
                    }
                }
            }
        }

        $this->info("Hourly revenue data generation completed successfully!");
        $this->info("Records created: {$totalRecordsCreated}");
        $this->info("Records updated: {$totalRecordsUpdated}");

        return 0;
    }
}
