<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MonthlyStatistic;
use App\Models\Business;
use App\Models\Branch;
use Carbon\Carbon;

class GenerateMonthlyStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'statistics:generate-monthly 
                            {--month= : The month to generate statistics for (YYYY-MM). Defaults to last month}
                            {--business-id= : Generate for a specific business ID}
                            {--branch-id= : Generate for a specific branch ID}
                            {--force : Force regeneration of existing data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly statistics from booking data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $monthOption = $this->option('month');
        $businessId = $this->option('business-id');
        $branchId = $this->option('branch-id');
        $force = $this->option('force');

        // Determine the month to process
        if ($monthOption) {
            $yearMonth = $monthOption;
            $this->info("Generating monthly statistics for {$yearMonth}");
        } else {
            // Default to last month
            $lastMonth = Carbon::now()->subMonth();
            $yearMonth = $lastMonth->format('Y-m');
            $this->info("Generating monthly statistics for last month ({$yearMonth})");
        }

        try {
            // Check if data already exists and skip if not forced
            if (!$force) {
                $existingCount = MonthlyStatistic::where('year_month', $yearMonth);

                if ($businessId) {
                    $existingCount->where('business_id', $businessId);
                }

                if ($branchId) {
                    $existingCount->where('branch_id', $branchId);
                }

                $existingCount = $existingCount->count();

                if ($existingCount > 0) {
                    $this->warn("Statistics data already exists for {$yearMonth}. Use --force to regenerate.");
                    return 1;
                }
            }

            // Generate statistics
            $result = MonthlyStatistic::generateForMonth($yearMonth, $businessId, $branchId);

            $this->info("Monthly statistics generation completed successfully!");
            $this->info("Records created: {$result['created']}");
            $this->info("Records updated: {$result['updated']}");

            return 0;
        } catch (\Exception $e) {
            $this->error("Error generating monthly statistics: " . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }
}