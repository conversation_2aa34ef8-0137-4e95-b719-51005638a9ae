<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\Branch;
use App\Models\CourtBooking;
use App\Models\Court;
use App\Models\CourtPrice;
use App\Models\Customer;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateTestBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-test-bookings {count=10 : Number of bookings to generate} 
                                                       {days=7 : Number of days from today to generate bookings for} 
                                                       {--branch_id= : ID of the branch to generate bookings for, random if not specified} 
                                                       {--booking_type=offline : Type of booking to generate (online/offline)}
                                                       {--payment_status=paid : Payment status (paid/unpaid)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate test bookings for development and testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = $this->argument('count');
        $days = $this->argument('days');
        $branchId = $this->option('branch_id');
        $bookingType = $this->option('booking_type');
        $paymentStatus = $this->option('payment_status');

        $this->info("Generating {$count} test bookings for the next {$days} days");
        $this->info("Booking type: {$bookingType}, Payment status: {$paymentStatus}");

        try {
            // Get branches
            $branches = $branchId
                ? Branch::where('id', $branchId)->get()
                : Branch::where('status', 'active')->get();

            if ($branches->isEmpty()) {
                $this->error("No active branches found");
                return 1;
            }

            // Get some random customers for bookings
            $customers = Customer::inRandomOrder()->limit(min(10, Customer::count()))->get();
            if ($customers->isEmpty()) {
                $this->warn("No customers found, bookings will be created without customer association");
            }

            // Get staff users for branch bookings
            $staffUsers = User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['branch_admin', 'branch_staff']);
            })->get();

            if ($staffUsers->isEmpty() && $bookingType === 'offline') {
                $this->warn("No staff users found, using system user for offline bookings");
                // Create a dummy user for bookings
                $staffUsers = collect([new User(['id' => 1, 'name' => 'System User'])]);
            }

            $bar = $this->output->createProgressBar($count);
            $bar->start();

            $generatedCount = 0;
            $failedCount = 0;

            while ($generatedCount < $count) {
                // Select random branch
                $branch = $branches->random();

                // Get courts for this branch
                try {
                    $courts = Court::where('branch_id', $branch->id)
                        ->where('is_active', true)
                        ->get();

                    $this->info("Found {$courts->count()} active courts for branch #{$branch->id}");
                } catch (\Exception $e) {
                    $this->error("Error fetching courts: " . $e->getMessage());
                    $courts = collect();
                }

                if ($courts->isEmpty()) {
                    $this->warn("No active courts found for branch #{$branch->id}");
                    continue;
                }

                // Random date within the next X days
                $randomDay = rand(0, $days);
                $bookingDate = Carbon::now()->addDays($randomDay)->format('Y-m-d');

                // Select a random court
                $court = $courts->random();

                // Get price list for the court and date
                $priceList = CourtPrice::getPriceListByBranchIdAndDate($branch->id, $bookingDate);

                if ($priceList->isEmpty()) {
                    $this->warn("No price list found for branch #{$branch->id} on {$bookingDate}");
                    continue;
                }

                // Select a random time slot
                $priceItem = $priceList->random();

                // Ensure we have valid start and end times
                $startTime = Carbon::parse($priceItem->start_time);
                $endTime = Carbon::parse($priceItem->end_time);

                // Calculate a random start time within the price range
                $maxStartHour = $endTime->copy()->subHour(); // At least 1 hour before end time

                if ($maxStartHour->isBefore($startTime)) {
                    $maxStartHour = $startTime;
                }

                $startHour = rand(
                    $startTime->hour,
                    $maxStartHour->hour
                );

                $startMinute = [0, 30][rand(0, 1)]; // Only allow bookings at hour or half hour
                $bookingStartTime = Carbon::parse($bookingDate)->setHour($startHour)->setMinute($startMinute);

                // Random duration between 1-2 hours (in 30 min increments)
                $durationInMinutes = [60, 90, 120][rand(0, 2)];
                $bookingEndTime = $bookingStartTime->copy()->addMinutes($durationInMinutes);

                // Ensure end time is within the price range
                if ($bookingEndTime->isAfter($endTime)) {
                    $bookingEndTime = $endTime;
                }

                // Skip if the booking would be too short
                if ($bookingEndTime->diffInMinutes($bookingStartTime) < 60) {
                    continue;
                }

                // Select a random customer or create anonymous booking
                $customer = $customers->isEmpty() ? null : $customers->random();
                $isCustomerMember = $customer !== null;

                // Create booking data
                $bookingData = [
                    'branch_id' => $branch->id,
                    'booking_date' => $bookingDate,
                    'reference_number' => Booking::generateReferenceNumber($bookingType),
                    'customer_name' => $customer ? $customer->name : 'Guest ' . rand(1000, 9999),
                    'customer_phone' => $customer ? $customer->phone : '0' . rand(100000000, 999999999),
                    'customer_email' => $customer ? $customer->email : 'guest' . rand(1000, 9999) . '@example.com',
                    'notes' => 'Test booking generated by system',
                    'number_of_players' => rand(2, 4),
                    'payment_status' => $paymentStatus,
                    'booking_type' => $bookingType,
                    'booking_courts' => [
                        [
                            'court_id' => $court->id,
                            'booking_slot' => $this->generateTimeSlots($bookingStartTime, $bookingEndTime),
                            'start_time' => $bookingStartTime->format('H:i'),
                            'end_time' => $bookingEndTime->format('H:i')
                        ]
                    ]
                ];

                // Add additional data for payment if status is paid
                if ($paymentStatus === 'paid') {
                    $bookingData['payment_methods'] = [
                        [
                            'method' => ['cash', 'card', 'transfer'][rand(0, 2)],
                            'amount' => $court->price_per_hour * ($durationInMinutes / 60),
                            'reference_code' => 'TEST' . rand(100000, 999999)
                        ]
                    ];
                }

                // Select a random staff user for offline bookings
                $user = $bookingType === 'offline'
                    ? $staffUsers->random()
                    : ($customer && $customer->user_id ? User::find($customer->user_id) : null);

                // Create the booking
                try {
                    DB::beginTransaction();

                    // First, create a parent booking record
                    $booking = new Booking();
                    $booking->branch_id = $branch->id;
                    $booking->user_id = $user ? $user->id : null;
                    $booking->customer_id = $customer ? $customer->id : null;
                    $booking->reference_number = $bookingData['reference_number'];
                    $booking->customer_name = $bookingData['customer_name'];
                    $booking->customer_phone = $bookingData['customer_phone'];
                    $booking->customer_email = $bookingData['customer_email'];
                    $booking->notes = $bookingData['notes'];
                    $booking->number_of_players = $bookingData['number_of_players'];
                    $booking->status = $bookingType === 'offline' ? 'confirmed' : 'pending';
                    $booking->payment_status = $paymentStatus;
                    $booking->booking_type = $bookingType;
                    $booking->save();

                    // Now use the existing court booking creation logic
                    $createdBookings = [];
                    $result = CourtBooking::processBookings($bookingData, $user, $bookingType, $createdBookings);

                    if ($result['success']) {
                        DB::commit();
                        $generatedCount++;

                        // Set a check-in/check-out status for some past bookings
                        if ($bookingDate < Carbon::today()->format('Y-m-d')) {
                            foreach ($createdBookings as $createdBookingInfo) {
                                $createdBooking = CourtBooking::find($createdBookingInfo['id']);
                                if ($createdBooking && rand(0, 10) > 3) { // 70% chance of check-in for past bookings
                                    $checkInTime = Carbon::parse($bookingDate . ' ' . $bookingStartTime->format('H:i'))->subMinutes(rand(5, 15));
                                    $createdBooking->checkin_status = 'checked_in';
                                    $createdBooking->checkin_time = $checkInTime;

                                    // 80% chance of checkout for checked-in bookings
                                    if (rand(0, 10) > 2) {
                                        $scheduledEndTime = Carbon::parse($bookingDate . ' ' . $bookingEndTime->format('H:i'));
                                        // Sometimes checkout late (20% chance)
                                        $checkoutDelay = rand(0, 10) > 8 ? rand(5, 30) : 0;
                                        $checkOutTime = $scheduledEndTime->addMinutes($checkoutDelay);

                                        $createdBooking->checkin_status = 'checked_out';
                                        $createdBooking->checkout_time = $checkOutTime;

                                        // Calculate overtime if applicable
                                        if ($checkoutDelay > 0) {
                                            $createdBooking->overtime_minutes = $checkoutDelay;
                                            $createdBooking->overtime_calculated = true;
                                            $createdBooking->overtime_fee = $court->price_per_hour * ($checkoutDelay / 60);
                                            $createdBooking->overtime_fee_paid = rand(0, 1) == 1; // 50% chance of paid overtime
                                        }
                                    }

                                    $createdBooking->save();
                                }
                            }
                        }
                    } else {
                        DB::rollBack();
                        $failedCount++;
                    }
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error("Error generating test booking: " . $e->getMessage(), ['exception' => $e]);
                    $failedCount++;
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("Successfully generated {$generatedCount} bookings");
            if ($failedCount > 0) {
                $this->warn("Failed to generate {$failedCount} bookings");
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            Log::error("Error in generate test bookings command: " . $e->getMessage(), ['exception' => $e]);
            return 1;
        }
    }

    /**
     * Generate time slots between start and end time in 30-minute increments
     *
     * @param Carbon $startTime
     * @param Carbon $endTime
     * @return array
     */
    private function generateTimeSlots(Carbon $startTime, Carbon $endTime): array
    {
        $slots = [];
        $currentTime = $startTime->copy();

        while ($currentTime < $endTime) {
            $slots[] = $currentTime->format('H:i');
            $currentTime->addMinutes(30);
        }

        return $slots;
    }
}