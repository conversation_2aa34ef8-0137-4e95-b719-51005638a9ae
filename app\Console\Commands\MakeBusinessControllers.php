<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakeBusinessControllers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:business-controllers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create all required Business controllers';

    /**
     * The controller namespace
     *
     * @var string
     */
    protected $namespace = 'App\\Http\\Controllers\\Business';

    /**
     * The controller path
     *
     * @var string
     */
    protected $path = 'app/Http/Controllers/Business';

    /**
     * List of controller names to create
     *
     * @var array
     */
    protected $controllers = [
        'CourtController',
        'BookingController',
        'CustomerController',
        'PromotionController',
        'CouponController',
        'SettingController',
        'TransactionController',
        'FinanceController',
        'StatisticController',
        'NotificationController',
        'PaymentMethodController',
        'ScheduleController',
        'CustomerGroupController',
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (!File::isDirectory($this->path)) {
            File::makeDirectory($this->path, 0755, true);
            $this->info('Created Business controllers directory');
        }
        foreach ($this->controllers as $controller) {
            $this->createController($controller);
        }

        $this->info('All Business controllers created successfully!');
        return 0;
    }

    /**
     * Create a controller file
     *
     * @param string $controller
     * @return void
     */
    protected function createController($controller)
    {
        $filePath = $this->path . '/' . $controller . '.php';
        if (File::exists($filePath)) {
            $this->warn("Controller {$controller} already exists. Skipping.");
            return;
        }
        $modelName = str_replace('Controller', '', $controller);
        $content = $this->getStubContent($controller, $modelName);
        File::put($filePath, $content);
        $this->info("Created {$controller}");
    }

    /**
     * Get the content for the controller stub
     *
     * @param string $controller
     * @param string $modelName
     * @return string
     */
    protected function getStubContent($controller, $modelName)
    {
        $modelSnake = Str::snake($modelName);
        $modelSlug = Str::plural($modelSnake);
        $modelClass = "\\App\\Models\\{$modelName}";
        $modelVariable = lcfirst($modelName);

        return <<<PHP
<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use {$modelClass};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class {$controller} extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Inertia\Response
     */
    public function index(Request \$request)
    {
        \$businessId = \$request->user()->business_id;

        if (!\\$businessId) {
            return redirect()->route('dashboard')
                ->with('error', 'You are not associated with any business.');
        }
        \$query = {$modelName}::where('business_id', \$businessId);
        if (\$request->has('search') && !empty(\$request->search)) {
            \$search = \$request->search;
            \$query->where(function (\$q) use (\$search) {
                \$q->where('name', 'like', "%{\$search}%");
            });
        }

        if (\$request->has('status') && !empty(\$request->status)) {
            \$query->where('status', \$request->status);
        }

        \$sortField = \$request->input('sort', 'created_at');
        \$sortDirection = \$request->input('direction', 'desc');
        \$query->orderBy(\$sortField, \$sortDirection);

        \${$modelSlug} = \$query->paginate(10)->withQueryString();

        return Inertia::render('Business/{$modelSlug}/Index', [
            '{$modelSlug}' => \${$modelSlug},
            'filters' => [
                'search' => \$request->search ?? '',
                'status' => \$request->status ?? '',
                'sort' => \$sortField,
                'direction' => \$sortDirection,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Inertia\Response
     */
    public function create(Request \$request)
    {
        \$businessId = \$request->user()->business_id;
        
        return Inertia::render('Business/{$modelSlug}/Create', [
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  \$request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request \$request)
    {
        \$businessId = \$request->user()->business_id;

        if (!\\$businessId) {
            return redirect()->back()->withErrors([
                'error' => 'You are not associated with any business.'
            ]);
        }

        \$validated = \$request->validate([
            'name' => 'required|string|max:255',
        ]);

        \$validated['business_id'] = \$businessId;

        try {
            DB::beginTransaction();
            \${$modelVariable} = {$modelName}::create(\$validated);

            DB::commit();

            return redirect()->route('{$modelSlug}.index')
                ->with('success', '{$modelName} created successfully.');
        } catch (\Exception \$e) {
            DB::rollBack();
            Log::error('Failed to create {$modelSnake}: ' . \$e->getMessage(), [
                'exception' => \$e,
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to create {$modelSnake}: ' . \$e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \{$modelClass}  \${$modelVariable}
     * @return \Inertia\Response
     */
    public function show(Request \$request, {$modelName} \${$modelVariable})
    {
        \$businessId = \$request->user()->business_id;
        if (\${$modelVariable}->business_id !== \$businessId) {
            return redirect()->route('{$modelSlug}.index')
                ->with('error', 'You do not have permission to view this {$modelSnake}.');
        }

        return Inertia::render('Business/{$modelSlug}/Show', [
            '{$modelVariable}' => \${$modelVariable},
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \{$modelClass}  \${$modelVariable}
     * @return \Inertia\Response
     */
    public function edit(Request \$request, {$modelName} \${$modelVariable})
    {
        \$businessId = \$request->user()->business_id;
        if (\${$modelVariable}->business_id !== \$businessId) {
            return redirect()->route('{$modelSlug}.index')
                ->with('error', 'You do not have permission to edit this {$modelSnake}.');
        }

        return Inertia::render('Business/{$modelSlug}/Edit', [
            '{$modelVariable}' => \${$modelVariable},
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  \$request
     * @param  \{$modelClass}  \${$modelVariable}
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request \$request, {$modelName} \${$modelVariable})
    {
        \$businessId = \$request->user()->business_id;
        if (\${$modelVariable}->business_id !== \$businessId) {
            return redirect()->route('{$modelSlug}.index')
                ->with('error', 'You do not have permission to update this {$modelSnake}.');
        }

        \$validated = \$request->validate([
            'name' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();
            \${$modelVariable}->update(\$validated);

            DB::commit();

            return redirect()->route('{$modelSlug}.index')
                ->with('success', '{$modelName} updated successfully.');
        } catch (\Exception \$e) {
            DB::rollBack();
            Log::error('Failed to update {$modelSnake}: ' . \$e->getMessage(), [
                'exception' => \$e,
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to update {$modelSnake}: ' . \$e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \{$modelClass}  \${$modelVariable}
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request \$request, {$modelName} \${$modelVariable})
    {
        \$businessId = \$request->user()->business_id;
        if (\${$modelVariable}->business_id !== \$businessId) {
            return redirect()->route('{$modelSlug}.index')
                ->with('error', 'You do not have permission to delete this {$modelSnake}.');
        }

        try {
            DB::beginTransaction();
            \${$modelVariable}->delete();

            DB::commit();

            return redirect()->route('{$modelSlug}.index')
                ->with('success', '{$modelName} deleted successfully.');
        } catch (\Exception \$e) {
            DB::rollBack();
            Log::error('Failed to delete {$modelSnake}: ' . \$e->getMessage(), [
                'exception' => \$e,
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to delete {$modelSnake}: ' . \$e->getMessage()
            ]);
        }
    }
}
PHP;
    }
}