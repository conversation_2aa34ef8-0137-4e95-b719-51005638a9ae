<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class OptimizeDatabaseIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:optimize
                            {--table= : Specific table to optimize}
                            {--all : Optimize all tables}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database tables and analyze them for better query performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $table = $this->option('table');
        $all = $this->option('all');

        if (!$table && !$all) {
            $this->error('Please specify a table with --table or use --all to optimize all tables');
            return 1;
        }

        $tables = [];

        if ($all) {
            // Get all tables except migrations and other system tables
            $tables = $this->getAllTables();
        } else {
            // Validate table exists
            if (!Schema::hasTable($table)) {
                $this->error("Table '{$table}' does not exist");
                return 1;
            }
            $tables = [$table];
        }

        $this->info('Starting database optimization...');
        $bar = $this->output->createProgressBar(count($tables));
        $bar->start();

        foreach ($tables as $tableName) {
            $this->optimizeTable($tableName);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Database optimization completed successfully!');

        return 0;
    }

    /**
     * Get all tables in the database except system tables
     */
    private function getAllTables(): array
    {
        // Get database connection name
        $connection = config('database.default');
        $dbName = config("database.connections.{$connection}.database");

        // Get all tables
        $tables = DB::select("SHOW TABLES FROM `{$dbName}`");
        $tableColumn = "Tables_in_{$dbName}";

        // Filter out system tables
        $systemTables = ['migrations', 'failed_jobs', 'password_resets', 'personal_access_tokens'];
        return array_filter(array_map(function ($table) use ($tableColumn) {
            return $table->$tableColumn;
        }, $tables), function ($table) use ($systemTables) {
            return !in_array($table, $systemTables);
        });
    }

    /**
     * Optimize a specific table
     */
    private function optimizeTable(string $tableName): void
    {
        $this->line("Optimizing table: {$tableName}");

        try {
            // Optimize table
            DB::statement("OPTIMIZE TABLE `{$tableName}`");

            // Analyze table to update statistics
            DB::statement("ANALYZE TABLE `{$tableName}`");

            $this->line("✓ Table {$tableName} optimized successfully");
        } catch (\Exception $e) {
            $this->warn("✗ Failed to optimize table {$tableName}: " . $e->getMessage());
        }
    }
}
