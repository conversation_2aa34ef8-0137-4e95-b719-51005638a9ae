<?php

namespace App\Console\Commands;

use App\Models\Branch;
use App\Models\CourtBooking;
use App\Models\Statistic;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PopulateBranchStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'statistics:populate-branch {--branch=} {--from=} {--to=} {--period=monthly}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate branch revenue statistics in the statistics table from existing booking data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $branchId = $this->option('branch');
        $fromDate = $this->option('from');
        $toDate = $this->option('to');
        $period = $this->option('period');

        if (!$fromDate) {
            $fromDate = Carbon::now()->subYear()->startOfMonth()->format('Y-m-d');
        }

        if (!$toDate) {
            $toDate = Carbon::now()->format('Y-m-d');
        }

        $this->info("Populating statistics from {$fromDate} to {$toDate} with period: {$period}");

        $branches = [];
        if ($branchId) {
            $branch = Branch::find($branchId);
            if ($branch) {
                $branches[] = $branch;
            } else {
                $this->error("Branch with ID {$branchId} not found");
                return 1;
            }
        } else {
            $branches = Branch::all();
            $this->info("Processing all branches: " . $branches->count() . " branches found");
        }

        $progressBar = $this->output->createProgressBar(count($branches));
        $progressBar->start();

        foreach ($branches as $branch) {
            $this->processStatisticsForBranch($branch, $fromDate, $toDate, $period);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info('Branch statistics population completed');

        return 0;
    }

    /**
     * Process statistics for a branch
     *
     * @param Branch $branch
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return void
     */
    private function processStatisticsForBranch($branch, $fromDate, $toDate, $period)
    {
        $from = Carbon::parse($fromDate);
        $to = Carbon::parse($toDate);

        // Based on the period, create statistics entries
        switch ($period) {
            case 'daily':
                $current = $from->copy();
                while ($current->lte($to)) {
                    $this->createDailyStatistics($branch, $current);
                    $current->addDay();
                }
                break;

            case 'weekly':
                $current = $from->copy()->startOfWeek();
                while ($current->lte($to)) {
                    $this->createWeeklyStatistics($branch, $current);
                    $current->addWeek();
                }
                break;

            case 'monthly':
            default:
                $current = $from->copy()->startOfMonth();
                while ($current->lte($to)) {
                    $this->createMonthlyStatistics($branch, $current);
                    $current->addMonth();
                }
                break;
        }
    }

    /**
     * Create daily statistics for a branch
     *
     * @param Branch $branch
     * @param Carbon $date
     * @return void
     */
    private function createDailyStatistics($branch, $date)
    {
        $dateString = $date->format('Y-m-d');

        // Check if statistics already exist for this date
        $statistic = Statistic::where('branch_id', $branch->id)
            ->where('statistics_type', 'revenue_daily')
            ->where('statistics_date', $dateString)
            ->first();

        if ($statistic) {
            // Skip if already exists
            return;
        }

        // Get bookings for this day
        $bookings = CourtBooking::whereHas('court', function($query) use ($branch) {
            $query->where('branch_id', $branch->id);
        })
        ->where('status', 'completed')
        ->whereDate('booking_date', $dateString)
        ->with(['customer', 'court', 'payment'])
        ->get();

        // Calculate statistics
        $stats = $this->calculateStatistics($branch, $bookings);

        // Store the statistics
        if ($stats) {
            Statistic::create([
                'business_id' => $branch->business_id,
                'branch_id' => $branch->id,
                'statistics_type' => 'revenue_daily',
                'statistics_date' => $dateString,
                'values' => $stats
            ]);
        }
    }

    /**
     * Create weekly statistics for a branch
     *
     * @param Branch $branch
     * @param Carbon $weekStart
     * @return void
     */
    private function createWeeklyStatistics($branch, $weekStart)
    {
        $weekEnd = $weekStart->copy()->endOfWeek();
        $dateString = $weekStart->format('Y-m-d');

        // Check if statistics already exist for this week
        $statistic = Statistic::where('branch_id', $branch->id)
            ->where('statistics_type', 'revenue_weekly')
            ->where('statistics_date', $dateString)
            ->first();

        if ($statistic) {
            // Skip if already exists
            return;
        }

        // Get bookings for this week
        $bookings = CourtBooking::whereHas('court', function($query) use ($branch) {
            $query->where('branch_id', $branch->id);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$dateString, $weekEnd->format('Y-m-d')])
        ->with(['customer', 'court', 'payment'])
        ->get();

        // Calculate statistics
        $stats = $this->calculateStatistics($branch, $bookings);

        // Store the statistics
        if ($stats) {
            Statistic::create([
                'business_id' => $branch->business_id,
                'branch_id' => $branch->id,
                'statistics_type' => 'revenue_weekly',
                'statistics_date' => $dateString,
                'values' => $stats
            ]);
        }
    }

    /**
     * Create monthly statistics for a branch
     *
     * @param Branch $branch
     * @param Carbon $monthStart
     * @return void
     */
    private function createMonthlyStatistics($branch, $monthStart)
    {
        $monthEnd = $monthStart->copy()->endOfMonth();
        $dateString = $monthStart->format('Y-m-d');

        // Check if statistics already exist for this month
        $statistic = Statistic::where('branch_id', $branch->id)
            ->where('statistics_type', 'revenue_monthly')
            ->where('statistics_date', $dateString)
            ->first();

        if ($statistic) {
            // Skip if already exists
            return;
        }

        // Get bookings for this month
        $bookings = CourtBooking::whereHas('court', function($query) use ($branch) {
            $query->where('branch_id', $branch->id);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$dateString, $monthEnd->format('Y-m-d')])
        ->with(['customer', 'court', 'payment'])
        ->get();

        // Calculate statistics
        $stats = $this->calculateStatistics($branch, $bookings);

        // Store the statistics
        if ($stats) {
            Statistic::create([
                'business_id' => $branch->business_id,
                'branch_id' => $branch->id,
                'statistics_type' => 'revenue_monthly',
                'statistics_date' => $dateString,
                'values' => $stats
            ]);
        }
    }

    /**
     * Calculate statistics from bookings
     *
     * @param Branch $branch
     * @param \Illuminate\Database\Eloquent\Collection $bookings
     * @return array|null
     */
    private function calculateStatistics($branch, $bookings)
    {
        if ($bookings->isEmpty()) {
            return null;
        }

        $totalRevenue = $bookings->sum('total_price');
        $totalBookings = $bookings->count();

        // Group bookings by reference number
        $transactionsByReference = [];
        foreach ($bookings as $booking) {
            if (!isset($transactionsByReference[$booking->reference_number])) {
                $transactionsByReference[$booking->reference_number] = [
                    'reference_number' => $booking->reference_number,
                    'cusname' => $booking->customer ? $booking->customer->name : $booking->customer_name,
                    'phone' => $booking->customer ? $booking->customer->phone : $booking->customer_phone,
                    'payment_status' => $booking->payment ? $booking->payment->status : '',
                    'total_price' => 0,
                    'bookingCourt' => []
                ];
            }

            // Add booking details
            $transactionsByReference[$booking->reference_number]['bookingCourt'][] = [
                'court_id' => $booking->court_id,
                'start_date' => $booking->booking_date->format('Y-m-d'),
                'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
                'end_time' => Carbon::parse($booking->end_time)->format('H:i')
            ];

            // Add to total price
            $transactionsByReference[$booking->reference_number]['total_price'] += $booking->total_price;
        }

        // Convert to array
        $transactions = array_values($transactionsByReference);

        return [
            'total_revenue' => $totalRevenue,
            'revenue_formatted' => number_format($totalRevenue, 0, ',', '.') . ' ₫',
            'total_bookings' => $totalBookings,
            'avg_revenue_per_booking' => $totalBookings > 0 ? round($totalRevenue / $totalBookings, 0) : 0,
            'avg_revenue_per_booking_formatted' => $totalBookings > 0 ? number_format(round($totalRevenue / $totalBookings, 0), 0, ',', '.') . ' ₫' : '0 ₫',
            'transactions_data' => $transactions
        ];
    }
}
