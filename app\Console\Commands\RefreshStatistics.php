<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class RefreshStatistics extends Command
{
    protected $signature = 'statistics:refresh';
    protected $description = 'Refresh all statistics data for all time periods';

    public function handle()
    {
        $this->info('Beginning statistics refresh for all time periods...');

        // Update statistics for all time periods
        $this->info('Updating daily statistics...');
        Artisan::call('statistics:update --type=daily');
        $this->info(Artisan::output());

        $this->info('Updating weekly statistics...');
        Artisan::call('statistics:update --type=weekly');
        $this->info(Artisan::output());

        $this->info('Updating monthly statistics...');
        Artisan::call('statistics:update --type=monthly');
        $this->info(Artisan::output());

        $this->info('Updating yearly statistics...');
        Artisan::call('statistics:update --type=yearly');
        $this->info(Artisan::output());

        $this->info('All statistics have been refreshed successfully!');
        return 0;
    }
}
