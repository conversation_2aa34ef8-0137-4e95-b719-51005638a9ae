<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\CustomerUserSeeder;

class SeedCustomerUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:customers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed customer users with the user role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Seeding customer users...');

        $seeder = new CustomerUserSeeder();
        $seeder->setCommand($this);
        $seeder->run();

        $this->info('Customer users seeded successfully!');

        return Command::SUCCESS;
    }
}