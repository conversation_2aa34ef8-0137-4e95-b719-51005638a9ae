<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationService;
use Illuminate\Support\Str;

class TestBookingNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:booking-notification 
                            {branch_id : ID của chi nhánh để gửi thông báo}
                            {--title=* : Tiêu đề thông báo}
                            {--message=* : Nội dung thông báo}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tạo thông báo đặt sân test để kiểm tra WebSocket realtime';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Lấy thông tin từ tham số command
        $branchId = $this->argument('branch_id');
        $title = $this->option('title') ? implode(' ', $this->option('title')) : 'Test Booking Notification';
        $message = $this->option('message') ? implode(' ', $this->option('message')) : 'This is a test booking notification';

        // Tạo mã tham chiếu ngẫu nhiên để giả lập booking
        $referenceNumber = 'TEST-' . strtoupper(Str::random(8));

        // Thông tin khách hàng mẫu
        $customerName = 'Test Customer';
        $customerPhone = '0987654321';

        // Thông tin chi tiết đặt sân mẫu
        $bookingDetails = [
            [
                'court_name' => 'Test Court 1',
                'booking_date' => now()->format('Y-m-d'),
                'start_time' => now()->format('H:i'),
                'end_time' => now()->addHour()->format('H:i'),
                'price' => 150000
            ],
            [
                'court_name' => 'Test Court 2',
                'booking_date' => now()->format('Y-m-d'),
                'start_time' => now()->format('H:i'),
                'end_time' => now()->addHour()->format('H:i'),
                'price' => 150000
            ]
        ];

        $this->info('Sending test booking notification to branch #' . $branchId);
        $this->info('Title: ' . $title);
        $this->info('Message: ' . $message);

        try {
            // Tạo thông báo booking test
            $notification = NotificationService::create(
                null, // user_id
                null, // business_id
                (int) $branchId,
                'booking',
                $title,
                $message,
                [
                    'reference_number' => $referenceNumber,
                    'customer_name' => $customerName,
                    'customer_phone' => $customerPhone,
                    'booking_details' => $bookingDetails,
                    'is_test' => true
                ]
            );

            $this->info('Test notification sent successfully!');
            $this->info('Notification ID: ' . $notification->id);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to send test notification: ' . $e->getMessage());

            return Command::FAILURE;
        }
    }
}