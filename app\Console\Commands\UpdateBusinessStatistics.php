<?php

namespace App\Console\Commands;

use App\Http\Controllers\Business\DashboardController;
use App\Models\Business;
use App\Models\Statistic;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateBusinessStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'business:update-statistics {business_id? : Cập nhật thống kê cho một doanh nghiệp cụ thể} {--force : X<PERSON>a tất cả thống kê hiện có và tính toán lại}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật thống kê của các doanh nghiệp';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $businessId = $this->argument('business_id');
        $force = $this->option('force');

        $dashboardController = new DashboardController();

        if ($force) {
            $this->info('<PERSON><PERSON>a tất cả thống kê hiện có...');

            if ($businessId) {
                Statistic::where('business_id', $businessId)->delete();
            } else {
                Statistic::whereNotNull('business_id')->delete();
            }
        }

        if ($businessId) {
            $business = Business::find($businessId);

            if (!$business) {
                $this->error("Không tìm thấy doanh nghiệp với ID: {$businessId}");
                return 1;
            }

            $this->updateStatisticsForBusiness($business, $dashboardController);
        } else {
            $businesses = Business::all();
            $this->info("Cập nhật thống kê cho {$businesses->count()} doanh nghiệp...");

            $bar = $this->output->createProgressBar($businesses->count());
            $bar->start();

            foreach ($businesses as $business) {
                $this->updateStatisticsForBusiness($business, $dashboardController, false);
                $bar->advance();
            }

            $bar->finish();
            $this->newLine();
        }

        $this->info('Thống kê đã được cập nhật thành công!');
        return 0;
    }

    /**
     * Cập nhật thống kê cho một doanh nghiệp
     *
     * @param Business $business
     * @param DashboardController $controller
     * @param bool $verbose
     * @return void
     */
    private function updateStatisticsForBusiness($business, $controller, $verbose = true)
    {
        if ($verbose) {
            $this->info("Đang cập nhật thống kê cho doanh nghiệp: {$business->name} (ID: {$business->id})");
        }

        $periods = ['daily', 'weekly', 'monthly', 'yearly'];

        foreach ($periods as $period) {
            try {
                // Gọi phương thức để lấy thống kê doanh nghiệp
                $stats = $controller->getDashboardStats($business->id, $period);

                // Lưu thống kê mới hoặc cập nhật thống kê cũ
                Statistic::updateOrCreate(
                    [
                        'business_id' => $business->id,
                        'branch_id' => null,
                        'statistics_type' => $period,
                        'statistics_date' => Carbon::now()->toDateString(),
                    ],
                    [
                        'values' => $stats
                    ]
                );

                if ($verbose) {
                    $this->info("  - Đã cập nhật thành công thống kê {$period}");
                }
            } catch (\Exception $e) {
                $message = "Lỗi khi cập nhật thống kê {$period} cho doanh nghiệp {$business->id}: " . $e->getMessage();
                Log::error($message);

                if ($verbose) {
                    $this->error("  - {$message}");
                }
            }
        }
    }
}
