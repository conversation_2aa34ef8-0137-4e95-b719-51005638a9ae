<?php

namespace App\Console\Commands;

use App\Http\Controllers\SuperAdmin\StatisticController;
use App\Models\Statistic;
use App\Models\Business;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateStatistics extends Command
{
    protected $signature = 'statistics:update {--type=daily : Lo<PERSON><PERSON> thống kê (daily, weekly, monthly)}';
    protected $description = 'Cập nhật thống kê cho dashboard';

    public function handle()
    {
        $type = $this->option('type');
        $now = Carbon::now();
        $this->info("Đang cập nhật thống kê {$type}...");

        // Thống kê toàn hệ thống (SuperAdmin)
        $statisticController = new StatisticController();
        $stats = $statisticController->getDashboardStats(request()->merge(['period' => $type]));

        Statistic::updateOrCreate(
            [
                'business_id' => null,
                'branch_id' => null,
                'statistics_type' => $type,
                'statistics_date' => $now->toDateString(),
            ],
            [
                'values' => $stats
            ]
        );

        $this->info("Đ<PERSON> cập nhật thống kê toàn hệ thống.");

        // Thống kê theo từng business
        Business::chunk(100, function($businesses) use ($type, $now, $statisticController) {
            foreach ($businesses as $business) {
                $businessStats = $statisticController->getBusinessStats($business->id, $type);

                Statistic::updateOrCreate(
                    [
                        'business_id' => $business->id,
                        'branch_id' => null,
                        'statistics_type' => $type,
                        'statistics_date' => $now->toDateString(),
                    ],
                    [
                        'values' => $businessStats
                    ]
                );

                $this->info("Đã cập nhật thống kê cho business: {$business->name}");
            }
        });

        // Thống kê theo từng chi nhánh
        Branch::chunk(100, function($branches) use ($type, $now, $statisticController) {
            foreach ($branches as $branch) {
                $branchStats = $statisticController->getBranchStats($branch->id, $type);

                Statistic::updateOrCreate(
                    [
                        'business_id' => $branch->business_id,
                        'branch_id' => $branch->id,
                        'statistics_type' => $type,
                        'statistics_date' => $now->toDateString(),
                    ],
                    [
                        'values' => $branchStats
                    ]
                );

                $this->info("Đã cập nhật thống kê cho chi nhánh: {$branch->name}");
            }
        });

        $this->info('Đã hoàn tất cập nhật thống kê!');

        return 0;
    }
}
