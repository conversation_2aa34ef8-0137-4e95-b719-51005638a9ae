<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('statistics:update --type=daily')->dailyAt('01:00');

        $schedule->command('statistics:update --type=weekly')->weeklyOn(2, '02:00');

        $schedule->command('statistics:update --type=monthly')->monthlyOn(2, '03:00');

        $schedule->command('statistics:update --type=yearly')->yearlyOn(1, 2, '04:00');

        $schedule->command('statistics:populate-branch --period=daily')->dailyAt('01:30');
        $schedule->command('statistics:populate-branch --period=weekly')->weeklyOn(2, '02:30');
        $schedule->command('statistics:populate-branch --period=monthly')->monthlyOn(2, '03:30');

        $schedule->command('statistics:cleanup')->monthly();

        $schedule->command('app:cancel-expired-bookings')
            ->everyFiveMinutes()
            ->appendOutputTo(storage_path('logs/expired-bookings.log'));

        $schedule->command('revenue:generate-daily')
            ->dailyAt('02:00')
            ->appendOutputTo(storage_path('logs/daily-revenue.log'));

        $schedule->command('revenue:generate-hourly')
            ->dailyAt('02:30')
            ->appendOutputTo(storage_path('logs/hourly-revenue.log'));

        $schedule->command('statistics:generate-monthly')
            ->monthlyOn(1, '03:00')
            ->appendOutputTo(storage_path('logs/monthly-statistics.log'));

        // Run database optimization weekly during low traffic hours
        $schedule->command('db:optimize --all')
            ->weekly()
            ->sundays()
            ->at('03:00')
            ->appendOutputTo(storage_path('logs/db-optimize.log'));

        // Regenerate daily revenue data daily
        $schedule->command('revenue:generate-daily')
            ->dailyAt('02:00')
            ->appendOutputTo(storage_path('logs/revenue-generation.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        // This will automatically register all commands in the Commands directory
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    /**
     * Get the commands that should be registered for the application.
     *
     * @return array
     */
    protected function getCommands()
    {
        return [
            \App\Console\Commands\FixPermissionCache::class,
            \App\Console\Commands\CancelExpiredBookings::class,
            \App\Console\Commands\UpdateStatistics::class,
            \App\Console\Commands\PopulateBranchStatistics::class,
            \App\Console\Commands\CleanupStatistics::class,
            \App\Console\Commands\UpdateBusinessStatistics::class,
            \App\Console\Commands\RefreshStatistics::class,
            \App\Console\Commands\SeedCustomerUsers::class,
            \App\Console\Commands\MakeBusinessControllers::class,
            \App\Console\Commands\TestBookingNotification::class,
            \App\Console\Commands\GenerateTestBookings::class,
            \App\Console\Commands\GenerateDailyRevenue::class,
            \App\Console\Commands\GenerateHourlyRevenue::class,
            \App\Console\Commands\GenerateMonthlyStatistics::class,
        ];
    }
}
