<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Notification;

class BookingNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Thời gian chờ trước khi bỏ qua event nếu không thể broadcast (giây)
     */
    public $broadcastTimeout = 10;

    /**
     * Số lần thử lại nếu không thể broadcast
     */
    public $tries = 3;

    public $notification;

    /**
     * Create a new event instance.
     */
    public function __construct(Notification $notification)
    {
        $this->notification = $notification;
        Log::info("BookingNotification created", [
            'notification_id' => $notification->id,
            'branch_id' => $notification->branch_id
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channelName = 'branch.' . $this->notification->branch_id;
        Log::info("Broadcasting notification to channel: " . $channelName, [
            'notification_id' => $this->notification->id,
            'type' => $this->notification->type
        ]);

        return [
            new PrivateChannel($channelName),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'new.booking';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        $data = [
            'id' => $this->notification->id,
            'title' => $this->notification->title,
            'message' => $this->notification->message,
            'type' => $this->notification->type,
            'created_at' => $this->notification->created_at,
            'data' => $this->notification->data,
            'is_read' => false,
            'read_at' => null,
            'branch_id' => $this->notification->branch_id,
            'timestamp' => now()->timestamp
        ];

        Log::info("Broadcasting notification data", [
            'id' => $this->notification->id,
            'title' => $this->notification->title,
            'channel' => 'branch.' . $this->notification->branch_id
        ]);

        return $data;
    }
}