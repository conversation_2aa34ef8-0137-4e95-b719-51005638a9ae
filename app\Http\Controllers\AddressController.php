<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Province;
use App\Models\District;
use App\Models\Ward;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AddressController extends Controller
{
    /**
     * Thời gian lưu cache (1 ngày)
     */
    const CACHE_TTL = 86400;

    /**
     * Get all provinces
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function provinces()
    {
        $provinces = Cache::remember('api_provinces_list', self::CACHE_TTL, function () {
            return Province::select('id', 'name', 'alias', 'lat', 'lng')
                ->orderBy('name')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $provinces
        ]);
    }

    /**
     * Get districts by province ID
     *
     * @param int $provinceId
     * @return \Illuminate\Http\JsonResponse
     */
    public function districts($provinceId)
    {
        $cacheKey = "api_districts_by_province_{$provinceId}";

        $districts = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceId) {
            
            return DB::table('districts')
                ->select('id', 'name', 'alias', 'lat', 'lng')
                ->where('province_id', $provinceId)
                ->orderBy('name')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $districts
        ]);
    }

    /**
     * Get wards by district ID
     *
     * @param int $districtId
     * @return \Illuminate\Http\JsonResponse
     */
    public function wards($districtId)
    {
        $cacheKey = "api_wards_by_district_{$districtId}";

        $wards = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($districtId) {
            
            return DB::table('wards')
                ->select('id', 'name', 'alias', 'lat', 'lng')
                ->where('district_id', $districtId)
                ->orderBy('name')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $wards
        ]);
    }
}