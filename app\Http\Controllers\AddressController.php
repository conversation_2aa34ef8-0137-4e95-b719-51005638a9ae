<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Province;
use App\Models\Ward;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AddressController extends Controller
{
    const CACHE_TTL = 86400;

    /**
     * Get all provinces
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function provinces()
    {
        $provinces = Cache::remember('api_provinces_list', self::CACHE_TTL, function () {
            return Province::select('id', 'province_code', 'name', 'short_name', 'code', 'place_type')
                ->orderBy('name')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $provinces
        ]);
    }

    /**
     * Get wards by province code
     *
     * @param string $provinceCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function wards($provinceCode)
    {
        $cacheKey = "api_wards_by_province_{$provinceCode}";

        $wards = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceCode) {
            return Ward::where('province_code', $provinceCode)
                ->select('id', 'ward_code', 'name', 'province_code')
                ->orderBy('name')
                ->get();
        });

        return response()->json([
            'success' => true,
            'data' => $wards
        ]);
    }

    /**
     * Get full address by province code and ward code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFullAddress(Request $request)
    {
        $provinceCode = $request->input('province_code');
        $wardCode = $request->input('ward_code');

        if (!$provinceCode || !$wardCode) {
            return response()->json([
                'success' => false,
                'message' => 'Province code and ward code are required'
            ], 400);
        }

        $province = Province::where('province_code', $provinceCode)->first();
        $ward = Ward::where('ward_code', $wardCode)->first();

        if (!$province || !$ward) {
            return response()->json([
                'success' => false,
                'message' => 'Province or ward not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'province' => $province,
                'ward' => $ward,
                'full_address' => $ward->name . ', ' . $province->name
            ]
        ]);
    }
}