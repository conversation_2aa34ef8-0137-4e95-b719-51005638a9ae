<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HourlyRevenue;
use App\Models\Branch;
use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class HourlyRevenueController extends Controller
{
    /**
     * Display hourly revenue data
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $businessId = null;
        $branchId = null;

        // For super admin, get the selected business from request
        if ($user->hasRole('super-admin')) {
            $businessId = $request->input('business_id');
            $businesses = Business::all();
        } else {
            // For business admin, use their business
            $businessId = $user->business_id;
            $businesses = collect([$user->business]);
        }

        // Get selected branch if provided
        $branchId = $request->input('branch_id');

        // Get branches based on selected business
        $branches = $businessId
            ? Branch::where('business_id', $businessId)->get()
            : collect();

        // Get date range from request or use default (last 7 days)
        $startDate = $request->input('start_date', Carbon::now()->subDays(6)->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // Get hourly revenue data
        $hourlyData = HourlyRevenue::getHourlyRevenueByDateRange(
            $startDate,
            $endDate,
            $businessId,
            $branchId
        );

        // Get peak hours
        $peakHours = HourlyRevenue::getPeakHours(
            $startDate,
            $endDate,
            $businessId,
            $branchId,
            5 // Top 5 peak hours
        );

        // Get hourly distribution for chart
        $hourlyDistribution = HourlyRevenue::getHourlyDistributionForChart(
            $startDate,
            $endDate,
            $businessId,
            $branchId
        );

        // Get busiest days of week
        $busiestDays = HourlyRevenue::getBusiestDaysOfWeek(
            $startDate,
            $endDate,
            $businessId,
            $branchId
        );

        return Inertia::render('Admin/HourlyRevenue/Index', [
            'hourlyData' => $hourlyData,
            'peakHours' => $peakHours,
            'hourlyDistribution' => $hourlyDistribution,
            'busiestDays' => $busiestDays,
            'businesses' => $businesses,
            'branches' => $branches,
            'filters' => [
                'business_id' => $businessId,
                'branch_id' => $branchId,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
    }

    /**
     * Get hourly revenue data for AJAX requests
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHourlyData(Request $request)
    {
        $user = Auth::user();
        $businessId = null;
        $branchId = null;

        // Handle permissions
        if ($user->hasRole('super-admin')) {
            $businessId = $request->input('business_id');
        } else {
            $businessId = $user->business_id;
        }

        $branchId = $request->input('branch_id');

        // Get date range
        $startDate = $request->input('start_date', Carbon::now()->subDays(6)->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // Get requested data type
        $dataType = $request->input('data_type', 'hourly_distribution');

        $data = [];

        switch ($dataType) {
            case 'peak_hours':
                $limit = $request->input('limit', 5);
                $data = HourlyRevenue::getPeakHours(
                    $startDate,
                    $endDate,
                    $businessId,
                    $branchId,
                    $limit
                );
                break;

            case 'hourly_distribution':
                $data = HourlyRevenue::getHourlyDistributionForChart(
                    $startDate,
                    $endDate,
                    $businessId,
                    $branchId
                );
                break;

            case 'busiest_days':
                $data = HourlyRevenue::getBusiestDaysOfWeek(
                    $startDate,
                    $endDate,
                    $businessId,
                    $branchId
                );
                break;

            default:
                $data = HourlyRevenue::getHourlyRevenueByDateRange(
                    $startDate,
                    $endDate,
                    $businessId,
                    $branchId
                );
        }

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Manually trigger hourly revenue generation for a specific date
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateForDate(Request $request)
    {
        // Validate request
        $request->validate([
            'date' => 'required|date_format:Y-m-d',
            'business_id' => 'nullable|exists:businesses,id',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $date = $request->input('date');
        $businessId = $request->input('business_id');
        $branchId = $request->input('branch_id');

        // Check permissions
        $user = Auth::user();
        if (!$user->hasRole('super-admin') && $user->business_id != $businessId) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to generate data for this business.',
            ], 403);
        }

        try {
            // Get businesses to process
            $businesses = [];
            if ($businessId) {
                $businesses = Business::where('id', $businessId)->get();
            } else {
                $businesses = Business::all();
            }

            $recordsCreated = 0;
            $recordsUpdated = 0;

            foreach ($businesses as $business) {
                // Get branches to process
                $branches = [];
                if ($branchId) {
                    $branches = Branch::where('id', $branchId)
                        ->where('business_id', $business->id)
                        ->get();
                } else {
                    $branches = Branch::where('business_id', $business->id)->get();
                }

                foreach ($branches as $branch) {
                    // Process hourly revenue for this branch and date
                    $result = HourlyRevenue::generateForBranchAndDate($branch, $date);
                    $recordsCreated += $result['created'];
                    $recordsUpdated += $result['updated'];
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Hourly revenue data generated successfully.",
                'records_created' => $recordsCreated,
                'records_updated' => $recordsUpdated,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => "Error generating hourly revenue data: " . $e->getMessage(),
            ], 500);
        }
    }
}