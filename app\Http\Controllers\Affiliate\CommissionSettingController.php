<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CommissionSettingController extends Controller
{
    /**
     * Display commission settings.
     */
    public function index()
    {
        // TODO: Get commission settings from database
        $settings = [
            'default_commission_rate' => 5.0,
            'tier_rates' => [
                'bronze' => 3.0,
                'silver' => 5.0,
                'gold' => 7.0,
                'platinum' => 10.0,
            ],
            'product_specific_rates' => [],
            'minimum_payout' => 100000, // VND
            'payment_schedule' => 'monthly', // weekly, monthly, quarterly
            'payment_method' => 'bank_transfer',
            'cookie_duration' => 30, // days
            'commission_structure' => 'percentage', // percentage, fixed, tiered
        ];

        return Inertia::render('SuperAdmin/Affiliate/Commission/Settings', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update commission settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'default_commission_rate' => 'required|numeric|min:0|max:100',
            'tier_rates.bronze' => 'required|numeric|min:0|max:100',
            'tier_rates.silver' => 'required|numeric|min:0|max:100',
            'tier_rates.gold' => 'required|numeric|min:0|max:100',
            'tier_rates.platinum' => 'required|numeric|min:0|max:100',
            'minimum_payout' => 'required|numeric|min:0',
            'payment_schedule' => 'required|string|in:weekly,monthly,quarterly',
            'payment_method' => 'required|string|in:bank_transfer,paypal,crypto',
            'cookie_duration' => 'required|integer|min:1|max:365',
            'commission_structure' => 'required|string|in:percentage,fixed,tiered',
        ]);

        // TODO: Save settings to database
        // CommissionSetting::updateOrCreate(
        //     ['key' => 'affiliate_commission_settings'],
        //     ['value' => json_encode($request->all())]
        // );

        return back()->with('success', 'Cài đặt hoa hồng đã được cập nhật thành công.');
    }
}
