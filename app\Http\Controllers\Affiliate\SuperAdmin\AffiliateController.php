<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class AffiliateController extends Controller
{
    /**
     * Display a listing of affiliates.
     */
    public function index(Request $request)
    {
        $query = AffAffiliate::with(['user', 'approver']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('referral_code', 'like', "%{$search}%");
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Apply tier filter
        if ($request->has('tier') && !empty($request->tier)) {
            $query->where('tier', $request->tier);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if ($sortField === 'name') {
            $query->join('users', 'aff_affiliates.user_id', '=', 'users.id')
                ->orderBy('users.name', $sortDirection)
                ->select('aff_affiliates.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $affiliates = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Index', [
            'affiliates' => $affiliates,
            'filters' => $request->only(['search', 'status', 'tier', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new affiliate.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Create');
    }

    /**
     * Store a newly created affiliate.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'tier' => 'required|string|in:bronze,silver,gold,platinum',
            'status' => 'required|string|in:active,inactive,pending',
            'bio' => 'nullable|string|max:1000',
            'website_url' => 'nullable|url|max:255',
        ]);

        // Create user first
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make('password'), // Default password
        ]);

        // Create affiliate record
        $affiliate = AffAffiliate::create([
            'user_id' => $user->id,
            'referral_code' => Str::upper(Str::random(8)),
            'status' => $request->status,
            'tier' => $request->tier,
            'commission_rate' => $request->commission_rate,
            'bio' => $request->bio,
            'website_url' => $request->website_url,
            'approved_at' => $request->status === 'active' ? now() : null,
            'approved_by' => $request->status === 'active' ? auth()->id() : null,
        ]);

        return redirect()->route('superadmin.affiliate.affiliates.index')
            ->with('success', 'Affiliate đã được tạo thành công.');
    }

    /**
     * Display the specified affiliate.
     */
    public function show($id)
    {
        // TODO: Find affiliate by ID
        $affiliate = null; // Placeholder

        if (!$affiliate) {
            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('error', 'Không tìm thấy affiliate.');
        }

        // Get affiliate statistics
        $stats = [
            'total_clicks' => 0,
            'total_conversions' => 0,
            'total_commissions' => 0,
            'conversion_rate' => 0,
        ];

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Show', [
            'affiliate' => $affiliate,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified affiliate.
     */
    public function edit($id)
    {
        // TODO: Find affiliate by ID
        $affiliate = null; // Placeholder

        if (!$affiliate) {
            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('error', 'Không tìm thấy affiliate.');
        }

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Edit', [
            'affiliate' => $affiliate,
        ]);
    }

    /**
     * Update the specified affiliate.
     */
    public function update(Request $request, $id)
    {
        // TODO: Find affiliate by ID
        $affiliate = null; // Placeholder

        if (!$affiliate) {
            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('error', 'Không tìm thấy affiliate.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'tier' => 'required|string|in:bronze,silver,gold,platinum',
            'status' => 'required|string|in:active,inactive,pending',
        ]);

        // TODO: Update affiliate
        // $affiliate->update($request->only([
        //     'name', 'email', 'phone', 'commission_rate', 'tier', 'status'
        // ]));

        return redirect()->route('superadmin.affiliate.affiliates.index')
            ->with('success', 'Affiliate đã được cập nhật thành công.');
    }

    /**
     * Remove the specified affiliate.
     */
    public function destroy($id)
    {
        // TODO: Find and delete affiliate
        // $affiliate = Affiliate::findOrFail($id);
        // $affiliate->delete();

        return redirect()->route('superadmin.affiliate.affiliates.index')
            ->with('success', 'Affiliate đã được xóa thành công.');
    }

    /**
     * Toggle affiliate status.
     */
    public function toggleStatus($id)
    {
        // TODO: Find affiliate and toggle status
        // $affiliate = Affiliate::findOrFail($id);
        // $affiliate->status = $affiliate->status === 'active' ? 'inactive' : 'active';
        // $affiliate->save();

        return back()->with('success', 'Trạng thái affiliate đã được cập nhật.');
    }
}
