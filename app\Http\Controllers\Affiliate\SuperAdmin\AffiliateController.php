<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class AffiliateController extends Controller
{
    /**
     * Display a listing of affiliates.
     */
    public function index(Request $request)
    {
        $query = AffAffiliate::with(['user', 'approver']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('referral_code', 'like', "%{$search}%");
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Apply tier filter
        if ($request->has('tier') && !empty($request->tier)) {
            $query->where('tier', $request->tier);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if ($sortField === 'name') {
            $query->join('users', 'aff_affiliates.user_id', '=', 'users.id')
                ->orderBy('users.name', $sortDirection)
                ->select('aff_affiliates.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $affiliates = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Index', [
            'affiliates' => $affiliates,
            'filters' => $request->only(['search', 'status', 'tier', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new affiliate.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Create');
    }

    /**
     * Store a newly created affiliate.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'tier' => 'required|string|in:bronze,silver,gold,platinum',
            'status' => 'required|string|in:active,inactive,pending',
            'bio' => 'nullable|string|max:1000',
            'website_url' => 'nullable|url|max:255',
        ], [
            'email.unique' => 'Email này đã được sử dụng. Vui lòng chọn email khác.',
            'name.required' => 'Tên là bắt buộc.',
            'email.required' => 'Email là bắt buộc.',
            'email.email' => 'Email không đúng định dạng.',
            'commission_rate.required' => 'Tỷ lệ hoa hồng là bắt buộc.',
            'commission_rate.numeric' => 'Tỷ lệ hoa hồng phải là số.',
            'commission_rate.min' => 'Tỷ lệ hoa hồng không được nhỏ hơn 0%.',
            'commission_rate.max' => 'Tỷ lệ hoa hồng không được lớn hơn 100%.',
            'tier.required' => 'Cấp độ là bắt buộc.',
            'tier.in' => 'Cấp độ không hợp lệ.',
            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái không hợp lệ.',
            'website_url.url' => 'URL website không đúng định dạng.',
        ]);

        // Check if user already exists and is already an affiliate
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            $existingAffiliate = AffAffiliate::where('user_id', $existingUser->id)->first();
            if ($existingAffiliate) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['email' => 'Email này đã được đăng ký làm affiliate. Vui lòng sử dụng email khác.']);
            }
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make('password'),
            ]);

            $affiliate = AffAffiliate::create([
                'user_id' => $user->id,
                'referral_code' => Str::upper(Str::random(8)),
                'status' => $request->status,
                'tier' => $request->tier,
                'commission_rate' => $request->commission_rate,
                'bio' => $request->bio,
                'website_url' => $request->website_url,
                'approved_at' => $request->status === 'active' ? now() : null,
                'approved_by' => $request->status === 'active' ? auth()->id() : null,
            ]);

            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('success', 'Affiliate đã được tạo thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Có lỗi xảy ra khi tạo affiliate. Vui lòng thử lại.']);
        }
    }

    /**
     * Display the specified affiliate.
     */
    public function show($id)
    {
        $affiliate = AffAffiliate::with([
            'user',
            'campaigns.campaign',
            'links',
            'conversions' => function ($query) {
                $query->latest()->take(10);
            },
            'commissions' => function ($query) {
                $query->latest()->take(10);
            },
            'withdrawals' => function ($query) {
                $query->latest()->take(5);
            }
        ])->findOrFail($id);

        $stats = [
            'total_clicks' => $affiliate->total_clicks,
            'total_conversions' => $affiliate->total_conversions,
            'total_earnings' => $affiliate->total_earnings,
            'available_balance' => $affiliate->available_balance,
            'pending_balance' => $affiliate->pending_balance,
            'conversion_rate' => $affiliate->total_clicks > 0
                ? round(($affiliate->total_conversions / $affiliate->total_clicks) * 100, 2)
                : 0,
            'average_order_value' => $affiliate->total_conversions > 0
                ? round($affiliate->total_earnings / $affiliate->total_conversions, 2)
                : 0,
        ];

        $recentActivity = collect()
            ->merge($affiliate->conversions->map(function ($conversion) {
                return [
                    'type' => 'conversion',
                    'description' => "Conversion #{$conversion->id} - " . number_format($conversion->order_value) . ' VND',
                    'date' => $conversion->converted_at,
                    'amount' => $conversion->order_value,
                ];
            }))
            ->merge($affiliate->commissions->map(function ($commission) {
                return [
                    'type' => 'commission',
                    'description' => "Commission earned - " . number_format($commission->amount) . ' VND',
                    'date' => $commission->created_at,
                    'amount' => $commission->amount,
                ];
            }))
            ->sortByDesc('date')
            ->take(10)
            ->values();

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Show', [
            'affiliate' => $affiliate,
            'stats' => $stats,
            'recent_activity' => $recentActivity,
        ]);
    }

    /**
     * Show the form for editing the specified affiliate.
     */
    public function edit($id)
    {
        $affiliate = AffAffiliate::with('user')->findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Affiliates/Edit', [
            'affiliate' => $affiliate,
        ]);
    }

    /**
     * Update the specified affiliate.
     */
    public function update(Request $request, $id)
    {
        $affiliate = AffAffiliate::with('user')->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $affiliate->user_id,
            'phone' => 'nullable|string|max:20',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'tier' => 'required|string|in:bronze,silver,gold,platinum',
            'status' => 'required|string|in:active,inactive,pending,suspended',
            'bio' => 'nullable|string|max:1000',
            'website_url' => 'nullable|url|max:255',
        ], [
            'email.unique' => 'Email này đã được sử dụng. Vui lòng chọn email khác.',
            'name.required' => 'Tên là bắt buộc.',
            'email.required' => 'Email là bắt buộc.',
            'email.email' => 'Email không đúng định dạng.',
            'commission_rate.required' => 'Tỷ lệ hoa hồng là bắt buộc.',
            'commission_rate.numeric' => 'Tỷ lệ hoa hồng phải là số.',
            'commission_rate.min' => 'Tỷ lệ hoa hồng không được nhỏ hơn 0%.',
            'commission_rate.max' => 'Tỷ lệ hoa hồng không được lớn hơn 100%.',
            'tier.required' => 'Cấp độ là bắt buộc.',
            'tier.in' => 'Cấp độ không hợp lệ.',
            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái không hợp lệ.',
            'website_url.url' => 'URL website không đúng định dạng.',
        ]);

        try {
            $affiliate->user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            $affiliate->update([
                'commission_rate' => $request->commission_rate,
                'tier' => $request->tier,
                'status' => $request->status,
                'bio' => $request->bio,
                'website_url' => $request->website_url,
                'approved_at' => $request->status === 'active' && !$affiliate->approved_at ? now() : $affiliate->approved_at,
                'approved_by' => $request->status === 'active' && !$affiliate->approved_by ? auth()->id() : $affiliate->approved_by,
            ]);

            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('success', 'Affiliate đã được cập nhật thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Có lỗi xảy ra khi cập nhật affiliate. Vui lòng thử lại.']);
        }
    }

    /**
     * Remove the specified affiliate.
     */
    public function destroy($id)
    {
        try {
            $affiliate = AffAffiliate::with('user')->findOrFail($id);

            $hasActiveCampaigns = $affiliate->campaigns()->where('status', 'approved')->exists();
            $hasPendingCommissions = $affiliate->commissions()->where('status', 'pending')->exists();

            if ($hasActiveCampaigns || $hasPendingCommissions) {
                return redirect()->back()
                    ->withErrors(['error' => 'Không thể xóa affiliate này vì còn có chiến dịch đang hoạt động hoặc hoa hồng chờ xử lý.']);
            }

            $affiliate->delete();

            $user = $affiliate->user;
            if ($user && $user->roles()->count() === 0) {
                $user->delete();
            }

            return redirect()->route('superadmin.affiliate.affiliates.index')
                ->with('success', 'Affiliate đã được xóa thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Có lỗi xảy ra khi xóa affiliate. Vui lòng thử lại.']);
        }
    }

    /**
     * Toggle affiliate status.
     */
    public function toggleStatus($id)
    {
        try {
            $affiliate = AffAffiliate::findOrFail($id);
            $newStatus = $affiliate->status === 'active' ? 'inactive' : 'active';

            $affiliate->update([
                'status' => $newStatus,
                'approved_at' => $newStatus === 'active' && !$affiliate->approved_at ? now() : $affiliate->approved_at,
                'approved_by' => $newStatus === 'active' && !$affiliate->approved_by ? auth()->id() : $affiliate->approved_by,
            ]);

            $statusText = $newStatus === 'active' ? 'kích hoạt' : 'vô hiệu hóa';

            return back()->with('success', "Affiliate đã được {$statusText} thành công.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Có lỗi xảy ra khi cập nhật trạng thái affiliate.']);
        }
    }
}
