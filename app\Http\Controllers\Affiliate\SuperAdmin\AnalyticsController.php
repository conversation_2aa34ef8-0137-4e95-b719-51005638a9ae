<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Display affiliate analytics.
     */
    public function index(Request $request)
    {
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $affiliateId = $request->input('affiliate_id');
        $campaignId = $request->input('campaign_id');

        try {
            $startDate = Carbon::parse($startDate)->startOfDay();
            $endDate = Carbon::parse($endDate)->endOfDay();
        } catch (\Exception $e) {
            $startDate = now()->subDays(30)->startOfDay();
            $endDate = now()->endOfDay();
        }

        $clicksQuery = AffClick::whereBetween('clicked_at', [$startDate, $endDate]);
        $conversionsQuery = AffConversion::whereBetween('converted_at', [$startDate, $endDate])
            ->where('status', 'approved');
        $commissionsQuery = AffCommission::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', ['approved', 'paid']);

        if ($affiliateId) {
            $clicksQuery->where('affiliate_id', $affiliateId);
            $conversionsQuery->where('affiliate_id', $affiliateId);
            $commissionsQuery->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $clicksQuery->where('campaign_id', $campaignId);
            $conversionsQuery->where('campaign_id', $campaignId);
        }

        $totalClicks = (clone $clicksQuery)->count();
        $totalConversions = (clone $conversionsQuery)->count();
        $totalRevenue = (clone $conversionsQuery)->sum('order_value');
        $totalCommissions = (clone $commissionsQuery)->sum('amount');
        $conversionRate = $totalClicks > 0 ? round(($totalConversions / $totalClicks) * 100, 2) : 0;

        $avgOrderValue = $totalConversions > 0 ? round($totalRevenue / $totalConversions, 2) : 0;
        $customerLifetimeValue = $this->calculateCLV($startDate, $endDate, $affiliateId, $campaignId);
        $returnOnAdSpend = $totalCommissions > 0 ? round($totalRevenue / $totalCommissions, 2) : 0;

        $analytics = [
            'overview' => [
                'total_clicks' => $totalClicks,
                'total_conversions' => $totalConversions,
                'conversion_rate' => $conversionRate,
                'total_revenue' => $totalRevenue,
                'total_commissions' => $totalCommissions,
            ],
            'charts' => [
                'clicks_over_time' => $this->getClicksOverTime($startDate, $endDate, $affiliateId, $campaignId),
                'conversions_over_time' => $this->getConversionsOverTime($startDate, $endDate, $affiliateId, $campaignId),
                'revenue_over_time' => $this->getRevenueOverTime($startDate, $endDate, $affiliateId, $campaignId),
                'top_affiliates' => $this->getTopAffiliates($startDate, $endDate, $campaignId),
                'top_campaigns' => $this->getTopCampaigns($startDate, $endDate, $affiliateId),
            ],
            'performance_metrics' => [
                'avg_order_value' => $avgOrderValue,
                'customer_lifetime_value' => $customerLifetimeValue,
                'return_on_ad_spend' => $returnOnAdSpend,
            ],
        ];

        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'affiliate_id' => $affiliateId,
            'campaign_id' => $campaignId,
        ];

        return Inertia::render('SuperAdmin/Affiliate/Analytics', [
            'analytics' => $analytics,
            'filters' => $filters,
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Get clicks over time data for chart.
     */
    private function getClicksOverTime($startDate, $endDate, $affiliateId = null, $campaignId = null)
    {
        $query = AffClick::selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
            ->whereBetween('clicked_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date');

        if ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $query->where('campaign_id', $campaignId);
        }

        return $query->get()->map(function ($item) {
            return [
                'date' => $item->date,
                'clicks' => $item->clicks,
            ];
        });
    }

    /**
     * Get conversions over time data for chart.
     */
    private function getConversionsOverTime($startDate, $endDate, $affiliateId = null, $campaignId = null)
    {
        $query = AffConversion::selectRaw('DATE(converted_at) as date, COUNT(*) as conversions')
            ->whereBetween('converted_at', [$startDate, $endDate])
            ->where('status', 'approved')
            ->groupBy('date')
            ->orderBy('date');

        if ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $query->where('campaign_id', $campaignId);
        }

        return $query->get()->map(function ($item) {
            return [
                'date' => $item->date,
                'conversions' => $item->conversions,
            ];
        });
    }

    /**
     * Get revenue over time data for chart.
     */
    private function getRevenueOverTime($startDate, $endDate, $affiliateId = null, $campaignId = null)
    {
        $query = AffConversion::selectRaw('DATE(converted_at) as date, SUM(order_value) as revenue')
            ->whereBetween('converted_at', [$startDate, $endDate])
            ->where('status', 'approved')
            ->groupBy('date')
            ->orderBy('date');

        if ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $query->where('campaign_id', $campaignId);
        }

        return $query->get()->map(function ($item) {
            return [
                'date' => $item->date,
                'revenue' => (float) $item->revenue,
            ];
        });
    }

    /**
     * Get top performing affiliates.
     */
    private function getTopAffiliates($startDate, $endDate, $campaignId = null, $limit = 10)
    {
        $query = AffAffiliate::with('user')
            ->select('aff_affiliates.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ?) as clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as revenue,
                (SELECT SUM(amount) FROM aff_commissions WHERE affiliate_id = aff_affiliates.id AND created_at BETWEEN ? AND ? AND status IN ("approved", "paid")) as commissions
            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate])
            ->where('status', 'active');

        if ($campaignId) {
            $query->whereHas('clicks', function ($q) use ($campaignId, $startDate, $endDate) {
                $q->where('campaign_id', $campaignId)
                    ->whereBetween('clicked_at', [$startDate, $endDate]);
            });
        }

        return $query->havingRaw('clicks > 0 OR conversions > 0')
            ->orderByRaw('revenue DESC')
            ->take($limit)
            ->get()
            ->map(function ($affiliate) {
                return [
                    'id' => $affiliate->id,
                    'name' => $affiliate->user->name,
                    'email' => $affiliate->user->email,
                    'tier' => $affiliate->tier,
                    'clicks' => $affiliate->clicks,
                    'conversions' => $affiliate->conversions,
                    'revenue' => (float) $affiliate->revenue,
                    'commissions' => (float) $affiliate->commissions,
                ];
            });
    }

    /**
     * Get top performing campaigns.
     */
    private function getTopCampaigns($startDate, $endDate, $affiliateId = null, $limit = 10)
    {
        $query = AffCampaign::select('aff_campaigns.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE campaign_id = aff_campaigns.id AND clicked_at BETWEEN ? AND ?) as clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE campaign_id = aff_campaigns.id AND converted_at BETWEEN ? AND ? AND status = "approved") as conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE campaign_id = aff_campaigns.id AND converted_at BETWEEN ? AND ? AND status = "approved") as revenue
            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate])
            ->where('status', 'active');

        if ($affiliateId) {
            $query->whereHas('clicks', function ($q) use ($affiliateId, $startDate, $endDate) {
                $q->where('affiliate_id', $affiliateId)
                    ->whereBetween('clicked_at', [$startDate, $endDate]);
            });
        }

        return $query->havingRaw('clicks > 0 OR conversions > 0')
            ->orderByRaw('revenue DESC')
            ->take($limit)
            ->get()
            ->map(function ($campaign) {
                $conversionRate = $campaign->clicks > 0
                    ? round(($campaign->conversions / $campaign->clicks) * 100, 2)
                    : 0;

                return [
                    'id' => $campaign->id,
                    'name' => $campaign->name,
                    'code' => $campaign->campaign_code,
                    'clicks' => $campaign->clicks,
                    'conversions' => $campaign->conversions,
                    'conversion_rate' => $conversionRate,
                    'revenue' => (float) $campaign->revenue,
                ];
            });
    }

    /**
     * Calculate Customer Lifetime Value.
     */
    private function calculateCLV($startDate, $endDate, $affiliateId = null, $campaignId = null)
    {
        $baseQuery = AffConversion::where('status', 'approved')
            ->whereBetween('converted_at', [$startDate, $endDate]);

        if ($affiliateId) {
            $baseQuery->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $baseQuery->where('campaign_id', $campaignId);
        }

        $totalRevenue = (clone $baseQuery)->sum('order_value');

        $uniqueCustomers = (clone $baseQuery)
            ->whereNotNull('customer_id')
            ->distinct('customer_id')
            ->count('customer_id');

        return $uniqueCustomers > 0 ? round($totalRevenue / $uniqueCustomers, 2) : 0;
    }
}
