<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ApplicationController extends Controller
{
    /**
     * Display a listing of affiliate applications.
     */
    public function index(Request $request)
    {
        // TODO: Replace with actual AffiliateApplication model query
        $applications = collect([]); // Placeholder for application data
        
        // Apply filters
        $query = $applications;
        
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            // TODO: Implement search functionality
        }
        
        if ($request->has('status') && !empty($request->status)) {
            $status = $request->status;
            // TODO: Implement status filter (pending, approved, rejected)
        }

        // TODO: Implement pagination
        $paginatedApplications = $query;

        return Inertia::render('SuperAdmin/Affiliate/Applications/Index', [
            'applications' => $paginatedApplications,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Approve an affiliate application.
     */
    public function approve($id)
    {
        // TODO: Find application by ID
        $application = null; // Placeholder

        if (!$application) {
            return back()->with('error', 'Không tìm thấy đơn đăng ký.');
        }

        // TODO: Approve application logic
        // 1. Update application status to 'approved'
        // 2. Create affiliate account
        // 3. Send approval email
        // 4. Generate referral code

        return back()->with('success', 'Đơn đăng ký đã được duyệt thành công.');
    }

    /**
     * Reject an affiliate application.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        // TODO: Find application by ID
        $application = null; // Placeholder

        if (!$application) {
            return back()->with('error', 'Không tìm thấy đơn đăng ký.');
        }

        // TODO: Reject application logic
        // 1. Update application status to 'rejected'
        // 2. Save rejection reason
        // 3. Send rejection email

        return back()->with('success', 'Đơn đăng ký đã được từ chối.');
    }
}
