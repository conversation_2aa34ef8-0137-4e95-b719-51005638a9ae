<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ApplicationController extends Controller
{
    /**
     * Display a listing of affiliate applications.
     */
    public function index(Request $request)
    {
        $query = AffAffiliate::with(['user', 'approver', 'rejector']);

        $status = $request->input('status', 'pending');
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if (in_array($sortField, ['created_at', 'updated_at', 'status'])) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $applications = $query->paginate(20);

        $stats = [
            'total_pending' => AffAffiliate::where('status', 'pending')->count(),
            'total_approved' => AffAffiliate::where('status', 'active')->count(),
            'total_rejected' => AffAffiliate::where('status', 'rejected')->count(),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Applications/Index', [
            'applications' => $applications,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Approve an affiliate application.
     */
    public function approve($id)
    {
        try {
            $affiliate = AffAffiliate::with('user')->findOrFail($id);

            if ($affiliate->status !== 'pending') {
                return back()->with('error', 'Đơn đăng ký này đã được xử lý trước đó.');
            }

            $affiliate->update([
                'status' => 'active',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
            ]);

            if (!$affiliate->referral_code) {
                $affiliate->update([
                    'referral_code' => Str::upper(Str::random(8))
                ]);
            }

            return back()->with('success', 'Đơn đăng ký đã được duyệt thành công.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi duyệt đơn đăng ký.');
        }
    }

    /**
     * Reject an affiliate application.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ], [
            'reason.required' => 'Lý do từ chối là bắt buộc.',
            'reason.max' => 'Lý do từ chối không được vượt quá 500 ký tự.',
        ]);

        try {
            $affiliate = AffAffiliate::with('user')->findOrFail($id);

            if ($affiliate->status !== 'pending') {
                return back()->with('error', 'Đơn đăng ký này đã được xử lý trước đó.');
            }

            $affiliate->update([
                'status' => 'rejected',
                'rejection_reason' => $request->reason,
                'rejected_at' => now(),
                'rejected_by' => Auth::id(),
            ]);

            return back()->with('success', 'Đơn đăng ký đã được từ chối.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi từ chối đơn đăng ký.');
        }
    }
}
