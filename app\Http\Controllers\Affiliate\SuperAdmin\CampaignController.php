<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CampaignController extends Controller
{
    /**
     * Display a listing of campaigns.
     */
    public function index(Request $request)
    {
        $query = AffCampaign::query();
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('campaign_code', 'like', "%{$search}%");
            });
        }

        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if (in_array($sortField, ['name', 'status', 'type', 'commission_rate', 'start_date', 'end_date', 'created_at'])) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $campaigns = $query->paginate(20);

        $stats = [
            'total_campaigns' => AffCampaign::count(),
            'active_campaigns' => AffCampaign::where('status', 'active')->count(),
            'draft_campaigns' => AffCampaign::where('status', 'draft')->count(),
            'completed_campaigns' => AffCampaign::where('status', 'completed')->count(),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Campaigns/Index', [
            'campaigns' => $campaigns,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'type', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new campaign.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Affiliate/Campaigns/Create');
    }

    /**
     * Store a newly created campaign.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:general,product,service,event',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'budget' => 'nullable|numeric|min:0',
            'target_audience' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'status' => 'required|string|in:draft,active,paused,completed',
        ], [
            'name.required' => 'Tên chiến dịch là bắt buộc.',
            'type.required' => 'Loại chiến dịch là bắt buộc.',
            'commission_rate.required' => 'Tỷ lệ hoa hồng là bắt buộc.',
            'commission_rate.numeric' => 'Tỷ lệ hoa hồng phải là số.',
            'commission_rate.min' => 'Tỷ lệ hoa hồng không được nhỏ hơn 0%.',
            'commission_rate.max' => 'Tỷ lệ hoa hồng không được lớn hơn 100%.',
            'start_date.required' => 'Ngày bắt đầu là bắt buộc.',
            'end_date.after' => 'Ngày kết thúc phải sau ngày bắt đầu.',
        ]);

        try {
            $campaign = AffCampaign::create([
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'commission_rate' => $request->commission_rate,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'budget' => $request->budget,
                'target_audience' => $request->target_audience,
                'terms_conditions' => $request->terms_conditions,
                'status' => $request->status,
                'campaign_code' => Str::upper(Str::random(10)),
                'created_by' => Auth::id(),
            ]);

            return redirect()->route('superadmin.affiliate.campaigns.index')
                ->with('success', 'Chiến dịch đã được tạo thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Có lỗi xảy ra khi tạo chiến dịch. Vui lòng thử lại.']);
        }
    }

    /**
     * Display the specified campaign.
     */
    public function show($id)
    {
        $campaign = AffCampaign::with([
            'affiliates.user',
            'links',
            'conversions',
            'commissions'
        ])->findOrFail($id);

        $stats = [
            'total_affiliates' => $campaign->affiliates()->count(),
            'total_clicks' => $campaign->links()->sum('clicks'),
            'total_conversions' => $campaign->conversions()->count(),
            'total_revenue' => $campaign->conversions()->sum('order_value'),
            'total_commissions' => $campaign->commissions()->sum('amount'),
            'conversion_rate' => $campaign->links()->sum('clicks') > 0
                ? round(($campaign->conversions()->count() / $campaign->links()->sum('clicks')) * 100, 2)
                : 0,
        ];

        return Inertia::render('SuperAdmin/Affiliate/Campaigns/Show', [
            'campaign' => $campaign,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified campaign.
     */
    public function edit($id)
    {
        $campaign = AffCampaign::findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Campaigns/Edit', [
            'campaign' => $campaign,
        ]);
    }

    /**
     * Update the specified campaign.
     */
    public function update(Request $request, $id)
    {
        $campaign = AffCampaign::findOrFail($id);



        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:general,product,service,event',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'budget' => 'nullable|numeric|min:0',
            'target_audience' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'status' => 'required|string|in:draft,active,paused,completed',
        ]);

        try {
            $campaign->update($request->only([
                'name',
                'description',
                'type',
                'commission_rate',
                'start_date',
                'end_date',
                'budget',
                'target_audience',
                'terms_conditions',
                'status'
            ]));

            return redirect()->route('superadmin.affiliate.campaigns.index')
                ->with('success', 'Chiến dịch đã được cập nhật thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Có lỗi xảy ra khi cập nhật chiến dịch. Vui lòng thử lại.']);
        }
    }

    /**
     * Remove the specified campaign.
     */
    public function destroy($id)
    {
        try {
            $campaign = AffCampaign::findOrFail($id);
            $hasActiveAffiliates = $campaign->affiliates()->exists();
            $hasConversions = $campaign->conversions()->exists();

            if ($hasActiveAffiliates || $hasConversions) {
                return redirect()->back()
                    ->withErrors(['error' => 'Không thể xóa chiến dịch này vì còn có affiliate hoặc conversion liên quan.']);
            }

            $campaign->delete();

            return redirect()->route('superadmin.affiliate.campaigns.index')
                ->with('success', 'Chiến dịch đã được xóa thành công.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Có lỗi xảy ra khi xóa chiến dịch.']);
        }
    }

    /**
     * Toggle campaign status.
     */
    public function toggleStatus($id)
    {
        try {
            $campaign = AffCampaign::findOrFail($id);
            $newStatus = $campaign->status === 'active' ? 'paused' : 'active';
            $campaign->update(['status' => $newStatus]);

            $statusText = $newStatus === 'active' ? 'kích hoạt' : 'tạm dừng';

            return back()->with('success', "Chiến dịch đã được {$statusText} thành công.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Có lỗi xảy ra khi cập nhật trạng thái chiến dịch.']);
        }
    }
}
