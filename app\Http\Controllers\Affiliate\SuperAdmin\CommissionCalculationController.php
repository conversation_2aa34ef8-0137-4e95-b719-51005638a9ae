<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CommissionCalculationController extends Controller
{
    /**
     * Display commission calculation dashboard.
     */
    public function index(Request $request)
    {
        $pendingConversions = AffConversion::with(['affiliate.user', 'campaign', 'link'])
            ->where('status', 'pending')
            ->whereNull('commission_amount')
            ->orderBy('converted_at', 'desc')
            ->paginate(15);

        $recentCommissions = AffCommission::with(['affiliate.user', 'conversion'])
            ->where('status', 'approved')
            ->latest()
            ->take(10)
            ->get();

        $stats = [
            'pending_conversions' => AffConversion::where('status', 'pending')->whereNull('commission_amount')->count(),
            'total_commissions_today' => AffCommission::whereDate('created_at', today())->sum('amount'),
            'total_commissions_month' => AffCommission::whereMonth('created_at', now()->month)->sum('amount'),
            'avg_commission_rate' => (float) AffCommission::avg('rate') ?: 0,
        ];

        return Inertia::render('SuperAdmin/Affiliate/Commission/Calculator', [
            'pending_conversions' => $pendingConversions,
            'recent_commissions' => $recentCommissions,
            'stats' => $stats,
        ]);
    }

    /**
     * Calculate commission for a specific conversion.
     */
    public function calculate(Request $request, $conversionId)
    {
        $conversion = AffConversion::with(['affiliate', 'campaign', 'link'])
            ->findOrFail($conversionId);

        if ($conversion->status !== 'pending') {
            return back()->withErrors(['error' => 'Chỉ có thể tính hoa hồng cho conversion đang chờ duyệt.']);
        }

        $commissionData = $this->calculateCommissionAmount($conversion);

        $conversion->update([
            'commission_rate' => $commissionData['rate'],
            'commission_amount' => $commissionData['amount'],
        ]);

        return back()->with('success', 'Hoa hồng đã được tính toán: ' . number_format($commissionData['amount']) . ' VND (' . $commissionData['rate'] . '%)');
    }

    /**
     * Bulk calculate commissions for multiple conversions.
     */
    public function bulkCalculate(Request $request)
    {
        $request->validate([
            'conversion_ids' => 'required|array',
            'conversion_ids.*' => 'exists:aff_conversions,id',
        ]);

        $conversions = AffConversion::with(['affiliate', 'campaign', 'link'])
            ->whereIn('id', $request->conversion_ids)
            ->where('status', 'pending')
            ->whereNull('commission_amount')
            ->get();

        $totalCalculated = 0;
        $totalAmount = 0;

        foreach ($conversions as $conversion) {
            $commissionData = $this->calculateCommissionAmount($conversion);

            $conversion->update([
                'commission_rate' => $commissionData['rate'],
                'commission_amount' => $commissionData['amount'],
            ]);

            $totalCalculated++;
            $totalAmount += $commissionData['amount'];
        }

        return back()->with('success', "Đã tính toán hoa hồng cho {$totalCalculated} conversion. Tổng hoa hồng: " . number_format($totalAmount) . ' VND');
    }

    /**
     * Auto-calculate commissions for all pending conversions.
     */
    public function autoCalculate(Request $request)
    {
        $dryRun = $request->boolean('dry_run', false);

        $conversions = AffConversion::with(['affiliate', 'campaign', 'link'])
            ->where('status', 'pending')
            ->whereNull('commission_amount')
            ->get();

        $calculations = [];
        $totalAmount = 0;

        foreach ($conversions as $conversion) {
            $commissionData = $this->calculateCommissionAmount($conversion);

            $calculations[] = [
                'conversion_id' => $conversion->id,
                'affiliate_name' => $conversion->affiliate->user->name,
                'order_value' => $conversion->order_value,
                'commission_rate' => $commissionData['rate'],
                'commission_amount' => $commissionData['amount'],
                'calculation_method' => $commissionData['method'],
            ];

            $totalAmount += $commissionData['amount'];

            if (!$dryRun) {
                $conversion->update([
                    'commission_rate' => $commissionData['rate'],
                    'commission_amount' => $commissionData['amount'],
                ]);
            }
        }

        $message = $dryRun
            ? count($calculations) . ' conversion sẽ được tính hoa hồng. Tổng: ' . number_format($totalAmount) . ' VND'
            : count($calculations) . ' conversion đã được tính hoa hồng. Tổng: ' . number_format($totalAmount) . ' VND';

        return back()->with('success', $message)->with('calculations', $calculations);
    }

    /**
     * Show commission calculation rules.
     */
    public function rules()
    {
        $settings = [
            'default_commission_rate' => AffSetting::get('aff_default_commission_rate', 5.0),
            'tier_rates' => AffSetting::get('aff_tier_rates', [
                'bronze' => 3.0,
                'silver' => 5.0,
                'gold' => 7.0,
                'platinum' => 10.0,
            ]),
            'commission_structure' => AffSetting::get('aff_commission_structure', 'percentage'),
        ];

        $campaignRates = AffCampaign::where('status', 'active')
            ->whereNotNull('commission_rate')
            ->select('id', 'name', 'commission_rate')
            ->get();

        return Inertia::render('SuperAdmin/Affiliate/Commission/Rules', [
            'settings' => $settings,
            'campaign_rates' => $campaignRates,
        ]);
    }

    /**
     * Test commission calculation.
     */
    public function test(Request $request)
    {
        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'campaign_id' => 'nullable|exists:aff_campaigns,id',
            'order_value' => 'required|numeric|min:0',
            'conversion_type' => 'required|string|in:sale,lead,signup',
        ]);

        $affiliate = AffAffiliate::findOrFail($request->affiliate_id);
        $campaign = $request->campaign_id ? AffCampaign::findOrFail($request->campaign_id) : null;

        $mockConversion = new AffConversion([
            'affiliate_id' => $affiliate->id,
            'campaign_id' => $campaign?->id,
            'conversion_type' => $request->conversion_type,
            'order_value' => $request->order_value,
        ]);

        $mockConversion->affiliate = $affiliate;
        $mockConversion->campaign = $campaign;

        $commissionData = $this->calculateCommissionAmount($mockConversion);

        return response()->json([
            'commission_rate' => $commissionData['rate'],
            'commission_amount' => $commissionData['amount'],
            'calculation_method' => $commissionData['method'],
            'breakdown' => $commissionData['breakdown'],
        ]);
    }

    /**
     * Calculate commission amount for a conversion.
     */
    private function calculateCommissionAmount(AffConversion $conversion): array
    {
        $affiliate = $conversion->affiliate;
        $campaign = $conversion->campaign;
        $orderValue = $conversion->order_value;

        $breakdown = [];
        $method = 'default';

        if ($campaign && $campaign->commission_rate) {
            $rate = $campaign->commission_rate;
            $method = 'campaign_specific';
            $breakdown[] = "Tỷ lệ chiến dịch: {$rate}%";
        } elseif ($affiliate->commission_rate) {
            $rate = $affiliate->commission_rate;
            $method = 'affiliate_specific';
            $breakdown[] = "Tỷ lệ affiliate: {$rate}%";
        } else {
            $tierRates = AffSetting::get('aff_tier_rates', [
                'bronze' => 3.0,
                'silver' => 5.0,
                'gold' => 7.0,
                'platinum' => 10.0,
            ]);

            $rate = $tierRates[$affiliate->tier] ?? AffSetting::get('aff_default_commission_rate', 5.0);
            $method = 'tier_based';
            $breakdown[] = "Tỷ lệ tier {$affiliate->tier}: {$rate}%";
        }

        $typeMultipliers = [
            'sale' => 1.0,
            'lead' => 0.5,
            'signup' => 0.3,
        ];

        $multiplier = $typeMultipliers[$conversion->conversion_type] ?? 1.0;
        if ($multiplier !== 1.0) {
            $rate *= $multiplier;
            $breakdown[] = "Hệ số loại conversion ({$conversion->conversion_type}): x{$multiplier}";
        }

        $amount = round(($orderValue * $rate) / 100, 2);

        $minCommission = AffSetting::get('aff_min_commission_amount', 0);
        $maxCommission = AffSetting::get('aff_max_commission_amount', null);

        if ($amount < $minCommission) {
            $amount = $minCommission;
            $breakdown[] = "Áp dụng hoa hồng tối thiểu: " . number_format($minCommission) . " VND";
        }

        if ($maxCommission && $amount > $maxCommission) {
            $amount = $maxCommission;
            $breakdown[] = "Áp dụng hoa hồng tối đa: " . number_format($maxCommission) . " VND";
        }

        return [
            'rate' => round($rate, 2),
            'amount' => $amount,
            'method' => $method,
            'breakdown' => $breakdown,
        ];
    }

    /**
     * Recalculate all commissions for a specific period.
     */
    public function recalculate(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'affiliate_id' => 'nullable|exists:aff_affiliates,id',
            'dry_run' => 'boolean',
        ]);

        $dryRun = $request->boolean('dry_run', true);

        $query = AffConversion::with(['affiliate', 'campaign'])
            ->whereBetween('converted_at', [$request->start_date, $request->end_date])
            ->where('status', 'approved');

        if ($request->affiliate_id) {
            $query->where('affiliate_id', $request->affiliate_id);
        }

        $conversions = $query->get();
        $recalculated = 0;
        $totalDifference = 0;

        foreach ($conversions as $conversion) {
            $oldAmount = $conversion->commission_amount;
            $newCommissionData = $this->calculateCommissionAmount($conversion);
            $newAmount = $newCommissionData['amount'];

            if ($oldAmount != $newAmount) {
                $recalculated++;
                $totalDifference += ($newAmount - $oldAmount);

                if (!$dryRun) {
                    $conversion->update([
                        'commission_rate' => $newCommissionData['rate'],
                        'commission_amount' => $newAmount,
                    ]);

                    $commission = AffCommission::where('conversion_id', $conversion->id)->first();
                    if ($commission) {
                        $commission->update([
                            'amount' => $newAmount,
                            'rate' => $newCommissionData['rate'],
                        ]);
                    }
                }
            }
        }

        $message = $dryRun
            ? "{$recalculated} conversion sẽ được tính lại. Chênh lệch: " . number_format($totalDifference) . " VND"
            : "{$recalculated} conversion đã được tính lại. Chênh lệch: " . number_format($totalDifference) . " VND";

        return back()->with('success', $message);
    }
}
