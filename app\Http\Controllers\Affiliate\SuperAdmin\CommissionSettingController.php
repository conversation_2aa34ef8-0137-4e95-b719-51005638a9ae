<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CommissionSettingController extends Controller
{
    /**
     * Display commission settings.
     */
    public function index()
    {
        $settings = [
            'default_commission_rate' => (float) SystemSetting::getValue('aff_default_commission_rate', 5.0),
            'tier_rates' => [
                'bronze' => (float) SystemSetting::getValue('aff_tier_rate_bronze', 3.0),
                'silver' => (float) SystemSetting::getValue('aff_tier_rate_silver', 5.0),
                'gold' => (float) SystemSetting::getValue('aff_tier_rate_gold', 7.0),
                'platinum' => (float) SystemSetting::getValue('aff_tier_rate_platinum', 10.0),
            ],
            'minimum_payout' => (float) SystemSetting::getValue('aff_minimum_payout', 100000),
            'payment_schedule' => SystemSetting::getValue('aff_payment_schedule', 'monthly'),
            'payment_method' => SystemSetting::getValue('aff_payment_method', 'bank_transfer'),
            'cookie_duration' => (int) SystemSetting::getValue('aff_cookie_duration', 30),
            'commission_structure' => SystemSetting::getValue('aff_commission_structure', 'percentage'),
            'auto_approve_affiliates' => (bool) SystemSetting::getValue('aff_auto_approve_affiliates', false),
            'require_tax_info' => (bool) SystemSetting::getValue('aff_require_tax_info', false),
            'fraud_detection_enabled' => (bool) SystemSetting::getValue('aff_fraud_detection_enabled', true),
            'email_notifications_enabled' => (bool) SystemSetting::getValue('aff_email_notifications_enabled', true),
            'attribution_model' => SystemSetting::getValue('aff_attribution_model', 'last_click'),
            'max_commission_rate' => (float) SystemSetting::getValue('aff_max_commission_rate', 50.0),
            'min_commission_rate' => (float) SystemSetting::getValue('aff_min_commission_rate', 1.0),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Commission/Settings', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update commission settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'default_commission_rate' => 'required|numeric|min:0|max:100',
            'tier_rates.bronze' => 'required|numeric|min:0|max:100',
            'tier_rates.silver' => 'required|numeric|min:0|max:100',
            'tier_rates.gold' => 'required|numeric|min:0|max:100',
            'tier_rates.platinum' => 'required|numeric|min:0|max:100',
            'minimum_payout' => 'required|numeric|min:0',
            'payment_schedule' => 'required|string|in:weekly,monthly,quarterly',
            'payment_method' => 'required|string|in:bank_transfer,paypal,momo,zalopay,vnpay',
            'cookie_duration' => 'required|integer|min:1|max:365',
            'commission_structure' => 'required|string|in:percentage,fixed,tiered',
            'auto_approve_affiliates' => 'boolean',
            'require_tax_info' => 'boolean',
            'fraud_detection_enabled' => 'boolean',
            'email_notifications_enabled' => 'boolean',
            'attribution_model' => 'required|string|in:first_click,last_click,linear',
            'max_commission_rate' => 'required|numeric|min:0|max:100',
            'min_commission_rate' => 'required|numeric|min:0|max:100',
        ], [
            'default_commission_rate.required' => 'Tỷ lệ hoa hồng mặc định là bắt buộc.',
            'minimum_payout.required' => 'Số tiền rút tối thiểu là bắt buộc.',
            'payment_schedule.required' => 'Lịch thanh toán là bắt buộc.',
            'payment_method.required' => 'Phương thức thanh toán là bắt buộc.',
            'cookie_duration.required' => 'Thời gian cookie là bắt buộc.',
            'commission_structure.required' => 'Cấu trúc hoa hồng là bắt buộc.',
        ]);

        try {
            // Save individual settings
            SystemSetting::setValue('aff_default_commission_rate', $request->default_commission_rate, 'affiliate', 'Tỷ lệ hoa hồng mặc định (%)', 'number');
            SystemSetting::setValue('aff_tier_rate_bronze', $request->tier_rates['bronze'], 'affiliate', 'Tỷ lệ hoa hồng Bronze (%)', 'number');
            SystemSetting::setValue('aff_tier_rate_silver', $request->tier_rates['silver'], 'affiliate', 'Tỷ lệ hoa hồng Silver (%)', 'number');
            SystemSetting::setValue('aff_tier_rate_gold', $request->tier_rates['gold'], 'affiliate', 'Tỷ lệ hoa hồng Gold (%)', 'number');
            SystemSetting::setValue('aff_tier_rate_platinum', $request->tier_rates['platinum'], 'affiliate', 'Tỷ lệ hoa hồng Platinum (%)', 'number');
            SystemSetting::setValue('aff_minimum_payout', $request->minimum_payout, 'affiliate', 'Số tiền rút tối thiểu (VND)', 'number');
            SystemSetting::setValue('aff_payment_schedule', $request->payment_schedule, 'affiliate', 'Lịch thanh toán', 'text');
            SystemSetting::setValue('aff_payment_method', $request->payment_method, 'affiliate', 'Phương thức thanh toán', 'text');
            SystemSetting::setValue('aff_cookie_duration', $request->cookie_duration, 'affiliate', 'Thời gian cookie (ngày)', 'number');
            SystemSetting::setValue('aff_commission_structure', $request->commission_structure, 'affiliate', 'Cấu trúc hoa hồng', 'text');
            SystemSetting::setValue('aff_auto_approve_affiliates', $request->auto_approve_affiliates ?? false, 'affiliate', 'Tự động duyệt affiliate', 'boolean');
            SystemSetting::setValue('aff_require_tax_info', $request->require_tax_info ?? false, 'affiliate', 'Yêu cầu thông tin thuế', 'boolean');
            SystemSetting::setValue('aff_fraud_detection_enabled', $request->fraud_detection_enabled ?? true, 'affiliate', 'Bật phát hiện gian lận', 'boolean');
            SystemSetting::setValue('aff_email_notifications_enabled', $request->email_notifications_enabled ?? true, 'affiliate', 'Bật thông báo email', 'boolean');
            SystemSetting::setValue('aff_attribution_model', $request->attribution_model, 'affiliate', 'Mô hình phân bổ', 'text');
            SystemSetting::setValue('aff_max_commission_rate', $request->max_commission_rate, 'affiliate', 'Tỷ lệ hoa hồng tối đa (%)', 'number');
            SystemSetting::setValue('aff_min_commission_rate', $request->min_commission_rate, 'affiliate', 'Tỷ lệ hoa hồng tối thiểu (%)', 'number');

            return back()->with('success', 'Cài đặt hoa hồng đã được cập nhật thành công.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi cập nhật cài đặt. Vui lòng thử lại.');
        }
    }

    /**
     * Get all affiliate settings as an array
     */
    public static function getAllSettings()
    {
        $affiliateSettings = SystemSetting::where('group', 'affiliate')->get();
        $settings = [];

        foreach ($affiliateSettings as $setting) {
            $key = str_replace('aff_', '', $setting->key_setting);
            $settings[$key] = $setting->value;
        }

        return $settings;
    }

    /**
     * Get a specific affiliate setting
     */
    public static function getSetting($key, $default = null)
    {
        return SystemSetting::getValue('aff_' . $key, $default);
    }

    /**
     * Set a specific affiliate setting
     */
    public static function setSetting($key, $value, $displayName = null, $type = 'text')
    {
        return SystemSetting::setValue('aff_' . $key, $value, 'affiliate', $displayName, $type);
    }
}
