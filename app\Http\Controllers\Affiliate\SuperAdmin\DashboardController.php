<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffWithdrawal;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the affiliate dashboard.
     */
    public function index(Request $request)
    {
        // Get affiliate statistics
        $stats = [
            'total_affiliates' => AffAffiliate::count(),
            'active_affiliates' => AffAffiliate::where('status', 'active')->count(),
            'total_commissions' => AffCommission::where('status', 'paid')->sum('amount'),
            'pending_withdrawals' => AffWithdrawal::where('status', 'pending')->count(),
            'total_clicks' => AffClick::count(),
            'total_conversions' => AffConversion::where('status', 'approved')->count(),
            'conversion_rate' => $this->calculateOverallConversionRate(),
            'total_revenue' => AffConversion::where('status', 'approved')->sum('order_value'),
        ];

        // Get recent activities (last 10 activities)
        $recentActivities = collect([
            // Recent affiliate registrations
            ...AffAffiliate::with('user')
                ->latest()
                ->take(5)
                ->get()
                ->map(fn($affiliate) => [
                    'type' => 'affiliate_registration',
                    'message' => "Affiliate mới đăng ký: {$affiliate->user->name}",
                    'time' => $affiliate->created_at,
                    'status' => $affiliate->status,
                ]),

            // Recent conversions
            ...AffConversion::with(['affiliate.user', 'campaign'])
                ->where('status', 'approved')
                ->latest()
                ->take(5)
                ->get()
                ->map(fn($conversion) => [
                    'type' => 'conversion',
                    'message' => "Conversion mới: {$conversion->affiliate->user->name} - " . number_format($conversion->order_value) . " VND",
                    'time' => $conversion->converted_at,
                    'status' => $conversion->status,
                ]),
        ])->sortByDesc('time')->take(10)->values();

        // Get top performing affiliates (by total earnings)
        $topAffiliates = AffAffiliate::with('user')
            ->where('status', 'active')
            ->orderBy('total_earnings', 'desc')
            ->take(5)
            ->get()
            ->map(function ($affiliate) {
                return [
                    'id' => $affiliate->id,
                    'name' => $affiliate->user->name,
                    'email' => $affiliate->user->email,
                    'tier' => $affiliate->tier,
                    'total_earnings' => $affiliate->total_earnings,
                    'conversion_rate' => $affiliate->conversion_rate,
                    'total_clicks' => $affiliate->total_clicks,
                    'total_conversions' => $affiliate->total_conversions,
                ];
            });

        // Get recent registrations (last 7 days)
        $recentRegistrations = AffAffiliate::with('user')
            ->where('created_at', '>=', now()->subDays(7))
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($affiliate) {
                return [
                    'id' => $affiliate->id,
                    'name' => $affiliate->user->name,
                    'email' => $affiliate->user->email,
                    'status' => $affiliate->status,
                    'tier' => $affiliate->tier,
                    'created_at' => $affiliate->created_at,
                ];
            });

        return Inertia::render('SuperAdmin/Affiliate/Dashboard', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
            'topAffiliates' => $topAffiliates,
            'recentRegistrations' => $recentRegistrations,
        ]);
    }

    /**
     * Calculate overall conversion rate.
     */
    private function calculateOverallConversionRate(): float
    {
        $totalClicks = AffClick::count();
        $totalConversions = AffConversion::where('status', 'approved')->count();

        if ($totalClicks == 0) {
            return 0;
        }

        return round(($totalConversions / $totalClicks) * 100, 2);
    }
}
