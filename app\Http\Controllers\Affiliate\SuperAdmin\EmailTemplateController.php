<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffEmailTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of email templates.
     */
    public function index(Request $request)
    {
        $query = AffEmailTemplate::query();
        
        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%");
        }
        
        // Apply type filter
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $templates = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/EmailTemplates/Index', [
            'templates' => $templates,
            'filters' => $request->only(['search', 'type', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new email template.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Affiliate/EmailTemplates/Create');
    }

    /**
     * Store a newly created email template.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:welcome,approval,rejection,commission,withdrawal,reminder',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        AffEmailTemplate::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'type' => $request->type,
            'subject' => $request->subject,
            'body' => $request->body,
            'variables' => $request->variables ?: [],
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('superadmin.affiliate.email.templates.index')
            ->with('success', 'Mẫu email đã được tạo thành công.');
    }

    /**
     * Display the specified email template.
     */
    public function show($id)
    {
        $template = AffEmailTemplate::findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/EmailTemplates/Show', [
            'template' => $template,
        ]);
    }

    /**
     * Show the form for editing the specified email template.
     */
    public function edit($id)
    {
        $template = AffEmailTemplate::findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/EmailTemplates/Edit', [
            'template' => $template,
        ]);
    }

    /**
     * Update the specified email template.
     */
    public function update(Request $request, $id)
    {
        $template = AffEmailTemplate::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:welcome,approval,rejection,commission,withdrawal,reminder',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $template->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'type' => $request->type,
            'subject' => $request->subject,
            'body' => $request->body,
            'variables' => $request->variables ?: [],
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('superadmin.affiliate.email.templates.index')
            ->with('success', 'Mẫu email đã được cập nhật thành công.');
    }

    /**
     * Remove the specified email template.
     */
    public function destroy($id)
    {
        $template = AffEmailTemplate::findOrFail($id);
        $template->delete();

        return redirect()->route('superadmin.affiliate.email.templates.index')
            ->with('success', 'Mẫu email đã được xóa thành công.');
    }

    /**
     * Preview email template.
     */
    public function preview($id)
    {
        $template = AffEmailTemplate::findOrFail($id);
        
        // Sample data for preview
        $sampleData = [
            'affiliate_name' => 'Nguyễn Văn A',
            'affiliate_email' => '<EMAIL>',
            'commission_amount' => '500,000 VND',
            'withdrawal_amount' => '1,000,000 VND',
            'campaign_name' => 'Chiến dịch mùa hè 2025',
            'site_name' => 'Pickleball Platform',
            'site_url' => url('/'),
        ];

        $subject = $this->replaceVariables($template->subject, $sampleData);
        $body = $this->replaceVariables($template->body, $sampleData);

        return response()->json([
            'subject' => $subject,
            'body' => $body,
        ]);
    }

    /**
     * Replace variables in template content.
     */
    private function replaceVariables(string $content, array $data): string
    {
        foreach ($data as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }
        
        return $content;
    }
}
