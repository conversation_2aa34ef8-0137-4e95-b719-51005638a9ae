<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffKolContract;
use App\Models\AffKol;
use App\Models\AffCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class KolContractController extends Controller
{
    /**
     * Display a listing of KOL contracts.
     */
    public function index(Request $request)
    {
        $query = AffKolContract::with(['kol.affiliate.user', 'campaign', 'creator']);
        
        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('title', 'like', "%{$search}%")
                  ->orWhere('contract_number', 'like', "%{$search}%")
                  ->orWhereHas('kol.affiliate.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
        }
        
        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }
        
        // Apply type filter
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $contracts = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/KolContracts/Index', [
            'contracts' => $contracts,
            'filters' => $request->only(['search', 'status', 'type', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new KOL contract.
     */
    public function create()
    {
        $kols = AffKol::with('affiliate.user')->where('is_verified', true)->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/KolContracts/Create', [
            'kols' => $kols,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Store a newly created KOL contract.
     */
    public function store(Request $request)
    {
        $request->validate([
            'kol_id' => 'required|exists:aff_kols,id',
            'campaign_id' => 'nullable|exists:aff_campaigns,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:one_time,recurring,exclusive,non_exclusive',
            'total_value' => 'required|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'fixed_fee' => 'nullable|numeric|min:0',
            'required_posts' => 'required|integer|min:1',
            'deliverables' => 'nullable|array',
            'content_requirements' => 'nullable|array',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'deadline' => 'nullable|date',
            'payment_schedule' => 'nullable|array',
            'terms_conditions' => 'nullable|string',
        ]);

        $contract = AffKolContract::create([
            'kol_id' => $request->kol_id,
            'campaign_id' => $request->campaign_id,
            'contract_number' => $this->generateContractNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'status' => 'draft',
            'total_value' => $request->total_value,
            'commission_rate' => $request->commission_rate,
            'fixed_fee' => $request->fixed_fee,
            'required_posts' => $request->required_posts,
            'completed_posts' => 0,
            'deliverables' => $request->deliverables ?: [],
            'content_requirements' => $request->content_requirements ?: [],
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'deadline' => $request->deadline,
            'payment_schedule' => $request->payment_schedule ?: [],
            'terms_conditions' => $request->terms_conditions,
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('superadmin.affiliate.kol-contracts.index')
            ->with('success', 'Hợp đồng KOL đã được tạo thành công.');
    }

    /**
     * Display the specified KOL contract.
     */
    public function show($id)
    {
        $contract = AffKolContract::with(['kol.affiliate.user', 'campaign', 'creator'])
            ->findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/KolContracts/Show', [
            'contract' => $contract,
        ]);
    }

    /**
     * Show the form for editing the specified KOL contract.
     */
    public function edit($id)
    {
        $contract = AffKolContract::with(['kol.affiliate.user', 'campaign'])
            ->findOrFail($id);
        
        $kols = AffKol::with('affiliate.user')->where('is_verified', true)->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/KolContracts/Edit', [
            'contract' => $contract,
            'kols' => $kols,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Update the specified KOL contract.
     */
    public function update(Request $request, $id)
    {
        $contract = AffKolContract::findOrFail($id);

        // Only allow editing if contract is in draft or sent status
        if (!in_array($contract->status, ['draft', 'sent'])) {
            return back()->withErrors(['error' => 'Không thể chỉnh sửa hợp đồng đã ký hoặc đang hoạt động.']);
        }

        $request->validate([
            'kol_id' => 'required|exists:aff_kols,id',
            'campaign_id' => 'nullable|exists:aff_campaigns,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:one_time,recurring,exclusive,non_exclusive',
            'total_value' => 'required|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'fixed_fee' => 'nullable|numeric|min:0',
            'required_posts' => 'required|integer|min:1',
            'deliverables' => 'nullable|array',
            'content_requirements' => 'nullable|array',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'deadline' => 'nullable|date',
            'payment_schedule' => 'nullable|array',
            'terms_conditions' => 'nullable|string',
        ]);

        $contract->update([
            'kol_id' => $request->kol_id,
            'campaign_id' => $request->campaign_id,
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'total_value' => $request->total_value,
            'commission_rate' => $request->commission_rate,
            'fixed_fee' => $request->fixed_fee,
            'required_posts' => $request->required_posts,
            'deliverables' => $request->deliverables ?: [],
            'content_requirements' => $request->content_requirements ?: [],
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'deadline' => $request->deadline,
            'payment_schedule' => $request->payment_schedule ?: [],
            'terms_conditions' => $request->terms_conditions,
        ]);

        return redirect()->route('superadmin.affiliate.kol-contracts.index')
            ->with('success', 'Hợp đồng KOL đã được cập nhật thành công.');
    }

    /**
     * Remove the specified KOL contract.
     */
    public function destroy($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        // Only allow deletion if contract is in draft status
        if ($contract->status !== 'draft') {
            return back()->withErrors(['error' => 'Chỉ có thể xóa hợp đồng ở trạng thái nháp.']);
        }

        $contract->delete();

        return redirect()->route('superadmin.affiliate.kol-contracts.index')
            ->with('success', 'Hợp đồng KOL đã được xóa thành công.');
    }

    /**
     * Send contract to KOL.
     */
    public function send($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if ($contract->status !== 'draft') {
            return back()->withErrors(['error' => 'Chỉ có thể gửi hợp đồng ở trạng thái nháp.']);
        }

        $contract->send();

        // TODO: Send email notification to KOL

        return back()->with('success', 'Hợp đồng đã được gửi đến KOL.');
    }

    /**
     * Mark contract as signed.
     */
    public function markSigned($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if ($contract->status !== 'sent') {
            return back()->withErrors(['error' => 'Chỉ có thể ký hợp đồng đã được gửi.']);
        }

        $contract->markAsSigned();

        return back()->with('success', 'Hợp đồng đã được đánh dấu là đã ký.');
    }

    /**
     * Activate contract.
     */
    public function activate($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if ($contract->status !== 'signed') {
            return back()->withErrors(['error' => 'Chỉ có thể kích hoạt hợp đồng đã ký.']);
        }

        $contract->activate();

        return back()->with('success', 'Hợp đồng đã được kích hoạt.');
    }

    /**
     * Complete contract.
     */
    public function complete($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if ($contract->status !== 'active') {
            return back()->withErrors(['error' => 'Chỉ có thể hoàn thành hợp đồng đang hoạt động.']);
        }

        $contract->complete();

        return back()->with('success', 'Hợp đồng đã được hoàn thành.');
    }

    /**
     * Cancel contract.
     */
    public function cancel($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if (in_array($contract->status, ['completed', 'cancelled'])) {
            return back()->withErrors(['error' => 'Không thể hủy hợp đồng đã hoàn thành hoặc đã hủy.']);
        }

        $contract->cancel();

        return back()->with('success', 'Hợp đồng đã được hủy.');
    }

    /**
     * Add completed post to contract.
     */
    public function addCompletedPost($id)
    {
        $contract = AffKolContract::findOrFail($id);
        
        if ($contract->status !== 'active') {
            return back()->withErrors(['error' => 'Chỉ có thể cập nhật tiến độ cho hợp đồng đang hoạt động.']);
        }

        $contract->addCompletedPost();

        $message = $contract->isCompleted() 
            ? 'Đã cập nhật tiến độ và hoàn thành hợp đồng.'
            : 'Đã cập nhật tiến độ thực hiện.';

        return back()->with('success', $message);
    }

    /**
     * Generate unique contract number.
     */
    private function generateContractNumber(): string
    {
        $prefix = 'KOL';
        $year = now()->format('Y');
        $month = now()->format('m');
        
        // Get the last contract number for this month
        $lastContract = AffKolContract::where('contract_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('contract_number', 'desc')
            ->first();

        if ($lastContract) {
            $lastNumber = (int) substr($lastContract->contract_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return "{$prefix}{$year}{$month}{$newNumber}";
    }
}
