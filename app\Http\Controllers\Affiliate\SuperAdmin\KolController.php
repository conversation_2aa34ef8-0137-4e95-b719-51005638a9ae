<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffKol;
use App\Models\AffAffiliate;
use Illuminate\Http\Request;
use Inertia\Inertia;

class <PERSON><PERSON><PERSON>ontroller extends Controller
{
    /**
     * Display a listing of KOLs.
     */
    public function index(Request $request)
    {
        $query = AffKol::with(['affiliate.user', 'verifier']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('stage_name', 'like', "%{$search}%")
                ->orWhereHas('affiliate.user', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
        }

        // Apply category filter
        if ($request->has('category') && !empty($request->category)) {
            $query->where('category', $request->category);
        }

        // Apply tier filter
        if ($request->has('tier') && !empty($request->tier)) {
            $query->where('tier', $request->tier);
        }

        // Apply verification filter
        if ($request->has('verified') && $request->verified !== '') {
            $query->where('is_verified', $request->boolean('verified'));
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if ($sortField === 'name') {
            $query->join('aff_affiliates', 'aff_kols.affiliate_id', '=', 'aff_affiliates.id')
                ->join('users', 'aff_affiliates.user_id', '=', 'users.id')
                ->orderBy('users.name', $sortDirection)
                ->select('aff_kols.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $kols = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/Kols/Index', [
            'kols' => $kols,
            'filters' => $request->only(['search', 'category', 'tier', 'verified', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new KOL.
     */
    public function create()
    {
        // Get affiliates that don't have KOL profiles yet
        $availableAffiliates = AffAffiliate::with('user')
            ->whereDoesntHave('kol')
            ->where('status', 'active')
            ->get();

        return Inertia::render('SuperAdmin/Affiliate/Kols/Create', [
            'availableAffiliates' => $availableAffiliates,
        ]);
    }

    /**
     * Store a newly created KOL.
     */
    public function store(Request $request)
    {
        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'stage_name' => 'nullable|string|max:255',
            'category' => 'required|string|in:influencer,blogger,youtuber,tiktoker,celebrity,expert',
            'niches' => 'nullable|array',
            'followers_count' => 'required|integer|min:0',
            'engagement_rate' => 'required|numeric|min:0|max:100',
            'social_stats' => 'nullable|array',
            'base_rate' => 'nullable|numeric|min:0',
            'preferred_content_type' => 'nullable|string|max:100',
            'content_samples' => 'nullable|array',
            'media_kit_url' => 'nullable|url|max:255',
            'tier' => 'required|string|in:micro,macro,mega,celebrity',
            'audience_demographics' => 'nullable|array',
            'average_views' => 'nullable|integer|min:0',
            'average_likes' => 'nullable|integer|min:0',
            'average_comments' => 'nullable|integer|min:0',
            'average_shares' => 'nullable|integer|min:0',
        ]);

        // Check if affiliate already has a KOL profile
        $existingKol = AffKol::where('affiliate_id', $request->affiliate_id)->first();
        if ($existingKol) {
            return back()->withErrors(['affiliate_id' => 'Affiliate này đã có profile KOL.']);
        }

        AffKol::create([
            'affiliate_id' => $request->affiliate_id,
            'stage_name' => $request->stage_name,
            'category' => $request->category,
            'niches' => $request->niches ?: [],
            'followers_count' => $request->followers_count,
            'engagement_rate' => $request->engagement_rate,
            'social_stats' => $request->social_stats ?: [],
            'base_rate' => $request->base_rate,
            'preferred_content_type' => $request->preferred_content_type,
            'content_samples' => $request->content_samples ?: [],
            'media_kit_url' => $request->media_kit_url,
            'tier' => $request->tier,
            'audience_demographics' => $request->audience_demographics ?: [],
            'average_views' => $request->average_views ?: 0,
            'average_likes' => $request->average_likes ?: 0,
            'average_comments' => $request->average_comments ?: 0,
            'average_shares' => $request->average_shares ?: 0,
        ]);

        return redirect()->route('superadmin.affiliate.kols.index')
            ->with('success', 'Profile KOL đã được tạo thành công.');
    }

    /**
     * Display the specified KOL.
     */
    public function show($id)
    {
        $kol = AffKol::with(['affiliate.user', 'verifier', 'contracts.campaign'])
            ->findOrFail($id);

        // Get KOL performance statistics
        $stats = [
            'total_contracts' => $kol->contracts()->count(),
            'active_contracts' => $kol->contracts()->where('status', 'active')->count(),
            'completed_contracts' => $kol->contracts()->where('status', 'completed')->count(),
            'total_contract_value' => $kol->contracts()->sum('total_value'),
            'avg_contract_value' => $kol->contracts()->avg('total_value'),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Kols/Show', [
            'kol' => $kol,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified KOL.
     */
    public function edit($id)
    {
        $kol = AffKol::with('affiliate.user')->findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Kols/Edit', [
            'kol' => $kol,
        ]);
    }

    /**
     * Update the specified KOL.
     */
    public function update(Request $request, $id)
    {
        $kol = AffKol::findOrFail($id);

        $request->validate([
            'stage_name' => 'nullable|string|max:255',
            'category' => 'required|string|in:influencer,blogger,youtuber,tiktoker,celebrity,expert',
            'niches' => 'nullable|array',
            'followers_count' => 'required|integer|min:0',
            'engagement_rate' => 'required|numeric|min:0|max:100',
            'social_stats' => 'nullable|array',
            'base_rate' => 'nullable|numeric|min:0',
            'preferred_content_type' => 'nullable|string|max:100',
            'content_samples' => 'nullable|array',
            'media_kit_url' => 'nullable|url|max:255',
            'tier' => 'required|string|in:micro,macro,mega,celebrity',
            'audience_demographics' => 'nullable|array',
            'average_views' => 'nullable|integer|min:0',
            'average_likes' => 'nullable|integer|min:0',
            'average_comments' => 'nullable|integer|min:0',
            'average_shares' => 'nullable|integer|min:0',
        ]);

        $kol->update([
            'stage_name' => $request->stage_name,
            'category' => $request->category,
            'niches' => $request->niches ?: [],
            'followers_count' => $request->followers_count,
            'engagement_rate' => $request->engagement_rate,
            'social_stats' => $request->social_stats ?: [],
            'base_rate' => $request->base_rate,
            'preferred_content_type' => $request->preferred_content_type,
            'content_samples' => $request->content_samples ?: [],
            'media_kit_url' => $request->media_kit_url,
            'tier' => $request->tier,
            'audience_demographics' => $request->audience_demographics ?: [],
            'average_views' => $request->average_views ?: 0,
            'average_likes' => $request->average_likes ?: 0,
            'average_comments' => $request->average_comments ?: 0,
            'average_shares' => $request->average_shares ?: 0,
        ]);

        return redirect()->route('superadmin.affiliate.kols.index')
            ->with('success', 'Profile KOL đã được cập nhật thành công.');
    }

    /**
     * Remove the specified KOL.
     */
    public function destroy($id)
    {
        $kol = AffKol::findOrFail($id);

        // Check if KOL has active contracts
        if ($kol->contracts()->whereIn('status', ['active', 'signed'])->exists()) {
            return back()->withErrors(['error' => 'Không thể xóa KOL có hợp đồng đang hoạt động.']);
        }

        $kol->delete();

        return redirect()->route('superadmin.affiliate.kols.index')
            ->with('success', 'Profile KOL đã được xóa thành công.');
    }

    /**
     * Verify KOL profile.
     */
    public function verify($id)
    {
        $kol = AffKol::findOrFail($id);
        $kol->verify(auth()->id());

        return back()->with('success', 'KOL đã được xác minh thành công.');
    }

    /**
     * Unverify KOL profile.
     */
    public function unverify($id)
    {
        $kol = AffKol::findOrFail($id);
        $kol->unverify();

        return back()->with('success', 'Đã hủy xác minh KOL.');
    }

    /**
     * Bulk verify KOLs.
     */
    public function bulkVerify(Request $request)
    {
        $request->validate([
            'kol_ids' => 'required|array',
            'kol_ids.*' => 'exists:aff_kols,id',
        ]);

        $updated = AffKol::whereIn('id', $request->kol_ids)
            ->update([
                'is_verified' => true,
                'verified_at' => now(),
                'verified_by' => auth()->id(),
            ]);

        return back()->with('success', "{$updated} KOL đã được xác minh thành công.");
    }

    /**
     * Export KOL list.
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'csv');

        $kols = AffKol::with(['affiliate.user'])
            ->when($request->category, fn($q) => $q->where('category', $request->category))
            ->when($request->tier, fn($q) => $q->where('tier', $request->tier))
            ->when($request->has('verified'), fn($q) => $q->where('is_verified', $request->boolean('verified')))
            ->get();

        if ($format === 'csv') {
            return $this->exportToCsv($kols);
        }

        return back()->with('error', 'Định dạng xuất không được hỗ trợ.');
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($kols)
    {
        $filename = "kol_list_" . now()->format('Y-m-d') . ".csv";

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($kols) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Tên thật',
                'Tên sân khấu',
                'Email',
                'Danh mục',
                'Tier',
                'Số followers',
                'Tỷ lệ tương tác (%)',
                'Giá cơ bản (VND)',
                'Đã xác minh',
                'Ngày tạo'
            ]);

            // Add data rows
            foreach ($kols as $kol) {
                fputcsv($file, [
                    $kol->id,
                    $kol->affiliate->user->name,
                    $kol->stage_name ?: '-',
                    $kol->affiliate->user->email,
                    $kol->category,
                    $kol->tier,
                    number_format($kol->followers_count),
                    $kol->engagement_rate,
                    $kol->base_rate ? number_format($kol->base_rate) : '-',
                    $kol->is_verified ? 'Có' : 'Không',
                    $kol->created_at->format('d/m/Y'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
