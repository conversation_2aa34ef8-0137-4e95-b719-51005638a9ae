<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffLink;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LinkController extends Controller
{
    /**
     * Display a listing of affiliate links.
     */
    public function index(Request $request)
    {
        $query = AffLink::with(['affiliate.user', 'campaign']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('title', 'like', "%{$search}%")
                ->orWhere('short_code', 'like', "%{$search}%")
                ->orWhere('original_url', 'like', "%{$search}%")
                ->orWhereHas('affiliate.user', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
        }

        // Apply affiliate filter
        if ($request->has('affiliate_id') && !empty($request->affiliate_id)) {
            $query->where('affiliate_id', $request->affiliate_id);
        }

        // Apply campaign filter
        if ($request->has('campaign_id') && !empty($request->campaign_id)) {
            $query->where('campaign_id', $request->campaign_id);
        }

        // Apply type filter
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        // Apply status filter
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $links = $query->paginate(15)->withQueryString();

        // Get filter options
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Links/Index', [
            'links' => $links,
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
            'filters' => $request->only(['search', 'affiliate_id', 'campaign_id', 'type', 'is_active', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new affiliate link.
     */
    public function create()
    {
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Links/Create', [
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Store a newly created affiliate link.
     */
    public function store(Request $request)
    {
        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'campaign_id' => 'nullable|exists:aff_campaigns,id',
            'original_url' => 'required|url|max:2000',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:product,category,landing_page,custom',
            'utm_source' => 'nullable|string|max:100',
            'utm_medium' => 'nullable|string|max:100',
            'utm_campaign' => 'nullable|string|max:100',
            'utm_content' => 'nullable|string|max:100',
            'utm_term' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        $shortCode = $this->generateUniqueShortCode();

        $link = AffLink::create([
            'affiliate_id' => $request->affiliate_id,
            'campaign_id' => $request->campaign_id,
            'short_code' => $shortCode,
            'original_url' => $request->original_url,
            'affiliate_url' => $this->generateAffiliateUrl($shortCode),
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'utm_source' => $request->utm_source,
            'utm_medium' => $request->utm_medium,
            'utm_campaign' => $request->utm_campaign,
            'utm_content' => $request->utm_content,
            'utm_term' => $request->utm_term,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('superadmin.affiliate.links.index')
            ->with('success', 'Link affiliate đã được tạo thành công.');
    }

    /**
     * Display the specified affiliate link.
     */
    public function show($id)
    {
        $link = AffLink::with(['affiliate.user', 'campaign', 'clickRecords', 'linkConversions'])
            ->findOrFail($id);

        // Get link statistics
        $stats = [
            'total_clicks' => $link->clicks,
            'unique_clicks' => $link->unique_clicks,
            'total_conversions' => $link->conversions,
            'total_revenue' => $link->revenue,
            'total_commissions' => $link->commissions,
            'conversion_rate' => $link->conversion_rate,
            'click_through_rate' => $link->click_through_rate,
            'last_clicked' => $link->last_clicked_at,
        ];

        // Get recent clicks (last 10)
        $recentClicks = $link->clickRecords()
            ->with('affiliate.user')
            ->latest('clicked_at')
            ->take(10)
            ->get();

        // Get click sources breakdown
        $clickSources = $link->clickRecords()
            ->selectRaw('referrer_url, COUNT(*) as count')
            ->groupBy('referrer_url')
            ->orderByDesc('count')
            ->take(10)
            ->get();

        return Inertia::render('SuperAdmin/Affiliate/Links/Show', [
            'link' => $link,
            'stats' => $stats,
            'recent_clicks' => $recentClicks,
            'click_sources' => $clickSources,
        ]);
    }

    /**
     * Show the form for editing the specified affiliate link.
     */
    public function edit($id)
    {
        $link = AffLink::with(['affiliate.user', 'campaign'])->findOrFail($id);
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Links/Edit', [
            'link' => $link,
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
        ]);
    }

    /**
     * Update the specified affiliate link.
     */
    public function update(Request $request, $id)
    {
        $link = AffLink::findOrFail($id);

        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'campaign_id' => 'nullable|exists:aff_campaigns,id',
            'original_url' => 'required|url|max:2000',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:product,category,landing_page,custom',
            'utm_source' => 'nullable|string|max:100',
            'utm_medium' => 'nullable|string|max:100',
            'utm_campaign' => 'nullable|string|max:100',
            'utm_content' => 'nullable|string|max:100',
            'utm_term' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        $link->update([
            'affiliate_id' => $request->affiliate_id,
            'campaign_id' => $request->campaign_id,
            'original_url' => $request->original_url,
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'utm_source' => $request->utm_source,
            'utm_medium' => $request->utm_medium,
            'utm_campaign' => $request->utm_campaign,
            'utm_content' => $request->utm_content,
            'utm_term' => $request->utm_term,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('superadmin.affiliate.links.index')
            ->with('success', 'Link affiliate đã được cập nhật thành công.');
    }

    /**
     * Remove the specified affiliate link.
     */
    public function destroy($id)
    {
        $link = AffLink::findOrFail($id);

        // Check if link has clicks or conversions
        if ($link->clicks > 0) {
            return back()->withErrors(['error' => 'Không thể xóa link đã có lượt click.']);
        }

        $link->delete();

        return redirect()->route('superadmin.affiliate.links.index')
            ->with('success', 'Link affiliate đã được xóa thành công.');
    }

    /**
     * Toggle link status.
     */
    public function toggleStatus($id)
    {
        $link = AffLink::findOrFail($id);
        $link->update(['is_active' => !$link->is_active]);

        $status = $link->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return back()->with('success', "Link đã được {$status}.");
    }

    /**
     * Bulk toggle status.
     */
    public function bulkToggleStatus(Request $request)
    {
        $request->validate([
            'link_ids' => 'required|array',
            'link_ids.*' => 'exists:aff_links,id',
            'is_active' => 'required|boolean',
        ]);

        $updated = AffLink::whereIn('id', $request->link_ids)
            ->update(['is_active' => $request->is_active]);

        $status = $request->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return back()->with('success', "{$updated} link đã được {$status}.");
    }

    /**
     * Generate unique short code.
     */
    private function generateUniqueShortCode(): string
    {
        do {
            $shortCode = Str::random(8);
        } while (AffLink::where('short_code', $shortCode)->exists());

        return $shortCode;
    }

    /**
     * Generate affiliate URL.
     */
    private function generateAffiliateUrl(string $shortCode): string
    {
        return url("/aff/{$shortCode}");
    }

    /**
     * Refresh link statistics.
     */
    public function refreshStats($id)
    {
        $link = AffLink::findOrFail($id);
        $link->updateStats();

        return back()->with('success', 'Thống kê link đã được cập nhật.');
    }

    /**
     * Export links data.
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'csv');

        $query = AffLink::with(['affiliate.user', 'campaign']);

        // Apply same filters as index
        if ($request->affiliate_id) {
            $query->where('affiliate_id', $request->affiliate_id);
        }
        if ($request->campaign_id) {
            $query->where('campaign_id', $request->campaign_id);
        }
        if ($request->type) {
            $query->where('type', $request->type);
        }
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $links = $query->get();

        if ($format === 'csv') {
            return $this->exportToCsv($links);
        }

        return back()->with('error', 'Định dạng xuất không được hỗ trợ.');
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($links)
    {
        $filename = "affiliate_links_" . now()->format('Y-m-d') . ".csv";

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($links) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Affiliate',
                'Chiến dịch',
                'Tiêu đề',
                'Loại',
                'URL gốc',
                'URL affiliate',
                'Clicks',
                'Unique Clicks',
                'Conversions',
                'Tỷ lệ chuyển đổi (%)',
                'Doanh thu (VND)',
                'Hoa hồng (VND)',
                'Trạng thái',
                'Ngày tạo'
            ]);

            // Add data rows
            foreach ($links as $link) {
                fputcsv($file, [
                    $link->id,
                    $link->affiliate->user->name,
                    $link->campaign->name ?? '-',
                    $link->title ?: '-',
                    $link->type,
                    $link->original_url,
                    $link->affiliate_url,
                    $link->clicks,
                    $link->unique_clicks,
                    $link->conversions,
                    $link->conversion_rate,
                    number_format($link->revenue, 0, ',', '.'),
                    number_format($link->commissions, 0, ',', '.'),
                    $link->is_active ? 'Hoạt động' : 'Không hoạt động',
                    $link->created_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
