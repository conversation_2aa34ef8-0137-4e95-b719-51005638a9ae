<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffCommission;
use App\Models\AffWithdrawal;
use App\Models\AffAffiliate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class PaymentHistoryController extends Controller
{
    /**
     * Display payment history.
     */
    public function index(Request $request)
    {
        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $affiliateId = $request->input('affiliate_id');
        $paymentType = $request->input('payment_type', 'all'); // all, commissions, withdrawals

        // Get payment history data
        $payments = collect();

        if ($paymentType === 'all' || $paymentType === 'commissions') {
            // Get commission payments
            $commissions = AffCommission::with(['affiliate.user', 'conversion'])
                ->where('status', 'paid')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
                ->get()
                ->map(function ($commission) {
                    return [
                        'id' => $commission->id,
                        'type' => 'commission',
                        'affiliate_id' => $commission->affiliate_id,
                        'affiliate_name' => $commission->affiliate->user->name,
                        'affiliate_email' => $commission->affiliate->user->email,
                        'amount' => $commission->amount,
                        'description' => "Hoa hồng từ conversion #{$commission->conversion_id}",
                        'payment_method' => 'system',
                        'reference' => $commission->payment_reference,
                        'date' => $commission->paid_at,
                        'status' => 'completed',
                        'processed_by' => $commission->payer->name ?? 'System',
                    ];
                });

            $payments = $payments->merge($commissions);
        }

        if ($paymentType === 'all' || $paymentType === 'withdrawals') {
            // Get withdrawal payments
            $withdrawals = AffWithdrawal::with(['affiliate.user', 'processor'])
                ->where('status', 'completed')
                ->whereBetween('completed_at', [$startDate, $endDate])
                ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
                ->get()
                ->map(function ($withdrawal) {
                    return [
                        'id' => $withdrawal->id,
                        'type' => 'withdrawal',
                        'affiliate_id' => $withdrawal->affiliate_id,
                        'affiliate_name' => $withdrawal->affiliate->user->name,
                        'affiliate_email' => $withdrawal->affiliate->user->email,
                        'amount' => $withdrawal->net_amount,
                        'description' => "Rút tiền - Phí: " . number_format($withdrawal->fee) . " VND",
                        'payment_method' => $withdrawal->payment_method,
                        'reference' => $withdrawal->transaction_id,
                        'date' => $withdrawal->completed_at,
                        'status' => 'completed',
                        'processed_by' => $withdrawal->processor->name ?? 'System',
                    ];
                });

            $payments = $payments->merge($withdrawals);
        }

        // Sort by date descending
        $payments = $payments->sortByDesc('date')->values();

        // Paginate manually
        $perPage = 15;
        $currentPage = $request->input('page', 1);
        $total = $payments->count();
        $paginatedPayments = $payments->slice(($currentPage - 1) * $perPage, $perPage)->values();

        // Calculate summary statistics
        $totalPaid = $payments->sum('amount');
        $totalCommissions = $payments->where('type', 'commission')->sum('amount');
        $totalWithdrawals = $payments->where('type', 'withdrawal')->sum('amount');
        $totalTransactions = $payments->count();

        // Get payment method breakdown
        $paymentMethods = $payments->groupBy('payment_method')->map(function ($group) {
            return [
                'count' => $group->count(),
                'total' => $group->sum('amount'),
            ];
        });

        // Get monthly payment trends
        $monthlyTrends = $payments->groupBy(function ($payment) {
            return Carbon::parse($payment['date'])->format('Y-m');
        })->map(function ($group, $month) {
            return [
                'month' => $month,
                'total_amount' => $group->sum('amount'),
                'commission_amount' => $group->where('type', 'commission')->sum('amount'),
                'withdrawal_amount' => $group->where('type', 'withdrawal')->sum('amount'),
                'transaction_count' => $group->count(),
            ];
        })->values();

        // Get available affiliates for filter
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Payments/History', [
            'payments' => [
                'data' => $paginatedPayments,
                'current_page' => $currentPage,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
            ],
            'summary' => [
                'total_paid' => $totalPaid,
                'total_commissions' => $totalCommissions,
                'total_withdrawals' => $totalWithdrawals,
                'total_transactions' => $totalTransactions,
            ],
            'payment_methods' => $paymentMethods,
            'monthly_trends' => $monthlyTrends,
            'affiliates' => $affiliates,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'affiliate_id' => $affiliateId,
                'payment_type' => $paymentType,
            ],
        ]);
    }

    /**
     * Export payment history.
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'csv');
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $affiliateId = $request->input('affiliate_id');
        $paymentType = $request->input('payment_type', 'all');

        // Get all payment data (similar to index method but without pagination)
        $payments = collect();

        if ($paymentType === 'all' || $paymentType === 'commissions') {
            $commissions = AffCommission::with(['affiliate.user', 'conversion', 'payer'])
                ->where('status', 'paid')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
                ->get()
                ->map(function ($commission) {
                    return [
                        'type' => 'Hoa hồng',
                        'affiliate_name' => $commission->affiliate->user->name,
                        'affiliate_email' => $commission->affiliate->user->email,
                        'amount' => $commission->amount,
                        'description' => "Hoa hồng từ conversion #{$commission->conversion_id}",
                        'payment_method' => 'Hệ thống',
                        'reference' => $commission->payment_reference ?: '-',
                        'date' => $commission->paid_at->format('d/m/Y H:i'),
                        'processed_by' => $commission->payer->name ?? 'Hệ thống',
                    ];
                });

            $payments = $payments->merge($commissions);
        }

        if ($paymentType === 'all' || $paymentType === 'withdrawals') {
            $withdrawals = AffWithdrawal::with(['affiliate.user', 'processor'])
                ->where('status', 'completed')
                ->whereBetween('completed_at', [$startDate, $endDate])
                ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
                ->get()
                ->map(function ($withdrawal) {
                    return [
                        'type' => 'Rút tiền',
                        'affiliate_name' => $withdrawal->affiliate->user->name,
                        'affiliate_email' => $withdrawal->affiliate->user->email,
                        'amount' => $withdrawal->net_amount,
                        'description' => "Rút tiền (Phí: " . number_format($withdrawal->fee) . " VND)",
                        'payment_method' => ucfirst(str_replace('_', ' ', $withdrawal->payment_method)),
                        'reference' => $withdrawal->transaction_id ?: '-',
                        'date' => $withdrawal->completed_at->format('d/m/Y H:i'),
                        'processed_by' => $withdrawal->processor->name ?? 'Hệ thống',
                    ];
                });

            $payments = $payments->merge($withdrawals);
        }

        // Sort by date descending
        $payments = $payments->sortByDesc('date')->values();

        if ($format === 'csv') {
            return $this->exportToCsv($payments, $startDate, $endDate);
        }

        return back()->with('error', 'Định dạng xuất không được hỗ trợ.');
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($payments, $startDate, $endDate)
    {
        $filename = "payment_history_{$startDate}_to_{$endDate}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Add CSV headers
            fputcsv($file, [
                'Loại thanh toán',
                'Tên Affiliate',
                'Email Affiliate',
                'Số tiền (VND)',
                'Mô tả',
                'Phương thức',
                'Mã tham chiếu',
                'Ngày thanh toán',
                'Người xử lý'
            ]);

            // Add data rows
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment['type'],
                    $payment['affiliate_name'],
                    $payment['affiliate_email'],
                    number_format($payment['amount'], 0, ',', '.'),
                    $payment['description'],
                    $payment['payment_method'],
                    $payment['reference'],
                    $payment['date'],
                    $payment['processed_by'],
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
