<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffPolicy;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PolicyController extends Controller
{
    /**
     * Display a listing of policies.
     */
    public function index(Request $request)
    {
        $query = AffPolicy::query();
        
        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('title', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%");
        }
        
        // Apply type filter
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $policies = $query->paginate(15)->withQueryString();

        return Inertia::render('SuperAdmin/Affiliate/Policies/Index', [
            'policies' => $policies,
            'filters' => $request->only(['search', 'type', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new policy.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Affiliate/Policies/Create');
    }

    /**
     * Store a newly created policy.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:terms,privacy,commission,conduct,guidelines',
            'content' => 'required|string',
            'version' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'effective_date' => 'nullable|date',
        ]);

        AffPolicy::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'type' => $request->type,
            'content' => $request->content,
            'version' => $request->version ?: '1.0',
            'is_active' => $request->boolean('is_active', true),
            'effective_date' => $request->effective_date ?: now(),
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('superadmin.affiliate.policies.index')
            ->with('success', 'Chính sách đã được tạo thành công.');
    }

    /**
     * Display the specified policy.
     */
    public function show($id)
    {
        $policy = AffPolicy::with('creator')->findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Policies/Show', [
            'policy' => $policy,
        ]);
    }

    /**
     * Show the form for editing the specified policy.
     */
    public function edit($id)
    {
        $policy = AffPolicy::findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Policies/Edit', [
            'policy' => $policy,
        ]);
    }

    /**
     * Update the specified policy.
     */
    public function update(Request $request, $id)
    {
        $policy = AffPolicy::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:terms,privacy,commission,conduct,guidelines',
            'content' => 'required|string',
            'version' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'effective_date' => 'nullable|date',
        ]);

        $policy->update([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'type' => $request->type,
            'content' => $request->content,
            'version' => $request->version,
            'is_active' => $request->boolean('is_active'),
            'effective_date' => $request->effective_date,
        ]);

        return redirect()->route('superadmin.affiliate.policies.index')
            ->with('success', 'Chính sách đã được cập nhật thành công.');
    }

    /**
     * Remove the specified policy.
     */
    public function destroy($id)
    {
        $policy = AffPolicy::findOrFail($id);
        $policy->delete();

        return redirect()->route('superadmin.affiliate.policies.index')
            ->with('success', 'Chính sách đã được xóa thành công.');
    }
}
