<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffCommission;
use App\Models\AffConversion;
use App\Models\AffClick;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class RankingController extends Controller
{
    /**
     * Display affiliate rankings.
     */
    public function index(Request $request)
    {
        // Get date range from request or default to current month
        $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $rankingType = $request->input('ranking_type', 'earnings'); // earnings, conversions, clicks, revenue
        $period = $request->input('period', 'month'); // week, month, quarter, year

        // Adjust date range based on period
        if ($request->input('period') && !$request->input('start_date')) {
            switch ($period) {
                case 'week':
                    $startDate = now()->startOfWeek()->format('Y-m-d');
                    $endDate = now()->endOfWeek()->format('Y-m-d');
                    break;
                case 'month':
                    $startDate = now()->startOfMonth()->format('Y-m-d');
                    $endDate = now()->endOfMonth()->format('Y-m-d');
                    break;
                case 'quarter':
                    $startDate = now()->startOfQuarter()->format('Y-m-d');
                    $endDate = now()->endOfQuarter()->format('Y-m-d');
                    break;
                case 'year':
                    $startDate = now()->startOfYear()->format('Y-m-d');
                    $endDate = now()->endOfYear()->format('Y-m-d');
                    break;
            }
        }

        // Build ranking query
        $query = AffAffiliate::with('user')
            ->select('aff_affiliates.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ?) as period_clicks,
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ? AND is_unique = 1) as period_unique_clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as period_conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as period_revenue,
                (SELECT SUM(amount) FROM aff_commissions WHERE affiliate_id = aff_affiliates.id AND created_at BETWEEN ? AND ? AND status IN ("approved", "paid")) as period_earnings
            ', [
                $startDate, $endDate, $startDate, $endDate, $startDate, $endDate,
                $startDate, $endDate, $startDate, $endDate
            ])
            ->where('status', 'active');

        // Apply ranking order
        switch ($rankingType) {
            case 'earnings':
                $query->orderByRaw('period_earnings DESC NULLS LAST');
                break;
            case 'conversions':
                $query->orderByRaw('period_conversions DESC');
                break;
            case 'clicks':
                $query->orderByRaw('period_clicks DESC');
                break;
            case 'revenue':
                $query->orderByRaw('period_revenue DESC NULLS LAST');
                break;
            default:
                $query->orderByRaw('period_earnings DESC NULLS LAST');
        }

        $rankings = $query->paginate(20)->withQueryString();

        // Add ranking position and calculate metrics
        $rankings->getCollection()->transform(function ($affiliate, $index) {
            $affiliate->ranking_position = $index + 1 + (($rankings->currentPage() - 1) * $rankings->perPage());
            $affiliate->period_conversion_rate = $affiliate->period_clicks > 0 
                ? round(($affiliate->period_conversions / $affiliate->period_clicks) * 100, 2) 
                : 0;
            $affiliate->period_epc = $affiliate->period_clicks > 0 
                ? round($affiliate->period_earnings / $affiliate->period_clicks, 2) 
                : 0;
            $affiliate->period_aov = $affiliate->period_conversions > 0 
                ? round($affiliate->period_revenue / $affiliate->period_conversions, 2) 
                : 0;
            
            return $affiliate;
        });

        // Get top performers summary
        $topPerformers = [
            'top_earner' => $rankings->first(),
            'top_converter' => AffAffiliate::with('user')
                ->selectRaw('aff_affiliates.*, (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as period_conversions', [$startDate, $endDate])
                ->where('status', 'active')
                ->orderByRaw('period_conversions DESC')
                ->first(),
            'top_clicker' => AffAffiliate::with('user')
                ->selectRaw('aff_affiliates.*, (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ?) as period_clicks', [$startDate, $endDate])
                ->where('status', 'active')
                ->orderByRaw('period_clicks DESC')
                ->first(),
        ];

        // Get tier distribution
        $tierDistribution = AffAffiliate::where('status', 'active')
            ->selectRaw('tier, COUNT(*) as count')
            ->groupBy('tier')
            ->get()
            ->keyBy('tier');

        // Get performance trends (last 6 periods)
        $trends = [];
        for ($i = 5; $i >= 0; $i--) {
            $periodStart = match($period) {
                'week' => now()->subWeeks($i)->startOfWeek(),
                'month' => now()->subMonths($i)->startOfMonth(),
                'quarter' => now()->subQuarters($i)->startOfQuarter(),
                'year' => now()->subYears($i)->startOfYear(),
            };
            
            $periodEnd = match($period) {
                'week' => now()->subWeeks($i)->endOfWeek(),
                'month' => now()->subMonths($i)->endOfMonth(),
                'quarter' => now()->subQuarters($i)->endOfQuarter(),
                'year' => now()->subYears($i)->endOfYear(),
            };

            $periodData = [
                'period' => $periodStart->format(match($period) {
                    'week' => 'W',
                    'month' => 'M/Y',
                    'quarter' => 'Q Y',
                    'year' => 'Y',
                }),
                'total_earnings' => AffCommission::whereBetween('created_at', [$periodStart, $periodEnd])
                    ->whereIn('status', ['approved', 'paid'])
                    ->sum('amount'),
                'total_conversions' => AffConversion::whereBetween('converted_at', [$periodStart, $periodEnd])
                    ->where('status', 'approved')
                    ->count(),
                'total_clicks' => AffClick::whereBetween('clicked_at', [$periodStart, $periodEnd])
                    ->count(),
            ];

            $trends[] = $periodData;
        }

        return Inertia::render('SuperAdmin/Affiliate/Rankings/Index', [
            'rankings' => $rankings,
            'top_performers' => $topPerformers,
            'tier_distribution' => $tierDistribution,
            'trends' => $trends,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'ranking_type' => $rankingType,
                'period' => $period,
            ],
        ]);
    }

    /**
     * Update affiliate tier based on performance.
     */
    public function updateTier(Request $request, $id)
    {
        $affiliate = AffAffiliate::findOrFail($id);

        $request->validate([
            'tier' => 'required|string|in:bronze,silver,gold,platinum',
            'reason' => 'nullable|string|max:500',
        ]);

        $oldTier = $affiliate->tier;
        $affiliate->update([
            'tier' => $request->tier,
        ]);

        // Log tier change
        // TODO: Create tier change log model and record the change

        return back()->with('success', "Tier của affiliate đã được cập nhật từ {$oldTier} thành {$request->tier}.");
    }

    /**
     * Auto-promote affiliates based on performance criteria.
     */
    public function autoPromote(Request $request)
    {
        $period = $request->input('period', 'month');
        $dryRun = $request->boolean('dry_run', true);

        // Define promotion criteria
        $criteria = [
            'bronze_to_silver' => [
                'min_earnings' => 1000000, // 1M VND
                'min_conversions' => 10,
                'min_conversion_rate' => 2.0,
            ],
            'silver_to_gold' => [
                'min_earnings' => 5000000, // 5M VND
                'min_conversions' => 50,
                'min_conversion_rate' => 3.0,
            ],
            'gold_to_platinum' => [
                'min_earnings' => 20000000, // 20M VND
                'min_conversions' => 200,
                'min_conversion_rate' => 4.0,
            ],
        ];

        // Get date range based on period
        $startDate = match($period) {
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'quarter' => now()->subQuarter(),
            'year' => now()->subYear(),
        };

        $promotions = [];

        foreach ($criteria as $promotion => $rules) {
            [$fromTier, $toTier] = explode('_to_', $promotion);

            $candidates = AffAffiliate::with('user')
                ->where('tier', $fromTier)
                ->where('status', 'active')
                ->selectRaw('
                    aff_affiliates.*,
                    (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at >= ?) as period_clicks,
                    (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at >= ? AND status = "approved") as period_conversions,
                    (SELECT SUM(amount) FROM aff_commissions WHERE affiliate_id = aff_affiliates.id AND created_at >= ? AND status IN ("approved", "paid")) as period_earnings
                ', [$startDate, $startDate, $startDate])
                ->havingRaw('period_earnings >= ?', [$rules['min_earnings']])
                ->havingRaw('period_conversions >= ?', [$rules['min_conversions']])
                ->havingRaw('(period_conversions / GREATEST(period_clicks, 1)) * 100 >= ?', [$rules['min_conversion_rate']])
                ->get();

            foreach ($candidates as $candidate) {
                $promotions[] = [
                    'affiliate' => $candidate,
                    'from_tier' => $fromTier,
                    'to_tier' => $toTier,
                    'period_earnings' => $candidate->period_earnings,
                    'period_conversions' => $candidate->period_conversions,
                    'period_clicks' => $candidate->period_clicks,
                    'conversion_rate' => $candidate->period_clicks > 0 
                        ? round(($candidate->period_conversions / $candidate->period_clicks) * 100, 2) 
                        : 0,
                ];

                // Actually promote if not dry run
                if (!$dryRun) {
                    $candidate->update(['tier' => $toTier]);
                    // TODO: Send promotion notification email
                }
            }
        }

        $message = $dryRun 
            ? count($promotions) . ' affiliate đủ điều kiện thăng hạng.'
            : count($promotions) . ' affiliate đã được thăng hạng thành công.';

        return back()->with('success', $message)->with('promotions', $promotions);
    }
}
