<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffReferralCode;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ReferralCodeController extends Controller
{
    /**
     * Display a listing of referral codes.
     */
    public function index(Request $request)
    {
        $query = AffReferralCode::with(['affiliate.user']);
        
        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('affiliate.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                  });
        }
        
        // Apply affiliate filter
        if ($request->has('affiliate_id') && !empty($request->affiliate_id)) {
            $query->where('affiliate_id', $request->affiliate_id);
        }
        
        // Apply status filter
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $codes = $query->paginate(15)->withQueryString();

        // Get filter options
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/ReferralCodes/Index', [
            'codes' => $codes,
            'affiliates' => $affiliates,
            'filters' => $request->only(['search', 'affiliate_id', 'is_active', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new referral code.
     */
    public function create()
    {
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/ReferralCodes/Create', [
            'affiliates' => $affiliates,
        ]);
    }

    /**
     * Store a newly created referral code.
     */
    public function store(Request $request)
    {
        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'code' => 'required|string|max:50|unique:aff_referral_codes,code',
            'description' => 'nullable|string|max:255',
            'discount_type' => 'required|string|in:percentage,fixed,free_shipping',
            'discount_value' => 'required|numeric|min:0',
            'minimum_order_value' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'valid_from' => 'required|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'is_active' => 'boolean',
        ]);

        // Additional validation for discount value
        if ($request->discount_type === 'percentage' && $request->discount_value > 100) {
            return back()->withErrors(['discount_value' => 'Giá trị giảm giá phần trăm không được vượt quá 100%.']);
        }

        AffReferralCode::create([
            'affiliate_id' => $request->affiliate_id,
            'code' => strtoupper($request->code),
            'description' => $request->description,
            'discount_type' => $request->discount_type,
            'discount_value' => $request->discount_value,
            'minimum_order_value' => $request->minimum_order_value,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_customer' => $request->usage_limit_per_customer,
            'valid_from' => $request->valid_from,
            'valid_until' => $request->valid_until,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('superadmin.affiliate.referral-codes.index')
            ->with('success', 'Mã giới thiệu đã được tạo thành công.');
    }

    /**
     * Display the specified referral code.
     */
    public function show($id)
    {
        $code = AffReferralCode::with(['affiliate.user', 'usages.customer'])
            ->findOrFail($id);

        // Get usage statistics
        $stats = [
            'total_uses' => $code->used_count,
            'remaining_uses' => $code->usage_limit ? max(0, $code->usage_limit - $code->used_count) : null,
            'total_discount_given' => $code->usages->sum('discount_amount'),
            'total_orders' => $code->usages->count(),
            'average_order_value' => $code->usages->avg('order_value'),
            'unique_customers' => $code->usages->unique('customer_id')->count(),
        ];

        // Get recent usages
        $recentUsages = $code->usages()
            ->with('customer')
            ->latest('used_at')
            ->take(10)
            ->get();

        return Inertia::render('SuperAdmin/Affiliate/ReferralCodes/Show', [
            'code' => $code,
            'stats' => $stats,
            'recent_usages' => $recentUsages,
        ]);
    }

    /**
     * Show the form for editing the specified referral code.
     */
    public function edit($id)
    {
        $code = AffReferralCode::with('affiliate.user')->findOrFail($id);
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/ReferralCodes/Edit', [
            'code' => $code,
            'affiliates' => $affiliates,
        ]);
    }

    /**
     * Update the specified referral code.
     */
    public function update(Request $request, $id)
    {
        $code = AffReferralCode::findOrFail($id);

        $request->validate([
            'affiliate_id' => 'required|exists:aff_affiliates,id',
            'code' => 'required|string|max:50|unique:aff_referral_codes,code,' . $id,
            'description' => 'nullable|string|max:255',
            'discount_type' => 'required|string|in:percentage,fixed,free_shipping',
            'discount_value' => 'required|numeric|min:0',
            'minimum_order_value' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'valid_from' => 'required|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'is_active' => 'boolean',
        ]);

        // Additional validation for discount value
        if ($request->discount_type === 'percentage' && $request->discount_value > 100) {
            return back()->withErrors(['discount_value' => 'Giá trị giảm giá phần trăm không được vượt quá 100%.']);
        }

        $code->update([
            'affiliate_id' => $request->affiliate_id,
            'code' => strtoupper($request->code),
            'description' => $request->description,
            'discount_type' => $request->discount_type,
            'discount_value' => $request->discount_value,
            'minimum_order_value' => $request->minimum_order_value,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_customer' => $request->usage_limit_per_customer,
            'valid_from' => $request->valid_from,
            'valid_until' => $request->valid_until,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('superadmin.affiliate.referral-codes.index')
            ->with('success', 'Mã giới thiệu đã được cập nhật thành công.');
    }

    /**
     * Remove the specified referral code.
     */
    public function destroy($id)
    {
        $code = AffReferralCode::findOrFail($id);
        
        // Check if code has been used
        if ($code->used_count > 0) {
            return back()->withErrors(['error' => 'Không thể xóa mã giới thiệu đã được sử dụng.']);
        }

        $code->delete();

        return redirect()->route('superadmin.affiliate.referral-codes.index')
            ->with('success', 'Mã giới thiệu đã được xóa thành công.');
    }

    /**
     * Toggle code status.
     */
    public function toggleStatus($id)
    {
        $code = AffReferralCode::findOrFail($id);
        $code->update(['is_active' => !$code->is_active]);

        $status = $code->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return back()->with('success', "Mã giới thiệu đã được {$status}.");
    }

    /**
     * Generate random referral code.
     */
    public function generateCode(Request $request)
    {
        $request->validate([
            'prefix' => 'nullable|string|max:10',
            'length' => 'required|integer|min:4|max:20',
        ]);

        $prefix = strtoupper($request->prefix ?: '');
        $length = $request->length - strlen($prefix);
        
        do {
            $randomPart = strtoupper(Str::random($length));
            $code = $prefix . $randomPart;
        } while (AffReferralCode::where('code', $code)->exists());

        return response()->json(['code' => $code]);
    }

    /**
     * Bulk toggle status.
     */
    public function bulkToggleStatus(Request $request)
    {
        $request->validate([
            'code_ids' => 'required|array',
            'code_ids.*' => 'exists:aff_referral_codes,id',
            'is_active' => 'required|boolean',
        ]);

        $updated = AffReferralCode::whereIn('id', $request->code_ids)
            ->update(['is_active' => $request->is_active]);

        $status = $request->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return back()->with('success', "{$updated} mã giới thiệu đã được {$status}.");
    }

    /**
     * Export referral codes data.
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'csv');
        
        $query = AffReferralCode::with(['affiliate.user']);
        
        // Apply same filters as index
        if ($request->affiliate_id) {
            $query->where('affiliate_id', $request->affiliate_id);
        }
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $codes = $query->get();

        if ($format === 'csv') {
            return $this->exportToCsv($codes);
        }

        return back()->with('error', 'Định dạng xuất không được hỗ trợ.');
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($codes)
    {
        $filename = "referral_codes_" . now()->format('Y-m-d') . ".csv";
        
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($codes) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Affiliate',
                'Mã',
                'Mô tả',
                'Loại giảm giá',
                'Giá trị giảm giá',
                'Đơn hàng tối thiểu',
                'Giới hạn sử dụng',
                'Đã sử dụng',
                'Có hiệu lực từ',
                'Có hiệu lực đến',
                'Trạng thái',
                'Ngày tạo'
            ]);

            // Add data rows
            foreach ($codes as $code) {
                fputcsv($file, [
                    $code->id,
                    $code->affiliate->user->name,
                    $code->code,
                    $code->description ?: '-',
                    $code->discount_type,
                    $code->discount_value,
                    $code->minimum_order_value ? number_format($code->minimum_order_value) : '-',
                    $code->usage_limit ?: 'Không giới hạn',
                    $code->used_count,
                    $code->valid_from->format('d/m/Y'),
                    $code->valid_until ? $code->valid_until->format('d/m/Y') : 'Không giới hạn',
                    $code->is_active ? 'Hoạt động' : 'Không hoạt động',
                    $code->created_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
