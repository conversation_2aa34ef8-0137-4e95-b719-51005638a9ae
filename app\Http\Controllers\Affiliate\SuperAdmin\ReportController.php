<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display performance report.
     */
    public function performance(Request $request)
    {
        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));
        $affiliateId = $request->input('affiliate_id');
        $campaignId = $request->input('campaign_id');

        // Build base query
        $clicksQuery = AffClick::whereBetween('clicked_at', [$startDate, $endDate]);
        $conversionsQuery = AffConversion::whereBetween('converted_at', [$startDate, $endDate]);
        $commissionsQuery = AffCommission::whereBetween('created_at', [$startDate, $endDate]);

        // Apply filters
        if ($affiliateId) {
            $clicksQuery->where('affiliate_id', $affiliateId);
            $conversionsQuery->where('affiliate_id', $affiliateId);
            $commissionsQuery->where('affiliate_id', $affiliateId);
        }

        if ($campaignId) {
            $clicksQuery->where('campaign_id', $campaignId);
            $conversionsQuery->where('campaign_id', $campaignId);
        }

        // Get performance metrics
        $totalClicks = $clicksQuery->count();
        $uniqueClicks = $clicksQuery->where('is_unique', true)->count();
        $totalConversions = $conversionsQuery->where('status', 'approved')->count();
        $totalRevenue = $conversionsQuery->where('status', 'approved')->sum('order_value');
        $totalCommissions = $commissionsQuery->where('status', 'approved')->sum('amount');

        $conversionRate = $totalClicks > 0 ? round(($totalConversions / $totalClicks) * 100, 2) : 0;
        $avgOrderValue = $totalConversions > 0 ? round($totalRevenue / $totalConversions, 2) : 0;
        $epc = $totalClicks > 0 ? round($totalCommissions / $totalClicks, 2) : 0; // Earnings per click

        // Get top performing affiliates
        $topAffiliates = AffAffiliate::with('user')
            ->select('aff_affiliates.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ?) as clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as revenue,
                (SELECT SUM(amount) FROM aff_commissions WHERE affiliate_id = aff_affiliates.id AND created_at BETWEEN ? AND ? AND status = "approved") as commissions
            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate])
            ->havingRaw('clicks > 0 OR conversions > 0')
            ->orderByRaw('commissions DESC')
            ->take(10)
            ->get();

        // Get top performing campaigns
        $topCampaigns = AffCampaign::select('aff_campaigns.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE campaign_id = aff_campaigns.id AND clicked_at BETWEEN ? AND ?) as clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE campaign_id = aff_campaigns.id AND converted_at BETWEEN ? AND ? AND status = "approved") as conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE campaign_id = aff_campaigns.id AND converted_at BETWEEN ? AND ? AND status = "approved") as revenue
            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate])
            ->havingRaw('clicks > 0 OR conversions > 0')
            ->orderByRaw('revenue DESC')
            ->take(10)
            ->get();

        // Get daily performance data for charts
        $dailyData = DB::table('aff_clicks')
            ->selectRaw('DATE(clicked_at) as date, COUNT(*) as clicks')
            ->whereBetween('clicked_at', [$startDate, $endDate])
            ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
            ->when($campaignId, fn($q) => $q->where('campaign_id', $campaignId))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $dailyConversions = DB::table('aff_conversions')
            ->selectRaw('DATE(converted_at) as date, COUNT(*) as conversions, SUM(order_value) as revenue')
            ->whereBetween('converted_at', [$startDate, $endDate])
            ->where('status', 'approved')
            ->when($affiliateId, fn($q) => $q->where('affiliate_id', $affiliateId))
            ->when($campaignId, fn($q) => $q->where('campaign_id', $campaignId))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Merge daily data
        $chartData = [];
        $period = Carbon::parse($startDate)->daysUntil(Carbon::parse($endDate));
        
        foreach ($period as $date) {
            $dateStr = $date->format('Y-m-d');
            $chartData[] = [
                'date' => $dateStr,
                'clicks' => $dailyData[$dateStr]->clicks ?? 0,
                'conversions' => $dailyConversions[$dateStr]->conversions ?? 0,
                'revenue' => $dailyConversions[$dateStr]->revenue ?? 0,
            ];
        }

        // Get available affiliates and campaigns for filters
        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Reports/Performance', [
            'metrics' => [
                'total_clicks' => $totalClicks,
                'unique_clicks' => $uniqueClicks,
                'total_conversions' => $totalConversions,
                'total_revenue' => $totalRevenue,
                'total_commissions' => $totalCommissions,
                'conversion_rate' => $conversionRate,
                'avg_order_value' => $avgOrderValue,
                'earnings_per_click' => $epc,
            ],
            'top_affiliates' => $topAffiliates,
            'top_campaigns' => $topCampaigns,
            'chart_data' => $chartData,
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'affiliate_id' => $affiliateId,
                'campaign_id' => $campaignId,
            ],
        ]);
    }

    /**
     * Export performance report.
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'csv');
        $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));

        // Get detailed affiliate performance data
        $affiliateData = AffAffiliate::with('user')
            ->select('aff_affiliates.*')
            ->selectRaw('
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ?) as clicks,
                (SELECT COUNT(*) FROM aff_clicks WHERE affiliate_id = aff_affiliates.id AND clicked_at BETWEEN ? AND ? AND is_unique = 1) as unique_clicks,
                (SELECT COUNT(*) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as conversions,
                (SELECT SUM(order_value) FROM aff_conversions WHERE affiliate_id = aff_affiliates.id AND converted_at BETWEEN ? AND ? AND status = "approved") as revenue,
                (SELECT SUM(amount) FROM aff_commissions WHERE affiliate_id = aff_affiliates.id AND created_at BETWEEN ? AND ? AND status = "approved") as commissions
            ', [
                $startDate, $endDate, $startDate, $endDate, $startDate, $endDate, 
                $startDate, $endDate, $startDate, $endDate
            ])
            ->get();

        if ($format === 'csv') {
            return $this->exportToCsv($affiliateData, $startDate, $endDate);
        } elseif ($format === 'excel') {
            return $this->exportToExcel($affiliateData, $startDate, $endDate);
        }

        return back()->with('error', 'Định dạng xuất không được hỗ trợ.');
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($data, $startDate, $endDate)
    {
        $filename = "affiliate_report_{$startDate}_to_{$endDate}.csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($file, [
                'Affiliate ID',
                'Name',
                'Email',
                'Tier',
                'Status',
                'Clicks',
                'Unique Clicks',
                'Conversions',
                'Conversion Rate (%)',
                'Revenue (VND)',
                'Commissions (VND)',
                'EPC (VND)'
            ]);

            // Add data rows
            foreach ($data as $affiliate) {
                $conversionRate = $affiliate->clicks > 0 ? round(($affiliate->conversions / $affiliate->clicks) * 100, 2) : 0;
                $epc = $affiliate->clicks > 0 ? round($affiliate->commissions / $affiliate->clicks, 2) : 0;

                fputcsv($file, [
                    $affiliate->id,
                    $affiliate->user->name,
                    $affiliate->user->email,
                    $affiliate->tier,
                    $affiliate->status,
                    $affiliate->clicks,
                    $affiliate->unique_clicks,
                    $affiliate->conversions,
                    $conversionRate,
                    $affiliate->revenue,
                    $affiliate->commissions,
                    $epc,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to Excel format (simplified CSV for now).
     */
    private function exportToExcel($data, $startDate, $endDate)
    {
        // For now, return CSV with .xlsx extension
        // In production, you might want to use a proper Excel library like PhpSpreadsheet
        return $this->exportToCsv($data, $startDate, $endDate);
    }
}
