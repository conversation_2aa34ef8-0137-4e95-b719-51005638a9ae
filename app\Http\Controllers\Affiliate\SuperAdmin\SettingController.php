<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SettingController extends Controller
{
    /**
     * Display general settings.
     */
    public function general()
    {
        $settings = [
            // General Settings
            'aff_system_enabled' => AffSetting::get('aff_system_enabled', true),
            'aff_auto_approve_affiliates' => AffSetting::get('aff_auto_approve_affiliates', false),
            'aff_require_approval' => AffSetting::get('aff_require_approval', true),
            'aff_allow_self_registration' => AffSetting::get('aff_allow_self_registration', true),
            
            // Commission Settings
            'aff_default_commission_rate' => AffSetting::get('aff_default_commission_rate', 5.0),
            'aff_tier_rates' => AffSetting::get('aff_tier_rates', [
                'bronze' => 3.0,
                'silver' => 5.0,
                'gold' => 7.0,
                'platinum' => 10.0,
            ]),
            'aff_commission_structure' => AffSetting::get('aff_commission_structure', 'percentage'),
            
            // Payment Settings
            'aff_minimum_payout' => AffSetting::get('aff_minimum_payout', 100000),
            'aff_payment_schedule' => AffSetting::get('aff_payment_schedule', 'monthly'),
            'aff_payment_methods' => AffSetting::get('aff_payment_methods', ['bank_transfer', 'momo', 'zalopay']),
            'aff_withdrawal_fee' => AffSetting::get('aff_withdrawal_fee', 0),
            
            // Tracking Settings
            'aff_cookie_duration' => AffSetting::get('aff_cookie_duration', 30),
            'aff_track_subaffiliates' => AffSetting::get('aff_track_subaffiliates', false),
            'aff_attribution_model' => AffSetting::get('aff_attribution_model', 'last_click'),
            
            // Email Settings
            'aff_welcome_email_enabled' => AffSetting::get('aff_welcome_email_enabled', true),
            'aff_commission_email_enabled' => AffSetting::get('aff_commission_email_enabled', true),
            'aff_withdrawal_email_enabled' => AffSetting::get('aff_withdrawal_email_enabled', true),
            'aff_monthly_report_enabled' => AffSetting::get('aff_monthly_report_enabled', true),
            
            // Security Settings
            'aff_max_login_attempts' => AffSetting::get('aff_max_login_attempts', 5),
            'aff_session_timeout' => AffSetting::get('aff_session_timeout', 120),
            'aff_require_2fa' => AffSetting::get('aff_require_2fa', false),
            
            // Content Settings
            'aff_terms_url' => AffSetting::get('aff_terms_url', ''),
            'aff_privacy_url' => AffSetting::get('aff_privacy_url', ''),
            'aff_support_email' => AffSetting::get('aff_support_email', '<EMAIL>'),
            'aff_support_phone' => AffSetting::get('aff_support_phone', ''),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Settings/General', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            // General Settings
            'aff_system_enabled' => 'boolean',
            'aff_auto_approve_affiliates' => 'boolean',
            'aff_require_approval' => 'boolean',
            'aff_allow_self_registration' => 'boolean',
            
            // Commission Settings
            'aff_default_commission_rate' => 'required|numeric|min:0|max:100',
            'aff_tier_rates' => 'required|array',
            'aff_tier_rates.bronze' => 'required|numeric|min:0|max:100',
            'aff_tier_rates.silver' => 'required|numeric|min:0|max:100',
            'aff_tier_rates.gold' => 'required|numeric|min:0|max:100',
            'aff_tier_rates.platinum' => 'required|numeric|min:0|max:100',
            'aff_commission_structure' => 'required|string|in:percentage,fixed,tiered',
            
            // Payment Settings
            'aff_minimum_payout' => 'required|numeric|min:0',
            'aff_payment_schedule' => 'required|string|in:weekly,monthly,quarterly',
            'aff_payment_methods' => 'required|array',
            'aff_withdrawal_fee' => 'required|numeric|min:0',
            
            // Tracking Settings
            'aff_cookie_duration' => 'required|integer|min:1|max:365',
            'aff_track_subaffiliates' => 'boolean',
            'aff_attribution_model' => 'required|string|in:first_click,last_click,linear',
            
            // Email Settings
            'aff_welcome_email_enabled' => 'boolean',
            'aff_commission_email_enabled' => 'boolean',
            'aff_withdrawal_email_enabled' => 'boolean',
            'aff_monthly_report_enabled' => 'boolean',
            
            // Security Settings
            'aff_max_login_attempts' => 'required|integer|min:1|max:10',
            'aff_session_timeout' => 'required|integer|min:30|max:480',
            'aff_require_2fa' => 'boolean',
            
            // Content Settings
            'aff_terms_url' => 'nullable|url',
            'aff_privacy_url' => 'nullable|url',
            'aff_support_email' => 'required|email',
            'aff_support_phone' => 'nullable|string|max:20',
        ]);

        // Update all settings
        foreach ($request->all() as $key => $value) {
            if (str_starts_with($key, 'aff_')) {
                AffSetting::set($key, $value, $this->getSettingGroup($key), $this->getSettingDescription($key));
            }
        }

        return back()->with('success', 'Cài đặt đã được cập nhật thành công.');
    }

    /**
     * Get setting group by key.
     */
    private function getSettingGroup(string $key): string
    {
        if (str_contains($key, 'commission')) {
            return 'commission';
        } elseif (str_contains($key, 'payment') || str_contains($key, 'withdrawal')) {
            return 'payment';
        } elseif (str_contains($key, 'email')) {
            return 'email';
        } elseif (str_contains($key, 'cookie') || str_contains($key, 'track') || str_contains($key, 'attribution')) {
            return 'tracking';
        } elseif (str_contains($key, 'login') || str_contains($key, 'session') || str_contains($key, '2fa')) {
            return 'security';
        } elseif (str_contains($key, 'terms') || str_contains($key, 'privacy') || str_contains($key, 'support')) {
            return 'content';
        }
        
        return 'general';
    }

    /**
     * Get setting description by key.
     */
    private function getSettingDescription(string $key): string
    {
        $descriptions = [
            'aff_system_enabled' => 'Enable/disable affiliate system',
            'aff_auto_approve_affiliates' => 'Auto approve new affiliate registrations',
            'aff_require_approval' => 'Require admin approval for new affiliates',
            'aff_allow_self_registration' => 'Allow public affiliate registration',
            'aff_default_commission_rate' => 'Default commission rate percentage',
            'aff_tier_rates' => 'Commission rates by tier',
            'aff_commission_structure' => 'Commission calculation structure',
            'aff_minimum_payout' => 'Minimum payout amount in VND',
            'aff_payment_schedule' => 'Payment schedule frequency',
            'aff_payment_methods' => 'Available payment methods',
            'aff_withdrawal_fee' => 'Withdrawal processing fee',
            'aff_cookie_duration' => 'Cookie duration in days',
            'aff_track_subaffiliates' => 'Enable sub-affiliate tracking',
            'aff_attribution_model' => 'Attribution model for conversions',
            'aff_welcome_email_enabled' => 'Send welcome email to new affiliates',
            'aff_commission_email_enabled' => 'Send email notifications for new commissions',
            'aff_withdrawal_email_enabled' => 'Send email notifications for withdrawals',
            'aff_monthly_report_enabled' => 'Send monthly performance reports',
            'aff_max_login_attempts' => 'Maximum login attempts before lockout',
            'aff_session_timeout' => 'Session timeout in minutes',
            'aff_require_2fa' => 'Require two-factor authentication',
            'aff_terms_url' => 'Terms of service URL',
            'aff_privacy_url' => 'Privacy policy URL',
            'aff_support_email' => 'Support contact email',
            'aff_support_phone' => 'Support contact phone',
        ];

        return $descriptions[$key] ?? '';
    }
}
