<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class TrackingController extends Controller
{
    /**
     * Display click tracking data.
     */
    public function clicks(Request $request)
    {
        // Get date range from request or default to last 7 days
        $startDate = $request->input('start_date', now()->subDays(7)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));

        $query = AffClick::with(['affiliate.user', 'campaign', 'link'])
            ->whereBetween('clicked_at', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ]);

        // Apply filters
        if ($request->has('affiliate_id') && !empty($request->affiliate_id)) {
            $query->where('affiliate_id', $request->affiliate_id);
        }

        if ($request->has('campaign_id') && !empty($request->campaign_id)) {
            $query->where('campaign_id', $request->campaign_id);
        }

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ip_address', 'like', "%{$search}%")
                    ->orWhere('user_agent', 'like', "%{$search}%")
                    ->orWhere('referrer', 'like', "%{$search}%")
                    ->orWhereHas('affiliate.user', function ($subQ) use ($search) {
                        $subQ->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $clicks = $query->orderBy('clicked_at', 'desc')->paginate(20);

        // Get summary statistics
        $totalClicks = AffClick::whereBetween('clicked_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->count();

        $uniqueClicks = AffClick::whereBetween('clicked_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->where('is_unique', true)->count();

        $topAffiliates = AffClick::select('affiliate_id', DB::raw('COUNT(*) as click_count'))
            ->with('affiliate.user')
            ->whereBetween('clicked_at', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ])
            ->groupBy('affiliate_id')
            ->orderBy('click_count', 'desc')
            ->limit(10)
            ->get();

        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Tracking/Clicks', [
            'clicks' => $clicks,
            'stats' => [
                'total_clicks' => $totalClicks,
                'unique_clicks' => $uniqueClicks,
                'conversion_rate' => $totalClicks > 0 ? round(($uniqueClicks / $totalClicks) * 100, 2) : 0,
            ],
            'top_affiliates' => $topAffiliates,
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
            'filters' => $request->only(['start_date', 'end_date', 'affiliate_id', 'campaign_id', 'search']),
        ]);
    }

        // Get click statistics
        $stats = [
            'total_clicks' => 0,
            'unique_clicks' => 0,
            'click_through_rate' => 0,
            'top_sources' => [],
            'top_countries' => [],
            'device_breakdown' => [
                'desktop' => 0,
                'mobile' => 0,
                'tablet' => 0,
            ],
        ];

        // TODO: Implement pagination
        $paginatedClicks = $query;

        return Inertia::render('SuperAdmin/Affiliate/Tracking/Clicks', [
            'clicks' => $paginatedClicks,
            'stats' => $stats,
            'filters' => $request->only(['start_date', 'end_date', 'affiliate_id', 'campaign_id']),
        ]);
    }

    /**
     * Display conversion tracking data.
     */
    public function conversions(Request $request)
    {
        // Get date range from request or default to last 7 days
        $startDate = $request->input('start_date', now()->subDays(7)->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));

        $query = AffConversion::with(['affiliate.user', 'campaign'])
            ->whereBetween('converted_at', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ]);

        // Apply filters
        if ($request->has('affiliate_id') && !empty($request->affiliate_id)) {
            $query->where('affiliate_id', $request->affiliate_id);
        }

        if ($request->has('campaign_id') && !empty($request->campaign_id)) {
            $query->where('campaign_id', $request->campaign_id);
        }

        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        if ($request->has('type') && !empty($request->type)) {
            $query->where('conversion_type', $request->type);
        }

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_id', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhereHas('affiliate.user', function ($subQ) use ($search) {
                      $subQ->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $conversions = $query->orderBy('converted_at', 'desc')->paginate(20);

        // Get summary statistics
        $totalConversions = AffConversion::whereBetween('converted_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->count();

        $pendingConversions = AffConversion::whereBetween('converted_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->where('status', 'pending')->count();

        $approvedConversions = AffConversion::whereBetween('converted_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->where('status', 'approved')->count();

        $totalValue = AffConversion::whereBetween('converted_at', [
            Carbon::parse($startDate)->startOfDay(),
            Carbon::parse($endDate)->endOfDay()
        ])->where('status', 'approved')->sum('order_value');

        $affiliates = AffAffiliate::with('user')->where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        return Inertia::render('SuperAdmin/Affiliate/Tracking/Conversions', [
            'conversions' => $conversions,
            'stats' => [
                'total_conversions' => $totalConversions,
                'pending_conversions' => $pendingConversions,
                'approved_conversions' => $approvedConversions,
                'total_value' => $totalValue,
            ],
            'affiliates' => $affiliates,
            'campaigns' => $campaigns,
            'filters' => $request->only(['start_date', 'end_date', 'affiliate_id', 'campaign_id', 'status', 'type', 'search']),
        ]);
    }

    /**
     * Show conversion details.
     */
    public function showConversion($id)
    {
        $conversion = AffConversion::with(['affiliate.user', 'campaign', 'commission'])
            ->findOrFail($id);

        return Inertia::render('SuperAdmin/Affiliate/Tracking/ConversionDetails', [
            'conversion' => $conversion,
        ]);
    }

    /**
     * Approve a conversion.
     */
    public function approveConversion(Request $request, $id)
    {
        $conversion = AffConversion::findOrFail($id);

        $conversion->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Conversion approved successfully.');
    }

    /**
     * Reject a conversion.
     */
    public function rejectConversion(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $conversion = AffConversion::findOrFail($id);

        $conversion->update([
            'status' => 'rejected',
            'rejection_reason' => $request->reason,
            'rejected_at' => now(),
            'rejected_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Conversion rejected successfully.');
    }
}
