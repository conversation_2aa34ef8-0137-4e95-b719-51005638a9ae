<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffWithdrawal;
use App\Models\AffAffiliate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class WithdrawalController extends Controller
{
    /**
     * Display a listing of withdrawal requests.
     */
    public function index(Request $request)
    {
        $query = AffWithdrawal::with(['affiliate.user']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('affiliate.user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');

        if (in_array($sortField, ['amount', 'status', 'created_at', 'processed_at'])) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $withdrawals = $query->paginate(20);

        // Get statistics
        $stats = [
            'total_pending' => AffWithdrawal::where('status', 'pending')->count(),
            'total_approved' => AffWithdrawal::where('status', 'approved')->count(),
            'total_processed' => AffWithdrawal::where('status', 'processed')->count(),
            'total_amount_pending' => AffWithdrawal::where('status', 'pending')->sum('amount'),
            'total_amount_processed' => AffWithdrawal::where('status', 'processed')->sum('amount'),
        ];

        return Inertia::render('SuperAdmin/Affiliate/Withdrawals/Index', [
            'withdrawals' => $withdrawals,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Approve a withdrawal request.
     */
    public function approve($id)
    {
        try {
            $withdrawal = AffWithdrawal::with('affiliate')->findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return back()->with('error', 'Chỉ có thể duyệt yêu cầu rút tiền đang chờ xử lý.');
            }

            // Update withdrawal status to approved
            $withdrawal->update([
                'status' => 'approved',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
            ]);

            return back()->with('success', 'Yêu cầu rút tiền đã được duyệt.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi duyệt yêu cầu rút tiền.');
        }
    }

    /**
     * Reject a withdrawal request.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ], [
            'reason.required' => 'Lý do từ chối là bắt buộc.',
            'reason.max' => 'Lý do từ chối không được vượt quá 500 ký tự.',
        ]);

        try {
            $withdrawal = AffWithdrawal::with('affiliate')->findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return back()->with('error', 'Chỉ có thể từ chối yêu cầu rút tiền đang chờ xử lý.');
            }

            // Update withdrawal status to rejected
            $withdrawal->update([
                'status' => 'rejected',
                'rejection_reason' => $request->reason,
                'rejected_at' => now(),
                'rejected_by' => Auth::id(),
            ]);

            // Return amount to affiliate's available balance
            $withdrawal->affiliate->increment('available_balance', $withdrawal->amount);

            return back()->with('success', 'Yêu cầu rút tiền đã được từ chối.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi từ chối yêu cầu rút tiền.');
        }
    }

    /**
     * Process a withdrawal request (mark as completed).
     */
    public function process($id)
    {
        try {
            $withdrawal = AffWithdrawal::with('affiliate')->findOrFail($id);

            if ($withdrawal->status !== 'approved') {
                return back()->with('error', 'Chỉ có thể xử lý yêu cầu rút tiền đã được duyệt.');
            }

            // Update withdrawal status to processed
            $withdrawal->update([
                'status' => 'processed',
                'processed_at' => now(),
                'processed_by' => Auth::id(),
            ]);

            return back()->with('success', 'Yêu cầu rút tiền đã được xử lý thành công.');

        } catch (\Exception $e) {
            return back()->with('error', 'Có lỗi xảy ra khi xử lý yêu cầu rút tiền.');
        }
    }
}
