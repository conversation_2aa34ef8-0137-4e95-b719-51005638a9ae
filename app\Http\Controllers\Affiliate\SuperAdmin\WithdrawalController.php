<?php

namespace App\Http\Controllers\Affiliate\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WithdrawalController extends Controller
{
    /**
     * Display a listing of withdrawal requests.
     */
    public function index(Request $request)
    {
        // TODO: Replace with actual WithdrawalRequest model query
        $withdrawals = collect([]); // Placeholder for withdrawal data

        // Apply filters
        $query = $withdrawals;

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            // TODO: Implement search functionality
        }

        if ($request->has('status') && !empty($request->status)) {
            $status = $request->status;
            // TODO: Implement status filter (pending, approved, rejected, processed)
        }

        // TODO: Implement pagination
        $paginatedWithdrawals = $query;

        return Inertia::render('SuperAdmin/Affiliate/Withdrawals/Index', [
            'withdrawals' => $paginatedWithdrawals,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Approve a withdrawal request.
     */
    public function approve($id)
    {
        // TODO: Find withdrawal by ID
        $withdrawal = null; // Placeholder

        if (!$withdrawal) {
            return back()->with('error', 'Không tìm thấy yêu cầu rút tiền.');
        }

        // TODO: Approve withdrawal logic
        // 1. Update withdrawal status to 'approved'
        // 2. Send approval notification
        // 3. Log the action

        return back()->with('success', 'Yêu cầu rút tiền đã được duyệt.');
    }

    /**
     * Reject a withdrawal request.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        // TODO: Find withdrawal by ID
        $withdrawal = null; // Placeholder

        if (!$withdrawal) {
            return back()->with('error', 'Không tìm thấy yêu cầu rút tiền.');
        }

        // TODO: Reject withdrawal logic
        // 1. Update withdrawal status to 'rejected'
        // 2. Save rejection reason
        // 3. Return commission to affiliate balance
        // 4. Send rejection notification

        return back()->with('success', 'Yêu cầu rút tiền đã được từ chối.');
    }

    /**
     * Process a withdrawal request (mark as completed).
     */
    public function process($id)
    {
        // TODO: Find withdrawal by ID
        $withdrawal = null; // Placeholder

        if (!$withdrawal) {
            return back()->with('error', 'Không tìm thấy yêu cầu rút tiền.');
        }

        // TODO: Process withdrawal logic
        // 1. Update withdrawal status to 'processed'
        // 2. Record transaction details
        // 3. Send completion notification
        // 4. Update affiliate balance

        return back()->with('success', 'Yêu cầu rút tiền đã được xử lý thành công.');
    }
}
