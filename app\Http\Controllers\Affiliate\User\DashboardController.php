<?php

namespace App\Http\Controllers\Affiliate\User;

use App\Http\Controllers\Controller;
use App\Models\AffAffiliate;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffWithdrawal;
use App\Models\AffLink;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display affiliate dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $affiliate = AffAffiliate::with('user')->where('user_id', $user->id)->first();

        if (!$affiliate) {
            return redirect()->route('affiliate.apply')
                ->with('info', 'Bạn cần đăng ký làm affiliate trước khi truy cập dashboard.');
        }

        // Get date range (default to current month)
        $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->format('Y-m-d'));

        // Get overview statistics
        $stats = [
            'total_clicks' => AffClick::where('affiliate_id', $affiliate->id)->count(),
            'total_conversions' => AffConversion::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')->count(),
            'total_earnings' => AffCommission::where('affiliate_id', $affiliate->id)
                ->whereIn('status', ['approved', 'paid'])->sum('amount'),
            'pending_earnings' => AffCommission::where('affiliate_id', $affiliate->id)
                ->where('status', 'pending')->sum('amount'),
            'available_balance' => AffCommission::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')->sum('amount'),
            'total_withdrawn' => AffWithdrawal::where('affiliate_id', $affiliate->id)
                ->where('status', 'completed')->sum('amount'),
        ];

        // Calculate conversion rate
        $stats['conversion_rate'] = $stats['total_clicks'] > 0
            ? round(($stats['total_conversions'] / $stats['total_clicks']) * 100, 2)
            : 0;

        // Get period statistics
        $periodStats = [
            'period_clicks' => AffClick::where('affiliate_id', $affiliate->id)
                ->whereBetween('clicked_at', [$startDate, $endDate])->count(),
            'period_conversions' => AffConversion::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')
                ->whereBetween('converted_at', [$startDate, $endDate])->count(),
            'period_earnings' => AffCommission::where('affiliate_id', $affiliate->id)
                ->whereIn('status', ['approved', 'paid'])
                ->whereBetween('created_at', [$startDate, $endDate])->sum('amount'),
        ];

        // Get daily performance data for chart (last 30 days)
        $chartStartDate = now()->subDays(29)->startOfDay();
        $chartEndDate = now()->endOfDay();

        $dailyData = [];
        $period = Carbon::parse($chartStartDate)->daysUntil(Carbon::parse($chartEndDate));

        foreach ($period as $date) {
            $dateStr = $date->format('Y-m-d');

            $clicks = AffClick::where('affiliate_id', $affiliate->id)
                ->whereDate('clicked_at', $date)
                ->count();

            $conversions = AffConversion::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')
                ->whereDate('converted_at', $date)
                ->count();

            $earnings = AffCommission::where('affiliate_id', $affiliate->id)
                ->whereIn('status', ['approved', 'paid'])
                ->whereDate('created_at', $date)
                ->sum('amount');

            $dailyData[] = [
                'date' => $dateStr,
                'clicks' => $clicks,
                'conversions' => $conversions,
                'earnings' => $earnings,
            ];
        }

        // Get recent activities
        $recentClicks = AffClick::where('affiliate_id', $affiliate->id)
            ->with('campaign')
            ->latest('clicked_at')
            ->take(5)
            ->get();

        $recentConversions = AffConversion::where('affiliate_id', $affiliate->id)
            ->with('campaign')
            ->latest('converted_at')
            ->take(5)
            ->get();

        $recentCommissions = AffCommission::where('affiliate_id', $affiliate->id)
            ->with('conversion')
            ->latest('created_at')
            ->take(5)
            ->get();

        // Get top performing links
        $topLinks = AffLink::where('affiliate_id', $affiliate->id)
            ->where('clicks', '>', 0)
            ->orderByDesc('clicks')
            ->take(5)
            ->get();

        return Inertia::render('Affiliate/Dashboard', [
            'affiliate' => $affiliate,
            'stats' => $stats,
            'period_stats' => $periodStats,
            'chart_data' => $dailyData,
            'recent_clicks' => $recentClicks,
            'recent_conversions' => $recentConversions,
            'recent_commissions' => $recentCommissions,
            'top_links' => $topLinks,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
    }

    /**
     * Get quick stats for AJAX requests.
     */
    public function quickStats()
    {
        $user = Auth::user();
        $affiliate = AffAffiliate::where('user_id', $user->id)->first();

        if (!$affiliate) {
            return response()->json(['error' => 'Affiliate not found'], 404);
        }

        $stats = [
            'available_balance' => AffCommission::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')->sum('amount'),
            'pending_earnings' => AffCommission::where('affiliate_id', $affiliate->id)
                ->where('status', 'pending')->sum('amount'),
            'today_clicks' => AffClick::where('affiliate_id', $affiliate->id)
                ->whereDate('clicked_at', today())->count(),
            'today_conversions' => AffConversion::where('affiliate_id', $affiliate->id)
                ->where('status', 'approved')
                ->whereDate('converted_at', today())->count(),
        ];

        return response()->json($stats);
    }
}
