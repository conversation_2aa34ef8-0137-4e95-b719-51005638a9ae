<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CronJob;
use App\Models\CronJobLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class CronJobController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Display a listing of cron jobs
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized',
                    'error' => 'Not authenticated'
                ], 401);
            }

            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized',
                    'error' => 'Insufficient permissions'
                ], 403);
            }

            $query = CronJob::with(['creator:id,name,email', 'updater:id,name,email']);

            if ($request->has('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }
            if ($request->has('environment') && $request->environment !== 'all') {
                $query->where('environment', $request->environment);
            }
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('command', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            }
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $cronJobs = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $cronJobs,
                'message' => 'Cron jobs retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving cron jobs: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving cron jobs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cron jobs overview statistics
     */
    public function getOverview(): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $stats = [
                'total_jobs' => CronJob::count(),
                'active_jobs' => CronJob::where('status', CronJob::STATUS_ACTIVE)->count(),
                'running_jobs' => CronJob::where('status', CronJob::STATUS_RUNNING)->count(),
                'failed_jobs' => CronJob::where('status', CronJob::STATUS_FAILED)->count(),
                'inactive_jobs' => CronJob::where('status', CronJob::STATUS_INACTIVE)->count(),
                'total_executions' => CronJob::sum('success_count') + CronJob::sum('failure_count'),
                'success_rate' => $this->calculateOverallSuccessRate(),
                'last_updated' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Overview statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving overview: ' . $e->getMessage(), [
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving overview statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created cron job
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:cron_jobs,name',
                'command' => 'required|string|max:1000',
                'cron_expression' => 'required|string|max:100',
                'description' => 'nullable|string|max:1000',
                'timeout' => 'nullable|integer|min:30|max:3600',
                'max_retries' => 'nullable|integer|min:0|max:10',
                'environment' => 'nullable|in:production,staging,development'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate cron expression
            if (!$this->isValidCronExpression($request->cron_expression)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid cron expression format'
                ], 422);
            }

            $cronJob = CronJob::create([
                'name' => $request->name,
                'command' => $request->command,
                'cron_expression' => $request->cron_expression,
                'description' => $request->description,
                'timeout' => $request->get('timeout', 300),
                'max_retries' => $request->get('max_retries', 3),
                'environment' => $request->get('environment', 'production'),
                'created_by' => auth()->id(),
                'updated_by' => auth()->id()
            ]);

            // Calculate next run time
            $cronJob->next_run_at = $cronJob->calculateNextRun();
            $cronJob->save();

            // Log creation
            $cronJob->logInfo("Cron job created by " . auth()->user()->name, [
                'user_id' => auth()->id(),
                'user_email' => auth()->user()->email
            ]);

            return response()->json([
                'success' => true,
                'data' => $cronJob->load(['creator', 'updater']),
                'message' => 'Cron job created successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Error creating cron job: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error creating cron job',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified cron job
     */
    public function show(CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $cronJob->load(['creator', 'updater', 'recentLogs']);

            return response()->json([
                'success' => true,
                'data' => $cronJob,
                'message' => 'Cron job retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving cron job: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving cron job',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified cron job
     */
    public function update(Request $request, CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:255|unique:cron_jobs,name,' . $cronJob->id,
                'command' => 'sometimes|required|string|max:1000',
                'cron_expression' => 'sometimes|required|string|max:100',
                'description' => 'nullable|string|max:1000',
                'status' => 'sometimes|required|in:active,inactive,failed,running',
                'timeout' => 'nullable|integer|min:30|max:3600',
                'max_retries' => 'nullable|integer|min:0|max:10',
                'environment' => 'nullable|in:production,staging,development'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate cron expression if provided
            if ($request->has('cron_expression') && !$this->isValidCronExpression($request->cron_expression)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid cron expression format'
                ], 422);
            }

            $oldData = $cronJob->toArray();

            $cronJob->update(array_merge($request->only([
                'name',
                'command',
                'cron_expression',
                'description',
                'status',
                'timeout',
                'max_retries',
                'environment'
            ]), [
                'updated_by' => auth()->id()
            ]));

            // Recalculate next run time if cron expression changed
            if ($request->has('cron_expression')) {
                $cronJob->next_run_at = $cronJob->calculateNextRun();
                $cronJob->save();
            }

            // Log update
            $changes = array_diff_assoc($cronJob->toArray(), $oldData);
            $cronJob->logInfo("Cron job updated by " . auth()->user()->name, [
                'user_id' => auth()->id(),
                'user_email' => auth()->user()->email,
                'changes' => $changes
            ]);

            return response()->json([
                'success' => true,
                'data' => $cronJob->load(['creator', 'updater']),
                'message' => 'Cron job updated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating cron job: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id,
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating cron job',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified cron job
     */
    public function destroy(CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            if ($cronJob->isRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete a running cron job'
                ], 422);
            }

            $jobName = $cronJob->name;

            $cronJob->logWarning("Cron job deleted by " . auth()->user()->name, [
                'user_id' => auth()->id(),
                'user_email' => auth()->user()->email
            ]);

            $cronJob->delete();

            return response()->json([
                'success' => true,
                'message' => "Cron job '{$jobName}' deleted successfully"
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting cron job: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error deleting cron job',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Run a cron job immediately
     */
    public function runNow(CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Check if job is already running
            if ($cronJob->isRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cron job is already running'
                ], 422);
            }

            // Check if job is active
            if (!$cronJob->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot run inactive cron job'
                ], 422);
            }

            // Mark as running
            $cronJob->markAsRunning();

            // Log start
            $cronJob->logInfo("Manual execution started by " . auth()->user()->name, [
                'user_id' => auth()->id(),
                'user_email' => auth()->user()->email
            ]);

            // Execute the command
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);

            try {
                // Run the artisan command
                $exitCode = Artisan::call($cronJob->command);
                $output = Artisan::output();

                $endTime = microtime(true);
                $endMemory = memory_get_usage(true);

                $executionTime = round($endTime - $startTime, 3);
                $memoryUsage = $endMemory - $startMemory;

                if ($exitCode === 0) {
                    // Success
                    $cronJob->markAsCompleted($executionTime, $memoryUsage);
                    $cronJob->logSuccess("Manual execution completed successfully", [
                        'execution_time' => $executionTime,
                        'memory_usage' => $memoryUsage,
                        'output' => $output,
                        'executed_by' => auth()->user()->name
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Cron job executed successfully',
                        'data' => [
                            'execution_time' => $executionTime,
                            'memory_usage' => $memoryUsage,
                            'output' => $output
                        ]
                    ]);
                } else {
                    // Command failed
                    $cronJob->markAsFailed("Command exited with code: {$exitCode}");
                    $cronJob->logError("Manual execution failed", [
                        'exit_code' => $exitCode,
                        'output' => $output,
                        'executed_by' => auth()->user()->name
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Cron job execution failed',
                        'error' => "Command exited with code: {$exitCode}",
                        'output' => $output
                    ], 500);
                }

            } catch (\Exception $e) {
                $cronJob->markAsFailed($e->getMessage());
                $cronJob->logError("Manual execution exception: " . $e->getMessage(), [
                    'executed_by' => auth()->user()->name,
                    'exception' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Cron job execution failed',
                    'error' => $e->getMessage()
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error running cron job: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error running cron job',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get logs for a specific cron job
     */
    public function getLogs(Request $request, CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $query = $cronJob->logs();

            if ($request->has('level') && $request->level !== 'all') {
                $query->where('level', $request->level);
            }

            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            if ($request->has('search') && !empty($request->search)) {
                $query->where('message', 'like', '%' . $request->search . '%');
            }

            $logs = $query->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'data' => $logs,
                'message' => 'Logs retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving cron job logs: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Toggle cron job status (enable/disable)
     */
    public function toggleStatus(CronJob $cronJob): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            if ($cronJob->isRunning()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot change status of a running cron job'
                ], 422);
            }

            $oldStatus = $cronJob->status;
            $newStatus = $cronJob->isActive() ? CronJob::STATUS_INACTIVE : CronJob::STATUS_ACTIVE;

            $cronJob->update([
                'status' => $newStatus,
                'updated_by' => auth()->id()
            ]);

            $cronJob->logInfo("Status changed from {$oldStatus} to {$newStatus} by " . auth()->user()->name, [
                'user_id' => auth()->id(),
                'user_email' => auth()->user()->email,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            return response()->json([
                'success' => true,
                'data' => $cronJob->load(['creator', 'updater']),
                'message' => "Cron job status changed to {$newStatus} successfully"
            ]);

        } catch (\Exception $e) {
            Log::error('Error toggling cron job status: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error toggling cron job status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system logs (all cron jobs combined)
     */
    public function getSystemLogs(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $query = CronJobLog::with(['cronJob:id,name']);

            // Filter by level
            if ($request->has('level') && $request->level !== 'all') {
                $query->where('level', $request->level);
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            // Search in message
            if ($request->has('search') && !empty($request->search)) {
                $query->where('message', 'like', '%' . $request->search . '%');
            }

            $logs = $query->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 100));

            return response()->json([
                'success' => true,
                'data' => $logs,
                'message' => 'System logs retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving system logs: ' . $e->getMessage(), [
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving system logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear logs for a specific cron job or all logs
     */
    public function clearLogs(Request $request, CronJob $cronJob = null): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'older_than_days' => 'nullable|integer|min:1|max:365',
                'levels' => 'nullable|array',
                'levels.*' => 'in:info,success,warning,error'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = $cronJob ? $cronJob->logs() : CronJobLog::query();

            // Filter by age
            if ($request->has('older_than_days')) {
                $cutoffDate = now()->subDays($request->older_than_days);
                $query->where('created_at', '<', $cutoffDate);
            }

            // Filter by levels
            if ($request->has('levels') && !empty($request->levels)) {
                $query->whereIn('level', $request->levels);
            }

            $deletedCount = $query->count();
            $query->delete();

            // Log the cleanup action
            $logMessage = $cronJob
                ? "Cleared {$deletedCount} logs for cron job '{$cronJob->name}' by " . auth()->user()->name
                : "Cleared {$deletedCount} system logs by " . auth()->user()->name;

            if ($cronJob) {
                $cronJob->logInfo($logMessage, [
                    'user_id' => auth()->id(),
                    'user_email' => auth()->user()->email,
                    'deleted_count' => $deletedCount,
                    'filters' => $request->only(['older_than_days', 'levels'])
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully cleared {$deletedCount} log entries",
                'data' => ['deleted_count' => $deletedCount]
            ]);

        } catch (\Exception $e) {
            Log::error('Error clearing logs: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error clearing logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export logs to file
     */
    public function exportLogs(Request $request, CronJob $cronJob = null): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'format' => 'required|in:csv,json,txt',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'levels' => 'nullable|array',
                'levels.*' => 'in:info,success,warning,error'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = $cronJob ? $cronJob->logs() : CronJobLog::with(['cronJob:id,name']);

            // Apply filters
            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            if ($request->has('levels') && !empty($request->levels)) {
                $query->whereIn('level', $request->levels);
            }

            $logs = $query->orderBy('created_at', 'desc')->get();

            // Generate filename
            $timestamp = now()->format('Y-m-d_H-i-s');
            $jobName = $cronJob ? $cronJob->name : 'system';
            $filename = "cron_logs_{$jobName}_{$timestamp}.{$request->format}";

            // Generate file content based on format
            $content = $this->generateLogFileContent($logs, $request->format);

            // In a real application, you would save the file to storage and return a download URL
            // For this example, we'll return the content directly
            return response()->json([
                'success' => true,
                'message' => 'Logs exported successfully',
                'data' => [
                    'filename' => $filename,
                    'content' => $content,
                    'download_url' => url("/api/cron-jobs/download-logs/{$filename}"),
                    'records_count' => $logs->count()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error exporting logs: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'cron_job_id' => $cronJob?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error exporting logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate overall success rate for all cron jobs
     */
    private function calculateOverallSuccessRate(): float
    {
        $totalSuccess = CronJob::sum('success_count');
        $totalFailure = CronJob::sum('failure_count');
        $total = $totalSuccess + $totalFailure;

        if ($total === 0) {
            return 0;
        }

        return round(($totalSuccess / $total) * 100, 2);
    }

    /**
     * Validate cron expression format
     */
    private function isValidCronExpression(string $expression): bool
    {
        $parts = explode(' ', trim($expression));

        if (count($parts) !== 5) {
            return false;
        }

        $patterns = [
            '/^(\*|([0-5]?\d)(,([0-5]?\d))*|\*\/([0-5]?\d))$/', // minute
            '/^(\*|([01]?\d|2[0-3])(,([01]?\d|2[0-3]))*|\*\/([01]?\d|2[0-3]))$/', // hour
            '/^(\*|([12]?\d|3[01])(,([12]?\d|3[01]))*|\*\/([12]?\d|3[01]))$/', // day
            '/^(\*|([1-9]|1[0-2])(,([1-9]|1[0-2]))*|\*\/([1-9]|1[0-2]))$/', // month
            '/^(\*|[0-7](,[0-7])*|\*\/[0-7])$/' // day of week
        ];

        foreach ($parts as $index => $part) {
            if (!preg_match($patterns[$index], $part)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Generate file content for log export
     */
    private function generateLogFileContent($logs, string $format): string
    {
        switch ($format) {
            case 'csv':
                $content = "Timestamp,Level,Job Name,Message,Execution Time,Memory Usage\n";
                foreach ($logs as $log) {
                    $jobName = $log->cronJob ? $log->cronJob->name : 'N/A';
                    $content .= sprintf(
                        '"%s","%s","%s","%s","%s","%s"' . "\n",
                        $log->created_at->format('Y-m-d H:i:s'),
                        $log->level,
                        $jobName,
                        str_replace('"', '""', $log->message),
                        $log->execution_time ?? 'N/A',
                        $log->memory_usage ?? 'N/A'
                    );
                }
                break;

            case 'json':
                $content = json_encode($logs->map(function ($log) {
                    return [
                        'timestamp' => $log->created_at->format('Y-m-d H:i:s'),
                        'level' => $log->level,
                        'job_name' => $log->cronJob ? $log->cronJob->name : null,
                        'message' => $log->message,
                        'execution_time' => $log->execution_time,
                        'memory_usage' => $log->memory_usage,
                        'context' => $log->context
                    ];
                }), JSON_PRETTY_PRINT);
                break;

            case 'txt':
            default:
                $content = "Cron Jobs Log Export\n";
                $content .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
                $content .= "Total Records: " . $logs->count() . "\n";
                $content .= str_repeat("=", 80) . "\n\n";

                foreach ($logs as $log) {
                    $jobName = $log->cronJob ? $log->cronJob->name : 'System';
                    $content .= "[{$log->created_at->format('Y-m-d H:i:s')}] ";
                    $content .= strtoupper($log->level) . " ";
                    $content .= "({$jobName}): ";
                    $content .= $log->message . "\n";

                    if ($log->execution_time) {
                        $content .= "  Execution Time: {$log->execution_time}s\n";
                    }

                    if ($log->memory_usage) {
                        $content .= "  Memory Usage: " . number_format($log->memory_usage / 1024 / 1024, 2) . "MB\n";
                    }

                    $content .= "\n";
                }
                break;
        }

        return $content;
    }
}
