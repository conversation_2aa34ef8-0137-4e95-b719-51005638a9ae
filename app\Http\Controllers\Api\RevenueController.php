<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DailyRevenue;
use App\Models\HourlyRevenue;
use App\Models\MonthlyStatistic;
use App\Models\Business;
use App\Models\Branch;
use App\Models\Booking;
use App\Models\Court;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Inertia\Inertia;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class RevenueController extends Controller
{
    public function __construct()
    {

    }

    public function showRevenueStatisticsPage(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return Inertia::render('Auth/Login', [
                    'error' => 'Bạn cần đăng nhập với quyền Super Admin để truy cập trang này.'
                ]);
            }

            $initialData = $this->getInitialPageData();

            $businesses = Business::where('status', 'active')
                ->select('id', 'name', 'logo_url')
                ->orderBy('name')
                ->get();

            $branches = Branch::with('business:id,name')
                ->select('id', 'name', 'business_id', 'address', 'province_name', 'district_name', 'ward_name')
                ->orderBy('name')
                ->get();

            $dashboardStats = $this->getDashboardStats();

            return Inertia::render('SuperAdmin/RevenueStatistics', [
                'initialData' => $initialData,
                'businesses' => $businesses,
                'branches' => $branches,
                'dashboardStats' => $dashboardStats
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading revenue statistics page: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return Inertia::render('Errors/500', [
                'error' => 'Có lỗi xảy ra khi tải trang thống kê doanh thu.',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get dashboard statistics for overview
     */
    private function getDashboardStats(): array
    {
        try {
            $today = Carbon::today();
            $thisMonth = Carbon::now()->startOfMonth();

            return [
                'today_revenue' => (float) DailyRevenue::where('revenue_date', $today)->sum('gross_revenue'),
                'today_bookings' => (int) DailyRevenue::where('revenue_date', $today)->sum('total_bookings'),
                'month_revenue' => (float) DailyRevenue::where('revenue_date', '>=', $thisMonth)->sum('gross_revenue'),
                'month_bookings' => (int) DailyRevenue::where('revenue_date', '>=', $thisMonth)->sum('total_bookings'),
                'active_businesses' => Business::where('status', 'active')->count(),
                'active_branches' => Branch::whereHas('business', function ($q) {
                    $q->where('status', 'active');
                })->count(),
                'total_courts' => Court::count(),
                'avg_utilization_rate' => 75.5
            ];
        } catch (\Exception $e) {
            Log::error('Error getting dashboard stats: ' . $e->getMessage());
            return [
                'today_revenue' => 0,
                'today_bookings' => 0,
                'month_revenue' => 0,
                'month_bookings' => 0,
                'active_businesses' => 0,
                'active_branches' => 0,
                'total_courts' => 0,
                'avg_utilization_rate' => 0
            ];
        }
    }
    /**
     * Get initial data for the revenue statistics page
     */
    private function getInitialPageData(): array
    {
        try {
            $currentMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();

            $overview = DailyRevenue::whereBetween('revenue_date', [$currentMonth, $endOfMonth])
                ->selectRaw('
                    SUM(gross_revenue) as total_revenue,
                    SUM(commission_amount) as total_commission,
                    SUM(net_revenue) as net_revenue,
                    SUM(total_bookings) as total_bookings,
                    SUM(confirmed_bookings) as confirmed_bookings,
                    SUM(cancelled_bookings) as cancelled_bookings,
                    AVG(gross_revenue/NULLIF(total_bookings, 0)) as avg_booking_value,
                    MAX(gross_revenue) as peak_day_revenue
                ')->first();

            $previousMonth = Carbon::now()->subMonth()->startOfMonth();
            $endOfPreviousMonth = Carbon::now()->subMonth()->endOfMonth();

            $previousOverview = DailyRevenue::whereBetween('revenue_date', [$previousMonth, $endOfPreviousMonth])
                ->selectRaw('SUM(gross_revenue) as total_revenue')
                ->first();

            $growthRate = 0;
            if ($previousOverview && $previousOverview->total_revenue > 0) {
                $growthRate = (($overview->total_revenue - $previousOverview->total_revenue) / $previousOverview->total_revenue) * 100;
            }

            $trends = DailyRevenue::whereBetween('revenue_date', [Carbon::now()->subDays(7), Carbon::now()])
                ->selectRaw('
                    revenue_date,
                    SUM(gross_revenue) as revenue,
                    SUM(total_bookings) as bookings
                ')
                ->groupBy('revenue_date')
                ->orderBy('revenue_date')
                ->get();

            $topBusinesses = DailyRevenue::join('businesses', 'daily_revenues.business_id', '=', 'businesses.id')
                ->whereBetween('revenue_date', [$currentMonth, $endOfMonth])
                ->selectRaw('
                    businesses.id,
                    businesses.name,
                    SUM(daily_revenues.gross_revenue) as total_revenue,
                    SUM(daily_revenues.total_bookings) as total_bookings
                ')
                ->groupBy('businesses.id', 'businesses.name')
                ->orderByDesc('total_revenue')
                ->limit(5)
                ->get();

            return [
                'overview' => [
                    'total_revenue' => (float) ($overview->total_revenue ?? 0),
                    'total_commission' => (float) ($overview->total_commission ?? 0),
                    'net_revenue' => (float) ($overview->net_revenue ?? 0),
                    'total_bookings' => (int) ($overview->total_bookings ?? 0),
                    'confirmed_bookings' => (int) ($overview->confirmed_bookings ?? 0),
                    'cancelled_bookings' => (int) ($overview->cancelled_bookings ?? 0),
                    'avg_booking_value' => (float) ($overview->avg_booking_value ?? 0),
                    'peak_day_revenue' => (float) ($overview->peak_day_revenue ?? 0),
                    'growth_rate' => round($growthRate, 2),
                    'success_rate' => $overview->total_bookings > 0 ? round(($overview->confirmed_bookings / $overview->total_bookings) * 100, 2) : 0,
                ],
                'trends' => $trends,
                'top_businesses' => $topBusinesses,
                'last_updated' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Error getting initial page data: ' . $e->getMessage());

            return [
                'overview' => [
                    'total_revenue' => 0,
                    'total_commission' => 0,
                    'net_revenue' => 0,
                    'total_bookings' => 0,
                    'confirmed_bookings' => 0,
                    'cancelled_bookings' => 0,
                    'avg_booking_value' => 0,
                    'peak_day_revenue' => 0,
                    'growth_rate' => 0,
                    'success_rate' => 0,
                ],
                'trends' => [],
                'top_businesses' => [],
                'last_updated' => now()->toISOString()
            ];
        }
    }


    /**
     * Get basic revenue overview for all authenticated users
     */
    public function getBasicOverview(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Please login to access this resource.'
                ], 401);
            }
            $stats = [
                'total_revenue' => DailyRevenue::sum('gross_revenue') ?? 0,
                'total_bookings' => DailyRevenue::sum('total_bookings') ?? 0,
                'active_businesses' => Business::where('status', 'active')->count(),
                'user_role' => $user->getRoleNames()->first() ?? 'user',
                'can_access_detailed' => $user->hasRole('super-admin')
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Basic revenue overview retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving basic revenue overview: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving revenue overview',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get comprehensive revenue overview for super-admin
     */
    public function getRevenueOverview(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');
            $branchId = $request->get('branchId');
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            // Generate a cache key based on request parameters
            $cacheKey = 'revenue_overview_' . md5(json_encode([
                'timeRange' => $timeRange,
                'businessId' => $businessId,
                'branchId' => $branchId,
                'startDate' => $startDate,
                'endDate' => $endDate
            ]));

            // Try to get from cache first (cache for 1 hour)
            $result = Cache::remember($cacheKey, 3600, function () use ($timeRange, $businessId, $branchId, $startDate, $endDate) {
                $query = DailyRevenue::query()
                    ->select(DB::raw('
                SUM(gross_revenue) as total_revenue,
                SUM(commission_amount) as total_commission,
                SUM(net_revenue) as net_revenue,
                SUM(total_bookings) as total_bookings,
                SUM(confirmed_bookings) as confirmed_bookings,
                SUM(cancelled_bookings) as cancelled_bookings,
                AVG(gross_revenue/NULLIF(total_bookings, 0)) as avg_booking_value,
                MAX(gross_revenue) as peak_day_revenue
                '))
                    ->whereNull('court_id'); // Only include branch-level records, not court-level

                if ($businessId && $businessId !== 'all') {
                    $query->where('business_id', $businessId);
                }

                if ($branchId && $branchId !== 'all') {
                    $query->where('branch_id', $branchId);
                    if ($businessId && $businessId !== 'all') {
                        $branchBelongsToBusiness = Branch::where('id', $branchId)
                            ->where('business_id', $businessId)
                            ->exists();

                        if (!$branchBelongsToBusiness) {
                            return [
                                'error' => true,
                                'message' => 'The specified branch does not belong to the selected business'
                            ];
                        }
                    }
                } else if ($businessId && $businessId !== 'all') {
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    if (!empty($branchIds)) {
                        $query->whereIn('branch_id', $branchIds);
                    }
                }

                $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);
                $query->whereBetween('revenue_date', $dateRange);

                // Get the main stats
                $stats = $query->first();

                // Get peak day date with a separate optimized query
                $peakDayQuery = DailyRevenue::query()
                    ->select('revenue_date', 'gross_revenue')
                    ->whereNull('court_id');

                if ($businessId && $businessId !== 'all') {
                    $peakDayQuery->where('business_id', $businessId);
                }

                if ($branchId && $branchId !== 'all') {
                    $peakDayQuery->where('branch_id', $branchId);
                }

                $peakDayQuery->whereBetween('revenue_date', $dateRange)
                    ->orderByDesc('gross_revenue')
                    ->limit(1);

                $peakDay = $peakDayQuery->first();
                $peakDayDate = $peakDay ? $peakDay->revenue_date : null;

                // Calculate previous period stats
                $previousPeriodRange = $this->getPreviousPeriodRange($timeRange);
                $previousStatsQuery = DailyRevenue::query()
                    ->select(DB::raw('SUM(gross_revenue) as total_revenue'))
                    ->whereNull('court_id');

                if ($businessId && $businessId !== 'all') {
                    $previousStatsQuery->where('business_id', $businessId);
                }

                if ($branchId && $branchId !== 'all') {
                    $previousStatsQuery->where('branch_id', $branchId);
                } else if ($businessId && $businessId !== 'all') {
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    if (!empty($branchIds)) {
                        $previousStatsQuery->whereIn('branch_id', $branchIds);
                    }
                }

                $previousStatsQuery->whereBetween('revenue_date', $previousPeriodRange);
                $previousStats = $previousStatsQuery->first();

                $growthRate = 0;
                if ($previousStats && $previousStats->total_revenue > 0) {
                    $growthRate = (($stats->total_revenue - $previousStats->total_revenue) / $previousStats->total_revenue) * 100;
                }

                return [
                    'total_revenue' => (float) ($stats->total_revenue ?? 0),
                    'total_commission' => (float) ($stats->total_commission ?? 0),
                    'net_revenue' => (float) ($stats->net_revenue ?? 0),
                    'total_bookings' => (int) ($stats->total_bookings ?? 0),
                    'confirmed_bookings' => (int) ($stats->confirmed_bookings ?? 0),
                    'cancelled_bookings' => (int) ($stats->cancelled_bookings ?? 0),
                    'avg_booking_value' => (float) ($stats->avg_booking_value ?? 0),
                    'peak_day_revenue' => (float) ($stats->peak_day_revenue ?? 0),
                    'peak_day_date' => $peakDayDate,
                    'growth_rate' => round($growthRate, 2),
                    'success_rate' => $stats->total_bookings > 0 ? round(($stats->confirmed_bookings / $stats->total_bookings) * 100, 2) : 0,
                    'time_range' => $timeRange,
                    'date_range' => $dateRange,
                    'last_updated' => now()->toISOString()
                ];
            });

            // If there was an error in the cache callback
            if (isset($result['error']) && $result['error'] === true) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Revenue overview retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving revenue overview: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving revenue overview',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get revenue summary for dashboard
     */
    public function getRevenueSummary(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);

            $currentStats = DailyRevenue::whereBetween('revenue_date', $dateRange)
                ->selectRaw('
                    SUM(gross_revenue) as total_revenue,
                    SUM(net_revenue) as net_revenue,
                    SUM(total_bookings) as total_bookings,
                    COUNT(DISTINCT business_id) as active_businesses,
                    COUNT(DISTINCT branch_id) as active_branches
                ')->first();

            $previousRange = $this->getPreviousPeriodRange($timeRange);
            $previousStats = DailyRevenue::whereBetween('revenue_date', $previousRange)
                ->selectRaw('
                    SUM(gross_revenue) as total_revenue,
                    SUM(total_bookings) as total_bookings
                ')->first();

            $revenueGrowth = 0;
            $bookingGrowth = 0;

            if ($previousStats && $previousStats->total_revenue > 0) {
                $revenueGrowth = (($currentStats->total_revenue - $previousStats->total_revenue) / $previousStats->total_revenue) * 100;
            }

            if ($previousStats && $previousStats->total_bookings > 0) {
                $bookingGrowth = (($currentStats->total_bookings - $previousStats->total_bookings) / $previousStats->total_bookings) * 100;
            }

            $summary = [
                'current_period' => [
                    'total_revenue' => (float) ($currentStats->total_revenue ?? 0),
                    'net_revenue' => (float) ($currentStats->net_revenue ?? 0),
                    'total_bookings' => (int) ($currentStats->total_bookings ?? 0),
                    'active_businesses' => (int) ($currentStats->active_businesses ?? 0),
                    'active_branches' => (int) ($currentStats->active_branches ?? 0),
                ],
                'growth_rates' => [
                    'revenue_growth' => round($revenueGrowth, 2),
                    'booking_growth' => round($bookingGrowth, 2),
                ],
                'time_range' => $timeRange,
                'period_label' => $this->getPeriodLabel($timeRange),
                'last_updated' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $summary,
                'message' => 'Revenue summary retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving revenue summary: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving revenue summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get revenue trends for charts
     */
    public function getRevenueTrends(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');
            $branchId = $request->get('branchId');
            $chartType = $request->get('chart_type', 'daily');
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            $query = DailyRevenue::query();

            // Filter by business if specified
            if ($businessId && $businessId !== 'all') {
                $query->where('business_id', $businessId);
            }

            // Filter by branch if specified
            if ($branchId && $branchId !== 'all') {
                $query->where('branch_id', $branchId);

                // If business is also specified, validate that the branch belongs to the business
                if ($businessId && $businessId !== 'all') {
                    $branchBelongsToBusiness = Branch::where('id', $branchId)
                        ->where('business_id', $businessId)
                        ->exists();

                    if (!$branchBelongsToBusiness) {
                        return response()->json([
                            'success' => false,
                            'message' => 'The specified branch does not belong to the selected business'
                        ], 400);
                    }
                }
            } else if ($businessId && $businessId !== 'all') {
                // If business is specified but branch is 'all', get all branches for that business
                $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                if (!empty($branchIds)) {
                    $query->whereIn('branch_id', $branchIds);
                }
            }

            $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);
            $query->whereBetween('revenue_date', $dateRange);

            if ($chartType === 'daily') {
                $data = $query->selectRaw('
                        revenue_date,
                        SUM(gross_revenue) as revenue,
                        SUM(total_bookings) as bookings
                    ')
                    ->groupBy('revenue_date')
                    ->orderBy('revenue_date')
                    ->get();
            } elseif ($chartType === 'weekly') {
                $data = $query->selectRaw('
                        YEAR(revenue_date) as year,
                        WEEK(revenue_date) as week,
                        SUM(gross_revenue) as revenue,
                        SUM(total_bookings) as bookings
                    ')
                    ->groupBy('year', 'week')
                    ->orderBy('year')
                    ->orderBy('week')
                    ->get();
            } else {
                $monthlyQuery = MonthlyStatistic::query();

                // Apply the same filters to monthly statistics
                if ($businessId && $businessId !== 'all') {
                    $monthlyQuery->where('business_id', $businessId);
                }

                if ($branchId && $branchId !== 'all') {
                    $monthlyQuery->where('branch_id', $branchId);
                } else if ($businessId && $businessId !== 'all') {
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    if (!empty($branchIds)) {
                        $monthlyQuery->whereIn('branch_id', $branchIds);
                    }
                }



                $data = $monthlyQuery->selectRaw('
                        year,
                        month,
                        SUM(total_revenue) as revenue,
                        SUM(total_bookings) as bookings
                    ')
                    ->groupBy('year', 'month')
                    ->orderBy('year')
                    ->orderBy('month')
                    ->get();
            }

            return response()->json([
                'success' => true,
                'data' => $data,
                'chart_type' => $chartType,
                'message' => 'Revenue trends retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving revenue trends: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving revenue trends',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get revenue comparison between periods
     */
    public function getRevenueComparison(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');

            $currentRange = $this->getDateRange($timeRange);
            $currentQuery = DailyRevenue::whereBetween('revenue_date', $currentRange);

            if ($businessId) {
                $currentQuery->where('business_id', $businessId);
            }

            $currentData = $currentQuery->selectRaw('
                    DATE(revenue_date) as date,
                    SUM(gross_revenue) as revenue,
                    SUM(total_bookings) as bookings
                ')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            $previousRange = $this->getPreviousPeriodRange($timeRange);
            $previousQuery = DailyRevenue::whereBetween('revenue_date', $previousRange);

            if ($businessId) {
                $previousQuery->where('business_id', $businessId);
            }

            $previousData = $previousQuery->selectRaw('
                    DATE(revenue_date) as date,
                    SUM(gross_revenue) as revenue,
                    SUM(total_bookings) as bookings
                ')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'current_period' => $currentData,
                    'previous_period' => $previousData,
                    'current_range' => $currentRange,
                    'previous_range' => $previousRange
                ],
                'message' => 'Revenue comparison retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving revenue comparison: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving revenue comparison',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get hourly revenue analysis
     */
    public function getHourlyAnalysis(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $date = $request->get('date', Carbon::today()->format('Y-m-d'));
            $businessId = $request->get('businessId');
            $query = HourlyRevenue::where('revenue_date', $date);

            if ($businessId) {
                $query->where('business_id', $businessId);
            }

            $data = $query->selectRaw('
                    hour_slot,
                    SUM(total_revenue) as hourly_revenue,
                    SUM(total_bookings) as hourly_bookings
                ')
                ->groupBy('hour_slot')
                ->orderBy('hour_slot')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $data,
                'date' => $date,
                'message' => 'Hourly analysis retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving hourly analysis: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving hourly analysis',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get daily revenue analysis
     */
    public function getDailyAnalysis(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);
            $query = DailyRevenue::whereBetween('revenue_date', $dateRange);

            if ($businessId) {
                $query->where('business_id', $businessId);
            }

            $data = $query->selectRaw('
                    revenue_date,
                    SUM(gross_revenue) as daily_revenue,
                    SUM(total_bookings) as daily_bookings,
                    SUM(confirmed_bookings) as confirmed_bookings,
                    SUM(cancelled_bookings) as cancelled_bookings
                ')
                ->groupBy('revenue_date')
                ->orderBy('revenue_date')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $data,
                'time_range' => $timeRange,
                'message' => 'Daily analysis retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving daily analysis: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving daily analysis',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get monthly revenue analysis
     */
    public function getMonthlyAnalysis(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $year = $request->get('year', Carbon::now()->year);
            $businessId = $request->get('businessId');

            $query = MonthlyStatistic::where('year', $year);

            if ($businessId) {
                $query->where('business_id', $businessId);
            }

            $data = $query->selectRaw('
                    month,
                    SUM(total_revenue) as monthly_revenue,
                    SUM(total_bookings) as monthly_bookings,
                    AVG(growth_rate) as avg_growth_rate
                ')
                ->groupBy('month')
                ->orderBy('month')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $data,
                'year' => $year,
                'message' => 'Monthly analysis retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving monthly analysis: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving monthly analysis',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get top performing businesses
     */
    public function getTopBusinesses(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $limit = $request->get('limit', 10);
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            // Generate cache key
            $cacheKey = 'top_businesses_' . md5(json_encode([
                'timeRange' => $timeRange,
                'limit' => $limit,
                'startDate' => $startDate,
                'endDate' => $endDate
            ]));

            // Try to get from cache first (cache for 30 minutes)
            $data = Cache::remember($cacheKey, 1800, function () use ($timeRange, $limit, $startDate, $endDate) {
                $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);

                // Use optimized query with specific columns and joins
                return DB::table('daily_revenues')
                    ->join('businesses', 'daily_revenues.business_id', '=', 'businesses.id')
                    ->whereNull('daily_revenues.court_id') // Only include branch-level records
                    ->whereBetween('revenue_date', $dateRange)
                    ->select([
                        'businesses.id',
                        'businesses.name',
                        'businesses.logo_url',
                        DB::raw('SUM(daily_revenues.gross_revenue) as total_revenue'),
                        DB::raw('SUM(daily_revenues.total_bookings) as total_bookings'),
                        DB::raw('AVG(daily_revenues.gross_revenue/NULLIF(daily_revenues.total_bookings, 0)) as avg_booking_value'),
                        DB::raw('COUNT(DISTINCT daily_revenues.branch_id) as branch_count')
                    ])
                    ->groupBy('businesses.id', 'businesses.name', 'businesses.logo_url')
                    ->orderByDesc('total_revenue')
                    ->limit($limit)
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $data,
                'time_range' => $timeRange,
                'message' => 'Top businesses retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving top businesses: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving top businesses',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get top performing branches
     */
    public function getTopBranches(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');
            $limit = $request->get('limit', 10);
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            // Generate cache key
            $cacheKey = 'top_branches_' . md5(json_encode([
                'timeRange' => $timeRange,
                'businessId' => $businessId,
                'limit' => $limit,
                'startDate' => $startDate,
                'endDate' => $endDate
            ]));

            // Try to get from cache first (cache for 30 minutes)
            $data = Cache::remember($cacheKey, 1800, function () use ($timeRange, $businessId, $limit, $startDate, $endDate) {
                $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);

                // Start building the query with specific columns
                $query = DB::table('daily_revenues')
                    ->join('branches', 'daily_revenues.branch_id', '=', 'branches.id')
                    ->join('businesses', 'daily_revenues.business_id', '=', 'businesses.id')
                    ->whereNull('daily_revenues.court_id') // Only include branch-level records
                    ->whereBetween('revenue_date', $dateRange);

                // Filter by business if specified
                if ($businessId && $businessId !== 'all') {
                    $query->where('daily_revenues.business_id', $businessId)
                        ->where('branches.business_id', $businessId); // Ensure branches belong to the business
                }

                // Complete the query with selections, grouping and ordering
                return $query->select([
                    'branches.id',
                    'branches.name as branch_name',
                    'businesses.name as business_name',
                    'branches.address',
                    DB::raw('SUM(daily_revenues.gross_revenue) as total_revenue'),
                    DB::raw('SUM(daily_revenues.total_bookings) as total_bookings'),
                    DB::raw('AVG(daily_revenues.gross_revenue/NULLIF(daily_revenues.total_bookings, 0)) as avg_booking_value'),
                    DB::raw('CASE 
                        WHEN SUM(daily_revenues.gross_revenue) > 500000000 THEN "Xuất sắc"
                        WHEN SUM(daily_revenues.gross_revenue) > 300000000 THEN "Tốt"
                        WHEN SUM(daily_revenues.gross_revenue) > 100000000 THEN "Trung bình"
                        ELSE "Cần cải thiện"
                    END as performance_rating')
                ])
                    ->groupBy('branches.id', 'branches.name', 'businesses.name', 'branches.address')
                    ->orderByDesc('total_revenue')
                    ->limit($limit)
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $data,
                'time_range' => $timeRange,
                'message' => 'Top branches retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving top branches: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving top branches',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get top performing courts
     */
    public function getTopCourts(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $timeRange = $request->get('timeRange', 'month');
            $businessId = $request->get('businessId');
            $branchId = $request->get('branchId');
            $limit = $request->get('limit', 10);
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            $dateRange = $this->getDateRange($timeRange, $startDate, $endDate);

            $query = DailyRevenue::join('courts', 'daily_revenues.court_id', '=', 'courts.id')
                ->join('branches', 'daily_revenues.branch_id', '=', 'branches.id')
                ->join('businesses', 'daily_revenues.business_id', '=', 'businesses.id')
                ->whereBetween('revenue_date', $dateRange);

            // Filter by business if specified
            if ($businessId && $businessId !== 'all') {
                $query->where('daily_revenues.business_id', $businessId);
                $query->where('branches.business_id', $businessId); // Ensure branches belong to the business
            }

            // Filter by branch if specified
            if ($branchId && $branchId !== 'all') {
                $query->where('daily_revenues.branch_id', $branchId);
                $query->where('courts.branch_id', $branchId); // Ensure courts belong to the branch

                // If business is also specified, validate that the branch belongs to the business
                if ($businessId && $businessId !== 'all') {
                    $branchBelongsToBusiness = Branch::where('id', $branchId)
                        ->where('business_id', $businessId)
                        ->exists();

                    if (!$branchBelongsToBusiness) {
                        return response()->json([
                            'success' => false,
                            'message' => 'The specified branch does not belong to the selected business'
                        ], 400);
                    }
                }
            } else if ($businessId && $businessId !== 'all') {
                // If business is specified but branch is 'all', get all branches for that business
                $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                if (!empty($branchIds)) {
                    $query->whereIn('daily_revenues.branch_id', $branchIds);
                    $query->whereIn('courts.branch_id', $branchIds);
                }
            }



            $data = $query->selectRaw('
                    courts.id,
                    courts.name as court_name,
                    branches.name as branch_name,
                    businesses.name as business_name,
                    SUM(daily_revenues.gross_revenue) as total_revenue,
                    SUM(daily_revenues.total_bookings) as total_bookings,
                    ROUND((SUM(daily_revenues.total_bookings) / COUNT(DISTINCT daily_revenues.revenue_date)) * 100, 2) as utilization_rate
                ')
                ->groupBy('courts.id', 'courts.name', 'branches.name', 'businesses.name')
                ->orderByDesc('total_revenue')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $data,
                'time_range' => $timeRange,
                'message' => 'Top courts retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving top courts: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving top courts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get detailed revenue report with pagination
     */
    public function getDetailedReport(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'businessId' => 'nullable',
                'branchId' => 'nullable',
                'per_page' => 'nullable|integer|min:1|max:100',
                'search' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Generate cache key based on request parameters
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);
            $search = $request->get('search');

            $cacheKey = 'detailed_report_' . md5(json_encode([
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
                'businessId' => $request->businessId,
                'branchId' => $request->branchId,
                'per_page' => $perPage,
                'page' => $page,
                'search' => $search
            ]));

            // Try to get from cache first (cache for 15 minutes)
            $data = Cache::remember($cacheKey, 900, function () use ($request, $perPage, $search) {
                // Start with an optimized query - select only needed columns
                $query = DailyRevenue::with([
                    'business:id,name',
                    'branch:id,name,address'
                ])
                    ->select([
                        'id',
                        'business_id',
                        'branch_id',
                        'revenue_date',
                        'total_bookings',
                        'confirmed_bookings',
                        'cancelled_bookings',
                        'gross_revenue',
                        'commission_amount',
                        'net_revenue',
                    ])
                    ->whereNull('court_id'); // Only include branch-level records

                // Date range filters
                if ($request->has('date_from')) {
                    $query->where('revenue_date', '>=', $request->date_from);
                }

                if ($request->has('date_to')) {
                    $query->where('revenue_date', '<=', $request->date_to);
                }

                // Business filter
                $businessId = $request->get('businessId');
                if ($businessId && $businessId !== 'all') {
                    $query->where('business_id', $businessId);
                }

                // Branch filter with business validation
                $branchId = $request->get('branchId');
                if ($branchId && $branchId !== 'all') {
                    $query->where('branch_id', $branchId);

                    // If business is also specified, validate that the branch belongs to the business
                    if ($businessId && $businessId !== 'all') {
                        $branchBelongsToBusiness = Branch::where('id', $branchId)
                            ->where('business_id', $businessId)
                            ->exists();

                        if (!$branchBelongsToBusiness) {
                            return [
                                'error' => true,
                                'message' => 'The specified branch does not belong to the selected business'
                            ];
                        }
                    }
                } else if ($businessId && $businessId !== 'all') {
                    // If business is specified but branch is 'all', get all branches for that business
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    if (!empty($branchIds)) {
                        $query->whereIn('branch_id', $branchIds);
                    }
                }

                // Search functionality
                if ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->whereHas('business', function ($subQ) use ($search) {
                            $subQ->where('name', 'like', '%' . $search . '%');
                        })
                            ->orWhereHas('branch', function ($subQ) use ($search) {
                                $subQ->where('name', 'like', '%' . $search . '%')
                                    ->orWhere('address', 'like', '%' . $search . '%');
                            });
                    });
                }

                // Use a covering index for sorting
                return $query->orderBy('revenue_date', 'desc')
                    ->paginate($request->get('per_page', 20));
            });

            // If there was an error in the cache callback
            if (is_array($data) && isset($data['error']) && $data['error'] === true) {
                return response()->json([
                    'success' => false,
                    'message' => $data['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'Detailed report retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving detailed report: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving detailed report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export revenue report
     */
    public function exportRevenueReport(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'format' => 'required|in:csv,excel,pdf',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'businessId' => 'nullable',
                'branchId' => 'nullable',
                'paymentMethod' => 'nullable|string',
                'revenueType' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = DailyRevenue::with(['business:id,name', 'branch:id,name,address']);

            // Date range filters
            if ($request->has('date_from')) {
                $query->where('revenue_date', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('revenue_date', '<=', $request->date_to);
            }

            // Business filter
            $businessId = $request->get('businessId');
            if ($businessId && $businessId !== 'all') {
                $query->where('business_id', $businessId);
            }

            // Branch filter with business validation
            $branchId = $request->get('branchId');
            if ($branchId && $branchId !== 'all') {
                $query->where('branch_id', $branchId);

                // If business is also specified, validate that the branch belongs to the business
                if ($businessId && $businessId !== 'all') {
                    $branchBelongsToBusiness = Branch::where('id', $branchId)
                        ->where('business_id', $businessId)
                        ->exists();

                    if (!$branchBelongsToBusiness) {
                        return response()->json([
                            'success' => false,
                            'message' => 'The specified branch does not belong to the selected business'
                        ], 400);
                    }
                }
            } else if ($businessId && $businessId !== 'all') {
                // If business is specified but branch is 'all', get all branches for that business
                $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                if (!empty($branchIds)) {
                    $query->whereIn('branch_id', $branchIds);
                }
            }



            $data = $query->orderBy('revenue_date', 'desc')->get();

            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "revenue_report_{$timestamp}.{$request->format}";

            $content = $this->generateReportContent($data, $request->format);

            return response()->json([
                'success' => true,
                'message' => 'Report exported successfully',
                'data' => [
                    'filename' => $filename,
                    'download_url' => url("/api/downloads/{$filename}"),
                    'records_count' => $data->count(),
                    'export_format' => $request->format,
                    'content' => $content
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error exporting revenue report: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error exporting report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time revenue data
     */
    public function getRealTimeRevenue(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $today = Carbon::today();

            $todayStats = DailyRevenue::where('revenue_date', $today)
                ->selectRaw('
                SUM(gross_revenue) as today_revenue,
                SUM(total_bookings) as today_bookings,
                SUM(confirmed_bookings) as confirmed_bookings
            ')
                ->first();

            $currentHour = now()->hour;
            $hourlyStats = HourlyRevenue::where('revenue_date', $today)
                ->where('hour_slot', $currentHour)
                ->selectRaw('
                SUM(total_revenue) as current_hour_revenue,
                SUM(total_bookings) as current_hour_bookings
            ')
                ->first();

            $recentBookings = Booking::with(['branch:id,name'])
                ->where('created_at', '>=', now()->subHours(2))
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'today_stats' => $todayStats,
                    'current_hour_stats' => $hourlyStats,
                    'recent_bookings' => $recentBookings,
                    'last_updated' => now()->toISOString()
                ],
                'message' => 'Real-time data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving real-time data: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving real-time data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get live bookings data
     */
    public function getLiveBookings(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if (!$user || !$user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Super admin access required.'
                ], 403);
            }

            $limit = $request->get('limit', 20);

            $liveBookings = Booking::with([
                'branch:id,name',
                'customer:id,name,phone',
                'courtBookings:id,booking_id,court_id,start_time,end_time,total_price',
                'courtBookings.court:id,name'
            ])
                ->where('created_at', '>=', now()->subHours(24))
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $liveBookings,
                'message' => 'Live bookings retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving live bookings: ' . $e->getMessage(), [
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving live bookings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to get date range based on time range parameter
     */
    private function getDateRange(string $timeRange, ?string $startDate = null, ?string $endDate = null): array
    {
        $now = Carbon::now();
        if ($timeRange === 'custom' && $startDate && $endDate) {
            return [Carbon::parse($startDate)->startOfDay(), Carbon::parse($endDate)->endOfDay()];
        }
        if ($timeRange === 'custom' && $startDate && !$endDate) {
            return [Carbon::parse($startDate)->startOfDay(), $now->copy()->endOfDay()];
        }
        if ($timeRange === 'custom' && !$startDate && $endDate) {
            return [$now->copy()->subMonth()->startOfDay(), Carbon::parse($endDate)->endOfDay()];
        }

        return match ($timeRange) {
            'today' => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
            'yesterday' => [$now->copy()->subDay()->startOfDay(), $now->copy()->subDay()->endOfDay()],
            'week' => [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
            'month' => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            'quarter' => [$now->copy()->startOfQuarter(), $now->copy()->endOfQuarter()],
            'year' => [$now->copy()->startOfYear(), $now->copy()->endOfYear()],
            'last_7_days' => [$now->copy()->subDays(7), $now],
            'last_30_days' => [$now->copy()->subDays(30), $now],
            'last_90_days' => [$now->copy()->subDays(90), $now],
            default => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()]
        };
    }

    /**
     * Helper method to get previous period range for comparison
     */
    private function getPreviousPeriodRange(string $timeRange): array
    {
        $now = Carbon::now();

        return match ($timeRange) {
            'today' => [$now->copy()->subDay()->startOfDay(), $now->copy()->subDay()->endOfDay()],
            'week' => [$now->copy()->subWeek()->startOfWeek(), $now->copy()->subWeek()->endOfWeek()],
            'month' => [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()],
            'quarter' => [$now->copy()->subQuarter()->startOfQuarter(), $now->copy()->subQuarter()->endOfQuarter()],
            'year' => [$now->copy()->subYear()->startOfYear(), $now->copy()->subYear()->endOfYear()],
            'last_7_days' => [$now->copy()->subDays(14), $now->copy()->subDays(7)],
            'last_30_days' => [$now->copy()->subDays(60), $now->copy()->subDays(30)],
            'last_90_days' => [$now->copy()->subDays(180), $now->copy()->subDays(90)],
            default => [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()]
        };
    }

    /**
     * Helper method to get period label for display
     */
    private function getPeriodLabel(string $timeRange): string
    {
        return match ($timeRange) {
            'today' => 'Hôm nay',
            'yesterday' => 'Hôm qua',
            'week' => 'Tuần này',
            'month' => 'Tháng này',
            'quarter' => 'Quý này',
            'year' => 'Năm này',
            'last_7_days' => '7 ngày qua',
            'last_30_days' => '30 ngày qua',
            'last_90_days' => '90 ngày qua',
            default => 'Tháng này'
        };
    }

    /**
     * Generate report content based on format
     */
    private function generateReportContent($data, string $format): string
    {
        switch ($format) {
            case 'csv':
                $content = "Ngày,Doanh nghiệp,Chi nhánh,Tổng booking,Booking xác nhận,Doanh thu thô,Hoa hồng,Doanh thu ròng\n";
                foreach ($data as $row) {
                    $content .= sprintf(
                        '"%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                        $row->revenue_date->format('d/m/Y'),
                        $row->business ? $row->business->name : 'N/A',
                        $row->branch ? $row->branch->name : 'N/A',
                        $row->total_bookings,
                        $row->confirmed_bookings,
                        number_format($row->gross_revenue, 0, ',', '.'),
                        number_format($row->commission_amount, 0, ',', '.'),
                        number_format($row->net_revenue, 0, ',', '.')
                    );
                }
                break;

            case 'excel':
                $content = $this->generateReportContent($data, 'csv');
                break;

            case 'pdf':
                $content = "BÁO CÁO DOANH THU\n";
                $content .= "Ngày tạo: " . now()->format('d/m/Y H:i:s') . "\n";
                $content .= str_repeat("=", 80) . "\n\n";

                foreach ($data as $row) {
                    $content .= "Ngày: " . $row->revenue_date->format('d/m/Y') . "\n";
                    $content .= "Doanh nghiệp: " . ($row->business ? $row->business->name : 'N/A') . "\n";
                    $content .= "Chi nhánh: " . ($row->branch ? $row->branch->name : 'N/A') . "\n";
                    $content .= "Tổng booking: " . $row->total_bookings . "\n";
                    $content .= "Doanh thu thô: " . number_format($row->gross_revenue, 0, ',', '.') . " VNĐ\n";
                    $content .= "Doanh thu ròng: " . number_format($row->net_revenue, 0, ',', '.') . " VNĐ\n";
                    $content .= str_repeat("-", 40) . "\n";
                }
                break;

            default:
                $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }

        return $content;
    }
}

