<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Role;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        try {
            // Start timing the login process
            $startTime = microtime(true);

            // Authenticate user
            $request->authenticate();
            $authTime = microtime(true);
            Log::info('Authentication time: ' . ($authTime - $startTime) . ' seconds');

            // Regenerate session
            $request->session()->regenerate();
            $sessionTime = microtime(true);
            Log::info('Session regeneration time: ' . ($sessionTime - $authTime) . ' seconds');

            // Get user and redirect URL
            $user = Auth::user();
            $redirectUrl = $request->input('redirectAfterLogin') ?? $request->query('redirect');

            // Check for admin role - use eager loading to avoid N+1 query
            $roleStartTime = microtime(true);
            $adminRoles = ['super-admin', 'admin', 'manager', 'staff'];

            // Use more efficient query with proper indexing
            $hasAdminRole = DB::table('model_has_roles')
                ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                ->where('model_has_roles.model_id', $user->id)
                ->where('model_has_roles.model_type', get_class($user))
                ->whereIn('roles.name', $adminRoles)
                ->exists();

            $roleCheckTime = microtime(true);
            Log::info('Role check time: ' . ($roleCheckTime - $roleStartTime) . ' seconds');

            // Handle redirects
            $redirectStartTime = microtime(true);
            if ($hasAdminRole) {
                $result = redirect()->intended(route('dashboard', absolute: false));
            } elseif ($redirectUrl) {
                $result = redirect()->intended($redirectUrl)
                    ->with('flash.success', 'Đăng nhập thành công !');
            } else {
                $result = redirect()->intended(route('about', absolute: false))
                    ->with('flash.success', 'Đăng nhập thành công !');
            }

            $redirectEndTime = microtime(true);
            Log::info('Redirect generation time: ' . ($redirectEndTime - $redirectStartTime) . ' seconds');
            Log::info('Total login processing time: ' . ($redirectEndTime - $startTime) . ' seconds');
            return $result;
        } catch (\Exception $e) {
            Log::error('Authentication error: ' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        try {
            // Start timing the logout process
            $startTime = microtime(true);

            Auth::guard('web')->logout();
            $logoutTime = microtime(true);
            Log::info('Logout time: ' . ($logoutTime - $startTime) . ' seconds');

            $request->session()->invalidate();
            $invalidateTime = microtime(true);
            Log::info('Session invalidation time: ' . ($invalidateTime - $logoutTime) . ' seconds');

            $request->session()->regenerateToken();
            $regenerateTime = microtime(true);
            Log::info('Token regeneration time: ' . ($regenerateTime - $invalidateTime) . ' seconds');

            $redirectStartTime = microtime(true);
            $result = redirect('/');
            $redirectEndTime = microtime(true);
            Log::info('Redirect generation time: ' . ($redirectEndTime - $redirectStartTime) . ' seconds');
            Log::info('Total logout processing time: ' . ($redirectEndTime - $startTime) . ' seconds');

            return $result;
        } catch (\Exception $e) {
            Log::error('Logout error: ' . $e->getMessage());
            throw $e;
        }
    }
}
