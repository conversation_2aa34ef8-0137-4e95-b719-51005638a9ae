<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class Facebook<PERSON>uthController extends Controller
{
    /**
     * Handle Facebook authentication from frontend.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function authenticate(Request $request)
    {
        $request->validate([
            'userID' => 'required',
            'accessToken' => 'required',
            'profile' => 'required|array',
            'profile.id' => 'required',
            'profile.name' => 'required',
            'profile.email' => 'nullable|email',
        ]);

        $profile = $request->profile;

        // Check if user exists based on Facebook ID
        $user = User::where('provider', 'facebook')
            ->where('provider_id', $profile['id'])
            ->first();

        if (!$user && isset($profile['email'])) {
            // If no user found with Facebook ID, check by email
            $user = User::where('email', $profile['email'])->first();

            if ($user) {
                // If user exists with email, update their Facebook info
                $user->update([
                    'provider' => 'facebook',
                    'provider_id' => $profile['id'],
                    'provider_avatar' => "https://graph.facebook.com/{$profile['id']}/picture?type=large",
                ]);
            }
        }

        if (!$user) {
            // Create new user if doesn't exist
            $user = User::create([
                'name' => $profile['name'],
                'email' => $profile['email'] ?? "{$profile['id']}@facebook.com",
                'password' => Hash::make(Str::random(24)),
                'avatar_url' => "https://graph.facebook.com/{$profile['id']}/picture?type=large",
                'provider' => 'facebook',
                'provider_id' => $profile['id'],
                'provider_avatar' => "https://graph.facebook.com/{$profile['id']}/picture?type=large",
                'is_verified' => true,
            ]);

            // Create a corresponding customer record
            Customer::create([
                'user_id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => null,
            ]);
        }

        // Log the user in
        Auth::login($user, true);

        return response()->json([
            'success' => true,
            'redirect' => route('dashboard'),
        ]);
    }
}