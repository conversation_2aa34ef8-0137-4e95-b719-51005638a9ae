<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;
use App\Services\SystemSettingService;
use App\Providers\RouteServiceProvider;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Register');
    }

    /**
     * Handle an incoming registration request.
     */
    public function store(RegisterRequest $request)
    {
        try {
            DB::beginTransaction();
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'status' => 'active',
                'is_verified' => false,
                'notification_preferences' => [
                    'email' => true,
                    'push' => true,
                    'sms' => true
                ],
            ]);

            \App\Models\Customer::create([
                'user_id' => $user->id,
                'name' => $request->name,
                'phone' => $request->phone,
                'email' => $request->email,
            ]);

            $customerRole = Role::where('name', 'Customer')->first();
            $userRole = Role::where('name', 'user')->first() ?? Role::where('name', 'User')->first();

            if ($customerRole) {
                $user->assignRole($customerRole);
            }

            if ($userRole) {
                $user->assignRole($userRole);
            } else {
                $userRole = Role::create([
                    'name' => 'user',
                    'guard_name' => 'web',
                    'description' => 'Regular user with basic permissions'
                ]);
                $user->assignRole($userRole);
            }

            // Only send verification email if email verification is enabled
            $emailVerificationEnabled = SystemSettingService::get('email_verification') === 'on';
            if ($emailVerificationEnabled) {
                event(new Registered($user));

                // Redirect to verification notice
                Auth::login($user);
                return redirect()->route('verification.notice');
            } else {
                // Mark email as verified automatically if verification is disabled
                $user->markEmailAsVerified();

                // Login user and redirect to dashboard
                Auth::login($user);
                return redirect()->intended(RouteServiceProvider::HOME);
            }

            DB::commit();

            // Use Inertia redirect with proper parameters
            return redirect()->to('/login?tab=login')->with('flash.success', 'Đăng ký thành công !');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors([
                'flash.error' => 'Có lỗi xảy ra trong quá trình đăng ký. Vui lòng thử lại.'
            ]);
        }
    }
}
