<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Branch;
use App\Models\CourtBooking;
use App\Models\Court;
use App\Services\BookingEventService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class BookingCheckController extends Controller
{
    /**
     * Display the check-in/check-out management page
     *
     * @param Request $request
     * @return \Inertia\Response|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userType = $this->getUserType($user);

        $branch = $this->getBranchByUserType($user, $request);

        $date = $request->date ?? Carbon::now()->format('Y-m-d');
        $confirmedBookings = $this->getConfirmedBookings($branch, $date, $userType);
        $checkedInBookings = $this->getCheckedInBookings($branch, $date, $userType);
        $completedBookings = $this->getCompletedBookings($branch, $date, $userType);
        $notCompletedBookings = $this->getNotCompletedBookings($branch, $date, $userType);

        if ($request->wantsJson()) {

            if ($userType === 'superadmin') {

                $enhanceBookings = function ($bookings) {
                    return $bookings->map(function ($booking) {
                        if ($booking['branch_id']) {
                            $branch = \App\Models\Branch::with('business')->find($booking['branch_id']);
                            if ($branch && $branch->business) {
                                $booking['business_id'] = $branch->business->id;
                                $booking['business_name'] = $branch->business->name;
                            }
                        }
                        return $booking;
                    });
                };


                $confirmedBookings = $enhanceBookings($confirmedBookings);
                $checkedInBookings = $enhanceBookings($checkedInBookings);
                $completedBookings = $enhanceBookings($completedBookings);
                $notCompletedBookings = $enhanceBookings($notCompletedBookings);
            }

            return response()->json([
                'success' => true,
                'confirmedBookings' => $confirmedBookings,
                'checkedInBookings' => $checkedInBookings,
                'completedBookings' => $completedBookings,
                'notCompletedBookings' => $notCompletedBookings,
                'message' => 'Data has been updated.'
            ]);
        }


        if ($userType === 'superadmin' || $userType === 'business') {

            $enhanceBookings = function ($bookings) use ($userType) {
                return $bookings->map(function ($booking) use ($userType) {
                    if ($userType === 'superadmin') {

                        if ($booking['branch_id']) {
                            $branch = \App\Models\Branch::with('business')->find($booking['branch_id']);
                            if ($branch && $branch->business) {
                                $booking['business_id'] = $branch->business->id;
                                $booking['business_name'] = $branch->business->name;
                            }
                        }
                    } else if ($userType === 'business') {

                        $booking['branch_detail'] = [
                            'id' => $booking['branch_id'],
                            'name' => $booking['branch_name'],
                            'address' => $booking['branch_id'] ? \App\Models\Branch::find($booking['branch_id'])?->address : null,
                        ];
                    }
                    return $booking;
                });
            };


            $confirmedBookings = $enhanceBookings($confirmedBookings);
            $checkedInBookings = $enhanceBookings($checkedInBookings);
            $completedBookings = $enhanceBookings($completedBookings);
            $notCompletedBookings = $enhanceBookings($notCompletedBookings);
        }

        $allReferenceNumbers = collect([]);
        $confirmedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $checkedInBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $completedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $notCompletedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });

        $uniqueReferenceNumbers = $allReferenceNumbers->unique()->values();

        $referenceGroupsInfo = [];
        foreach ($uniqueReferenceNumbers as $refNumber) {
            $totalCount = CourtBooking::where('reference_number', $refNumber)->count();

            $currentDateCount = $confirmedBookings->where('reference_number', $refNumber)->count() +
                $checkedInBookings->where('reference_number', $refNumber)->count() +
                $completedBookings->where('reference_number', $refNumber)->count() +
                $notCompletedBookings->where('reference_number', $refNumber)->count();

            if ($totalCount > $currentDateCount) {
                $referenceGroupsInfo[$refNumber] = [
                    'total' => $totalCount,
                    'current' => $currentDateCount,
                    'missing' => $totalCount - $currentDateCount
                ];
            }
        }


        if ($userType === 'superadmin') {
            foreach ($referenceGroupsInfo as $refNumber => $info) {
                $missingBookings = CourtBooking::with(['court', 'customer', 'branch.business'])
                    ->where('reference_number', $refNumber)
                    ->whereDate('booking_date', '!=', $date)
                    ->get();

                foreach ($missingBookings as $booking) {
                    $formattedBooking = $this->formatBooking($booking);

                    if ($booking->status === 'completed') {
                        $completedBookings->push($formattedBooking);
                    } elseif ($booking->checkin_status === 'checked_out' && $booking->status !== 'completed') {
                        $notCompletedBookings->push($formattedBooking);
                    } elseif ($booking->checkin_status === 'checked_in') {
                        $checkedInBookings->push($formattedBooking);
                    } elseif ($booking->status === 'confirmed') {
                        $confirmedBookings->push($formattedBooking);
                    }
                }
            }
        } else if ($branch) {

            foreach ($referenceGroupsInfo as $refNumber => $info) {
                $missingBookings = CourtBooking::with(['court', 'customer', 'branch'])
                    ->where('reference_number', $refNumber)
                    ->where('branch_id', $branch->id)
                    ->whereDate('booking_date', '!=', $date)
                    ->get();

                foreach ($missingBookings as $booking) {
                    $formattedBooking = $this->formatBooking($booking);

                    if ($booking->status === 'completed') {
                        $completedBookings->push($formattedBooking);
                    } elseif ($booking->checkin_status === 'checked_out' && $booking->status !== 'completed') {
                        $notCompletedBookings->push($formattedBooking);
                    } elseif ($booking->checkin_status === 'checked_in') {
                        $checkedInBookings->push($formattedBooking);
                    } elseif ($booking->status === 'confirmed') {
                        $confirmedBookings->push($formattedBooking);
                    }
                }
            }
        }

        $sortByDateAndTime = function ($a, $b) {
            $dateComparison = strcmp($a['booking_date'], $b['booking_date']);
            if ($dateComparison !== 0) {
                return $dateComparison;
            }
            return strcmp($a['start_time'], $b['start_time']);
        };

        $confirmedBookings = $confirmedBookings->sort($sortByDateAndTime)->values();
        $checkedInBookings = $checkedInBookings->sort($sortByDateAndTime)->values();
        $completedBookings = $completedBookings->sort($sortByDateAndTime)->values();
        $notCompletedBookings = $notCompletedBookings->sort($sortByDateAndTime)->values();


        $viewPath = $this->getViewPathByUserType($userType);

        return Inertia::render($viewPath, [
            'branch' => $branch,
            'currentDate' => $date,
            'confirmedBookings' => $confirmedBookings,
            'checkedInBookings' => $checkedInBookings,
            'completedBookings' => $completedBookings,
            'notCompletedBookings' => $notCompletedBookings,
            'filters' => $request->all('date'),
            'referenceGroups' => $referenceGroupsInfo,
        ]);
    }

    /**
     * Perform check-in for a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkIn(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $userId = Auth::id();
        $isForced = $request->input('force_checkin', false);

        if ($booking->status !== 'confirmed' && !$isForced) {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải được xác nhận trước khi check-in'
            ], 400);
        }

        if ($booking->checkin_status === 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân đã được check-in'
            ], 400);
        }

        if (!$isForced) {
            if (strlen($booking->start_time) > 8) {
                $startDateTime = \Carbon\Carbon::parse($booking->start_time);
            } else {
                $bookingStartTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->start_time;
                $startDateTime = \Carbon\Carbon::parse($bookingStartTime);
            }

            $now = now();

            if ($now->lt($startDateTime->copy()->subMinutes(30))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chưa đến thời gian check-in (sớm hơn 30 phút so với giờ bắt đầu)'
                ], 400);
            }

            if (strlen($booking->end_time) > 8) {
                $endDateTime = \Carbon\Carbon::parse($booking->end_time);
            } else {
                $bookingEndTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->end_time;
                $endDateTime = \Carbon\Carbon::parse($bookingEndTime);
            }

            if ($now->gt($endDateTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Đã quá thời gian check-in (sau giờ kết thúc đặt sân)'
                ], 400);
            }
        }

        $success = $booking->checkIn($userId);


        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Check-in thành công',
                'booking' => $booking
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Có lỗi xảy ra khi check-in'
        ], 500);
    }

    /**
     * Perform check-out for a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkOut(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $userId = Auth::id();

        if ($booking->checkin_status !== 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải đã được check-in trước khi check-out'
            ], 400);
        }

        $checkoutAll = $request->input('checkout_all', false);
        $forceCheckout = $request->input('force_checkout', false);
        $referenceNumber = $booking->reference_number;

        if ($checkoutAll) {
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('checkin_status', 'checked_in')
                ->where('id', '!=', $booking->id)
                ->get();

            $successCount = 0;
            $failedCount = 0;

            $success = $booking->checkOut($userId);

            if ($success && $booking->overtime_minutes > 0) {
                $this->calculateOvertimeFee($booking);
            }

            if ($success) {
                $successCount++;
            } else {
                $failedCount++;
            }

            foreach ($relatedBookings as $relatedBooking) {
                $relatedSuccess = $relatedBooking->checkOut($userId);

                if ($relatedSuccess && $relatedBooking->overtime_minutes > 0) {
                    $this->calculateOvertimeFee($relatedBooking);
                }

                if ($relatedSuccess) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            if ($failedCount === 0) {
                return response()->json([
                    'success' => true,
                    'message' => "Đã check-out thành công tất cả {$successCount} sân với mã tham chiếu {$referenceNumber}",
                    'booking' => $booking,
                    'all_completed' => true,
                    'checkout_count' => $successCount
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => "Đã check-out thành công {$successCount} sân, {$failedCount} sân thất bại",
                    'booking' => $booking,
                    'all_completed' => false,
                    'checkout_count' => $successCount,
                    'failed_count' => $failedCount
                ]);
            }
        } else {
            $success = $booking->checkOut($userId);

            if ($success) {
                if ($booking->overtime_minutes > 0) {
                    $this->calculateOvertimeFee($booking);
                }

                $allBookings = CourtBooking::where('reference_number', $referenceNumber)->get();
                $allCheckedOut = true;

                foreach ($allBookings as $relatedBooking) {
                    if ($relatedBooking->checkin_status !== 'checked_out') {
                        $allCheckedOut = false;
                        break;
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => $allCheckedOut ?
                        'Đã check-out thành công. Tất cả các sân đã được check-out.' :
                        'Đã check-out thành công',
                    'booking' => $booking,
                    'all_checked_out' => $allCheckedOut,
                    'remaining_bookings' => $allCheckedOut ? 0 : $allBookings->where('checkin_status', '!=', 'checked_out')->count()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi check-out'
            ], 500);
        }
    }

    /**
     * Process overtime payment
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processOvertimePayment(Request $request)
    {
        $request->validate([
            'reference_number' => 'required|string',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'amount_received' => 'required_if:payment_method,cash|numeric|min:0',
        ]);

        try {
            \Illuminate\Support\Facades\DB::beginTransaction();

            $bookings = CourtBooking::where('reference_number', $request->reference_number)
                ->where('checkin_status', 'checked_out')
                ->where('overtime_fee', '>', 0)
                ->whereNull('overtime_payment_id')
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy đơn đặt sân cần thanh toán phí phát sinh'
                ], 404);
            }

            $totalAmount = $bookings->sum('overtime_fee');

            $payment = new \App\Models\Payment([
                'reference_number' => $request->reference_number,
                'amount' => $totalAmount,
                'payment_method_id' => $request->payment_method_id,
                'status' => 'completed',
                'type' => 'overtime',
                'staff_id' => Auth::id(),
                'metadata' => [
                    'amount_received' => $request->amount_received,
                    'change_amount' => $request->amount_received ? ($request->amount_received - $totalAmount) : 0,
                ]
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->overtime_payment_id = $payment->id;
                $booking->save();
            }

            \Illuminate\Support\Facades\DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thanh toán phí phát sinh thành công',
                'payment' => $payment
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xử lý thanh toán: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate overtime fee based on checkout time and court price
     *
     * @param CourtBooking $booking
     * @return bool
     */
    private function calculateOvertimeFee(CourtBooking $booking)
    {
        $overtimeMinutes = $booking->overtime_minutes;
        if ($overtimeMinutes <= 0) {
            return false;
        }

        try {
            $court = Court::find($booking->court_id);
            if (!$court) {
                return false;
            }

            $checkoutTime = $booking->checkout_time;
            $hour = $checkoutTime->format('H:i');
            $date = $booking->booking_date->format('Y-m-d');

            $priceModel = \App\Models\CourtPrice::getPriceForDateTime(
                $booking->branch_id,
                $court->court_type,
                $date,
                $hour
            );

            if ($priceModel) {
                $hourlyRate = $booking->is_member_price && $priceModel->member_price_per_hour ?
                    $priceModel->member_price_per_hour :
                    $priceModel->price_per_hour;

                \Illuminate\Support\Facades\Log::info('Tính phí chênh lệch: ', [
                    'booking_id' => $booking->id,
                    'branch_id' => $booking->branch_id,
                    'court_type' => $court->court_type,
                    'checkout_time' => $hour,
                    'date' => $date,
                    'price_model' => $priceModel->toArray(),
                    'is_member' => $booking->is_member_price,
                    'hourly_rate' => $hourlyRate,
                    'overtime_minutes' => $booking->overtime_minutes
                ]);

                return $booking->calculateOvertimeFee($hourlyRate);
            }

            if ($court->price_per_hour > 0) {
                $hourlyRate = $booking->is_member_price ?
                    ($court->price_per_hour * 0.85) :
                    $court->price_per_hour;

                \Illuminate\Support\Facades\Log::info('Sử dụng giá mặc định của sân: ', [
                    'booking_id' => $booking->id,
                    'court_id' => $court->id,
                    'price_per_hour' => $court->price_per_hour,
                    'is_member' => $booking->is_member_price,
                    'hourly_rate' => $hourlyRate
                ]);

                return $booking->calculateOvertimeFee($hourlyRate);
            }

            \Illuminate\Support\Facades\Log::warning('Không tìm thấy giá sân cho booking ID: ' . $booking->id);
            return false;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi tính phí chênh lệch: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get confirmed bookings for a branch or all branches (if superadmin) on a specific date
     *
     * @param Branch|null $branch
     * @param string $date
     * @param string $userType
     * @return \Illuminate\Support\Collection
     */
    private function getConfirmedBookings($branch, $date, $userType = 'branch')
    {
        $relations = ['court', 'customer', 'branch', 'payment'];


        if ($userType === 'superadmin') {
            $relations[] = 'branch.business';
        }

        $query = CourtBooking::with($relations)
            ->whereIn('status', ['confirmed', 'pending'])
            ->whereNull('checkin_time')
            ->whereDate('booking_date', $date);


        if ($branch || $userType !== 'superadmin') {
            $query->where('branch_id', $branch ? $branch->id : null);
        }

        $bookings = $query->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get checked-in bookings for a branch or all branches (if superadmin) on a specific date
     *
     * @param Branch|null $branch
     * @param string $date
     * @param string $userType
     * @return \Illuminate\Support\Collection
     */
    private function getCheckedInBookings($branch, $date, $userType = 'branch')
    {
        $relations = ['court', 'customer', 'branch'];


        if ($userType === 'superadmin') {
            $relations[] = 'branch.business';
        }

        $query = CourtBooking::with($relations)
            ->where('checkin_status', 'checked_in')
            ->whereNotNull('checkin_time')
            ->whereNull('checkout_time')
            ->whereDate('booking_date', $date);


        if ($branch || $userType !== 'superadmin') {
            $query->where('branch_id', $branch ? $branch->id : null);
        }

        $bookings = $query->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get bookings that have been checked out but not completed
     *
     * @param Branch|null $branch
     * @param string $date
     * @param string $userType
     * @return \Illuminate\Support\Collection
     */
    private function getNotCompletedBookings($branch, $date, $userType = 'branch')
    {
        $relations = ['court', 'customer', 'branch'];


        if ($userType === 'superadmin') {
            $relations[] = 'branch.business';
        }

        $query = CourtBooking::with($relations)
            ->where('checkin_status', 'checked_out')
            ->where('status', '!=', 'completed')
            ->whereNotNull('checkout_time')
            ->whereDate('booking_date', $date);


        if ($branch || $userType !== 'superadmin') {
            $query->where('branch_id', $branch ? $branch->id : null);
        }

        $bookings = $query->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get completed bookings for a branch or all branches (if superadmin) on a specific date
     *
     * @param Branch|null $branch
     * @param string $date
     * @param string $userType
     * @return \Illuminate\Support\Collection
     */
    private function getCompletedBookings($branch, $date, $userType = 'branch')
    {
        $relations = ['court', 'customer', 'branch'];


        if ($userType === 'superadmin') {
            $relations[] = 'branch.business';
        }

        $query = CourtBooking::with($relations)
            ->where('status', 'completed')
            ->whereDate('booking_date', $date);


        if ($branch || $userType !== 'superadmin') {
            $query->where('branch_id', $branch ? $branch->id : null);
        }

        $bookings = $query->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Format booking data for frontend display
     *
     * @param CourtBooking $booking
     * @return array
     */
    private function formatBooking($booking)
    {
        $now = Carbon::now();
        $duration = null;
        $durationSoFar = null;
        $overtime = null;

        if ($booking->checkin_time && $booking->checkout_time) {
            $checkinTime = Carbon::parse($booking->checkin_time);
            $checkoutTime = Carbon::parse($booking->checkout_time);
            $diffInMinutes = $checkinTime->diffInMinutes($checkoutTime);
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            $duration = [
                'minutes' => $diffInMinutes,
                'text' => $hours . ' giờ ' . $minutes . ' phút'
            ];

            if ($booking->overtime_minutes > 0) {
                $overtimeHours = floor($booking->overtime_minutes / 60);
                $overtimeMinutes = $booking->overtime_minutes % 60;
                $overtime = [
                    'minutes' => $booking->overtime_minutes,
                    'text' => $overtimeHours > 0
                        ? $overtimeHours . ' giờ ' . $overtimeMinutes . ' phút'
                        : $overtimeMinutes . ' phút',
                    'fee' => $booking->overtime_fee,
                    'fee_paid' => $booking->overtime_fee_paid
                ];
            }
        }

        if ($booking->checkin_time && !$booking->checkout_time) {
            $checkinTime = Carbon::parse($booking->checkin_time);
            $diffInMinutes = $checkinTime->diffInMinutes($now);
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            $durationSoFar = [
                'minutes' => $diffInMinutes,
                'text' => $hours . ' giờ ' . $minutes . ' phút'
            ];

            $endTimeDate = $booking->booking_date->format('Y-m-d');
            $endTime = Carbon::parse($endTimeDate . ' ' . $booking->end_time->format('H:i'));

            if ($now->gt($endTime)) {
                $overtimeMinutes = $now->diffInMinutes($endTime);
                $overtimeHours = floor($overtimeMinutes / 60);
                $overtimeMinutesRem = $overtimeMinutes % 60;
                $overtime = [
                    'minutes' => $overtimeMinutes,
                    'text' => $overtimeHours > 0
                        ? $overtimeHours . ' giờ ' . $overtimeMinutesRem . ' phút'
                        : $overtimeMinutesRem . ' phút',
                    'fee' => null,
                    'fee_paid' => false,
                    'is_currently_overtime' => true
                ];
            }
        }

        if ($booking->customer) {
            $customerName = $booking->customer->name;
            $customerPhone = $booking->customer->phone;
        } else {
            $customerName = $booking->customer_name ?? 'Unknown';
            $customerPhone = $booking->customer_phone ?? 'N/A';
        }

        $paymentStatus = 'unpaid';
        if ($booking->status === 'cancelled') {
            $paymentStatus = 'cancelled';
        } else if ($booking->status === 'completed') {
            $paymentStatus = 'completed';
        } else {
            $payment = \App\Models\Payment::where('booking_reference', $booking->reference_number)->first();
            if ($payment) {
                $paymentStatus = $payment->status;
            }
        }


        $courtType = $booking->court && $booking->court->type ? $booking->court->type : 'indoor';

        $formattedBooking = [
            'id' => $booking->id,
            'reference_number' => $booking->reference_number,
            'court_id' => $booking->court_id,
            'court_name' => $booking->court ? $booking->court->name : 'N/A',
            'court_type' => $courtType,
            'customer_id' => $booking->customer_id,
            'customer_name' => $customerName,
            'customer_phone' => $customerPhone,
            'customer_email' => $booking->customer_email,
            'branch_id' => $booking->branch_id,
            'booking_type' => $booking->booking_type,
            'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
            'booking_date' => $booking->booking_date,
            'booking_date_formatted' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
            'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
            'end_time' => Carbon::parse($booking->end_time)->format('H:i'),
            'duration' => $booking->duration,
            'number_of_players' => $booking->number_of_players,
            'price_per_hour' => $booking->price_per_hour,
            'total_price' => $booking->total_price,
            'deposit_amount' => $booking->deposit_amount,
            'overtime_fee' => $booking->overtime_fee,
            'is_member_price' => $booking->is_member_price,
            'status' => $booking->status,
            'payment_status' => $paymentStatus,
            'notes' => $booking->notes,
            'checkin_status' => $booking->checkin_status,
            'checkin_time' => $booking->checkin_time ? Carbon::parse($booking->checkin_time)->format('H:i d/m/Y') : null,
            'checkout_time' => $booking->checkout_time ? Carbon::parse($booking->checkout_time)->format('H:i d/m/Y') : null,
            'duration' => $duration,
            'overtime_fee_paid' => $booking->overtime_fee_paid,
            'duration_so_far' => $durationSoFar,
            'overtime' => $overtime,
            'created_at' => $booking->created_at,
            'updated_at' => $booking->updated_at
        ];


        if ($booking->branch && $booking->branch->business) {
            $formattedBooking['business_id'] = $booking->branch->business->id;
            $formattedBooking['business_name'] = $booking->branch->business->name;
        }

        return $formattedBooking;
    }

    /**
     * Get user type (branch, business, superadmin)
     *
     * @param \App\Models\User $user
     * @return string
     */
    private function getUserType($user)
    {
        if ($user->hasRole('super-admin')) {
            return 'superadmin';
        } elseif ($user->hasRole('admin')) {
            return 'business';
        } else {
            return 'branch';
        }
    }

    /**
     * Get branch based on user type
     *
     * @param \App\Models\User $user
     * @param Request $request
     * @return Branch|null
     */
    private function getBranchByUserType($user, $request)
    {
        $userType = $this->getUserType($user);

        if ($userType === 'branch') {

            return $user->branch;
        } elseif ($userType === 'business') {

            $branchId = $request->branch_id ?? $user->currentBranch->id ?? null;
            if ($branchId) {
                $branch = Branch::find($branchId);
                if ($branch && $branch->business_id === $user->business_id) {
                    return $branch;
                }
            }

            return Branch::where('business_id', $user->business_id)->first();
        } elseif ($userType === 'superadmin') {

            $branchId = $request->branch_id;
            if ($branchId) {
                return Branch::find($branchId);
            }

            return null;
        }

        return null;
    }

    /**
     * Get view path based on user type
     *
     * @param string $userType
     * @return string
     */
    private function getViewPathByUserType($userType)
    {
        if ($userType === 'superadmin') {
            return 'SuperAdmin/Booking/CheckInOut';
        } elseif ($userType === 'business') {
            return 'Business/Booking/CheckInOut';
        } else {
            return 'Branchs/Booking/CheckInOut';
        }
    }
}