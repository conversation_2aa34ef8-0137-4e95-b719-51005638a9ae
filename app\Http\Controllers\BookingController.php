<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\CourtService;
use App\Models\CourtPrice;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use App\Services\MailService;
use App\Models\Booking;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    /**
     * Determine user context based on role
     *
     * @param \App\Models\User $user
     * @return array
     */
    protected function getUserContext($user)
    {
        $businessId = null;
        $branchId = null;
        $isAdmin = false;
        $role = 'staff';

        if (!$user) {
            return compact('businessId', 'branchId', 'isAdmin', 'role');
        }

        if ($user->hasRole('super-admin')) {
            $isAdmin = true;
            $role = 'superadmin';
        } elseif ($user->hasRole('admin')) {
            $isAdmin = true;
            $businessId = $user->business_id;
            $role = 'business';
        } elseif ($user->hasRole('manager')) {
            $businessId = $user->business_id;
            $branchId = $user->branch_id;
            $role = 'manager';
        } else {
            $businessId = $user->business_id;
            $branchId = $user->branch_id;
            $role = 'staff';
        }


        if ($isAdmin && $requestBranchId = request()->input('branch_id')) {
            if ($businessId) {
                $branch = Branch::where('id', $requestBranchId)
                    ->where('business_id', $businessId)
                    ->first();
                $branchId = $branch ? $branch->id : null;
            } else {
                $branch = Branch::find($requestBranchId);
                $branchId = $branch ? $branch->id : null;
            }
        }

        return compact('businessId', 'branchId', 'isAdmin', 'role');
    }

    /**
     * Return unauthorized response
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function unauthorizedResponse()
    {
        $user = request()->user();
        $context = $this->getUserContext($user);
        $role = $context['role'];

        $message = 'Bạn không có quyền truy cập vào khu vực này.';
        $areaText = 'trang quản lý đặt sân';

        if ($role === 'business') {
            return Inertia::render('Business/Unauthorized', [
                'unauthorized_role' => $areaText,
                'unauthorized_message' => $message
            ]);
        } else {
            return Inertia::render('Branchs/Unauthorized', [
                'unauthorized_role' => $areaText,
                'unauthorized_message' => $message
            ]);
        }
    }

    /**
     * Check if user has access to the branch
     *
     * @param \App\Models\User $user
     * @param int $branchId
     * @return bool
     */
    protected function userHasAccessToBranch($user, $branchId)
    {
        if (!$user || !$branchId) {
            return false;
        }

        $context = $this->getUserContext($user);
        extract($context);

        if ($isAdmin && $role === 'superadmin') {
            return true;
        }

        if ($isAdmin && $role === 'business') {
            return Branch::where('id', $branchId)
                ->where('business_id', $businessId)
                ->exists();
        }

        return $branchId === $user->branch_id;
    }

    /**
     * Get view path based on user role
     *
     * @param string $role
     * @param string $viewName
     * @return string
     */
    protected function getViewPath($role, $viewName)
    {
        switch ($role) {
            case 'superadmin':
                return "superadmin/Booking/{$viewName}";
            case 'business':
                return "Business/Booking/{$viewName}";
            default:
                return "Branchs/Booking/{$viewName}";
        }
    }

    /**
     * Get route name based on user role
     *
     * @param string $role
     * @param string $routeName
     * @return string
     */
    protected function getRouteName($role, $routeName)
    {
        switch ($role) {
            case 'superadmin':
                return "bookings.{$routeName}";
            case 'business':
                return "business.booking.{$routeName}";
            default:
                return "branch.booking.{$routeName}";
        }
    }

    /**
     * Display a listing of bookings.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);


        if ($isAdmin && !$businessId) {
            return $this->superAdminIndex($request);
        } elseif ($businessId && !$branchId) {
            return $this->businessIndex($request, $businessId);
        } else {
            return $this->branchIndex($request, $branchId);
        }
    }

    /**
     * Display bookings for superadmin (all businesses/branches)
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    protected function superAdminIndex(Request $request)
    {

        $businesses = \App\Models\Business::with('branches')->get();

        return Inertia::render('Bookings/SelectBranch', [
            'businesses' => $businesses,
        ]);
    }

    /**
     * Display bookings for business owner (all branches in business)
     *
     * @param Request $request
     * @param int $businessId
     * @return \Inertia\Response
     */
    protected function businessIndex(Request $request, $businessId)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);


        if ($request->has('branch_id')) {
            $branchId = $request->branch_id;
            return $this->branchIndex($request, $branchId);
        }


        $branches = Branch::where('business_id', $businessId)->get();
        $branchIds = $branches->pluck('id')->toArray();

        if (empty($branchIds)) {
            return Inertia::render('Business/Booking/SelectBranch', [
                'branches' => $branches,
                'message' => 'No branches found for this business'
            ]);
        }


        $queryBuilder = Booking::whereIn('branch_id', $branchIds);


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $queryBuilder->where(function ($query) use ($search) {
                $query->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        if ($request->has('status') && !empty($request->status)) {
            $queryBuilder->where('status', $request->status);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $queryBuilder->where('booking_date', '>=', Carbon::parse($request->date_from)->startOfDay());
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $queryBuilder->where('booking_date', '<=', Carbon::parse($request->date_to)->endOfDay());
        }


        if ($request->has('branch_filter') && !empty($request->branch_filter)) {
            $queryBuilder->where('branch_id', $request->branch_filter);
        }


        if ($request->has('court_id') && !empty($request->court_id)) {
            $courtId = $request->court_id;
            $queryBuilder->whereHas('courtBookings', function ($query) use ($courtId) {
                $query->where('court_id', $courtId);
            });
        }


        $sortField = $request->input('sort', 'booking_date');
        $sortDirection = $request->input('direction', 'desc');
        $queryBuilder->orderBy($sortField, $sortDirection);


        $bookings = $queryBuilder->with(['courtBookings.court', 'user', 'customer', 'branch'])->paginate(10);


        $processedBookings = $bookings->map(function ($booking) {

            $courtBookings = $booking->courtBookings;


            $payments = Payment::where('booking_reference', $booking->reference_number)
                ->with('paymentMethod')
                ->get();


            foreach ($payments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $proofUrl = asset('storage/' . $details['proof_file']);
                        $payment->proof_url = $proofUrl;
                        $payment->has_proof = true;
                    }
                }
            }


            $totalPrice = $booking->total_price;
            $paidAmount = $payments->sum('amount');
            $paymentStatus = 'pending';

            if ($paidAmount >= $totalPrice) {
                $paymentStatus = 'completed';
            } elseif ($paidAmount > 0) {
                $paymentStatus = 'partial';
            }


            $courtNames = $courtBookings->pluck('court.name')->unique()->implode(', ');
            $courtIds = $courtBookings->pluck('court_id')->unique()->toArray();


            $startTime = $courtBookings->min('start_time');
            $endTime = $courtBookings->max('end_time');


            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => $booking->booking_date,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'customer_email' => $booking->customer_email,
                'status' => $booking->status,
                'created_at' => $booking->created_at,
                'updated_at' => $booking->updated_at,
                'total_price' => $totalPrice,
                'payment_status' => $paymentStatus,
                'paid_amount' => $paidAmount,
                'customer' => $booking->customer,
                'user' => $booking->user,
                'branch' => $booking->branch,
                'branch_name' => $booking->branch ? $booking->branch->name : 'Unknown',
                'booking_count' => $courtBookings->count(),
                'courts' => $courtNames,
                'court_ids' => $courtIds,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'is_member_price' => $courtBookings->first() ? $courtBookings->first()->is_member_price : false,
                'bookings' => $courtBookings,
                'payments' => $payments,
                'payment_summary' => [
                    'total_paid' => $paidAmount,
                    'payment_count' => $payments->count(),
                    'has_bank_transfer' => $payments->contains('payment_method_id', 2),
                    'has_proof' => $payments->contains('has_proof', true),
                    'payment_methods' => $payments->pluck('payment_method')->unique()->implode(', '),
                    'latest_payment' => $payments->sortByDesc('created_at')->first(),
                ]
            ];
        });


        $processedPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $processedBookings,
            $bookings->total(),
            $bookings->perPage(),
            $bookings->currentPage(),
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $courts = Court::whereIn('branch_id', $branchIds)->get(['id', 'name', 'branch_id']);

        return Inertia::render('Business/Booking/List', [
            'bookings' => $processedPaginator,
            'branches' => $branches,
            'courts' => $courts,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
                'date_from' => $request->date_from ?? '',
                'date_to' => $request->date_to ?? '',
                'court_id' => $request->court_id ?? '',
                'branch_filter' => $request->branch_filter ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'statuses' => [
                'pending' => 'Đang chờ',
                'confirmed' => 'Đã xác nhận',
                'cancelled' => 'Đã hủy',
                'completed' => 'Đã hoàn thành',
            ],
        ]);
    }

    /**
     * Display bookings for branch staff or filtered by branch
     *
     * @param Request $request
     * @param int $branchId
     * @return \Inertia\Response
     */
    protected function branchIndex(Request $request, $branchId)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with('business')->findOrFail($branchId);
        $businessId = $branch->business_id;


        $queryBuilder = Booking::where('branch_id', $branchId);


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $queryBuilder->where(function ($query) use ($search) {
                $query->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        if ($request->has('status') && !empty($request->status)) {
            $queryBuilder->where('status', $request->status);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $queryBuilder->where('booking_date', '>=', Carbon::parse($request->date_from)->startOfDay());
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $queryBuilder->where('booking_date', '<=', Carbon::parse($request->date_to)->endOfDay());
        }


        if ($request->has('court_id') && !empty($request->court_id)) {
            $courtId = $request->court_id;
            $queryBuilder->whereHas('courtBookings', function ($query) use ($courtId) {
                $query->where('court_id', $courtId);
            });
        }


        $sortField = $request->input('sort', 'booking_date');
        $sortDirection = $request->input('direction', 'desc');
        $queryBuilder->orderBy($sortField, $sortDirection);


        $bookings = $queryBuilder->with(['courtBookings.court', 'user', 'customer'])->paginate(10);


        $processedBookings = $bookings->map(function ($booking) {

            $courtBookings = $booking->courtBookings;


            $payments = Payment::where('booking_reference', $booking->reference_number)
                ->with('paymentMethod')
                ->get();


            foreach ($payments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $proofUrl = asset('storage/' . $details['proof_file']);
                        $payment->proof_url = $proofUrl;
                        $payment->has_proof = true;
                    }
                }
            }


            $totalPrice = $booking->total_price;
            $paidAmount = $payments->sum('amount');
            $paymentStatus = 'pending';

            if ($paidAmount >= $totalPrice) {
                $paymentStatus = 'completed';
            } elseif ($paidAmount > 0) {
                $paymentStatus = 'partial';
            }


            $courtNames = $courtBookings->pluck('court.name')->unique()->implode(', ');
            $courtIds = $courtBookings->pluck('court_id')->unique()->toArray();


            $startTime = $courtBookings->min('start_time');
            $endTime = $courtBookings->max('end_time');


            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => $booking->booking_date,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'customer_email' => $booking->customer_email,
                'status' => $booking->status,
                'created_at' => $booking->created_at,
                'updated_at' => $booking->updated_at,
                'total_price' => $totalPrice,
                'payment_status' => $paymentStatus,
                'paid_amount' => $paidAmount,
                'customer' => $booking->customer,
                'user' => $booking->user,
                'booking_count' => $courtBookings->count(),
                'courts' => $courtNames,
                'court_ids' => $courtIds,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'is_member_price' => $courtBookings->first() ? $courtBookings->first()->is_member_price : false,
                'bookings' => $courtBookings,
                'payments' => $payments,
                'payment_summary' => [
                    'total_paid' => $paidAmount,
                    'payment_count' => $payments->count(),
                    'has_bank_transfer' => $payments->contains('payment_method_id', 2),
                    'has_proof' => $payments->contains('has_proof', true),
                    'payment_methods' => $payments->pluck('payment_method')->unique()->implode(', '),
                    'latest_payment' => $payments->sortByDesc('created_at')->first(),
                ]
            ];
        });


        $processedPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $processedBookings,
            $bookings->total(),
            $bookings->perPage(),
            $bookings->currentPage(),
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $courts = Court::where('branch_id', $branchId)->get(['id', 'name']);


        $view = $this->getViewPath($context['role'], 'List');

        return Inertia::render($view, [
            'bookings' => $processedPaginator,
            'branch' => $branch,
            'courts' => $courts,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
                'date_from' => $request->date_from ?? '',
                'date_to' => $request->date_to ?? '',
                'court_id' => $request->court_id ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'statuses' => [
                'pending' => 'Đang chờ',
                'confirmed' => 'Đã xác nhận',
                'cancelled' => 'Đã hủy',
                'completed' => 'Đã hoàn thành',
            ],
        ]);
    }

    /**
     * Show the form for creating a new booking.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function create(Request $request)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);
        $services = CourtService::where('business_id', $branch->business_id)
            ->where('is_active', true)
            ->get();


        $view = $this->getViewPath($context['role'], 'Create');

        return Inertia::render($view, [
            'branch' => $branch,
            'courts' => $branch->courts,
            'services' => $services,
        ]);
    }

    /**
     * Store a newly created booking in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'services' => 'nullable|array',
            'services.*.id' => 'exists:court_services,id',
            'services.*.quantity' => 'integer|min:1',
            'services.*.price' => 'numeric|min:0',
            'booking_courts' => 'required|array',
            'booking_courts.*.court_id' => 'required|exists:courts,id',
            'booking_courts.*.booking_slot' => 'required|array',
            'booking_courts.*.start_time' => 'required|date_format:H:i',
            'booking_courts.*.end_time' => 'required|date_format:H:i|after:booking_courts.*.start_time',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            return DB::transaction(function () use ($request, $branchId, $user, $context) {
                $data = $request->all();
                $createdBookings = [];

                $result = CourtBooking::processBookings(
                    $data,
                    $user,
                    'offline',
                    $createdBookings
                );

                if (!$result['success']) {
                    return redirect()->back()
                        ->with('flash.error', $result['message'])
                        ->withInput();
                }


                $firstBooking = null;
                if (!empty($createdBookings)) {
                    $firstBooking = CourtBooking::find($createdBookings[0]['id']);


                    \App\Services\NotificationService::createBookingNotification(
                        $branchId,
                        $result['reference_number'],
                        $data['customer_name'],
                        $data['customer_phone'],
                        $result['bookings']
                    );
                }


                if (!empty($data['customer_email'])) {
                    try {
                        $this->sendBookingConfirmationEmail(
                            $result['reference_number'],
                            $data['customer_email'],
                            $data,
                            $firstBooking
                        );

                        Log::info('Booking confirmation email sent to: ' . $data['customer_email'], [
                            'reference_number' => $result['reference_number']
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                            'exception' => $e,
                            'email' => $data['customer_email'],
                            'reference_number' => $result['reference_number']
                        ]);
                    }
                }


                $redirectRoute = $this->getRouteName($context['role'], 'index');

                return redirect()->route($redirectRoute)
                    ->with('flash.success', 'Đặt sân thành công với mã tham chiếu: ' . $result['reference_number']);
            });
        } catch (\Exception $e) {
            Log::error('Booking error: ' . $e->getMessage(), ['exception' => $e]);

            return redirect()->back()
                ->with('flash.error', 'Đã xảy ra lỗi khi đặt sân: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Send booking confirmation email
     * 
     * @param string $referenceNumber
     * @param string $email
     * @param array $bookingData
     * @param CourtBooking $booking
     * @return bool
     */
    protected function sendBookingConfirmationEmail($referenceNumber, $email, $bookingData, $booking)
    {
        if (empty($email)) {
            return false;
        }


        $mainBooking = Booking::where('reference_number', $referenceNumber)->first();
        if (!$mainBooking) {

            $mainBooking = new Booking();
            $mainBooking->reference_number = $referenceNumber;
            $mainBooking->total_price = $bookingData['total_price'] ?? 0;
        }


        $branch = $booking->court->branch;
        $business = $branch ? $branch->business : null;


        if (!$business) {
            $business = new \App\Models\Business();
            $business->name = 'Pickleball';
        }

        return MailService::sendUsingBranchConfig(
            $booking->court->branch_id,
            $email,
            new BookingConfirmation(
                $mainBooking,
                $bookingData['total_price'] ?? 0,
                $bookingData['bookings'] ?? [],
                $business
            )
        );
    }

    /**
     * Display the specified booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Inertia\Response|\Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $reference_number)
    {
        try {
            $user = $request->user();

            if (!$user->can('booking:view_booking')) {
                return $this->unauthorizedResponse();
            }

            $booking = Booking::where('reference_number', $reference_number)->first();

            if (!$booking) {
                abort(404, 'Không tìm thấy đơn đặt sân');
            }

            if ($user->hasRole('super-admin')) {
            } elseif ($user->hasRole('admin')) {
                $businessId = $user->business_id;
                $branch = Branch::find($booking->branch_id);

                if (!$branch || $branch->business_id != $businessId) {
                    return $this->unauthorizedResponse();
                }
            } elseif ($user->hasRole('manager')) {
                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            } else {

                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            }

            $context = $this->getUserContext($user);
            extract($context);


            $courtBookings = $booking->courtBookings()->with(['court', 'user'])->get();

            if ($courtBookings->isEmpty()) {
                abort(404, 'Không tìm thấy chi tiết đặt sân');
            }

            $branch = Branch::findOrFail($booking->branch_id);

            $payments = Payment::where('booking_reference', $reference_number)
                ->with(['paymentMethod', 'user'])
                ->get();

            foreach ($payments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            $totalPrice = $booking->total_price;
            $paymentSummary = [
                'total_paid' => $payments->sum('amount'),
                'payment_count' => $payments->count(),
                'has_bank_transfer' => $payments->contains('payment_method_id', 2),
                'has_proof' => $payments->contains('has_proof', true),
                'payment_methods' => $payments->pluck('payment_method')->unique()->implode(', '),
                'latest_payment' => $payments->sortByDesc('created_at')->first(),
                'payment_status' => $payments->sum('amount') >= $totalPrice ? 'completed' :
                    ($payments->sum('amount') > 0 ? 'partial' : 'pending'),
            ];

            $booking->payments = $payments;
            $booking->payment_summary = $paymentSummary;
            $booking->total_group_price = $totalPrice;


            $booking->is_member_price = !empty($booking->customer_id) || $courtBookings->first()?->is_member_price;


            if (method_exists($booking, 'getTimeline')) {
                $booking->timeline = $booking->getTimeline();
            }


            $view = $this->getViewPath($context['role'], 'Show');

            return Inertia::render($view, [
                'booking' => $booking,
                'bookings' => $courtBookings,
                'branch' => $branch,
                'payments' => $payments,
            ]);

        } catch (\Exception $e) {
            Log::error('Error in BookingController@show: ' . $e->getMessage(), [
                'reference_number' => $reference_number,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Đã xảy ra lỗi khi hiển thị chi tiết đặt sân.',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Inertia\Response
     */
    public function edit(Request $request, $reference_number)
    {
        try {
            $user = $request->user();

            if (!$user->can('booking:update_booking')) {
                return $this->unauthorizedResponse();
            }

            $booking = Booking::where('reference_number', $reference_number)->first();

            if (!$booking) {
                abort(404, 'Không tìm thấy đơn đặt sân');
            }


            if ($user->hasRole('super-admin')) {

            } elseif ($user->hasRole('admin')) {
                $businessId = $user->business_id;
                $branch = Branch::find($booking->branch_id);

                if (!$branch || $branch->business_id != $businessId) {
                    return $this->unauthorizedResponse();
                }
            } elseif ($user->hasRole('manager')) {
                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            } else {

                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            }

            $context = $this->getUserContext($user);
            extract($context);
            $branchId = $booking->branch_id;


            $courtBookings = $booking->courtBookings()->with(['court', 'user'])->get();

            if ($courtBookings->isEmpty()) {
                abort(404, 'Không tìm thấy chi tiết đặt sân');
            }

            $payments = Payment::where('booking_reference', $reference_number)
                ->with(['paymentMethod', 'user'])
                ->get();

            $totalPrice = $booking->total_price;
            $paymentSummary = [
                'total_paid' => $payments->sum('amount'),
                'payment_count' => $payments->count(),
                'payment_status' => $payments->sum('amount') >= $totalPrice ? 'completed' :
                    ($payments->sum('amount') > 0 ? 'partial' : 'pending'),
            ];

            $booking->payments = $payments;
            $booking->payment_summary = $paymentSummary;
            $booking->total_group_price = $totalPrice;

            $booking->is_member_price = !empty($booking->customer_id) || $courtBookings->first()?->is_member_price;

            $formattedBookings = $courtBookings->map(function ($courtBooking) {
                return [
                    'id' => $courtBooking->id,
                    'court_id' => $courtBooking->court_id,
                    'court_name' => $courtBooking->court->name,
                    'court_type' => $courtBooking->court->type,
                    'booking_date' => $courtBooking->booking_date->format('Y-m-d'),
                    'start_time' => $courtBooking->start_time->format('H:i'),
                    'end_time' => $courtBooking->end_time->format('H:i'),
                    'total_price' => $courtBooking->total_price,
                    'status' => $courtBooking->status,
                    'number_of_players' => $courtBooking->number_of_players,
                    'notes' => $courtBooking->notes,
                    'reference_number' => $courtBooking->reference_number,
                    'metadata' => $courtBooking->metadata,

                    'is_member_price' => !empty($courtBooking->customer_id) || $courtBooking->is_member_price,
                ];
            });

            $booking->formatted_bookings = $formattedBookings;


            $formattedBookingsCollection = collect($formattedBookings);
            $groupedBookings = $formattedBookingsCollection->groupBy('booking_date')
                ->map(function ($dateGroup) {

                    $dateGroupCollection = collect($dateGroup);
                    return $dateGroupCollection->groupBy('court_id');
                });

            $booking->grouped_bookings = $groupedBookings;

            $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);


            $view = $this->getViewPath($context['role'], 'Edit');

            return Inertia::render($view, [
                'booking' => $booking,
                'courtBookings' => $courtBookings,
                'formatted_bookings' => $formattedBookings,
                'grouped_bookings' => $groupedBookings,
                'branch' => $branch,
                'courts' => $branch->courts,
                'payments' => $payments,
            ]);

        } catch (\Exception $e) {
            Log::error('Error in BookingController@edit: ' . $e->getMessage(), [
                'reference_number' => $reference_number,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Đã xảy ra lỗi khi chỉnh sửa đặt sân: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update the specified booking in storage.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $reference_number)
    {
        try {
            $user = $request->user();

            if (!$user->can('booking:update_booking')) {
                return $this->unauthorizedResponse();
            }

            $booking = Booking::where('reference_number', $reference_number)->first();

            if (!$booking) {
                return redirect()->back()->withErrors([
                    'error' => 'Booking not found.'
                ]);
            }


            if ($user->hasRole('super-admin')) {

            } elseif ($user->hasRole('admin')) {
                $businessId = $user->business_id;
                $branch = Branch::find($booking->branch_id);

                if (!$branch || $branch->business_id != $businessId) {
                    return $this->unauthorizedResponse();
                }
            } elseif ($user->hasRole('manager')) {
                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            } else {

                if ($booking->branch_id != $user->branch_id) {
                    return $this->unauthorizedResponse();
                }
            }

            $context = $this->getUserContext($user);
            extract($context);
            $branchId = $booking->branch_id;



            $validator = Validator::make($request->all(), [
                'customer_name' => 'required|string|max:255',
                'customer_phone' => 'required|string|max:20',
                'customer_email' => 'nullable|email|max:255',
                'notes' => 'nullable|string',
                'status' => 'required|in:pending,confirmed,cancelled,completed',

            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            try {
                DB::beginTransaction();


                $booking->customer_name = $request->customer_name;
                $booking->customer_phone = $request->customer_phone;
                $booking->customer_email = $request->customer_email;
                $booking->notes = $request->notes;
                $booking->status = $request->status;


                $metadata = $booking->metadata ?? [];
                $metadata['last_updated_by'] = $context['role'];
                $metadata['staff_id'] = $user->id;
                $metadata['staff_name'] = $user->name;
                $metadata['updated_at'] = now()->toDateTimeString();
                $booking->metadata = $metadata;

                $booking->save();


                $existingCourtBookings = $booking->courtBookings;
                $existingBookingIds = $existingCourtBookings->pluck('id')->toArray();
                $processedBookingIds = [];

                foreach ($request->booking_courts as $courtBookingData) {
                    if (isset($courtBookingData['is_modified']) && $courtBookingData['is_modified'] === false && !empty($courtBookingData['id'])) {
                        $processedBookingIds[] = $courtBookingData['id'];
                        continue;
                    }

                    if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified'] === true) {
                        $conflictingBookings = CourtBooking::where('court_id', $courtBookingData['court_id'])
                            ->where('booking_date', $courtBookingData['booking_date'])
                            ->where(function ($query) use ($courtBookingData) {
                                $query->where(function ($q) use ($courtBookingData) {
                                    $q->where('start_time', '<', $courtBookingData['end_time'])
                                        ->where('end_time', '>', $courtBookingData['start_time']);
                                });
                            });

                        if (!empty($courtBookingData['id'])) {
                            $conflictingBookings->where('id', '!=', $courtBookingData['id']);
                        }

                        $conflictingBookings = $conflictingBookings->whereIn('status', ['pending', 'confirmed'])->count();

                        if ($conflictingBookings > 0) {
                            DB::rollBack();
                            return redirect()->back()->withErrors([
                                'time_conflict' => 'The selected time slot for ' . $courtBookingData['court_name'] . ' is no longer available. Please choose a different time.'
                            ])->withInput();
                        }
                    }

                    $courtBookingModel = null;
                    if (!empty($courtBookingData['id'])) {
                        $courtBookingModel = CourtBooking::find($courtBookingData['id']);
                        if (!$courtBookingModel || $courtBookingModel->reference_number !== $reference_number) {
                            DB::rollBack();
                            return redirect()->back()->withErrors([
                                'error' => 'Invalid booking ID provided.'
                            ])->withInput();
                        }
                        $processedBookingIds[] = $courtBookingModel->id;
                    } else {
                        $courtBookingModel = new CourtBooking();
                        $courtBookingModel->booking_id = $booking->id;
                        $courtBookingModel->reference_number = $reference_number;
                        $courtBookingModel->branch_id = $branchId;
                        $courtBookingModel->user_id = $booking->user_id;
                        $courtBookingModel->customer_id = $booking->customer_id;
                        $courtBookingModel->booking_type = $booking->booking_type;
                        $courtBookingModel->status = $request->status;
                    }

                    $courtBookingModel->customer_name = $request->customer_name;
                    $courtBookingModel->customer_phone = $request->customer_phone;
                    $courtBookingModel->customer_email = $request->customer_email;
                    $courtBookingModel->notes = $request->notes;
                    $courtBookingModel->number_of_players = $request->number_of_players ?? $booking->number_of_players;
                    $courtBookingModel->status = $request->status;

                    if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified']) {
                        $courtBookingModel->court_id = $courtBookingData['court_id'];
                        $courtBookingModel->booking_date = $courtBookingData['booking_date'];
                        $courtBookingModel->start_time = $courtBookingData['start_time'];
                        $courtBookingModel->end_time = $courtBookingData['end_time'];
                    }

                    $useCurrentPrices = $request->use_current_prices ?? false;
                    $isMemberPrice = $request->is_member_price ?? $courtBookingModel->is_member_price;


                    if (!empty($booking->customer_id)) {
                        $isMemberPrice = true;
                    }

                    $courtBookingModel->is_member_price = $isMemberPrice;

                    if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified'] || $useCurrentPrices) {
                        if ($useCurrentPrices) {
                            $court = Court::findOrFail($courtBookingData['court_id']);
                            $courtType = $court->type;
                            $totalPrice = 0;

                            foreach ($courtBookingData['time_slots'] as $slot) {
                                $slotTime = Carbon::parse($slot);
                                $slotHour = $slotTime->hour;

                                $price = CourtPrice::where('branch_id', $branchId)
                                    ->where('court_type', $courtType)
                                    ->where(function ($query) use ($slotHour) {
                                        $query->whereRaw("CAST(SUBSTRING_INDEX(start_time, ':', 1) AS UNSIGNED) <= ?", [$slotHour])
                                            ->whereRaw("CAST(SUBSTRING_INDEX(end_time, ':', 1) AS UNSIGNED) > ?", [$slotHour]);
                                    })
                                    ->first();

                                if ($price) {
                                    $hourlyRate = $isMemberPrice && $price->member_price_per_hour
                                        ? $price->member_price_per_hour
                                        : $price->price_per_hour;

                                    $slotPrice = $hourlyRate * (30 / 60);
                                    $totalPrice += $slotPrice;
                                } else {
                                    $hourlyRate = $court->price_per_hour;
                                    if ($isMemberPrice) {
                                        $hourlyRate *= 0.85;
                                    }
                                    $slotPrice = $hourlyRate * (30 / 60);
                                    $totalPrice += $slotPrice;
                                }
                            }

                            $courtBookingModel->total_price = $totalPrice;
                        } else if (isset($courtBookingData['price_details']) && isset($courtBookingData['price_details']['price_per_hour'])) {
                            $hourlyRate = $courtBookingData['price_details']['price_per_hour'];
                            $totalSlots = count($courtBookingData['time_slots']);
                            $totalHours = $totalSlots * (30 / 60);
                            $courtBookingModel->total_price = $hourlyRate * $totalHours;
                        } else {
                            $courtBookingModel->total_price = $courtBookingData['total_price'];
                        }
                    }

                    $metadata = $courtBookingModel->metadata ?? [];

                    if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified']) {
                        $metadata['last_updated_by'] = $context['role'];
                        $metadata['staff_id'] = $user->id;
                        $metadata['staff_name'] = $user->name;
                        $metadata['updated_at'] = now()->toDateTimeString();
                        $metadata['court_name'] = $courtBookingData['court_name'];
                        $metadata['court_price'] = $courtBookingModel->total_price;
                        $metadata['time_slots'] = $courtBookingData['time_slots'];

                        if (isset($courtBookingData['price_details'])) {
                            $metadata['price_details'] = $courtBookingData['price_details'];
                        }
                    }

                    $courtBookingModel->metadata = $metadata;
                    $courtBookingModel->save();
                }


                $bookingsToDelete = array_diff($existingBookingIds, $processedBookingIds);
                if (!empty($bookingsToDelete)) {
                    CourtBooking::whereIn('id', $bookingsToDelete)->delete();
                }


                $updatedCourtBookings = $booking->courtBookings()->get();
                $booking->total_price = $updatedCourtBookings->sum('total_price');
                $booking->save();


                Booking::syncBookingDate($reference_number);

                DB::commit();


                $redirectRoute = $this->getRouteName($context['role'], 'show');

                return redirect()->route($redirectRoute, $reference_number)
                    ->with('flash.success', __('booking.update_success'));

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Failed to update booking: ' . $e->getMessage(), [
                    'exception' => $e,
                    'request' => $request->all()
                ]);

                return redirect()->back()->withErrors([
                    'error' => 'Failed to update booking. ' . $e->getMessage()
                ])->withInput();
            }
        } catch (\Exception $e) {
            Log::error('Error in BookingController@update: ' . $e->getMessage(), [
                'reference_number' => $reference_number,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Đã xảy ra lỗi khi cập nhật đặt sân: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel the specified booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $reference_number)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }


        $booking = Booking::where('reference_number', $reference_number)
            ->where('branch_id', $branchId)
            ->first();

        if (!$booking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($booking->status, ['completed', 'cancelled'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot cancel a booking that is already completed or cancelled.'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();


            $booking->status = 'cancelled';


            $metadata = $booking->metadata ?? [];
            $metadata['cancelled_by'] = $role;
            $metadata['staff_id'] = $user->id;
            $metadata['staff_name'] = $user->name;
            $metadata['cancelled_at'] = now()->toDateTimeString();

            if ($request->filled('cancellation_reason')) {
                $metadata['cancellation_reason'] = $request->cancellation_reason;
            }

            $booking->metadata = $metadata;
            $booking->save();


            foreach ($booking->courtBookings as $courtBooking) {
                $courtBooking->status = 'cancelled';
                $courtMeta = $courtBooking->metadata ?? [];
                $courtMeta['cancelled_by'] = $role;
                $courtMeta['staff_id'] = $user->id;
                $courtMeta['staff_name'] = $user->name;
                $courtMeta['cancelled_at'] = now()->toDateTimeString();

                if ($request->filled('cancellation_reason')) {
                    $courtMeta['cancellation_reason'] = $request->cancellation_reason;
                }

                $courtBooking->metadata = $courtMeta;
                $courtBooking->save();
            }

            DB::commit();


            $redirectRoute = $this->getRouteName($role, 'show');

            return redirect()->route($redirectRoute, $reference_number)
                ->with('flash.success', 'Booking cancelled successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel booking: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to cancel booking. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the court booking schedule.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function schedule(Request $request)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with([
            'prices' => function ($query) {
                $query->where('is_active', true)
                    ->where('status', 'active')
                    ->orderBy('court_type')
                    ->orderBy('start_time');
            }
        ])->findOrFail($branchId);

        $courts = Court::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->orderBy('name')
            ->get(['id', 'name', 'type', 'description']);

        $courtTypes = $courts->pluck('type')->unique()->values()->map(function ($type) use ($branch) {
            $basePrice = $branch->prices
                ->where('court_type', $type)
                ->where('price_type', 'normal')
                ->first();

            return [
                'type' => $type,
                'base_price' => $basePrice ? $basePrice->price_per_hour : null,
                'formatted_price' => $basePrice ? number_format($basePrice->price_per_hour, 0, ',', '.') . '₫' : null,
            ];
        });


        $view = $this->getViewPath($role, 'Schedule');

        return Inertia::render($view, [
            'branch' => [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->address,
                'contact_phone' => $branch->contact_phone,
                'contact_email' => $branch->contact_email,
                'opening_hour' => $branch->opening_hour ?? '06:00',
                'closing_hour' => $branch->closing_hour ?? '22:00',
                'logo' => $branch->logo,
                'banner' => $branch->banner,
                'main_image_url' => $branch->main_image_url,
            ],
            'courts' => $courts,
            'courtTypes' => $courtTypes,
            'today' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    /**
     * API endpoint to get courts and bookings for the schedule view
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getScheduleData(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'date' => 'required|date',
                'court_id' => 'nullable|string',
                'duration' => 'nullable|integer|min:15|max:120',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = $request->user();
            $context = $this->getUserContext($user);
            extract($context);

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to access this branch',
                ], 403);
            }

            if (!$this->userHasAccessToBranch($user, $branchId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to access this branch',
                ], 403);
            }

            $date = $request->date;
            $courtId = $request->court_id;
            $duration = (int) $request->input('duration', 30);
            $useMemberPrice = $request->boolean('use_member_price', false);

            $branch = Branch::with('prices')->findOrFail($branchId);

            $courts = Court::getCourtsForSchedule($branchId, $date, $duration, $useMemberPrice);

            if ($courtId && $courtId !== 'all') {
                $courts = $courts->filter(function ($court) use ($courtId) {
                    return $court['id'] == $courtId;
                })->values();
            }

            $bookings = CourtBooking::getBookingsForSchedule($date, $branchId, $courtId);

            $pricingInfo = CourtPrice::where('branch_id', $branchId)
                ->where(function ($query) use ($date) {
                    $query->where('price_type', 'normal')
                        ->orWhere(function ($q) use ($date) {
                            $q->where('price_type', 'special_date')
                                ->where('special_date', $date);
                        });
                })
                ->where('is_active', true)
                ->get()
                ->map(function ($price) use ($useMemberPrice) {
                    return [
                        'id' => $price->id,
                        'court_type' => $price->court_type,
                        'price_type' => $price->price_type,
                        'start_time' => Carbon::parse($price->start_time)->format('H:i'),
                        'end_time' => Carbon::parse($price->end_time)->format('H:i'),
                        'price_per_hour' => $useMemberPrice ? (float) $price->member_price_per_hour : (float) $price->price_per_hour,
                        'member_price_per_hour' => (float) $price->member_price_per_hour,
                        'is_member_price' => $useMemberPrice,
                        'special_date' => $price->special_date,
                        'description' => $price->description ?? '',
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'branch' => [
                        'id' => $branch->id,
                        'name' => $branch->name,
                        'opening_hour' => $branch->opening_hour ?? '06:00',
                        'closing_hour' => $branch->closing_hour ?? '22:00',
                    ],
                    'courts' => $courts,
                    'bookings' => $bookings,
                    'date' => $date,
                    'pricing' => $pricingInfo,
                    'duration' => $duration,
                    'use_member_price' => $useMemberPrice,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching schedule data: ' . $e->getMessage(), ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching schedule data',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Remove the specified booking from storage.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, $reference_number)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }


        $booking = Booking::where('reference_number', $reference_number)
            ->where('branch_id', $branchId)
            ->first();

        if (!$booking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($booking->status, ['completed'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot delete a booking that is already completed.'
            ]);
        }

        try {
            DB::beginTransaction();


            $metadata = $booking->metadata ?? [];
            $metadata['deleted_by'] = $role;
            $metadata['staff_id'] = $user->id;
            $metadata['staff_name'] = $user->name;
            $metadata['deleted_at'] = now()->toDateTimeString();
            $booking->metadata = $metadata;
            $booking->save();


            $booking->delete();

            DB::commit();


            $redirectRoute = $this->getRouteName($role, 'index');

            return redirect()->route($redirectRoute)
                ->with('flash.success', 'Booking deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete booking: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to delete booking. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Mark the specified booking as completed.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function complete(Request $request, $reference_number)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }


        $booking = Booking::where('reference_number', $reference_number)
            ->where('branch_id', $branchId)
            ->first();

        if (!$booking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($booking->status, ['completed', 'cancelled'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot complete a booking that is already completed or cancelled.'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'completion_notes' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();


            $booking->status = 'completed';


            $metadata = $booking->metadata ?? [];
            $metadata['completed_by'] = $role;
            $metadata['staff_id'] = $user->id;
            $metadata['staff_name'] = $user->name;
            $metadata['completed_at'] = now()->toDateTimeString();

            if ($request->filled('completion_notes')) {
                $metadata['completion_notes'] = $request->completion_notes;
            }

            $booking->metadata = $metadata;
            $booking->save();


            foreach ($booking->courtBookings as $courtBooking) {
                $courtBooking->status = 'completed';
                $courtMeta = $courtBooking->metadata ?? [];
                $courtMeta['completed_by'] = $role;
                $courtMeta['staff_id'] = $user->id;
                $courtMeta['staff_name'] = $user->name;
                $courtMeta['completed_at'] = now()->toDateTimeString();

                if ($request->filled('completion_notes')) {
                    $courtMeta['completion_notes'] = $request->completion_notes;
                }

                $courtBooking->metadata = $courtMeta;
                $courtBooking->save();
            }

            DB::commit();


            $redirectRoute = $this->getRouteName($role, 'show');

            return redirect()->route($redirectRoute, $reference_number)
                ->with('flash.success', 'Booking marked as completed successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete booking: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to complete booking. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Confirm payment for the specified booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function confirmPayment(Request $request, $reference_number)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }


        $booking = Booking::where('reference_number', $reference_number)
            ->where('branch_id', $branchId)
            ->first();

        if (!$booking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($booking->status, ['cancelled'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot confirm payment for a cancelled booking.'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string|in:cash,card,bank_transfer,other',
            'payment_amount' => 'required|numeric|min:0',
            'payment_reference' => 'nullable|string|max:255',
            'payment_notes' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();


            $metadata = $booking->metadata ?? [];


            if (!isset($metadata['payments'])) {
                $metadata['payments'] = [];
            }


            $payment = [
                'payment_id' => Str::uuid()->toString(),
                'payment_method' => $request->payment_method,
                'payment_amount' => $request->payment_amount,
                'payment_date' => now()->toDateTimeString(),
                'payment_reference' => $request->payment_reference,
                'payment_notes' => $request->payment_notes,
                'recorded_by' => $role,
                'staff_id' => $user->id,
                'staff_name' => $user->name,
            ];

            $metadata['payments'][] = $payment;


            $totalPaid = array_sum(array_column($metadata['payments'], 'payment_amount'));
            $metadata['total_paid'] = $totalPaid;


            if ($totalPaid >= $booking->total_price) {
                $metadata['payment_status'] = 'paid';
            } elseif ($totalPaid > 0) {
                $metadata['payment_status'] = 'partial';
            } else {
                $metadata['payment_status'] = 'unpaid';
            }

            $booking->metadata = $metadata;


            if ($booking->status === 'pending') {
                $booking->status = 'confirmed';


                foreach ($booking->courtBookings as $courtBooking) {
                    if ($courtBooking->status === 'pending') {
                        $courtBooking->status = 'confirmed';
                        $courtBooking->save();
                    }
                }
            }

            $booking->save();

            DB::commit();


            $redirectRoute = $this->getRouteName($role, 'show');

            return redirect()->route($redirectRoute, $reference_number)
                ->with('flash.success', 'Payment confirmed successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to confirm payment: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to confirm payment. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send confirmation email for the booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendConfirmationEmail(Request $request, $reference_number)
    {
        $user = $request->user();
        $context = $this->getUserContext($user);
        extract($context);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        if (!$this->userHasAccessToBranch($user, $branchId)) {
            return $this->unauthorizedResponse();
        }


        $booking = Booking::where('reference_number', $reference_number)
            ->where('branch_id', $branchId)
            ->first();

        if (!$booking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($booking->status, ['cancelled'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot send confirmation email for a cancelled booking.'
            ]);
        }

        if (empty($booking->customer_email)) {
            return redirect()->back()->withErrors([
                'error' => 'Customer email address is not available.'
            ]);
        }

        try {

            $branch = Branch::find($branchId);
            $business = $branch->business;


            $courtBookings = $booking->courtBookings;


            $mailService = app(MailService::class);
            $mailService->sendBookingConfirmationEmail($booking, $courtBookings, $branch, $business);


            $metadata = $booking->metadata ?? [];

            if (!isset($metadata['emails_sent'])) {
                $metadata['emails_sent'] = [];
            }

            $metadata['emails_sent'][] = [
                'type' => 'confirmation',
                'sent_at' => now()->toDateTimeString(),
                'sent_by' => $role,
                'staff_id' => $user->id,
                'staff_name' => $user->name,
                'email_address' => $booking->customer_email,
            ];

            $booking->metadata = $metadata;
            $booking->save();


            $redirectRoute = $this->getRouteName($role, 'show');

            return redirect()->route($redirectRoute, $reference_number)
                ->with('flash.success', 'Booking confirmation email sent successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to send confirmation email. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update the status of a booking.
     *
     * @param  string  $referenceNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(string $referenceNumber)
    {
        try {
            $booking = Booking::where('reference_number', $referenceNumber)->firstOrFail();


            $validated = request()->validate([
                'status' => 'required|string|in:pending,confirmed,completed,cancelled',
            ]);


            $booking->status = $validated['status'];
            $booking->save();


            if (function_exists('activity')) {
                activity()
                    ->performedOn($booking)
                    ->withProperties(['status' => $validated['status']])
                    ->log('booking_status_updated');
            }

            return response()->json([
                'success' => true,
                'message' => 'Booking status updated successfully',
                'booking' => $booking
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a payment to a booking.
     *
     * @param  string  $referenceNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function addPayment(string $referenceNumber)
    {
        try {
            $booking = Booking::where('reference_number', $referenceNumber)->firstOrFail();


            $validated = request()->validate([
                'payment_method_id' => 'required|exists:payment_methods,id',
                'amount' => 'required|numeric|min:0',
                'payment_reference' => 'nullable|string|max:255',
                'payment_notes' => 'nullable|string|max:1000',
            ]);


            $payment = new \App\Models\Payment();
            $payment->booking_id = $booking->id;
            $payment->payment_method_id = $validated['payment_method_id'];
            $payment->amount = $validated['amount'];
            $payment->payment_reference = $validated['payment_reference'] ?? null;
            $payment->payment_notes = $validated['payment_notes'] ?? null;
            $payment->status = 'completed';
            $payment->save();


            $totalPaid = $booking->payments()->sum('amount') + $validated['amount'];


            if ($totalPaid >= $booking->total_price) {
                $paymentStatus = 'completed';
            } elseif ($totalPaid > 0) {
                $paymentStatus = 'partial';
            } else {
                $paymentStatus = 'pending';
            }

            $booking->payment_status = $paymentStatus;
            $booking->save();


            if (function_exists('activity')) {
                activity()
                    ->performedOn($booking)
                    ->withProperties([
                        'payment_id' => $payment->id,
                        'amount' => $validated['amount'],
                        'payment_method_id' => $validated['payment_method_id']
                    ])
                    ->log('booking_payment_added');
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment added successfully',
                'payment' => $payment,
                'payment_summary' => [
                    'total_paid' => $totalPaid,
                    'payment_count' => $booking->payments()->count(),
                    'payment_status' => $paymentStatus
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add payment: ' . $e->getMessage()
            ], 500);
        }
    }
}
