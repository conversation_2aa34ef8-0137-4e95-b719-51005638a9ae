<?php

namespace App\Http\Controllers;

use App\Models\CourtBooking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use App\Models\Booking;
use Illuminate\Support\Facades\Auth;
use App\Models\Branch;
use App\Services\ExportService;

class BookingHistoryController extends Controller
{
    /**
     * Display the booking history page
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $businessId = null;
        $branchId = null;
        $isAdmin = false;


        if ($user->hasRole('super-admin')) {
            $isAdmin = true;

        } elseif ($user->hasRole('admin')) {
            $isAdmin = true;
            $businessId = $user->business_id;
        } elseif ($user->hasRole('manager')) {
            $businessId = $user->business_id;
            $branchId = $user->branch_id;
        } else {

            $businessId = $user->business_id;
            $branchId = $user->branch_id;
        }


        if ($isAdmin && !$businessId) {
            return $this->superAdminIndex($request);
        } else {
            return $this->businessIndex($request, $businessId, $branchId);
        }
    }

    /**
     * SuperAdmin view - displays all bookings
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    private function superAdminIndex(Request $request)
    {

        $branchIds = Branch::pluck('id')->toArray();

        $query = Booking::query()
            ->with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->select('bookings.*')
            ->whereIn('branch_id', $branchIds);


        if ($request->has('from_date') && $request->has('to_date')) {
            $from = Carbon::parse($request->input('from_date'))->startOfDay();
            $to = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('booking_date', [$from, $to]);
        } else {
            $to = null;
            $from = null;
        }


        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }


        if ($request->has('branch_id') && $request->input('branch_id') !== 'all') {
            $query->where('branch_id', $request->input('branch_id'));
        }


        if ($request->has('search') && $request->input('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_phone', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_email', 'like', "%{$searchTerm}%")
                    ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('customer', function ($customerQuery) use ($searchTerm) {
                        $customerQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%")
                            ->orWhere('phone', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('courtBookings.court', function ($courtQuery) use ($searchTerm) {
                        $courtQuery->where('name', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('branch', function ($branchQuery) use ($searchTerm) {
                        $branchQuery->where('name', 'like', "%{$searchTerm}%");
                    });
            });
        }


        $referenceNumbers = clone $query;
        $referenceNumbers = $referenceNumbers->distinct('reference_number')
            ->pluck('reference_number');


        $mainQuery = Booking::with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->whereIn('reference_number', $referenceNumbers);


        if ($from && $to) {
            $mainQuery->whereBetween('booking_date', [$from, $to]);
        }

        if ($request->has('status') && $request->input('status') !== 'all') {
            $mainQuery->where('status', $request->input('status'));
        }

        if ($request->has('branch_id') && $request->input('branch_id') !== 'all') {
            $mainQuery->where('branch_id', $request->input('branch_id'));
        }


        $mainQuery->select(DB::raw('MIN(id) as id'))
            ->groupBy('reference_number');

        $bookingIds = $mainQuery->pluck('id');


        $bookingQuery = Booking::with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->whereIn('id', $bookingIds);


        $sortField = $request->input('sort_field', 'booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $bookingQuery->orderBy($sortField, $sortDirection);


        $perPage = $request->input('per_page', 10);
        $bookings = $bookingQuery->paginate($perPage)->withQueryString();


        $bookings->getCollection()->transform(function ($booking) {
            $payment = $booking->payment;
            $relatedBookings = Booking::where('reference_number', $booking->reference_number)->get();
            $relatedCount = $relatedBookings->count();
            $totalPrice = $relatedBookings->sum('total_price');

            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => Carbon::parse($booking->booking_date),
                'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                'user_name' => $booking->user ? $booking->user->name : 'N/A',
                'user_email' => $booking->user ? $booking->user->email : 'N/A',
                'customer_name' => $booking->customer ? $booking->customer->name : ($booking->customer_name ?? 'N/A'),
                'customer_contact' => $booking->customer ? ($booking->customer->phone ?? $booking->customer->email) : ($booking->customer_phone ?? $booking->customer_email ?? 'N/A'),
                'court_name' => $booking->courtBookings->first() ? $booking->courtBookings->first()->court->name : 'N/A',
                'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                'total_price' => $booking->total_price,
                'total_price_formatted' => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
                'combined_total_price' => $totalPrice,
                'combined_total_price_formatted' => number_format((float) $totalPrice, 0, ',', '.') . ' ₫',
                'status' => $booking->status,
                'status_display' => $this->getBookingStatusDisplay($booking->status),
                'status_class' => $this->getBookingStatusClass($booking->status),
                'payment_status' => $payment ? $payment->status : null,
                'payment_method' => $payment ? $payment->payment_method : null,
                'payment_method_display' => $payment ? $this->getPaymentMethodName($payment->payment_method) : null,
                'notes' => $booking->notes,
                'number_of_players' => $booking->number_of_players,
                'booking_type' => $booking->booking_type,
                'cancellation_reason' => $booking->cancellation_reason,
                'cancelled_at' => $booking->cancelled_at ? Carbon::parse($booking->cancelled_at)->format('d/m/Y H:i') : null,
                'related_courts_count' => $relatedCount,
                'created_at' => $booking->created_at,
            ];
        });


        $branches = DB::table('branches')
            ->select('id', 'name')
            ->orderBy('name')
            ->get()
            ->map(function ($branch) {
                return [
                    'value' => $branch->id,
                    'label' => $branch->name
                ];
            })
            ->toArray();


        array_unshift($branches, ['value' => 'all', 'label' => 'Tất cả chi nhánh']);


        $statuses = [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xác nhận'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
            ['value' => 'no_show', 'label' => 'Không đến'],
        ];


        $summaryStats = $this->getSummaryStats($from, $to);

        return Inertia::render('SuperAdmin/BookingHistory', [
            'bookings' => $bookings,
            'filters' => [
                'search' => $request->input('search', ''),
                'from_date' => $from ? $from->format('Y-m-d') : null,
                'to_date' => $to ? $to->format('Y-m-d') : null,
                'branch_id' => $request->input('branch_id', 'all'),
                'status' => $request->input('status', 'all'),
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
                'per_page' => $perPage,
            ],
            'branches' => $branches,
            'statuses' => $statuses,
            'summary' => $summaryStats,
        ]);
    }

    /**
     * Get booking status display name
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusDisplay($status)
    {
        $statuses = [
            'pending' => 'Chờ xác nhận',
            'confirmed' => 'Đã xác nhận',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            'no_show' => 'Không đến',
        ];

        return $statuses[$status] ?? 'Không xác định';
    }
    /**
     * Get payment status display name
     *
     * @param string $status
     * @return string
     */
    private function getPaymentStatusDisplay($status)
    {
        $statuses = [
            'pending' => 'Chờ thanh toán',
            'completed' => 'Đã thanh toán',
            'unpaid' => 'Chưa thanh toán',
            'partial' => 'Thanh toán một phần',
        ];

        return $statuses[$status] ?? 'Không xác định';
    }

    /**
     * Get booking status CSS class
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusClass($status)
    {
        $classes = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'confirmed' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            'no_show' => 'bg-gray-100 text-gray-800',
        ];

        return $classes[$status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Get payment method display name
     *
     * @param string|null $methodCode
     * @return string
     */
    private function getPaymentMethodName($methodCode)
    {
        $methods = [
            'cash' => 'Tiền mặt',
            'bank_transfer' => 'Chuyển khoản',
            'credit_card' => 'Thẻ tín dụng',
            'momo' => 'Ví MoMo',
            'vnpay' => 'VNPAY',
            'zalopay' => 'ZaloPay',
        ];

        return $methods[$methodCode] ?? 'Khác';
    }

    /**
     * Get summary statistics
     *
     * @param Carbon|null $from
     * @param Carbon|null $to
     * @param int|null $businessId
     * @param int|null $branchId
     * @return array
     */
    private function getSummaryStats(?Carbon $from = null, ?Carbon $to = null, ?int $businessId = null, ?int $branchId = null)
    {

        $dateCondition = [];
        if ($from && $to) {
            $dateCondition = ['booking_date' => [$from, $to]];
        }


        $totalAmount = 0;
        $countByStatus = [
            'pending' => 0,
            'confirmed' => 0,
            'completed' => 0,
            'cancelled' => 0,
            'no_show' => 0
        ];


        $bookingQuery = Booking::query();

        if ($from && $to) {
            $bookingQuery->whereBetween('booking_date', [$from, $to]);
        }

        if ($businessId) {
            $bookingQuery->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        }

        if ($branchId) {
            $bookingQuery->where('branch_id', $branchId);
        }


        $mainCompletedAmount = $bookingQuery->where('status', 'completed')->sum('total_price');
        $totalAmount += $mainCompletedAmount;


        $courtBookingQuery = DB::table('court_bookings')
            ->select('court_bookings.reference_number', 'court_bookings.status', DB::raw('SUM(court_bookings.total_price) as total_price'));

        if ($from && $to) {
            $courtBookingQuery->whereBetween('court_bookings.booking_date', [$from, $to]);
        }

        if ($businessId) {
            $courtBookingQuery->join('branches', 'court_bookings.branch_id', '=', 'branches.id')
                ->where('branches.business_id', $businessId);
        }

        if ($branchId) {
            $courtBookingQuery->where('court_bookings.branch_id', $branchId);
        }


        $courtBookingResults = $courtBookingQuery
            ->groupBy('court_bookings.reference_number', 'court_bookings.status')
            ->get();


        $refNumbersByStatus = [];
        $courtBookingAmount = 0;

        foreach ($courtBookingResults as $result) {
            if (!isset($refNumbersByStatus[$result->status])) {
                $refNumbersByStatus[$result->status] = [];
            }

            $refNumbersByStatus[$result->status][] = $result->reference_number;

            if ($result->status === 'completed') {
                $courtBookingAmount += $result->total_price;
            }
        }


        foreach ($refNumbersByStatus as $status => $refNumbers) {
            $uniqueRefNumbers = array_unique($refNumbers);
            $countByStatus[$status] = count($uniqueRefNumbers);
        }

        $totalAmount += $courtBookingAmount;


        $branchesQuery = DB::table('branches')
            ->select('branches.id', 'branches.name')
            ->distinct();

        if ($businessId) {
            $branchesQuery->where('branches.business_id', $businessId);
        }

        if ($branchId) {
            $branchesQuery->where('branches.id', $branchId);
        }

        $branches = $branchesQuery->get();
        $bookingsByBranch = [];

        foreach ($branches as $branch) {

            $branchQuery = DB::table('court_bookings')
                ->select('court_bookings.reference_number', DB::raw('SUM(court_bookings.total_price) as total_amount'))
                ->where('court_bookings.branch_id', $branch->id)
                ->where('court_bookings.status', 'completed');

            if ($from && $to) {
                $branchQuery->whereBetween('court_bookings.booking_date', [$from, $to]);
            }

            $branchRefResults = $branchQuery
                ->groupBy('court_bookings.reference_number')
                ->get();

            $branchCount = $branchRefResults->count();
            $branchTotal = $branchRefResults->sum('total_amount');

            if ($branchCount > 0) {
                $amountPercentage = $totalAmount > 0 ? round(($branchTotal / $totalAmount) * 100, 1) : 0;

                $bookingsByBranch[] = [
                    'branch_name' => $branch->name,
                    'booking_count' => $branchCount,
                    'total_amount' => $branchTotal,
                    'total_amount_formatted' => number_format((float) $branchTotal, 0, ',', '.') . ' ₫',
                    'percentage' => $amountPercentage,
                ];
            }
        }


        usort($bookingsByBranch, function ($a, $b) {
            return $b['booking_count'] <=> $a['booking_count'];
        });

        return [
            'total_amount' => $totalAmount,
            'total_amount_formatted' => number_format((float) $totalAmount, 0, ',', '.') . ' ₫',
            'total_bookings' => array_sum($countByStatus),
            'pending_count' => $countByStatus['pending'] ?? 0,
            'confirmed_count' => $countByStatus['confirmed'] ?? 0,
            'completed_count' => $countByStatus['completed'] ?? 0,
            'cancelled_count' => $countByStatus['cancelled'] ?? 0,
            'no_show_count' => $countByStatus['no_show'] ?? 0,
            'bookings_by_branch' => $bookingsByBranch,
        ];
    }

    /**
     * Business view - displays bookings for a specific business/branch
     *
     * @param Request $request
     * @param int|null $businessId
     * @param int|null $branchId
     * @return \Inertia\Response
     */
    private function businessIndex(Request $request, ?int $businessId, ?int $branchId)
    {

        $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();


        $query = Booking::query()
            ->with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->select('bookings.*')
            ->whereIn('branch_id', $branchIds);


        if ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        }


        if ($branchId) {
            $query->where('branch_id', $branchId);
        }


        if ($request->has('from_date') && $request->has('to_date')) {
            $from = Carbon::parse($request->input('from_date'))->startOfDay();
            $to = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('booking_date', [$from, $to]);
        } else {
            $to = null;
            $from = null;
        }


        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }


        if (!$branchId && $request->has('branch_id') && $request->input('branch_id') !== 'all') {
            $query->where('branch_id', $request->input('branch_id'));
        }


        if ($request->has('search') && $request->input('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_phone', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_email', 'like', "%{$searchTerm}%")
                    ->orWhereHas('courtBookings.court', function ($courtQuery) use ($searchTerm) {
                        $courtQuery->where('name', 'like', "%{$searchTerm}%");
                    });
            });
        }


        $referenceNumbers = clone $query;
        $referenceNumbers = $referenceNumbers->pluck('reference_number');


        $mainQuery = Booking::with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->whereIn('reference_number', $referenceNumbers);


        if ($businessId) {
            $mainQuery->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        }

        if ($branchId) {
            $mainQuery->where('branch_id', $branchId);
        }


        if ($from && $to) {
            $mainQuery->whereBetween('booking_date', [$from, $to]);
        }

        if ($request->has('status') && $request->input('status') !== 'all') {
            $mainQuery->where('status', $request->input('status'));
        }


        $mainQuery->select(DB::raw('MIN(id) as id'))->groupBy('reference_number');
        $bookingIds = $mainQuery->pluck('id');


        $sortField = $request->input('sort_field', 'booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $bookings = Booking::with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->whereIn('id', $bookingIds)
            ->orderBy($sortField, $sortDirection)
            ->paginate($request->input('per_page', 10))
            ->withQueryString();


        $bookings->getCollection()->transform(function ($booking) {
            $payment = $booking->payment;
            $relatedBookings = Booking::where('reference_number', $booking->reference_number)->get();
            $totalPrice = $relatedBookings->sum('total_price');

            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                'customer_name' => $booking->customer ? $booking->customer->name : ($booking->customer_name ?? 'N/A'),
                'court_name' => $booking->courtBookings->first() ? $booking->courtBookings->first()->court->name : 'N/A',
                'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                'total_price' => $booking->total_price,
                'total_price_formatted' => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
                'combined_total_price' => $totalPrice,
                'combined_total_price_formatted' => number_format((float) $totalPrice, 0, ',', '.') . ' ₫',
                'status' => $booking->status,
                'status_display' => $this->getBookingStatusDisplay($booking->status),
                'status_class' => $this->getBookingStatusClass($booking->status),
                'payment_status' => $payment ? $payment->status : null,
                'payment_method' => $payment ? $payment->payment_method : null,
                'payment_method_display' => $payment ? $this->getPaymentMethodName($payment->payment_method) : null,
                'related_courts_count' => $relatedBookings->count(),
            ];
        });


        $branches = [];
        if (!$branchId && $businessId) {
            $branches = DB::table('branches')
                ->where('business_id', $businessId)
                ->select('id', 'name')
                ->orderBy('name')
                ->get()
                ->map(fn($branch) => ['value' => $branch->id, 'label' => $branch->name])
                ->toArray();
            array_unshift($branches, ['value' => 'all', 'label' => 'Tất cả chi nhánh']);
        }


        $statuses = [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xác nhận'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
            ['value' => 'no_show', 'label' => 'Không đến'],
        ];


        $summaryStats = $this->getSummaryStats($from, $to, $businessId, $branchId);

        return Inertia::render('Business/BookingHistory', [
            'bookings' => $bookings,
            'filters' => [
                'search' => $request->input('search', ''),
                'from_date' => $from ? $from->format('Y-m-d') : null,
                'to_date' => $to ? $to->format('Y-m-d') : null,
                'branch_id' => $request->input('branch_id', 'all'),
                'status' => $request->input('status', 'all'),
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
                'per_page' => $request->input('per_page', 10),
            ],
            'branches' => $branches,
            'statuses' => $statuses,
            'summary' => $summaryStats,
        ]);
    }

    /**
     * Get booking details including all related courts
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBookingDetails(Request $request)
    {
        $bookingId = $request->input('booking_id');
        $booking = Booking::with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->findOrFail($bookingId);


        $user = $request->user();
        if (!$user->hasRole('super-admin')) {
            if ($user->business_id) {
                $branchExists = DB::table('branches')
                    ->where('id', $booking->branch_id)
                    ->where('business_id', $user->business_id)
                    ->exists();

                if (!$branchExists) {
                    return response()->json(['error' => 'You do not have permission to view this booking'], 403);
                }
            }


            if ($user->hasRole('staff') && $user->branch_id != $booking->branch_id) {
                return response()->json(['error' => 'You do not have permission to view this booking'], 403);
            }
        }


        $relatedBookings = Booking::with(['branch:id,name'])
            ->where('reference_number', $booking->reference_number)
            ->where('id', '!=', $booking->id)
            ->get()
            ->map(function ($relatedBooking) {
                return [
                    'id' => $relatedBooking->id,
                    'court_name' => $relatedBooking->courtBookings->first() ? $relatedBooking->courtBookings->first()->court->name : 'N/A',
                    'booking_time' => Carbon::parse($relatedBooking->start_time)->format('H:i') . ' - ' . Carbon::parse($relatedBooking->end_time)->format('H:i'),
                    'total_price' => $relatedBooking->total_price,
                    'total_price_formatted' => number_format((float) $relatedBooking->total_price, 0, ',', '.') . ' ₫',
                    'metadata' => $relatedBooking->metadata,
                ];
            });

        $payment = $booking->payment;


        $totalPrice = $booking->total_price;
        $relatedBookings->each(function ($relatedBooking) use (&$totalPrice) {
            $totalPrice += $relatedBooking['total_price'];
        });
        $totalPriceFormatted = number_format((float) $totalPrice, 0, ',', '.') . ' ₫';


        $allCourts = Booking::with(['branch:id,name'])
            ->where('reference_number', $booking->reference_number)
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'court_name' => $booking->courtBookings->first() ? $booking->courtBookings->first()->court->name : 'N/A',
                    'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                    'total_price' => $booking->total_price,
                    'total_price_formatted' => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
                    'status' => $booking->status,
                    'status_display' => $this->getBookingStatusDisplay($booking->status),
                    'status_class' => $this->getBookingStatusClass($booking->status),
                    'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                    'number_of_players' => $booking->number_of_players,
                ];
            });

        $bookingDetails = [
            'id' => $booking->id,
            'reference_number' => $booking->reference_number,
            'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
            'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
            'created_at' => $booking->created_at->format('d/m/Y H:i'),
            'user_name' => $booking->user ? $booking->user->name : 'N/A',
            'user_email' => $booking->user ? $booking->user->email : 'N/A',
            'customer_name' => $booking->customer ? $booking->customer->name : ($booking->customer_name ?? 'N/A'),
            'customer_contact' => $booking->customer ? ($booking->customer->phone ?? $booking->customer->email) : ($booking->customer_phone ?? $booking->customer_email ?? 'N/A'),
            'court_name' => $booking->courtBookings->first() ? $booking->courtBookings->first()->court->name : 'N/A',
            'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
            'total_price' => $booking->total_price,
            'total_price_formatted' => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
            'combined_total_price' => $totalPrice,
            'combined_total_price_formatted' => $totalPriceFormatted,
            'status' => $booking->status,
            'status_display' => $this->getBookingStatusDisplay($booking->status),
            'status_class' => $this->getBookingStatusClass($booking->status),
            'payment_status' => $payment ? $payment->status : null,
            'payment_method' => $payment ? $payment->payment_method : null,
            'payment_method_display' => $payment ? $this->getPaymentMethodName($payment->payment_method) : null,
            'notes' => $booking->notes,
            'number_of_players' => $booking->number_of_players,
            'booking_type' => $booking->booking_type,
            'cancellation_reason' => $booking->cancellation_reason,
            'cancelled_at' => $booking->cancelled_at ? Carbon::parse($booking->cancelled_at)->format('d/m/Y H:i') : null,
            'related_courts' => $relatedBookings,
            'all_courts' => $allCourts,
        ];

        return response()->json($bookingDetails);
    }

    /**
     * Export bookings as Excel or PDF
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'excel');
        $user = $request->user();
        $businessId = null;
        $isSuperAdmin = false;

        if ($user->hasRole('super-admin')) {
            $isSuperAdmin = true;
        } elseif ($user->hasRole('admin')) {
            $isSuperAdmin = true;
            $businessId = $user->business_id;
        } else {
            $businessId = $user->business_id;
        }

        $query = Booking::query()
            ->with(['branch:id,name', 'user:id,name,email', 'customer:id,name,phone,email', 'courtBookings.court:id,name'])
            ->select('bookings.*');

        if (!($isSuperAdmin && !$businessId)) {
            $branchIds = DB::table('branches')
                ->where('business_id', $businessId)
                ->pluck('id')
                ->toArray();

            $query->whereIn('branch_id', $branchIds);
        }

        if ($request->has('from_date') && $request->has('to_date')) {
            $from = Carbon::parse($request->input('from_date'))->startOfDay();
            $to = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('booking_date', [$from, $to]);
        }

        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('branch_id') && $request->input('branch_id') !== 'all') {
            $query->where('branch_id', $request->input('branch_id'));
        }

        if ($request->has('search') && $request->input('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_phone', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_email', 'like', "%{$searchTerm}%")
                    ->orWhereHas('courtBookings.court', function ($courtQuery) use ($searchTerm) {
                        $courtQuery->where('name', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('branch', function ($branchQuery) use ($searchTerm) {
                        $branchQuery->where('name', 'like', "%{$searchTerm}%");
                    });
            });
        }

        $sortField = $request->input('sort_field', 'booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $referenceNumbers = (clone $query)->distinct('reference_number')->pluck('reference_number');

        $paymentsData = DB::table('payments')
            ->whereIn('booking_reference', $referenceNumbers)
            ->select('booking_reference', 'amount', 'reference_number', 'payment_method', 'status')
            ->get()
            ->groupBy('booking_reference');

        $exportData = collect();

        foreach ($referenceNumbers as $refNumber) {
            $booking = (clone $query)
                ->where('reference_number', $refNumber)
                ->first();

            if (!$booking) {
                continue;
            }

            $courtNames = $booking->courtBookings->pluck('court.name')->join(', ');

            $paymentStatusDisplay = 'N/A';
            if ($booking->payment_status) {
                $paymentStatusDisplay = $this->getPaymentStatusDisplay($booking->payment_status);
            }

            $bookingStatusDisplay = $this->getBookingStatusDisplay($booking->status);

            $totalPaidAmount = 0;
            $paymentReferences = [];

            if (isset($paymentsData[$refNumber])) {
                $bookingPayments = $paymentsData[$refNumber];
                $totalPaidAmount = $bookingPayments->where('status', 'completed')->sum('amount');
                $paymentReferences = $bookingPayments->pluck('reference_number')->filter()->unique()->implode(', ');
            }

            $formattedPaidAmount = number_format((float) $totalPaidAmount, 0, ',', '.') . ' ₫';

            $exportData->push([
                'ID' => $booking->id,
                __('booking.reference_number') => $booking->reference_number,
                __('booking.date') => $booking->booking_date ? Carbon::parse($booking->booking_date)->format('d/m/Y') : 'N/A',
                __('booking.time') => $booking->start_time ? Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i') : 'N/A',
                __('booking.customer') => $booking->customer ? $booking->customer->name : ($booking->customer_name ?: 'N/A'),
                __('booking.customer') . ' ' . __('booking.contact') => $booking->customer ? ($booking->customer->phone ?? $booking->customer->email) : ($booking->customer_phone ?? $booking->customer_email ?? 'N/A'),
                __('booking.courts') => $courtNames ?: 'N/A',
                __('booking.branch') => $booking->branch ? $booking->branch->name : 'N/A',
                __('booking.price') => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
                __('booking.total_paid') => $formattedPaidAmount,
                __('booking.payment_status') => $paymentStatusDisplay,
                __('booking.payment_references') => $paymentReferences ?: 'N/A',
                __('booking.status') => $bookingStatusDisplay,
                __('booking.created_at') => $booking->created_at ? $booking->created_at->format('d/m/Y H:i') : 'N/A',
                __('booking.booking_type') => $booking->booking_type === 'online' ? __('booking.online') : __('booking.offline'),
                __('booking.notes') => $booking->notes ?: '',
            ]);
        }

        $filename = 'booking_history';
        if (!($isSuperAdmin && !$businessId) && $businessId) {
            $businessName = DB::table('businesses')->where('id', $businessId)->value('name') ?? 'business';
            $businessName = str_replace(' ', '_', strtolower($businessName));
            $filename .= '_' . $businessName;
        } else {
            $filename .= '_all_businesses';
        }

        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = Carbon::parse($request->input('from_date'));
            $toDate = Carbon::parse($request->input('to_date'));
            $filename .= '_' . $fromDate->format('Ymd') . '_to_' . $toDate->format('Ymd');
        } else {
            $filename .= '_' . date('Ymd');
        }

        $exportService = new ExportService();

        if ($format === 'pdf') {
            $title = 'Lịch sử đặt sân';
            $subtitle = !($isSuperAdmin && !$businessId) && $businessId
                ? DB::table('businesses')->where('id', $businessId)->value('name') ?? 'Doanh nghiệp'
                : 'Tất cả các doanh nghiệp';

            return $exportService->exportPDF($exportData, $filename, $title, $subtitle);
        } else {
            return $exportService->exportExcel($exportData, $filename);
        }
    }
}
