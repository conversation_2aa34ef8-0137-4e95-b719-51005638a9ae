<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Mail\BookingRejection;
use App\Models\Branch;
use App\Models\Business;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\Booking;
use App\Services\BookingEventService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class BookingOnlineController extends Controller
{
    /**
     * Display a listing of bookings based on user role.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('super-admin', $userRoles)) {
            return $this->superAdminIndex($request);
        } elseif (count(array_intersect(['admin'], $userRoles)) > 0) {
            return $this->businessIndex($request);
        } else {
            return $this->branchIndex($request);
        }
    }

    /**
     * Display bookings for super admin.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    private function superAdminIndex(Request $request)
    {
        $branch = null;
        $branchId = null;
        $businessId = null;

        $query = Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
            ->where('booking_type', 'online');


        if ($request->has('business_id') && !empty($request->business_id) && !$request->has('business_id')) {
            $businessId = $request->input('business_id');
            $business = Business::find($businessId);
            $listBranchIds = $business->branches->pluck('id')->toArray();
            $query->whereIn('branch_id', $listBranchIds);
        }

        if ($request->has('branch_id') && !empty($request->branch_id)) {
            $branchId = $request->input('branch_id');
            $query->where('branch_id', $branchId);
            $branch = Branch::with('business')->find($branchId);
            if ($branch) {
                $businessId = $branch->business_id;
            }
        }

        $query = $this->applyBookingFilters($request, $query);


        $bookings = $query->paginate(10);


        $formattedBookings = $this->formatBookingsWithPayments($bookings, $request);


        $today = Carbon::today();
        $yesterday = Carbon::yesterday();


        $statsQuery = Booking::where('booking_type', 'online');
        if ($branchId) {
            $statsQuery->where('branch_id', $branchId);
        }


        $todayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $today)->count();
        $todayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $today)->count();
        $todayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $today)->count();
        $todayTotal = (clone $statsQuery)->whereDate('booking_date', $today)->count();


        $yesterdayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayTotal = (clone $statsQuery)->whereDate('booking_date', $yesterday)->count();


        $pendingChange = $yesterdayPending > 0 ? round(($todayPending - $yesterdayPending) / $yesterdayPending * 100) : 0;
        $approvedChange = $yesterdayApproved > 0 ? round(($todayApproved - $yesterdayApproved) / $yesterdayApproved * 100) : 0;
        $rejectedChange = $yesterdayRejected > 0 ? round(($todayRejected - $yesterdayRejected) / $yesterdayRejected * 100) : 0;
        $totalChange = $yesterdayTotal > 0 ? round(($todayTotal - $yesterdayTotal) / $yesterdayTotal * 100) : 0;

        $stats = [
            'pending' => $todayPending,
            'approved' => $todayApproved,
            'rejected' => $todayRejected,
            'total' => $todayTotal,
            'pending_change' => $pendingChange,
            'approved_change' => $approvedChange,
            'rejected_change' => $rejectedChange,
            'total_change' => $totalChange,
        ];

        $allBusiness = Business::orderBy('name')->get(['id', 'name']);
        $allBranches = Branch::with('business')->orderBy('name')->get(['id', 'name', 'business_id']);

        $courts = [];
        if ($branchId) {
            $courts = Court::where('branch_id', $branchId)->get(['id', 'name']);
        }

        $bankSettings = null;
        if ($businessId) {
            $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);
        }

        return $this->renderBookingView($request, $formattedBookings, $branch, $bankSettings, $courts, $allBranches, $stats, $allBusiness);
    }

    /**
     * Display bookings for business admin.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    private function businessIndex(Request $request)
    {
        $user = $request->user();
        $businessId = $user->business_id;
        $userBranchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
        $branchId = $request->input('branch_id', null);
        $branch = null;

        if (!$branchId || !in_array($branchId, $userBranchIds)) {

            if ($branchId == 'all') {
                $branchId = null;
            } else {

                $branch = Branch::where('business_id', $businessId)->first();
                $branchId = $branch ? $branch->id : null;
            }
        } else {
            $branch = Branch::with('business')->find($branchId);
        }

        if (empty($userBranchIds)) {
            return $this->unauthorizedResponse();
        }


        $query = Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
            ->where('booking_type', 'online')
            ->whereIn('branch_id', $userBranchIds);


        if ($branchId) {
            $query->where('branch_id', $branchId);
        }


        $query = $this->applyBookingFilters($request, $query);


        $bookings = $query->paginate(10);


        $formattedBookings = $this->formatBookingsWithPayments($bookings, $request);


        $today = Carbon::today();
        $yesterday = Carbon::yesterday();


        $statsQuery = Booking::where('booking_type', 'online')
            ->whereIn('branch_id', $userBranchIds);
        if ($branchId) {
            $statsQuery->where('branch_id', $branchId);
        }


        $todayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $today)->count();
        $todayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $today)->count();
        $todayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $today)->count();
        $todayTotal = (clone $statsQuery)->whereDate('booking_date', $today)->count();


        $yesterdayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayTotal = (clone $statsQuery)->whereDate('booking_date', $yesterday)->count();


        $pendingChange = $yesterdayPending > 0 ? round(($todayPending - $yesterdayPending) / $yesterdayPending * 100) : 0;
        $approvedChange = $yesterdayApproved > 0 ? round(($todayApproved - $yesterdayApproved) / $yesterdayApproved * 100) : 0;
        $rejectedChange = $yesterdayRejected > 0 ? round(($todayRejected - $yesterdayRejected) / $yesterdayRejected * 100) : 0;
        $totalChange = $yesterdayTotal > 0 ? round(($todayTotal - $yesterdayTotal) / $yesterdayTotal * 100) : 0;


        $stats = [
            'pending' => $todayPending,
            'approved' => $todayApproved,
            'rejected' => $todayRejected,
            'total' => $todayTotal,
            'pending_change' => $pendingChange,
            'approved_change' => $approvedChange,
            'rejected_change' => $rejectedChange,
            'total_change' => $totalChange,
        ];

        $allBranches = Branch::whereIn('id', $userBranchIds)->orderBy('name')->get(['id', 'name']);
        $courts = [];
        if ($branchId) {
            $courts = Court::where('branch_id', $branchId)->get(['id', 'name']);
        }

        $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);

        return $this->renderBookingView($request, $formattedBookings, $branch, $bankSettings, $courts, $allBranches, $stats);
    }

    /**
     * Display bookings for branch staff.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    private function branchIndex(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id;
        $branch = null;
        $businessId = null;

        if ($branchId) {
            $userBranchIds = [$branchId];
            $branch = Branch::with('business')->find($branchId);
            if ($branch) {
                $businessId = $branch->business_id;
            }
        }

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }


        $query = Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
            ->where('booking_type', 'online')
            ->where('branch_id', $branchId);


        $query = $this->applyBookingFilters($request, $query);


        $bookings = $query->paginate(10);


        $formattedBookings = $this->formatBookingsWithPayments($bookings, $request);


        $today = Carbon::today();
        $yesterday = Carbon::yesterday();


        $statsQuery = Booking::where('booking_type', 'online')
            ->where('branch_id', $branchId);


        $todayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $today)->count();
        $todayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $today)->count();
        $todayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $today)->count();
        $todayTotal = (clone $statsQuery)->whereDate('booking_date', $today)->count();


        $yesterdayPending = (clone $statsQuery)->where('status', 'pending')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayApproved = (clone $statsQuery)->where('status', 'confirmed')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayRejected = (clone $statsQuery)->where('status', 'cancelled')
            ->whereDate('booking_date', $yesterday)->count();
        $yesterdayTotal = (clone $statsQuery)->whereDate('booking_date', $yesterday)->count();


        $pendingChange = $yesterdayPending > 0 ? round(($todayPending - $yesterdayPending) / $yesterdayPending * 100) : 0;
        $approvedChange = $yesterdayApproved > 0 ? round(($todayApproved - $yesterdayApproved) / $yesterdayApproved * 100) : 0;
        $rejectedChange = $yesterdayRejected > 0 ? round(($todayRejected - $yesterdayRejected) / $yesterdayRejected * 100) : 0;
        $totalChange = $yesterdayTotal > 0 ? round(($todayTotal - $yesterdayTotal) / $yesterdayTotal * 100) : 0;


        $stats = [
            'pending' => $todayPending,
            'approved' => $todayApproved,
            'rejected' => $todayRejected,
            'total' => $todayTotal,
            'pending_change' => $pendingChange,
            'approved_change' => $approvedChange,
            'rejected_change' => $rejectedChange,
            'total_change' => $totalChange,
        ];

        $courts = Court::where('branch_id', $branchId)->get(['id', 'name']);

        $bankSettings = null;
        if ($businessId) {
            $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);
        }

        return $this->renderBookingView($request, $formattedBookings, $branch, $bankSettings, $courts, [], $stats);
    }

    /**
     * Apply common filters to booking queries
     *
     * @param Request $request
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyBookingFilters(Request $request, $query)
    {
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_phone', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->has('status') && !empty($request->status) && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->has('time') && !empty($request->time) && $request->time !== 'all') {

            if (Schema::hasColumn('bookings', 'booking_date')) {
                switch ($request->time) {
                    case 'today':
                        $query->whereDate('booking_date', Carbon::today());
                        break;
                    case 'tomorrow':
                        $query->whereDate('booking_date', Carbon::tomorrow());
                        break;
                    case 'yesterday':
                        $query->whereDate('booking_date', Carbon::yesterday());
                        break;
                    case 'lastweek':
                        $query->whereBetween('booking_date', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->subWeek()->endOfWeek()]);
                        break;
                    case 'lastmonth':
                        $query->whereBetween('booking_date', [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]);
                        break;
                    case 'thismonth':
                        $query->whereBetween('booking_date', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()]);
                        break;
                    case 'thisweek':
                        $query->whereBetween('booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                        break;
                    case 'nextweek':
                        $query->whereBetween('booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()]);
                        break;
                    default:

                        $query->whereHas('courtBookings', function ($q) use ($request) {
                            $this->applyTimeFilterToQuery($q, $request->time);
                        });
                        break;
                }
            } else {

                $query->whereHas('courtBookings', function ($q) use ($request) {
                    $this->applyTimeFilterToQuery($q, $request->time);
                });
            }
        }

        if ($request->has('court_id') && !empty($request->court_id) && $request->court_id !== 'all') {
            $courtId = $request->court_id;
            $query->whereHas('courtBookings', function ($q) use ($courtId) {
                $q->where('court_id', $courtId);
            });
        }

        $query->select('bookings.*');
        $query->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'));

        return $query;
    }

    /**
     * Apply time filter to a query
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $timeFilter
     * @return void
     */
    private function applyTimeFilterToQuery($query, $timeFilter)
    {
        switch ($timeFilter) {
            case 'today':
                $query->whereDate('booking_date', Carbon::today());
                break;
            case 'tomorrow':
                $query->whereDate('booking_date', Carbon::tomorrow());
                break;
            case 'yesterday':
                $query->whereDate('booking_date', Carbon::yesterday());
                break;
            case 'lastweek':
                $query->whereBetween('booking_date', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->subWeek()->endOfWeek()]);
                break;
            case 'lastmonth':
                $query->whereBetween('booking_date', [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]);
                break;
            case 'thismonth':
                $query->whereBetween('booking_date', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()]);
                break;
            case 'thisweek':
                $query->whereBetween('booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                break;
            case 'nextweek':
                $query->whereBetween('booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()]);
                break;
        }
    }

    /**
     * Format bookings with payment information
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator $bookings
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    private function formatBookingsWithPayments($bookings, Request $request)
    {
        $referenceNumbers = collect($bookings->items())->pluck('reference_number')->unique()->toArray();

        $payments = \App\Models\Payment::whereIn('booking_reference', $referenceNumbers)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $paymentsByReference = $payments->groupBy('booking_reference');

        $formattedBookings = collect($bookings->items())->map(function ($booking) use ($paymentsByReference) {
            $courtBookings = $booking->courtBookings;
            $totalPrice = $booking->total_price;
            $referenceNumber = $booking->reference_number;

            $bookingPayments = isset($paymentsByReference[$referenceNumber])
                ? $paymentsByReference[$referenceNumber]
                : collect();

            $paidAmount = $bookingPayments->sum('amount');
            $paymentStatus = 'pending';

            if ($paidAmount >= $totalPrice) {
                $paymentStatus = 'completed';
            } elseif ($paidAmount > 0) {
                $paymentStatus = Booking::PAYMENT_STATUS_PARTIAL;
            }

            $hasBankTransfer = $bookingPayments->contains('payment_method_id', 2);
            $hasProof = $bookingPayments->contains('has_proof', true);
            $latestPayment = $bookingPayments->sortByDesc('created_at')->first();
            $paymentMethods = $bookingPayments->pluck('payment_method')->unique()->implode(', ');


            $firstCourtBooking = $courtBookings->first();
            $bookingDate = $firstCourtBooking ? $firstCourtBooking->booking_date->format('Y-m-d') : null;
            $startTime = $firstCourtBooking ? $firstCourtBooking->start_time->format('H:i') : null;
            $endTime = $firstCourtBooking ? $firstCourtBooking->end_time->format('H:i') : null;

            return [
                'id' => $booking->id,
                'reference_number' => $referenceNumber,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'customer_email' => $booking->customer_email,
                'status' => $booking->status,
                'created_at' => $booking->created_at->format('Y-m-d H:i:s'),
                'booking_date' => $bookingDate,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'total_price' => $totalPrice,
                'formatted_total_price' => number_format($totalPrice, 0, ',', '.') . 'đ',
                'court_bookings' => $courtBookings->map(function ($courtBooking) {
                    return [
                        'id' => $courtBooking->id,
                        'court' => $courtBooking->court,
                        'booking_date' => $courtBooking->booking_date->format('Y-m-d'),
                        'start_time' => $courtBooking->start_time->format('H:i'),
                        'end_time' => $courtBooking->end_time->format('H:i'),
                        'price' => $courtBooking->total_price,
                        'formatted_price' => number_format($courtBooking->total_price, 0, ',', '.') . 'đ',
                    ];
                })->values(),

                'payments' => $bookingPayments->values(),
                'payment_summary' => [
                    'total_paid' => $paidAmount,
                    'payment_count' => $bookingPayments->count(),
                    'has_bank_transfer' => $hasBankTransfer,
                    'has_proof' => $hasProof,
                    'payment_methods' => $paymentMethods,
                    'latest_payment' => $latestPayment,
                    'payment_status' => $paymentStatus
                ],
                'branch' => [
                    'id' => $booking->branch_id,
                    'name' => $booking->branch ? $booking->branch->name : null,
                ]
            ];
        });

        $formattedPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $formattedBookings,
            $bookings->total(),
            $bookings->perPage(),
            $bookings->currentPage(),
            [
                'path' => $bookings->path(),
                'query' => $request->query(),
            ]
        );

        return $formattedPaginator;
    }

    /**
     * Render the booking view with all necessary data
     *
     * @param Request $request
     * @param \Illuminate\Pagination\LengthAwarePaginator $bookings
     * @param \App\Models\Branch|null $branch
     * @param array|null $bankSettings
     * @param \Illuminate\Database\Eloquent\Collection $courts
     * @param \Illuminate\Database\Eloquent\Collection $allBranches
     * @param \Illuminate\Database\Eloquent\Builder $statsQuery
     * @param \Illuminate\Database\Eloquent\Collection $allBusiness
     * @return \Inertia\Response
     */
    private function renderBookingView(Request $request, $bookings, $branch, $bankSettings, $courts, $allBranches, $stats, $allBusiness = [])
    {
        $user = $request->user();
        $componentPath = '';

        if ($user->hasRole('super-admin')) {
            $componentPath = 'SuperAdmin/BookingOnline/index';
        } elseif ($user->hasRole('admin')) {
            $componentPath = 'Business/BookingOnline/index';
        } else {
            $componentPath = 'Branchs/BookingOnline/index';
        }

        $routePrefix = $branch ? 'branch' : 'admin';


        $statuses = $this->getAvailableStatuses();

        return Inertia::render($componentPath, [
            'bookings' => $bookings,
            'branch' => $branch,
            'bankSettings' => $bankSettings,
            'courts' => $courts,
            'allBranches' => $allBranches,
            'allBusiness' => $allBusiness,
            'stats' => $stats,
            'statuses' => $statuses,
            'routePrefix' => $routePrefix,
            'filters' => $request->only(['search', 'status', 'court_id', 'time', 'branch_id', 'business_id']),
        ]);
    }

    /**
     * Get available booking statuses with their display names.
     *
     * @return array
     */
    private function getAvailableStatuses()
    {
        return [
            'pending' => 'Chờ xác nhận',
            'confirmed' => 'Đã xác nhận',
            'cancelled' => 'Đã hủy',
            'completed' => 'Đã hoàn thành',
        ];
    }

    /**
     * Return unauthorized response
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    private function unauthorizedResponse()
    {
        return redirect()->route('redirect.to.unauthorized', [
            'message' => 'Bạn không có quyền truy cập trang này',
            'redirect_url' => route('dashboard')
        ]);
    }

    /**
     * Approve a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(Request $request, $id)
    {
        $user = $request->user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (
            !(in_array('super-admin', $userRoles) ||
                in_array('admin', $userRoles) ||
                $this->userHasPermission($user, 'booking:approve'))
        ) {
            return response()->json(['error' => 'Bạn không có quyền thực hiện hành động này'], 403);
        }

        $mainBooking = Booking::find($id);

        if (!$mainBooking) {
            $courtBooking = CourtBooking::findOrFail($id);
            $referenceNumber = $courtBooking->reference_number;

            $mainBooking = Booking::where('reference_number', $referenceNumber)->first();
        } else {
            $referenceNumber = $mainBooking->reference_number;
            $courtBooking = CourtBooking::where('reference_number', $referenceNumber)->first();
        }


        $relatedCourtBookings = CourtBooking::where('reference_number', $referenceNumber)->get();

        $pendingPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->with('paymentMethod')
            ->get();

        if ($pendingPayments->count() > 0 && !$request->force_approve) {
            foreach ($pendingPayments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            return response()->json([
                'warning' => true,
                'message' => 'Đơn đặt sân này có thanh toán chưa được xác nhận.',
                'requires_confirmation' => true,
                'pending_payments' => $pendingPayments,
                'pending_payment_count' => $pendingPayments->count()
            ]);
        }

        DB::beginTransaction();

        try {

            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes ?? "",
                'approved_at' => now()->toDateTimeString(),
            ];


            if ($mainBooking) {
                $mainBooking->status = 'confirmed';


                if (Schema::hasColumn('bookings', 'notes')) {
                    $mainBooking->notes = $request->notes ?? $mainBooking->notes;
                }


                if (Schema::hasColumn('bookings', 'metadata')) {
                    $mainBooking->metadata = array_merge($mainBooking->metadata ?? [], $approvalMetadata);
                }

                $mainBooking->save();
            }


            foreach ($relatedCourtBookings as $relatedCourtBooking) {
                $relatedCourtBooking->status = 'confirmed';


                if (Schema::hasColumn('court_bookings', 'notes')) {
                    $relatedCourtBooking->notes = $request->notes ?? $relatedCourtBooking->notes;
                }


                if (Schema::hasColumn('court_bookings', 'metadata')) {
                    $relatedCourtBooking->metadata = array_merge($relatedCourtBooking->metadata ?? [], $approvalMetadata);
                }

                $relatedCourtBooking->save();


                BookingEventService::bookingApproved($relatedCourtBooking, Auth::id());
            }


            $completedPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->where('status', 'completed')
                ->get();

            if ($completedPayments->count() > 0) {
                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $relatedCourtBookings->sum('total_price');

                $paymentStatus = ($totalPaid >= $totalPrice) ? Booking::PAYMENT_STATUS_PAID :
                    ($totalPaid > 0 ? Booking::PAYMENT_STATUS_PARTIAL : Booking::PAYMENT_STATUS_UNPAID);


                if ($mainBooking) {
                    $mainBooking->payment_status = $paymentStatus;
                    $mainBooking->save();
                }


                foreach ($relatedCourtBookings as $relatedCourtBooking) {
                    $relatedCourtBooking->payment_status = $paymentStatus;
                    $relatedCourtBooking->save();
                }
            }

            DB::commit();


            try {
                if ($courtBooking && $courtBooking->customer_email) {
                    $bookingsArray = $relatedCourtBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($courtBooking->customer_email)->send(
                        new BookingConfirmation(
                            $referenceNumber,
                            $relatedCourtBookings->sum('total_price'),
                            $bookingsArray,
                            $courtBooking
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving booking: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject(Request $request, $id)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();


        if (
            !(in_array('super-admin', $userRoles) ||
                in_array('admin', $userRoles) ||
                $this->userHasPermission($user, 'booking:reject'))
        ) {
            return response()->json(['error' => 'Bạn không có quyền thực hiện hành động này'], 403);
        }

        $booking = CourtBooking::with(['booking'])->findOrFail($id);

        if (!$request->has('reason') || empty($request->reason)) {
            return response()->json([
                'error' => 'Vui lòng nhập lý do từ chối.'
            ], 422);
        }

        DB::beginTransaction();

        try {
            $referenceNumber = $booking->reference_number;
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();

            $rejectionMetadata = [
                'rejected_by' => $user->id,
                'rejected_by_name' => $user->name,
                'rejected_at' => now()->toDateTimeString(),
                'rejection_reason' => $request->reason
            ];

            $cancelledAt = now();

            foreach ($relatedBookings as $relatedBooking) {
                $relatedBooking->status = 'cancelled';
                $relatedBooking->cancellation_reason = $request->reason;
                $relatedBooking->cancelled_at = $cancelledAt;
                $relatedBooking->metadata = array_merge($relatedBooking->metadata ?? [], $rejectionMetadata);
                $relatedBooking->save();


                if ($relatedBooking->booking_id) {
                    $parentBooking = Booking::find($relatedBooking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'cancelled';
                        $parentBooking->payment_status = 'cancelled';
                        $parentBooking->cancelled_at = $cancelledAt;
                        $parentBooking->cancellation_reason = $request->reason;
                        $parentBooking->save();
                    }
                }


                BookingEventService::bookingCancelled($relatedBooking, $request->reason, null, Auth::id());
            }


            $pendingPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->whereIn('status', ['pending', 'pending_approval'])
                ->get();

            foreach ($pendingPayments as $payment) {
                $payment->status = 'cancelled';
                $payment->payment_details = array_merge(
                    is_array($payment->payment_details) ? $payment->payment_details : [],
                    [
                        'cancelled_by' => $user->id,
                        'cancelled_by_name' => $user->name,
                        'cancelled_at' => now()->toDateTimeString(),
                        'reason' => $request->reason
                    ]
                );
                $payment->save();
            }

            DB::commit();


            try {
                if ($booking->customer_email) {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($booking->customer_email)->send(
                        new BookingRejection(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking,
                            $booking->cancellation_reason
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking rejection email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được từ chối'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error rejecting booking: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi từ chối đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve a payment
     *
     * @param Request $request
     * @param int $paymentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvePayment(Request $request, $paymentId)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (
            !(in_array('super-admin', $userRoles) ||
                in_array('admin', $userRoles) || in_array('manager', $userRoles)
                || $this->userHasPermission($user, 'booking:approve_payment'))
        ) {
            return response()->json(['error' => 'Bạn không có quyền thực hiện hành động này'], 403);
        }

        $payment = \App\Models\Payment::findOrFail($paymentId);

        DB::beginTransaction();

        try {
            $payment->status = 'completed';
            $payment->approved_at = now();
            $payment->approved_by = Auth::id();

            $paymentDetails = is_array($payment->payment_details)
                ? $payment->payment_details
                : [];

            $paymentDetails['approved_by'] = $user->id;
            $paymentDetails['approved_by_name'] = $user->name;
            $paymentDetails['approved_at'] = now()->toDateTimeString();

            $payment->payment_details = $paymentDetails;
            $payment->save();

            $referenceNumber = $payment->booking_reference;
            $bookings = CourtBooking::where('reference_number', $referenceNumber)->get();

            if ($bookings->isNotEmpty()) {
                $totalBookingAmount = $bookings->sum('total_price');
                $approvedPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                    ->where('status', 'completed')
                    ->get();

                $totalPaidAmount = $approvedPayments->sum('amount');

                $paymentStatus = ($totalPaidAmount >= $totalBookingAmount) ? Booking::PAYMENT_STATUS_PAID : Booking::PAYMENT_STATUS_PARTIAL;


                Booking::syncBookingStatusByReference($referenceNumber, null, $paymentStatus);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thanh toán đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving payment: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận thanh toán: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment information for a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentInfo(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $referenceNumber = $booking->reference_number;

        $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();
        $totalPrice = $relatedBookings->sum('total_price');
        $paidAmount = $payments->sum('amount');

        return response()->json([
            'success' => true,
            'payments' => $payments,
            'total_price' => $totalPrice,
            'paid_amount' => $paidAmount,
            'remaining_amount' => $totalPrice - $paidAmount,
        ]);
    }

    /**
     * Approve a booking by reference number
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveByReference(Request $request, $reference_number)
    {
        $user = $request->user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (
            !(in_array('super-admin', $userRoles) ||
                in_array('admin', $userRoles) ||
                $this->userHasPermission($user, 'booking:approve'))
        ) {
            return response()->json(['error' => 'Bạn không có quyền thực hiện hành động này'], 403);
        }

        $bookings = CourtBooking::where('reference_number', $reference_number)->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'error' => 'Không tìm thấy đơn đặt sân với mã tham chiếu này.'
            ], 404);
        }

        $firstBooking = $bookings->first();

        $pendingPayments = \App\Models\Payment::where('booking_reference', $reference_number)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->with('paymentMethod')
            ->get();

        if ($pendingPayments->count() > 0 && !$request->force_approve) {
            foreach ($pendingPayments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            return response()->json([
                'warning' => true,
                'message' => 'Đơn đặt sân này có thanh toán chưa được xác nhận.',
                'requires_confirmation' => true,
                'pending_payments' => $pendingPayments,
                'pending_payment_count' => $pendingPayments->count()
            ]);
        }

        DB::beginTransaction();

        try {
            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes,
                'approved_at' => now()->toDateTimeString(),
            ];


            Booking::syncBookingStatusByReference($reference_number, 'confirmed', null, $approvalMetadata);


            foreach ($bookings as $booking) {
                BookingEventService::bookingApproved($booking, Auth::id());
            }


            $completedPayments = \App\Models\Payment::where('booking_reference', $reference_number)
                ->where('status', 'completed')
                ->get();

            if ($completedPayments->count() > 0) {
                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $bookings->sum('total_price');

                $paymentStatus = ($totalPaid >= $totalPrice) ? Booking::PAYMENT_STATUS_PAID :
                    ($totalPaid > 0 ? Booking::PAYMENT_STATUS_PARTIAL : Booking::PAYMENT_STATUS_UNPAID);


                Booking::syncBookingStatusByReference($reference_number, null, $paymentStatus);
            }

            DB::commit();


            try {
                if ($firstBooking->customer_email) {
                    $relatedBookings = CourtBooking::where('reference_number', $firstBooking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($firstBooking->customer_email)->send(
                        new BookingConfirmation(
                            $firstBooking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $firstBooking
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving booking by reference: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add payment information to a booking
     *
     * @param array $booking
     * @return array
     */
    private function addPaymentInfo($booking)
    {
        $referenceNumber = $booking['reference_number'];

        $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $booking['payments'] = $payments;

        $totalPrice = $booking['total_price'];
        $paidAmount = $payments->sum('amount');
        $paymentStatus = 'pending';

        if ($paidAmount >= $totalPrice) {
            $paymentStatus = 'completed';
        } elseif ($paidAmount > 0) {
            $paymentStatus = Booking::PAYMENT_STATUS_PARTIAL;
        }

        $hasBankTransfer = $payments->contains('payment_method_id', 2);
        $hasProof = $payments->contains('has_proof', true);
        $latestPayment = $payments->sortByDesc('created_at')->first();
        $paymentMethods = $payments->pluck('payment_method')->unique()->implode(', ');

        $booking['payment_summary'] = [
            'total_paid' => $paidAmount,
            'payment_count' => $payments->count(),
            'has_bank_transfer' => $hasBankTransfer,
            'has_proof' => $hasProof,
            'payment_methods' => $paymentMethods,
            'latest_payment' => $latestPayment,
            'payment_status' => $paymentStatus
        ];

        return $booking;
    }

    /**
     * Check if user has a specific permission
     *
     * @param \App\Models\User $user
     * @param string $permission
     * @return bool
     */
    private function userHasPermission($user, $permission)
    {

        if (method_exists($user, 'hasPermissionTo')) {
            return $user->hasPermissionTo($permission);
        }

        if (method_exists($user, 'can')) {
            return $user->can($permission);
        }


        if (isset($user->permissions)) {
            return $user->permissions->pluck('name')->contains($permission);
        }

        return false;
    }


}