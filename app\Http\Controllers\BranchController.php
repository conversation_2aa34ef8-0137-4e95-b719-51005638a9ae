<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\BranchImage;
use App\Models\Business;
use App\Models\User;
use App\Services\BranchService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Inertia\Inertia;

class BranchController extends Controller
{
    protected $branchService;
    protected $viewPrefix;
    protected $routePrefix;
    protected $isSuperAdmin;

    public function __construct(BranchService $branchService, Request $request)
    {
        $this->branchService = $branchService;

        $routeName = $request->route() ? $request->route()->getName() : '';
        $this->isSuperAdmin = str_starts_with($routeName, 'superadmin.');

        $this->viewPrefix = $this->isSuperAdmin ? 'SuperAdmin' : 'Business';
        $this->routePrefix = $this->isSuperAdmin ? 'superadmin' : 'business';
    }

    /**
     * Display a listing of the branchs.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $query = Branch::with('business');
        if (!$this->isSuperAdmin) {
            $businessId = $request->user()->business_id;
            $user = $request->user();
            $roles = $user->roles ?? collect();
            $roleNames = isset($user->roles) ? $roles->pluck('name')->toArray() : [];
            $isAdmin = in_array('admin', $roleNames) || in_array('super-admin', $roleNames);

            if (!$businessId && !$isAdmin) {
                session([
                    'unauthorized_role' => 'trang quản lý business',
                    'unauthorized_message' => 'Bạn cần phải được gán cho một doanh nghiệp để truy cập khu vực này.',
                ]);
                return redirect()->route('unauthorized');
            }
            if (!$businessId && $isAdmin) {
                session([
                    'unauthorized_role' => 'trang quản trị doanh nghiệp',
                    'unauthorized_message' => 'Bạn không có quyền truy cập vào khu vực quản trị doanh nghiệp.',
                ]);
                return redirect()->route('unauthorized');
            }

            $query->where('business_id', $businessId);
        }

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('contact_email', 'like', "%{$search}%")
                    ->orWhere('contact_phone', 'like', "%{$search}%");

                if ($this->isSuperAdmin) {
                    $q->orWhereHas('business', function ($businessQ) use ($search) {
                        $businessQ->where('name', 'like', "%{$search}%");
                    });
                }
            });
        }

        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        if ($this->isSuperAdmin && $request->has('business_id') && !empty($request->business_id)) {
            $query->where('business_id', $request->business_id);
        }

        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $branchs = $query->paginate(10)->withQueryString();

        $branchs->getCollection()->transform(function ($branch) {
            if ($this->isSuperAdmin) {
                $branch->business_name = $branch->business->name;
            }

            $branch->formatted_opening_hour = $branch->opening_hour ? $branch->opening_hour->format('H:i') : null;
            $branch->formatted_closing_hour = $branch->closing_hour ? $branch->closing_hour->format('H:i') : null;

            return $branch;
        });

        $viewData = [
            'branchs' => $branchs,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ];

        if ($this->isSuperAdmin) {
            $viewData['businesses'] = Business::where('status', 'active')->get(['id', 'name']);
            $viewData['filters']['business_id'] = $request->business_id ?? '';
        }

        return Inertia::render("{$this->viewPrefix}/Branch/Index", $viewData);
    }

    /**
     * Show the form for creating a new branch.
     *
     * @return \Inertia\Response
     */
    public function create(Request $request)
    {
        $viewData = [];

        if ($this->isSuperAdmin) {
            $businesses = Business::get(['id', 'name', 'contact_phone', 'contact_email']);

            $activeUsers = User::where('status', 'active')
                ->get(['id', 'name', 'email']);

            $businessesWithUsers = $this->branchService->prepareBusinessesWithUsers($businesses, $activeUsers);

            $viewData['businesses'] = $businessesWithUsers;
            $viewData['users'] = $activeUsers;
        } else {
            $businessId = $request->user()->business_id;

            $activeUsers = User::where('status', 'active')
                ->where('business_id', $businessId)
                ->with('roles:id,name')
                ->get(['id', 'name', 'email']);

            $viewData['users'] = $activeUsers;
        }

        return Inertia::render("{$this->viewPrefix}/Branch/Create", $viewData);
    }

    /**
     * Store a newly created branch in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validationRules = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'required|string',
            'province_id' => 'nullable',
            'district_id' => 'nullable',
            'ward_id' => 'nullable',
            'province_name' => 'nullable|string',
            'district_name' => 'nullable|string',
            'ward_name' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'opening_hour' => 'nullable|date_format:H:i',
            'closing_hour' => 'nullable|date_format:H:i',
            'status' => 'required|in:active,inactive,pending_approval',
            'managers' => 'nullable|array',
            'managers.*' => 'exists:users,id',
            'main_image_id' => 'nullable|integer',
        ];

        if ($this->isSuperAdmin) {
            $validationRules['business_id'] = 'required|exists:businesses,id';
        }

        $validated = $request->validate($validationRules, __('validation.branch'));

        if (!$this->isSuperAdmin) {
            $businessId = $request->user()->business_id;

            if (!$businessId) {
                return redirect()->back()->withErrors([
                    'error' => __('business.no_business')
                ]);
            }
            $validated['business_id'] = $businessId;
            $validated['status'] = 'pending_approval';
        }

        try {
            DB::beginTransaction();

            
            $branch = Branch::create($validated);

            
            if ($request->hasFile('branch_images')) {
                $this->branchService->handleBranchImages($request, $branch);
            }
            if ($request->hasFile('branch_main_image')) {
                $this->branchService->handleBranchImages($request, $branch, true);
            }

            
            if ($request->has('managers') && !empty($request->managers)) {
                $this->branchService->assignBranchManagers($branch, $request->managers);
            }

            
            NotificationService::createNewBranchForBusiness(
                $branch->business_id,
                $branch->id,
                $branch->name,
                $branch->address,
                $branch->contact_phone ?? '',
                $branch->main_image_url
            );

            DB::commit();

            return redirect()->route("{$this->routePrefix}.branch.index")
                ->with('flash.success', __('branch.created_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create branch: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'flash.error' => __('branch.failed_to_create')
            ])->withInput();
        }
    }


    /**
     * Display the specified branch.
     *
     * @param  \App\Models\Branch  $branch
     * @return \Inertia\Response
     */
    public function show(Request $request, Branch $branch)
    {
        if (!$this->isSuperAdmin) {
            $user = $request->user();
            $businessId = $user->business_id;
            $hasAccess = $user->branch_id === $branch->id || $businessId === $branch->business_id;

            if (!$hasAccess) {
                return redirect()->route('business.branch.index')
                    ->with('flash.error', __('branch.unauthorized_view'));
            }
        }

        $branch->load('business', 'images', 'prices');
        $branch->formatted_opening_hour = $branch->opening_hour ? $branch->opening_hour->format('H:i') : null;
        $branch->formatted_closing_hour = $branch->closing_hour ? $branch->closing_hour->format('H:i') : null;
        $branch->images->transform(function ($image) {
            $image->url = Storage::url($image->image_url);
            return $image;
        });

        $branchStaff = $branch->getAllStaffByBranch($branch->id);

        return Inertia::render("{$this->viewPrefix}/Branch/Show", [
            'branch' => $branch,
            'branchStaff' => $branchStaff,
        ]);
    }

    /**
     * Show the form for editing the specified branch.
     *
     * @param  \App\Models\Branch  $branch
     * @return \Inertia\Response
     */
    public function edit(Request $request, Branch $branch)
    {
        if (!$this->isSuperAdmin) {
            $user = $request->user();
            $businessId = $user->business_id;
            $hasAccess = $user->branch_id === $branch->id || $businessId === $branch->business_id;

            if (!$hasAccess) {
                return redirect()->route('business.branch.index')
                    ->with('flash.error', __('branch.unauthorized_edit'));
            }
        }

        $branch->load('images');
        $branch->formatted_opening_hour = $branch->opening_hour ? $branch->opening_hour->format('H:i') : null;
        $branch->formatted_closing_hour = $branch->closing_hour ? $branch->closing_hour->format('H:i') : null;


        $branchStaff = \App\Models\User::where('branch_id', $branch->id)
            ->where('status', 'active')
            ->with('roles:id,name')
            ->get(['id', 'name', 'email']);

        Log::info('Branch staff count: ' . $branchStaff->count());
        foreach ($branchStaff as $index => $staff) {
            Log::info("Branch staff #{$index}: ID {$staff->id}, Name: {$staff->name}, Email: {$staff->email}");
        }

        $viewData = [
            'branch' => $branch,
            'branchManagers' => $branchStaff,
        ];


        $businessId = $this->isSuperAdmin ? $branch->business_id : $request->user()->business_id;

        $activeUsers = User::where('status', 'active')
            ->where('business_id', $businessId)
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['staff', 'manager']);
            })
            ->with('roles:id,name')
            ->where(function ($query) use ($branch) {
                $query->whereNull('branch_id')
                    ->orWhere('branch_id', $branch->id);
            })
            ->get(['id', 'name', 'email']);

        $viewData['users'] = $activeUsers;

        if ($this->isSuperAdmin) {
            $businesses = Business::where('status', 'active')->get(['id', 'name']);
            $viewData['businesses'] = $businesses;
        }

        return Inertia::render("{$this->viewPrefix}/Branch/Edit", $viewData);
    }

    /**
     * Update the specified branch in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Branch  $branch
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Branch $branch)
    {
        if (!$this->isSuperAdmin) {
            $user = $request->user();
            $businessId = $user->business_id;
            $hasAccess = $user->branch_id === $branch->id || $businessId === $branch->business_id;

            if (!$hasAccess) {
                return redirect()->route('business.branch.index')
                    ->with('flash.error', __('branch.unauthorized_update'));
            }
        }

        $validationRules = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'required|string',
            'province_id' => 'nullable',
            'district_id' => 'nullable',
            'ward_id' => 'nullable',
            'province_name' => 'nullable|string',
            'district_name' => 'nullable|string',
            'ward_name' => 'nullable|string',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'opening_hour' => 'nullable|date_format:H:i',
            'closing_hour' => 'nullable|date_format:H:i',
            'status' => 'required|in:active,inactive,pending_approval',
            'deleted_images' => 'nullable|array',
            'deleted_images.*' => 'exists:branch_images,id',
            'managers' => 'nullable|array',
            'managers.*' => 'exists:users,id',
        ];

        if ($this->isSuperAdmin) {
            $validationRules['business_id'] = 'required|exists:businesses,id';
        }

        $validated = $request->validate($validationRules, __('validation.branch'));

        try {
            DB::beginTransaction();

            if (!$this->isSuperAdmin) {
                $validated['business_id'] = $branch->business_id;
            }

            $changedFields = [];
            foreach ($validated as $key => $value) {
                if ($branch->$key != $value) {
                    $changedFields[] = $key;
                }
            }

            $branch->update($validated);

            if ($request->has('deleted_images') && !empty($request->deleted_images)) {
                $this->branchService->handleDeletedImages($branch, $request->deleted_images);
            }

            if ($request->hasFile('branch_images')) {
                $this->branchService->handleBranchImages($request, $branch);
            }
            if ($request->hasFile('branch_main_image')) {
                $this->branchService->handleBranchImages($request, $branch, true);
            }

            if ($request->has('managers') && !empty($request->managers)) {
                $this->branchService->updateBranchManagers($branch, $request->managers);
            }

            if (!empty($changedFields)) {
                $updaterInfo = $this->isSuperAdmin ? 'SUPERADMIN: ' : 'ADMIN: ';
                $updaterInfo .= $request->user()->name . ' - ' . $request->user()->id;

                NotificationService::updateBranchForBusiness(
                    $branch->business_id,
                    $branch->id,
                    $branch->name,
                    $branch->address,
                    $branch->contact_phone ?? '',
                    $branch->main_image_url,
                    $changedFields
                );
            }

            DB::commit();

            return redirect()->route("{$this->routePrefix}.branch.index")
                ->with('flash.success', __('branch.updated_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update branch: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return redirect()->back()->withErrors([
                'flash.error' => __('branch.failed_to_update')
            ])->withInput();
        }
    }

    /**
     * Remove the specified branch from storage.
     *
     * @param  \App\Models\Branch  $branch
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, Branch $branch)
    {
        if (!$this->isSuperAdmin) {
            $user = $request->user();
            $businessId = $user->business_id;
            $hasAccess = $user->branch_id === $branch->id || $businessId === $branch->business_id;

            if (!$hasAccess) {
                return redirect()->route('business.branch.index')
                    ->with('flash.error', __('branch.unauthorized_delete'));
            }
        }

        try {
            DB::beginTransaction();

            
            $branchId = $branch->id;
            $branchName = $branch->name;
            $businessId = $branch->business_id;
            $deleterInfo = $this->isSuperAdmin ? 'SUPERADMIN: ' : 'ADMIN: ';
            $deleterInfo .= $request->user()->name . ' - ' . $request->user()->id;

            foreach ($branch->images as $image) {
                if ($image->image_url && Storage::disk('public')->exists(str_replace('/storage/', '', $image->image_url))) {
                    Storage::disk('public')->delete(str_replace('/storage/', '', $image->image_url));
                }
                $image->delete();
            }
            if ($branch->main_image_url && Storage::disk('public')->exists(str_replace('/storage/', '', $branch->main_image_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $branch->main_image_url));
            }

            User::where('branch_id', $branch->id)->update(['branch_id' => null]);
            $oldBranchId = $branch->id;
            $branch->delete();

            NotificationService::deleteBranchForBusiness(
                $businessId,
                $oldBranchId,
                $branchName,
                $deleterInfo
            );

            DB::commit();

            return redirect()->route("{$this->routePrefix}.branch.index")
                ->with('flash.success', __('branch.deleted_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete branch: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return redirect()->back()->withErrors([
                'flash.error' => __('branch.failed_to_delete')
            ]);
        }
    }
}