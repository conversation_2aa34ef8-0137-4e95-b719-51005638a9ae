<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class BranchStaffController extends Controller
{
    /**
     * Store a newly created staff.
     */
    public function store(Request $request)
    {
        if (!Auth::user()->hasAnyRole(['super-admin', 'admin']) && !Auth::user()->can('booking:create_staff')) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'branch_id' => 'required|exists:branches,id',
            'business_id' => 'required|exists:businesses,id',
            'role' => 'required|string|exists:roles,name',
            'status' => 'required|string|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'status' => $request->status,
                'branch_id' => $request->branch_id,
                'business_id' => $request->business_id,
            ]);

            $user->assignRole($request->role);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('branch.staff_created_successfully'),
                'data' => $user
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('branch.failed_to_create_staff'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified staff.
     */
    public function update(Request $request, User $user)
    {
        if (!Auth::user()->hasAnyRole(['super-admin', 'admin']) && !Auth::user()->can('booking:update_staff')) {
            abort(403, 'Unauthorized action.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8',
            'branch_id' => 'required|exists:branches,id',
            'business_id' => 'required|exists:businesses,id',
            'role' => 'required|string|exists:roles,name',
            'status' => 'required|string|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $user->name = $request->name;
            $user->email = $request->email;
            if ($request->password) {
                $user->password = bcrypt($request->password);
            }
            $user->status = $request->status;
            $user->branch_id = $request->branch_id;
            $user->business_id = $request->business_id;
            $user->save();

            $role = Role::where('name', $request->role)->first();
            $user->syncRoles([$role]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('branch.staff_updated_successfully'),
                'data' => $user
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('branch.failed_to_update_staff'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified staff from the branch (unassign).
     */
    public function unassignStaff(Request $request, User $user)
    {
        if (!Auth::user()->hasAnyRole(['super-admin', 'admin']) && !Auth::user()->can('booking:delete_staff')) {
            abort(403, 'Unauthorized action.');
        }

        DB::beginTransaction();
        try {
            if ($user->branch_id === null) {
                return response()->json([
                    'message' => __('branch.staff_not_assigned_to_branch'),
                ], 422);
            }

            $user->branch_id = null;
            $user->save();

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => __('branch.staff_unassigned_successfully')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('branch.failed_to_remove_staff'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get business staff not assigned to the branch.
     */
    public function getBusinessStaff(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'business_id' => 'required|exists:businesses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $businessStaff = User::select('id', 'name', 'email', 'status', 'branch_id')
            ->with('roles')
            ->with('branch:id,name')
            ->where('business_id', $request->business_id)
            ->where(function ($query) use ($request) {
                $query->whereNull('branch_id')
                    ->orWhere('branch_id', '!=', $request->branch_id);
            })
            ->whereDoesntHave('roles', function ($query) {
                $query->whereIn('name', ['user', 'admin', 'super-admin']);
            })
            ->get();

        $businessStaff->each(function ($user) {
            $user->role_name = $user->roles->pluck('name')->first();
        });

        return response()->json([
            'success' => true,
            'staff' => $businessStaff
        ]);
    }

    /**
     * Assign staff to branch.
     */
    public function assignStaff(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'branch_id' => 'required|exists:branches,id',
            'business_id' => 'required|exists:businesses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $businessUserCount = User::where('business_id', $request->business_id)
            ->whereIn('id', $request->user_ids)
            ->count();

        if ($businessUserCount != count($request->user_ids)) {
            return response()->json([
                'success' => false,
                'message' => __('branch.invalid_staff_selection')
            ], 422);
        }

        DB::beginTransaction();
        try {
            foreach ($request->user_ids as $userId) {
                $user = User::find($userId);
                if ($user) {
                    $user->branch_id = $request->branch_id;
                    $user->business_id = $request->business_id;
                    $user->save();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('branch.staff_assigned_successfully')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => __('branch.failed_to_assign_staff'),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}