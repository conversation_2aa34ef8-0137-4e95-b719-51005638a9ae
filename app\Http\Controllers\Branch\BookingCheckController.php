<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\CourtBooking;
use App\Models\Court;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class BookingCheckController extends Controller
{
    /**
     * Hiển thị trang quản lý check-in/check-out
     *
     * @param Request $request
     * @return \Inertia\Response|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $branch = Auth::user()->branch;
        $date = $request->date ?? Carbon::now()->format('Y-m-d');
        $confirmedBookings = $this->getConfirmedBookings($branch, $date);
        $checkedInBookings = $this->getCheckedInBookings($branch, $date);
        $completedBookings = $this->getCompletedBookings($branch, $date);
        $notCompletedBookings = $this->getNotCompletedBookings($branch, $date);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'confirmedBookings' => $confirmedBookings,
                'checkedInBookings' => $checkedInBookings,
                'completedBookings' => $completedBookings,
                'notCompletedBookings' => $notCompletedBookings,
                'message' => 'Dữ liệu đã được cập nhật.'
            ]);
        }

        $allReferenceNumbers = collect([]);
        $confirmedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $checkedInBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $completedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });
        $notCompletedBookings->each(function ($booking) use ($allReferenceNumbers) {
            $allReferenceNumbers->push($booking['reference_number']);
        });


        $uniqueReferenceNumbers = $allReferenceNumbers->unique()->values();


        $referenceGroupsInfo = [];
        foreach ($uniqueReferenceNumbers as $refNumber) {

            $totalCount = CourtBooking::where('reference_number', $refNumber)->count();


            $currentDateCount = $confirmedBookings->where('reference_number', $refNumber)->count() +
                $checkedInBookings->where('reference_number', $refNumber)->count() +
                $completedBookings->where('reference_number', $refNumber)->count() +
                $notCompletedBookings->where('reference_number', $refNumber)->count();


            if ($totalCount > $currentDateCount) {
                $referenceGroupsInfo[$refNumber] = [
                    'total' => $totalCount,
                    'current' => $currentDateCount,
                    'missing' => $totalCount - $currentDateCount
                ];
            }
        }


        foreach ($referenceGroupsInfo as $refNumber => $info) {

            $missingBookings = CourtBooking::with(['court', 'customer', 'branch'])
                ->where('reference_number', $refNumber)
                ->where('branch_id', $branch->id)
                ->whereDate('booking_date', '!=', $date)
                ->get();

            foreach ($missingBookings as $booking) {
                $formattedBooking = $this->formatBooking($booking);


                if ($booking->status === 'completed') {
                    $completedBookings->push($formattedBooking);
                } elseif ($booking->checkin_status === 'checked_out' && $booking->status !== 'completed') {
                    $notCompletedBookings->push($formattedBooking);
                } elseif ($booking->checkin_status === 'checked_in') {
                    $checkedInBookings->push($formattedBooking);
                } elseif ($booking->status === 'confirmed') {
                    $confirmedBookings->push($formattedBooking);
                }
            }
        }


        $sortByDateAndTime = function ($a, $b) {

            $dateComparison = strcmp($a['booking_date'], $b['booking_date']);
            if ($dateComparison !== 0) {
                return $dateComparison;
            }


            return strcmp($a['start_time'], $b['start_time']);
        };

        $confirmedBookings = $confirmedBookings->sort($sortByDateAndTime)->values();
        $checkedInBookings = $checkedInBookings->sort($sortByDateAndTime)->values();
        $completedBookings = $completedBookings->sort($sortByDateAndTime)->values();
        $notCompletedBookings = $notCompletedBookings->sort($sortByDateAndTime)->values();

        return Inertia::render('Branchs/Booking/CheckInOut', [
            'branch' => $branch,
            'currentDate' => $date,
            'confirmedBookings' => $confirmedBookings,
            'checkedInBookings' => $checkedInBookings,
            'completedBookings' => $completedBookings,
            'notCompletedBookings' => $notCompletedBookings,
            'filters' => $request->all('date'),
            'referenceGroups' => $referenceGroupsInfo,
        ]);
    }

    /**
     * Display the check-in page for a reference number
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Inertia\Response
     */
    public function show(Request $request, $reference_number = null)
    {
        $user = Auth::user();
        $branch = $user->branch;


        $referenceNumber = $reference_number;


        if (!$referenceNumber && $request->has('reference')) {
            $referenceNumber = $request->query('reference');
        }

        $booking = null;
        $bookings = [];

        if ($referenceNumber) {

            $booking = CourtBooking::with(['branch', 'customer', 'services'])
                ->where('reference_number', $referenceNumber)
                ->orderBy('id')
                ->first();

            if ($booking) {

                $bookings = CourtBooking::with(['court', 'branch', 'services'])
                    ->where('reference_number', $referenceNumber)
                    ->orderBy('booking_date')
                    ->orderBy('start_time')
                    ->get();
            }
        }

        return Inertia::render('Branchs/Checkin/Index', [
            'branch' => $branch,
            'reference_number' => $referenceNumber,
            'booking' => $booking,
            'bookings' => $bookings,
            'error' => $booking ? null : 'Không tìm thấy đơn đặt sân với mã tham chiếu này'
        ]);
    }

    /**
     * Thực hiện check-in cho booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkIn(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);


        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải ở trạng thái đã xác nhận để có thể check-in'
            ], 400);
        }


        if ($booking->checkin_status === 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân đã được check-in trước đó'
            ], 400);
        }


        $forceCheckin = $request->input('force_checkin', false);


        if (!$forceCheckin) {

            if (strlen($booking->start_time) > 8) {

                $startDateTime = \Carbon\Carbon::parse($booking->start_time);
            } else {

                $bookingStartTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->start_time;
                $startDateTime = \Carbon\Carbon::parse($bookingStartTime);
            }

            $now = now();


            if ($now->lt($startDateTime->copy()->subMinutes(30))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chưa đến thời gian check-in (sớm hơn 30 phút so với giờ bắt đầu)'
                ], 400);
            }


            if (strlen($booking->end_time) > 8) {

                $endDateTime = \Carbon\Carbon::parse($booking->end_time);
            } else {

                $bookingEndTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->end_time;
                $endDateTime = \Carbon\Carbon::parse($bookingEndTime);
            }

            if ($now->gt($endDateTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Đã quá thời gian check-in (sau giờ kết thúc đặt sân)'
                ], 400);
            }
        }


        $userId = Auth::id();
        $success = $booking->checkIn($userId);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Đã check-in thành công',
                'booking' => $booking
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi check-in'
            ], 500);
        }
    }

    /**
     * Thực hiện check-out cho booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkOut(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);


        if ($booking->checkin_status !== 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải đã được check-in trước khi check-out'
            ], 400);
        }


        $checkoutAll = $request->input('checkout_all', false);
        $forceCheckout = $request->input('force_checkout', false);
        $userId = Auth::id();


        $referenceNumber = $booking->reference_number;

        if ($checkoutAll) {

            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('checkin_status', 'checked_in')
                ->where('id', '!=', $booking->id)
                ->get();


            $successCount = 0;
            $failedCount = 0;


            $success = $booking->checkOut($userId);


            if ($success && $booking->overtime_minutes > 0) {
                $this->calculateOvertimeFee($booking);
            }

            if ($success) {
                $successCount++;
            } else {
                $failedCount++;
            }


            foreach ($relatedBookings as $relatedBooking) {
                $relatedSuccess = $relatedBooking->checkOut($userId);


                if ($relatedSuccess && $relatedBooking->overtime_minutes > 0) {
                    $this->calculateOvertimeFee($relatedBooking);
                }

                if ($relatedSuccess) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }


            if ($failedCount === 0) {
                return response()->json([
                    'success' => true,
                    'message' => "Đã check-out thành công tất cả {$successCount} sân với mã tham chiếu {$referenceNumber}",
                    'booking' => $booking,
                    'all_completed' => true,
                    'checkout_count' => $successCount
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => "Đã check-out thành công {$successCount} sân, {$failedCount} sân thất bại",
                    'booking' => $booking,
                    'all_completed' => false,
                    'checkout_count' => $successCount,
                    'failed_count' => $failedCount
                ]);
            }
        } else {
            $success = $booking->checkOut($userId);


            if ($success && $booking->overtime_minutes > 0) {
                $this->calculateOvertimeFee($booking);
            }

            if ($success) {

                $allBookings = CourtBooking::where('reference_number', $referenceNumber)->get();
                $allCheckedOut = true;

                foreach ($allBookings as $relatedBooking) {
                    if ($relatedBooking->checkin_status !== 'checked_out') {
                        $allCheckedOut = false;
                        break;
                    }
                }


                return response()->json([
                    'success' => true,
                    'message' => $allCheckedOut ?
                        'Đã check-out thành công. Tất cả các sân đã được check-out.' :
                        'Đã check-out thành công',
                    'booking' => $booking,
                    'all_checked_out' => $allCheckedOut,
                    'remaining_bookings' => $allCheckedOut ? 0 : $allBookings->where('checkin_status', '!=', 'checked_out')->count()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi check-out'
                ], 500);
            }
        }
    }

    /**
     * Tính phí chênh lệch dựa trên thời gian checkout và giá sân
     * 
     * @param CourtBooking $booking
     * @return bool
     */
    private function calculateOvertimeFee(CourtBooking $booking)
    {

        if ($booking->overtime_minutes <= 0) {
            return false;
        }

        try {

            $court = Court::find($booking->court_id);
            if (!$court) {
                return false;
            }


            $checkoutTime = $booking->checkout_time;
            $hour = $checkoutTime->format('H:i');
            $date = $booking->booking_date->format('Y-m-d');


            $priceModel = \App\Models\CourtPrice::getPriceForDateTime(
                $booking->branch_id,
                $court->court_type,
                $date,
                $hour
            );

            if ($priceModel) {

                $hourlyRate = $booking->is_member_price && $priceModel->member_price_per_hour ?
                    $priceModel->member_price_per_hour :
                    $priceModel->price_per_hour;


                \Illuminate\Support\Facades\Log::info('Tính phí chênh lệch: ', [
                    'booking_id' => $booking->id,
                    'branch_id' => $booking->branch_id,
                    'court_type' => $court->court_type,
                    'checkout_time' => $hour,
                    'date' => $date,
                    'price_model' => $priceModel->toArray(),
                    'is_member' => $booking->is_member_price,
                    'hourly_rate' => $hourlyRate,
                    'overtime_minutes' => $booking->overtime_minutes
                ]);


                return $booking->calculateOvertimeFee($hourlyRate);
            }


            if ($court->price_per_hour > 0) {
                $hourlyRate = $booking->is_member_price ?
                    ($court->price_per_hour * 0.85) :
                    $court->price_per_hour;

                \Illuminate\Support\Facades\Log::info('Sử dụng giá mặc định của sân: ', [
                    'booking_id' => $booking->id,
                    'court_id' => $court->id,
                    'price_per_hour' => $court->price_per_hour,
                    'is_member' => $booking->is_member_price,
                    'hourly_rate' => $hourlyRate
                ]);

                return $booking->calculateOvertimeFee($hourlyRate);
            }

            \Illuminate\Support\Facades\Log::warning('Không tìm thấy giá sân cho booking ID: ' . $booking->id);
            return false;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi tính phí chênh lệch: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Hoàn thành toàn bộ đơn đặt sân theo reference_number
     * 
     * @param Request $request
     * @param string $referenceNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function completeBooking(Request $request, $referenceNumber)
    {

        $bookings = CourtBooking::where('reference_number', $referenceNumber)->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy đơn đặt sân với mã tham chiếu này'
            ], 404);
        }


        $notCheckedOut = $bookings->where('checkin_status', '!=', 'checked_out')->count();

        if ($notCheckedOut > 0 && !$request->input('force_complete', false)) {
            return response()->json([
                'success' => false,
                'message' => "Có {$notCheckedOut} sân chưa được check-out. Không thể hoàn thành đơn.",
                'require_confirmation' => true
            ], 400);
        }


        $additionalFees = 0;
        $additionalDetails = [];

        foreach ($bookings as $booking) {

            if ($booking->checkin_status !== 'checked_out' && $booking->checkin_status === 'checked_in') {
                $booking->checkOut(Auth::id());
            }


            if ($booking->checkin_time && $booking->checkout_time) {

                $bookedStartDateTime = null;


                if (strlen($booking->start_time) > 8) {

                    $bookedStartDateTime = Carbon::parse($booking->start_time);
                } else {

                    $bookedStartTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->start_time;
                    $bookedStartDateTime = Carbon::parse($bookedStartTime);
                }


                $bookedEndDateTime = null;


                if (strlen($booking->end_time) > 8) {

                    $bookedEndDateTime = Carbon::parse($booking->end_time);
                } else {

                    $bookedEndTime = $booking->booking_date->format('Y-m-d') . ' ' . $booking->end_time;
                    $bookedEndDateTime = Carbon::parse($bookedEndTime);
                }

                $bookedDuration = $bookedEndDateTime->diffInMinutes($bookedStartDateTime);

                $actualStartTime = $booking->checkin_time;
                $actualEndTime = $booking->checkout_time;
                $actualDuration = $actualEndTime->diffInMinutes($actualStartTime);


                if ($actualDuration > $bookedDuration) {
                    $extraMinutes = $actualDuration - $bookedDuration;


                    $court = $booking->court;
                    $hourlyRate = $court->price_per_hour;
                    $extraFee = ($hourlyRate / 60) * $extraMinutes;

                    $additionalFees += $extraFee;
                    $additionalDetails[] = [
                        'court_id' => $booking->court_id,
                        'court_name' => $court->name,
                        'extra_minutes' => $extraMinutes,
                        'extra_fee' => $extraFee
                    ];
                }
            }


            $booking->status = 'completed';
            $booking->save();
        }


        if ($additionalFees > 0) {
            $firstBooking = $bookings->first();
            $metadata = $firstBooking->metadata ?? [];
            $metadata['completion'] = [
                'completed_at' => now()->toDateTimeString(),
                'staff_id' => Auth::id(),
                'staff_name' => Auth::user()->name,
                'additional_fees' => $additionalFees,
                'additional_details' => $additionalDetails
            ];
            $firstBooking->metadata = $metadata;
            $firstBooking->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã hoàn thành toàn bộ đơn đặt sân',
            'additional_fees' => $additionalFees > 0 ? $additionalFees : null,
            'additional_details' => $additionalFees > 0 ? $additionalDetails : null
        ]);
    }

    /**
     * Get confirmed bookings for a branch on a specific date
     *
     * @param Branch $branch
     * @param string $date
     * @return \Illuminate\Support\Collection
     */
    private function getConfirmedBookings($branch, $date)
    {
        $bookings = CourtBooking::with(['court', 'customer', 'branch', 'payment'])
            ->where('branch_id', $branch->id)
            ->whereIn('status', ['confirmed', 'pending'])
            ->whereNull('checkin_time')
            ->whereDate('booking_date', $date)
            ->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get checked-in bookings for a branch on a specific date
     *
     * @param Branch $branch
     * @param string $date
     * @return \Illuminate\Support\Collection
     */
    private function getCheckedInBookings($branch, $date)
    {
        $bookings = CourtBooking::with(['court', 'customer', 'branch'])
            ->where('branch_id', $branch->id)
            ->where('checkin_status', 'checked_in')
            ->whereNotNull('checkin_time')
            ->whereNull('checkout_time')
            ->whereDate('booking_date', $date)
            ->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get bookings that have been checked out but not completed
     *
     * @param Branch $branch
     * @param string $date
     * @return \Illuminate\Support\Collection
     */
    private function getNotCompletedBookings($branch, $date)
    {
        $bookings = CourtBooking::with(['court', 'customer', 'branch'])
            ->where('branch_id', $branch->id)
            ->where('checkin_status', 'checked_out')
            ->where('status', '!=', 'completed')
            ->whereNotNull('checkout_time')
            ->whereDate('booking_date', $date)
            ->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Get completed bookings for a branch on a specific date
     *
     * @param Branch $branch
     * @param string $date
     * @return \Illuminate\Support\Collection
     */
    private function getCompletedBookings($branch, $date)
    {
        $bookings = CourtBooking::with(['court', 'customer', 'branch'])
            ->where('branch_id', $branch->id)
            ->where('status', 'completed')
            ->whereDate('booking_date', $date)
            ->get();

        return $bookings->map(function ($booking) {
            return $this->formatBooking($booking);
        });
    }

    /**
     * Format booking data for frontend display
     *
     * @param CourtBooking $booking
     * @return array
     */
    private function formatBooking($booking)
    {
        $now = Carbon::now();
        $duration = null;
        $durationSoFar = null;
        $overtime = null;

        if ($booking->checkin_time && $booking->checkout_time) {
            $checkinTime = Carbon::parse($booking->checkin_time);
            $checkoutTime = Carbon::parse($booking->checkout_time);
            $diffInMinutes = $checkinTime->diffInMinutes($checkoutTime);
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            $duration = [
                'minutes' => $diffInMinutes,
                'text' => $hours . ' giờ ' . $minutes . ' phút'
            ];


            if ($booking->overtime_minutes > 0) {
                $overtimeHours = floor($booking->overtime_minutes / 60);
                $overtimeMinutes = $booking->overtime_minutes % 60;
                $overtime = [
                    'minutes' => $booking->overtime_minutes,
                    'text' => $overtimeHours > 0
                        ? $overtimeHours . ' giờ ' . $overtimeMinutes . ' phút'
                        : $overtimeMinutes . ' phút',
                    'fee' => $booking->overtime_fee,
                    'fee_paid' => $booking->overtime_fee_paid
                ];
            }
        }


        if ($booking->checkin_time && !$booking->checkout_time) {
            $checkinTime = Carbon::parse($booking->checkin_time);
            $diffInMinutes = $checkinTime->diffInMinutes($now);
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            $durationSoFar = [
                'minutes' => $diffInMinutes,
                'text' => $hours . ' giờ ' . $minutes . ' phút'
            ];


            $endTimeDate = $booking->booking_date->format('Y-m-d');
            $endTime = Carbon::parse($endTimeDate . ' ' . $booking->end_time->format('H:i'));

            if ($now->gt($endTime)) {
                $overtimeMinutes = $now->diffInMinutes($endTime);
                $overtimeHours = floor($overtimeMinutes / 60);
                $overtimeMinutesRem = $overtimeMinutes % 60;
                $overtime = [
                    'minutes' => $overtimeMinutes,
                    'text' => $overtimeHours > 0
                        ? $overtimeHours . ' giờ ' . $overtimeMinutesRem . ' phút'
                        : $overtimeMinutesRem . ' phút',
                    'fee' => null,
                    'fee_paid' => false,
                    'is_currently_overtime' => true
                ];
            }
        }


        $customerName = 'N/A';
        $customerPhone = 'N/A';

        if ($booking->customer_id) {

            $customer = \App\Models\Customer::find($booking->customer_id);
            if ($customer) {
                $customerName = $customer->name;
                $customerPhone = $customer->phone;
            }
        } else {

            $customerName = $booking->customer_name ?? 'N/A';
            $customerPhone = $booking->customer_phone ?? 'N/A';
        }


        $paymentStatus = 'unpaid';
        if ($booking->payment) {
            $paymentStatus = $booking->payment->status;
        } else {
            $payment = \App\Models\Payment::where('booking_reference', $booking->reference_number)->first();
            if ($payment) {
                $paymentStatus = $payment->status;
            }
        }


        return [
            'id' => $booking->id,
            'reference_number' => $booking->reference_number,
            'court_id' => $booking->court_id,
            'court_name' => $booking->court ? $booking->court->name : 'N/A',
            'customer_id' => $booking->customer_id,
            'customer_name' => $customerName,
            'customer_phone' => $customerPhone,
            'branch_id' => $booking->branch_id,
            'booking_type' => $booking->booking_type,
            'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
            'booking_date' => $booking->booking_date,
            'booking_date_formatted' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
            'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
            'end_time' => Carbon::parse($booking->end_time)->format('H:i'),
            'duration' => $booking->duration,
            'number_of_players' => $booking->number_of_players,
            'price_per_hour' => $booking->price_per_hour,
            'total_price' => $booking->total_price,
            'deposit_amount' => $booking->deposit_amount,
            'overtime_fee' => $booking->overtime_fee,
            'is_member_price' => $booking->is_member_price,
            'status' => $booking->status,
            'payment_status' => $paymentStatus,
            'notes' => $booking->notes,
            'checkin_status' => $booking->checkin_status,
            'checkin_time' => $booking->checkin_time ? Carbon::parse($booking->checkin_time)->format('H:i d/m/Y') : null,
            'checkout_time' => $booking->checkout_time ? Carbon::parse($booking->checkout_time)->format('H:i d/m/Y') : null,
            'duration' => $duration,
            'overtime_fee_paid' => $booking->overtime_fee_paid,
            'duration_so_far' => $durationSoFar,
            'overtime' => $overtime,
            'created_at' => $booking->created_at,
            'updated_at' => $booking->updated_at
        ];
    }


}
