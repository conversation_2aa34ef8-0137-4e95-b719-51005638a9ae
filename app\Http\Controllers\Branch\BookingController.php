<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\CourtService;
use App\Models\CourtPrice;
use App\Models\Payment;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use App\Services\MailService;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings for the branch.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::findOrFail($branchId);


        $queryBuilder = CourtBooking::select('reference_number')
            ->where('branch_id', $branchId)
            ->groupBy('reference_number');


        if ($request->has('status') && !empty($request->status)) {
            $queryBuilder->where('status', $request->status);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $queryBuilder->where('booking_date', '>=', Carbon::parse($request->date_from)->startOfDay());
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $queryBuilder->where('booking_date', '<=', Carbon::parse($request->date_to)->endOfDay());
        }

        if ($request->has('court_id') && !empty($request->court_id)) {
            $queryBuilder->where('court_id', $request->court_id);
        }

        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $queryBuilder->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }


        $sortField = $request->input('sort', 'booking_date');
        $sortDirection = $request->input('direction', 'desc');
        $queryBuilder->orderBy($sortField, $sortDirection);


        $paginatedRefNumbers = $queryBuilder->paginate(10);
        $referenceNumbers = $paginatedRefNumbers->pluck('reference_number')->toArray();


        $bookingsData = CourtBooking::with(['court', 'user', 'customer'])
            ->whereIn('reference_number', $referenceNumbers)
            ->orderBy($sortField, $sortDirection)
            ->get()
            ->groupBy('reference_number');


        $payments = Payment::whereIn('booking_reference', $referenceNumbers)
            ->with('paymentMethod')
            ->get();


        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $proofUrl = asset('storage/' . $details['proof_file']);
                    $payment->proof_url = $proofUrl;
                    $payment->has_proof = true;
                }
            }
        }

        $paymentsByReference = $payments->groupBy('booking_reference');


        $combinedBookings = collect();
        foreach ($referenceNumbers as $refNumber) {
            if (isset($bookingsData[$refNumber])) {
                $bookingGroup = $bookingsData[$refNumber];


                $primaryBooking = $bookingGroup->first();


                $totalGroupPrice = $bookingGroup->sum('total_price');


                $bookingPayments = isset($paymentsByReference[$refNumber]) ? $paymentsByReference[$refNumber] : collect();


                $paymentStatus = 'pending';
                $paidAmount = $bookingPayments->sum('amount');

                if ($paidAmount >= $totalGroupPrice) {
                    $paymentStatus = 'completed';
                } elseif ($paidAmount > 0) {
                    $paymentStatus = 'partial';
                }


                $combinedBooking = [
                    'id' => $primaryBooking->id,
                    'reference_number' => $refNumber,
                    'booking_date' => $primaryBooking->booking_date,
                    'customer_name' => $primaryBooking->customer_name,
                    'customer_phone' => $primaryBooking->customer_phone,
                    'customer_email' => $primaryBooking->customer_email,
                    'status' => $primaryBooking->status,
                    'created_at' => $primaryBooking->created_at,
                    'updated_at' => $primaryBooking->updated_at,
                    'total_price' => $totalGroupPrice,
                    'payment_status' => $paymentStatus,
                    'paid_amount' => $paidAmount,
                    'customer' => $primaryBooking->customer,
                    'user' => $primaryBooking->user,
                    'booking_count' => $bookingGroup->count(),
                    'courts' => $bookingGroup->pluck('court.name')->unique()->implode(', '),
                    'court_ids' => $bookingGroup->pluck('court_id')->unique()->toArray(),
                    'start_time' => $bookingGroup->min('start_time'),
                    'end_time' => $bookingGroup->max('end_time'),
                    'is_member_price' => $primaryBooking->is_member_price,
                    'bookings' => $bookingGroup,
                    'payments' => $bookingPayments,
                    'payment_summary' => [
                        'total_paid' => $paidAmount,
                        'payment_count' => $bookingPayments->count(),
                        'has_bank_transfer' => $bookingPayments->contains('payment_method_id', 2),
                        'has_proof' => $bookingPayments->contains('has_proof', true),
                        'payment_methods' => $bookingPayments->pluck('payment_method')->unique()->implode(', '),
                        'latest_payment' => $bookingPayments->sortByDesc('created_at')->first(),
                    ]
                ];

                $combinedBookings->push($combinedBooking);
            }
        }


        $bookings = new \Illuminate\Pagination\LengthAwarePaginator(
            $combinedBookings,
            $paginatedRefNumbers->total(),
            $paginatedRefNumbers->perPage(),
            $paginatedRefNumbers->currentPage(),
            ['path' => $request->url(), 'query' => $request->query()]
        );

        return Inertia::render('Branchs/Booking/List', [
            'bookings' => $bookings,
            'branch' => $branch,
            'courts' => Court::where('branch_id', $branchId)->get(['id', 'name']),
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
                'date_from' => $request->date_from ?? '',
                'date_to' => $request->date_to ?? '',
                'court_id' => $request->court_id ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'statuses' => [
                'pending' => 'Đang chờ',
                'confirmed' => 'Đã xác nhận',
                'cancelled' => 'Đã hủy',
                'completed' => 'Đã hoàn thành',
            ],
        ]);
    }

    /**
     * Show the form for creating a new booking.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function create(Request $request)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);
        $services = CourtService::where('business_id', $branch->business_id)
            ->where('is_active', true)
            ->get();

        return Inertia::render('Branchs/Booking/Create', [
            'branch' => $branch,
            'courts' => $branch->courts,
            'services' => $services,
        ]);
    }

    /**
     * Store a newly created booking in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'services' => 'nullable|array',
            'services.*.id' => 'exists:court_services,id',
            'services.*.quantity' => 'integer|min:1',
            'services.*.price' => 'numeric|min:0',
            'booking_courts' => 'required|array',
            'booking_courts.*.court_id' => 'required|exists:courts,id',
            'booking_courts.*.booking_slot' => 'required|array',
            'booking_courts.*.start_time' => 'required|date_format:H:i',
            'booking_courts.*.end_time' => 'required|date_format:H:i|after:booking_courts.*.start_time',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            return DB::transaction(function () use ($request, $branchId, $user) {
                $data = $request->all();
                $createdBookings = [];

                $result = CourtBooking::processBookings(
                    $data,
                    $user,
                    'offline',
                    $createdBookings
                );

                if (!$result['success']) {
                    return redirect()->back()
                        ->with('flash.error', $result['message'])
                        ->withInput();
                }


                $firstBooking = null;
                if (!empty($createdBookings)) {
                    $firstBooking = CourtBooking::find($createdBookings[0]['id']);


                    \App\Services\NotificationService::createBookingNotification(
                        $branchId,
                        $result['reference_number'],
                        $data['customer_name'],
                        $data['customer_phone'],
                        $result['bookings']
                    );
                }


                if (!empty($data['customer_email'])) {
                    try {
                        $this->sendBookingConfirmationEmail($result['reference_number'], $data['customer_email'], $data, $firstBooking);

                        Log::info('Booking confirmation email sent to: ' . $data['customer_email'], [
                            'reference_number' => $result['reference_number']
                        ]);
                    } catch (\Exception $e) {

                        Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                            'exception' => $e,
                            'email' => $data['customer_email'],
                            'reference_number' => $result['reference_number']
                        ]);
                    }
                }

                return redirect()->route('branch.booking.index')
                    ->with('flash.success', 'Đặt sân thành công với mã tham chiếu: ' . $result['reference_number']);
            });
        } catch (\Exception $e) {
            Log::error('Booking error: ' . $e->getMessage(), ['exception' => $e]);

            return redirect()->back()
                ->with('flash.error', 'Đã xảy ra lỗi khi đặt sân: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified booking.
     *
     * @param Request $request
     * @param int $id
     * @return \Inertia\Response
     */
    public function show(Request $request, $reference_number)
    {
        try {
            $user = $request->user();
            $branchId = $this->getBranchIdForUser($user);

            if (!$branchId) {
                return $this->unauthorizedResponse();
            }
            $bookings = CourtBooking::with(['court', 'user'])
                ->where('reference_number', $reference_number)
                ->get();

            if ($bookings->isEmpty()) {
                abort(404, 'Không tìm thấy đơn đặt sân');
            }

            $booking = $bookings->first();

            $branch = Branch::findOrFail($branchId);

            $payments = Payment::where('booking_reference', $reference_number)
                ->with(['paymentMethod', 'user'])
                ->get();

            foreach ($payments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            $totalPrice = $bookings->sum('total_price');

            $paymentSummary = [
                'total_paid' => $payments->sum('amount'),
                'payment_count' => $payments->count(),
                'has_bank_transfer' => $payments->contains('payment_method_id', 2),
                'has_proof' => $payments->contains('has_proof', true),
                'payment_methods' => $payments->pluck('payment_method')->unique()->implode(', '),
                'latest_payment' => $payments->sortByDesc('created_at')->first(),
                'payment_status' => $payments->sum('amount') >= $totalPrice ? 'completed' :
                    ($payments->sum('amount') > 0 ? 'partial' : 'pending'),
            ];

            $booking->payments = $payments;
            $booking->payment_summary = $paymentSummary;
            $booking->total_group_price = $totalPrice;

            
            \Illuminate\Support\Facades\Log::info('Booking data for reference: ' . $reference_number, [
                'booking_id' => $booking->id,
                'booking_count' => $bookings->count(),
                'payment_count' => $payments->count(),
                'branch_id' => $branch->id
            ]);

            return Inertia::render('Branchs/Booking/Show', [
                'booking' => $booking,
                'bookings' => $bookings,
                'branch' => $branch,
                'payments' => $payments,
            ]);
        } catch (\Exception $e) {
            
            \Illuminate\Support\Facades\Log::error('Error in BookingController@show: ' . $e->getMessage(), [
                'reference_number' => $reference_number,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            
            return response()->json([
                'error' => 'Đã xảy ra lỗi khi hiển thị chi tiết đặt sân.',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified booking.
     *
     * @param Request $request
     * @param int $id
     * @return \Inertia\Response
     */
    public function edit(Request $request, $referenceNumber)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);
        if (!$branchId) {
            return $this->unauthorizedResponse();
        }
        $bookings = CourtBooking::with(['court', 'user'])
            ->where('branch_id', $branchId)
            ->where('reference_number', $referenceNumber)
            ->get();

        if ($bookings->isEmpty()) {
            abort(404, 'Không tìm thấy đơn đặt sân');
        }
        $booking = $bookings->first();
        $payments = Payment::where('booking_reference', $referenceNumber)
            ->with(['paymentMethod', 'user'])
            ->get();
        $totalPrice = $bookings->sum('total_price');
        $paymentSummary = [
            'total_paid' => $payments->sum('amount'),
            'payment_count' => $payments->count(),
            'payment_status' => $payments->sum('amount') >= $totalPrice ? 'completed' :
                ($payments->sum('amount') > 0 ? 'partial' : 'pending'),
        ];

        $booking->payments = $payments;
        $booking->payment_summary = $paymentSummary;
        $booking->total_group_price = $totalPrice;


        $formattedBookings = $bookings->map(function ($courtBooking) {
            return [
                'id' => $courtBooking->id,
                'court_id' => $courtBooking->court_id,
                'court_name' => $courtBooking->court->name,
                'court_type' => $courtBooking->court->type,
                'booking_date' => $courtBooking->booking_date->format('Y-m-d'),
                'start_time' => $courtBooking->start_time->format('H:i'),
                'end_time' => $courtBooking->end_time->format('H:i'),
                'total_price' => $courtBooking->total_price,
                'status' => $courtBooking->status,
                'number_of_players' => $courtBooking->number_of_players,
                'notes' => $courtBooking->notes,
                'reference_number' => $courtBooking->reference_number,
                'metadata' => $courtBooking->metadata,
            ];
        });


        $booking->formatted_bookings = $formattedBookings;


        $groupedBookings = $formattedBookings->groupBy('booking_date')
            ->map(function ($dateGroup) {
                return $dateGroup->groupBy('court_id');
            });

        $booking->grouped_bookings = $groupedBookings;

        $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);

        return Inertia::render('Branchs/Booking/Edit', [
            'booking' => $booking,
            'bookings' => $bookings,
            'formatted_bookings' => $formattedBookings,
            'grouped_bookings' => $groupedBookings,
            'branch' => $branch,
            'courts' => $branch->courts,
            'payments' => $payments,
        ]);
    }

    /**
     * Update the specified booking in storage.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $reference_number)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }


        $primaryBooking = CourtBooking::where('branch_id', $branchId)
            ->where('reference_number', $reference_number)
            ->first();

        if (!$primaryBooking) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }

        if (in_array($primaryBooking->status, ['completed', 'cancelled'])) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot update a booking that is already completed or cancelled.'
            ]);
        }


        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,confirmed,cancelled,completed',
            'booking_courts' => 'required|array',
            'booking_courts.*.court_id' => 'required|exists:courts,id',
            'booking_courts.*.booking_date' => 'required|date',
            'booking_courts.*.start_time' => 'required|date_format:H:i',
            'booking_courts.*.end_time' => 'required|date_format:H:i|after:booking_courts.*.start_time',
            'booking_courts.*.time_slots' => 'required|array',
            'booking_courts.*.total_price' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();


            $existingBookings = CourtBooking::where('reference_number', $reference_number)->get();
            $existingBookingIds = $existingBookings->pluck('id')->toArray();
            $processedBookingIds = [];


            foreach ($request->booking_courts as $courtBookingData) {

                if (isset($courtBookingData['is_modified']) && $courtBookingData['is_modified'] === false && !empty($courtBookingData['id'])) {
                    $processedBookingIds[] = $courtBookingData['id'];
                    continue;
                }


                if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified'] === true) {
                    $conflictingBookings = CourtBooking::where('court_id', $courtBookingData['court_id'])
                        ->where('booking_date', $courtBookingData['booking_date'])
                        ->where(function ($query) use ($courtBookingData) {
                            $query->where(function ($q) use ($courtBookingData) {
                                $q->where('start_time', '<', $courtBookingData['end_time'])
                                    ->where('end_time', '>', $courtBookingData['start_time']);
                            });
                        });


                    if (!empty($courtBookingData['id'])) {
                        $conflictingBookings->where('id', '!=', $courtBookingData['id']);
                    }

                    $conflictingBookings = $conflictingBookings->whereIn('status', ['pending', 'confirmed'])->count();

                    if ($conflictingBookings > 0) {
                        DB::rollBack();
                        return redirect()->back()->withErrors([
                            'time_conflict' => 'The selected time slot for ' . $courtBookingData['court_name'] . ' is no longer available. Please choose a different time.'
                        ])->withInput();
                    }
                }


                $bookingModel = null;
                if (!empty($courtBookingData['id'])) {

                    $bookingModel = CourtBooking::find($courtBookingData['id']);
                    if (!$bookingModel || $bookingModel->reference_number !== $reference_number) {
                        DB::rollBack();
                        return redirect()->back()->withErrors([
                            'error' => 'Invalid booking ID provided.'
                        ])->withInput();
                    }
                    $processedBookingIds[] = $bookingModel->id;
                } else {

                    $bookingModel = new CourtBooking();
                    $bookingModel->reference_number = $reference_number;
                    $bookingModel->branch_id = $branchId;
                    $bookingModel->user_id = $primaryBooking->user_id;
                    $bookingModel->customer_id = $primaryBooking->customer_id;
                    $bookingModel->booking_type = $primaryBooking->booking_type;
                    $bookingModel->status = $request->status;
                }


                $bookingModel->customer_name = $request->customer_name;
                $bookingModel->customer_phone = $request->customer_phone;
                $bookingModel->customer_email = $request->customer_email;
                $bookingModel->notes = $request->notes;
                $bookingModel->number_of_players = $request->number_of_players ?? $primaryBooking->number_of_players;
                $bookingModel->status = $request->status;


                if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified']) {
                    $bookingModel->court_id = $courtBookingData['court_id'];
                    $bookingModel->booking_date = $courtBookingData['booking_date'];
                    $bookingModel->start_time = $courtBookingData['start_time'];
                    $bookingModel->end_time = $courtBookingData['end_time'];
                }


                $useCurrentPrices = $request->use_current_prices ?? false;
                $isMemberPrice = $request->is_member_price ?? $primaryBooking->is_member_price;
                $bookingModel->is_member_price = $isMemberPrice;


                if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified'] || $useCurrentPrices) {

                    if ($useCurrentPrices) {

                        $court = Court::findOrFail($courtBookingData['court_id']);
                        $courtType = $court->type;
                        $totalPrice = 0;

                        foreach ($courtBookingData['time_slots'] as $slot) {
                            $slotTime = Carbon::parse($slot);
                            $slotHour = $slotTime->hour;


                            $price = CourtPrice::where('branch_id', $branchId)
                                ->where('court_type', $courtType)
                                ->where(function ($query) use ($slotHour) {
                                    $query->whereRaw("CAST(SUBSTRING_INDEX(start_time, ':', 1) AS UNSIGNED) <= ?", [$slotHour])
                                        ->whereRaw("CAST(SUBSTRING_INDEX(end_time, ':', 1) AS UNSIGNED) > ?", [$slotHour]);
                                })
                                ->first();

                            if ($price) {

                                $hourlyRate = $isMemberPrice && $price->member_price_per_hour
                                    ? $price->member_price_per_hour
                                    : $price->price_per_hour;


                                $slotPrice = $hourlyRate * (30 / 60);
                                $totalPrice += $slotPrice;
                            } else {

                                $hourlyRate = $court->price_per_hour;
                                if ($isMemberPrice) {
                                    $hourlyRate *= 0.85;
                                }
                                $slotPrice = $hourlyRate * (30 / 60);
                                $totalPrice += $slotPrice;
                            }
                        }

                        $bookingModel->total_price = $totalPrice;
                    } else if (isset($courtBookingData['price_details']) && isset($courtBookingData['price_details']['price_per_hour'])) {

                        $hourlyRate = $courtBookingData['price_details']['price_per_hour'];
                        $totalSlots = count($courtBookingData['time_slots']);
                        $totalHours = $totalSlots * (30 / 60);
                        $bookingModel->total_price = $hourlyRate * $totalHours;
                    } else {

                        $bookingModel->total_price = $courtBookingData['total_price'];
                    }
                }


                $metadata = $bookingModel->metadata ?? [];


                if (!isset($courtBookingData['is_modified']) || $courtBookingData['is_modified']) {
                    $metadata['last_updated_by'] = 'branch_staff';
                    $metadata['staff_id'] = $user->id;
                    $metadata['staff_name'] = $user->name;
                    $metadata['updated_at'] = now()->toDateTimeString();
                    $metadata['court_name'] = $courtBookingData['court_name'];
                    $metadata['court_price'] = $bookingModel->total_price;
                    $metadata['time_slots'] = $courtBookingData['time_slots'];


                    if (isset($courtBookingData['price_details'])) {
                        $metadata['price_details'] = $courtBookingData['price_details'];
                    }
                }

                $bookingModel->metadata = $metadata;
                $bookingModel->save();
            }


            $bookingsToDelete = array_diff($existingBookingIds, $processedBookingIds);
            if (!empty($bookingsToDelete)) {
                CourtBooking::whereIn('id', $bookingsToDelete)->delete();
            }

            DB::commit();

            return redirect()->route('branch.booking.show', $reference_number)
                ->with('flash.success', 'Booking updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update booking: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to update booking. ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Cancel the specified booking.
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $reference_number)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }


        $bookings = CourtBooking::where('branch_id', $branchId)
            ->where('reference_number', $reference_number)
            ->get();

        if ($bookings->isEmpty()) {
            return redirect()->back()->withErrors([
                'error' => 'Booking not found.'
            ]);
        }


        $invalidBookings = $bookings->filter(function ($booking) {
            return in_array($booking->status, ['completed', 'cancelled']);
        });

        if ($invalidBookings->isNotEmpty()) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot cancel bookings that are already completed or cancelled.'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            foreach ($bookings as $booking) {
                $booking->status = 'cancelled';
                $booking->cancelled_at = now();
                $booking->cancellation_reason = $request->cancellation_reason;

                $metadata = $booking->metadata ?? [];
                $metadata['cancelled_by'] = 'branch_staff';
                $metadata['staff_id'] = $user->id;
                $metadata['staff_name'] = $user->name;
                $metadata['cancelled_at'] = now()->toDateTimeString();
                $booking->metadata = $metadata;

                $booking->save();
            }

            DB::commit();

            $count = $bookings->count();
            $message = $count > 1
                ? "{$count} bookings cancelled successfully."
                : "Booking cancelled successfully.";

            return redirect()->route('branch.booking.index')
                ->with('flash.success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel bookings: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to cancel bookings. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified booking from storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $booking = CourtBooking::where('branch_id', $branchId)->findOrFail($id);

        try {
            DB::beginTransaction();

            $booking->delete();

            DB::commit();

            return redirect()->route('branch.booking.index')
                ->with('flash.success', 'Booking deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete booking: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return redirect()->back()->withErrors([
                'error' => 'Failed to delete booking. ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get branch ID for the authenticated user.
     *
     * @param \App\Models\User $user
     * @return int|null
     */
    private function getBranchIdForUser($user)
    {
        if (!$user) {
            return null;
        }

        if ($user->branch_id) {
            return $user->branch_id;
        }

        if ($user && $user->branch_id) {
            return $user->branch_id;
        }

        return null;
    }

    /**
     * Return unauthorized response.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    private function unauthorizedResponse()
    {
        session([
            'unauthorized_role' => 'trang quản lý chi nhánh',
            'unauthorized_message' => 'Bạn không có quyền truy cập vào khu vực này.',
        ]);

        return redirect()->route('unauthorized');
    }

    /**
     * Display the court booking schedule.
     *
     * @return \Inertia\Response
     */
    public function schedule(Request $request)
    {
        $user = $request->user();
        $branchId = $this->getBranchIdForUser($user);

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }


        $branch = Branch::with([
            'prices' => function ($query) {
                $query->where('is_active', true)
                    ->where('status', 'active')
                    ->orderBy('court_type')
                    ->orderBy('start_time');
            }
        ])->findOrFail($branchId);


        $courts = Court::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->orderBy('name')
            ->get(['id', 'name', 'type', 'description']);


        $courtTypes = $courts->pluck('type')->unique()->values()->map(function ($type) use ($branch) {
            $basePrice = $branch->prices
                ->where('court_type', $type)
                ->where('price_type', 'normal')
                ->first();

            return [
                'type' => $type,
                'base_price' => $basePrice ? $basePrice->price_per_hour : null,
                'formatted_price' => $basePrice ? number_format($basePrice->price_per_hour, 0, ',', '.') . '₫' : null,
            ];
        });

        return Inertia::render('Branchs/Booking/Schedule', [
            'branch' => [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->address,
                'contact_phone' => $branch->contact_phone,
                'contact_email' => $branch->contact_email,
                'opening_hour' => $branch->opening_hour ?? '06:00',
                'closing_hour' => $branch->closing_hour ?? '22:00',
                'logo' => $branch->logo,
                'banner' => $branch->banner,
                'main_image_url' => $branch->main_image_url,
            ],
            'courts' => $courts,
            'courtTypes' => $courtTypes,
            'today' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    /**
     * API endpoint to get courts and bookings for the schedule view
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getScheduleData(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'date' => 'required|date',
                'court_id' => 'nullable|string',
                'duration' => 'nullable|integer|min:15|max:120',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = $request->user();
            $branchId = $this->getBranchIdForUser($user);

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to access this branch',
                ], 403);
            }

            $date = $request->date;
            $courtId = $request->court_id;
            $duration = (int) $request->input('duration', 30);
            $useMemberPrice = $request->boolean('use_member_price', false);

            $branch = Branch::with('prices')->findOrFail($branchId);

            $courts = Court::getCourtsForSchedule($branchId, $date, $duration, $useMemberPrice);

            if ($courtId && $courtId !== 'all') {
                $courts = $courts->filter(function ($court) use ($courtId) {
                    return $court['id'] == $courtId;
                })->values();
            }

            $bookings = CourtBooking::getBookingsForSchedule($date, $branchId, $courtId);

            $pricingInfo = CourtPrice::where('branch_id', $branchId)
                ->where(function ($query) use ($date) {
                    $query->where('price_type', 'normal')
                        ->orWhere(function ($q) use ($date) {
                            $q->where('price_type', 'special_date')
                                ->where('special_date', $date);
                        });
                })
                ->where('is_active', true)
                ->get()
                ->map(function ($price) use ($useMemberPrice) {
                    return [
                        'id' => $price->id,
                        'court_type' => $price->court_type,
                        'price_type' => $price->price_type,
                        'start_time' => Carbon::parse($price->start_time)->format('H:i'),
                        'end_time' => Carbon::parse($price->end_time)->format('H:i'),
                        'price_per_hour' => $useMemberPrice ? (float) $price->member_price_per_hour : (float) $price->price_per_hour,
                        'member_price_per_hour' => (float) $price->member_price_per_hour,
                        'is_member_price' => $useMemberPrice,
                        'special_date' => $price->special_date,
                        'description' => $price->description ?? '',
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'branch' => [
                        'id' => $branch->id,
                        'name' => $branch->name,
                        'opening_hour' => $branch->opening_hour ?? '06:00',
                        'closing_hour' => $branch->closing_hour ?? '22:00',
                    ],
                    'courts' => $courts,
                    'bookings' => $bookings,
                    'date' => $date,
                    'pricing' => $pricingInfo,
                    'duration' => $duration,
                    'use_member_price' => $useMemberPrice,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching schedule data: ' . $e->getMessage(), ['exception' => $e]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching schedule data',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Display a listing of bookings for the branch in a DataTable format.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function list(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with('business')->findOrFail($branchId);
        $businessId = $branch->business_id;


        $queryBuilder = CourtBooking::select('reference_number')
            ->where('branch_id', $branchId)
            ->groupBy('reference_number');


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $queryBuilder->where(function ($query) use ($search) {
                $query->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }


        if ($request->has('status') && !empty($request->status)) {
            $queryBuilder->where('status', $request->status);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $queryBuilder->where('booking_date', '>=', Carbon::parse($request->date_from)->startOfDay());
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $queryBuilder->where('booking_date', '<=', Carbon::parse($request->date_to)->endOfDay());
        }

        if ($request->has('court_id') && !empty($request->court_id)) {
            $queryBuilder->where('court_id', $request->court_id);
        }


        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $queryBuilder->orderBy($sortField, $sortDirection);


        $paginatedRefNumbers = $queryBuilder->paginate(10);


        $referenceNumbers = $paginatedRefNumbers->pluck('reference_number')->toArray();


        $bookingsData = CourtBooking::with(['court', 'user', 'customer'])
            ->whereIn('reference_number', $referenceNumbers)
            ->orderBy($sortField, $sortDirection)
            ->get()
            ->groupBy('reference_number');


        $payments = Payment::whereIn('booking_reference', $referenceNumbers)
            ->with('paymentMethod')
            ->get();


        $bankSettings = null;
        if ($businessId) {
            $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);
        }


        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {

                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {

                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $paymentsByReference = $payments->groupBy('booking_reference');


        $combinedBookings = collect();
        foreach ($referenceNumbers as $refNumber) {
            if (isset($bookingsData[$refNumber])) {
                $bookingGroup = $bookingsData[$refNumber];


                $primaryBooking = $bookingGroup->first();


                $totalGroupPrice = $bookingGroup->sum('total_price');


                $bookingPayments = isset($paymentsByReference[$refNumber]) ? $paymentsByReference[$refNumber] : collect();
                $paymentStatus = 'pending';
                $paidAmount = $bookingPayments->sum('amount');

                if ($paidAmount >= $totalGroupPrice) {
                    $paymentStatus = 'completed';
                } elseif ($paidAmount > 0) {
                    $paymentStatus = 'partial';
                }


                $hasBankTransfer = $bookingPayments->contains('payment_method_id', 2);
                $hasProof = $bookingPayments->contains('has_proof', true);
                $latestPayment = $bookingPayments->sortByDesc('created_at')->first();
                $paymentMethods = $bookingPayments->pluck('payment_method')->unique()->implode(', ');


                $combinedBooking = [
                    'id' => $primaryBooking->id,
                    'reference_number' => $refNumber,
                    'booking_date' => $primaryBooking->booking_date,
                    'customer_name' => $primaryBooking->customer_name,
                    'customer_phone' => $primaryBooking->customer_phone,
                    'status' => $primaryBooking->status,
                    'created_at' => $primaryBooking->created_at,
                    'updated_at' => $primaryBooking->updated_at,
                    'total_price' => $totalGroupPrice,
                    'payment_status' => $paymentStatus,
                    'paid_amount' => $paidAmount,
                    'customer' => $primaryBooking->customer,
                    'court' => $primaryBooking->court,
                    'user' => $primaryBooking->user,
                    'booking_count' => $bookingGroup->count(),
                    'courts' => $bookingGroup->pluck('court.name')->implode(', '),
                    'start_time' => $bookingGroup->min('start_time'),
                    'end_time' => $bookingGroup->max('end_time'),
                    'is_member_price' => $primaryBooking->is_member_price,
                    'payments' => $bookingPayments,
                    'payment_summary' => [
                        'total_paid' => $paidAmount,
                        'payment_count' => $bookingPayments->count(),
                        'has_bank_transfer' => $hasBankTransfer,
                        'has_proof' => $hasProof,
                        'payment_methods' => $paymentMethods,
                        'latest_payment' => $latestPayment,
                    ]
                ];

                $combinedBookings->push($combinedBooking);
            }
        }


        $bookings = new \Illuminate\Pagination\LengthAwarePaginator(
            $combinedBookings,
            $paginatedRefNumbers->total(),
            $paginatedRefNumbers->perPage(),
            $paginatedRefNumbers->currentPage(),
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $courts = Court::where('branch_id', $branchId)->get();

        return Inertia::render('Branchs/Booking/List', [
            'bookings' => $bookings,
            'branch' => $branch,
            'courts' => $courts,
            'bank_settings' => $bankSettings,
            'filters' => [
                'search' => $request->search,
                'status' => $request->status,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
                'court_id' => $request->court_id,
            ],
            'sort' => [
                'field' => $sortField,
                'direction' => $sortDirection
            ]
        ]);
    }

    /**
     * Gửi email xác nhận đặt sân
     * 
     * @param string $referenceNumber Mã đơn đặt sân
     * @param string $email Email người nhận
     * @param array $bookingData Dữ liệu đặt sân
     * @return bool
     */
    protected function sendBookingConfirmationEmail($referenceNumber, $email, $bookingData, $booking)
    {
        if (empty($email)) {
            return false;
        }

        return MailService::sendUsingBranchConfig(
            $booking->court->branch_id,
            $email,
            new BookingConfirmation(
                $referenceNumber,
                $bookingData['total_price'] ?? 0,
                $bookingData['bookings'] ?? [],
                $booking
            )
        );
    }

    /**
     * Update the status of a booking.
     *
     * @param  string  $referenceNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(string $referenceNumber)
    {
        try {
            $booking = Booking::where('reference_number', $referenceNumber)->firstOrFail();

            
            $validated = request()->validate([
                'status' => 'required|string|in:pending,confirmed,completed,cancelled',
            ]);

            if ($validated['status'] === 'confirmed') {
                $user = Auth::user();

                // if (!$user || !Auth::user()->can('approve_booking')) {
                //     return response()->json([
                //         'success' => false,
                //         'message' => 'Bạn không có quyền duyệt đơn đặt sân.'
                //     ], 403);
                // }
            }

            
            $booking->status = $validated['status'];
            $booking->save();

            
            CourtBooking::where('reference_number', $referenceNumber)
                ->update(['status' => $validated['status']]);

            
            Log::info('Booking status updated', [
                'reference_number' => $referenceNumber,
                'status' => $validated['status'],
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Booking status updated successfully',
                'booking' => $booking
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a payment to a booking.
     *
     * @param  string  $referenceNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function addPayment(string $referenceNumber)
    {
        try {
            $booking = Booking::where('reference_number', $referenceNumber)->firstOrFail();

            
            $validated = request()->validate([
                'payment_method_id' => 'required|exists:payment_methods,id',
                'amount' => 'required|numeric|min:0',
                'payment_reference' => 'nullable|string|max:255',
                'payment_notes' => 'nullable|string|max:1000',
            ]);

            
            $payment = new \App\Models\Payment();
            $payment->booking_id = $booking->id;
            $payment->payment_method_id = $validated['payment_method_id'];
            $payment->amount = $validated['amount'];
            $payment->payment_reference = $validated['payment_reference'] ?? null;
            $payment->payment_notes = $validated['payment_notes'] ?? null;
            $payment->status = 'completed';
            $payment->save();

            
            $totalPaid = $booking->payments()->sum('amount') + $validated['amount'];

            
            if ($totalPaid >= $booking->total_price) {
                $paymentStatus = 'completed';
            } elseif ($totalPaid > 0) {
                $paymentStatus = 'partial';
            } else {
                $paymentStatus = 'pending';
            }

            $booking->payment_status = $paymentStatus;
            $booking->save();

            
            if (function_exists('activity')) {
                activity()
                    ->performedOn($booking)
                    ->withProperties([
                        'payment_id' => $payment->id,
                        'amount' => $validated['amount'],
                        'payment_method_id' => $validated['payment_method_id']
                    ])
                    ->log('booking_payment_added');
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment added successfully',
                'payment' => $payment,
                'payment_summary' => [
                    'total_paid' => $totalPaid,
                    'payment_count' => $booking->payments()->count(),
                    'payment_status' => $paymentStatus
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add payment: ' . $e->getMessage()
            ], 500);
        }
    }
}