<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\CourtService;
use App\Models\CourtPrice;
use App\Models\Customer;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BookingOfflineController extends Controller
{
    /**
     * Display the form for creating a new offline booking.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);


        return Inertia::render('Branchs/BookingOffline/Offline', [
            'branch' => $branch,
            'courts' => $branch->courts,
        ]);
    }

    /**
     * Store a newly created offline booking in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'booking_date' => 'required|date',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'notes' => 'nullable|string',
            'total_price' => 'required|numeric|min:0',
            'booking_courts' => 'nullable|array',
            'booking_courts.*.court_id' => 'required|exists:courts,id',
            'booking_courts.*.booking_slot' => 'required|array',
            'booking_courts.*.start_time' => 'required|string',
            'booking_courts.*.end_time' => 'required|string',
            'services' => 'nullable|array',
            'services.*.id' => 'exists:court_services,id',
            'services.*.quantity' => 'integer|min:1',
            'services.*.price' => 'numeric|min:0',
            'payment_methods' => 'nullable|array',
            'payment_methods.*.method' => 'required|string|in:cash,card,transfer,e-wallet',
            'payment_methods.*.amount' => 'required|numeric|min:0',
            'payment_methods.*.reference_code' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            DB::beginTransaction();

            $createdBookings = [];
            $result = CourtBooking::processBookings($request->all(), $user, 'offline', $createdBookings);

            if (!$result['success']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], $result['status_code'] ?? 400);
            }


            if (!empty($request->customer_email)) {
                try {

                    $firstBooking = null;
                    if (!empty($createdBookings)) {
                        $firstBooking = CourtBooking::find($createdBookings[0]['id']);
                    }

                    Mail::to($request->customer_email)
                        ->send(new BookingConfirmation(
                            $result['reference_number'],
                            $result['total_price'],
                            $result['bookings'],
                            $firstBooking
                        ));

                    Log::info('Booking confirmation email sent to: ' . $request->customer_email, [
                        'reference_number' => $result['reference_number']
                    ]);
                } catch (\Exception $e) {

                    Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                        'exception' => $e,
                        'email' => $request->customer_email,
                        'reference_number' => $result['reference_number']
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'reference_number' => $result['reference_number'],
                'total_price' => $result['total_price'],
                'bookings' => $result['bookings'],
                'payment_status' => $request->payment_status || $request->all()['payment_status'] || 'pending',
                'email_sent' => !empty($request->customer_email)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Không thể tạo đơn đặt sân offline: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Không thể tạo đơn đặt sân offline: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified offline booking.
     *
     * @param Request $request
     * @param int $id
     * @return \Inertia\Response
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }


        $primaryBooking = CourtBooking::findOrFail($id);
        $referenceNumber = $primaryBooking->reference_number;


        $relatedBookings = CourtBooking::with(['court', 'user'])
            ->where('reference_number', $referenceNumber)
            ->where('branch_id', $branchId)
            ->get();


        $payments = Payment::where('booking_reference', $referenceNumber)
            ->orderBy('created_at', 'desc')
            ->get();


        $totalAmount = $relatedBookings->sum('total_price');
        $paidAmount = $payments->sum('amount');
        $paymentStatus = 'pending';

        if ($paidAmount >= $totalAmount) {
            $paymentStatus = 'completed';
        } elseif ($paidAmount > 0) {
            $paymentStatus = 'partial';
        }


        $bookingData = [
            'id' => $primaryBooking->id,
            'reference_number' => $referenceNumber,
            'booking_date' => $primaryBooking->booking_date,
            'customer_name' => $primaryBooking->customer_name,
            'customer_phone' => $primaryBooking->customer_phone,
            'customer_email' => $primaryBooking->customer_email,
            'notes' => $primaryBooking->notes,
            'status' => $primaryBooking->status,
            'is_member_price' => $primaryBooking->is_member_price,
            'created_at' => $primaryBooking->created_at,
            'updated_at' => $primaryBooking->updated_at,
            'metadata' => $primaryBooking->metadata,
            'total_price' => $totalAmount,
            'paid_amount' => $paidAmount,
            'payment_status' => $paymentStatus,
            'court_bookings' => $relatedBookings,
            'payments' => $payments,
            'remaining_balance' => max(0, $totalAmount - $paidAmount),
            'booking_count' => $relatedBookings->count(),
            'courts' => $relatedBookings->pluck('court.name')->implode(', '),
            'start_time' => $relatedBookings->min('start_time'),
            'end_time' => $relatedBookings->max('end_time'),
        ];

        $branch = Branch::findOrFail($branchId);

        return Inertia::render('Branchs/BookingOffline/OfflineShow', [
            'booking' => $bookingData,
            'branch' => $branch,
        ]);
    }

    /**
     * Show the form for editing the specified offline booking.
     *
     * @param Request $request
     * @param int $id
     * @return \Inertia\Response
     */
    public function edit(Request $request, $id)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $booking = CourtBooking::with(['court'])
            ->where('branch_id', $branchId)
            ->where(function ($query) {
                $query->whereJsonContains('metadata->booking_type', 'offline')
                    ->orWhereJsonContains('metadata', ['booking_type' => 'offline']);
            })
            ->findOrFail($id);

        $branch = Branch::with(['courts', 'prices'])->findOrFail($branchId);
        $services = CourtService::where('business_id', $branch->business_id)
            ->where('is_active', true)
            ->get();

        return Inertia::render('Branchs/BookingOffline/OfflineEdit', [
            'booking' => $booking,
            'branch' => $branch,
            'courts' => $branch->courts,
            'services' => $services,
        ]);
    }

    /**
     * Update the specified offline booking in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized.',
            ], 403);
        }

        try {
            DB::beginTransaction();

            $booking = CourtBooking::where('branch_id', $branchId)->findOrFail($id);


            $validator = Validator::make($request->all(), [
                'customer_name' => 'required|string|max:255',
                'customer_phone' => 'required|string|max:20',
                'customer_email' => 'nullable|email|max:255',
                'notes' => 'nullable|string',
                'number_of_players' => 'integer|min:1|max:10',
                'payment_status' => 'required|in:pending,completed,cancelled',
                'payment_method' => 'required_if:payment_status,completed|in:cash,card,transfer,other',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }


            $booking->customer_name = $request->customer_name;
            $booking->customer_phone = $request->customer_phone;
            $booking->customer_email = $request->customer_email;
            $booking->notes = $request->notes;
            $booking->number_of_players = $request->number_of_players;


            $metadata = $booking->metadata ?? [];
            $metadata['payment_status'] = $request->payment_status;
            $metadata['payment_method'] = $request->payment_method;
            $metadata['updated_by'] = 'branch_staff';
            $metadata['staff_id'] = $user->id;
            $metadata['staff_name'] = $user->name;
            $metadata['updated_at'] = now()->toDateTimeString();

            $booking->metadata = $metadata;
            $booking->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đã cập nhật đơn đặt sân offline thành công.',
                'booking' => $booking,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Không thể cập nhật đơn đặt sân offline: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Không thể cập nhật đơn đặt sân offline: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel the specified offline booking.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, $id)
    {
        $user = $request->user();
        $branchId = $user->branch->id;

        if (!$branchId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized.',
            ], 403);
        }

        try {
            DB::beginTransaction();

            $booking = CourtBooking::where('branch_id', $branchId)->findOrFail($id);


            if ($booking->status === 'cancelled') {
                return response()->json([
                    'success' => false,
                    'message' => 'Đơn đặt sân đã được hủy trước đó.',
                ], 400);
            }


            $booking->status = 'cancelled';


            $metadata = $booking->metadata ?? [];
            $metadata['cancelled_by'] = 'branch_staff';
            $metadata['staff_id'] = $user->id;
            $metadata['staff_name'] = $user->name;
            $metadata['cancelled_at'] = now()->toDateTimeString();
            $metadata['cancellation_reason'] = $request->reason ?? 'Hủy từ quản lý chi nhánh';

            $booking->metadata = $metadata;
            $booking->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đã hủy đơn đặt sân offline thành công.',
                'booking' => $booking,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Không thể hủy đơn đặt sân offline: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Không thể hủy đơn đặt sân offline: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get the branch ID for the authenticated user.
     *
     * @param \App\Models\User $user
     * @return int|null
     */
    private function getBranchIdForUser($user)
    {
        if ($user->hasRole('branch-admin')) {
            $branch = Branch::where('user_id', $user->id)->first();
            return $branch ? $branch->id : null;
        } elseif ($user->hasRole('branch-staff')) {
            return $user->branch_id;
        } elseif ($user->hasRole('business-admin') || $user->hasRole('super-admin')) {



            return request()->route('branch') ?: request('branch_id');
        }

        return null;
    }

    /**
     * Return unauthorized response.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    private function unauthorizedResponse()
    {
        return redirect()->route('unauthorized');
    }
}
