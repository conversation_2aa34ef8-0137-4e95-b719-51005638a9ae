<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Mail\BookingRejection;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Services\BookingEventService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Carbon\Carbon;

class BookingOnlineController extends Controller
{
    /**
     * Display a listing of online bookings for the branch.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $userBranchIds = [];

        // Handle different user roles
        if ($user->hasRole(['super-admin'])) {
            // Super admins can view any branch
            $branchId = $request->input('branch_id', null);
            if (!$branchId) {
                $branch = Branch::first();
                $branchId = $branch ? $branch->id : null;
            }
        } elseif ($user->hasRole(['admin'])) {
            // Admin role can view all branches in their business
            $businessId = $user->business_id;
            $userBranchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
            $branchId = $request->input('branch_id', null);
            if (!$branchId || !in_array($branchId, $userBranchIds)) {
                $branch = Branch::where('business_id', $businessId)->first();
                $branchId = $branch ? $branch->id : null;
            }
        } else {
            // Branch manager and staff can only view their assigned branch
            $branchId = $user->branch_id;
            if ($branchId) {
                $userBranchIds = [$branchId];
            }
        }

        if (!$branchId) {
            return $this->unauthorizedResponse();
        }

        $branch = Branch::with('business')->findOrFail($branchId);
        $businessId = $branch->business_id;

        // Query bookings using both CourtBooking and Booking models
        $query = CourtBooking::with(['court', 'user', 'customer', 'booking'])
            ->where('branch_id', $branchId);

        // Filter by parent booking's branch_id as well if needed
        $bookingQuery = \App\Models\Booking::where('branch_id', $branchId);
        $bookingIds = $bookingQuery->pluck('id')->toArray();

        if (!empty($bookingIds)) {
            $query->where(function ($q) use ($branchId, $bookingIds) {
                $q->where('branch_id', $branchId)
                    ->orWhereIn('booking_id', $bookingIds);
            });
        }

        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('court_bookings.reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('court_bookings.customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('court_bookings.customer_phone', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->has('status') && !empty($request->status) && $request->status !== 'all') {
            $query->where('court_bookings.status', $request->status);
        }

        if ($request->has('court_id') && !empty($request->court_id) && $request->court_id !== 'all') {
            $query->where('court_bookings.court_id', $request->court_id);
        }

        if ($request->has('time') && !empty($request->time) && $request->time !== 'all') {
            switch ($request->time) {
                case 'today':
                    $query->whereDate('court_bookings.booking_date', Carbon::today());
                    break;
                case 'tomorrow':
                    $query->whereDate('court_bookings.booking_date', Carbon::tomorrow());
                    break;
                case 'thisweek':
                    $query->whereBetween('court_bookings.booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'nextweek':
                    $query->whereBetween('court_bookings.booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()]);
                    break;
            }
        }

        // Make sure we're selecting all the needed fields
        $query->select('court_bookings.*');
        $query->orderBy($request->input('sort', 'court_bookings.created_at'), $request->input('direction', 'desc'));

        $bookings = $query->paginate(10);

        $referenceNumbers = collect($bookings->items())->pluck('reference_number')->unique()->toArray();

        $bankSettings = null;
        if ($businessId) {
            $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);
        }

        $payments = \App\Models\Payment::whereIn('booking_reference', $referenceNumbers)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $paymentsByReference = $payments->groupBy('booking_reference');

        $groupedBookings = collect($bookings->items())->groupBy('reference_number')->map(function ($group) use ($paymentsByReference) {
            $totalPrice = $group->sum('total_price');
            $firstBooking = $group->first();
            $referenceNumber = $firstBooking->reference_number;

            $bookingPayments = isset($paymentsByReference[$referenceNumber])
                ? $paymentsByReference[$referenceNumber]
                : collect();

            $paidAmount = $bookingPayments->sum('amount');
            $paymentStatus = 'pending';

            if ($paidAmount >= $totalPrice) {
                $paymentStatus = 'completed';
            } elseif ($paidAmount > 0) {
                $paymentStatus = 'partial';
            }

            $hasBankTransfer = $bookingPayments->contains('payment_method_id', 2);
            $hasProof = $bookingPayments->contains('has_proof', true);
            $latestPayment = $bookingPayments->sortByDesc('created_at')->first();
            $paymentMethods = $bookingPayments->pluck('payment_method')->unique()->implode(', ');

            return [
                'id' => $firstBooking->id,
                'reference_number' => $referenceNumber,
                'customer_name' => $firstBooking->customer_name,
                'customer_phone' => $firstBooking->customer_phone,
                'customer_email' => $firstBooking->customer_email,
                'status' => $firstBooking->status,
                'created_at' => $firstBooking->created_at->format('Y-m-d H:i:s'),
                'booking_date' => $firstBooking->booking_date->format('Y-m-d'),
                'start_time' => $firstBooking->start_time->format('H:i'),
                'end_time' => $firstBooking->end_time->format('H:i'),
                'total_price' => $totalPrice,
                'formatted_total_price' => number_format($totalPrice, 0, ',', '.') . 'đ',
                'bookings' => $group->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'court' => $booking->court,
                        'booking_date' => $booking->booking_date->format('Y-m-d'),
                        'start_time' => $booking->start_time->format('H:i'),
                        'end_time' => $booking->end_time->format('H:i'),
                        'total_price' => $booking->total_price,
                        'formatted_price' => number_format($booking->total_price, 0, ',', '.') . 'đ',
                        'booking_id' => $booking->booking_id,
                        'parent_booking' => $booking->booking
                    ];
                })->values(),

                'payments' => $bookingPayments->values(),
                'payment_summary' => [
                    'total_paid' => $paidAmount,
                    'payment_count' => $bookingPayments->count(),
                    'has_bank_transfer' => $hasBankTransfer,
                    'has_proof' => $hasProof,
                    'payment_methods' => $paymentMethods,
                    'latest_payment' => $latestPayment,
                    'payment_status' => $paymentStatus
                ],
                'branch' => [
                    'id' => $firstBooking->branch_id,
                    'name' => $firstBooking->branch ? $firstBooking->branch->name : null,
                ]
            ];
        })->values();

        $groupedPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $groupedBookings,
            $bookings->total(),
            $bookings->perPage(),
            $bookings->currentPage(),
            [
                'path' => $bookings->path(),
                'query' => $request->query(),
            ]
        );

        // Get stats based on user's branch access
        $statsQuery = CourtBooking::where('branch_id', $branchId);

        return Inertia::render('Branchs/BookingOnline/index', [
            'bookings' => $groupedPaginator,
            'branch' => $branch,
            'bank_settings' => $bankSettings,
            'courts' => Court::where('branch_id', $branchId)->get(['id', 'name']),
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? 'pending',
                'court_id' => $request->court_id ?? '',
                'time' => $request->time ?? 'all',
                'sort' => $request->input('sort', 'created_at'),
                'direction' => $request->input('direction', 'desc'),
            ],
            'stats' => [
                'pending' => $statsQuery->where('status', 'pending')->count(),
                'approved' => $statsQuery->where('status', 'confirmed')
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'rejected' => $statsQuery->where('status', 'cancelled')
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'total' => $statsQuery->whereDate('created_at', Carbon::today())
                    ->count(),
            ],
            'statuses' => [
                'pending' => 'Chờ xác nhận',
                'confirmed' => 'Đã xác nhận',
                'cancelled' => 'Đã hủy',
                'completed' => 'Đã hoàn thành',
            ],
            'userBranches' => count($userBranchIds) > 1 ? Branch::whereIn('id', $userBranchIds)->get(['id', 'name']) : [],
        ]);
    }

    /**
     * Get booking details
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $userBranchIds = [];

        if ($user->hasRole(['super-admin'])) {
            $booking = CourtBooking::with(['court', 'user', 'services', 'customer', 'booking'])->findOrFail($id);
            $this->addPaymentInfo($booking);
            return response()->json(['booking' => $booking]);
        } elseif ($user->hasRole(['admin'])) {
            // Admin can view all bookings in their business
            $businessId = $user->business_id;
            $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();

            $booking = CourtBooking::with(['court', 'user', 'services', 'customer', 'booking', 'branch.business'])
                ->whereIn('branch_id', $branchIds)
                ->findOrFail($id);

            $this->addPaymentInfo($booking);
            return response()->json(['booking' => $booking]);
        } else {
            // Staff or branch manager can only view bookings from their branch
            $branchId = $user->branch_id;

            if (!$branchId) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $booking = CourtBooking::with(['court', 'user', 'services', 'customer', 'booking', 'branch.business'])
                ->where('branch_id', $branchId)
                ->findOrFail($id);

            $this->addPaymentInfo($booking);
            return response()->json(['booking' => $booking]);
        }
    }

    /**
     * Add payment information to a booking
     * 
     * @param \App\Models\CourtBooking $booking
     * @return void
     */
    private function addPaymentInfo($booking)
    {

        $payments = \App\Models\Payment::where('booking_reference', $booking->reference_number)
            ->with('paymentMethod')
            ->get();


        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {

                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {

                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }


        $totalPrice = $booking->total_price;
        $paidAmount = $payments->sum('amount');
        $paymentStatus = 'pending';

        if ($paidAmount >= $totalPrice) {
            $paymentStatus = 'completed';
        } elseif ($paidAmount > 0) {
            $paymentStatus = 'partial';
        }


        $booking->payments = $payments;
        $booking->payment_summary = [
            'total_paid' => $paidAmount,
            'payment_count' => $payments->count(),
            'has_bank_transfer' => $payments->contains('payment_method_id', 2),
            'has_proof' => $payments->contains('has_proof', true),
            'payment_methods' => $payments->pluck('payment_method')->unique()->implode(', '),
            'latest_payment' => $payments->sortByDesc('created_at')->first(),
            'payment_status' => $paymentStatus
        ];


        if ($booking->branch && $booking->branch->business_id) {
            $booking->bank_settings = \App\Models\BusinessSetting::getBankSettingsFormatted($booking->branch->business_id);
        }
    }

    /**
     * Approve a booking
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(Request $request, $id)
    {
        $user = $request->user();

        if ($user->hasRole(['super-admin'])) {
            $booking = CourtBooking::findOrFail($id);
        } elseif ($user->hasRole(['admin'])) {
            // Admin can approve bookings in their business
            $businessId = $user->business_id;
            $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();

            $booking = CourtBooking::whereIn('branch_id', $branchIds)->findOrFail($id);
        } else {
            // Staff or branch manager can only approve bookings from their branch
            if (!$user->branch_id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $booking = CourtBooking::where('branch_id', $user->branch_id)->findOrFail($id);
        }

        if ($booking->status !== 'pending') {
            return response()->json(['error' => 'Only pending bookings can be approved'], 422);
        }

        $referenceNumber = $booking->reference_number;

        $pendingPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->get();

        if ($pendingPayments->count() > 0 && !$request->has('force_approve')) {
            $serializedPayments = $pendingPayments->map(function ($payment) {
                $payment->payment_method_name = '';
                if ($payment->payment_method_id && $payment->paymentMethod) {
                    $payment->payment_method_name = $payment->paymentMethod->payment_name;
                } elseif (is_string($payment->payment_method)) {
                    $payment->payment_method_name = $payment->payment_method;
                }

                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'payment_method_id' => $payment->payment_method_id,
                    'payment_method_name' => $payment->payment_method_name,
                    'status' => $payment->status,
                    'created_at' => $payment->created_at->format('Y-m-d H:i:s')
                ];
            })->toArray();

            return response()->json([
                'warning' => true,
                'message' => 'Cảnh báo: Có ' . $pendingPayments->count() . ' giao dịch thanh toán chưa được xác nhận. Bạn nên xác nhận các thanh toán trước khi xác nhận đơn đặt sân.',
                'pending_payments' => $serializedPayments,
                'pending_payment_count' => $pendingPayments->count(),
                'booking_id' => $id,
                'requires_confirmation' => true
            ], 200);
        }

        try {
            DB::beginTransaction();

            // Update related Court Bookings
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('status', 'pending');

            // Restrict to user's branch for non-super-admin and non-admin users
            if (!$user->hasRole(['super-admin'])) {
                if ($user->hasRole(['admin'])) {
                    $businessId = $user->business_id;
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    $relatedBookings->whereIn('branch_id', $branchIds);
                } else {
                    $relatedBookings->where('branch_id', $user->branch_id);
                }
            }

            $relatedBookings = $relatedBookings->get();

            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes,
                'approved_at' => now()->toDateTimeString(),
            ];

            foreach ($relatedBookings as $relatedBooking) {
                $relatedBooking->status = 'confirmed';
                $relatedBooking->metadata = array_merge($relatedBooking->metadata ?? [], $approvalMetadata);
                $relatedBooking->save();

                // Update parent Booking if it exists
                if ($relatedBooking->booking_id) {
                    $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'confirmed';
                        $parentBooking->save();
                    }
                }
            }

            $completedPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->where('status', 'completed')
                ->get();

            if ($completedPayments->count() > 0) {
                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $relatedBookings->sum('total_price');

                $paymentStatus = ($totalPaid >= $totalPrice) ? 'paid' :
                    ($totalPaid > 0 ? 'partial' : 'pending');

                foreach ($relatedBookings as $relatedBooking) {
                    $relatedBooking->payment_status = $paymentStatus;
                    $relatedBooking->save();

                    // Update parent Booking payment status if it exists
                    if ($relatedBooking->booking_id) {
                        $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                        if ($parentBooking) {
                            $parentBooking->payment_status = $paymentStatus;
                            $parentBooking->save();
                        }
                    }
                }
            }

            if (!empty($booking->customer_email)) {
                try {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($booking->customer_email)->send(
                        new BookingConfirmation(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking
                        )
                    );

                    Log::info('Booking confirmation email sent to: ' . $booking->customer_email, [
                        'reference_number' => $booking->reference_number
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Booking approved successfully',
                'booking' => $booking,
                'affected_bookings' => $relatedBookings->count(),
                'payments_auto_approved' => $completedPayments->count() > 0 ? $completedPayments->count() : 0,
                'email_sent' => !empty($booking->customer_email)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to approve booking: ' . $e->getMessage(), [
                'exception' => $e,
                'booking_id' => $id,
                'reference_number' => $booking->reference_number ?? null
            ]);

            return response()->json([
                'error' => 'Failed to approve booking',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a booking
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject(Request $request, $id)
    {
        $user = $request->user();

        if ($user->hasRole(['super-admin'])) {
            $booking = CourtBooking::findOrFail($id);
        } elseif ($user->hasRole(['admin'])) {
            // Admin can reject bookings in their business
            $businessId = $user->business_id;
            $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();

            $booking = CourtBooking::whereIn('branch_id', $branchIds)->findOrFail($id);
        } else {
            // Staff or branch manager can only reject bookings from their branch
            if (!$user->branch_id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $booking = CourtBooking::where('branch_id', $user->branch_id)->findOrFail($id);
        }

        if ($booking->status !== 'pending') {
            return response()->json(['error' => 'Only pending bookings can be rejected'], 422);
        }

        if (empty($request->reason)) {
            return response()->json(['error' => 'Rejection reason is required'], 422);
        }

        try {
            DB::beginTransaction();

            $referenceNumber = $booking->reference_number;

            // Get all related court bookings
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('status', 'pending');

            // Restrict to user's branch for non-super-admin and non-admin users
            if (!$user->hasRole(['super-admin'])) {
                if ($user->hasRole(['admin'])) {
                    $businessId = $user->business_id;
                    $branchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
                    $relatedBookings->whereIn('branch_id', $branchIds);
                } else {
                    $relatedBookings->where('branch_id', $user->branch_id);
                }
            }

            $relatedBookings = $relatedBookings->get();

            $rejectionMetadata = [
                'rejected_by' => $user->id,
                'rejected_by_name' => $user->name,
                'rejected_at' => now()->toDateTimeString(),
                'rejection_reason' => $request->reason
            ];

            $cancelledAt = now();

            foreach ($relatedBookings as $relatedBooking) {
                $relatedBooking->status = 'cancelled';
                $relatedBooking->payment_status = 'cancelled';
                $relatedBooking->cancelled_at = $cancelledAt;
                $relatedBooking->cancellation_reason = $request->reason;
                $relatedBooking->metadata = array_merge($relatedBooking->metadata ?? [], $rejectionMetadata);
                $relatedBooking->save();

                // Update parent Booking if it exists
                if ($relatedBooking->booking_id) {
                    $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'cancelled';
                        $parentBooking->payment_status = 'cancelled';
                        $parentBooking->cancelled_at = $cancelledAt;
                        $parentBooking->cancellation_reason = $request->reason;
                        $parentBooking->save();
                    }
                }
            }

            $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->whereIn('status', ['pending', 'pending_approval'])
                ->get();

            foreach ($payments as $payment) {
                $payment->status = 'cancelled';
                $payment->payment_details = array_merge(
                    is_array($payment->payment_details) ? $payment->payment_details : [],
                    [
                        'rejected_by' => $user->id,
                        'rejected_by_name' => $user->name,
                        'rejected_at' => now()->toDateTimeString(),
                        'reason' => $request->reason
                    ]
                );
                $payment->save();
            }

            if (!empty($booking->customer_email)) {
                try {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($booking->customer_email)
                        ->send(new BookingRejection(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking,
                            $request->reason
                        ));

                    Log::info('Booking rejection email sent to: ' . $booking->customer_email, [
                        'reference_number' => $booking->reference_number
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send booking rejection email: ' . $e->getMessage());
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Booking rejected successfully',
                'booking' => $booking,
                'affected_bookings' => $relatedBookings->count(),
                'email_sent' => !empty($booking->customer_email)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to reject booking: ' . $e->getMessage(), [
                'exception' => $e,
                'booking_id' => $id,
                'reference_number' => $booking->reference_number ?? null
            ]);

            return response()->json([
                'error' => 'Failed to reject booking',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve a payment for bookings
     * 
     * @param Request $request
     * @param int $paymentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvePayment(Request $request, $paymentId)
    {
        $user = $request->user();

        try {

            $payment = \App\Models\Payment::findOrFail($paymentId);


            if (!$user->hasRole(['super-admin', 'admin'])) {
                if (!$user->branch_id) {
                    return response()->json(['error' => 'Unauthorized'], 403);
                }


                $bookingRef = $payment->booking_reference;
                $booking = CourtBooking::where('reference_number', $bookingRef)
                    ->where('branch_id', $user->branch_id)
                    ->first();

                if (!$booking) {
                    return response()->json(['error' => 'Unauthorized to approve this payment'], 403);
                }
            }


            if ($payment->status !== 'pending_approval' && $payment->status !== 'pending') {
                return response()->json([
                    'error' => 'Only pending payments can be approved',
                    'current_status' => $payment->status
                ], 422);
            }

            DB::beginTransaction();


            $payment->status = 'completed';


            $paymentDetails = [];
            if (!empty($payment->payment_details)) {
                $paymentDetails = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true) ?? [];
            }


            $paymentDetails['approved_by'] = $user->id;
            $paymentDetails['approved_by_name'] = $user->name;
            $paymentDetails['approved_at'] = now()->toDateTimeString();


            $payment->payment_details = $paymentDetails;
            $payment->save();


            $bookingReference = $payment->booking_reference;
            $bookings = CourtBooking::where('reference_number', $bookingReference)->get();


            $totalBookingAmount = $bookings->sum('total_price');


            $approvedPayments = \App\Models\Payment::where('booking_reference', $bookingReference)
                ->where('status', 'completed')
                ->get();

            $totalPaidAmount = $approvedPayments->sum('amount');


            $paymentStatus = ($totalPaidAmount >= $totalBookingAmount) ? 'paid' : 'partial';


            foreach ($bookings as $booking) {
                $booking->payment_status = $paymentStatus;
                $booking->save();
            }

            DB::commit();


            return response()->json([
                'success' => true,
                'message' => 'Payment approved successfully',
                'payment' => $payment,
                'payment_status' => $paymentStatus,
                'total_booking_amount' => $totalBookingAmount,
                'total_paid_amount' => $totalPaidAmount,
                'affected_bookings' => $bookings->count()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to approve payment: ' . $e->getMessage(), [
                'exception' => $e,
                'payment_id' => $paymentId,
                'user_id' => $user->id
            ]);

            return response()->json([
                'error' => 'Failed to approve payment',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending payment information for a booking
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentInfo(Request $request, $id)
    {
        $user = $request->user();

        if ($user->hasRole(['super-admin', 'admin'])) {
            $booking = CourtBooking::findOrFail($id);
        } else {
            if (!$user->branch_id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $booking = CourtBooking::where('branch_id', $user->branch_id)->findOrFail($id);
        }

        $referenceNumber = $booking->reference_number;

        $pendingPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->get();


        $formattedPayments = [];
        foreach ($pendingPayments as $payment) {
            $formattedPayment = [
                'id' => $payment->id,
                'amount' => $payment->amount,
                'payment_method_id' => $payment->payment_method_id,
                'payment_method_name' => $payment->payment_method ? $payment->payment_method->payment_name : 'Unknown',
                'status' => $payment->status,
                'created_at' => $payment->created_at ? $payment->created_at->format('Y-m-d H:i:s') : null,
            ];


            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $formattedPayment['proof_url'] = asset('storage/' . $details['proof_file']);
                    $formattedPayment['has_proof'] = true;
                }
            }

            $formattedPayments[] = $formattedPayment;
        }


        $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();
        $totalPrice = $relatedBookings->sum('total_price');


        $requiresConfirmation = $pendingPayments->count() > 0;

        return response()->json([
            'success' => true,
            'pending_payments' => $formattedPayments,
            'pending_payment_count' => $pendingPayments->count(),
            'total_price' => $totalPrice,
            'requires_confirmation' => $requiresConfirmation,
            'warning' => $requiresConfirmation,
            'message' => $requiresConfirmation ? 'Đơn đặt sân này có thanh toán chưa được xác nhận.' : null
        ]);
    }

    /**
     * Approve all bookings with the same reference number
     * 
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveByReference(Request $request, $reference_number)
    {
        $user = $request->user();

        if (!$user->hasRole(['super-admin', 'admin']) && !$user->branch_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }


        $bookings = CourtBooking::where('reference_number', $reference_number)
            ->where('status', 'pending');


        if (!$user->hasRole(['super-admin', 'admin'])) {
            $bookings->where('branch_id', $user->branch_id);
        }

        $bookings = $bookings->get();

        if ($bookings->isEmpty()) {
            return response()->json(['error' => 'No pending bookings found with this reference number'], 404);
        }


        $firstBooking = $bookings->first();


        $pendingPayments = \App\Models\Payment::where('booking_reference', $reference_number)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->get();

        if ($pendingPayments->count() > 0 && !$request->has('force_approve')) {
            $serializedPayments = $pendingPayments->map(function ($payment) {
                $payment->payment_method_name = '';
                if ($payment->payment_method_id && $payment->paymentMethod) {
                    $payment->payment_method_name = $payment->paymentMethod->payment_name;
                } elseif (is_string($payment->payment_method)) {
                    $payment->payment_method_name = $payment->payment_method;
                }

                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'payment_method_id' => $payment->payment_method_id,
                    'payment_method_name' => $payment->payment_method_name,
                    'status' => $payment->status,
                    'created_at' => $payment->created_at->format('Y-m-d H:i:s')
                ];
            })->toArray();

            return response()->json([
                'warning' => true,
                'message' => 'Cảnh báo: Có ' . $pendingPayments->count() . ' giao dịch thanh toán chưa được xác nhận. Bạn nên xác nhận các thanh toán trước khi xác nhận đơn đặt sân.',
                'pending_payments' => $serializedPayments,
                'pending_payment_count' => $pendingPayments->count(),
                'reference_number' => $reference_number,
                'requires_confirmation' => true
            ], 200);
        }

        try {
            DB::beginTransaction();

            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes,
                'approved_at' => now()->toDateTimeString(),
            ];


            foreach ($bookings as $booking) {
                $booking->status = 'confirmed';
                $booking->metadata = array_merge($booking->metadata ?? [], $approvalMetadata);
                $booking->save();
            }



            $completedPayments = \App\Models\Payment::where('booking_reference', $reference_number)
                ->where('status', 'completed')
                ->get();


            if ($completedPayments->count() > 0) {

                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $bookings->sum('total_price');


                $paymentStatus = ($totalPaid >= $totalPrice) ? 'paid' :
                    ($totalPaid > 0 ? 'partial' : 'pending');

                foreach ($bookings as $booking) {
                    $booking->payment_status = $paymentStatus;
                    $booking->save();
                }
            }


            if (!empty($firstBooking->customer_email)) {
                try {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($firstBooking->customer_email)->send(
                        new BookingConfirmation(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking
                        )
                    );

                    Log::info('Booking confirmation email sent to: ' . $firstBooking->customer_email, [
                        'reference_number' => $booking->reference_number
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
                }
            }

            DB::commit();

            BookingEventService::bookingApproved($bookings, $user->id);

            return response()->json([
                'success' => true,
                'message' => 'Tất cả booking đã được xác nhận thành công',
                'reference_number' => $reference_number,
                'affected_bookings' => $bookings->count(),
                'payments_auto_approved' => $completedPayments->count() > 0 ? $completedPayments->count() : 0,
                'email_sent' => !empty($firstBooking->customer_email)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to approve bookings by reference: ' . $e->getMessage(), [
                'exception' => $e,
                'reference_number' => $reference_number
            ]);

            return response()->json([
                'error' => 'Failed to approve bookings',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Return unauthorized response
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    private function unauthorizedResponse()
    {
        return redirect()->route('unauthorized')->with('message', 'You do not have access to manage this branch.');
    }
}