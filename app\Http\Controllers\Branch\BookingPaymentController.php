<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CourtBooking;
use App\Models\Payment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\CourtPrice;
use App\Models\PaymentMethod;

class BookingPaymentController extends Controller
{
    /**
     * Xử lý thanh toán phí quá giờ cho booking
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function processOvertimePayment(Request $request)
    {
        
        $request->validate([
            'reference_number' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'payment_method_id' => 'required|exists:payment_methods,id',
        ]);

        try {
            DB::beginTransaction();

            
            $bookings = CourtBooking::where('reference_number', $request->reference_number)
                ->where('status', '!=', 'cancelled')
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy đơn đặt sân với mã tham chiếu này.'
                ], 404);
            }

            
            if ($bookings->first()->status === 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Đơn đặt sân này đã được hoàn thành, không thể thanh toán phí quá giờ.'
                ], 400);
            }

            
            $payment = new Payment();
            $payment->booking_reference = $request->reference_number;
            $payment->amount = $request->amount;
            $payment->payment_type = 'overtime_fee';
            $payment->status = 'completed';
            $payment->user_id = $bookings->first()->user_id ?? null;
            $payment->transaction_id = 'OT' . time() . rand(1000, 9999);
            $payment->transaction_date = now();
            $payment->notes = 'Thanh toán phí quá giờ cho đơn đặt sân ' . $request->reference_number;
            $payment->payment_method = $request->payment_method;
            
            $payment->payment_details = [
                'payment_type' => 'overtime_fee',
                'staff_id' => Auth::id(),
                'staff_name' => Auth::user()->name,
                'overtime_fee' => $request->amount,
                'payment_date' => now()->format('Y-m-d H:i:s')
            ];

            $payment->save();

            
            foreach ($bookings as $booking) {
                if ($booking->checkout_time) {
                    $metadata = $booking->metadata ?? [];
                    $metadata['overtime_fee'] = [
                        'paid' => true,
                        'amount' => $request->amount / count($bookings), 
                        'payment_id' => $payment->id,
                        'payment_date' => now()->format('Y-m-d H:i:s')
                    ];
                    $booking->metadata = $metadata;

                    $booking->overtime_fee_paid = true;
                    $booking->overtime_fee = 0;

                    $booking->save();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thanh toán phí quá giờ thành công',
                'payment' => $payment
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi xử lý thanh toán: ' . $e->getMessage()
            ], 500);
        }
    }
}