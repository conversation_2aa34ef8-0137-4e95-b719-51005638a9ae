<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\User;
use App\Models\Customer;
use App\Models\Payment;
use App\Services\ExportService;
use App\Services\StatusService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class BookingReportController extends Controller
{
    /**
     * Display the booking reports page
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }
        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $status = $request->input('status', 'all');
        $courtId = $request->input('court_id', 'all');
        $sortField = $request->input('sort_field', 'booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $query = Booking::with(['branch', 'customer', 'user', 'courtBookings.court'])
            ->where('branch_id', $branchId);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%")
                    ->orWhereHas('courtBookings.court', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($courtId !== 'all') {
            $query->whereHas('courtBookings', function ($q) use ($courtId) {
                $q->where('court_id', $courtId);
            });
        }

        $bookings = $query->orderBy($sortField, $sortDirection)
            ->get()
            ->map(function ($booking) {

                $firstCourtBooking = $booking->courtBookings->first();
                $courtNames = $booking->courtBookings->pluck('court.name')->join(', ');


                $totalPrice = $booking->total_price;

                return [
                    'id' => $booking->id,
                    'reference_number' => $booking->reference_number,
                    'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                    'customer_name' => $booking->customer ? $booking->customer->name : $booking->customer_name,
                    'customer_contact' => $booking->customer ?
                        ($booking->customer->phone ?? $booking->customer->email) :
                        ($booking->customer_phone ?? $booking->customer_email ?? 'N/A'),
                    'court_name' => $courtNames,
                    'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                    'total_price' => $totalPrice,
                    'total_price_formatted' => number_format($totalPrice, 0, ',', '.') . ' ₫',
                    'status' => $booking->status,
                    'created_at' => $booking->created_at->format('d/m/Y H:i'),
                    'user_name' => $booking->user ? $booking->user->name : 'N/A',
                    'user_email' => $booking->user ? $booking->user->email : 'N/A',
                    'booking_type' => $booking->booking_type,
                    'notes' => $booking->notes,
                    'cancelled_at' => $booking->cancelled_at ? Carbon::parse($booking->cancelled_at)->format('d/m/Y H:i') : null,
                    'cancellation_reason' => $booking->cancellation_reason,
                    'related_courts_count' => $booking->courtBookings->count(),
                    'payment_status' => $booking->payment_status,
                ];
            });


        $summary = $this->getSummaryStats($branchId, $fromDate, $toDate);


        $total = $bookings->count();
        $pageStart = ($page - 1) * $perPage;
        $pageItems = $bookings->slice($pageStart, $perPage)->values();


        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $pageItems,
            $total,
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $courts = Court::where('branch_id', $branchId)
            ->orderBy('name')
            ->get()
            ->map(function ($court) {
                return [
                    'value' => $court->id,
                    'label' => $court->name
                ];
            })
            ->prepend(['value' => 'all', 'label' => __('common.all_courts')]);


        $topCourts = $this->getTopCourtsByBookings($branchId, $fromDate ?: Carbon::now()->subYear()->toDateString(), $toDate ?: Carbon::now()->toDateString());


        $statuses = StatusService::getBookingStatuses();

        return Inertia::render('Branchs/Reports/Bookings', [
            'bookings' => $paginator,
            'filters' => [
                'search' => $search,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'status' => $status,
                'court_id' => $courtId,
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
                'per_page' => $perPage
            ],
            'courts' => $courts,
            'statuses' => $statuses,
            'summary' => $summary,
            'top_courts' => $topCourts
        ]);
    }

    /**
     * Get summary statistics
     *
     * @param int $branchId
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    private function getSummaryStats($branchId, ?string $fromDate = null, ?string $toDate = null)
    {
        $query = Booking::where('branch_id', $branchId);


        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }


        $totalAmount = $query->sum('total_price');


        $totalBookingsCount = $query->count();


        $completedCount = Booking::where('branch_id', $branchId)
            ->where('status', 'completed')
            ->when($fromDate && $toDate, function ($q) use ($fromDate, $toDate) {
                $q->whereBetween('booking_date', [$fromDate, $toDate]);
            })
            ->count();

        $confirmedCount = Booking::where('branch_id', $branchId)
            ->where('status', 'confirmed')
            ->when($fromDate && $toDate, function ($q) use ($fromDate, $toDate) {
                $q->whereBetween('booking_date', [$fromDate, $toDate]);
            })
            ->count();

        $pendingCount = Booking::where('branch_id', $branchId)
            ->where('status', 'pending')
            ->when($fromDate && $toDate, function ($q) use ($fromDate, $toDate) {
                $q->whereBetween('booking_date', [$fromDate, $toDate]);
            })
            ->count();

        $cancelledCount = Booking::where('branch_id', $branchId)
            ->where('status', 'cancelled')
            ->when($fromDate && $toDate, function ($q) use ($fromDate, $toDate) {
                $q->whereBetween('booking_date', [$fromDate, $toDate]);
            })
            ->count();


        $courtCount = Court::where('branch_id', $branchId)->count();

        return [
            'total_amount' => $totalAmount,
            'total_amount_formatted' => number_format($totalAmount, 0, ',', '.') . ' ₫',
            'total_bookings' => $totalBookingsCount,
            'completed_count' => $completedCount,
            'confirmed_count' => $confirmedCount,
            'pending_count' => $pendingCount,
            'cancelled_count' => $cancelledCount,
            'court_count' => $courtCount
        ];
    }

    /**
     * Get booking details by ID
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBookingDetails(Request $request)
    {
        $bookingId = $request->input('booking_id');
        $booking = Booking::with(['courtBookings.court', 'branch', 'customer', 'user'])->find($bookingId);

        if (!$booking) {
            return response()->json(['error' => 'Booking not found'], 404);
        }

        $referenceNumber = $booking->reference_number;


        $courtBookings = $booking->courtBookings->map(function ($courtBooking) {
            return [
                'id' => $courtBooking->id,
                'court_id' => $courtBooking->court_id,
                'court_name' => $courtBooking->court->name,
                'branch_name' => $courtBooking->court->branch->name,
                'booking_date' => Carbon::parse($courtBooking->booking_date)->format('d/m/Y'),
                'booking_time' => Carbon::parse($courtBooking->start_time)->format('H:i') . ' - ' . Carbon::parse($courtBooking->end_time)->format('H:i'),
                'status' => $courtBooking->status,
                'status_display' => StatusService::getBookingStatusDisplay($courtBooking->status),
                'status_class' => StatusService::getBookingStatusClass($courtBooking->status),
                'total_price' => $courtBooking->total_price,
                'total_price_formatted' => number_format($courtBooking->total_price, 0, ',', '.') . ' ₫',
            ];
        });


        $payments = Payment::where('booking_reference', $referenceNumber)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'amount_formatted' => number_format($payment->amount, 0, ',', '.') . ' ₫',
                    'payment_method' => $payment->payment_method,
                    'payment_method_display' => $this->getPaymentMethodDisplay($payment->payment_method),
                    'status' => $payment->status,
                    'transaction_date' => $payment->transaction_date ? Carbon::parse($payment->transaction_date)->format('d/m/Y H:i') : null,
                    'reference_number' => $payment->reference_number,
                ];
            });

        return response()->json([
            'booking' => [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                'customer_name' => $booking->customer ? $booking->customer->name : $booking->customer_name,
                'customer_contact' => $booking->customer ? ($booking->customer->phone ?? $booking->customer->email) : ($booking->customer_phone ?? $booking->customer_email),
                'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                'total_price' => $booking->total_price,
                'total_price_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                'status' => $booking->status,
                'status_display' => $this->getBookingStatusDisplay($booking->status),
                'payment_status' => $booking->payment_status,
                'payment_status_display' => $this->getPaymentStatusDisplay($booking->payment_status),
                'booking_type' => $booking->booking_type,
                'created_at' => $booking->created_at->format('d/m/Y H:i'),
                'notes' => $booking->notes,
            ],
            'related_courts' => $courtBookings,
            'all_courts' => $courtBookings,
            'payments' => $payments
        ]);
    }

    /**
     * Get top 10 courts by booking count
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getTopCourtsByBookings($branchId, $fromDate, $toDate)
    {

        $topCourts = CourtBooking::select('court_id', DB::raw('count(*) as total_bookings'), DB::raw('sum(total_price) as revenue'))
            ->whereHas('court', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->whereBetween('booking_date', [$fromDate, $toDate])
            ->groupBy('court_id')
            ->orderBy('total_bookings', 'desc')
            ->limit(10)
            ->get();


        $courtIds = $topCourts->pluck('court_id')->toArray();
        $courts = Court::whereIn('id', $courtIds)->get()->keyBy('id');

        return $topCourts->map(function ($item, $index) use ($courts) {
            $court = $courts[$item->court_id] ?? null;
            return [
                'stt' => $index + 1,
                'court_id' => $item->court_id,
                'court_name' => $court ? $court->name : __('common.unknown_court'),
                'branch_name' => $court && $court->branch ? $court->branch->name : __('common.unknown_branch'),
                'total_bookings' => $item->total_bookings,
                'revenue' => $item->revenue,
                'revenue_formatted' => number_format($item->revenue, 0, ',', '.') . ' ₫',
            ];
        });
    }

    /**
     * Export booking history data
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->back()->with('error', __('branch.no_branch'));
        }


        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $status = $request->input('status', 'all');
        $courtId = $request->input('court_id', 'all');
        $sortField = $request->input('sort_field', 'booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $format = $request->input('format', 'excel');


        $query = Booking::with(['branch', 'customer', 'user', 'courtBookings.court'])
            ->where('branch_id', $branchId);


        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_phone', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%")
                    ->orWhereHas('courtBookings.court', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            });
        }


        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }


        if ($status !== 'all') {
            $query->where('status', $status);
        }


        if ($courtId !== 'all') {
            $query->whereHas('courtBookings', function ($q) use ($courtId) {
                $q->where('court_id', $courtId);
            });
        }


        $bookingsData = $query->orderBy($sortField, $sortDirection)
            ->get()
            ->map(function ($booking) {

                $firstCourtBooking = $booking->courtBookings->first();
                $courtNames = $booking->courtBookings->pluck('court.name')->join(', ');

                return [
                    __('booking.reference') => $booking->reference_number,
                    __('booking.date') => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                    __('booking.time') => $firstCourtBooking ?
                        Carbon::parse($firstCourtBooking->start_time)->format('H:i') . ' - ' .
                        Carbon::parse($firstCourtBooking->end_time)->format('H:i') : 'N/A',
                    __('customer.customer') => $booking->customer ? $booking->customer->name : $booking->customer_name,
                    __('customer.contact') => $booking->customer ?
                        ($booking->customer->phone ?? $booking->customer->email) :
                        ($booking->customer_phone ?? $booking->customer_email ?? 'N/A'),
                    __('booking.court') => $courtNames . ' (' . $booking->courtBookings->count() . ' ' . __('booking.courts') . ')',
                    __('branch.branch') => $booking->branch ? $booking->branch->name : 'N/A',
                    __('booking.price') => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                    __('booking.status') => $this->getBookingStatusDisplay($booking->status),
                    __('booking.payment_status') => $this->getPaymentStatusDisplay($booking->payment_status),
                    __('booking.booking_type') => $booking->booking_type === 'online' ? __('booking.online') : __('booking.offline'),
                    __('common.created_at') => $booking->created_at->format('d/m/Y H:i'),
                    __('booking.notes') => $booking->notes ?? '',
                ];
            });


        $dateRangeStr = $fromDate && $toDate
            ? Carbon::parse($fromDate)->format('d-m-Y') . '_to_' . Carbon::parse($toDate)->format('d-m-Y')
            : 'all_time';
        $filename = __('booking.booking_history') . '_' . $dateRangeStr;


        $branchName = DB::table('branches')->where('id', $branchId)->value('name') ?? '';

        $exportService = new ExportService();

        switch ($format) {
            case 'excel':
                return $exportService->exportExcel($bookingsData, $filename);
            case 'pdf':
                $title = __('booking.booking_history');
                $subtitle = $branchName;
                return $exportService->exportPDF($bookingsData, $filename, $title, $subtitle);
            default:
                return redirect()->back()->with('error', __('common.unsupported_export_format'));
        }
    }

    /**
     * Get booking status display text
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusDisplay($status)
    {
        return StatusService::getBookingStatusDisplay($status);
    }

    /**
     * Get booking status CSS class
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusClass($status)
    {
        return StatusService::getBookingStatusClass($status);
    }

    /**
     * Get payment status display text
     *
     * @param string|null $status
     * @return string
     */
    private function getPaymentStatusDisplay($status)
    {
        return StatusService::getPaymentStatusDisplay($status);
    }

    /**
     * Get payment method display text
     *
     * @param string|null $method
     * @return string
     */
    private function getPaymentMethodDisplay($method)
    {
        return StatusService::getPaymentMethodDisplay($method);
    }
}
