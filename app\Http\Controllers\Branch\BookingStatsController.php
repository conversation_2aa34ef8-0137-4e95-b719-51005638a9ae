<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class BookingStatsController extends Controller
{
    /**
     * Cache TTL in minutes
     */
    const CACHE_TTL = 30;

    /**
     * Get booking statistics for a branch
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        $user = $request->user();
        if ($user->hasRole(['super-admin', 'admin'])) {
            $branchId = $request->input('branch_id', null);
            if (!$branchId) {
                $branch = \App\Models\Branch::first();
                $branchId = $branch ? $branch->id : null;
            }
        } else {
            $branchId = $user->branch_id;
        }

        if (!$branchId) {
            return response()->json([
                'error' => 'Unauthorized or no branch assigned'
            ], 403);
        }

        $today = Carbon::today()->toDateString();
        $cacheKey = "booking_stats_{$branchId}_{$today}";

        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($branchId, $today) {
            $now = Carbon::now();
            $yesterday = Carbon::yesterday();

            
            $pendingCount = $this->getPendingBookingsCount($branchId);
            $approvedTodayCount = $this->getApprovedTodayBookingsCount($branchId);
            $rejectedTodayCount = $this->getRejectedTodayBookingsCount($branchId);
            $totalTodayCount = $this->getTotalTodayBookingsCount($branchId);

            
            $pendingYesterdayCount = $this->getPendingBookingsCount($branchId, $yesterday);
            $approvedYesterdayCount = $this->getApprovedTodayBookingsCount($branchId, $yesterday);
            $rejectedYesterdayCount = $this->getRejectedTodayBookingsCount($branchId, $yesterday);
            $totalYesterdayCount = $this->getTotalTodayBookingsCount($branchId, $yesterday);

            
            $pendingChange = $this->calculatePercentageChange($pendingYesterdayCount, $pendingCount);
            $approvedChange = $this->calculatePercentageChange($approvedYesterdayCount, $approvedTodayCount);
            $rejectedChange = $this->calculatePercentageChange($rejectedYesterdayCount, $rejectedTodayCount);
            $totalChange = $this->calculatePercentageChange($totalYesterdayCount, $totalTodayCount);

            return response()->json([
                'stats' => [
                    'pending' => [
                        'count' => $pendingCount,
                        'change' => $pendingChange,
                        'label' => 'Đơn chờ xác nhận'
                    ],
                    'approved' => [
                        'count' => $approvedTodayCount,
                        'change' => $approvedChange,
                        'label' => 'Đã xác nhận hôm nay'
                    ],
                    'rejected' => [
                        'count' => $rejectedTodayCount,
                        'change' => $rejectedChange,
                        'label' => 'Đã từ chối hôm nay'
                    ],
                    'total' => [
                        'count' => $totalTodayCount,
                        'change' => $totalChange,
                        'label' => 'Tổng đơn trong ngày'
                    ],
                    'last_updated' => $now->format('Y-m-d H:i:s'),
                    'cache_expires_in' => self::CACHE_TTL . ' minutes'
                ]
            ]);
        });
    }

    /**
     * Refresh booking statistics (clear cache)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshStatistics(Request $request)
    {
        $user = $request->user();

        
        if ($user->hasRole(['super-admin', 'admin'])) {
            $branchId = $request->input('branch_id', null);
            if (!$branchId) {
                $branch = \App\Models\Branch::first();
                $branchId = $branch ? $branch->id : null;
            }
        } else {
            
            $branchId = $user->branch_id;
        }

        if (!$branchId) {
            return response()->json([
                'error' => 'Unauthorized or no branch assigned'
            ], 403);
        }

        $today = Carbon::today()->toDateString();
        $cacheKey = "booking_stats_{$branchId}_{$today}";

        Cache::forget($cacheKey);

        return $this->getStatistics($request);
    }

    /**
     * Get pending bookings count
     *
     * @param int $branchId
     * @param Carbon|null $date
     * @return int
     */
    private function getPendingBookingsCount($branchId, ?Carbon $date = null)
    {
        $query = CourtBooking::where('branch_id', $branchId)
            ->where('status', 'pending')
            ->whereNull('cancelled_at');

        if ($date) {
            $query->where('booking_date', '>=', $date->startOfDay())
                ->where('booking_date', '<=', $date->endOfDay());
        } else {
            $query->where('booking_date', '>=', Carbon::today());
        }

        return $query->count();
    }

    /**
     * Get approved bookings count for today
     *
     * @param int $branchId
     * @param Carbon|null $date
     * @return int
     */
    private function getApprovedTodayBookingsCount($branchId, ?Carbon $date = null)
    {
        $targetDate = $date ? $date : Carbon::today();

        return CourtBooking::where('branch_id', $branchId)
            ->where('status', 'confirmed')
            ->whereDate('created_at', $targetDate)
            ->whereNull('cancelled_at')
            ->count();
    }

    /**
     * Get rejected bookings count for today
     *
     * @param int $branchId
     * @param Carbon|null $date
     * @return int
     */
    private function getRejectedTodayBookingsCount($branchId, ?Carbon $date = null)
    {
        $targetDate = $date ? $date : Carbon::today();

        return CourtBooking::where('branch_id', $branchId)
            ->where('status', 'cancelled')
            ->whereDate('cancelled_at', $targetDate)
            ->count();
    }

    /**
     * Get total bookings count for today
     *
     * @param int $branchId
     * @param Carbon|null $date
     * @return int
     */
    private function getTotalTodayBookingsCount($branchId, ?Carbon $date = null)
    {
        $targetDate = $date ? $date : Carbon::today();

        return CourtBooking::where('branch_id', $branchId)
            ->whereDate('created_at', $targetDate)
            ->count();
    }

    /**
     * Calculate percentage change between two values
     *
     * @param int $oldValue
     * @param int $newValue
     * @return string
     */
    private function calculatePercentageChange($oldValue, $newValue)
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? '+100%' : '0%';
        }

        $change = (($newValue - $oldValue) / $oldValue) * 100;
        $prefix = $change >= 0 ? '+' : '';
        return $prefix . round($change, 0) . '%';
    }
}