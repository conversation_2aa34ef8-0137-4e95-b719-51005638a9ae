<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\BranchService;
use App\Models\CourtService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BranchServiceController extends Controller
{

    public function index(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;


        $branch = Branch::with('business:id,name')->findOrFail($branchId);


        $branchServices = BranchService::where('branch_id', $branchId)
            ->with([
                'courtService' => function ($query) use ($request) {
                    if ($request->input('search')) {
                        $search = $request->input('search');
                        $query->where('name', 'like', "%{$search}%")
                            ->orWhere('description', 'like', "%{$search}%");
                    }
                }
            ])
            ->when($request->input('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->whereHas('courtService', function ($query) use ($request) {
                if ($request->input('search')) {
                    $search = $request->input('search');
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                }
            })
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Branchs/Services/Index', [
            'branchServices' => $branchServices,
            'branch' => $branch,
            'filters' => $request->only(['search', 'status']),
        ]);
    }


    public function edit($branchServiceId)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;


        $branchService = BranchService::where('branch_id', $branchId)
            ->with('courtService')
            ->findOrFail($branchServiceId);

        $branch = Branch::with('business:id,name')->findOrFail($branchId);

        return Inertia::render('Branchs/Services/Edit', [
            'branchService' => $branchService,
            'branch' => $branch,
        ]);
    }


    public function update(Request $request, $branchServiceId)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        $validator = Validator::make($request->all(), [
            'price' => 'nullable|numeric|min:0',
            'member_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        $branchService = BranchService::where('branch_id', $branchId)
            ->findOrFail($branchServiceId);

        $branchService->update([
            'price' => $request->price,
            'member_price' => $request->member_price,
            'is_active' => $request->is_active,
            'description' => $request->description,
        ]);

        return redirect()->route('branch.services.index')
            ->with('message', __('service.branch_update_success'));
    }


    public function show($branchServiceId)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;


        $branchService = BranchService::where('branch_id', $branchId)
            ->with('courtService')
            ->findOrFail($branchServiceId);

        $branch = Branch::with('business:id,name')->findOrFail($branchId);


        $usage = DB::table('court_booking_services')
            ->join('court_bookings', 'court_booking_services.court_booking_id', '=', 'court_bookings.id')
            ->where('court_bookings.branch_id', $branchId)
            ->where('court_booking_services.court_service_id', $branchService->court_service_id)
            ->select(
                DB::raw('COUNT(*) as total_bookings'),
                DB::raw('SUM(court_booking_services.price * court_booking_services.quantity) as total_revenue')
            )
            ->first();

        return Inertia::render('Branchs/Services/Show', [
            'branchService' => $branchService,
            'branch' => $branch,
            'usage' => [
                'total_bookings' => $usage->total_bookings ?? 0,
                'total_revenue' => $usage->total_revenue ?? 0,
            ],
        ]);
    }


    public function bulkUpdateStatus(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        $validator = Validator::make($request->all(), [
            'services' => 'required|array',
            'services.*' => 'exists:branch_services,id',
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        BranchService::where('branch_id', $branchId)
            ->whereIn('id', $request->services)
            ->update(['is_active' => $request->status]);

        return back()->with('message', __('service.status_updated'));
    }


    public function getActiveServices()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        $services = BranchService::where('branch_id', $branchId)
            ->where('is_active', true)
            ->with('courtService:id,name,description')
            ->get()
            ->map(function ($branchService) {
                return [
                    'id' => $branchService->id,
                    'court_service_id' => $branchService->court_service_id,
                    'name' => $branchService->courtService->name,
                    'description' => $branchService->description ?? $branchService->courtService->description,
                    'price' => $branchService->price,
                    'member_price' => $branchService->member_price,
                ];
            });

        return response()->json($services);
    }
}
