<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Branch;
use App\Models\Business;
use App\Models\CourtBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers for the branch.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId) {
                return redirect()->route('unauthorized');
            }

            $branch = Branch::findOrFail($branchId);

            
            DB::statement("SET SQL_MODE=''");

            $query = Customer::query()
                ->leftJoin('court_bookings', function ($join) use ($branchId) {
                    $join->on('customers.id', '=', 'court_bookings.customer_id')
                        ->where('court_bookings.branch_id', '=', $branchId);
                })
                ->select(
                    'customers.*',
                    DB::raw('COUNT(DISTINCT court_bookings.id) as booking_count'),
                    DB::raw('SUM(CASE WHEN court_bookings.branch_id = ' . $branchId . ' THEN court_bookings.total_price ELSE 0 END) as total_spent')
                )
                ->groupBy('customers.id');

            
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('customers.name', 'like', "%{$searchTerm}%")
                        ->orWhere('customers.email', 'like', "%{$searchTerm}%")
                        ->orWhere('customers.phone', 'like', "%{$searchTerm}%");
                });
            }

            
            if ($request->has('membership') && $request->membership !== 'all') {
                $membershipValue = $request->membership === 'member' ? true : false;
                $query->where('customers.is_member', $membershipValue);
            }

            
            $sortField = $request->input('sort_field', 'customers.created_at');
            $sortDirection = $request->input('sort_direction', 'desc');

            
            if ($sortField === 'booking_count' || $sortField === 'total_spent') {
                $query->orderBy($sortField, $sortDirection);
            } else {
                $query->orderBy($sortField, $sortDirection);
            }

            
            $customers = $query->having('booking_count', '>', 0)
                ->paginate(15)
                ->withQueryString();

            return Inertia::render('Branchs/Customers/Index', [
                'customers' => $customers,
                'branch' => $branch,
                'filters' => [
                    'search' => $request->search,
                    'membership' => $request->membership ?? 'all',
                ],
                'sort' => [
                    'field' => $sortField,
                    'direction' => $sortDirection
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in customer index: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi tải danh sách khách hàng.');
        }
    }

    /**
     * Search for customers by name, email or phone.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        try {
            $query = $request->input('query');
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId) {
                return response()->json([
                    'message' => 'Branch not found',
                    'customers' => []
                ], 404);
            }

            $branch = Branch::find($branchId);

            if (empty($query) || strlen($query) < 2) {
                return response()->json([
                    'message' => 'Search query too short',
                    'customers' => []
                ], 200);
            }


            $customers = Customer::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                    ->orWhere('email', 'like', "%{$query}%")
                    ->orWhere('phone', 'like', "%{$query}%");
            })
                ->select('id', 'name', 'email', 'phone', 'created_at')
                ->orderByRaw("CASE WHEN name LIKE '{$query}%' THEN 1 
                              WHEN phone LIKE '{$query}%' THEN 2 
                              WHEN email LIKE '{$query}%' THEN 3 
                              ELSE 4 END")
                ->orderBy('name')
                ->limit(10)
                ->get();

            return response()->json([
                'message' => 'Customers found',
                'customers' => $customers
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error searching customers: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error searching customers',
                'error' => $e->getMessage(),
                'customers' => []
            ], 500);
        }
    }

    /**
     * Show the specified customer details
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId) {
                return redirect()->route('unauthorized');
            }

            $customer = Customer::findOrFail($id);
            $branch = Branch::findOrFail($branchId);

            
            $bookings = CourtBooking::with(['court'])
                ->where('branch_id', $branchId)
                ->where('customer_id', $id)
                ->orWhere(function ($query) use ($customer, $branchId) {
                    
                    $query->where('branch_id', $branchId)
                        ->where(function ($q) use ($customer) {
                        if (!empty($customer->phone)) {
                            $q->where('customer_phone', $customer->phone);
                        }
                        if (!empty($customer->email)) {
                            $q->orWhere('customer_email', $customer->email);
                        }
                    });
                })
                ->orderBy('booking_date', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            
            $totalBookings = $bookings->total();
            $totalSpent = $bookings->sum('total_price');
            $lastBooking = $bookings->first();

            return Inertia::render('Branchs/Customers/Show', [
                'customer' => $customer,
                'branch' => $branch,
                'bookings' => $bookings,
                'stats' => [
                    'total_bookings' => $totalBookings,
                    'total_spent' => $totalSpent,
                    'last_booking_date' => $lastBooking ? $lastBooking->booking_date : null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error in customer show: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi tải thông tin khách hàng.');
        }
    }
}