<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Customer;
use App\Models\CourtBooking;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CustomerReportController extends Controller
{
    /**
     * Display the customer reports page
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }


        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $customerType = $request->input('customer_type', 'all');
        $sortField = $request->input('sort_field', 'last_booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);


        
        $registeredUsers = DB::table('users')
            ->select(
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                'users.created_at',
                DB::raw("'registered' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as booking_count'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date'),
                DB::raw('SUM(court_bookings.total_price) as total_spent')
            )
            ->join('court_bookings', 'users.id', '=', 'court_bookings.user_id')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('users.name', 'like', "%{$search}%")
                        ->orWhere('users.email', 'like', "%{$search}%")
                        ->orWhere('users.phone', 'like', "%{$search}%");
                });
            })
            ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('court_bookings.booking_date', [$fromDate, $toDate]);
            })
            ->groupBy('users.id', 'users.name', 'users.email', 'users.phone', 'users.created_at');


        
        
        $walkInCustomers = DB::table('customers')
            ->select(
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                'customers.created_at',
                DB::raw("'walk-in' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as booking_count'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date'),
                DB::raw('SUM(court_bookings.total_price) as total_spent')
            )
            ->join('court_bookings', 'customers.id', '=', 'court_bookings.customer_id')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->whereNull('customers.user_id') 
            ->where('courts.branch_id', $branchId)
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('customers.name', 'like', "%{$search}%")
                        ->orWhere('customers.email', 'like', "%{$search}%")
                        ->orWhere('customers.phone', 'like', "%{$search}%");
                });
            })
            ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('court_bookings.booking_date', [$fromDate, $toDate]);
            })
            ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.phone', 'customers.created_at');


        if ($customerType === 'registered') {
            $customers = $registeredUsers;
        } elseif ($customerType === 'walk-in') {
            $customers = $walkInCustomers;
        } else {
            $customers = $registeredUsers->union($walkInCustomers);
        }


        $orderByDirection = $sortDirection === 'asc' ? 'asc' : 'desc';
        $orderByField = $sortField;

        if (!in_array($sortField, ['name', 'email', 'phone', 'booking_count', 'total_spent', 'last_booking_date', 'created_at'])) {
            $orderByField = 'last_booking_date';
        }

        $customers = $customers->orderBy($orderByField, $orderByDirection);


        $customersCollection = collect($customers->get());


        $formattedCustomers = $customersCollection->map(function ($customer) {
            return [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'customer_type' => $customer->customer_type,
                'customer_type_display' => $customer->customer_type === 'registered' ? 'Đã đăng ký' : 'Khách vãng lai',
                'booking_count' => $customer->booking_count,
                'total_spent' => $customer->total_spent,
                'total_spent_formatted' => number_format($customer->total_spent, 0, ',', '.') . ' ₫',
                'last_booking_date' => $customer->last_booking_date ? Carbon::parse($customer->last_booking_date)->format('d/m/Y') : null,
                'created_at' => Carbon::parse($customer->created_at)->format('d/m/Y'),
                'average_booking_value' => $customer->booking_count > 0 ? $customer->total_spent / $customer->booking_count : 0,
                'average_booking_value_formatted' => $customer->booking_count > 0 ? number_format($customer->total_spent / $customer->booking_count, 0, ',', '.') . ' ₫' : '0 ₫',
            ];
        });


        $total = $formattedCustomers->count();
        $pageStart = ($page - 1) * $perPage;
        $pageItems = $formattedCustomers->slice($pageStart, $perPage)->values();


        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $pageItems,
            $total,
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $stats = $this->getCustomerStats($branchId, $fromDate, $toDate);

        
        $topCustomers = $this->getTopCustomersByRevenue($branchId, $fromDate ?: Carbon::now()->subYear()->toDateString(), $toDate ?: Carbon::now()->toDateString());

        $customerTypes = [
            ['value' => 'all', 'label' => 'Tất cả khách hàng'],
            ['value' => 'registered', 'label' => 'Khách hàng đã đăng ký'],
            ['value' => 'walk-in', 'label' => 'Khách hàng vãng lai'],
        ];

        return Inertia::render('Branchs/Reports/Customers', [
            'customers' => $paginator,
            'filters' => [
                'search' => $search,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'customer_type' => $customerType,
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
                'per_page' => $perPage
            ],
            'customer_types' => $customerTypes,
            'summary' => [
                'total_customers' => $stats['total_customers'],
                'registered_customers' => $stats['registered_customers'],
                'walk_in_customers' => $stats['walk_in_customers'],
                'new_customers' => $stats['new_customers'],
                'active_customers' => $stats['active_customers'],
                'avg_customer_spend' => $stats['avg_customer_spend'],
                'avg_customer_spend_formatted' => $stats['avg_customer_spend_formatted'],
            ],
            'date_range' => $fromDate && $toDate ? [
                'from' => $fromDate,
                'to' => $toDate,
                'formatted' => Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y')
            ] : null,
            'stats' => $stats,
            'top_customers' => $topCustomers
        ]);
    }

    /**
     * Get details of a specific customer
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerDetails(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return response()->json(['error' => 'No branch assigned'], 403);
        }

        $customerId = $request->input('customer_id');
        $customerType = $request->input('customer_type');

        if ($customerType === 'registered') {
            $customer = User::find($customerId);

            if (!$customer) {
                return response()->json(['error' => 'Customer not found'], 404);
            }


            $bookings = CourtBooking::with(['court.branch'])
                ->whereHas('court', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                })
                ->where('user_id', $customerId)
                ->orderBy('booking_date', 'desc')
                ->get()
                ->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'reference_number' => $booking->reference_number,
                        'booking_date' => $booking->booking_date->format('d/m/Y'),
                        'booking_time' => $booking->start_time->format('H:i') . ' - ' . $booking->end_time->format('H:i'),
                        'court_name' => $booking->court->name,
                        'branch_name' => $booking->court->branch->name,
                        'total_price' => $booking->total_price,
                        'total_price_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                        'status' => $booking->status,
                        'status_display' => $this->getBookingStatusDisplay($booking->status),
                        'status_class' => $this->getBookingStatusClass($booking->status),
                        'created_at' => $booking->created_at->format('d/m/Y H:i'),
                    ];
                });

            $customerData = [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'customer_type' => 'registered',
                'customer_type_display' => 'Đã đăng ký',
                'created_at' => $customer->created_at->format('d/m/Y'),
                'booking_count' => $bookings->count(),
                'total_spent' => $bookings->sum('total_price'),
                'total_spent_formatted' => number_format($bookings->sum('total_price'), 0, ',', '.') . ' ₫',
                'bookings' => $bookings
            ];
        } else {
            $customer = Customer::find($customerId);

            if (!$customer) {
                return response()->json(['error' => 'Customer not found'], 404);
            }


            $bookings = CourtBooking::with(['court.branch'])
                ->whereHas('court', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                })
                ->where('customer_id', $customerId)
                ->orderBy('booking_date', 'desc')
                ->get()
                ->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'reference_number' => $booking->reference_number,
                        'booking_date' => $booking->booking_date->format('d/m/Y'),
                        'booking_time' => $booking->start_time->format('H:i') . ' - ' . $booking->end_time->format('H:i'),
                        'court_name' => $booking->court->name,
                        'branch_name' => $booking->court->branch->name,
                        'total_price' => $booking->total_price,
                        'total_price_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                        'status' => $booking->status,
                        'status_display' => $this->getBookingStatusDisplay($booking->status),
                        'status_class' => $this->getBookingStatusClass($booking->status),
                        'created_at' => $booking->created_at->format('d/m/Y H:i'),
                    ];
                });

            $customerData = [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'customer_type' => 'walk-in',
                'customer_type_display' => 'Khách vãng lai',
                'created_at' => $customer->created_at->format('d/m/Y'),
                'booking_count' => $bookings->count(),
                'total_spent' => $bookings->sum('total_price'),
                'total_spent_formatted' => number_format($bookings->sum('total_price'), 0, ',', '.') . ' ₫',
                'bookings' => $bookings
            ];
        }

        return response()->json($customerData);
    }

    /**
     * Helper function to get booking status display text
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusDisplay($status)
    {
        switch ($status) {
            case 'pending':
                return __('common.status_pending');
            case 'confirmed':
                return __('common.status_confirmed');
            case 'completed':
                return __('common.status_completed');
            case 'cancelled':
                return __('common.status_cancelled');
            case 'no_show':
                return __('common.status_no_show');
            default:
                return ucfirst($status);
        }
    }

    /**
     * Helper function to get booking status CSS class
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusClass($status)
    {
        switch ($status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'no_show':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Get customer statistics for the branch
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    private function getCustomerStats($branchId, $fromDate, $toDate, $period = 'monthly')
    {
        $from = Carbon::parse($fromDate)->startOfDay();
        $to = Carbon::parse($toDate)->endOfDay();


        $daysDiff = $from->diffInDays($to);
        $prevFrom = $from->copy()->subDays($daysDiff + 1);
        $prevTo = $from->copy()->subDays(1);


        
        $registeredCustomers = User::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereHas('roles', function ($query) {
                $query->where('name', 'user');
            })
            ->count();


        
        $walkInCustomers = Customer::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereNull('user_id')
            ->count();


        
        $newRegisteredCustomers = User::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereHas('roles', function ($query) {
                $query->where('name', 'user');
            })
            ->whereBetween('created_at', [$from, $to])
            ->count();

        
        $newWalkInCustomers = Customer::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereNull('user_id')
            ->whereBetween('created_at', [$from, $to])
            ->count();

        $newCustomers = $newRegisteredCustomers + $newWalkInCustomers;


        
        $prevNewRegisteredCustomers = User::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereHas('roles', function ($query) {
                $query->where('name', 'user');
            })
            ->whereBetween('created_at', [$prevFrom, $prevTo])
            ->count();

        
        $prevNewWalkInCustomers = Customer::whereHas('bookings', function ($query) use ($branchId) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        })
            ->whereNull('user_id')
            ->whereBetween('created_at', [$prevFrom, $prevTo])
            ->count();

        $prevNewCustomers = $prevNewRegisteredCustomers + $prevNewWalkInCustomers;


        $newCustomerPercentage = 0;
        if ($prevNewCustomers > 0) {
            $newCustomerPercentage = round((($newCustomers - $prevNewCustomers) / $prevNewCustomers) * 100, 1);
        }


        $activeDate = Carbon::now()->subDays(30);
        
        $activeRegisteredCustomers = User::whereHas('bookings', function ($query) use ($branchId, $activeDate) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
                ->where('booking_date', '>=', $activeDate);
        })
            ->whereHas('roles', function ($query) {
                $query->where('name', 'user');
            })
            ->count();

        
        $activeWalkInCustomers = Customer::whereHas('bookings', function ($query) use ($branchId, $activeDate) {
            $query->whereHas('court', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
                ->where('booking_date', '>=', $activeDate);
        })
            ->whereNull('user_id')
            ->count();

        $activeCustomers = $activeRegisteredCustomers + $activeWalkInCustomers;


        $totalCustomers = $registeredCustomers + $walkInCustomers;
        $inactiveCustomers = $totalCustomers - $activeCustomers - $newCustomers;
        if ($inactiveCustomers < 0)
            $inactiveCustomers = 0;


        $customerTrends = $this->getCustomerTrends($branchId, $fromDate, $toDate, $period);


        
        $registeredCustomerFrequency = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.user_id')
            ->select('court_bookings.user_id', DB::raw('count(*) as booking_count'))
            ->groupBy('court_bookings.user_id')
            ->get();

        
        $walkInCustomerFrequency = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.customer_id')
            ->whereNull('customers.user_id')
            ->select('court_bookings.customer_id', DB::raw('count(*) as booking_count'))
            ->groupBy('court_bookings.customer_id')
            ->get();


        $oneTimeCustomers = $registeredCustomerFrequency->where('booking_count', 1)->count() +
            $walkInCustomerFrequency->where('booking_count', 1)->count();

        $casualCustomers = $registeredCustomerFrequency->where('booking_count', '>=', 2)
            ->where('booking_count', '<=', 3)->count() +
            $walkInCustomerFrequency->where('booking_count', '>=', 2)
                ->where('booking_count', '<=', 3)->count();

        $regularCustomers = $registeredCustomerFrequency->where('booking_count', '>=', 4)
            ->where('booking_count', '<=', 10)->count() +
            $walkInCustomerFrequency->where('booking_count', '>=', 4)
                ->where('booking_count', '<=', 10)->count();

        $loyalCustomers = $registeredCustomerFrequency->where('booking_count', '>', 10)->count() +
            $walkInCustomerFrequency->where('booking_count', '>', 10)->count();


        $regularCustomerPercentage = 0;
        if ($totalCustomers > 0) {
            $regularCustomerPercentage = round((($regularCustomers + $loyalCustomers) / $totalCustomers) * 100, 1);
        }


        
        $totalRegisteredSpend = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.user_id')
            ->where('court_bookings.status', 'completed')
            ->sum('court_bookings.total_price');

        
        $totalWalkInSpend = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.customer_id')
            ->whereNull('customers.user_id')
            ->where('court_bookings.status', 'completed')
            ->sum('court_bookings.total_price');

        $totalSpend = $totalRegisteredSpend + $totalWalkInSpend;
        $avgCustomerSpend = $totalCustomers > 0 ? round($totalSpend / $totalCustomers, 0) : 0;


        
        $prevTotalRegisteredSpend = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.user_id')
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$prevFrom, $prevTo])
            ->sum('court_bookings.total_price');

        
        $prevTotalWalkInSpend = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.customer_id')
            ->whereNull('customers.user_id')
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$prevFrom, $prevTo])
            ->sum('court_bookings.total_price');

        $prevTotalSpend = $prevTotalRegisteredSpend + $prevTotalWalkInSpend;


        $prevTotalCustomers = $registeredCustomers + $walkInCustomers - $newCustomers;
        if ($prevTotalCustomers <= 0)
            $prevTotalCustomers = 1;

        $prevAvgCustomerSpend = round($prevTotalSpend / $prevTotalCustomers, 0);


        $avgSpendChange = 0;
        if ($prevAvgCustomerSpend > 0) {
            $avgSpendChange = round((($avgCustomerSpend - $prevAvgCustomerSpend) / $prevAvgCustomerSpend) * 100, 1);
        }


        $acquisitionSources = [
            ['name' => 'Trực tiếp', 'count' => intval($totalCustomers * 0.6)],
            ['name' => 'Website', 'count' => intval($totalCustomers * 0.25)],
            ['name' => 'Giới thiệu', 'count' => intval($totalCustomers * 0.1)],
            ['name' => 'Khác', 'count' => $totalCustomers - intval($totalCustomers * 0.6) - intval($totalCustomers * 0.25) - intval($totalCustomers * 0.1)]
        ];


        
        $topRegisteredCustomers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.user_id')
            ->select(
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                DB::raw('count(*) as booking_count'),
                DB::raw('sum(court_bookings.total_price) as total_spent'),
                DB::raw('max(court_bookings.booking_date) as last_booking_date')
            )
            ->groupBy('users.id', 'users.name', 'users.email', 'users.phone')
            ->orderBy('total_spent', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'email' => $item->email,
                    'phone' => $item->phone,
                    'bookings' => $item->booking_count,
                    'total_spent' => $item->total_spent,
                    'total_spent_formatted' => number_format($item->total_spent, 0, ',', '.') . ' ₫',
                    'last_booking_date' => Carbon::parse($item->last_booking_date)->format('d/m/Y'),
                    'type' => 'registered'
                ];
            });

        
        $topWalkInCustomers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('courts.branch_id', $branchId)
            ->whereNotNull('court_bookings.customer_id')
            ->whereNull('customers.user_id')
            ->select(
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                DB::raw('count(*) as booking_count'),
                DB::raw('sum(court_bookings.total_price) as total_spent'),
                DB::raw('max(court_bookings.booking_date) as last_booking_date')
            )
            ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.phone')
            ->orderBy('total_spent', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'email' => $item->email,
                    'phone' => $item->phone,
                    'bookings' => $item->booking_count,
                    'total_spent' => $item->total_spent,
                    'total_spent_formatted' => number_format($item->total_spent, 0, ',', '.') . ' ₫',
                    'last_booking_date' => Carbon::parse($item->last_booking_date)->format('d/m/Y'),
                    'type' => 'walk-in'
                ];
            });


        $topCustomers = $topRegisteredCustomers->concat($topWalkInCustomers)
            ->sortByDesc('total_spent')
            ->values()
            ->take(10);


        return [
            'total_customers' => $totalCustomers,
            'registered_customers' => $registeredCustomers,
            'walk_in_customers' => $walkInCustomers,
            'new_customers' => $newCustomers,
            'new_customer_percentage' => $newCustomerPercentage,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $inactiveCustomers,
            'one_time_customers' => $oneTimeCustomers,
            'casual_customers' => $casualCustomers,
            'regular_customers' => $regularCustomers,
            'loyal_customers' => $loyalCustomers,
            'regular_customer_percentage' => $regularCustomerPercentage,
            'avg_customer_spend' => $avgCustomerSpend,
            'avg_customer_spend_formatted' => number_format($avgCustomerSpend, 0, ',', '.') . ' ₫',
            'avg_spend_change' => $avgSpendChange,
            'customer_trends' => $customerTrends,
            'acquisition_sources' => $acquisitionSources,
            'top_customers' => $topCustomers
        ];
    }

    /**
     * Get top 10 customers by revenue
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getTopCustomersByRevenue($branchId, $fromDate, $toDate)
    {
        
        $registeredUsers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->whereBetween('court_bookings.booking_date', [$fromDate, $toDate])
            ->whereNotNull('court_bookings.user_id')
            ->select(
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                DB::raw("'registered' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as total_bookings'),
                DB::raw('SUM(court_bookings.total_price) as revenue')
            )
            ->groupBy('users.id', 'users.name', 'users.email', 'users.phone')
            ->orderBy('revenue', 'desc');

        
        $walkInCustomers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('courts.branch_id', $branchId)
            ->whereBetween('court_bookings.booking_date', [$fromDate, $toDate])
            ->whereNotNull('court_bookings.customer_id')
            ->whereNull('customers.user_id') 
            ->select(
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                DB::raw("'walk-in' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as total_bookings'),
                DB::raw('SUM(court_bookings.total_price) as revenue')
            )
            ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.phone')
            ->orderBy('revenue', 'desc');

        
        $topCustomers = $registeredUsers->union($walkInCustomers)
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        return $topCustomers->map(function ($customer, $index) {
            return [
                'stt' => $index + 1,
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email ?: '-',
                'phone' => $customer->phone ?: '-',
                'customer_type' => $customer->customer_type,
                'customer_type_display' => $customer->customer_type === 'registered' ? 'Đã đăng ký' : 'Khách vãng lai',
                'total_bookings' => $customer->total_bookings,
                'revenue' => $customer->revenue,
                'revenue_formatted' => number_format($customer->revenue, 0, ',', '.') . ' ₫'
            ];
        });
    }

    /**
     * Get customer trends over time
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    private function getCustomerTrends($branchId, $fromDate, $toDate, $period = 'monthly')
    {
        $from = Carbon::parse($fromDate);
        $to = Carbon::parse($toDate);
        $diffInDays = $from->diffInDays($to) + 1;

        $labels = [];
        $newCustomersData = [];
        $totalCustomersData = [];

        if ($period === 'daily' || $diffInDays <= 31) {

            $current = $from->copy();
            while ($current->lte($to)) {
                $labels[] = $current->format('d/m');
                $currentDate = $current->format('Y-m-d');

                
                $newRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->whereDate('created_at', $currentDate)
                    ->count();

                
                $newWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->whereDate('created_at', $currentDate)
                    ->count();

                $newCustomersData[] = $newRegistered + $newWalkIn;

                
                $totalRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->where('created_at', '<=', $current->copy()->endOfDay())
                    ->count();

                
                $totalWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->where('created_at', '<=', $current->copy()->endOfDay())
                    ->count();

                $totalCustomersData[] = $totalRegistered + $totalWalkIn;

                $current->addDay();
            }
        } elseif ($period === 'weekly' || $diffInDays <= 90) {

            $current = $from->copy()->startOfWeek();
            while ($current->lte($to)) {
                $weekEnd = $current->copy()->endOfWeek();
                if ($weekEnd->gt($to)) {
                    $weekEnd = $to->copy();
                }

                $labels[] = $current->format('d/m') . '-' . $weekEnd->format('d/m');

                
                $newRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->whereBetween('created_at', [$current->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->count();

                
                $newWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->whereBetween('created_at', [$current->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->count();

                $newCustomersData[] = $newRegistered + $newWalkIn;

                
                $totalRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->where('created_at', '<=', $weekEnd->copy()->endOfDay())
                    ->count();

                
                $totalWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->where('created_at', '<=', $weekEnd->copy()->endOfDay())
                    ->count();

                $totalCustomersData[] = $totalRegistered + $totalWalkIn;

                $current->addWeek();
            }
        } else {

            $current = $from->copy()->startOfMonth();
            while ($current->lte($to)) {
                $monthEnd = $current->copy()->endOfMonth();
                if ($monthEnd->gt($to)) {
                    $monthEnd = $to->copy();
                }

                $labels[] = $current->format('m/Y');

                
                $newRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->whereYear('created_at', $current->year)
                    ->whereMonth('created_at', $current->month)
                    ->count();

                
                $newWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->whereYear('created_at', $current->year)
                    ->whereMonth('created_at', $current->month)
                    ->count();

                $newCustomersData[] = $newRegistered + $newWalkIn;

                
                $totalRegistered = User::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereHas('roles', function ($query) {
                        $query->where('name', 'user');
                    })
                    ->where('created_at', '<=', $monthEnd->copy()->endOfDay())
                    ->count();

                
                $totalWalkIn = Customer::whereHas('bookings', function ($query) use ($branchId) {
                    $query->whereHas('court', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    });
                })
                    ->whereNull('user_id')
                    ->where('created_at', '<=', $monthEnd->copy()->endOfDay())
                    ->count();

                $totalCustomersData[] = $totalRegistered + $totalWalkIn;

                $current->addMonth();
            }
        }

        return [
            'labels' => $labels,
            'new_customers' => $newCustomersData,
            'total_customers' => $totalCustomersData
        ];
    }

    /**
     * Export customer statistics
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->back()->with('error', __('branch.no_branch'));
        }

        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $customerType = $request->input('customer_type', 'all');
        $sortField = $request->input('sort_field', 'last_booking_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $format = $request->input('format', 'excel');


        
        $registeredUsers = DB::table('users')
            ->select(
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                'users.created_at',
                DB::raw("'registered' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as booking_count'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date'),
                DB::raw('SUM(court_bookings.total_price) as total_spent')
            )
            ->join('court_bookings', 'users.id', '=', 'court_bookings.user_id')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('model_has_roles', function ($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                    ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', '=', 'user')
            ->where('courts.branch_id', $branchId)
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('users.name', 'like', "%{$search}%")
                        ->orWhere('users.email', 'like', "%{$search}%")
                        ->orWhere('users.phone', 'like', "%{$search}%");
                });
            })
            ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('court_bookings.booking_date', [$fromDate, $toDate]);
            })
            ->groupBy('users.id', 'users.name', 'users.email', 'users.phone', 'users.created_at');


        
        $walkInCustomers = DB::table('customers')
            ->select(
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                'customers.created_at',
                DB::raw("'walk-in' as customer_type"),
                DB::raw('COUNT(DISTINCT court_bookings.id) as booking_count'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date'),
                DB::raw('SUM(court_bookings.total_price) as total_spent')
            )
            ->join('court_bookings', 'customers.id', '=', 'court_bookings.customer_id')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->where('courts.branch_id', $branchId)
            ->whereNull('customers.user_id') 
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('customers.name', 'like', "%{$search}%")
                        ->orWhere('customers.email', 'like', "%{$search}%")
                        ->orWhere('customers.phone', 'like', "%{$search}%");
                });
            })
            ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('court_bookings.booking_date', [$fromDate, $toDate]);
            })
            ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.phone', 'customers.created_at');


        if ($customerType === 'registered') {
            $customers = $registeredUsers;
        } elseif ($customerType === 'walk-in') {
            $customers = $walkInCustomers;
        } else {
            $customers = $registeredUsers->union($walkInCustomers);
        }


        $orderByDirection = $sortDirection === 'asc' ? 'asc' : 'desc';
        $orderByField = $sortField;

        if (!in_array($sortField, ['name', 'email', 'phone', 'booking_count', 'total_spent', 'last_booking_date', 'created_at'])) {
            $orderByField = 'last_booking_date';
        }

        $customers = $customers->orderBy($orderByField, $orderByDirection);


        $customersCollection = collect($customers->get());


        $customersData = $customersCollection->map(function ($customer, $index) {
            return [
                'index' => $index + 1,
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email ?: '-',
                'phone' => $customer->phone ?: '-',
                'customer_type' => $customer->customer_type === 'registered' ? 'Đã đăng ký' : 'Khách vãng lai',
                'booking_count' => $customer->booking_count,
                'total_spent' => $customer->total_spent,
                'total_spent_formatted' => number_format($customer->total_spent, 0, ',', '.') . ' ₫',
                'last_booking_date' => $customer->last_booking_date ? Carbon::parse($customer->last_booking_date)->format('d/m/Y') : '-',
                'created_at' => Carbon::parse($customer->created_at)->format('d/m/Y'),
                'average_booking_value' => $customer->booking_count > 0 ? $customer->total_spent / $customer->booking_count : 0,
                'average_booking_value_formatted' => $customer->booking_count > 0 ? number_format($customer->total_spent / $customer->booking_count, 0, ',', '.') . ' ₫' : '0 ₫',
            ];
        })->toArray();


        $dateRangeStr = $fromDate && $toDate
            ? Carbon::parse($fromDate)->format('d-m-Y') . '_to_' . Carbon::parse($toDate)->format('d-m-Y')
            : 'all_time';
        $filename = 'customer_report_' . $dateRangeStr . '.' . ($format === 'excel' ? 'xlsx' : $format);


        switch ($format) {
            case 'excel':
                return $this->exportToExcelSimple($customersData, $filename);
            case 'pdf':
                return $this->exportToPdfSimple($customersData, $filename);
            default:
                return redirect()->back()->with('error', 'Unsupported export format');
        }
    }

    /**
     * Export to Excel format (simple version - just the customer list)
     *
     * @param array $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcelSimple($data, $filename)
    {

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Danh sách khách hàng');


        $sheet->setCellValue('A1', 'STT');
        $sheet->setCellValue('B1', 'Tên khách hàng');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Số điện thoại');
        $sheet->setCellValue('E1', 'Loại khách hàng');
        $sheet->setCellValue('F1', 'Số lượt đặt');
        $sheet->setCellValue('G1', 'Tổng chi tiêu');
        $sheet->setCellValue('H1', 'Trung bình');
        $sheet->setCellValue('I1', 'Lần đặt cuối');
        $sheet->setCellValue('J1', 'Ngày tạo');


        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'E2EFDA',
                ],
            ],
        ];
        $sheet->getStyle('A1:J1')->applyFromArray($headerStyle);


        $row = 2;
        foreach ($data as $item) {
            $sheet->setCellValue('A' . $row, $item['index']);
            $sheet->setCellValue('B' . $row, $item['name']);
            $sheet->setCellValue('C' . $row, $item['email']);
            $sheet->setCellValue('D' . $row, $item['phone']);
            $sheet->setCellValue('E' . $row, $item['customer_type']);
            $sheet->setCellValue('F' . $row, $item['booking_count']);
            $sheet->setCellValue('G' . $row, $item['total_spent_formatted']);
            $sheet->setCellValue('H' . $row, $item['average_booking_value_formatted']);
            $sheet->setCellValue('I' . $row, $item['last_booking_date']);
            $sheet->setCellValue('J' . $row, $item['created_at']);
            $row++;
        }


        foreach (range('A', 'J') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }


        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($tempFile);


        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export to PDF format (simple version - just the customer list)
     *
     * @param array $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdfSimple($data, $filename)
    {
        $html = '<html><head>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <style>
                body { font-family: DejaVu Sans, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th { background-color: #e2efda; font-weight: bold; text-align: left; padding: 8px; border: 1px solid #ddd; }
                td { padding: 8px; border: 1px solid #ddd; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                h1 { text-align: center; }
                .footer { text-align: center; font-size: 0.8em; margin-top: 20px; }
            </style>
        </head><body>';

        $html .= '<h1>Báo cáo danh sách khách hàng</h1>';

        $html .= '<table>';
        $html .= '<thead><tr>
            <th>STT</th>
                            <th>Tên khách hàng</th>
                            <th>Liên hệ</th>
                            <th>Loại</th>
            <th>Lượt đặt</th>
                            <th>Tổng chi tiêu</th>
            <th>Lần đặt cuối</th>
        </tr></thead>';

        $html .= '<tbody>';
        foreach ($data as $item) {
            $html .= '<tr>';
            $html .= '<td>' . $item['index'] . '</td>';
            $html .= '<td>' . $item['name'] . '</td>';
            $html .= '<td>' . $item['phone'] . '<br><small>' . $item['email'] . '</small></td>';
            $html .= '<td>' . $item['customer_type'] . '</td>';
            $html .= '<td>' . $item['booking_count'] . '</td>';
            $html .= '<td>' . $item['total_spent_formatted'] . '<br><small>TB: ' . $item['average_booking_value_formatted'] . '</small></td>';
            $html .= '<td>' . $item['last_booking_date'] . '</td>';
            $html .= '</tr>';
        }
        $html .= '</tbody></table>';

        $html .= '<div class="footer">Báo cáo được tạo lúc: ' . now()->format('d/m/Y H:i:s') . '</div>';
        $html .= '</body></html>';


        $dompdf = new \Dompdf\Dompdf();
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($html);
        $dompdf->render();


        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $dompdf->output());


        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }
}
