<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

class DashboardController extends Controller
{
    /**
     * Display the branch dashboard
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }


        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {

            $stats = $this->getCustomRangeStats($branchId, $fromDate, $toDate);


            Log::info("Branch Dashboard Custom Range Stats", [
                'branch_id' => $branchId,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'stats' => $stats
            ]);

            return Inertia::render('Branch/Dashboard', [
                'stats' => $stats,
                'is_custom_range' => true,
                'custom_range' => [
                    'from' => $fromDate,
                    'to' => $toDate,
                    'formatted' => Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y')
                ],
                'last_updated' => now()->format('Y-m-d H:i:s')
            ]);
        }


        $period = $request->input('period', null);
        $stats = $this->getDashboardStats($branchId, $period);


        Log::info("Branch Dashboard Stats (Direct Calculation)", [
            'branch_id' => $branchId,
            'period' => $period,
            'stats' => $stats
        ]);

        return Inertia::render('Branchs/Dashboard', [
            'stats' => $stats,
            'last_updated' => now()->format('Y-m-d H:i:s'),
            'current_period' => $period
        ]);
    }

    /**
     * Get dashboard statistics for the Branch
     *
     * @param int $branchId
     * @param string $period
     * @return array
     */
    public function getDashboardStats($branchId, $period = null)
    {
        $now = Carbon::now();
        $previousMonth = $now->copy()->subMonth();
        $currentMonth = $now->copy()->startOfMonth();

        // If period is null, default to monthly for calculations but don't show as selected in UI
        $calculationPeriod = $period ?? 'monthly';

        $totalCourts = Court::where('branch_id', $branchId)->count();
        $activeCourts = Court::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->count();

        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;


        $totalUsers = User::where('branch_id', $branchId)->count();
        $previousMonthUsers = User::where('branch_id', $branchId)
            ->where('created_at', '<', $currentMonth)
            ->count();
        $newUsers = $totalUsers - $previousMonthUsers;
        $userGrowth = $previousMonthUsers > 0 ? round(($newUsers / $previousMonthUsers) * 100, 1) : 0;


        $totalRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->sum('total_price');


        $currentMonthRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->sum('total_price');


        $previousMonthRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->whereYear('booking_date', $previousMonth->year)
            ->whereMonth('booking_date', $previousMonth->month)
            ->sum('total_price');

        $revenueGrowth = $previousMonthRevenue > 0 ? round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1) : 0;


        $chartData = $this->getChartDataByPeriod($branchId, $calculationPeriod, $now);
        $labels = $chartData['labels'];
        $bookings = $chartData['bookings'];
        $revenue = $chartData['revenue'];


        $weeklyData = [];
        $startOfMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();
        $currentDate = $startOfMonth->copy();

        while ($currentDate->lte($endOfMonth)) {
            $weekStart = $currentDate->copy()->startOfWeek();
            $weekEnd = $currentDate->copy()->endOfWeek();

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $weeklyBookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

            $weeklyRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

            $weeklyData[] = [
                'week' => __('dashboard.week') . ' ' . $weekStart->weekOfMonth,
                'start_date' => $weekStart->format('d/m/Y'),
                'end_date' => $weekEnd->format('d/m/Y'),
                'bookings' => $weeklyBookings,
                'revenue' => $weeklyRevenue,
                'date_range' => $weekStart->format('d/m') . ' - ' . $weekEnd->format('d/m/Y')
            ];

            $currentDate->addWeek();
        }


        $topCourts = Court::select('courts.id', 'courts.name')
            ->where('courts.branch_id', $branchId)
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->where('court_bookings.status', 'completed')
            ->groupBy('courts.id', 'courts.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(5)
            ->get()
            ->map(function ($court) {
                $bookingCount = CourtBooking::where('court_id', $court->id)
                    ->where('status', 'completed')
                    ->count();

                return [
                    'name' => $court->name,
                    'bookings' => $bookingCount
                ];
            });


        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';


        $upcomingBookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->where('booking_date', '>=', now()->format('Y-m-d'))
            ->where('status', 'confirmed')
            ->orderBy('booking_date')
            ->orderBy('start_time')
            ->limit(5)
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'reference_number' => $booking->reference_number,
                    'user_name' => $booking->user ? $booking->user->name : ($booking->customer ? $booking->customer->name : 'N/A'),
                    'court_name' => $booking->court ? $booking->court->name : 'N/A',
                    'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                    'time_slot' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                    'total_price' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                    'status' => $booking->status
                ];
            });

        return [
            'users' => [
                'total' => $totalUsers,
                'growth' => $userGrowth
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $bookings,
                'revenue' => $revenue
            ],
            'weekly' => $weeklyData,
            'top_courts' => $topCourts,
            'upcoming_bookings' => $upcomingBookings
        ];
    }

    /**
     * Get chart data based on selected period
     *
     * @param int $branchId
     * @param string $period
     * @param Carbon $now
     * @return array
     */
    private function getChartDataByPeriod($branchId, $period, Carbon $now, $fromDate = null, $toDate = null)
    {
        $labels = [];
        $bookings = [];
        $revenue = [];

        // If period is null, default to monthly
        $period = $period ?? 'monthly';

        switch ($period) {
            case 'daily':

                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $labels[] = $hour->format('H:i');

                    $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':

                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':

                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:

                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                        $query->where('branch_id', $branchId);
                    })
                        ->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'bookings' => $bookings,
            'revenue' => $revenue
        ];
    }

    /**
     * Get statistics for a custom date range
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    public function getCustomRangeStats($branchId, $fromDate, $toDate)
    {
        $from = Carbon::parse($fromDate)->startOfDay();
        $to = Carbon::parse($toDate)->endOfDay();


        $diffInDays = $from->diffInDays($to) + 1;
        $previousFrom = $from->copy()->subDays($diffInDays);
        $previousTo = $from->copy()->subDay();


        $totalCourts = Court::where('branch_id', $branchId)->count();
        $activeCourts = Court::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->count();
        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;


        $totalUsers = User::where('branch_id', $branchId)->count();
        $newUsers = User::where('branch_id', $branchId)
            ->whereBetween('created_at', [$from, $to])
            ->count();
        $previousPeriodUsers = User::where('branch_id', $branchId)
            ->whereBetween('created_at', [$previousFrom, $previousTo])
            ->count();
        $userGrowth = $previousPeriodUsers > 0 ? round((($newUsers - $previousPeriodUsers) / $previousPeriodUsers) * 100, 1) : 0;


        $rangeRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->sum('total_price');

        $previousRangeRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->whereBetween('booking_date', [$previousFrom->format('Y-m-d'), $previousTo->format('Y-m-d')])
            ->sum('total_price');

        $revenueGrowth = $previousRangeRevenue > 0 ? round((($rangeRevenue - $previousRangeRevenue) / $previousRangeRevenue) * 100, 1) : 0;

        $totalRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('status', 'completed')
            ->sum('total_price');

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';


        $labels = [];
        $bookings = [];
        $revenue = [];


        if ($diffInDays <= 31) {

            for ($i = 0; $i < $diffInDays; $i++) {
                $day = $from->copy()->addDays($i);
                $labels[] = $day->format('d/m');

                $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->sum('total_price');

                $bookings[] = $count;
                $revenue[] = $rev / 1000000;
            }
        } else if ($diffInDays <= 90) {

            $currentDate = $from->copy();
            while ($currentDate->lte($to)) {
                $weekStart = $currentDate->copy()->startOfWeek();
                $weekEnd = $currentDate->copy()->endOfWeek();

                if ($weekEnd->gt($to)) {
                    $weekEnd = $to->copy();
                }

                $labels[] = $weekStart->format('d/m') . '-' . $weekEnd->format('d/m');

                $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->sum('total_price');

                $bookings[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addWeek();
            }
        } else {

            $currentDate = $from->copy()->startOfMonth();
            $endDate = $to->copy()->endOfMonth();

            while ($currentDate->lte($endDate)) {
                $monthStart = $currentDate->copy()->startOfMonth();
                $monthEnd = $currentDate->copy()->endOfMonth();

                if ($monthEnd->gt($to)) {
                    $monthEnd = $to->copy();
                }

                $labels[] = $currentDate->format('m/Y');

                $count = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->sum('total_price');

                $bookings[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addMonth();
            }
        }


        $weeklyData = [];
        $currentDate = $from->copy();

        while ($currentDate->lte($to)) {
            $weekStart = $currentDate->copy()->startOfWeek();
            $weekEnd = $currentDate->copy()->endOfWeek();

            if ($weekEnd->gt($to)) {
                $weekEnd = $to->copy();
            }

            $weeklyBookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

            $weeklyRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

            $weeklyData[] = [
                'week' => __('dashboard.week') . ' ' . $weekStart->weekOfMonth,
                'start_date' => $weekStart->format('d/m/Y'),
                'end_date' => $weekEnd->format('d/m/Y'),
                'bookings' => $weeklyBookings,
                'revenue' => $weeklyRevenue,
                'date_range' => $weekStart->format('d/m') . ' - ' . $weekEnd->format('d/m/Y')
            ];

            $currentDate->addWeek();
        }


        $topCourts = Court::select('courts.id', 'courts.name')
            ->where('courts.branch_id', $branchId)
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->groupBy('courts.id', 'courts.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(5)
            ->get()
            ->map(function ($court) use ($from, $to) {
                $bookingCount = CourtBooking::where('court_id', $court->id)
                    ->where('status', 'completed')
                    ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
                    ->count();

                return [
                    'name' => $court->name,
                    'bookings' => $bookingCount
                ];
            });

        return [
            'users' => [
                'total' => $totalUsers,
                'new' => $newUsers,
                'growth' => $userGrowth
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'range_revenue' => $rangeRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $bookings,
                'revenue' => $revenue
            ],
            'weekly' => $weeklyData,
            'top_courts' => $topCourts,
            'date_range' => [
                'from' => $from->format('Y-m-d'),
                'to' => $to->format('Y-m-d'),
                'formatted' => $from->format('d/m/Y') . ' - ' . $to->format('d/m/Y')
            ]
        ];
    }
}
