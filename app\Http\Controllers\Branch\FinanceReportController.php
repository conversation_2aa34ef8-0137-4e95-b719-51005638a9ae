<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Payment;
use App\Models\Statistic;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class FinanceReportController extends Controller
{
    /**
     * Display the finance reports page
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }

        
        
        $fromDate = $request->input('from_date', Carbon::now()->startOfMonth()->subMonth(3)->format('Y-m-d'));
        $toDate = $request->input('to_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $period = $request->input('period', 'monthly');
        
        $stats = $this->getRevenueStats($branchId, $fromDate, $toDate, $period);

        return Inertia::render('Branchs/Finance/Reports', [
            'stats' => $stats,
            'date_range' => [
                'from' => $fromDate,
                'to' => $toDate,
                'formatted' => Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y')
            ],
            'current_period' => $period
        ]);
    }

    /**
     * Get revenue statistics for the branch
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    private function getRevenueStats($branchId, $fromDate, $toDate, $period = 'monthly')
    {
        $from = Carbon::parse($fromDate)->startOfDay();
        $to = Carbon::parse($toDate)->endOfDay();

        
        $existingStats = $this->getStatisticsFromDatabase($branchId, $fromDate, $toDate, $period);
        if ($existingStats) {
            return $existingStats;
        }

        
        $totalRevenue = CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$fromDate, $toDate])
        ->sum('total_price');

        
        $totalBookings = CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$fromDate, $toDate])
        ->count();

        
        $revenueByMethod = $this->getRevenueByPaymentMethod($branchId, $fromDate, $toDate);

        
        $revenueTrends = $this->getRevenueTrends($branchId, $fromDate, $toDate, $period);

        
        $revenueByDayOfWeek = $this->getRevenueByDayOfWeek($branchId, $fromDate, $toDate);

        
        $revenueByHour = $this->getRevenueByHour($branchId, $fromDate, $toDate);

        
        $revenueByCourt = $this->getRevenueByCourt($branchId, $fromDate, $toDate);

        
        $recentTransactions = $this->getRecentTransactions($branchId, $fromDate, $toDate);

        $stats = [
            'total_revenue' => $totalRevenue,
            'revenue_formatted' => number_format($totalRevenue, 0, ',', '.') . ' ₫',
            'total_bookings' => $totalBookings,
            'avg_revenue_per_booking' => $totalBookings > 0 ? round($totalRevenue / $totalBookings, 0) : 0,
            'avg_revenue_per_booking_formatted' => $totalBookings > 0 ? number_format(round($totalRevenue / $totalBookings, 0), 0, ',', '.') . ' ₫' : '0 ₫',
            'revenue_by_method' => $revenueByMethod,
            'revenue_trends' => $revenueTrends,
            'revenue_by_day_of_week' => $revenueByDayOfWeek,
            'revenue_by_hour' => $revenueByHour,
            'revenue_by_court' => $revenueByCourt,
            'recent_transactions' => $recentTransactions,
        ];

        
        if (in_array($period, ['daily', 'weekly', 'monthly'])) {
            $this->saveStatisticsToDatabase($branchId, $period, $from, $stats);
        }

        return $stats;
    }

    /**
     * Get statistics from database if they exist
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array|null
     */
    private function getStatisticsFromDatabase($branchId, $fromDate, $toDate, $period)
    {
        
        if (!in_array($period, ['daily', 'weekly', 'monthly'])) {
            return null;
        }

        $from = Carbon::parse($fromDate);

        
        if ($period === 'daily') {
            $statistic = Statistic::where('branch_id', $branchId)
                ->where('statistics_type', 'revenue_daily')
                ->where('statistics_date', $from->format('Y-m-d'))
                ->first();
        }
        
        else if ($period === 'weekly') {
            $weekStart = $from->copy()->startOfWeek();
            $statistic = Statistic::where('branch_id', $branchId)
                ->where('statistics_type', 'revenue_weekly')
                ->where('statistics_date', $weekStart->format('Y-m-d'))
                ->first();
        }
        
        else if ($period === 'monthly') {
            $monthStart = $from->copy()->startOfMonth();
            $statistic = Statistic::where('branch_id', $branchId)
                ->where('statistics_type', 'revenue_monthly')
                ->where('statistics_date', $monthStart->format('Y-m-d'))
                ->first();
        }

        
        if (isset($statistic) && $statistic) {
            return $statistic->values;
        }

        return null;
    }

    /**
     * Save statistics to database
     *
     * @param int $branchId
     * @param string $period
     * @param Carbon $date
     * @param array $stats
     * @return void
     */
    private function saveStatisticsToDatabase($branchId, $period, Carbon $date, array $stats)
    {
        
        $businessId = DB::table('branches')->where('id', $branchId)->value('business_id');

        $statisticsDate = null;
        $statisticsType = null;

        
        if ($period === 'daily') {
            $statisticsDate = $date->format('Y-m-d');
            $statisticsType = 'revenue_daily';
        } else if ($period === 'weekly') {
            $statisticsDate = $date->copy()->startOfWeek()->format('Y-m-d');
            $statisticsType = 'revenue_weekly';
        } else if ($period === 'monthly') {
            $statisticsDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $statisticsType = 'revenue_monthly';
        }

        if ($statisticsDate && $statisticsType) {
            
            $transactionsData = [];
            foreach ($stats['recent_transactions'] as $transaction) {
                
                $booking = CourtBooking::where('reference_number', $transaction['reference_number'])
                    ->where('status', 'completed')
                    ->first();

                if ($booking) {
                    $bookingCourts = [];
                    $bookingCourts[] = [
                        'court_id' => $booking->court_id,
                        'start_date' => $booking->booking_date->format('Y-m-d'),
                        'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
                        'end_time' => Carbon::parse($booking->end_time)->format('H:i')
                    ];

                    $transactionsData[] = [
                        'reference_number' => $booking->reference_number,
                        'cusname' => $booking->customer ? $booking->customer->name : $transaction['customer_name'],
                        'phone' => $booking->customer ? $booking->customer->phone : '',
                        'payment_status' => $booking->payment ? $booking->payment->status : '',
                        'total_price' => $booking->total_price,
                        'created_at' => $booking->created_at ? $booking->created_at->toDateTimeString() : null,
                        'created_at_formatted' => $booking->created_at ? $booking->created_at->format('d/m/Y H:i:s') : null,
                        'bookingCourt' => $bookingCourts
                    ];
                }
            }

            
            Statistic::updateOrCreate(
                [
                    'business_id' => $businessId,
                    'branch_id' => $branchId,
                    'statistics_type' => $statisticsType,
                    'statistics_date' => $statisticsDate
                ],
                [
                    'values' => array_merge($stats, ['transactions_data' => $transactionsData])
                ]
            );
        }
    }

    /**
     * Get revenue by payment method
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getRevenueByPaymentMethod($branchId, $fromDate, $toDate)
    {
        $revenueByMethod = CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->join('payments', 'court_bookings.id', '=', 'payments.court_booking_id')
        ->where('court_bookings.status', 'completed')
        ->whereBetween('court_bookings.booking_date', [$fromDate, $toDate])
        ->select('payments.payment_method', DB::raw('SUM(court_bookings.total_price) as total_revenue'), DB::raw('COUNT(*) as booking_count'))
        ->groupBy('payments.payment_method')
        ->get()
        ->map(function ($item) {
            return [
                'method' => $item->payment_method ?: 'unknown',
                'method_name' => $this->getPaymentMethodName($item->payment_method),
                'revenue' => $item->total_revenue,
                'revenue_formatted' => number_format($item->total_revenue, 0, ',', '.') . ' ₫',
                'booking_count' => $item->booking_count,
                'percentage' => 0, 
            ];
        })
        ->toArray();

        
        $totalRevenue = array_sum(array_column($revenueByMethod, 'revenue'));
        foreach ($revenueByMethod as &$method) {
            $method['percentage'] = $totalRevenue > 0 ? round($method['revenue'] / $totalRevenue * 100, 1) : 0;
        }

        return $revenueByMethod;
    }

    /**
     * Get revenue trends over time
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    private function getRevenueTrends($branchId, $fromDate, $toDate, $period = 'monthly')
    {
        $from = Carbon::parse($fromDate);
        $to = Carbon::parse($toDate);
        $diffInDays = $from->diffInDays($to) + 1;

        $labels = [];
        $revenueData = [];
        $bookingsData = [];

        if ($period === 'daily' || $diffInDays <= 31) {
            
            $current = $from->copy();
            while ($current->lte($to)) {
                $labels[] = $current->format('d/m');

                $dayRevenue = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereDate('booking_date', $current->format('Y-m-d'))
                ->sum('total_price');

                $dayBookings = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereDate('booking_date', $current->format('Y-m-d'))
                ->count();

                $revenueData[] = $dayRevenue / 1000000; 
                $bookingsData[] = $dayBookings;

                $current->addDay();
            }
        } elseif ($period === 'weekly' || $diffInDays <= 90) {
            
            $current = $from->copy()->startOfWeek();
            while ($current->lte($to)) {
                $weekEnd = $current->copy()->endOfWeek();
                if ($weekEnd->gt($to)) {
                    $weekEnd = $to->copy();
                }

                $labels[] = $current->format('d/m') . '-' . $weekEnd->format('d/m');

                $weekRevenue = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereBetween('booking_date', [$current->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

                $weekBookings = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereBetween('booking_date', [$current->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

                $revenueData[] = $weekRevenue / 1000000; 
                $bookingsData[] = $weekBookings;

                $current->addWeek();
            }
        } else {
            
            $current = $from->copy()->startOfMonth();
            while ($current->lte($to)) {
                $monthEnd = $current->copy()->endOfMonth();
                if ($monthEnd->gt($to)) {
                    $monthEnd = $to->copy();
                }

                $labels[] = $current->format('m/Y');

                $monthRevenue = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereYear('booking_date', $current->year)
                ->whereMonth('booking_date', $current->month)
                ->sum('total_price');

                $monthBookings = CourtBooking::whereHas('court', function($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })
                ->where('status', 'completed')
                ->whereYear('booking_date', $current->year)
                ->whereMonth('booking_date', $current->month)
                ->count();

                $revenueData[] = $monthRevenue / 1000000; 
                $bookingsData[] = $monthBookings;

                $current->addMonth();
            }
        }

        return [
            'labels' => $labels,
            'revenue' => $revenueData,
            'bookings' => $bookingsData
        ];
    }

    /**
     * Get revenue by day of week
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getRevenueByDayOfWeek($branchId, $fromDate, $toDate)
    {
        $revenueByDay = CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$fromDate, $toDate])
        ->select(
            DB::raw('DAYOFWEEK(booking_date) - 1 as day_number'),
            DB::raw('SUM(total_price) as total_revenue'),
            DB::raw('COUNT(*) as booking_count')
        )
        ->groupBy('day_number')
        ->orderBy('day_number')
        ->get();

        $days = [
            0 => 'Chủ nhật',
            1 => 'Thứ hai',
            2 => 'Thứ ba',
            3 => 'Thứ tư',
            4 => 'Thứ năm',
            5 => 'Thứ sáu',
            6 => 'Thứ bảy',
        ];

        
        $result = [];
        for ($i = 0; $i < 7; $i++) {
            $dayData = $revenueByDay->firstWhere('day_number', $i);
            $result[] = [
                'day_number' => $i,
                'day_name' => $days[$i],
                'revenue' => $dayData ? $dayData->total_revenue : 0,
                'revenue_formatted' => $dayData ? number_format($dayData->total_revenue, 0, ',', '.') . ' ₫' : '0 ₫',
                'booking_count' => $dayData ? $dayData->booking_count : 0,
            ];
        }

        return $result;
    }

    /**
     * Get revenue by hour of day
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getRevenueByHour($branchId, $fromDate, $toDate)
    {
        $revenueByHour = CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$fromDate, $toDate])
        ->select(
            DB::raw('HOUR(start_time) as hour'),
            DB::raw('SUM(total_price) as total_revenue'),
            DB::raw('COUNT(*) as booking_count')
        )
        ->groupBy('hour')
        ->orderBy('hour')
        ->get();

        
        $result = [];
        for ($i = 5; $i < 23; $i++) { 
            $hourData = $revenueByHour->firstWhere('hour', $i);
            $result[] = [
                'hour' => $i,
                'time_slot' => sprintf('%02d:00 - %02d:00', $i, $i + 1),
                'revenue' => $hourData ? $hourData->total_revenue : 0,
                'revenue_formatted' => $hourData ? number_format($hourData->total_revenue, 0, ',', '.') . ' ₫' : '0 ₫',
                'booking_count' => $hourData ? $hourData->booking_count : 0,
            ];
        }

        return $result;
    }

    /**
     * Get revenue by court
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getRevenueByCourt($branchId, $fromDate, $toDate)
    {
        return CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->with('court:id,name')
        ->where('status', 'completed')
        ->whereBetween('booking_date', [$fromDate, $toDate])
        ->select(
            'court_id',
            DB::raw('SUM(total_price) as total_revenue'),
            DB::raw('COUNT(*) as booking_count')
        )
        ->groupBy('court_id')
        ->orderByDesc('total_revenue')
        ->get()
        ->map(function ($item) {
            return [
                'court_id' => $item->court_id,
                'court_name' => $item->court ? $item->court->name : 'Unknown Court',
                'revenue' => $item->total_revenue,
                'revenue_formatted' => number_format($item->total_revenue, 0, ',', '.') . ' ₫',
                'booking_count' => $item->booking_count,
            ];
        })
        ->toArray();
    }

    /**
     * Get recent transactions
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getRecentTransactions($branchId, $fromDate, $toDate)
    {
        return CourtBooking::whereHas('court', function($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
        ->with(['customer:id,name,phone,email'])
        ->leftJoin('payments', 'court_bookings.id', '=', 'payments.court_booking_id')
        ->where('court_bookings.status', 'completed')
        ->whereBetween('court_bookings.booking_date', [$fromDate, $toDate])
        ->select('court_bookings.*', 'payments.payment_method')
        ->orderByDesc('court_bookings.booking_date')
        ->orderByDesc('court_bookings.start_time')
        ->limit(10)
        ->get()
        ->map(function ($booking) {
            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                'customer_name' => $booking->customer ? $booking->customer->name : 'Unknown',
                'customer_contact' => $booking->customer ? ($booking->customer->phone ?: $booking->customer->email) : '',
                'amount' => $booking->total_price,
                'amount_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                'payment_method' => $this->getPaymentMethodName($booking->payment_method),
                'booking_source' => __('revenue.walk_in'), 
                'created_at' => $booking->created_at->toDateTimeString(),
                'created_at_formatted' => $booking->created_at->format('d/m/Y H:i:s'),
            ];
        })
        ->toArray();
    }

    /**
     * Get payment method name
     *
     * @param string|null $methodCode
     * @return string
     */
    private function getPaymentMethodName($methodCode)
    {
        $methods = [
            'cash' => 'Tiền mặt',
            'bank_transfer' => 'Chuyển khoản',
            'credit_card' => 'Thẻ tín dụng',
            'momo' => 'Ví MoMo',
            'vnpay' => 'VNPAY',
            'zalopay' => 'ZaloPay',
        ];

        return $methods[$methodCode] ?? 'Khác';
    }

    /**
     * Export revenue report
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }

        $fromDate = $request->input('from_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $toDate = $request->input('to_date', Carbon::now()->format('Y-m-d'));
        $format = $request->input('format', 'excel');

        $stats = $this->getRevenueStats($branchId, $fromDate, $toDate);

        
        $filename = 'finance_report_' . Carbon::now()->format('Y-m-d_H-i-s');
        if ($format === 'excel') {
            $filename .= '.xlsx';
        } else {
            $filename .= '.' . $format;
        }

        switch ($format) {
            case 'excel':
                return $this->exportToExcel($stats, $filename, $fromDate, $toDate);
            case 'csv':
                return $this->exportToCsv($stats, $filename, $fromDate, $toDate);
            case 'pdf':
                return $this->exportToPdf($stats, $filename, $fromDate, $toDate);
            default:
                return redirect()->back()->with('error', 'Invalid export format');
        }
    }

    /**
     * Export revenue report to Excel
     *
     * @param array $stats
     * @param string $filename
     * @param string $fromDate
     * @param string $toDate
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($stats, $filename, $fromDate, $toDate)
    {
        
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');

        
        $dateRange = Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y');

        
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Tổng quan');

        
        $sheet->setCellValue('A1', 'BÁO CÁO DOANH THU');
        $sheet->setCellValue('A2', 'Khoảng thời gian: ' . $dateRange);
        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A2')->getFont()->setSize(12);

        
        $sheet->setCellValue('A4', 'Tổng doanh thu:');
        $sheet->setCellValue('B4', $stats['revenue_formatted']);

        $sheet->setCellValue('A5', 'Số lượt đặt sân:');
        $sheet->setCellValue('B5', $stats['total_bookings']);

        $sheet->setCellValue('A6', 'Doanh thu trung bình:');
        $sheet->setCellValue('B6', $stats['avg_revenue_per_booking_formatted']);

        $sheet->setCellValue('A7', 'Số phương thức thanh toán:');
        $sheet->setCellValue('B7', count($stats['revenue_by_method'] ?? []));

        
        $sheet->getStyle('A4:A7')->getFont()->setBold(true);
        $sheet->getColumnDimension('A')->setWidth(25);
        $sheet->getColumnDimension('B')->setWidth(20);

        
        if (!empty($stats['revenue_by_method'])) {
            $methodSheet = $spreadsheet->createSheet();
            $methodSheet->setTitle('Phương thức thanh toán');

            
            $methodSheet->setCellValue('A1', 'Phương thức');
            $methodSheet->setCellValue('B1', 'Doanh thu');
            $methodSheet->setCellValue('C1', 'Số lượt thanh toán');
            $methodSheet->setCellValue('D1', 'Tỷ lệ (%)');

            
            $row = 2;
            foreach ($stats['revenue_by_method'] as $method) {
                $methodSheet->setCellValue('A' . $row, $method['method_name']);
                $methodSheet->setCellValue('B' . $row, $method['revenue_formatted']);
                $methodSheet->setCellValue('C' . $row, $method['booking_count']);
                $methodSheet->setCellValue('D' . $row, $method['percentage'] . '%');
                $row++;
            }

            
            $methodSheet->getStyle('A1:D1')->getFont()->setBold(true);
            $methodSheet->getColumnDimension('A')->setWidth(20);
            $methodSheet->getColumnDimension('B')->setWidth(20);
            $methodSheet->getColumnDimension('C')->setWidth(20);
            $methodSheet->getColumnDimension('D')->setWidth(15);
        }

        
        if (!empty($stats['revenue_trends']['labels'])) {
            $trendsSheet = $spreadsheet->createSheet();
            $trendsSheet->setTitle('Xu hướng doanh thu');

            
            $trendsSheet->setCellValue('A1', 'Thời gian');
            $trendsSheet->setCellValue('B1', 'Doanh thu (triệu)');
            $trendsSheet->setCellValue('C1', 'Số lượt đặt sân');

            
            $row = 2;
            foreach ($stats['revenue_trends']['labels'] as $index => $label) {
                $trendsSheet->setCellValue('A' . $row, $label);
                $trendsSheet->setCellValue('B' . $row, $stats['revenue_trends']['revenue'][$index] ?? 0);
                $trendsSheet->setCellValue('C' . $row, $stats['revenue_trends']['bookings'][$index] ?? 0);
                $row++;
            }

            
            $trendsSheet->getStyle('A1:C1')->getFont()->setBold(true);
            $trendsSheet->getColumnDimension('A')->setWidth(15);
            $trendsSheet->getColumnDimension('B')->setWidth(20);
            $trendsSheet->getColumnDimension('C')->setWidth(15);
        }

        
        if (!empty($stats['revenue_by_day_of_week'])) {
            $daysSheet = $spreadsheet->createSheet();
            $daysSheet->setTitle('Doanh thu theo ngày');

            
            $daysSheet->setCellValue('A1', 'Ngày trong tuần');
            $daysSheet->setCellValue('B1', 'Doanh thu');
            $daysSheet->setCellValue('C1', 'Số lượt đặt sân');

            
            $row = 2;
            foreach ($stats['revenue_by_day_of_week'] as $day) {
                $daysSheet->setCellValue('A' . $row, $day['day_name']);
                $daysSheet->setCellValue('B' . $row, $day['revenue_formatted']);
                $daysSheet->setCellValue('C' . $row, $day['booking_count']);
                $row++;
            }

            
            $daysSheet->getStyle('A1:C1')->getFont()->setBold(true);
            $daysSheet->getColumnDimension('A')->setWidth(20);
            $daysSheet->getColumnDimension('B')->setWidth(20);
            $daysSheet->getColumnDimension('C')->setWidth(15);
        }

        
        if (!empty($stats['revenue_by_hour'])) {
            $hoursSheet = $spreadsheet->createSheet();
            $hoursSheet->setTitle('Doanh thu theo giờ');

            
            $hoursSheet->setCellValue('A1', 'Khung giờ');
            $hoursSheet->setCellValue('B1', 'Doanh thu');
            $hoursSheet->setCellValue('C1', 'Số lượt đặt sân');

            
            $row = 2;
            foreach ($stats['revenue_by_hour'] as $hour) {
                $hoursSheet->setCellValue('A' . $row, $hour['time_slot']);
                $hoursSheet->setCellValue('B' . $row, $hour['revenue_formatted']);
                $hoursSheet->setCellValue('C' . $row, $hour['booking_count']);
                $row++;
            }

            
            $hoursSheet->getStyle('A1:C1')->getFont()->setBold(true);
            $hoursSheet->getColumnDimension('A')->setWidth(20);
            $hoursSheet->getColumnDimension('B')->setWidth(20);
            $hoursSheet->getColumnDimension('C')->setWidth(15);
        }

        
        if (!empty($stats['revenue_by_court'])) {
            $courtsSheet = $spreadsheet->createSheet();
            $courtsSheet->setTitle('Doanh thu theo sân');

            
            $courtsSheet->setCellValue('A1', 'Sân');
            $courtsSheet->setCellValue('B1', 'Doanh thu');
            $courtsSheet->setCellValue('C1', 'Số lượt đặt sân');

            
            $row = 2;
            foreach ($stats['revenue_by_court'] as $court) {
                $courtsSheet->setCellValue('A' . $row, $court['court_name']);
                $courtsSheet->setCellValue('B' . $row, $court['revenue_formatted']);
                $courtsSheet->setCellValue('C' . $row, $court['booking_count']);
                $row++;
            }

            
            $courtsSheet->getStyle('A1:C1')->getFont()->setBold(true);
            $courtsSheet->getColumnDimension('A')->setWidth(25);
            $courtsSheet->getColumnDimension('B')->setWidth(20);
            $courtsSheet->getColumnDimension('C')->setWidth(15);
        }

        
        if (!empty($stats['recent_transactions'])) {
            $transactionsSheet = $spreadsheet->createSheet();
            $transactionsSheet->setTitle('Giao dịch gần đây');

            
            $transactionsSheet->setCellValue('A1', 'Mã đặt sân');
            $transactionsSheet->setCellValue('B1', 'Ngày');
            $transactionsSheet->setCellValue('C1', 'Thời gian');
            $transactionsSheet->setCellValue('D1', 'Khách hàng');
            $transactionsSheet->setCellValue('E1', 'Số tiền');
            $transactionsSheet->setCellValue('F1', 'Phương thức thanh toán');

            
            $row = 2;
            foreach ($stats['recent_transactions'] as $transaction) {
                $transactionsSheet->setCellValue('A' . $row, $transaction['reference_number']);
                $transactionsSheet->setCellValue('B' . $row, $transaction['booking_date']);
                $transactionsSheet->setCellValue('C' . $row, $transaction['booking_time']);
                $transactionsSheet->setCellValue('D' . $row, $transaction['customer_name']);
                $transactionsSheet->setCellValue('E' . $row, $transaction['amount_formatted']);
                $transactionsSheet->setCellValue('F' . $row, $transaction['payment_method']);
                $row++;
            }

            
            $transactionsSheet->getStyle('A1:F1')->getFont()->setBold(true);
            $transactionsSheet->getColumnDimension('A')->setWidth(20);
            $transactionsSheet->getColumnDimension('B')->setWidth(15);
            $transactionsSheet->getColumnDimension('C')->setWidth(20);
            $transactionsSheet->getColumnDimension('D')->setWidth(25);
            $transactionsSheet->getColumnDimension('E')->setWidth(20);
            $transactionsSheet->getColumnDimension('F')->setWidth(20);
        }

        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($tempFile);

        
        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export revenue report to CSV
     *
     * @param array $stats
     * @param string $filename
     * @param string $fromDate
     * @param string $toDate
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToCsv($stats, $filename, $fromDate, $toDate)
    {
        
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_');
        $file = fopen($tempFile, 'w');

        
        fputs($file, "\xEF\xBB\xBF");

        
        $dateRange = Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y');

        
        fputcsv($file, ['BÁO CÁO DOANH THU']);
        fputcsv($file, ['Khoảng thời gian:', $dateRange]);
        fputcsv($file, []); 

        
        fputcsv($file, ['Tổng doanh thu:', $stats['revenue_formatted']]);
        fputcsv($file, ['Số lượt đặt sân:', $stats['total_bookings']]);
        fputcsv($file, ['Doanh thu trung bình:', $stats['avg_revenue_per_booking_formatted']]);
        fputcsv($file, ['Số phương thức thanh toán:', count($stats['revenue_by_method'] ?? [])]);
        fputcsv($file, []); 

        
        if (!empty($stats['revenue_by_method'])) {
            fputcsv($file, ['DOANH THU THEO PHƯƠNG THỨC THANH TOÁN']);
            fputcsv($file, ['Phương thức', 'Doanh thu', 'Số lượt thanh toán', 'Tỷ lệ (%)']);

            foreach ($stats['revenue_by_method'] as $method) {
                fputcsv($file, [
                    $method['method_name'],
                    $method['revenue_formatted'],
                    $method['booking_count'],
                    $method['percentage'] . '%'
                ]);
            }

            fputcsv($file, []); 
        }

        
        if (!empty($stats['revenue_trends']['labels'])) {
            fputcsv($file, ['XU HƯỚNG DOANH THU']);
            fputcsv($file, ['Thời gian', 'Doanh thu (triệu)', 'Số lượt đặt sân']);

            foreach ($stats['revenue_trends']['labels'] as $index => $label) {
                fputcsv($file, [
                    $label,
                    $stats['revenue_trends']['revenue'][$index] ?? 0,
                    $stats['revenue_trends']['bookings'][$index] ?? 0
                ]);
            }

            fputcsv($file, []); 
        }

        
        if (!empty($stats['revenue_by_day_of_week'])) {
            fputcsv($file, ['DOANH THU THEO NGÀY TRONG TUẦN']);
            fputcsv($file, ['Ngày trong tuần', 'Doanh thu', 'Số lượt đặt sân']);

            foreach ($stats['revenue_by_day_of_week'] as $day) {
                fputcsv($file, [
                    $day['day_name'],
                    $day['revenue_formatted'],
                    $day['booking_count']
                ]);
            }

            fputcsv($file, []); 
        }

        
        if (!empty($stats['revenue_by_hour'])) {
            fputcsv($file, ['DOANH THU THEO KHUNG GIỜ']);
            fputcsv($file, ['Khung giờ', 'Doanh thu', 'Số lượt đặt sân']);

            foreach ($stats['revenue_by_hour'] as $hour) {
                fputcsv($file, [
                    $hour['time_slot'],
                    $hour['revenue_formatted'],
                    $hour['booking_count']
                ]);
            }

            fputcsv($file, []); 
        }

        
        if (!empty($stats['revenue_by_court'])) {
            fputcsv($file, ['DOANH THU THEO SÂN']);
            fputcsv($file, ['Sân', 'Doanh thu', 'Số lượt đặt sân']);

            foreach ($stats['revenue_by_court'] as $court) {
                fputcsv($file, [
                    $court['court_name'],
                    $court['revenue_formatted'],
                    $court['booking_count']
                ]);
            }

            fputcsv($file, []); 
        }

        
        if (!empty($stats['recent_transactions'])) {
            fputcsv($file, ['GIAO DỊCH GẦN ĐÂY']);
            fputcsv($file, ['Mã đặt sân', 'Ngày', 'Thời gian', 'Khách hàng', 'Số tiền', 'Phương thức thanh toán']);

            foreach ($stats['recent_transactions'] as $transaction) {
                fputcsv($file, [
                    $transaction['reference_number'],
                    $transaction['booking_date'],
                    $transaction['booking_time'],
                    $transaction['customer_name'],
                    $transaction['amount_formatted'],
                    $transaction['payment_method']
                ]);
            }
        }

        fclose($file);

        
        return response()->download($tempFile, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export revenue report to PDF
     *
     * @param array $stats
     * @param string $filename
     * @param string $fromDate
     * @param string $toDate
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($stats, $filename, $fromDate, $toDate)
    {
        
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');

        
        $dateRange = Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y');

        
        $html = '
        <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <style>
                body { font-family: DejaVu Sans, sans-serif; }
                .header { text-align: center; margin-bottom: 20px; }
                .header h1 { margin-bottom: 5px; }
                .summary { margin-bottom: 20px; }
                .summary table { width: 50%; margin-bottom: 20px; }
                .summary td { padding: 5px; }
                .summary td:first-child { font-weight: bold; }
                .section { margin-bottom: 20px; }
                .section h2 { margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
                .data-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .data-table th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>BÁO CÁO DOANH THU</h1>
                <p>Khoảng thời gian: ' . $dateRange . '</p>
            </div>

            <div class="summary">
                <h2>Tổng quan</h2>
                <table>
                    <tr><td>Tổng doanh thu:</td><td>' . $stats['revenue_formatted'] . '</td></tr>
                    <tr><td>Số lượt đặt sân:</td><td>' . $stats['total_bookings'] . '</td></tr>
                    <tr><td>Doanh thu trung bình:</td><td>' . $stats['avg_revenue_per_booking_formatted'] . '</td></tr>
                    <tr><td>Số phương thức thanh toán:</td><td>' . count($stats['revenue_by_method'] ?? []) . '</td></tr>
                </table>
            </div>';

        
        if (!empty($stats['revenue_by_method'])) {
            $html .= '
            <div class="section">
                <h2>Doanh thu theo phương thức thanh toán</h2>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Phương thức</th>
                            <th>Doanh thu</th>
                            <th>Số lượt thanh toán</th>
                            <th>Tỷ lệ (%)</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($stats['revenue_by_method'] as $method) {
                $html .= '
                        <tr>
                            <td>' . $method['method_name'] . '</td>
                            <td>' . $method['revenue_formatted'] . '</td>
                            <td>' . $method['booking_count'] . '</td>
                            <td>' . $method['percentage'] . '%</td>
                        </tr>';
            }

            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        
        if (!empty($stats['revenue_trends']['labels'])) {
            $html .= '
            <div class="section">
                <h2>Xu hướng doanh thu</h2>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Thời gian</th>
                            <th>Doanh thu (triệu)</th>
                            <th>Số lượt đặt sân</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($stats['revenue_trends']['labels'] as $index => $label) {
                $html .= '
                        <tr>
                            <td>' . $label . '</td>
                            <td>' . ($stats['revenue_trends']['revenue'][$index] ?? 0) . '</td>
                            <td>' . ($stats['revenue_trends']['bookings'][$index] ?? 0) . '</td>
                        </tr>';
            }

            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        
        if (!empty($stats['revenue_by_day_of_week'])) {
            $html .= '
            <div class="section">
                <h2>Doanh thu theo ngày trong tuần</h2>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Ngày trong tuần</th>
                            <th>Doanh thu</th>
                            <th>Số lượt đặt sân</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($stats['revenue_by_day_of_week'] as $day) {
                $html .= '
                        <tr>
                            <td>' . $day['day_name'] . '</td>
                            <td>' . $day['revenue_formatted'] . '</td>
                            <td>' . $day['booking_count'] . '</td>
                        </tr>';
            }

            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        
        if (!empty($stats['revenue_by_court'])) {
            $html .= '
            <div class="section">
                <h2>Doanh thu theo sân</h2>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Sân</th>
                            <th>Doanh thu</th>
                            <th>Số lượt đặt sân</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($stats['revenue_by_court'] as $court) {
                $html .= '
                        <tr>
                            <td>' . $court['court_name'] . '</td>
                            <td>' . $court['revenue_formatted'] . '</td>
                            <td>' . $court['booking_count'] . '</td>
                        </tr>';
            }

            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        
        if (!empty($stats['recent_transactions'])) {
            $html .= '
            <div class="section">
                <h2>Giao dịch gần đây</h2>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mã đặt sân</th>
                            <th>Ngày</th>
                            <th>Thời gian</th>
                            <th>Khách hàng</th>
                            <th>Số tiền</th>
                            <th>Phương thức thanh toán</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($stats['recent_transactions'] as $transaction) {
                $html .= '
                        <tr>
                            <td>' . $transaction['reference_number'] . '</td>
                            <td>' . $transaction['booking_date'] . '</td>
                            <td>' . $transaction['booking_time'] . '</td>
                            <td>' . $transaction['customer_name'] . '</td>
                            <td>' . $transaction['amount_formatted'] . '</td>
                            <td>' . $transaction['payment_method'] . '</td>
                        </tr>';
            }

            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        $html .= '
        </body>
        </html>';

        
        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->loadHtml($html);
        $dompdf->render();

        
        file_put_contents($tempFile, $dompdf->output());

        
        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }
}
