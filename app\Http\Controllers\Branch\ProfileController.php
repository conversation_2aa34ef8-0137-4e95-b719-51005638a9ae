<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Carbon\Carbon;

class ProfileController extends Controller
{
    /**
     * Display the branch profile settings page.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId) {
                return redirect()->route('unauthorized');
            }

            $branch = Branch::with(['business'])->findOrFail($branchId);

            
            if ($branch->opening_hour) {
                $branch->opening_hour = $this->formatTo24Hour($branch->opening_hour);
            }

            if ($branch->closing_hour) {
                $branch->closing_hour = $this->formatTo24Hour($branch->closing_hour);
            }

            
            $staff = User::where('branch_id', $branchId)
                ->whereHas('roles', function ($query) {
                    $query->whereIn('name', ['manager', 'staff']);
                })
                ->get(['id', 'name', 'email', 'phone', 'created_at']);

            return Inertia::render('Branchs/Settings/Profile', [
                'branch' => $branch,
                'staff' => $staff,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in branch profile settings: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi tải trang cài đặt hồ sơ chi nhánh.');
        }
    }

    /**
     * Update the branch profile information.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        try {
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId || !$user->hasPermissionTo('booking:edit_branch_profile')) {
                return redirect()->route('unauthorized');
            }

            $branch = Branch::findOrFail($branchId);

            $validated = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'description' => ['nullable', 'string', 'max:5000'],
                'address' => ['required', 'string', 'max:255'],
                'contact_phone' => ['required', 'string', 'max:20'],
                'contact_email' => ['required', 'email', 'max:255', Rule::unique('branches')->ignore($branch->id)],
                'opening_hour' => ['required', 'string'],
                'closing_hour' => ['required', 'string'],
                'province_id' => ['required', 'integer'],
                'district_id' => ['required', 'integer'],
                'ward_id' => ['required', 'integer'],
                'province_name' => ['required', 'string', 'max:255'],
                'district_name' => ['required', 'string', 'max:255'],
                'ward_name' => ['required', 'string', 'max:255'],
                'latitude' => ['nullable', 'numeric'],
                'longitude' => ['nullable', 'numeric'],
                'logo' => ['nullable', 'image', 'max:2048'],
                'cover_image' => ['nullable', 'image', 'max:2048'],
            ]);

            
            if ($request->hasFile('logo')) {
                
                if ($branch->logo_path) {
                    Storage::disk('public')->delete($branch->logo_path);
                }

                $logoPath = $request->file('logo')->store('branches/logos', 'public');
                $branch->logo_path = $logoPath;
            }

            
            if ($request->hasFile('cover_image')) {
                
                if ($branch->cover_image_path) {
                    Storage::disk('public')->delete($branch->cover_image_path);
                }

                $coverImagePath = $request->file('cover_image')->store('branches/covers', 'public');
                $branch->cover_image_path = $coverImagePath;
            }

            $branch->name = $validated['name'];
            $branch->description = $validated['description'] ?? $branch->description;
            $branch->address = $validated['address'];
            $branch->contact_phone = $validated['contact_phone'];
            $branch->contact_email = $validated['contact_email'];

            
            $branch->opening_hour = $this->formatTo24Hour($validated['opening_hour']);
            $branch->closing_hour = $this->formatTo24Hour($validated['closing_hour']);

            
            $branch->province_id = $validated['province_id'];
            $branch->district_id = $validated['district_id'];
            $branch->ward_id = $validated['ward_id'];
            $branch->province_name = $validated['province_name'];
            $branch->district_name = $validated['district_name'];
            $branch->ward_name = $validated['ward_name'];

            
            if (isset($validated['latitude']) && isset($validated['longitude'])) {
                $branch->latitude = $validated['latitude'];
                $branch->longitude = $validated['longitude'];
            }

            $branch->save();

            return redirect()->route('branch.settings.profile')->with('success', 'Thông tin chi nhánh đã được cập nhật thành công.');
        } catch (\Exception $e) {
            Log::error('Error updating branch profile: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi cập nhật thông tin chi nhánh.');
        }
    }

    /**
     * Update the branch operating hours.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateHours(Request $request)
    {
        try {
            $user = $request->user();
            $branchId = $user->branch_id;

            if (!$branchId || !$user->hasPermissionTo('booking:edit_branch_profile')) {
                return redirect()->route('unauthorized');
            }

            $branch = Branch::findOrFail($branchId);

            $validated = $request->validate([
                'operating_hours' => ['required', 'array'],
                'operating_hours.*.day' => ['required', 'integer', 'between:0,6'],
                'operating_hours.*.open' => ['required', 'boolean'],
                'operating_hours.*.opening_time' => ['nullable', 'required_if:operating_hours.*.open,true', 'string'],
                'operating_hours.*.closing_time' => ['nullable', 'required_if:operating_hours.*.open,true', 'string'],
            ]);

            
            foreach ($validated['operating_hours'] as &$hours) {
                if (isset($hours['opening_time'])) {
                    $hours['opening_time'] = $this->formatTo24Hour($hours['opening_time']);
                }
                if (isset($hours['closing_time'])) {
                    $hours['closing_time'] = $this->formatTo24Hour($hours['closing_time']);
                }
            }

            $branch->operating_hours = $validated['operating_hours'];
            $branch->save();

            return redirect()->route('branch.settings.profile')->with('success', 'Giờ hoạt động của chi nhánh đã được cập nhật thành công.');
        } catch (\Exception $e) {
            Log::error('Error updating branch operating hours: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi cập nhật giờ hoạt động.');
        }
    }

    /**
     * Format a time string to 24-hour format.
     *
     * @param string $timeString The time string to format (can be in various formats)
     * @return string The formatted time string in 24-hour format (HH:MM)
     */
    private function formatTo24Hour($timeString)
    {
        try {
            
            $time = Carbon::parse($timeString);

            
            return $time->format('H:i');
        } catch (\Exception $e) {
            Log::error('Error formatting time: ' . $e->getMessage());
            return $timeString; 
        }
    }
}