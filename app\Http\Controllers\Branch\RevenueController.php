<?php

namespace App\Http\Controllers\Branch;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use App\Models\CourtBooking;
use App\Models\Court;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
class RevenueController extends Controller
{
    /**
     * Display revenue statistics
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function getRevenueStats(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('dashboard')
                ->with('error', __('branch.no_branch'));
        }

        $period = $request->input('period', 'monthly');
        $now = Carbon::now()->setTimezone('Asia/Ho_Chi_Minh');
        $applyFilter = $request->input('apply_filter') === 'true' || $request->boolean('apply_filter');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $perPage = $request->input('per_page', 10);
        $datePerPage = $request->input('date_per_page', 10);
        $datePage = $request->input('date_page', 1);


        if ($applyFilter) {
            if ($fromDate && $toDate) {

                try {

                    $startDate = Carbon::parse($fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                    $endDate = Carbon::parse($toDate)->setTimezone('Asia/Ho_Chi_Minh');
                } catch (\Exception $e) {

                    try {
                        $startDate = Carbon::createFromFormat('d/m/Y', $fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                        $endDate = Carbon::createFromFormat('d/m/Y', $toDate)->setTimezone('Asia/Ho_Chi_Minh');
                    } catch (\Exception $e2) {

                        $startDate = Carbon::createFromFormat('Y-m-d', $fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                        $endDate = Carbon::createFromFormat('Y-m-d', $toDate)->setTimezone('Asia/Ho_Chi_Minh');
                    }
                }


                $dateRange = $endDate->diffInDays($startDate) + 1;
                $previousStartDate = $startDate->copy()->subDays($dateRange);
                $previousEndDate = $startDate->copy()->subDay();
            } else {

                switch ($period) {
                    case 'daily':
                        $startDate = $now->copy()->startOfDay();
                        $endDate = $now->copy()->endOfDay();
                        $previousStartDate = $now->copy()->subDay()->startOfDay();
                        $previousEndDate = $now->copy()->subDay()->endOfDay();
                        break;
                    case 'weekly':
                        $startDate = $now->copy()->startOfWeek();
                        $endDate = $now->copy()->endOfWeek();
                        $previousStartDate = $now->copy()->subWeek()->startOfWeek();
                        $previousEndDate = $now->copy()->subWeek()->endOfWeek();
                        break;
                    case 'monthly':
                        $startDate = $now->copy()->startOfMonth();
                        $endDate = $now->copy()->endOfMonth();
                        $previousStartDate = $now->copy()->subMonth()->startOfMonth();
                        $previousEndDate = $now->copy()->subMonth()->endOfMonth();
                        break;
                    case 'yearly':
                        $startDate = $now->copy()->startOfYear();
                        $endDate = $now->copy()->endOfYear();
                        $previousStartDate = $now->copy()->subYear()->startOfYear();
                        $previousEndDate = $now->copy()->subYear()->endOfYear();
                        break;
                    default:
                        $startDate = $now->copy()->startOfMonth();
                        $endDate = $now->copy()->endOfMonth();
                        $previousStartDate = $now->copy()->subMonth()->startOfMonth();
                        $previousEndDate = $now->copy()->subMonth()->endOfMonth();
                }
            }
        } else {

            $startDate = null;
            $endDate = null;
            $previousStartDate = null;
            $previousEndDate = null;
        }


        $query = CourtBooking::with(['court', 'customer'])
            ->where('branch_id', $branchId)
            ->where('status', 'completed');


        if ($applyFilter && $startDate && $endDate) {
            $query->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
        }

        $bookings = $query->get();


        $previousQuery = CourtBooking::where('branch_id', $branchId)
            ->where('status', 'completed');


        if ($applyFilter && $previousStartDate && $previousEndDate) {
            $previousQuery->whereBetween('booking_date', [$previousStartDate->format('Y-m-d'), $previousEndDate->format('Y-m-d')]);
        } else {

            $previousQuery->whereRaw('1 = 0');
        }

        $previousBookings = $previousQuery->get();


        $totalRevenue = $bookings->sum('total_price');
        $previousTotalRevenue = $previousBookings->sum('total_price');


        $comparisonPercentage = 0;
        if ($previousTotalRevenue > 0) {
            $comparisonPercentage = (($totalRevenue - $previousTotalRevenue) / $previousTotalRevenue) * 100;
        }


        $allRevenueByDate = $bookings->groupBy(function ($booking) {
            return Carbon::parse($booking->booking_date)->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d');
        })->map(function ($dateBookings) {
            return [
                'date' => Carbon::parse($dateBookings->first()->booking_date)->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d'),
                'revenue' => $dateBookings->sum('total_price'),
                'count' => $dateBookings->count()
            ];
        })->values();


        $sortedRevenueByDate = $allRevenueByDate->sortByDesc('date');


        $totalDatesCount = $sortedRevenueByDate->count();
        $dateOffset = ($datePage - 1) * $datePerPage;
        $paginatedDates = $sortedRevenueByDate->slice($dateOffset, $datePerPage)->values();


        $dateLinks = [];
        $lastDatePage = ceil($totalDatesCount / $datePerPage);

        if ($lastDatePage > 1) {

            $dateLinks[] = [
                'url' => $datePage > 1 ? $this->generateDatePageUrl($request, 1) : null,
                'label' => '&laquo; First',
                'active' => false
            ];
            $dateLinks[] = [
                'url' => $datePage > 1 ? $this->generateDatePageUrl($request, $datePage - 1) : null,
                'label' => '&lsaquo; Previous',
                'active' => false
            ];


            $startPage = max(1, $datePage - 2);
            $endPage = min($lastDatePage, $datePage + 2);

            for ($i = $startPage; $i <= $endPage; $i++) {
                $dateLinks[] = [
                    'url' => $this->generateDatePageUrl($request, $i),
                    'label' => (string)$i,
                    'active' => $i == $datePage
                ];
            }


            $dateLinks[] = [
                'url' => $datePage < $lastDatePage ? $this->generateDatePageUrl($request, $datePage + 1) : null,
                'label' => 'Next &rsaquo;',
                'active' => false
            ];
            $dateLinks[] = [
                'url' => $datePage < $lastDatePage ? $this->generateDatePageUrl($request, $lastDatePage) : null,
                'label' => 'Last &raquo;',
                'active' => false
            ];
        }


        $highestRevenueDay = collect($allRevenueByDate)->sortByDesc('revenue')->first();


        $totalDays = $totalDatesCount > 0 ? $totalDatesCount : 1;
        $averageDailyRevenue = $totalRevenue / $totalDays;


        $onlineBookings = $bookings->filter(function($booking) {
            return $booking->customer_id !== null;
        });

        $walkInBookings = $bookings->filter(function($booking) {
            return $booking->customer_id === null;
        });


        $courtTypes = Court::whereHas('bookings', function($query) use ($bookings) {
            $query->whereIn('id', $bookings->pluck('court_id'));
        })->with(['bookings' => function($query) use ($startDate, $endDate, $applyFilter) {
            $query->where('status', 'completed');
            if ($applyFilter && $startDate && $endDate) {
                $query->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
            }
        }])
        ->get()
        ->groupBy('type')
        ->map(function($courts, $type) {
            $revenue = 0;
            foreach($courts as $court) {
                $revenue += $court->bookings->sum('total_price');
            }
            return [
                'type' => $type ?: 'Standard',
                'revenue' => $revenue,
            ];
        })->values()->all();


        $totalCourtTypeRevenue = collect($courtTypes)->sum('revenue');
        $courtTypes = collect($courtTypes)->map(function($item) use ($totalCourtTypeRevenue) {
            $percentage = $totalCourtTypeRevenue > 0 ? ($item['revenue'] / $totalCourtTypeRevenue) * 100 : 0;
            return array_merge($item, ['percentage' => round($percentage, 1)]);
        })->all();


        $chartData = collect($allRevenueByDate)->sortBy('date')->values()->map(function($item) {
            return [
                'date' => Carbon::parse($item['date'])->format('d/m/Y'),
                'revenue' => $item['revenue'],
                'count' => $item['count']
            ];
        })->all();


        $totalBookingCount = $bookings->count();
        $uniqueCustomersCount = $bookings->whereNotNull('customer_id')->pluck('customer_id')->unique()->count();
        $averageBookingValue = $totalBookingCount > 0 ? $totalRevenue / $totalBookingCount : 0;


        $previousTotalBookingCount = $previousBookings->count();
        $previousAverageBookingValue = $previousTotalBookingCount > 0 ? $previousTotalRevenue / $previousTotalBookingCount : 0;


        $bookingCountGrowth = $previousTotalBookingCount > 0
            ? (($totalBookingCount - $previousTotalBookingCount) / $previousTotalBookingCount) * 100
            : 0;

        $averageBookingGrowth = $previousAverageBookingValue > 0
            ? (($averageBookingValue - $previousAverageBookingValue) / $previousAverageBookingValue) * 100
            : 0;


        $dayOfWeekData = $bookings->groupBy(function($booking) {
            return Carbon::parse($booking->booking_date)->dayOfWeek;
        })->map(function($dayBookings, $dayNum) {

            $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            return [
                'day' => __('common.' . $dayNames[$dayNum]),
                'count' => $dayBookings->count(),
                'revenue' => $dayBookings->sum('total_price')
            ];
        })->sortBy(function($item, $key) {

            return $key == 0 ? 7 : $key;
        })->values()->all();


        $allTransactions = $bookings
            ->groupBy('reference_number')
            ->map(function($bookingsGroup, $refNumber) {
                $firstBooking = $bookingsGroup->first();
                $totalAmount = $bookingsGroup->sum('total_price');


                $bookingDate = Carbon::parse($firstBooking->booking_date)->setTimezone('Asia/Ho_Chi_Minh');
                $startTime = Carbon::parse($firstBooking->start_time)->setTimezone('Asia/Ho_Chi_Minh');

                return [
                    'id' => $firstBooking->id,
                    'date' => $bookingDate->format('Y-m-d'),
                    'time' => $startTime->format('H:i'),
                    'customer' => $firstBooking->customer ? $firstBooking->customer->name : ($firstBooking->customer_name ?: __('customer.guest')),
                    'amount' => $totalAmount,
                    'source' => $firstBooking->customer_id ? __('booking.online') : __('booking.offline'),
                    'status' => $firstBooking->status,
                    'reference_number' => $refNumber,
                    'booking_count' => $bookingsGroup->count()
                ];
            })
            ->sortByDesc(function($transaction) {
                return $transaction['date'] . ' ' . $transaction['time'];
            })
            ->values();


        $page = $request->input('page', 1);
        $offset = ($page - 1) * $perPage;
        $total = $allTransactions->count();
        $lastPage = ceil($total / $perPage);
        $recentTransactions = $allTransactions->slice($offset, $perPage)->values()->all();


        $links = [];
        if ($lastPage > 1) {
            $links[] = [
                'url' => $page > 1 ? $this->generatePageUrl($request, 1) : null,
                'label' => '&laquo; First',
                'active' => false
            ];
            $links[] = [
                'url' => $page > 1 ? $this->generatePageUrl($request, $page - 1) : null,
                'label' => '&lsaquo; Previous',
                'active' => false
            ];

            $startPage = max(1, $page - 2);
            $endPage = min($lastPage, $page + 2);

            for ($i = $startPage; $i <= $endPage; $i++) {
                $links[] = [
                    'url' => $this->generatePageUrl($request, $i),
                    'label' => (string)$i,
                    'active' => $i == $page
                ];
            }

            $links[] = [
                'url' => $page < $lastPage ? $this->generatePageUrl($request, $page + 1) : null,
                'label' => 'Next &rsaquo;',
                'active' => false
            ];
            $links[] = [
                'url' => $page < $lastPage ? $this->generatePageUrl($request, $lastPage) : null,
                'label' => 'Last &raquo;',
                'active' => false
            ];
        }


        $totalRevenueForSources = $totalRevenue > 0 ? $totalRevenue : 1;

        $sourceStats = [
            [
                'source' => __('booking.offline'),
                'revenue' => $walkInBookings->sum('total_price'),
                'count' => $walkInBookings->count(),
                'percentage' => round(($walkInBookings->sum('total_price') / $totalRevenueForSources) * 100, 1)
            ],
            [
                'source' => __('booking.online'),
                'revenue' => $onlineBookings->sum('total_price'),
                'count' => $onlineBookings->count(),
                'percentage' => round(($onlineBookings->sum('total_price') / $totalRevenueForSources) * 100, 1)
            ]
        ];


        $revenueData = [
            'summary' => [
                'total_revenue' => $totalRevenue,
                'comparison_percentage' => round($comparisonPercentage, 1),
                'average_daily' => $averageDailyRevenue,
                'highest_day' => $highestRevenueDay ? $highestRevenueDay['revenue'] : 0,
                'highest_day_date' => $highestRevenueDay ? $highestRevenueDay['date'] : $now->format('Y-m-d'),
                'total_bookings' => $totalBookingCount,
                'booking_count_growth' => round($bookingCountGrowth, 1),
                'unique_customers' => $uniqueCustomersCount,
                'average_booking_value' => $averageBookingValue,
                'average_booking_growth' => round($averageBookingGrowth, 1),
            ],
            'by_period' => $paginatedDates->all(),
            'by_period_pagination' => [
                'links' => $dateLinks,
                'current_page' => $datePage,
                'per_page' => $datePerPage,
                'total' => $totalDatesCount,
                'from' => $dateOffset + 1,
                'to' => min($dateOffset + $datePerPage, $totalDatesCount),
                'last_page' => $lastDatePage
            ],
            'chart_data' => $chartData,
            'by_source' => $sourceStats,
            'by_court_type' => $courtTypes,
            'by_day_of_week' => $dayOfWeekData,
            'recent_transactions' => $recentTransactions,
            'pagination' => [
                'links' => $links,
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $total),
                'last_page' => $lastPage
            ]
        ];

        return Inertia::render('Branchs/Reports/Revenue', [
            'filters' => [
                'period' => $period,
                'start_date' => $fromDate,
                'end_date' => $toDate,
                'apply_filter' => $applyFilter,
            ],
            'revenueData' => $revenueData,
        ]);
    }

    /**
     * Helper function to generate page URL for pagination
     *
     * @param Request $request
     * @param int $page
     * @return string
     */
    private function generatePageUrl(Request $request, $page)
    {
        $query = $request->all();
        $query['page'] = $page;
        return url()->current() . '?' . http_build_query($query);
    }

    /**
     * Helper function to generate date page URL for revenue by date pagination
     *
     * @param Request $request
     * @param int $page
     * @return string
     */
    private function generateDatePageUrl(Request $request, $page)
    {
        $query = $request->all();
        $query['date_page'] = $page;
        return url()->current() . '?' . http_build_query($query);
    }

    /**
     * Export revenue statistics as Excel or PDF
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportRevenue(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return redirect()->route('branch.dashboard')
                ->with('error', __('branch.no_branch'));
        }

        $format = $request->input('format', 'excel');
        $period = $request->input('period', 'monthly');
        $applyFilter = $request->input('apply_filter') === 'true' || $request->boolean('apply_filter');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $now = Carbon::now()->setTimezone('Asia/Ho_Chi_Minh');


        if ($applyFilter) {
            if ($fromDate && $toDate) {

                try {

                    $startDate = Carbon::parse($fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                    $endDate = Carbon::parse($toDate)->setTimezone('Asia/Ho_Chi_Minh');
                } catch (\Exception $e) {

                    try {
                        $startDate = Carbon::createFromFormat('d/m/Y', $fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                        $endDate = Carbon::createFromFormat('d/m/Y', $toDate)->setTimezone('Asia/Ho_Chi_Minh');
                    } catch (\Exception $e2) {

                        $startDate = Carbon::createFromFormat('Y-m-d', $fromDate)->setTimezone('Asia/Ho_Chi_Minh');
                        $endDate = Carbon::createFromFormat('Y-m-d', $toDate)->setTimezone('Asia/Ho_Chi_Minh');
                    }
                }
            } else {

                switch ($period) {
                    case 'daily':
                        $startDate = $now->copy()->startOfDay();
                        $endDate = $now->copy()->endOfDay();
                        break;
                    case 'weekly':
                        $startDate = $now->copy()->startOfWeek();
                        $endDate = $now->copy()->endOfWeek();
                        break;
                    case 'monthly':
                        $startDate = $now->copy()->startOfMonth();
                        $endDate = $now->copy()->endOfMonth();
                        break;
                    case 'yearly':
                        $startDate = $now->copy()->startOfYear();
                        $endDate = $now->copy()->endOfYear();
                        break;
                    default:
                        $startDate = $now->copy()->startOfMonth();
                        $endDate = $now->copy()->endOfMonth();
                }
            }
        } else {

            $startDate = null;
            $endDate = null;
        }


        $query = CourtBooking::with(['court', 'customer'])
            ->where('branch_id', $branchId)
            ->where('status', 'completed');


        if ($applyFilter && $startDate && $endDate) {
            $query->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
        }

        $bookings = $query->get();


        $exportData = collect();


        $revenueByDate = $bookings->groupBy(function ($booking) {
            return Carbon::parse($booking->booking_date)->format('Y-m-d');
        })->map(function ($dateBookings, $date) {
            return [
                'date' => $date,
                'revenue' => $dateBookings->sum('total_price'),
                'count' => $dateBookings->count(),
                'formatted_date' => Carbon::parse($date)->format('d/m/Y'),
                'formatted_revenue' => number_format($dateBookings->sum('total_price'), 0, ',', '.') . ' ₫',
            ];
        })->sortBy('date')->values();


        $onlineBookings = $bookings->filter(function($booking) {
            return $booking->customer_id !== null;
        });

        $walkInBookings = $bookings->filter(function($booking) {
            return $booking->customer_id === null;
        });

        $totalRevenueForSources = $bookings->sum('total_price') > 0 ? $bookings->sum('total_price') : 1;
        $sourceStats = [
            [
                'source' => __('booking.offline'),
                'revenue' => $walkInBookings->sum('total_price'),
                'percentage' => round(($walkInBookings->sum('total_price') / $totalRevenueForSources) * 100, 1),
                'formatted_revenue' => number_format($walkInBookings->sum('total_price'), 0, ',', '.') . ' ₫',
            ],
            [
                'source' => __('booking.online'),
                'revenue' => $onlineBookings->sum('total_price'),
                'percentage' => round(($onlineBookings->sum('total_price') / $totalRevenueForSources) * 100, 1),
                'formatted_revenue' => number_format($onlineBookings->sum('total_price'), 0, ',', '.') . ' ₫',
            ]
        ];


        $recentTransactions = $bookings
            ->groupBy('reference_number')
            ->map(function($bookingsGroup, $refNumber) {
                $firstBooking = $bookingsGroup->first();
                $totalAmount = $bookingsGroup->sum('total_price');


                $bookingDate = Carbon::parse($firstBooking->booking_date)->setTimezone('Asia/Ho_Chi_Minh');
                $startTime = Carbon::parse($firstBooking->start_time)->setTimezone('Asia/Ho_Chi_Minh');

                return [
                    'id' => $firstBooking->id,
                    'date' => $bookingDate->format('d/m/Y'),
                    'time' => $startTime->format('H:i'),
                    'customer' => $firstBooking->customer ? $firstBooking->customer->name : ($firstBooking->customer_name ?: __('customer.guest')),
                    'amount' => $totalAmount,
                    'amount_formatted' => number_format($totalAmount, 0, ',', '.') . ' ₫',
                    'source' => $firstBooking->customer_id ? __('booking.online') : __('booking.offline'),
                    'status' => $firstBooking->status,
                    'reference_number' => $refNumber,
                    'booking_count' => $bookingsGroup->count()
                ];
            })
            ->sortByDesc(function($transaction) {

                $date = Carbon::createFromFormat('d/m/Y', $transaction['date'])->format('Y-m-d');
                return $date . ' ' . $transaction['time'];
            })
            ->values();


        $filename = __('revenue.revenue_report_filename');
        if ($applyFilter && $startDate && $endDate) {
            $filename .= '_' . $startDate->format('d_m_Y') . '_' . __('revenue.to_date_short') . '_' . $endDate->format('d_m_Y');
        } else {
            $filename .= '_' . __('revenue.all_data');
        }


        if ($format === 'pdf') {
            return $this->exportRevenuePDF($revenueByDate, $sourceStats, $recentTransactions, $filename, $startDate, $endDate);
        } else {
            return $this->exportRevenueExcel($revenueByDate, $sourceStats, $recentTransactions, $filename, $startDate, $endDate);
        }
    }

    /**
     * Export revenue data to Excel format
     *
     * @param \Illuminate\Support\Collection $revenueByDate
     * @param array $sourceStats
     * @param \Illuminate\Support\Collection $recentTransactions
     * @param string $filename
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportRevenueExcel($revenueByDate, $sourceStats, $recentTransactions, $filename, $startDate = null, $endDate = null)
    {

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();


        $totalBookings = 0;
        $totalRevenue = 0;

        foreach ($revenueByDate as $data) {
            $totalBookings += $data['count'];
            $totalRevenue += $data['revenue'];
        }

        $averageRevenue = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;




        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle(__('revenue.overview_sheet'));


        $sheet->setCellValue('A1', __('revenue.revenue_report_title'));
        $sheet->mergeCells('A1:C1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);


        if ($startDate && $endDate) {
            $sheet->setCellValue('A2', __('revenue.from_date') . ' ' . $startDate->format('d/m/Y') . ' ' . __('revenue.to_date') . ' ' . $endDate->format('d/m/Y'));
            $sheet->mergeCells('A2:C2');
            $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $rowIndex = 3;
        } else {
            $sheet->setCellValue('A2', __('revenue.all_data'));
            $sheet->mergeCells('A2:C2');
            $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $rowIndex = 3;
        }


        $sheet->setCellValue('A' . $rowIndex, __('revenue.report_generated_at') . ': ' . now()->format('d/m/Y H:i:s'));
        $sheet->mergeCells('A' . $rowIndex . ':C' . $rowIndex);
        $rowIndex++;


        $sheet->setCellValue('A' . $rowIndex, __('revenue.revenue_overview'));
        $sheet->mergeCells('A' . $rowIndex . ':C' . $rowIndex);
        $sheet->getStyle('A' . $rowIndex)->getFont()->setBold(true)->setSize(12);
        $rowIndex += 2;


        $sheet->setCellValue('A' . $rowIndex, __('revenue.total_revenue') . ':');
        $sheet->setCellValue('B' . $rowIndex, number_format($totalRevenue, 0, ',', '.') . ' ₫');
        $rowIndex++;

        $sheet->setCellValue('A' . $rowIndex, __('revenue.total_bookings') . ':');
        $sheet->setCellValue('B' . $rowIndex, $totalBookings);
        $rowIndex++;

        $sheet->setCellValue('A' . $rowIndex, __('revenue.average_booking_value') . ':');
        $sheet->setCellValue('B' . $rowIndex, number_format($averageRevenue, 0, ',', '.') . ' ₫');


        foreach (range('A', 'C') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }




        $revenueSheet = $spreadsheet->createSheet();
        $revenueSheet->setTitle(__('revenue.revenue_by_date_sheet'));
        $rowIndex = 1;


        $revenueSheet->setCellValue('A' . $rowIndex, __('revenue.revenue_by_date_title'));
        $revenueSheet->mergeCells('A' . $rowIndex . ':C' . $rowIndex);
        $revenueSheet->getStyle('A' . $rowIndex)->getFont()->setBold(true)->setSize(14);
        $rowIndex += 2;


        $revenueSheet->setCellValue('A' . $rowIndex, __('revenue.date'));
        $revenueSheet->setCellValue('B' . $rowIndex, __('revenue.booking_count'));
        $revenueSheet->setCellValue('C' . $rowIndex, __('revenue.revenue'));
        $revenueSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFont()->setBold(true);
        $rowIndex++;


        foreach ($revenueByDate as $data) {
            $revenueSheet->setCellValue('A' . $rowIndex, $data['formatted_date']);
            $revenueSheet->setCellValue('B' . $rowIndex, $data['count']);
            $revenueSheet->setCellValue('C' . $rowIndex, $data['formatted_revenue']);
            $rowIndex++;
        }


        $revenueSheet->setCellValue('A' . $rowIndex, __('revenue.total'));
        $revenueSheet->setCellValue('B' . $rowIndex, $totalBookings);
        $revenueSheet->setCellValue('C' . $rowIndex, number_format($totalRevenue, 0, ',', '.') . ' ₫');
        $revenueSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFont()->setBold(true);
        $revenueSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E2EFDA');


        foreach (range('A', 'C') as $columnID) {
            $revenueSheet->getColumnDimension($columnID)->setAutoSize(true);
        }




        $sourceSheet = $spreadsheet->createSheet();
        $sourceSheet->setTitle(__('revenue.revenue_by_source_sheet'));
        $rowIndex = 1;


        $sourceSheet->setCellValue('A' . $rowIndex, __('revenue.revenue_by_source_title'));
        $sourceSheet->mergeCells('A' . $rowIndex . ':C' . $rowIndex);
        $sourceSheet->getStyle('A' . $rowIndex)->getFont()->setBold(true)->setSize(14);
        $rowIndex += 2;


        $sourceSheet->setCellValue('A' . $rowIndex, __('revenue.source'));
        $sourceSheet->setCellValue('B' . $rowIndex, __('revenue.revenue'));
        $sourceSheet->setCellValue('C' . $rowIndex, __('revenue.percentage'));
        $sourceSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFont()->setBold(true);
        $rowIndex++;


        foreach ($sourceStats as $source) {
            $sourceSheet->setCellValue('A' . $rowIndex, $source['source']);
            $sourceSheet->setCellValue('B' . $rowIndex, $source['formatted_revenue']);
            $sourceSheet->setCellValue('C' . $rowIndex, $source['percentage'] . '%');
            $rowIndex++;
        }


        $sourceSheet->setCellValue('A' . $rowIndex, __('revenue.total'));
        $sourceSheet->setCellValue('B' . $rowIndex, number_format($totalRevenue, 0, ',', '.') . ' ₫');
        $sourceSheet->setCellValue('C' . $rowIndex, '100%');
        $sourceSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFont()->setBold(true);
        $sourceSheet->getStyle('A' . $rowIndex . ':C' . $rowIndex)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E2EFDA');


        foreach (range('A', 'C') as $columnID) {
            $sourceSheet->getColumnDimension($columnID)->setAutoSize(true);
        }




        if (count($recentTransactions) > 0) {
            $transactionsSheet = $spreadsheet->createSheet();
            $transactionsSheet->setTitle(__('revenue.transactions_sheet'));
            $rowIndex = 1;


            $transactionsSheet->setCellValue('A' . $rowIndex, __('revenue.transactions_title'));
            $transactionsSheet->mergeCells('A' . $rowIndex . ':G' . $rowIndex);
            $transactionsSheet->getStyle('A' . $rowIndex)->getFont()->setBold(true)->setSize(14);
            $rowIndex += 2;


            $transactionsSheet->setCellValue('A' . $rowIndex, __('revenue.date'));
            $transactionsSheet->setCellValue('B' . $rowIndex, __('revenue.time'));
            $transactionsSheet->setCellValue('C' . $rowIndex, __('revenue.reference_number'));
            $transactionsSheet->setCellValue('D' . $rowIndex, __('revenue.customer'));
            $transactionsSheet->setCellValue('E' . $rowIndex, __('revenue.source'));
            $transactionsSheet->setCellValue('F' . $rowIndex, __('revenue.amount'));
            $transactionsSheet->setCellValue('G' . $rowIndex, __('revenue.status'));
            $transactionsSheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getFont()->setBold(true);
            $rowIndex++;


            foreach ($recentTransactions as $transaction) {
                $transactionsSheet->setCellValue('A' . $rowIndex, $transaction['reference_number']);
                $transactionsSheet->setCellValue('B' . $rowIndex, $transaction['date']);
                $transactionsSheet->setCellValue('C' . $rowIndex, $transaction['time']);
                $transactionsSheet->setCellValue('D' . $rowIndex, $transaction['customer']);
                $transactionsSheet->setCellValue('E' . $rowIndex, $transaction['booking_count']);
                $transactionsSheet->setCellValue('F' . $rowIndex, $transaction['amount_formatted']);
                $transactionsSheet->setCellValue('G' . $rowIndex, $transaction['source']);
                $rowIndex++;
            }


            $transactionsSheet->setCellValue('A' . $rowIndex, __('revenue.total'));
            $transactionsSheet->setCellValue('E' . $rowIndex, $recentTransactions->sum('booking_count'));
            $transactionsSheet->setCellValue('F' . $rowIndex, number_format($recentTransactions->sum('amount'), 0, ',', '.') . ' ₫');
            $transactionsSheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getFont()->setBold(true);
            $transactionsSheet->getStyle('A' . $rowIndex . ':G' . $rowIndex)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E2EFDA');


            foreach (range('A', 'G') as $columnID) {
                $transactionsSheet->getColumnDimension($columnID)->setAutoSize(true);
            }
        }


        $spreadsheet->setActiveSheetIndex(0);


        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($tempFile);


        return response()->download($tempFile, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export revenue data to PDF format
     *
     * @param \Illuminate\Support\Collection $revenueByDate
     * @param array $sourceStats
     * @param \Illuminate\Support\Collection $recentTransactions
     * @param string $filename
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportRevenuePDF($revenueByDate, $sourceStats, $recentTransactions, $filename, $startDate = null, $endDate = null)
    {

        $title = __('revenue.revenue_report_title');
        $dateRangeText = '';

        if ($startDate && $endDate) {
            $dateRangeText = __('revenue.from_date') . ' ' . $startDate->format('d/m/Y') . ' ' . __('revenue.to_date') . ' ' . $endDate->format('d/m/Y');
        } else {
            $dateRangeText = __('revenue.all_data');
        }

        $generatedAt = __('revenue.report_generated_at') . ': ' . now()->format('d/m/Y H:i:s');


        $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $html .= '<style>
            body {
                font-family: DejaVu Sans, sans-serif;
                font-size: 10px;
                margin: 0;
                padding: 10px;
            }
            h1 {
                text-align: center;
                font-size: 18px;
                margin-bottom: 5px;
            }
            h2 {
                font-size: 14px;
                margin-top: 20px;
                margin-bottom: 10px;
                color: #333;
            }
            .date-range {
                text-align: center;
                font-size: 12px;
                margin-bottom: 5px;
            }
            .generated-at {
                text-align: center;
                font-size: 10px;
                margin-bottom: 20px;
                color: #666;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            table.summary-table {
                width: 60%;
                margin-left: 0;
            }
            table, th, td {
                border: 1px solid #ddd;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
                text-align: left;
                padding: 8px;
            }
            td {
                padding: 8px;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .total-row {
                font-weight: bold;
                background-color: #e2efda !important;
            }
            .content-wrapper {
                margin: 0 auto;
                max-width: 100%;
            }
        </style>';
        $html .= '</head><body>';
        $html .= '<div class="content-wrapper">';
        $html .= '<h1>' . $title . '</h1>';
        $html .= '<p class="date-range">' . $dateRangeText . '</p>';
        $html .= '<p class="generated-at">' . $generatedAt . '</p>';


        $totalBookings = 0;
        $totalRevenue = 0;

        foreach ($revenueByDate as $data) {
            $totalBookings += $data['count'];
            $totalRevenue += $data['revenue'];
        }


        $html .= '<h2>' . __('revenue.revenue_overview') . '</h2>';
        $html .= '<table class="summary-table">';
        $html .= '<tr><td style="font-weight: bold;">' . __('revenue.total_revenue') . ':</td><td>' . number_format($totalRevenue, 0, ',', '.') . ' ₫</td></tr>';
        $html .= '<tr><td style="font-weight: bold;">' . __('revenue.total_bookings') . ':</td><td>' . $totalBookings . '</td></tr>';

        $averageRevenue = $totalBookings > 0 ? $totalRevenue / $totalBookings : 0;
        $html .= '<tr><td style="font-weight: bold;">' . __('revenue.average_booking_value') . ':</td><td>' . number_format($averageRevenue, 0, ',', '.') . ' ₫</td></tr>';
        $html .= '</table>';


        $html .= '<h2>' . __('revenue.revenue_by_date_title') . '</h2>';
        $html .= '<table>';
        $html .= '<tr><th>' . __('revenue.date') . '</th><th>' . __('revenue.booking_count') . '</th><th>' . __('revenue.revenue') . '</th></tr>';

        foreach ($revenueByDate as $data) {
            $html .= '<tr>';
            $html .= '<td>' . $data['formatted_date'] . '</td>';
            $html .= '<td>' . $data['count'] . '</td>';
            $html .= '<td>' . $data['formatted_revenue'] . '</td>';
            $html .= '</tr>';
        }

        $html .= '<tr class="total-row">';
        $html .= '<td>' . __('revenue.total') . '</td>';
        $html .= '<td>' . $totalBookings . '</td>';
        $html .= '<td>' . number_format($totalRevenue, 0, ',', '.') . ' ₫</td>';
        $html .= '</tr>';
        $html .= '</table>';


        $html .= '<h2>' . __('revenue.revenue_by_source_title') . '</h2>';
        $html .= '<table>';
        $html .= '<tr><th>' . __('revenue.source') . '</th><th>' . __('revenue.revenue') . '</th><th>' . __('revenue.percentage') . '</th></tr>';

        foreach ($sourceStats as $source) {
            $html .= '<tr>';
            $html .= '<td>' . $source['source'] . '</td>';
            $html .= '<td>' . $source['formatted_revenue'] . '</td>';
            $html .= '<td>' . $source['percentage'] . '%</td>';
            $html .= '</tr>';
        }

        $html .= '</table>';


        if (count($recentTransactions) > 0) {
            $html .= '<h2>' . __('revenue.transactions_title') . '</h2>';
            $html .= '<table>';
            $html .= '<tr><th>' . __('revenue.date') . '</th><th>' . __('revenue.time') . '</th><th>' . __('revenue.reference_number') . '</th><th>' . __('revenue.customer') . '</th><th>' . __('revenue.booking_count') . '</th><th>' . __('revenue.amount') . '</th><th>' . __('revenue.source') . '</th><th>' . __('revenue.status') . '</th></tr>';

            foreach ($recentTransactions as $transaction) {
                $html .= '<tr>';
                $html .= '<td>' . $transaction['date'] . '</td>';
                $html .= '<td>' . $transaction['time'] . '</td>';
                $html .= '<td>' . $transaction['reference_number'] . '</td>';
                $html .= '<td>' . $transaction['customer'] . '</td>';
                $html .= '<td>' . $transaction['booking_count'] . '</td>';
                $html .= '<td>' . $transaction['amount_formatted'] . '</td>';
                $html .= '<td>' . $transaction['source'] . '</td>';
                $html .= '<td>' . $transaction['status'] . '</td>';
                $html .= '</tr>';
            }

            $html .= '</table>';
        }

        $html .= '</div>';
        $html .= '</body></html>';


        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($html);
        $dompdf->render();


        $pdfContent = $dompdf->output();
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);


        return response()->download($tempFile, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }
}
