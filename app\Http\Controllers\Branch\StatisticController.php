<?php

namespace App\Http\Controllers\Branch;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Statistic;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StatisticController extends Controller
{
    /**
     * Get statistics for branch revenue dashboard
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRevenueStats(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return response()->json(['error' => __('branch.no_branch')], 400);
        }

        // Get date range parameters if provided
        $fromDate = $request->input('from_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $toDate = $request->input('to_date', Carbon::now()->format('Y-m-d'));
        $period = $request->input('period', 'monthly');

        // Try to get statistics from database first
        $statisticsType = 'revenue_' . $period;
        $statisticsDate = $this->getStatisticsDate($fromDate, $period);

        $statistic = Statistic::where('branch_id', $branchId)
            ->where('statistics_type', $statisticsType)
            ->where('statistics_date', $statisticsDate)
            ->first();

        if ($statistic) {
            return response()->json($statistic->values);
        }

        // If not found in database, calculate on the fly
        $stats = $this->calculateRevenueStats($branchId, $fromDate, $toDate, $period);
        return response()->json($stats);
    }

    /**
     * Get statistics date based on period
     *
     * @param string $date
     * @param string $period
     * @return string
     */
    private function getStatisticsDate($date, $period)
    {
        $carbon = Carbon::parse($date);

        if ($period === 'daily') {
            return $carbon->format('Y-m-d');
        } else if ($period === 'weekly') {
            return $carbon->startOfWeek()->format('Y-m-d');
        } else if ($period === 'monthly') {
            return $carbon->startOfMonth()->format('Y-m-d');
        }

        return $carbon->format('Y-m-d');
    }

    /**
     * Calculate revenue statistics for the branch
     *
     * @param int $branchId
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    private function calculateRevenueStats($branchId, $fromDate, $toDate, $period)
    {
        $from = Carbon::parse($fromDate)->startOfDay();
        $to = Carbon::parse($toDate)->endOfDay();

        // Get total revenue
        $totalRevenue = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$fromDate, $toDate])
            ->sum('total_price');

        // Get total bookings count
        $totalBookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$fromDate, $toDate])
            ->count();

        // Get bookings by reference number to get transaction data
        $bookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->with(['customer', 'court', 'payment'])
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$fromDate, $toDate])
            ->orderBy('reference_number')
            ->get();

        // Group bookings by reference number
        $transactionsByReference = [];
        foreach ($bookings as $booking) {
            if (!isset($transactionsByReference[$booking->reference_number])) {
                $transactionsByReference[$booking->reference_number] = [
                    'reference_number' => $booking->reference_number,
                    'cusname' => $booking->customer ? $booking->customer->name : $booking->customer_name,
                    'phone' => $booking->customer ? $booking->customer->phone : $booking->customer_phone,
                    'payment_status' => $booking->payment ? $booking->payment->status : '',
                    'total_price' => 0,
                    'bookingCourt' => []
                ];
            }

            // Add booking details
            $transactionsByReference[$booking->reference_number]['bookingCourt'][] = [
                'court_id' => $booking->court_id,
                'start_date' => $booking->booking_date->format('Y-m-d'),
                'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
                'end_time' => Carbon::parse($booking->end_time)->format('H:i')
            ];

            // Add to total price
            $transactionsByReference[$booking->reference_number]['total_price'] += $booking->total_price;
        }

        // Convert to array
        $transactions = array_values($transactionsByReference);

        // Create stats array
        $stats = [
            'total_revenue' => $totalRevenue,
            'revenue_formatted' => number_format($totalRevenue, 0, ',', '.') . ' ₫',
            'total_bookings' => $totalBookings,
            'avg_revenue_per_booking' => $totalBookings > 0 ? round($totalRevenue / $totalBookings, 0) : 0,
            'avg_revenue_per_booking_formatted' => $totalBookings > 0 ? number_format(round($totalRevenue / $totalBookings, 0), 0, ',', '.') . ' ₫' : '0 ₫',
            'transactions_data' => $transactions
        ];

        // Store in database if it's a standard period
        if (in_array($period, ['daily', 'weekly', 'monthly'])) {
            $this->saveStatisticsToDatabase($branchId, $period, $from, $stats);
        }

        return $stats;
    }

    /**
     * Save statistics to database
     *
     * @param int $branchId
     * @param string $period
     * @param Carbon $date
     * @param array $stats
     * @return void
     */
    private function saveStatisticsToDatabase($branchId, $period, Carbon $date, array $stats)
    {
        // Get the business ID for the branch
        $businessId = DB::table('branches')->where('id', $branchId)->value('business_id');

        $statisticsDate = null;
        $statisticsType = null;

        // Determine statistics date and type based on period
        if ($period === 'daily') {
            $statisticsDate = $date->format('Y-m-d');
            $statisticsType = 'revenue_daily';
        } else if ($period === 'weekly') {
            $statisticsDate = $date->copy()->startOfWeek()->format('Y-m-d');
            $statisticsType = 'revenue_weekly';
        } else if ($period === 'monthly') {
            $statisticsDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $statisticsType = 'revenue_monthly';
        }

        if ($statisticsDate && $statisticsType) {
            // Update or create statistic
            Statistic::updateOrCreate(
                [
                    'business_id' => $businessId,
                    'branch_id' => $branchId,
                    'statistics_type' => $statisticsType,
                    'statistics_date' => $statisticsDate
                ],
                [
                    'values' => $stats
                ]
            );
        }
    }

    /**
     * Get recent transactions with detailed data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecentTransactions(Request $request)
    {
        $user = Auth::user();
        $branchId = $user->branch_id;

        if (!$branchId) {
            return response()->json(['error' => __('branch.no_branch')], 400);
        }

        $limit = $request->input('limit', 10);
        $fromDate = $request->input('from_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $toDate = $request->input('to_date', Carbon::now()->format('Y-m-d'));

        // Get bookings grouped by reference number
        $bookings = CourtBooking::whereHas('court', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })
            ->with(['customer', 'court', 'payment'])
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$fromDate, $toDate])
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->get();

        // Group by reference number
        $transactionsByReference = [];
        foreach ($bookings as $booking) {
            if (!isset($transactionsByReference[$booking->reference_number])) {
                $transactionsByReference[$booking->reference_number] = [
                    'reference_number' => $booking->reference_number,
                    'booking_date' => $booking->booking_date->format('d/m/Y'),
                    'customer_name' => $booking->customer ? $booking->customer->name : $booking->customer_name,
                    'customer_contact' => $booking->customer ? ($booking->customer->phone ?: '') : $booking->customer_phone,
                    'payment_method' => $booking->payment ? $this->getPaymentMethodName($booking->payment->payment_method) : 'Không xác định',
                    'total_price' => 0,
                    'created_at' => $booking->created_at ? $booking->created_at->toDateTimeString() : null,
                    'created_at_formatted' => $booking->created_at ? $booking->created_at->format('d/m/Y H:i:s') : null,
                    'courts' => []
                ];
            }

            // Add court details
            $transactionsByReference[$booking->reference_number]['courts'][] = [
                'court_name' => $booking->court ? $booking->court->name : 'Sân không xác định',
                'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                'price' => $booking->total_price,
                'price_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫'
            ];

            // Add to total price
            $transactionsByReference[$booking->reference_number]['total_price'] += $booking->total_price;
        }


        foreach ($transactionsByReference as &$transaction) {
            $transaction['total_price_formatted'] = number_format($transaction['total_price'], 0, ',', '.') . ' ₫';
        }

        // Convert to array, limit and return
        $transactions = array_values($transactionsByReference);
        $transactions = array_slice($transactions, 0, $limit);

        return response()->json($transactions);
    }

    /**
     * Get payment method name
     *
     * @param string|null $methodCode
     * @return string
     */
    private function getPaymentMethodName($methodCode)
    {
        $methods = [
            'cash' => 'Tiền mặt',
            'bank_transfer' => 'Chuyển khoản',
            'credit_card' => 'Thẻ tín dụng',
            'momo' => 'Ví MoMo',
            'vnpay' => 'VNPAY',
            'zalopay' => 'ZaloPay',
        ];

        return $methods[$methodCode] ?? 'Khác';
    }
}
