<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class BookingCheckController extends Controller
{
    public function index(Request $request)
    {
        $branch = $request->user()->currentBranch;
        $currentDate = now()->format('Y-m-d');

        $confirmedBookings = CourtBooking::where('branch_id', $branch->id)
            ->where('status', 'confirmed')
            ->where('checkin_status', null)
            ->whereDate('start_time', $currentDate)
            ->with(['customer', 'court'])
            ->get();

        $checkedInBookings = CourtBooking::where('branch_id', $branch->id)
            ->where('checkin_status', 'checked_in')
            ->whereDate('start_time', $currentDate)
            ->with(['customer', 'court'])
            ->get();

        $completedBookings = CourtBooking::where('branch_id', $branch->id)
            ->where('checkin_status', 'checked_out')
            ->whereDate('start_time', $currentDate)
            ->with(['customer', 'court'])
            ->get();

        $notCompletedBookings = CourtBooking::where('branch_id', $branch->id)
            ->where('status', 'confirmed')
            ->where('checkin_status', '!=', 'checked_out')
            ->whereDate('start_time', '<', $currentDate)
            ->with(['customer', 'court'])
            ->get();

        $filters = [
            'date' => $currentDate,
        ];

        $referenceGroups = CourtBooking::where('branch_id', $branch->id)
            ->whereDate('start_time', $currentDate)
            ->select('reference_number')
            ->distinct()
            ->get()
            ->pluck('reference_number');

        return Inertia::render('Business/Booking/CheckInOut', [
            'branch' => $branch,
            'currentDate' => $currentDate,
            'confirmedBookings' => $confirmedBookings,
            'checkedInBookings' => $checkedInBookings,
            'completedBookings' => $completedBookings,
            'notCompletedBookings' => $notCompletedBookings,
            'filters' => $filters,
            'referenceGroups' => $referenceGroups,
        ]);
    }

    public function checkIn(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $userId = Auth::id();

        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải được xác nhận trước khi check-in'
            ], 400);
        }

        if ($booking->checkin_status === 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân đã được check-in'
            ], 400);
        }

        $success = $booking->checkIn($userId);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Check-in thành công'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Có lỗi xảy ra khi check-in'
        ], 500);
    }

    public function checkOut(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $userId = Auth::id();

        if ($booking->checkin_status !== 'checked_in') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn đặt sân phải đã được check-in trước khi check-out'
            ], 400);
        }

        $checkoutAll = $request->input('checkout_all', false);
        $forceCheckout = $request->input('force_checkout', false);

        $referenceNumber = $booking->reference_number;

        if ($checkoutAll) {
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('checkin_status', 'checked_in')
                ->where('id', '!=', $booking->id)
                ->get();

            $successCount = 0;
            $failedCount = 0;

            $success = $booking->checkOut($userId);

            if ($success && $booking->overtime_minutes > 0) {
                $this->calculateOvertimeFee($booking);
            }

            if ($success) {
                $successCount++;
            } else {
                $failedCount++;
            }

            foreach ($relatedBookings as $relatedBooking) {
                $relatedSuccess = $relatedBooking->checkOut($userId);

                if ($relatedSuccess && $relatedBooking->overtime_minutes > 0) {
                    $this->calculateOvertimeFee($relatedBooking);
                }

                if ($relatedSuccess) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            if ($failedCount === 0) {
                return response()->json([
                    'success' => true,
                    'message' => 'Check-out thành công cho tất cả đơn đặt sân'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => "Check-out thất bại cho $failedCount đơn đặt sân"
            ], 500);
        }

        $success = $booking->checkOut($userId);

        if ($success) {
            if ($booking->overtime_minutes > 0) {
                $this->calculateOvertimeFee($booking);
            }

            return response()->json([
                'success' => true,
                'message' => 'Check-out thành công'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Có lỗi xảy ra khi check-out'
        ], 500);
    }

    private function calculateOvertimeFee($booking)
    {
        $overtimeMinutes = $booking->overtime_minutes;
        if ($overtimeMinutes <= 0) {
            return;
        }

        $hourlyRate = $booking->court->price_per_hour;
        $overtimeFee = ceil($overtimeMinutes / 60) * $hourlyRate;

        $booking->overtime_fee = $overtimeFee;
        $booking->save();
    }
}