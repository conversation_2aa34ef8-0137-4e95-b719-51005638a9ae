<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BookingPaymentController extends Controller
{
    public function processOvertimePayment(Request $request)
    {
        $request->validate([
            'reference_number' => 'required|string',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'amount_received' => 'required_if:payment_method,cash|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $bookings = CourtBooking::where('reference_number', $request->reference_number)
                ->where('checkin_status', 'checked_out')
                ->where('overtime_fee', '>', 0)
                ->whereNull('overtime_payment_id')
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy đơn đặt sân cần thanh toán phí phát sinh'
                ], 404);
            }

            $totalAmount = $bookings->sum('overtime_fee');

            $payment = new Payment([
                'reference_number' => $request->reference_number,
                'amount' => $totalAmount,
                'payment_method_id' => $request->payment_method_id,
                'status' => 'completed',
                'type' => 'overtime',
                'staff_id' => Auth::id(),
                'metadata' => [
                    'amount_received' => $request->amount_received,
                    'change_amount' => $request->amount_received ? ($request->amount_received - $totalAmount) : 0,
                ]
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->overtime_payment_id = $payment->id;
                $booking->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thanh toán phí phát sinh thành công',
                'payment' => $payment
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xử lý thanh toán: ' . $e->getMessage()
            ], 500);
        }
    }
}