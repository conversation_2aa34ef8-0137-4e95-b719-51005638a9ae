<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Court;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CourtController extends Controller
{
    /**
     * Display a listing of the courts.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;
        $branches = Branch::where('business_id', $business->id)
            ->select('id', 'name')
            ->get();
        $branchIds = $branches->pluck('id')->toArray();
        $query = Court::query()->with('branch');
        $query->whereIn('branch_id', $branchIds);
        $query->whereNull('deleted_at');
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }
        if ($request->filled('status')) {
            $status = $request->status;
            if ($status === 'active') {
                $query->where('is_active', 1);
            } elseif ($status === 'inactive') {
                $query->where('is_active', 0);
            } elseif ($status === 'maintenance') {
                $query->where('is_active', 0);
            }
        }
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Determine which view mode is being requested
        $viewMode = $request->input('view_mode', 'grid');
        $perPage = $viewMode === 'list' ? 10 : 9;

        $courts = $query->latest()->paginate($perPage)->withQueryString();

        $courts->getCollection()->transform(function ($court) {
            $court->formatted_image_url = $court->getFormattedImageUrlAttribute();
            return $court;
        });

        return Inertia::render('Business/Courts/Index', [
            'courts' => $courts,
            'branches' => $branches,
            'filters' => [
                'branch_id' => $request->branch_id,
                'status' => $request->status,
                'search' => $request->search,
                'sort' => $request->sort,
                'direction' => $request->direction,
                'view_mode' => $viewMode,
            ],
        ]);
    }

    /**
     * Show the form for creating a new court.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        $user = Auth::user();
        $business = $user->business;
        $branches = Branch::where('business_id', $business->id)->get();

        return Inertia::render('Business/Courts/Create', [
            'branches' => $branches,
        ]);
    }

    /**
     * Store a newly created court in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:indoor,outdoor',
            'surface' => 'required|string|in:hard,clay,grass,carpet,concrete',
            'status' => 'required|string|in:active,inactive,maintenance',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], __('validation.court'));

        $user = Auth::user();
        $business = $user->business;
        $branchBelongsToBusiness = Branch::where('id', $request->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            return back()->withErrors([
                'branch_id' => __('courts.branch_not_found_in_business'),
            ]);
        }

        $court = new Court();
        $court->name = $request->name;
        $court->branch_id = $request->branch_id;
        $court->business_id = $business->id;
        $court->type = $request->type;
        $court->surface_type = $request->surface;
        $court->is_active = $request->status == "active" ?? 0;
        $court->description = $request->description;
        $court->amenities = "";


        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('courts', 'public');
            $court->image_url = $path;
        }

        $court->save();

        return redirect()->route('business.courts.index')
            ->with('flash.success', __('courts.court_created_successfully'));
    }

    /**
     * Display the specified court.
     *
     * @param  \App\Models\Court  $court
     * @return \Inertia\Response
     */
    public function show(Court $court)
    {
        $this->checkCourtBelongsToBusiness($court);

        $court->load('branch');
        $court->formatted_image_url = $court->getFormattedImageUrlAttribute();

        $upcomingReservations = [];

        return Inertia::render('Business/Courts/Show', [
            'court' => $court,
            'reservations' => $upcomingReservations,
        ]);
    }

    /**
     * Show the form for editing the specified court.
     *
     * @param  \App\Models\Court  $court
     * @return \Inertia\Response
     */
    public function edit(Court $court)
    {
        $this->checkCourtBelongsToBusiness($court);
        $user = Auth::user();
        $business = $user->business;
        $branches = Branch::where('business_id', $business->id)->get();

        $court->formatted_image_url = $court->getFormattedImageUrlAttribute();

        return Inertia::render('Business/Courts/Edit', [
            'court' => $court,
            'branches' => $branches,
        ]);
    }

    /**
     * Update the specified court in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Court  $court
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Court $court)
    {
        $this->checkCourtBelongsToBusiness($court);

        $request->validate([
            'name' => 'required|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'status' => 'required|string|in:active,inactive,maintenance',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], __('validation.court'));

        $user = Auth::user();
        $business = $user->business;
        $branchBelongsToBusiness = Branch::where('id', $request->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            return back()->withErrors([
                'branch_id' => __('courts.branch_not_found_in_business'),
            ]);
        }

        $court->name = $request->name;
        $court->branch_id = $request->branch_id;
        $court->business_id = $business->id;
        $court->type = $request->type;
        $court->is_active = $request->status == "active" ?? 0;
        $court->description = $request->description;
        $court->amenities = "";


        if ($request->hasFile('image')) {

            if ($court->image_url) {
                Storage::disk('public')->delete($court->image_url);
            }

            $path = $request->file('image')->store('courts', 'public');
            $court->image_url = $path;
        }

        $court->save();

        return redirect()->route('business.courts.show', $court)
            ->with('flash.success', __('courts.court_updated_successfully'));
    }

    /**
     * Remove the specified court from storage.
     *
     * @param  \App\Models\Court  $court
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Court $court)
    {
        $this->checkCourtBelongsToBusiness($court);
        $court->delete();

        return redirect()->route('business.courts.index')
            ->with('flash.success', __('courts.court_deleted_successfully'));
    }

    /**
     * Kiểm tra xem sân có thuộc về business hiện tại không.
     *
     * @param  \App\Models\Court  $court
     * @return void
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    private function checkCourtBelongsToBusiness(Court $court)
    {
        $user = Auth::user();
        $business = $user->business;


        $businessBranchIds = Branch::where('business_id', $business->id)
            ->pluck('id')
            ->toArray();


        if (!in_array($court->branch_id, $businessBranchIds)) {
            abort(403, __('courts.court_not_found_in_business'));
        }
    }
}
