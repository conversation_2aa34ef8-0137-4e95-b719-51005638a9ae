<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\CourtPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Carbon\Carbon;
use Spatie\Permission\Traits\HasRoles;

class CourtPriceController extends Controller
{
    use HasRoles;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        $branch_id = $request->input('branch_id');
        $court_type = $request->input('court_type');
        $price_type = $request->input('price_type');
        $is_active = $request->input('is_active');
        $search = $request->input('search');
        $sort_field = $request->input('sort_field', 'created_at');
        $sort_direction = $request->input('sort_direction', 'desc');

        $branches = Branch::where('business_id', $business->id)
            ->orderBy('name')
            ->get(['id', 'name']);
        $query = CourtPrice::query()
            ->whereIn('branch_id', $branches->pluck('id'));
        if ($branch_id) {
            $query->where('branch_id', $branch_id);
        }

        if ($court_type) {
            $query->where('court_type', $court_type);
        }

        if ($price_type) {
            $query->where('price_type', $price_type);
        }

        if ($is_active !== null && $is_active !== '') {
            $query->where('is_active', $is_active);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                    ->orWhere('price_per_hour', 'like', "%{$search}%");
            });
        }
        $query->orderBy($sort_field, $sort_direction);
        $prices = $query->paginate(20)->withQueryString();

        return Inertia::render('Business/Courts/Prices/Index', [
            'prices' => $prices,
            'branches' => $branches,
            'filters' => [
                'branch_id' => $branch_id,
                'court_type' => $court_type,
                'price_type' => $price_type,
                'is_active' => $is_active,
                'search' => $search,
                'sort_field' => $sort_field,
                'sort_direction' => $sort_direction,
            ],
            'courtTypes' => [
                'pickleball' => __('courts.pickleball'),
                'tennis' => __('courts.tennis'),
                'badminton' => __('courts.badminton'),
                'multi' => __('courts.multi')
            ],
            'priceTypes' => [
                'normal' => __('prices.normal'),
                'holiday' => __('prices.holiday'),
                'special_date' => __('prices.special_date')
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();
        $business = $user->business;

        $branches = Branch::where('business_id', $business->id)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('Business/Courts/Prices/Create', [
            'branches' => $branches,
            'courtTypes' => [
                'pickleball' => __('courts.pickleball'),
                'tennis' => __('courts.tennis'),
                'badminton' => __('courts.badminton'),
                'multi' => __('courts.multi')
            ],
            'priceTypes' => [
                'normal' => __('prices.normal'),
                'holiday' => __('prices.holiday'),
                'special_date' => __('prices.special_date')
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'court_type' => 'required|string|in:pickleball,tennis,badminton,multi',
            'price_data' => 'required|array',
            'price_data.*.start_time' => 'required|date_format:H:i',
            'price_data.*.end_time' => 'required|date_format:H:i',
            'price_data.*.price_per_hour' => 'required|numeric|min:0',
            'price_data.*.member_price_per_hour' => 'nullable|numeric|min:0',
            'price_data.*.days' => 'nullable|string',
            'price_data.*.mode' => 'nullable|string|max:50',
            'price_data.*.price_type' => 'required|string|in:normal,holiday,special_date',
            'price_data.*.special_date' => 'nullable|required_if:price_data.*.price_type,special_date|date',
            'notes' => 'nullable|string',
        ], __('validation.court_price'));

        $branchBelongsToBusiness = Branch::where('id', $request->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            return back()->withErrors([
                'branch_id' => __('courts.branch_not_found_in_business'),
            ]);
        }
        $createdRecords = [];
        $errors = [];

        foreach ($request->price_data as $index => $priceData) {
            $existingPrice = CourtPrice::where('branch_id', $request->branch_id)
                ->where('court_type', $request->court_type)
                ->where('price_type', $priceData['price_type'])
                ->when(isset($priceData['special_date']), function ($q) use ($priceData) {
                    return $q->where('special_date', $priceData['special_date']);
                })
                ->when(!isset($priceData['special_date']), function ($q) {
                    return $q->whereNull('special_date');
                })
                ->where(function ($q) use ($priceData) {
                    $q->where(function ($query) use ($priceData) {
                        $query->where('start_time', '<', $priceData['end_time'])
                            ->where('end_time', '>', $priceData['start_time']);
                    });
                })
                ->first();

            if ($existingPrice) {
                $errors[] = "Time range overlap at entry #" . ($index + 1);
                continue;
            }

            $createdRecords[] = CourtPrice::create([
                'branch_id' => $request->branch_id,
                'court_type' => $request->court_type,
                'start_time' => $priceData['start_time'],
                'end_time' => $priceData['end_time'],
                'price_per_hour' => $priceData['price_per_hour'],
                'member_price_per_hour' => $priceData['member_price_per_hour'] ?? null,
                'is_active' => $priceData['is_active'] ?? true,
                'price_type' => $priceData['price_type'],
                'special_date' => $priceData['price_type'] === 'special_date' ? $priceData['special_date'] : null,
                'days' => $priceData['days'] ?? null,
                'mode' => $priceData['mode'] ?? null,
                'notes' => $request->notes,
                'description' => $priceData['description'] ?? null,
            ]);
        }

        if (count($errors) > 0) {
            return back()->withErrors([
                'price_data' => $errors
            ]);
        }

        return redirect()->route('business.prices.index', ['branch_id' => $request->branch_id])
            ->with('message', __('prices.price_created_successfully'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = Auth::user();
        $business = $user->business;

        $price = CourtPrice::findOrFail($id);

        $branchBelongsToBusiness = Branch::where('id', $price->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            abort(403, __('courts.branch_not_found_in_business'));
        }

        $branches = Branch::where('business_id', $business->id)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('Business/Courts/Prices/Edit', [
            'price' => $price,
            'branches' => $branches,
            'courtTypes' => [
                'pickleball' => __('courts.pickleball'),
                'tennis' => __('courts.tennis'),
                'badminton' => __('courts.badminton'),
                'multi' => __('courts.multi')
            ],
            'priceTypes' => [
                'normal' => __('prices.normal'),
                'holiday' => __('prices.holiday'),
                'special_date' => __('prices.special_date')
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = Auth::user();
        $business = $user->business;
        $price = CourtPrice::findOrFail($id);
        $branchBelongsToBusiness = Branch::where('id', $price->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            abort(403, __('courts.branch_not_found_in_business'));
        }

        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'court_type' => 'required|string|in:pickleball,tennis,badminton,multi',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'price_per_hour' => 'required|numeric|min:0',
            'member_price_per_hour' => 'nullable|numeric|min:0',
            'price_type' => 'required|string|in:normal,holiday,special_date',
            'special_date' => 'nullable|required_if:price_type,special_date|date',
            'days' => 'nullable|string',
            'mode' => 'nullable|string|max:50',
            'notes' => 'nullable|string',
            'description' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ], __('validation.court_price'));
        $newBranchBelongsToBusiness = Branch::where('id', $request->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$newBranchBelongsToBusiness) {
            return back()->withErrors([
                'branch_id' => __('courts.branch_not_found_in_business'),
            ]);
        }

        $existingPrice = CourtPrice::where('branch_id', $request->branch_id)
            ->where('court_type', $request->court_type)
            ->where('price_type', $request->price_type)
            ->where('id', '!=', $id)
            ->when($request->price_type === 'special_date', function ($q) use ($request) {
                return $q->where('special_date', $request->special_date);
            })
            ->when($request->price_type !== 'special_date', function ($q) {
                return $q->whereNull('special_date');
            })
            ->where(function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $query->where('start_time', '<', $request->end_time)
                        ->where('end_time', '>', $request->start_time);
                });
            })
            ->first();

        if ($existingPrice) {
            return back()->withErrors([
                'start_time' => __('prices.time_range_overlap'),
            ]);
        }

        $price->update([
            'branch_id' => $request->branch_id,
            'court_type' => $request->court_type,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'price_per_hour' => $request->price_per_hour,
            'member_price_per_hour' => $request->member_price_per_hour,
            'is_active' => $request->is_active ?? true,
            'price_type' => $request->price_type,
            'special_date' => $request->price_type === 'special_date' ? $request->special_date : null,
            'days' => $request->days,
            'mode' => $request->mode,
            'notes' => $request->notes,
            'description' => $request->description,
        ]);

        return redirect()->route('business.prices.index', ['branch_id' => $request->branch_id, 'price_type' => $request->price_type])
            ->with('message', __('prices.price_updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();
        $business = $user->business;
        $price = CourtPrice::findOrFail($id);
        $branchBelongsToBusiness = Branch::where('id', $price->branch_id)
            ->where('business_id', $business->id)
            ->exists();

        if (!$branchBelongsToBusiness) {
            abort(403, __('courts.branch_not_found_in_business'));
        }

        $price->delete();

        return redirect()->route('business.prices.index', ['branch_id' => $price->branch_id, 'price_type' => $price->price_type])
            ->with('message', __('prices.price_deleted_successfully'));
    }

    /**
     * Bulk save pricing data for a branch
     */
    public function bulkSave(Request $request)
    {
        $user = Auth::user();
        $isSuperAdmin = $user->hasRole('super-admin');
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'price_data' => 'required|array',
            'price_data.*.start_time' => 'required|date_format:H:i',
            'price_data.*.end_time' => 'required|date_format:H:i',
            'price_data.*.price_per_hour' => 'required|numeric|min:0',
            'price_data.*.member_price_per_hour' => 'nullable|numeric|min:0',
            'price_data.*.days' => 'nullable|string',
            'price_data.*.mode' => 'nullable|string|max:50',
            'price_data.*.price_type' => 'required|string|in:normal,holiday,special_date',
            'notes' => 'nullable|string',
        ], __('validation.court_price'));

        if ($isSuperAdmin) {
            $branch = Branch::findOrFail($request->branch_id);
            $businessId = $branch->business_id;
        } else {
            $business = $user->business;
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'message' => __('courts.business_not_found'),
                ], 403);
            }
            $businessId = $business->id;
        }

        $branch = Branch::where('id', $request->branch_id)
            ->where('business_id', $businessId)
            ->first();

        if (!$branch) {
            return response()->json([
                'success' => false,
                'message' => __('courts.branch_not_found_in_business'),
            ], 403);
        }

        $openingHour = Carbon::parse($branch->opening_hour);
        $closingHour = Carbon::parse($branch->closing_hour);

        $normalizedData = [];
        foreach ($request->price_data as $index => $priceData) {
            $normalizedItem = $priceData;

            if (isset($normalizedItem['days'])) {
                if ($normalizedItem['days'] === 'all') {
                    $normalizedItem['days'] = '2,3,4,5,6,7,cn';
                }
            } else if ($normalizedItem['price_type'] === 'normal') {
                $normalizedItem['days'] = '2,3,4,5,6,7,cn';
            }

            if ($normalizedItem['price_type'] === 'special_date') {
                unset($normalizedItem['days']);

                if (isset($normalizedItem['special_date'])) {
                    $normalizedItem['special_date'] = Carbon::parse($normalizedItem['special_date'])->format('Y-m-d');
                }
            }

            $normalizedData[] = $normalizedItem;
        }

        $request->merge(['price_data' => $normalizedData]);

        $normalPrices = collect($request->price_data)->where('price_type', 'normal')->values();
        $specialDatePrices = collect($request->price_data)->where('price_type', 'special_date')->values();

        $errors = $this->validateTimeRanges($normalPrices, $openingHour, $closingHour);

        if ($specialDatePrices->isNotEmpty()) {
            $specialDateErrors = $this->validateTimeRanges($specialDatePrices, $openingHour, $closingHour);
            $errors = array_merge($errors, $specialDateErrors);
        }

        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi trong dữ liệu giá',
                'errors' => $errors
            ], 422);
        }

        DB::beginTransaction();

        try {
            CourtPrice::where('branch_id', $request->branch_id)->delete();
            $today = Carbon::now()->startOfDay();
            $processedRecords = [];
            $maxPrice = 0;
            $minPrice = 9999999999999999;
            foreach ($request->price_data as $index => $priceData) {
                $status = 'active';

                if ($priceData['price_type'] === 'special_date' && isset($priceData['special_date'])) {
                    $specialDate = Carbon::parse($priceData['special_date'])->startOfDay();

                    if ($specialDate->eq($today)) {
                        $status = 'active';
                    } elseif ($specialDate->lt($today)) {
                        $status = 'expired';
                    } else {
                        $status = 'not_started';
                    }
                }

                $createData = [
                    'branch_id' => $request->branch_id,
                    'court_type' => $priceData['court_type'] ?? $request->court_type,
                    'start_time' => $priceData['start_time'],
                    'end_time' => $priceData['end_time'],
                    'price_per_hour' => $priceData['price_per_hour'],
                    'member_price_per_hour' => $priceData['member_price_per_hour'] ?? null,
                    'is_active' => $priceData['is_active'] ?? true,
                    'price_type' => $priceData['price_type'],
                    'mode' => $priceData['mode'] ?? null,
                    'status' => $status,
                    'notes' => $request->notes,
                ];

                if ($priceData['price_type'] === 'special_date' && isset($priceData['special_date'])) {
                    $createData['special_date'] = Carbon::parse($priceData['special_date'])->format('Y-m-d');
                }

                if ($priceData['price_type'] === 'normal' && isset($priceData['days'])) {
                    $createData['days'] = $priceData['days'];
                }

                $uniqueKey = $createData['branch_id'] . '-' .
                    $createData['court_type'] . '-' .
                    $createData['start_time'] . '-' .
                    $createData['end_time'] . '-' .
                    ($createData['special_date'] ?? '');

                $processedRecords[] = $uniqueKey;
                if ($createData['price_per_hour'] > $maxPrice) {
                    $maxPrice = $createData['price_per_hour'];
                }
                if ($createData['price_per_hour'] < $minPrice) {
                    $minPrice = $createData['price_per_hour'];
                }
                CourtPrice::create($createData);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bảng giá đã lưu thành công!',
                'data' => [
                    'branch_id' => $request->branch_id,
                    'price_count' => count(array_unique($processedRecords)),
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'error' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Kiểm tra tính hợp lệ của các khoảng thời gian.
     *
     * @param \Illuminate\Support\Collection $prices
     * @param \Carbon\Carbon $openingHour
     * @param \Carbon\Carbon $closingHour
     * @return array
     */
    private function validateTimeRanges($prices, $openingHour, $closingHour)
    {
        $errors = [];

        
        foreach ($prices as $index => $price) {
            $startTime = Carbon::parse($price['start_time']);
            $endTime = Carbon::parse($price['end_time']);

            if ($startTime->gte($endTime)) {
                $errors[] = "Mục giá #" . ($index + 1) . ": Thời gian bắt đầu phải sớm hơn thời gian kết thúc.";
                continue;
            }

            if ($startTime->lt($openingHour)) {
                $errors[] = "Mục giá #" . ($index + 1) . ": Thời gian bắt đầu (" . $startTime->format('H:i') . ") phải sau hoặc bằng giờ mở cửa (" . $openingHour->format('H:i') . ").";
            }

            if ($endTime->gt($closingHour)) {
                $errors[] = "Mục giá #" . ($index + 1) . ": Thời gian kết thúc (" . $endTime->format('H:i') . ") phải trước hoặc bằng giờ đóng cửa (" . $closingHour->format('H:i') . ").";
            }
        }

        
        for ($i = 0; $i < count($prices); $i++) {
            $startTime1 = Carbon::parse($prices[$i]['start_time']);
            $endTime1 = Carbon::parse($prices[$i]['end_time']);
            $days1 = $prices[$i]['days'] ?? 'all';
            $isSpecialDate1 = isset($prices[$i]['price_type']) && $prices[$i]['price_type'] === 'special_date';
            $specialDate1 = $isSpecialDate1 ? ($prices[$i]['special_date'] ?? null) : null;
            $courtType1 = $prices[$i]['court_type'] ?? null;

            for ($j = $i + 1; $j < count($prices); $j++) {
                $startTime2 = Carbon::parse($prices[$j]['start_time']);
                $endTime2 = Carbon::parse($prices[$j]['end_time']);
                $days2 = $prices[$j]['days'] ?? 'all';
                $isSpecialDate2 = isset($prices[$j]['price_type']) && $prices[$j]['price_type'] === 'special_date';
                $specialDate2 = $isSpecialDate2 ? ($prices[$j]['special_date'] ?? null) : null;
                $courtType2 = $prices[$j]['court_type'] ?? null;

                
                if ($courtType1 !== $courtType2) {
                    continue;
                }

                if ($isSpecialDate1 && $isSpecialDate2) {
                    if ($specialDate1 !== $specialDate2) {
                        continue;
                    }
                }

                if ($this->timeRangesOverlap($startTime1, $endTime1, $startTime2, $endTime2)) {
                    if (!$isSpecialDate1 && !$isSpecialDate2) {
                        if ($this->daysOverlap($days1, $days2)) {
                            $errors[] = "Mục giá #" . ($i + 1) . " và #" . ($j + 1) . " có thời gian trùng lặp (" .
                                $startTime1->format('H:i') . "-" . $endTime1->format('H:i') . " và " .
                                $startTime2->format('H:i') . "-" . $endTime2->format('H:i') . ").";
                        }
                    } elseif ($isSpecialDate1 && $isSpecialDate2 && $specialDate1 === $specialDate2) {
                        $errors[] = "Mục giá đặc biệt ngày " . Carbon::parse($specialDate1)->format('d/m/Y') .
                            " #" . ($i + 1) . " và #" . ($j + 1) . " có thời gian trùng lặp (" .
                            $startTime1->format('H:i') . "-" . $endTime1->format('H:i') . " và " .
                            $startTime2->format('H:i') . "-" . $endTime2->format('H:i') . ").";
                    }
                }
            }
        }

        $pricesByCourtType = collect($prices)->groupBy('court_type');
        foreach ($pricesByCourtType as $courtType => $courtTypePrices) {
            $coverageErrors = $this->checkFullCoverage($courtTypePrices, $openingHour, $closingHour);
            if (!empty($coverageErrors)) {
                $errors = array_merge($errors, array_map(function ($error) use ($courtType) {
                    return "Sân $courtType: $error";
                }, $coverageErrors));
            }
        }

        return $errors;
    }

    /**
     * Kiểm tra xem bảng giá có phủ đầy từ giờ mở cửa đến giờ đóng cửa không
     *
     * @param array|\Illuminate\Support\Collection $prices
     * @param \Carbon\Carbon $openingHour
     * @param \Carbon\Carbon $closingHour
     * @return array
     */
    private function checkFullCoverage($prices, $openingHour, $closingHour)
    {
        $errors = [];

        
        $normalPrices = [];
        $specialDatePrices = [];

        foreach ($prices as $price) {
            if (isset($price['price_type']) && $price['price_type'] === 'special_date') {
                $date = $price['special_date'] ?? null;
                if ($date) {
                    if (!isset($specialDatePrices[$date])) {
                        $specialDatePrices[$date] = [];
                    }
                    $specialDatePrices[$date][] = $price;
                }
            } else {
                $days = $price['days'] ?? 'all';

                
                if (is_string($days) && $days !== 'all') {
                    $days = explode(',', $days);
                }

                if ($days === 'all') {
                    
                    $daysOfWeek = ['2', '3', '4', '5', '6', '7', 'cn'];
                    foreach ($daysOfWeek as $day) {
                        if (!isset($normalPrices[$day])) {
                            $normalPrices[$day] = [];
                        }
                        $normalPrices[$day][] = $price;
                    }
                } else if (is_array($days)) {
                    
                    foreach ($days as $day) {
                        if (!isset($normalPrices[$day])) {
                            $normalPrices[$day] = [];
                        }
                        $normalPrices[$day][] = $price;
                    }
                }
            }
        }

        
        if (!empty($normalPrices)) {
            $dayNames = [
                '2' => 'Thứ hai',
                '3' => 'Thứ ba',
                '4' => 'Thứ tư',
                '5' => 'Thứ năm',
                '6' => 'Thứ sáu',
                '7' => 'Thứ bảy',
                'cn' => 'Chủ nhật'
            ];

            foreach ($dayNames as $day => $dayName) {
                if (!isset($normalPrices[$day]) || empty($normalPrices[$day])) {
                    $errors[] = "Chưa có bảng giá cho " . $dayName . ".";
                    continue;
                }

                $dayErrors = $this->checkCoverageForTimeRanges($normalPrices[$day], $openingHour, $closingHour);
                if (!empty($dayErrors)) {
                    foreach ($dayErrors as $error) {
                        $errors[] = "Ngày " . $dayName . ": " . $error;
                    }
                }
            }
        }

        
        foreach ($specialDatePrices as $date => $datePrices) {
            $formattedDate = Carbon::parse($date)->format('d/m/Y');
            $dateErrors = $this->checkCoverageForTimeRanges($datePrices, $openingHour, $closingHour);
            if (!empty($dateErrors)) {
                foreach ($dateErrors as $error) {
                    $errors[] = "Ngày đặc biệt " . $formattedDate . ": " . $error;
                }
            }
        }

        return $errors;
    }

    /**
     * Kiểm tra phủ đầy thời gian cho một tập hợp các mục giá
     *
     * @param array $timeRanges
     * @param \Carbon\Carbon $openingHour
     * @param \Carbon\Carbon $closingHour
     * @return array
     */
    private function checkCoverageForTimeRanges($timeRanges, $openingHour, $closingHour)
    {
        $errors = [];

        
        $ranges = [];
        foreach ($timeRanges as $range) {
            $ranges[] = [
                'start' => Carbon::parse($range['start_time']),
                'end' => Carbon::parse($range['end_time'])
            ];
        }

        
        usort($ranges, function ($a, $b) {
            return $a['start']->lt($b['start']) ? -1 : 1;
        });

        
        $currentTime = clone $openingHour;

        foreach ($ranges as $range) {
            
            
            if ($currentTime->lt($range['start'])) {
                $errors[] = "Thiếu bảng giá từ " . $currentTime->format('H:i') . " đến " . $range['start']->format('H:i');
            }

            
            if ($range['end']->gt($currentTime)) {
                $currentTime = clone $range['end'];
            }
        }

        
        if ($currentTime->lt($closingHour)) {
            $errors[] = "Thiếu bảng giá từ " . $currentTime->format('H:i') . " đến " . $closingHour->format('H:i');
        }

        return $errors;
    }

    /**
     * Kiểm tra xem hai khoảng thời gian có trùng lặp không.
     *
     * @param \Carbon\Carbon $start1
     * @param \Carbon\Carbon $end1
     * @param \Carbon\Carbon $start2
     * @param \Carbon\Carbon $end2
     * @return bool
     */
    private function timeRangesOverlap($start1, $end1, $start2, $end2)
    {
        return $start1->lt($end2) && $end1->gt($start2);
    }

    /**
     * Kiểm tra xem hai chuỗi ngày có trùng lặp không.
     *
     * @param string $days1
     * @param string $days2
     * @return bool
     */
    private function daysOverlap($days1, $days2)
    {
        if ($days1 === 'all' || $days2 === 'all') {
            return true;
        }
        $daysArray1 = explode(',', $days1);
        $daysArray2 = explode(',', $days2);
        return !empty(array_intersect($daysArray1, $daysArray2));
    }

    /**
     * Get prices for a specific date
     * 
     * @param Request $request
     * @param string $id Branch ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPricesByDate(Request $request, $id)
    {
        try {
            $date = $request->input('date');
            if (!$date) {
                return response()->json([
                    'success' => false,
                    'message' => 'Date parameter is required'
                ], 400);
            }

            $carbonDate = Carbon::parse($date);
            $dayOfWeek = strtolower($carbonDate->format('D'));
            if ($dayOfWeek === 'sun') {
                $dayOfWeek = 'cn';
            } else {
                $dayOfWeek = $carbonDate->format('N');
            }

            
            $prices = CourtPrice::where('branch_id', $id)
                ->where('is_active', true)
                ->where(function ($query) use ($dayOfWeek) {
                    $query->where('days', 'like', '%' . $dayOfWeek . '%')
                        ->orWhere('days', 'all');
                })
                ->where('price_type', 'normal');

            
            $specialPrices = CourtPrice::where('branch_id', $id)
                ->where('is_active', true)
                ->where('price_type', 'special_date')
                ->where('special_date', $carbonDate->format('Y-m-d'));

            
            $allPrices = [];

            
            foreach ($prices->get() as $price) {
                $allPrices[$price->court_type][] = [
                    'id' => $price->id,
                    'start_time' => Carbon::parse($price->start_time)->format('H:i'),
                    'end_time' => Carbon::parse($price->end_time)->format('H:i'),
                    'price_per_hour' => $price->price_per_hour,
                    'member_price_per_hour' => $price->member_price_per_hour,
                    'price_type' => $price->price_type,
                    'description' => $price->description,
                    'court_type' => $price->court_type
                ];
            }

            
            foreach ($specialPrices->get() as $price) {
                
                if (isset($allPrices[$price->court_type])) {
                    $allPrices[$price->court_type] = array_filter(
                        $allPrices[$price->court_type],
                        function ($normalPrice) use ($price) {
                            $specialStart = Carbon::parse($price->start_time)->format('H:i');
                            $specialEnd = Carbon::parse($price->end_time)->format('H:i');
                            return !($normalPrice['start_time'] === $specialStart &&
                                $normalPrice['end_time'] === $specialEnd);
                        }
                    );
                }

                
                $allPrices[$price->court_type][] = [
                    'id' => $price->id,
                    'start_time' => Carbon::parse($price->start_time)->format('H:i'),
                    'end_time' => Carbon::parse($price->end_time)->format('H:i'),
                    'price_per_hour' => $price->price_per_hour,
                    'member_price_per_hour' => $price->member_price_per_hour,
                    'price_type' => $price->price_type,
                    'description' => $price->description,
                    'court_type' => $price->court_type,
                    'special_date' => $price->special_date
                ];
            }

            
            foreach ($allPrices as &$courtTypePrices) {
                usort($courtTypePrices, function ($a, $b) {
                    return strcmp($a['start_time'], $b['start_time']);
                });
                $courtTypePrices = array_values($courtTypePrices);
            }

            return response()->json([
                'success' => true,
                'data' => $allPrices
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving prices: ' . $e->getMessage()
            ], 500);
        }
    }
}
