<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Business;
use App\Models\CourtService;
use App\Models\CourtServiceAssign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CourtServiceAssignController extends Controller
{
    /**
     * Display a listing of available services and assigned services.
     */
    public function index(Request $request)
    {
        $business = $request->user()->business;

        // Get all available court services from super admin
        $availableServices = CourtService::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get services already assigned to this business
        $assignedServices = $business->assignedCourtServices()
            ->with([
                'serviceAssigns' => function ($query) use ($business) {
                    $query->where('business_id', $business->id);
                }
            ])
            ->get();

        // Get all branches for this business for assignment
        $branches = $business->branches()
            ->where('status', 'active')
            ->select('id', 'name')
            ->get();

        return Inertia::render('Business/Services/Index', [
            'availableServices' => $availableServices,
            'assignedServices' => $assignedServices,
            'branches' => $branches,
        ]);
    }

    /**
     * Show the form for assigning a new service.
     */
    public function create(Request $request)
    {
        $business = $request->user()->business;

        // Get all available court services from super admin
        $availableServices = CourtService::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get all branches for this business for assignment
        $branches = $business->branches()
            ->where('status', 'active')
            ->select('id', 'name')
            ->get();

        return Inertia::render('Business/Services/Create', [
            'availableServices' => $availableServices,
            'branches' => $branches,
        ]);
    }

    /**
     * Store a newly created service assignment.
     */
    public function store(Request $request)
    {
        $business = $request->user()->business;

        $validated = $request->validate([
            'court_service_id' => 'required|exists:court_services,id',
            'branch_id' => 'required|exists:branches,id',
            'price' => 'required|numeric|min:0',
            'unit' => 'required|string|in:hour,session,day',
            'discount_type' => 'nullable|string|in:fixed,percentage',
            'discount_person' => 'nullable|string|in:member,student,senior',
            'discount_amount' => 'nullable|required_with:discount_type|numeric|min:0',
            'status' => 'required|string|in:active,inactive',
            'settings' => 'nullable|array',
        ]);

        // Verify the branch belongs to this business
        $branch = Branch::where('id', $validated['branch_id'])
            ->where('business_id', $business->id)
            ->firstOrFail();

        // Add business_id to the validated data
        $validated['business_id'] = $business->id;

        // Check if this service is already assigned to this branch
        $existingAssignment = CourtServiceAssign::where('court_service_id', $validated['court_service_id'])
            ->where('branch_id', $validated['branch_id'])
            ->first();

        if ($existingAssignment) {
            return back()->withErrors([
                'court_service_id' => __('service.already_assigned_to_branch')
            ]);
        }

        $serviceAssign = CourtServiceAssign::create($validated);

        return redirect()->route('business.services.index')
            ->with('message', __('service.service_assigned'));
    }

    /**
     * Show the form for editing a service assignment.
     */
    public function edit(Request $request, CourtServiceAssign $serviceAssign)
    {
        $business = $request->user()->business;

        // Ensure the service assignment belongs to this business
        if ($serviceAssign->business_id !== $business->id) {
            abort(403);
        }

        // Get all branches for this business for assignment
        $branches = $business->branches()
            ->where('status', 'active')
            ->select('id', 'name')
            ->get();

        return Inertia::render('Business/Services/Edit', [
            'serviceAssign' => $serviceAssign->load('courtService'),
            'branches' => $branches,
        ]);
    }

    /**
     * Update the specified service assignment.
     */
    public function update(Request $request, CourtServiceAssign $serviceAssign)
    {
        $business = $request->user()->business;

        // Ensure the service assignment belongs to this business
        if ($serviceAssign->business_id !== $business->id) {
            abort(403);
        }

        $validated = $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'price' => 'required|numeric|min:0',
            'unit' => 'required|string|in:hour,session,day',
            'discount_type' => 'nullable|string|in:fixed,percentage',
            'discount_person' => 'nullable|string|in:member,student,senior',
            'discount_amount' => 'nullable|required_with:discount_type|numeric|min:0',
            'status' => 'required|string|in:active,inactive',
            'settings' => 'nullable|array',
        ]);

        // Verify the branch belongs to this business
        $branch = Branch::where('id', $validated['branch_id'])
            ->where('business_id', $business->id)
            ->firstOrFail();

        // Check if changing branch and if service already exists for that branch
        if ($serviceAssign->branch_id != $validated['branch_id']) {
            $existingAssignment = CourtServiceAssign::where('court_service_id', $serviceAssign->court_service_id)
                ->where('branch_id', $validated['branch_id'])
                ->first();

            if ($existingAssignment) {
                return back()->withErrors([
                    'branch_id' => __('service.already_assigned_to_branch')
                ]);
            }
        }

        $serviceAssign->update($validated);

        return redirect()->route('business.services.index')
            ->with('message', __('service.service_updated'));
    }

    /**
     * Remove the specified service assignment.
     */
    public function destroy(Request $request, CourtServiceAssign $serviceAssign)
    {
        $business = $request->user()->business;

        // Ensure the service assignment belongs to this business
        if ($serviceAssign->business_id !== $business->id) {
            abort(403);
        }

        $serviceAssign->delete();

        return redirect()->route('business.services.index')
            ->with('message', __('service.service_removed'));
    }
}
