<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\User;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class CustomerController extends Controller
{
    /**
     * Display a listing of the customers belonging to the current business.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        $search = $request->search;
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $customerIdsWithBookings = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->where('courts.business_id', $businessId)
            ->select('court_bookings.customer_id')
            ->distinct()
            ->pluck('customer_id');

        $query = Customer::whereIn('id', $customerIdsWithBookings);

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($sortField === 'booking_count') {
            $bookingCountSubQuery = DB::table('court_bookings')
                ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
                ->where('courts.business_id', $businessId)
                ->select('court_bookings.customer_id', DB::raw('COUNT(*) as booking_count'))
                ->groupBy('court_bookings.customer_id');

            $query->leftJoinSub($bookingCountSubQuery, 'booking_counts', function ($join) {
                $join->on('customers.id', '=', 'booking_counts.customer_id');
            })
                ->orderBy('booking_counts.booking_count', $sortDirection);
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $customers = $query->paginate(10)->withQueryString();

        
        $customers->getCollection()->transform(function ($customer) use ($businessId) {
            $bookingCount = DB::table('court_bookings')
                ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
                ->where('courts.business_id', $businessId)
                ->where('court_bookings.customer_id', $customer->id)
                ->count();

            $customer->booking_count = $bookingCount;

            $lastBooking = DB::table('court_bookings')
                ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
                ->where('courts.business_id', $businessId)
                ->where('court_bookings.customer_id', $customer->id)
                ->orderBy('court_bookings.booking_date', 'desc')
                ->first(['court_bookings.booking_date']);

            $customer->last_booking_date = $lastBooking ? $lastBooking->booking_date : null;

            return $customer;
        });

        return Inertia::render('Business/Customers/Index', [
            'customers' => $customers,
            'filters' => [
                'search' => $search ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    /**
     * Display the specified customer.
     *
     * @param  \App\Models\Customer  $customer
     * @return \Inertia\Response
     */
    public function show(Request $request, Customer $customer)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        
        $hasBookings = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->where('courts.business_id', $businessId)
            ->where('court_bookings.customer_id', $customer->id)
            ->exists();

        if (!$hasBookings) {
            return redirect()->route('business.customers.index')
                ->with('flash.type', 'error')
                ->with('flash.message', __('business.customer_not_found'));
        }

        
        $bookings = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('courts.business_id', $businessId)
            ->where('court_bookings.customer_id', $customer->id)
            ->orderBy('court_bookings.booking_date', 'desc')
            ->orderBy('court_bookings.start_time', 'desc')
            ->select([
                'court_bookings.*',
                'courts.name as court_name',
                'branches.name as branch_name'
            ])
            ->paginate(5);

        
        $totalBookingsCount = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->where('courts.business_id', $businessId)
            ->where('court_bookings.customer_id', $customer->id)
            ->count();

        
        $customer->booking_count = $totalBookingsCount;

        return Inertia::render('Business/Customers/Show', [
            'customer' => $customer,
            'bookings' => $bookings,
        ]);
    }
}