<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\Statistic;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

class DashboardController extends Controller
{
    /**
     * Display the business dashboard
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {
            $stats = $this->getCustomRangeStats($businessId, $fromDate, $toDate);

            Log::info("Business Dashboard Custom Range Stats", [
                'business_id' => $businessId,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'stats' => $stats
            ]);

            return Inertia::render('Business/Dashboard', [
                'stats' => $stats,
                'is_custom_range' => true,
                'custom_range' => [
                    'from' => $fromDate,
                    'to' => $toDate,
                    'formatted' => Carbon::parse($fromDate)->format('d/m/Y') . ' - ' . Carbon::parse($toDate)->format('d/m/Y')
                ],
                'last_updated' => now()->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s')
            ]);
        }

        $period = $request->input('period', 'monthly');
        $stats = $this->getDashboardStats($businessId, $period);

        Log::info("Business Dashboard Stats (Direct Calculation)", [
            'business_id' => $businessId,
            'period' => $period,
            'stats' => $stats
        ]);

        return Inertia::render('Business/Dashboard', [
            'stats' => $stats,
            'last_updated' => now()->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s'),
            'current_period' => $period
        ]);
    }

    /**
     * Get dashboard statistics for the Business
     *
     * @param int $businessId
     * @param string $period
     * @return array
     */
    public function getDashboardStats($businessId, $period = 'monthly')
    {
        $now = Carbon::now()->timezone('Asia/Ho_Chi_Minh');
        $previousMonth = $now->copy()->subMonth();
        $currentMonth = $now->copy()->startOfMonth();

        $totalBranches = Branch::where('business_id', $businessId)->count();

        $totalUsers = User::where('business_id', $businessId)->count();
        $previousMonthUsers = User::where('business_id', $businessId)
            ->where('created_at', '<', $currentMonth)
            ->count();
        $newUsers = $totalUsers - $previousMonthUsers;
        $userGrowth = $previousMonthUsers > 0 ? round(($newUsers / $previousMonthUsers) * 100, 1) : 0;

        $totalCourts = Court::whereHas('branch', function ($query) use ($businessId) {
            $query->where('business_id', $businessId);
        })->count();

        $activeCourts = Court::whereHas('branch', function ($query) use ($businessId) {
            $query->where('business_id', $businessId);
        })->where('is_active', 1)->count();

        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;

        $totalRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')
            ->sum('total_price');

        $currentMonthRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')
            ->whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->sum('total_price');

        $previousMonthRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')
            ->whereYear('booking_date', $previousMonth->year)
            ->whereMonth('booking_date', $previousMonth->month)
            ->sum('total_price');

        $revenueGrowth = $previousMonthRevenue > 0 ? round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1) : 0;

        $chartData = $this->getChartDataByPeriod($businessId, $period, $now);
        $labels = $chartData['labels'];
        $bookings = $chartData['bookings'];
        $revenue = $chartData['revenue'];

        $weeklyData = [];
        $startOfMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();
        $daysInMonth = $endOfMonth->day;

        $weekSizes = [
            ceil($daysInMonth / 4),
            ceil($daysInMonth / 4),
            ceil($daysInMonth / 4),
            $daysInMonth - (3 * ceil($daysInMonth / 4))
        ];

        if ($weekSizes[3] <= 0) {
            $weekSizes[3] = 1;
            $weekSizes[0]--;
        }

        $currentDate = $startOfMonth->copy();

        for ($weekIndex = 0; $weekIndex < 4; $weekIndex++) {
            $weekSize = $weekSizes[$weekIndex];

            $weekStart = $currentDate->copy();
            $weekEnd = $currentDate->copy()->addDays($weekSize - 1);

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $weeklyBookings = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

            $weeklyRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })->where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

            $weeklyData[] = [
                'week' => __('dashboard.week') . ' ' . ($weekIndex + 1),
                'start_date' => $weekStart->format('d/m/Y'),
                'end_date' => $weekEnd->format('d/m/Y'),
                'bookings' => $weeklyBookings,
                'revenue' => $weeklyRevenue,
                'date_range' => $weekStart->format('d/m') . ' - ' . $weekEnd->format('d/m/Y')
            ];

            $currentDate->addDays($weekSize);
        }

        $topBranches = Branch::select('branches.id', 'branches.name')
            ->where('branches.business_id', $businessId)
            ->join('courts', 'branches.id', '=', 'courts.branch_id')
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->where('court_bookings.status', 'completed')
            ->groupBy('branches.id', 'branches.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(5)
            ->get()
            ->map(function ($branch) {
                $bookingCount = CourtBooking::whereHas('court', function ($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })->where('status', 'completed')->count();

                return [
                    'name' => $branch->name,
                    'bookings' => $bookingCount
                ];
            });

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        return [
            'users' => [
                'total' => $totalUsers,
                'growth' => $userGrowth
            ],
            'branches' => [
                'total' => $totalBranches
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $bookings,
                'revenue' => $revenue
            ],
            'weekly' => $weeklyData,
            'top_branches' => $topBranches
        ];
    }

    /**
     * Get chart data by period
     *
     * @param int $businessId
     * @param string $period
     * @param Carbon $now
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getChartDataByPeriod($businessId, $period, Carbon $now, $fromDate = null, $toDate = null)
    {
        $labels = [];
        $bookings = [];
        $revenue = [];

        if ($fromDate && $toDate) {
            $startDate = Carbon::parse($fromDate)->timezone('Asia/Ho_Chi_Minh');
            $endDate = Carbon::parse($toDate)->timezone('Asia/Ho_Chi_Minh');
            $diffInDays = $startDate->diffInDays($endDate);

            if ($diffInDays <= 7) {
                for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
                    $labels[] = $date->format('d/m');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereDate('booking_date', $date->format('Y-m-d'))
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereDate('booking_date', $date->format('Y-m-d'))
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
            } else if ($diffInDays <= 60) {
                $currentDate = $startDate->copy()->startOfWeek();
                while ($currentDate->lte($endDate)) {
                    $weekEnd = $currentDate->copy()->endOfWeek();
                    if ($weekEnd->gt($endDate)) {
                        $weekEnd = $endDate->copy();
                    }
                    $weekLabel = $currentDate->format('d/m') . ' - ' . $weekEnd->format('d/m');
                    $labels[] = $weekLabel;

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereBetween('booking_date', [$currentDate->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereBetween('booking_date', [$currentDate->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;

                    $currentDate->addWeek();
                    if ($currentDate->gt($endDate)) {
                        break;
                    }
                }
            } else {
                $currentDate = $startDate->copy()->startOfMonth();
                while ($currentDate->lte($endDate)) {
                    $monthEnd = $currentDate->copy()->endOfMonth();
                    if ($monthEnd->gt($endDate)) {
                        $monthEnd = $endDate->copy();
                    }
                    $labels[] = $currentDate->format('M Y');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $currentDate->year)
                        ->whereMonth('booking_date', $currentDate->month)
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $currentDate->year)
                        ->whereMonth('booking_date', $currentDate->month)
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;

                    $currentDate->addMonth();
                    if ($currentDate->gt($endDate)) {
                        break;
                    }
                }
            }

            return [
                'labels' => $labels,
                'bookings' => $bookings,
                'revenue' => $revenue
            ];
        }

        switch ($period) {
            case 'daily':
                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $labels[] = $hour->format('H:i');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':
                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':
                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:
                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                        $query->whereHas('branch', function ($q) use ($businessId) {
                            $q->where('business_id', $businessId);
                        });
                    })->where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $bookings[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'bookings' => $bookings,
            'revenue' => $revenue
        ];
    }

    /**
     * Get statistics for a custom date range
     *
     * @param int $businessId
     * @param string $fromDate Format: Y-m-d
     * @param string $toDate Format: Y-m-d
     * @return array
     */
    public function getCustomRangeStats($businessId, $fromDate, $toDate)
    {
        $from = Carbon::parse($fromDate)->timezone('Asia/Ho_Chi_Minh')->startOfDay();
        $to = Carbon::parse($toDate)->timezone('Asia/Ho_Chi_Minh')->endOfDay();

        $diffInDays = $from->diffInDays($to) + 1;
        $previousFrom = $from->copy()->subDays($diffInDays);
        $previousTo = $from->copy()->subDay();

        $totalBranches = Branch::where('business_id', $businessId)->count();

        $totalUsers = User::where('business_id', $businessId)->count();
        $newUsers = User::where('business_id', $businessId)
            ->whereBetween('created_at', [$from, $to])
            ->count();
        $previousPeriodUsers = User::where('business_id', $businessId)
            ->whereBetween('created_at', [$previousFrom, $previousTo])
            ->count();
        $userGrowth = $previousPeriodUsers > 0 ? round((($newUsers - $previousPeriodUsers) / $previousPeriodUsers) * 100, 1) : 0;

        $totalCourts = Court::whereHas('branch', function ($query) use ($businessId) {
            $query->where('business_id', $businessId);
        })->count();

        $activeCourts = Court::whereHas('branch', function ($query) use ($businessId) {
            $query->where('business_id', $businessId);
        })->where('is_active', 1)->count();

        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;

        $rangeRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')
            ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->sum('total_price');

        $previousRangeRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')
            ->whereBetween('booking_date', [$previousFrom->format('Y-m-d'), $previousTo->format('Y-m-d')])
            ->sum('total_price');

        $revenueGrowth = $previousRangeRevenue > 0 ? round((($rangeRevenue - $previousRangeRevenue) / $previousRangeRevenue) * 100, 1) : 0;

        $totalRevenue = CourtBooking::whereHas('court', function ($query) use ($businessId) {
            $query->whereHas('branch', function ($q) use ($businessId) {
                $q->where('business_id', $businessId);
            });
        })->where('status', 'completed')->sum('total_price');

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        $labels = [];
        $counts = [];
        $revenue = [];

        if ($diffInDays <= 31) {
            for ($i = 0; $i < $diffInDays; $i++) {
                $day = $from->copy()->addDays($i);
                $labels[] = $day->format('d/m');

                $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;
            }
        } else if ($diffInDays <= 90) {
            $currentDate = $from->copy();
            while ($currentDate->lte($to)) {
                $weekStart = $currentDate->copy()->startOfWeek();
                $weekEnd = $currentDate->copy()->endOfWeek();

                if ($weekEnd->gt($to)) {
                    $weekEnd = $to->copy();
                }

                $labels[] = $weekStart->format('d/m') . '-' . $weekEnd->format('d/m');

                $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addWeek();
            }
        } else {
            $currentDate = $from->copy()->startOfMonth();
            $endDate = $to->copy()->endOfMonth();

            while ($currentDate->lte($endDate)) {
                $monthStart = $currentDate->copy()->startOfMonth();
                $monthEnd = $currentDate->copy()->endOfMonth();

                if ($monthEnd->gt($to)) {
                    $monthEnd = $to->copy();
                }

                $labels[] = $currentDate->format('m/Y');

                $count = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->count();

                $rev = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                    $query->whereHas('branch', function ($q) use ($businessId) {
                        $q->where('business_id', $businessId);
                    });
                })->where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addMonth();
            }
        }

        $topBranches = Branch::select('branches.id', 'branches.name')
            ->where('branches.business_id', $businessId)
            ->join('courts', 'branches.id', '=', 'courts.branch_id')
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->groupBy('branches.id', 'branches.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(5)
            ->get()
            ->map(function ($branch) use ($from, $to) {
                $bookingCount = CourtBooking::whereHas('court', function ($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })
                    ->where('status', 'completed')
                    ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
                    ->count();

                return [
                    'name' => $branch->name,
                    'bookings' => $bookingCount
                ];
            });

        return [
            'users' => [
                'total' => $totalUsers,
                'new' => $newUsers,
                'growth' => $userGrowth
            ],
            'branches' => [
                'total' => $totalBranches
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'range_revenue' => $rangeRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $counts,
                'revenue' => $revenue
            ],
            'top_branches' => $topBranches,
            'date_range' => [
                'from' => $from->format('Y-m-d'),
                'to' => $to->format('Y-m-d'),
                'formatted' => $from->format('d/m/Y') . ' - ' . $to->format('d/m/Y')
            ]
        ];
    }

    /**
     * Display the business reports
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function reports(Request $request)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        $period = $request->input('period', 'monthly');
        $isCustomRange = false;
        $customRange = null;

        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {
            $stats = $this->getCustomRangeStats($businessId, $fromDate, $toDate);
            $isCustomRange = true;
            $fromDateObj = Carbon::parse($fromDate);
            $toDateObj = Carbon::parse($toDate);
            $customRange = [
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'formatted' => $fromDateObj->format('d/m/Y') . ' - ' . $toDateObj->format('d/m/Y')
            ];
        } else {
            $stats = $this->getDashboardStats($businessId, $period);
        }

        $now = Carbon::now();
        $chartData = $this->getChartDataByPeriod($businessId, $period, $now, $fromDate, $toDate);
        $stats['chart'] = $chartData;

        $topCourts = $this->getTopCourts($businessId, 10, $period, $fromDate, $toDate);
        $topBranches = $this->getTopBranches($businessId, 10, $period, $fromDate, $toDate);

        $bookingTrends = $this->getBookingTrends($businessId);

        $userMetrics = $this->getUserMetrics($businessId, $period, $fromDate, $toDate);

        $topUsers = $this->getTopUsers($businessId, 10, $period, $fromDate, $toDate);

        $recentPayments = $this->getRecentPayments($businessId, 10);

        $popularTimes = $this->getPopularBookingTimes($businessId, $period, $fromDate, $toDate);

        return Inertia::render('Business/Reports', [
            'stats' => $stats,
            'top_courts' => $topCourts,
            'top_branches' => $topBranches,
            'booking_trends' => $bookingTrends,
            'user_metrics' => $userMetrics,
            'top_users' => $topUsers,
            'recent_payments' => $recentPayments,
            'popular_times' => $popularTimes,
            'last_updated' => now()->format('Y-m-d H:i:s'),
            'current_period' => $period,
            'is_custom_range' => $isCustomRange,
            'custom_range' => $customRange
        ]);
    }

    /**
     * Get top users based on bookings for a business
     *
     * @param int $businessId
     * @param int $limit
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getTopUsers($businessId, $limit = 10, $period = 'monthly', $fromDate = null, $toDate = null)
    {
        list($startDate, $endDate) = $this->getDateRangeFromPeriod($period, $fromDate, $toDate);

        $topUsers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->leftJoin('payments', 'court_bookings.id', '=', 'payments.court_booking_id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                'users.id',
                'users.name',
                'users.email',
                DB::raw('COUNT(court_bookings.id) as booking_count'),
                DB::raw('SUM(COALESCE(payments.amount, court_bookings.total_price)) as total_spending'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date'),
                DB::raw('MAX(court_bookings.start_time) as last_booking_time')
            )
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderBy('booking_count', 'desc')
            ->orderBy('total_spending', 'desc')
            ->limit($limit)
            ->get();

        $index = 1;
        foreach ($topUsers as &$user) {
            $user->index = $index++;
            $user->formatted_spending = number_format($user->total_spending, 0, ',', '.') . ' ₫';

            if (!empty($user->last_booking_date) && !empty($user->last_booking_time)) {
                $bookingDate = Carbon::parse($user->last_booking_date)->format('d/m/Y');
                $bookingTime = Carbon::parse($user->last_booking_time)->format('H:i');
                $user->last_booking_date = $bookingDate . ' ' . $bookingTime;
            } else {
                $user->last_booking_date = '-';
            }
        }

        return $topUsers;
    }

    /**
     * Get recent payments for a business
     *
     * @param int $businessId
     * @param int $limit
     * @return array
     */
    public function getRecentPayments($businessId, $limit = 10)
    {
        $recentPayments = DB::table('payments')
            ->join('court_bookings', 'payments.court_booking_id', '=', 'court_bookings.id')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->join('users', 'court_bookings.user_id', '=', 'users.id')
            ->join('payment_methods', 'payments.payment_method_id', '=', 'payment_methods.id')
            ->where('branches.business_id', $businessId)
            ->select(
                'payments.id',
                'payments.transaction_id',
                'users.name as user_name',
                DB::raw("CONCAT(courts.name, ' (', branches.name, ')') as court_info"),
                'court_bookings.booking_date',
                'court_bookings.start_time',
                'payment_methods.payment_name as payment_method',
                'payments.amount',
                'payments.status'
            )
            ->orderBy('payments.created_at', 'desc')
            ->limit($limit)
            ->get();

        foreach ($recentPayments as &$payment) {
            $payment->formatted_amount = number_format($payment->amount, 0, ',', '.') . ' ₫';

            if (!empty($payment->booking_date) && !empty($payment->start_time)) {
                $bookingDate = Carbon::parse($payment->booking_date)->format('d/m/Y');
                $bookingTime = Carbon::parse($payment->start_time)->format('H:i');
                $payment->booking_date = $bookingDate . ' ' . $bookingTime;
            } else {
                $payment->booking_date = '-';
            }
        }

        return $recentPayments;
    }

    /**
     * Get popular booking times for a business
     *
     * @param int $businessId
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getPopularBookingTimes($businessId, $period = 'monthly', $fromDate = null, $toDate = null)
    {
        list($startDate, $endDate) = $this->getDateRangeFromPeriod($period, $fromDate, $toDate);

        $popularTimes = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                DB::raw("HOUR(court_bookings.start_time) as hour"),
                DB::raw("COUNT(*) as count"),
                DB::raw("SUM(court_bookings.total_price) as revenue")
            )
            ->groupBy(DB::raw("HOUR(court_bookings.start_time)"))
            ->orderBy('count', 'desc')
            ->get();

        $totalBookings = $popularTimes->sum('count');

        $formattedTimes = [];
        foreach ($popularTimes as $time) {
            $startHour = str_pad($time->hour, 2, '0', STR_PAD_LEFT);
            $endHour = str_pad(($time->hour + 1) % 24, 2, '0', STR_PAD_LEFT);

            $percentage = $totalBookings > 0 ? round(($time->count / $totalBookings) * 100, 1) : 0;

            $formattedTimes[] = [
                'time_slot' => "{$startHour}:00 - {$endHour}:00",
                'count' => $time->count,
                'percentage' => $percentage,
                'formatted_percentage' => "{$percentage}%",
                'revenue' => $time->revenue,
                'formatted_revenue' => number_format($time->revenue, 0, ',', '.') . ' ₫',
                'popularity_width' => min($percentage * 2, 100)
            ];
        }

        return $formattedTimes;
    }

    /**
     * Get user metrics for a business
     *
     * @param int $businessId
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getUserMetrics($businessId, $period = 'monthly', $fromDate = null, $toDate = null)
    {
        list($startDate, $endDate) = $this->getDateRangeFromPeriod($period, $fromDate, $toDate);

        $businessUsers = DB::table('users')
            ->where('business_id', $businessId)
            ->pluck('id')
            ->toArray();

        $bookingUsers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->pluck('court_bookings.user_id')
            ->toArray();

        $allRelevantUserIds = array_unique(array_merge($businessUsers, $bookingUsers));

        Log::info("User Metrics - Business: {$businessId}, Users count: " . count($allRelevantUserIds));

        $roleDistribution = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', 'App\\Models\\User')
            ->whereIn('model_has_roles.model_id', function ($query) use ($businessId) {
                $query->select('id')
                    ->from('users')
                    ->where(function ($q) use ($businessId) {
                        $q->where('business_id', $businessId)
                            ->orWhereIn('id', function ($sq) use ($businessId) {
                                $sq->select('user_id')
                                    ->from('court_bookings')
                                    ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
                                    ->join('branches', 'courts.branch_id', '=', 'branches.id')
                                    ->where('branches.business_id', $businessId);
                            });
                    });
            })
            ->select('roles.name as role', DB::raw('COUNT(DISTINCT model_has_roles.model_id) as count'))
            ->groupBy('roles.name')
            ->orderBy('count', 'desc')
            ->get();

        $superAdminCount = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', 'App\\Models\\User')
            ->where('roles.name', 'super-admin')
            ->count();

        $adminRolesExist = false;
        foreach ($roleDistribution as $role) {
            if (in_array($role->role, ['super-admin', 'admin'])) {
                $adminRolesExist = true;
                break;
            }
        }

        if ($superAdminCount > 0 && !$adminRolesExist) {
            $roleDistribution->push((object) [
                'role' => 'super-admin',
                'count' => $superAdminCount
            ]);
        }

        $activityByDay = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                DB::raw("DAYOFWEEK(court_bookings.booking_date) as day_num"),
                DB::raw("DATE_FORMAT(court_bookings.booking_date, '%d/%m') as date"),
                DB::raw("COUNT(*) as count")
            )
            ->groupBy('day_num', 'date')
            ->orderBy('day_num')
            ->get();

        $dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
        $formattedActivity = [];

        for ($i = 1; $i <= 7; $i++) {
            $found = false;
            foreach ($activityByDay as $activity) {
                if ($activity->day_num == $i) {
                    $formattedActivity[] = [
                        'day' => $dayNames[$i - 1],
                        'date' => $activity->date,
                        'count' => $activity->count
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $formattedActivity[] = [
                    'day' => $dayNames[$i - 1],
                    'date' => '-',
                    'count' => 0
                ];
            }
        }

        return [
            'role_distribution' => $roleDistribution,
            'activity_by_day' => $formattedActivity
        ];
    }

    /**
     * Get booking trends for a business
     *
     * @param int $businessId
     * @return array
     */
    public function getBookingTrends($businessId)
    {
        $now = Carbon::now();

        Log::info("Calculating booking trends for business ID: {$businessId}, current time: {$now}");

        $todayBookings = $this->getBookingCountForDateRange(
            $businessId,
            $now->copy()->startOfDay(),
            $now->copy()->endOfDay()
        );

        $yesterdayBookings = $this->getBookingCountForDateRange(
            $businessId,
            $now->copy()->subDay()->startOfDay(),
            $now->copy()->subDay()->endOfDay()
        );

        Log::info("Daily bookings - Today: {$todayBookings}, Yesterday: {$yesterdayBookings}");

        $thisWeekStart = $now->copy()->startOfWeek();
        $thisWeekEnd = $now->copy()->endOfWeek();
        $lastWeekStart = $now->copy()->subWeek()->startOfWeek();
        $lastWeekEnd = $now->copy()->subWeek()->endOfWeek();

        $thisWeekBookings = $this->getBookingCountForDateRange(
            $businessId,
            $thisWeekStart,
            $thisWeekEnd
        );

        $lastWeekBookings = $this->getBookingCountForDateRange(
            $businessId,
            $lastWeekStart,
            $lastWeekEnd
        );

        Log::info("Weekly bookings - This week ({$thisWeekStart->format('Y-m-d')} to {$thisWeekEnd->format('Y-m-d')}): {$thisWeekBookings}, Last week: {$lastWeekBookings}");

        $thisMonthStart = $now->copy()->startOfMonth();
        $thisMonthEnd = $now->copy()->endOfMonth();
        $lastMonthStart = $now->copy()->subMonth()->startOfMonth();
        $lastMonthEnd = $now->copy()->subMonth()->endOfMonth();

        $thisMonthBookings = $this->getBookingCountForDateRange(
            $businessId,
            $thisMonthStart,
            $thisMonthEnd
        );

        $lastMonthBookings = $this->getBookingCountForDateRange(
            $businessId,
            $lastMonthStart,
            $lastMonthEnd
        );

        Log::info("Monthly bookings - This month ({$thisMonthStart->format('Y-m-d')} to {$thisMonthEnd->format('Y-m-d')}): {$thisMonthBookings}, Last month: {$lastMonthBookings}");

        $thisYearStart = $now->copy()->startOfYear();
        $thisYearEnd = $now->copy()->endOfYear();
        $lastYearStart = $now->copy()->subYear()->startOfYear();
        $lastYearEnd = $now->copy()->subYear()->endOfYear();

        $thisYearBookings = $this->getBookingCountForDateRange(
            $businessId,
            $thisYearStart,
            $thisYearEnd
        );

        $lastYearBookings = $this->getBookingCountForDateRange(
            $businessId,
            $lastYearStart,
            $lastYearEnd
        );

        Log::info("Yearly bookings - This year: {$thisYearBookings}, Last year: {$lastYearBookings}");

        $dailyGrowth = $yesterdayBookings > 0
            ? round((($todayBookings - $yesterdayBookings) / $yesterdayBookings) * 100, 1)
            : ($todayBookings > 0 ? 100 : 0);

        $weeklyGrowth = $lastWeekBookings > 0
            ? round((($thisWeekBookings - $lastWeekBookings) / $lastWeekBookings) * 100, 1)
            : ($thisWeekBookings > 0 ? 100 : 0);

        $monthlyGrowth = $lastMonthBookings > 0
            ? round((($thisMonthBookings - $lastMonthBookings) / $lastMonthBookings) * 100, 1)
            : ($thisMonthBookings > 0 ? 100 : 0);

        $yearlyGrowth = $lastYearBookings > 0
            ? round((($thisYearBookings - $lastYearBookings) / $lastYearBookings) * 100, 1)
            : ($thisYearBookings > 0 ? 100 : 0);

        Log::info("Growth calculations - Daily: {$dailyGrowth}%, Weekly: {$weeklyGrowth}%, Monthly: {$monthlyGrowth}%, Yearly: {$yearlyGrowth}%");

        return [
            'daily' => [
                'current' => $todayBookings,
                'previous' => $yesterdayBookings,
                'growth' => $dailyGrowth
            ],
            'weekly' => [
                'current' => $thisWeekBookings,
                'previous' => $lastWeekBookings,
                'growth' => $weeklyGrowth
            ],
            'monthly' => [
                'current' => $thisMonthBookings,
                'previous' => $lastMonthBookings,
                'growth' => $monthlyGrowth
            ],
            'yearly' => [
                'current' => $thisYearBookings,
                'previous' => $lastYearBookings,
                'growth' => $yearlyGrowth
            ]
        ];
    }

    /**
     * Helper method to get booking count for a date range
     *
     * @param int $businessId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    public function getBookingCountForDateRange($businessId, $startDate, $endDate)
    {
        return DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->count();
    }

    /**
     * Get top performing courts for a business
     *
     * @param int $businessId
     * @param int $limit
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getTopCourts($businessId, $limit = 10, $period = 'monthly', $fromDate = null, $toDate = null)
    {
        list($startDate, $endDate) = $this->getDateRangeFromPeriod($period, $fromDate, $toDate);

        return DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                'courts.id',
                'courts.name',
                'branches.name as branch',
                DB::raw('COUNT(court_bookings.id) as bookings'),
                DB::raw('SUM(court_bookings.total_price) as revenue')
            )
            ->groupBy('courts.id', 'courts.name', 'branches.name')
            ->orderBy('bookings', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get top performing branches for a business
     *
     * @param int $businessId
     * @param int $limit
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getTopBranches($businessId, $limit = 10, $period = 'monthly', $fromDate = null, $toDate = null)
    {
        list($startDate, $endDate) = $this->getDateRangeFromPeriod($period, $fromDate, $toDate);

        $business = \App\Models\Business::find($businessId);
        $businessName = $business ? $business->name : '';

        return DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                'branches.id',
                'branches.name',
                DB::raw("'$businessName' as business"),
                DB::raw('COUNT(court_bookings.id) as bookings'),
                DB::raw('SUM(court_bookings.total_price) as revenue')
            )
            ->groupBy('branches.id', 'branches.name')
            ->orderBy('revenue', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Helper method to get date range from period
     *
     * @param string $period
     * @param string|null $fromDate
     * @param string|null $toDate
     * @return array
     */
    public function getDateRangeFromPeriod($period = 'monthly', $fromDate = null, $toDate = null)
    {
        $now = Carbon::now();

        if ($fromDate && $toDate) {
            return [Carbon::parse($fromDate)->startOfDay(), Carbon::parse($toDate)->endOfDay()];
        }

        switch ($period) {
            case 'daily':
                return [$now->copy()->startOfDay(), $now->copy()->endOfDay()];
            case 'weekly':
                return [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()];
            case 'yearly':
                return [$now->copy()->startOfYear(), $now->copy()->endOfYear()];
            case 'monthly':
            default:
                return [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()];
        }
    }

    /**
     * Export statistics data in various formats (Excel, CSV, PDF)
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\Response
     */
    public function exportBusinessStatistics(Request $request)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        if (!$businessId) {
            return response()->json(['error' => __('business.no_business')], 400);
        }

        $dataType = $request->input('data_type', 'all');
        $format = $request->input('format', 'excel');
        $period = $request->input('period', 'monthly');

        $data = $this->getExportData($businessId, $dataType, $period, $request);

        $filename = $this->generateExportFilename($dataType, $format);

        switch ($format) {
            case 'excel':
                return $this->exportToExcel($data, $filename);
            case 'csv':
                return $this->exportToCsv($data, $filename);
            case 'pdf':
                return $this->exportToPdf($data, $filename, $dataType);
            default:
                return response()->json(['error' => 'Unsupported export format'], 400);
        }
    }

    /**
     * Process data before export to format values properly
     *
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function processDataForExport($data, $dataType)
    {
        if (empty($data)) {
            return [];
        }

        $processed = [];

        foreach ($data as $key => $item) {
            if (is_object($item)) {
                $item = (array) $item;
            }

            if (in_array($dataType, ['top_courts', 'top_branches'])) {
                if (isset($item['revenue'])) {
                    $item['formatted_revenue'] = number_format($item['revenue'], 0, ',', '.') . ' ₫';
                }
            }

            $processed[$key] = $item;
        }

        return $processed;
    }

    /**
     * Format a collection of objects to array for export
     *
     * @param mixed $data Collection or array of data
     * @return array
     */
    private function ensureArray($data)
    {
        if (empty($data)) {
            return [];
        }

        if (is_object($data) && method_exists($data, 'toArray')) {
            $data = $data->toArray();
        }

        $result = [];

        if (is_object($data) && !is_iterable($data)) {
            return [(array) $data];
        }

        if (is_array($data) || is_object($data)) {
            foreach ($data as $key => $item) {
                if (is_object($item)) {
                    $result[$key] = (array) $item;
                } elseif (is_array($item)) {
                    $result[$key] = [];
                    foreach ($item as $subKey => $subItem) {
                        if (is_object($subItem)) {
                            $result[$key][$subKey] = (array) $subItem;
                        } else {
                            $result[$key][$subKey] = $subItem;
                        }
                    }
                } else {
                    $result[$key] = $item;
                }
            }
            return $result;
        }

        return [];
    }

    /**
     * Get the data to be exported based on type
     *
     * @param int $businessId
     * @param string $dataType
     * @param string $period
     * @param Request $request
     * @return array
     */
    private function getExportData($businessId, $dataType, $period, Request $request)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {
            $stats = $this->getCustomRangeStats($businessId, $fromDate, $toDate);
            $isCustomRange = true;
        } else {
            $stats = $this->getDashboardStats($businessId, $period);
            $isCustomRange = false;
        }

        switch ($dataType) {
            case 'top_users':
                $users = $this->getTopUsers($businessId, 50, $period, $fromDate, $toDate);
                return $this->ensureArray($users);

            case 'recent_payments':
                $payments = $this->getRecentPayments($businessId, 50);
                return $this->ensureArray($payments);

            case 'popular_times':
                $times = $this->getPopularBookingTimes($businessId, $period, $fromDate, $toDate);
                return $this->ensureArray($times);

            case 'top_courts':
                $courts = $this->getTopCourts($businessId, 20, $period, $fromDate, $toDate);
                return $this->processDataForExport($courts, 'top_courts');

            case 'top_branches':
                $branches = $this->getTopBranches($businessId, 20, $period, $fromDate, $toDate);
                return $this->processDataForExport($branches, 'top_branches');

            case 'all':
            default:
                $now = Carbon::now();
                $chartData = $this->getChartDataByPeriod($businessId, $period, $now, $fromDate, $toDate);

                $topUsers = $this->ensureArray($this->getTopUsers($businessId, 10, $period, $fromDate, $toDate));
                $recentPayments = $this->ensureArray($this->getRecentPayments($businessId, 50));
                $popularTimes = $this->ensureArray($this->getPopularBookingTimes($businessId, $period, $fromDate, $toDate));
                $topCourts = $this->ensureArray($this->getTopCourts($businessId, 20, $period, $fromDate, $toDate));
                $topBranches = $this->ensureArray($this->getTopBranches($businessId, 20, $period, $fromDate, $toDate));

                $processedTopCourts = $this->processDataForExport($topCourts, 'top_courts');
                $processedTopBranches = $this->processDataForExport($topBranches, 'top_branches');

                return [
                    'top_users' => $topUsers,
                    'recent_payments' => $recentPayments,
                    'popular_times' => $popularTimes,
                    'top_courts' => $processedTopCourts,
                    'top_branches' => $processedTopBranches,
                    'stats' => $stats,
                    'period' => $period,
                    'is_custom_range' => $isCustomRange,
                    'date_range' => $isCustomRange ? $stats['date_range'] : null
                ];
        }
    }

    /**
     * Generate a filename for the export
     *
     * @param string $dataType
     * @param string $format
     * @return string
     */
    private function generateExportFilename($dataType, $format)
    {
        $date = Carbon::now()->format('Y-m-d');

        $dataTypeNames = [
            'top_users' => 'top-users',
            'recent_payments' => 'recent-payments',
            'popular_times' => 'popular-time-slots',
            'top_courts' => 'top-courts',
            'top_branches' => 'top-branches',
            'all' => 'full-statistics'
        ];

        $name = $dataTypeNames[$dataType] ?? $dataType;

        if ($format === 'excel') {
            $format = 'xlsx';
        }

        return "pickleball-business-statistics-{$name}-{$date}.{$format}";
    }

    /**
     * Export data to Excel format
     *
     * @param array $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($data, $filename)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $spreadsheet = new Spreadsheet();

        if (isset($data['top_users'])) {
            $topUsers = $this->ensureArray($data['top_users']);
            $recentPayments = $this->ensureArray($data['recent_payments']);
            $popularTimes = $this->ensureArray($data['popular_times']);
            $topCourts = $this->ensureArray($data['top_courts']);
            $topBranches = $this->ensureArray($data['top_branches']);

            $this->createExcelSheet(
                $spreadsheet,
                $this->limitSheetName(__('common.top_users')),
                $topUsers,
                $this->getTranslatedHeaders('top_users'),
                ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date']
            );

            $this->createExcelSheet(
                $spreadsheet,
                $this->limitSheetName(__('common.payments')),
                $recentPayments,
                $this->getTranslatedHeaders('recent_payments'),
                ['transaction_id', 'user_name', 'court_info', 'booking_date', 'payment_method', 'formatted_amount', 'status']
            );

            $this->createExcelSheet(
                $spreadsheet,
                $this->limitSheetName(__('common.booking_times')),
                $popularTimes,
                $this->getTranslatedHeaders('popular_times'),
                ['time_slot', 'count', 'formatted_percentage', 'formatted_revenue']
            );

            $this->createExcelSheet(
                $spreadsheet,
                $this->limitSheetName(__('common.courts')),
                $topCourts,
                $this->getTranslatedHeaders('top_courts'),
                ['name', 'branch', 'bookings', 'formatted_revenue']
            );

            $this->createExcelSheet(
                $spreadsheet,
                $this->limitSheetName(__('common.branches')),
                $topBranches,
                $this->getTranslatedHeaders('top_branches'),
                ['name', 'business', 'bookings', 'formatted_revenue']
            );

        } else {
            $data = $this->ensureArray($data);
            $sheetName = $this->limitSheetName($this->getTitleForDataType(pathinfo($filename, PATHINFO_FILENAME)));
            $headers = $this->getHeadersForDataType($data, pathinfo($filename, PATHINFO_FILENAME));
            $columns = $this->getColumnsForDataType($data, pathinfo($filename, PATHINFO_FILENAME));

            $this->createExcelSheet($spreadsheet, $sheetName, $data, $headers, $columns);
        }

        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);

        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Limit sheet name to Excel's maximum length of 31 characters
     *
     * @param string $name
     * @return string
     */
    private function limitSheetName($name)
    {
        if (mb_strlen($name) > 31) {
            return mb_substr($name, 0, 28) . '...';
        }

        return $name;
    }

    /**
     * Create an Excel worksheet with the provided data
     *
     * @param Spreadsheet $spreadsheet
     * @param string $sheetName
     * @param array $data
     * @param array $headers
     * @param array $columns
     */
    private function createExcelSheet($spreadsheet, $sheetName, $data, $headers, $columns)
    {
        $data = $this->ensureArray($data);

        if ($spreadsheet->getSheetCount() == 1 && $spreadsheet->getActiveSheet()->getTitle() == 'Worksheet') {
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle($sheetName);
        } else {
            $sheet = $spreadsheet->createSheet();
            $sheet->setTitle($sheetName);
        }

        foreach ($headers as $index => $header) {
            $sheet->setCellValue(chr(65 + $index) . '1', $header);
        }

        $headerStyle = $sheet->getStyle('A1:' . chr(65 + count($headers) - 1) . '1');
        $headerStyle->getFont()->setBold(true);
        $headerStyle->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $headerStyle->getFill()->getStartColor()->setARGB('FFD3D3D3');

        $row = 2;
        foreach ($data as $item) {
            if (is_object($item)) {
                $item = (array) $item;
            }

            foreach ($columns as $index => $column) {
                $value = isset($item[$column]) ? $item[$column] : '';
                $sheet->setCellValue(chr(65 + $index) . $row, $value);
            }
            $row++;
        }

        foreach (range(0, count($headers) - 1) as $columnIndex) {
            $sheet->getColumnDimension(chr(65 + $columnIndex))->setAutoSize(true);
        }
    }

    /**
     * Export data to CSV format
     *
     * @param array $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToCsv($data, $filename)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_');
        $file = fopen($tempFile, 'w');

        fputs($file, "\xEF\xBB\xBF");

        $data = $this->ensureArray($data);

        if (isset($data['top_users'])) {
            $data = $this->ensureArray($data['top_users']);
            $headers = $this->getTranslatedHeaders('top_users');
            $columns = ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date'];
        } else {
            $headers = $this->getHeadersForDataType($data, pathinfo($filename, PATHINFO_FILENAME));
            $columns = $this->getColumnsForDataType($data, pathinfo($filename, PATHINFO_FILENAME));
        }

        fputcsv($file, $headers);

        foreach ($data as $item) {
            if (is_object($item)) {
                $item = (array) $item;
            }

            $row = [];
            foreach ($columns as $column) {
                $row[] = isset($item[$column]) ? $item[$column] : '';
            }
            fputcsv($file, $row);
        }

        fclose($file);

        return response()->download($tempFile, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export data to PDF format
     *
     * @param array $data
     * @param string $filename
     * @param string $dataType
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($data, $filename, $dataType)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');

        $data = $this->ensureArray($data);

        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new \Dompdf\Dompdf($options);

        $orientation = 'portrait';
        if ($dataType == 'recent_payments' || count($this->getHeadersForDataType($data, $dataType)) > 5) {
            $orientation = 'landscape';
        }

        $dompdf->setPaper('A4', $orientation);

        if (isset($data['top_users'])) {
            $html = $this->generatePdfHtmlForAllData($data);
        } else {
            $html = $this->generatePdfHtmlForSingleData($data, $dataType);
        }

        $dompdf->loadHtml($html);
        $dompdf->render();

        file_put_contents($tempFile, $dompdf->output());

        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Get translated headers for export files
     *
     * @param string $dataType
     * @return array
     */
    private function getTranslatedHeaders($dataType)
    {
        $headerMappings = [
            'top_users' => [
                __('common.no'),
                __('common.full_name'),
                __('common.email'),
                __('common.booking_count'),
                __('common.total_spending'),
                __('common.last_booking')
            ],
            'recent_payments' => [
                __('common.transaction_id'),
                __('common.user'),
                __('common.court'),
                __('common.booking_date'),
                __('common.payment_method'),
                __('common.amount'),
                __('common.status')
            ],
            'popular_times' => [
                __('common.time_slot'),
                __('common.booking_count'),
                __('common.percentage'),
                __('common.revenue')
            ],
            'top_courts' => [
                __('common.court_name'),
                __('common.branch'),
                __('common.booking_count'),
                __('common.revenue')
            ],
            'top_branches' => [
                __('common.branch_name'),
                __('common.business'),
                __('common.booking_count'),
                __('common.revenue')
            ]
        ];

        return $headerMappings[$dataType] ?? [];
    }

    /**
     * Get headers for a data type
     *
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function getHeadersForDataType($data, $dataType)
    {
        if (strpos($dataType, 'top-users') !== false || $dataType === 'top_users') {
            return $this->getTranslatedHeaders('top_users');
        } elseif (strpos($dataType, 'recent-payments') !== false || $dataType === 'recent_payments') {
            return $this->getTranslatedHeaders('recent_payments');
        } elseif (strpos($dataType, 'popular-time-slots') !== false || $dataType === 'popular_times') {
            return $this->getTranslatedHeaders('popular_times');
        } elseif (strpos($dataType, 'top-courts') !== false || $dataType === 'top_courts') {
            return $this->getTranslatedHeaders('top_courts');
        } elseif (strpos($dataType, 'top-branches') !== false || $dataType === 'top_branches') {
            return $this->getTranslatedHeaders('top_branches');
        }

        if (!empty($data) && is_array($data) && !empty($data[0])) {
            return array_keys($data[0]);
        }

        return ['Data'];
    }

    /**
     * Get column keys for a data type
     *
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function getColumnsForDataType($data, $dataType)
    {
        if (strpos($dataType, 'top-users') !== false || $dataType === 'top_users') {
            return ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date'];
        } elseif (strpos($dataType, 'recent-payments') !== false || $dataType === 'recent_payments') {
            return ['transaction_id', 'user_name', 'court_info', 'booking_date', 'payment_method', 'formatted_amount', 'status'];
        } elseif (strpos($dataType, 'popular-time-slots') !== false || $dataType === 'popular_times') {
            return ['time_slot', 'count', 'formatted_percentage', 'formatted_revenue'];
        } elseif (strpos($dataType, 'top-courts') !== false || $dataType === 'top_courts') {
            return ['name', 'branch', 'bookings', 'formatted_revenue'];
        } elseif (strpos($dataType, 'top-branches') !== false || $dataType === 'top_branches') {
            return ['name', 'business', 'bookings', 'formatted_revenue'];
        }

        if (!empty($data) && is_array($data) && !empty($data[0])) {
            return array_keys($data[0]);
        }

        return ['data'];
    }

    /**
     * Get a title for a data type
     *
     * @param string $dataType
     * @return string
     */
    private function getTitleForDataType($dataType)
    {
        $titleMappings = [
            'top_users' => __('common.top_10_users_by_bookings'),
            'recent_payments' => __('common.recent_payments'),
            'popular_times' => __('common.popular_booking_times'),
            'top_courts' => __('common.top_10_most_booked_courts'),
            'top_branches' => __('common.top_10_highest_revenue_branches'),
            'all' => __('common.analytics') . ' - ' . __('common.detailed_analysis')
        ];

        return $titleMappings[$dataType] ?? __('common.analytics');
    }

    /**
     * Generate HTML for PDF export of a single data set
     *
     * @param array $data
     * @param string $dataType
     * @return string
     */
    private function generatePdfHtmlForSingleData($data, $dataType)
    {
        $title = $this->getTitleForDataType($dataType);
        $html = $this->getPdfHtmlHeader($title);

        if (is_object($data)) {
            $data = $this->ensureArray($data);
        }

        if (empty($data)) {
            $html .= '<p>' . __('common.no_data') . '</p>';
            $html .= $this->getPdfHtmlFooter();
            return $html;
        }

        $headers = $this->getHeadersForDataType($data, $dataType);

        $columns = [];
        if (strpos($dataType, 'top-users') !== false || $dataType === 'top_users') {
            $columns = ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date'];
        } elseif (strpos($dataType, 'recent-payments') !== false || $dataType === 'recent_payments') {
            $columns = ['transaction_id', 'user_name', 'court_info', 'booking_date', 'payment_method', 'formatted_amount', 'status'];
        } elseif (strpos($dataType, 'popular-time-slots') !== false || $dataType === 'popular_times') {
            $columns = ['time_slot', 'count', 'formatted_percentage', 'formatted_revenue'];
        } elseif (strpos($dataType, 'top-courts') !== false || $dataType === 'top_courts') {
            $columns = ['name', 'branch', 'bookings', 'formatted_revenue'];
        } elseif (strpos($dataType, 'top-branches') !== false || $dataType === 'top_branches') {
            $columns = ['name', 'business', 'bookings', 'formatted_revenue'];
        } else {
            $firstItem = reset($data);
            $columns = is_array($firstItem) ? array_keys($firstItem) : (is_object($firstItem) ? array_keys((array) $firstItem) : []);
        }

        $html .= $this->getPdfHtmlTable($data, $headers, $columns);
        $html .= $this->getPdfHtmlFooter();

        return $html;
    }

    /**
     * Generate HTML for PDF export of all data
     *
     * @param array $data
     * @return string
     */
    private function generatePdfHtmlForAllData($data)
    {
        $html = $this->getPdfHtmlHeader(__('common.analytics') . ' - ' . __('common.detailed_analysis'));

        $html .= '<h2>' . __('common.top_10_users_by_bookings') . '</h2>';
        if (!empty($data['top_users'])) {
            $topUsers = $this->ensureArray($data['top_users']);
            $html .= $this->getPdfHtmlTable(
                $topUsers,
                $this->getTranslatedHeaders('top_users'),
                ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date']
            );
        } else {
            $html .= '<p>' . __('common.no_data') . '</p>';
        }

        $html .= '<h2>' . __('common.recent_payments') . '</h2>';
        if (!empty($data['recent_payments'])) {
            $recentPayments = $this->ensureArray($data['recent_payments']);
            $html .= $this->getPdfHtmlTable(
                $recentPayments,
                $this->getTranslatedHeaders('recent_payments'),
                ['transaction_id', 'user_name', 'court_info', 'booking_date', 'payment_method', 'formatted_amount', 'status']
            );
        } else {
            $html .= '<p>' . __('common.no_data') . '</p>';
        }

        $html .= '<h2>' . __('common.popular_booking_times') . '</h2>';
        if (!empty($data['popular_times'])) {
            $popularTimes = $this->ensureArray($data['popular_times']);
            $html .= $this->getPdfHtmlTable(
                $popularTimes,
                $this->getTranslatedHeaders('popular_times'),
                ['time_slot', 'count', 'formatted_percentage', 'formatted_revenue']
            );
        } else {
            $html .= '<p>' . __('common.no_data') . '</p>';
        }

        $html .= '<h2>' . __('common.top_10_most_booked_courts') . '</h2>';
        if (!empty($data['top_courts'])) {
            $topCourts = $this->ensureArray($data['top_courts']);
            $html .= $this->getPdfHtmlTable(
                $topCourts,
                $this->getTranslatedHeaders('top_courts'),
                ['name', 'branch', 'bookings', 'formatted_revenue']
            );
        } else {
            $html .= '<p>' . __('common.no_data') . '</p>';
        }

        $html .= '<h2>' . __('common.top_10_highest_revenue_branches') . '</h2>';
        if (!empty($data['top_branches'])) {
            $topBranches = $this->ensureArray($data['top_branches']);
            $html .= $this->getPdfHtmlTable(
                $topBranches,
                $this->getTranslatedHeaders('top_branches'),
                ['name', 'business', 'bookings', 'formatted_revenue']
            );
        } else {
            $html .= '<p>' . __('common.no_data') . '</p>';
        }

        $html .= $this->getPdfHtmlFooter();

        return $html;
    }

    /**
     * Get the HTML header for a PDF document
     *
     * @param string $title
     * @return string
     */
    private function getPdfHtmlHeader($title)
    {
        return '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>' . $title . '</title>
            <style>
                @page {
                    margin: 1cm;
                }
                body {
                    font-family: DejaVu Sans, sans-serif;
                    font-size: 10px;
                    line-height: 1.3;
                }
                h1 {
                    text-align: center;
                    color: #333;
                    margin-bottom: 20px;
                    font-size: 16px;
                }
                h2 {
                    color: #555;
                    font-size: 14px;
                    margin-top: 15px;
                    margin-bottom: 10px;
                    page-break-after: avoid;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }
                th {
                    background-color: #f2f2f2;
                    text-align: left;
                    font-weight: bold;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 4px;
                    font-size: 9px;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .footer {
                    text-align: center;
                    font-size: 8px;
                    color: #777;
                    margin-top: 20px;
                }
                .page-break {
                    page-break-after: always;
                }
            </style>
        </head>
        <body>
            <h1>' . $title . '</h1>';
    }

    /**
     * Get the HTML for a PDF table
     *
     * @param array $data
     * @param array $headers
     * @param array $columns
     * @return string
     */
    private function getPdfHtmlTable($data, $headers, $columns)
    {
        $data = $this->ensureArray($data);

        $columnWidths = $this->calculateColumnWidths($data, $headers, $columns);

        $html = '
        <table border="1" cellpadding="4" cellspacing="0" width="100%" style="border-collapse: collapse; margin-bottom: 20px; font-size: 10px;">
            <thead>
                <tr style="background-color: #f2f2f2;">';

        foreach ($headers as $index => $header) {
            $width = $columnWidths[$index];
            $html .= '<th style="width: ' . $width . '%; word-wrap: break-word;">' . $header . '</th>';
        }

        $html .= '</tr>
            </thead>
            <tbody>';

        $statusTranslations = [
            'completed' => 'Thành công',
            'pending' => 'Đang xử lý',
            'failed' => 'Thất bại',
            'cancelled' => 'Đã hủy'
        ];

        foreach ($data as $item) {
            if (is_object($item)) {
                $item = (array) $item;
            }

            $html .= '<tr>';
            foreach ($columns as $index => $column) {
                $value = isset($item[$column]) ? $item[$column] : '';

                if ($column === 'status' && isset($statusTranslations[$value])) {
                    $value = $statusTranslations[$value];
                }

                $width = $columnWidths[$index];
                $html .= '<td style="width: ' . $width . '%; word-wrap: break-word;">' . $value . '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '</tbody>
        </table>';

        return $html;
    }

    /**
     * Calculate optimal column widths for a table
     *
     * @param array $data
     * @param array $headers
     * @param array $columns
     * @return array
     */
    private function calculateColumnWidths($data, $headers, $columns)
    {
        $columnLengths = [];

        foreach ($headers as $index => $header) {
            $columnLengths[$index] = mb_strlen($header);
        }

        foreach ($data as $item) {
            if (is_object($item)) {
                $item = (array) $item;
            }

            foreach ($columns as $index => $column) {
                $value = isset($item[$column]) ? $item[$column] : '';
                $valueLength = is_string($value) ? mb_strlen($value) : mb_strlen((string) $value);

                if (!isset($columnLengths[$index]) || $valueLength > $columnLengths[$index]) {
                    $columnLengths[$index] = $valueLength;
                }
            }
        }

        $totalLength = array_sum($columnLengths);

        $widths = [];
        $totalColumns = count($columnLengths);

        foreach ($columnLengths as $index => $length) {
            $width = $totalLength > 0 ? ($length / $totalLength) * 100 : (100 / $totalColumns);

            $width = max(min($width, 40), 4);

            $widths[$index] = round($width, 1);
        }

        $totalWidth = array_sum($widths);
        if ($totalWidth != 100) {
            $factor = 100 / $totalWidth;
            foreach ($widths as $index => $width) {
                $widths[$index] = round($width * $factor, 1);
            }
        }

        return $widths;
    }

    /**
     * Get the HTML footer for a PDF document
     *
     * @return string
     */
    private function getPdfHtmlFooter()
    {
        $date = Carbon::now()->format('d/m/Y H:i:s');
        return '
            <div class="footer">
                ' . __('common.exported_on') . ': ' . $date . ' - ' . config('app.name') . '
            </div>
        </body>
        </html>';
    }
}
