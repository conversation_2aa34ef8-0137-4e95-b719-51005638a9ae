<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Branch;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $business = $request->user()->business;
        $branchIds = $business->branches->pluck('id')->toArray();

        $query = Notification::query()
            ->whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->with(['branch'])
            ->orderBy('created_at', 'desc');

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('read_status')) {
            $query->where('is_read', $request->read_status === 'read');
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->paginate(10)->withQueryString();
        $unreadCount = Notification::whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->where('is_read', false)
            ->count();

        $branches = Branch::where('business_id', $business->id)->get(['id', 'name']);

        return Inertia::render('Business/notifications/Index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'filters' => $request->only(['type', 'read_status', 'branch_id', 'search']),
            'branches' => $branches
        ]);
    }

    /**
     * Get the count of unread notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount(Request $request)
    {
        $business = $request->user()->business;
        $branchIds = $business->branches->pluck('id')->toArray();

        $count = Notification::whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->where('is_read', false)
            ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Get notifications for dropdown menu.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDropdownNotifications(Request $request)
    {
        $business = $request->user()->business;
        $branchIds = $business->branches->pluck('id')->toArray();
        $limit = $request->input('limit', 5);

        $notifications = Notification::whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->with('branch')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        $unreadCount = Notification::whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->where('is_read', false)
            ->count();

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $unreadCount
        ]);
    }

    /**
     * Mark a notification as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, $id)
    {
        $business = $request->user()->business;
        $notification = Notification::findOrFail($id);


        if ($notification->business_id !== $business->id) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $notification->is_read = true;
        $notification->save();

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        $business = $request->user()->business;
        $branchIds = $business->branches->pluck('id')->toArray();

        $query = Notification::whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->where('is_read', false);

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        $query->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }

    /**
     * Delete a notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        $business = $request->user()->business;
        $notification = Notification::findOrFail($id);


        if ($notification->business_id !== $business->id) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $notification->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Filter notifications based on criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filter(Request $request)
    {
        $business = $request->user()->business;
        $branchIds = $business->branches->pluck('id')->toArray();

        $query = Notification::query()
            ->whereIn('branch_id', $branchIds)
            ->where('business_id', $business->id)
            ->with(['branch'])
            ->orderBy('created_at', 'desc');


        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('read_status')) {
            $query->where('is_read', $request->read_status === 'read');
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->paginate(10)->withQueryString();

        return response()->json($notifications);
    }
}
