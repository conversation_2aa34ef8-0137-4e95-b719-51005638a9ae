<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\Branch;
use App\Models\CourtService;
use App\Models\BranchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ServiceManagementController extends Controller
{

    public function index(Request $request)
    {
        $user = Auth::user();
        $businessId = $user->business_id;


        $business = Business::findOrFail($businessId);


        $services = CourtService::where('business_id', $businessId)
            ->when($request->input('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->input('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->with(['branches' => function ($query) use ($businessId) {
                $query->whereHas('business', function ($q) use ($businessId) {
                    $q->where('id', $businessId);
                });
            }])
            ->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'))
            ->paginate(10)
            ->withQueryString();


        $branches = Branch::where('business_id', $businessId)
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return Inertia::render('Business/Services/Index', [
            'services' => $services,
            'branches' => $branches,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
            'business' => $business->only(['id', 'name']),
        ]);
    }


    public function manage($serviceId)
    {
        $user = Auth::user();
        $businessId = $user->business_id;


        $business = Business::findOrFail($businessId);

        
        $service = CourtService::where('business_id', $businessId)
            ->findOrFail($serviceId);


        $branches = Branch::where('business_id', $businessId)
            ->select('id', 'name')
            ->orderBy('name')
            ->get();


        $branchServices = BranchService::whereHas('branch', function ($query) use ($businessId) {
                $query->where('business_id', $businessId);
            })
            ->where('court_service_id', $serviceId)
            ->with('branch:id,name')
            ->get();

        $assignedBranchIds = $branchServices->pluck('branch_id')->toArray();


        $unassignedBranches = $branches->filter(function ($branch) use ($assignedBranchIds) {
            return !in_array($branch->id, $assignedBranchIds);
        })->values();

        return Inertia::render('Business/Services/Manage', [
            'service' => $service,
            'business' => $business->only(['id', 'name']),
            'branches' => $branches,
            'branchServices' => $branchServices,
            'unassignedBranches' => $unassignedBranches,
        ]);
    }


    public function update(Request $request, $serviceId)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        $validator = Validator::make($request->all(), [
            'branches' => 'required|array',
            'branches.*.branch_id' => 'required|exists:branches,id',
            'branches.*.price' => 'nullable|numeric|min:0',
            'branches.*.member_price' => 'nullable|numeric|min:0',
            'branches.*.is_active' => 'boolean',
            'branches.*.description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        $service = CourtService::where('business_id', $businessId)
            ->findOrFail($serviceId);


        foreach ($request->branches as $branchData) {

            $branch = Branch::where('business_id', $businessId)
                ->findOrFail($branchData['branch_id']);


            BranchService::updateOrCreate(
                [
                    'branch_id' => $branch->id,
                    'court_service_id' => $service->id,
                ],
                [
                    'price' => $branchData['price'] ?? $service->price,
                    'member_price' => $branchData['member_price'] ?? null,
                    'is_active' => $branchData['is_active'] ?? true,
                    'description' => $branchData['description'] ?? null,
                ]
            );
        }

        return redirect()->route('business.services.index')
            ->with('message', __('service.business_update_success'));
    }


    public function removeFromBranch($serviceId, $branchId)
    {
        $user = Auth::user();
        $businessId = $user->business_id;


        $service = CourtService::where('business_id', $businessId)
            ->findOrFail($serviceId);


        $branch = Branch::where('business_id', $businessId)
            ->findOrFail($branchId);


        BranchService::where('branch_id', $branch->id)
            ->where('court_service_id', $service->id)
            ->delete();

        return back()->with('message', __('service.removed_from_branch_success'));
    }


    public function assignToBranch(Request $request, $serviceId)
    {
        $user = Auth::user();
        $businessId = $user->business_id;


        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'price' => 'nullable|numeric|min:0',
            'member_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        $service = CourtService::where('business_id', $businessId)
            ->findOrFail($serviceId);


        $branch = Branch::where('business_id', $businessId)
            ->findOrFail($request->branch_id);


        BranchService::updateOrCreate(
            [
                'branch_id' => $branch->id,
                'court_service_id' => $service->id,
            ],
            [
                'price' => $request->price ?? $service->price,
                'member_price' => $request->member_price ?? null,
                'is_active' => $request->is_active ?? true,
                'description' => $request->description ?? null,
            ]
        );

        return back()->with('message', __('service.assigned_to_branch_success'));
    }


    public function applyToAllBranches(Request $request, $serviceId)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        $validator = Validator::make($request->all(), [
            'price' => 'nullable|numeric|min:0',
            'member_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        $service = CourtService::where('business_id', $businessId)
            ->findOrFail($serviceId);


        $branches = Branch::where('business_id', $businessId)->get();


        foreach ($branches as $branch) {
            BranchService::updateOrCreate(
                [
                    'branch_id' => $branch->id,
                    'court_service_id' => $service->id,
                ],
                [
                    'price' => $request->price,
                    'member_price' => $request->member_price,
                    'is_active' => $request->is_active,
                    'description' => $request->description,
                ]
            );
        }

        return back()->with('message', __('service.applied_to_all_branches_success'));
    }


    public function create()
    {
        $user = Auth::user();
        $businessId = $user->business_id;


        $business = Business::findOrFail($businessId);

        return Inertia::render('Business/Services/Create', [
            'business' => $business->only(['id', 'name']),
        ]);
    }


    public function store(Request $request)
    {
        $user = Auth::user();
        $businessId = $user->business_id;

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }


        $service = CourtService::create([
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'is_active' => $request->is_active,
            'business_id' => $businessId,
        ]);


        if ($request->input('assign_to_all_branches', false)) {
            $branches = Branch::where('business_id', $businessId)->get();

            foreach ($branches as $branch) {
                BranchService::create([
                    'branch_id' => $branch->id,
                    'court_service_id' => $service->id,
                    'price' => $request->price,
                    'is_active' => $request->is_active,
                    'description' => $request->description,
                ]);
            }
        }

        return redirect()->route('business.services.index')
            ->with('message', __('service.business_store_success'));
    }
}
