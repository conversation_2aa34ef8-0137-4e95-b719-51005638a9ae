<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\BusinessSetting;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SettingController extends Controller
{
    /**
     * Display business settings page
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {

            return redirect()->route('dashboard')
                ->with('flash.error', 'Không tìm thấy thông tin doanh nghiệp.');
        }


        $settings = [
            'general' => $this->getGeneralSettings($business->id),
            'email' => $this->getEmailSettings($business->id),
            'payment' => $this->getPaymentSettings($business->id),
        ];

        return Inertia::render('Business/Settings/Index', [
            'business' => $business,
            'settings' => $settings,
        ]);
    }

    /**
     * Update general business settings
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json(['message' => 'Không tìm thấy thông tin doanh nghiệp.'], 404);
        }

        $request->validate([
            'business_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'tax_code' => 'nullable|string|max:20',
        ]);


        $business->update([
            'name' => $request->business_name,
            'phone' => $request->phone,
            'address' => $request->address,
            'tax_code' => $request->tax_code,
        ]);


        $generalSettings = [
            'business_hours' => $request->business_hours ?? [],
            'logo_url' => $request->logo_url,
            'description' => $request->description,
        ];

        $this->updateOrCreateSetting($business->id, 'general_settings', $generalSettings);

        return redirect()->back()->with('flash.success', 'Cập nhật thông tin doanh nghiệp thành công');
    }

    /**
     * Update email settings
     */
    public function updateEmail(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json(['message' => 'Không tìm thấy thông tin doanh nghiệp.'], 404);
        }

        $request->validate([
            'email_username' => 'required|email',
            'email_password' => 'required|string',
            'email_host' => 'required|string',
            'email_send_method' => 'required|string',
            'email_send_port' => 'required|integer',
            'email_encryption' => 'required|string',
        ]);

        $emailSettings = [
            'email_username' => $request->email_username,
            'email_password' => $request->email_password,
            'email_host' => $request->email_host,
            'email_send_method' => $request->email_send_method,
            'email_send_port' => $request->email_send_port,
            'email_encryption' => $request->email_encryption,
        ];

        $stringValue = json_encode($emailSettings);

        $this->updateOrCreateSetting($business->id, 'email_setting', $stringValue);

        return redirect()->back()->with('flash.success', 'Cập nhật cài đặt email thành công');
    }

    /**
     * Update payment settings
     */
    public function updatePayment(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json(['message' => 'Không tìm thấy thông tin doanh nghiệp.'], 404);
        }

        $paymentType = $request->payment_type;

        if ($paymentType === 'momo') {
            $request->validate([
                'partnerCode' => 'required|string',
                'accessKey' => 'required|string',
                'secretKey' => 'required|string',
                'endPoint' => 'required|string|url',
                'active' => 'required|boolean',
            ]);

            $momoSettings = [
                'provider' => 'momo',
                'partnerCode' => $request->partnerCode,
                'accessKey' => $request->accessKey,
                'secretKey' => $request->secretKey,
                'endPoint' => $request->endPoint,
                'active' => $request->active,
            ];

            BusinessSetting::setMomoSettings($business->id, $momoSettings);
        } elseif ($paymentType === 'vnpay') {
            $request->validate([
                'vnp_TmnCode' => 'required|string',
                'vnp_HashSecret' => 'required|string',
                'vnp_Version' => 'required|string',
                'endPoint' => 'required|string|url',
                'active' => 'required|boolean',
            ]);

            $vnpaySettings = [
                'provider' => 'vnpay',
                'vnp_TmnCode' => $request->vnp_TmnCode,
                'vnp_HashSecret' => $request->vnp_HashSecret,
                'vnp_Version' => $request->vnp_Version,
                'endPoint' => $request->endPoint,
                'active' => $request->active,
            ];

            BusinessSetting::setVnPaySettings($business->id, $vnpaySettings);
        } elseif ($paymentType === 'bank') {
            $request->validate([
                'accountNumber' => 'required|string',
                'bankName' => 'required|string',
                'accountName' => 'required|string',
                'bankBranch' => 'nullable|string',
            ]);

            $bankSettings = [
                'accountNumber' => $request->accountNumber,
                'bankName' => $request->bankName,
                'accountName' => $request->accountName,
                'bankBranch' => $request->bankBranch,
            ];

            $this->updateOrCreateSetting($business->id, 'bank_transfer', $bankSettings);
        }

        return redirect()->back()->with('flash.success', 'Cập nhật cài đặt thanh toán thành công');
    }

    /**
     * Get general business settings
     */
    private function getGeneralSettings($businessId)
    {
        $business = Business::find($businessId);
        $businessSettings = BusinessSetting::get($businessId, 'general_settings', []);

        return [
            'business_name' => $business->name ?? '',
            'phone' => $business->phone ?? '',
            'address' => $business->address ?? '',
            'tax_code' => $business->tax_code ?? '',
            'business_hours' => $businessSettings['business_hours'] ?? [],
            'logo_url' => $businessSettings['logo_url'] ?? '',
            'description' => $businessSettings['description'] ?? '',
        ];
    }

    /**
     * Get email settings
     */
    private function getEmailSettings($businessId)
    {
        $defaultEmailSettings = [
            'email_username' => '',
            'email_password' => '',
            'email_host' => 'smtp.gmail.com',
            'email_send_method' => 'smtp',
            'email_send_port' => 587,
            'email_encryption' => 'tls',
        ];

        $emailSettings = BusinessSetting::get($businessId, 'email_setting', $defaultEmailSettings);
        if ($emailSettings == null) {
            return $defaultEmailSettings;
        }

        if (is_string($emailSettings) && !is_array($emailSettings)) {
            $emailSettings = json_decode($emailSettings, true);
        }

        return $emailSettings;
    }

    /**
     * Get payment settings
     */
    private function getPaymentSettings($businessId)
    {
        $momoSettings = BusinessSetting::getMomoSettings($businessId);
        $vnpaySettings = BusinessSetting::getVnPaySettings($businessId);
        $bankSettings = BusinessSetting::getBankSettings($businessId);

        return [
            'momo' => $momoSettings,
            'vnpay' => $vnpaySettings,
            'bank' => $bankSettings,
        ];
    }

    /**
     * Update or create a business setting
     */
    private function updateOrCreateSetting($businessId, $key, $value)
    {
        return BusinessSetting::updateOrCreate(
            ['business_id' => $businessId, 'setting_key' => $key],
            ['setting_value' => $value]
        );
    }
}