<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Statistic;
use App\Models\CourtBooking;
use App\Models\Court;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class StatisticController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        return Inertia::render('Business/Statistics/Index', [
            'statistics' => [],
            'filters' => [
                'search' => $request->search ?? '',
                'period' => $request->period ?? 'monthly',
            ],
        ]);
    }

    /**
     * Display the overview dashboard.
     *
     * @return \Inertia\Response
     */
    public function overview(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        
        $period = $request->input('period', 'monthly');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $now = Carbon::now();

        
        switch ($period) {
            case 'daily':
                $startDate = $fromDate ? Carbon::parse($fromDate) : $now->copy()->subDays(7)->startOfDay();
                $endDate = $toDate ? Carbon::parse($toDate) : $now->copy()->endOfDay();
                $previousStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate) + 1);
                $previousEndDate = $startDate->copy()->subDay()->endOfDay();
                $dateFormat = 'd/m';
                break;
            case 'weekly':
                $startDate = $fromDate ? Carbon::parse($fromDate) : $now->copy()->subWeeks(4)->startOfWeek();
                $endDate = $toDate ? Carbon::parse($toDate) : $now->copy()->endOfWeek();
                $previousStartDate = $startDate->copy()->subWeeks(4);
                $previousEndDate = $startDate->copy()->subDay()->endOfDay();
                $dateFormat = 'd/m';
                break;
            case 'yearly':
                $startDate = $fromDate ? Carbon::parse($fromDate) : $now->copy()->startOfYear();
                $endDate = $toDate ? Carbon::parse($toDate) : $now->copy()->endOfYear();
                $previousStartDate = $startDate->copy()->subYear();
                $previousEndDate = $startDate->copy()->subDay()->endOfDay();
                $dateFormat = 'm/Y';
                break;
            case 'custom':
                $startDate = $fromDate ? Carbon::parse($fromDate) : $now->copy()->subMonth()->startOfDay();
                $endDate = $toDate ? Carbon::parse($toDate) : $now->copy()->endOfDay();
                $dateRange = $endDate->diffInDays($startDate) + 1;
                $previousStartDate = $startDate->copy()->subDays($dateRange);
                $previousEndDate = $startDate->copy()->subDay();
                $dateFormat = $dateRange > 60 ? 'm/Y' : 'd/m';
                break;
            case 'monthly':
            default:
                $startDate = $fromDate ? Carbon::parse($fromDate) : $now->copy()->subMonths(6)->startOfMonth();
                $endDate = $toDate ? Carbon::parse($toDate) : $now->copy()->endOfMonth();
                $previousStartDate = $startDate->copy()->subMonths(6);
                $previousEndDate = $startDate->copy()->subDay()->endOfDay();
                $dateFormat = 'm/Y';
                break;
        }

        
        $branchCount = DB::table('branches')
            ->where('business_id', $businessId)
            ->where('status', 'active')
            ->count();

        
        $courtCount = DB::table('courts')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('courts.is_active', 1)
            ->count();

        
        $completedBookings = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->get();

        
        $previousBookings = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$previousStartDate->format('Y-m-d'), $previousEndDate->format('Y-m-d')])
            ->get();

        
        $totalBookings = $completedBookings->count();
        $totalRevenue = $completedBookings->sum('total_price');
        $previousTotalBookings = $previousBookings->count();
        $previousTotalRevenue = $previousBookings->sum('total_price');

        
        $bookingChangePercentage = $previousTotalBookings > 0
            ? round((($totalBookings - $previousTotalBookings) / $previousTotalBookings) * 100, 1)
            : 100;

        $revenueChangePercentage = $previousTotalRevenue > 0
            ? round((($totalRevenue - $previousTotalRevenue) / $previousTotalRevenue) * 100, 1)
            : 100;

        
        $totalCustomers = $completedBookings->whereNotNull('customer_id')
            ->pluck('customer_id')
            ->unique()
            ->count();

        $previousCustomers = $previousBookings->whereNotNull('customer_id')
            ->pluck('customer_id')
            ->unique()
            ->count();

        $customerChangePercentage = $previousCustomers > 0
            ? round((($totalCustomers - $previousCustomers) / $previousCustomers) * 100, 1)
            : 100;

        
        $currentPeriodTrends = $this->calculateTrendsByPeriod($completedBookings, $startDate, $endDate, $period, $dateFormat);
        $previousPeriodTrends = $this->calculateTrendsByPeriod($previousBookings, $previousStartDate, $previousEndDate, $period, $dateFormat);

        
        $bookingsByDayOfWeek = $completedBookings->groupBy(function ($booking) {
            return Carbon::parse($booking->booking_date)->format('w'); 
        })->map(function ($bookings, $dayNumber) {
            $days = [
                '0' => 'Chủ nhật',
                '1' => 'Thứ hai',
                '2' => 'Thứ ba',
                '3' => 'Thứ tư',
                '4' => 'Thứ năm',
                '5' => 'Thứ sáu',
                '6' => 'Thứ bảy',
            ];

            return [
                'day' => $days[$dayNumber],
                'count' => $bookings->count(),
                'revenue' => $bookings->sum('total_price'),
            ];
        })->sortBy(function ($item, $key) {
            
            $order = ['1', '2', '3', '4', '5', '6', '0'];
            return array_search($key, $order);
        })->values()->all();

        
        $bookingsByHour = $completedBookings->groupBy(function ($booking) {
            return Carbon::parse($booking->start_time)->format('H');
        })->map(function ($bookings, $hour) {
            return [
                'hour' => (int)$hour,
                'time' => sprintf('%02d:00 - %02d:00', $hour, ($hour + 1) % 24),
                'count' => $bookings->count(),
                'revenue' => $bookings->sum('total_price'),
            ];
        })->sortBy('hour')->values()->all();

        
        $topCourts = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                'courts.id',
                'courts.name as court_name',
                'branches.name as branch_name',
                DB::raw('COUNT(court_bookings.id) as booking_count'),
                DB::raw('SUM(court_bookings.total_price) as total_revenue')
            )
            ->groupBy('courts.id', 'courts.name', 'branches.name')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get()
            ->map(function ($court) {
                return [
                    'id' => $court->id,
                    'court_name' => $court->court_name,
                    'branch_name' => $court->branch_name,
                    'booking_count' => $court->booking_count,
                    'total_revenue' => $court->total_revenue,
                    'revenue_formatted' => number_format($court->total_revenue, 0, ',', '.') . ' ₫',
                ];
            });

        
        $topBranches = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(
                'branches.id',
                'branches.name as branch_name',
                DB::raw('COUNT(court_bookings.id) as booking_count'),
                DB::raw('SUM(court_bookings.total_price) as total_revenue')
            )
            ->groupBy('branches.id', 'branches.name')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get()
            ->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'branch_name' => $branch->branch_name,
                    'booking_count' => $branch->booking_count,
                    'total_revenue' => $branch->total_revenue,
                    'revenue_formatted' => number_format($branch->total_revenue, 0, ',', '.') . ' ₫',
                ];
            });

        
        $topCustomers = DB::table('court_bookings')
            ->join('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->leftJoin('customers', 'court_bookings.customer_id', '=', 'customers.id')
            ->where('branches.business_id', $businessId)
            ->where('court_bookings.status', 'completed')
            ->whereBetween('court_bookings.booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->whereNotNull('court_bookings.customer_id')
            ->select(
                'customers.id',
                'customers.name',
                'customers.email',
                'customers.phone',
                DB::raw('COUNT(court_bookings.id) as booking_count'),
                DB::raw('SUM(court_bookings.total_price) as total_spending'),
                DB::raw('MAX(court_bookings.booking_date) as last_booking_date')
            )
            ->groupBy('customers.id', 'customers.name', 'customers.email', 'customers.phone')
            ->orderByDesc('total_spending')
            ->limit(10)
            ->get()
            ->map(function ($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'contact' => $customer->phone ?: $customer->email,
                    'booking_count' => $customer->booking_count,
                    'total_spending' => $customer->total_spending,
                    'spending_formatted' => number_format($customer->total_spending, 0, ',', '.') . ' ₫',
                    'last_booking' => Carbon::parse($customer->last_booking_date)->format('d/m/Y'),
                ];
            });

        
        $recentTransactions = $completedBookings
            ->sortByDesc(function ($booking) {
                return $booking->booking_date->format('Y-m-d') . ' ' . $booking->start_time;
            })
            ->take(10)
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'reference_number' => $booking->reference_number,
                    'booking_date' => $booking->booking_date->format('d/m/Y'),
                    'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                    'customer_name' => $booking->customer ? $booking->customer->name : ($booking->customer_name ?: 'Khách vãng lai'),
                    'customer_contact' => $booking->customer ? ($booking->customer->phone ?: $booking->customer->email) : '',
                    'court_name' => $booking->court ? $booking->court->name : 'Không xác định',
                    'amount' => $booking->total_price,
                    'amount_formatted' => number_format($booking->total_price, 0, ',', '.') . ' ₫',
                    'payment_method' => $booking->payment ? $this->getPaymentMethodName($booking->payment->payment_method) : 'Không xác định',
                ];
            });

        return Inertia::render('Business/Statistics/Overview', [
            'stats' => [
                'summary' => [
                    'branches' => [
                        'count' => $branchCount,
                        'label' => __('common.branches'),
                    ],
                    'courts' => [
                        'count' => $courtCount,
                        'label' => __('common.active_courts'),
                    ],
                    'bookings' => [
                        'count' => $totalBookings,
                        'label' => __('common.booking_count'),
                        'change' => $bookingChangePercentage,
                        'is_increase' => $bookingChangePercentage >= 0,
                    ],
                    'revenue' => [
                        'amount' => $totalRevenue,
                        'amount_formatted' => number_format($totalRevenue, 0, ',', '.') . ' ₫',
                        'label' => __('common.total_revenue'),
                        'change' => $revenueChangePercentage,
                        'is_increase' => $revenueChangePercentage >= 0,
                    ],
                    'customers' => [
                        'count' => $totalCustomers,
                        'label' => __('common.total_users'),
                        'change' => $customerChangePercentage,
                        'is_increase' => $customerChangePercentage >= 0,
                    ],
                ],
                'trends' => [
                    'current' => $currentPeriodTrends,
                    'previous' => $previousPeriodTrends,
                ],
                'distribution' => [
                    'by_day' => $bookingsByDayOfWeek,
                    'by_hour' => $bookingsByHour,
                ],
                'top_data' => [
                    'courts' => $topCourts,
                    'branches' => $topBranches,
                    'customers' => $topCustomers,
                ],
                'recent_transactions' => $recentTransactions,
            ],
            'filters' => [
                'period' => $period,
                'from_date' => $startDate->format('Y-m-d'),
                'to_date' => $endDate->format('Y-m-d'),
                'date_range_formatted' => $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y'),
            ],
            'last_updated' => now()->format('d/m/Y H:i:s'),
        ]);
    }

    /**
     * Calculate booking trends by period
     *
     * @param \Illuminate\Support\Collection $bookings
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @param string $period
     * @param string $dateFormat
     * @return array
     */
    private function calculateTrendsByPeriod($bookings, $startDate, $endDate, $period, $dateFormat)
    {
        $trends = [];
        $labels = [];

        switch ($period) {
            case 'daily':
                
                $current = $startDate->copy();
                while ($current->lte($endDate)) {
                    $labels[] = $current->format($dateFormat);
                    $dayRevenue = 0;
                    $dayCount = 0;

                    $dayBookings = $bookings->filter(function($booking) use ($current) {
                        return $booking->booking_date->format('Y-m-d') === $current->format('Y-m-d');
                    });

                    if ($dayBookings->count() > 0) {
                        $dayRevenue = $dayBookings->sum('total_price');
                        $dayCount = $dayBookings->count();
                    }

                    $trends[] = [
                        'date' => $current->format('Y-m-d'),
                        'label' => $current->format($dateFormat),
                        'revenue' => $dayRevenue / 1000000, 
                        'count' => $dayCount,
                    ];

                    $current->addDay();
                }
                break;

            case 'weekly':
                
                $current = $startDate->copy()->startOfWeek();
                while ($current->lte($endDate)) {
                    $weekEnd = $current->copy()->endOfWeek();

                    $weekBookings = $bookings->filter(function($booking) use ($current, $weekEnd) {
                        return $booking->booking_date->between($current, $weekEnd);
                    });

                    $weekRevenue = $weekBookings->sum('total_price');
                    $weekCount = $weekBookings->count();

                    $trends[] = [
                        'date' => $current->format('Y-m-d'),
                        'label' => $current->format($dateFormat) . '-' . $weekEnd->format($dateFormat),
                        'revenue' => $weekRevenue / 1000000, 
                        'count' => $weekCount,
                    ];

                    $current->addWeek();
                }
                break;

            case 'yearly':
                
                $current = $startDate->copy()->startOfMonth();
                while ($current->lte($endDate)) {
                    $monthBookings = $bookings->filter(function($booking) use ($current) {
                        return $booking->booking_date->format('Y-m') === $current->format('Y-m');
                    });

                    $monthRevenue = $monthBookings->sum('total_price');
                    $monthCount = $monthBookings->count();

                    $trends[] = [
                        'date' => $current->format('Y-m-d'),
                        'label' => $current->format($dateFormat),
                        'revenue' => $monthRevenue / 1000000, 
                        'count' => $monthCount,
                    ];

                    $current->addMonth();
                }
                break;

            case 'monthly':
            default:
                
                $current = $startDate->copy()->startOfMonth();
                while ($current->lte($endDate)) {
                    $monthBookings = $bookings->filter(function($booking) use ($current) {
                        return $booking->booking_date->format('Y-m') === $current->format('Y-m');
                    });

                    $monthRevenue = $monthBookings->sum('total_price');
                    $monthCount = $monthBookings->count();

                    $trends[] = [
                        'date' => $current->format('Y-m-d'),
                        'label' => $current->format($dateFormat),
                        'revenue' => $monthRevenue / 1000000, 
                        'count' => $monthCount,
                    ];

                    $current->addMonth();
                }
                break;
        }

        return $trends;
    }

    /**
     * Get payment method name
     *
     * @param string|null $methodCode
     * @return string
     */
    private function getPaymentMethodName($methodCode)
    {
        $methods = [
            'cash' => 'Tiền mặt',
            'bank_transfer' => 'Chuyển khoản',
            'credit_card' => 'Thẻ tín dụng',
            'momo' => 'Ví MoMo',
            'vnpay' => 'VNPAY',
            'zalopay' => 'ZaloPay',
        ];

        return $methods[$methodCode] ?? 'Khác';
    }

    /**
     * Display revenue statistics
     *
     * @return \Inertia\Response
     */
    public function revenue(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        $period = $request->input('period', 'monthly');
        $now = Carbon::now();

        
        switch ($period) {
            case 'daily':
                $startDate = $now->copy()->startOfDay();
                $endDate = $now->copy()->endOfDay();
                $previousStartDate = $now->copy()->subDay()->startOfDay();
                $previousEndDate = $now->copy()->subDay()->endOfDay();
                break;
            case 'weekly':
                $startDate = $now->copy()->startOfWeek();
                $endDate = $now->copy()->endOfWeek();
                $previousStartDate = $now->copy()->subWeek()->startOfWeek();
                $previousEndDate = $now->copy()->subWeek()->endOfWeek();
                break;
            case 'monthly':
                $startDate = $now->copy()->startOfMonth();
                $endDate = $now->copy()->endOfMonth();
                $previousStartDate = $now->copy()->subMonth()->startOfMonth();
                $previousEndDate = $now->copy()->subMonth()->endOfMonth();
                break;
            case 'yearly':
                $startDate = $now->copy()->startOfYear();
                $endDate = $now->copy()->endOfYear();
                $previousStartDate = $now->copy()->subYear()->startOfYear();
                $previousEndDate = $now->copy()->subYear()->endOfYear();
                break;
            case 'custom':
                $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : $now->copy()->subMonth()->startOfDay();
                $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : $now->copy()->endOfDay();
                $dateRange = $endDate->diffInDays($startDate) + 1;
                $previousStartDate = $startDate->copy()->subDays($dateRange);
                $previousEndDate = $startDate->copy()->subDay();
                break;
            default:
                $startDate = $now->copy()->startOfMonth();
                $endDate = $now->copy()->endOfMonth();
                $previousStartDate = $now->copy()->subMonth()->startOfMonth();
                $previousEndDate = $now->copy()->subMonth()->endOfMonth();
        }

        
        $bookings = CourtBooking::with(['court', 'customer'])
            ->whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->get();

        
        $previousBookings = CourtBooking::whereHas('court', function ($query) use ($businessId) {
                $query->whereHas('branch', function ($q) use ($businessId) {
                    $q->where('business_id', $businessId);
                });
            })
            ->where('status', 'completed')
            ->whereBetween('booking_date', [$previousStartDate->format('Y-m-d'), $previousEndDate->format('Y-m-d')])
            ->get();

        
        $totalRevenue = $bookings->sum('total_price');
        $previousTotalRevenue = $previousBookings->sum('total_price');

        
        $comparisonPercentage = 0;
        if ($previousTotalRevenue > 0) {
            $comparisonPercentage = (($totalRevenue - $previousTotalRevenue) / $previousTotalRevenue) * 100;
        }

        
        $revenueByDate = $bookings->groupBy(function ($booking) {
            return Carbon::parse($booking->booking_date)->format('Y-m-d');
        })->map(function ($dateBookings) {
            return [
                'date' => Carbon::parse($dateBookings->first()->booking_date)->format('Y-m-d'),
                'revenue' => $dateBookings->sum('total_price'),
                'count' => $dateBookings->count()
            ];
        })->values()->all();

        
        $highestRevenueDay = collect($revenueByDate)->sortByDesc('revenue')->first();

        
        $totalDays = count($revenueByDate) > 0 ? count($revenueByDate) : 1;
        $averageDailyRevenue = $totalRevenue / $totalDays;

        
        $onlineBookings = $bookings->filter(function($booking) {
            return $booking->customer_id !== null;
        });

        $walkInBookings = $bookings->filter(function($booking) {
            return $booking->customer_id === null;
        });

        
        $courtTypes = Court::whereHas('bookings', function($query) use ($bookings) {
            $query->whereIn('id', $bookings->pluck('court_id'));
        })->with(['bookings' => function($query) use ($startDate, $endDate) {
            $query->where('status', 'completed')
                ->whereBetween('booking_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
        }])
        ->get()
        ->groupBy('type')
        ->map(function($courts, $type) {
            $revenue = 0;
            foreach($courts as $court) {
                $revenue += $court->bookings->sum('total_price');
            }
            return [
                'type' => $type ?: 'Standard',
                'revenue' => $revenue,
            ];
        })->values()->all();

        
        $totalCourtTypeRevenue = collect($courtTypes)->sum('revenue');
        $courtTypes = collect($courtTypes)->map(function($item) use ($totalCourtTypeRevenue) {
            $percentage = $totalCourtTypeRevenue > 0 ? ($item['revenue'] / $totalCourtTypeRevenue) * 100 : 0;
            return array_merge($item, ['percentage' => round($percentage, 1)]);
        })->all();

        
        $recentTransactions = $bookings
            ->groupBy('reference_number')
            ->map(function($bookingsGroup, $refNumber) {
                $firstBooking = $bookingsGroup->first();
                $totalAmount = $bookingsGroup->sum('total_price');

                return [
                    'id' => $firstBooking->id,
                    'date' => Carbon::parse($firstBooking->booking_date)->format('Y-m-d'),
                    'time' => Carbon::parse($firstBooking->start_time)->format('H:i'),
                    'customer' => $firstBooking->customer ? $firstBooking->customer->name : ($firstBooking->customer_name ?: 'Walk-in customer'),
                    'amount' => $totalAmount,
                    'source' => $firstBooking->customer_id ? 'Đặt sân online' : 'Đặt sân trực tiếp',
                    'status' => $firstBooking->status,
                    'reference_number' => $refNumber,
                    'booking_count' => $bookingsGroup->count()
                ];
            })
            ->sortByDesc(function($transaction) {
                return Carbon::parse($transaction['date'])->format('Y-m-d') . ' ' . $transaction['time'];
            })
            ->take(10)
            ->values()
            ->all();

        
        $totalRevenueForSources = $totalRevenue > 0 ? $totalRevenue : 1; 

        $sourceStats = [
            [
                'source' => 'Đặt sân trực tiếp',
                'revenue' => $walkInBookings->sum('total_price'),
                'percentage' => round(($walkInBookings->sum('total_price') / $totalRevenueForSources) * 100, 1)
            ],
            [
                'source' => 'Đặt sân online',
                'revenue' => $onlineBookings->sum('total_price'),
                'percentage' => round(($onlineBookings->sum('total_price') / $totalRevenueForSources) * 100, 1)
            ]
        ];

        
        

        
        $revenueData = [
            'summary' => [
                'total_revenue' => $totalRevenue,
                'comparison_percentage' => round($comparisonPercentage, 1),
                'average_daily' => $averageDailyRevenue,
                'highest_day' => $highestRevenueDay ? $highestRevenueDay['revenue'] : 0,
                'highest_day_date' => $highestRevenueDay ? $highestRevenueDay['date'] : $startDate->format('Y-m-d'),
                'total_bookings' => $bookings->unique('reference_number')->count(),
            ],
            'by_period' => $revenueByDate,
            'by_source' => $sourceStats,
            'by_court_type' => $courtTypes,
            'recent_transactions' => $recentTransactions
        ];

        return Inertia::render('Business/reports/Revenue', [
            'filters' => [
                'period' => $period,
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
            'revenueData' => $revenueData,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        return Inertia::render('Business/Statistics/Create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        return redirect()->route('statistics.index')
            ->with('success', 'Statistic data created successfully');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function show($id)
    {
        return Inertia::render('Business/Statistics/Show', [
            'statistic' => []
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function edit($id)
    {
        return Inertia::render('Business/Statistics/Edit', [
            'statistic' => []
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        return redirect()->route('statistics.index')
            ->with('success', 'Statistic data updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        return redirect()->route('statistics.index')
            ->with('success', 'Statistic data deleted successfully');
    }
}
