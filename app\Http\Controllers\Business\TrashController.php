<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Court;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TrashController extends Controller
{
    /**
     * Display a listing of the trashed courts.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;
        $search = $request->input('search');
        $sort = $request->input('sort', 'deleted_at');
        $direction = $request->input('direction', 'desc');

        $query = Court::with('branch')
            ->onlyTrashed()
            ->where('business_id', $business->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%");
            });
        }

        $query->orderBy($sort, $direction);
        $courts = $query->paginate(10)->withQueryString();

        return Inertia::render('Business/Courts/Trash', [
            'courts' => $courts,
            'filters' => [
                'search' => $search,
                'sort' => $sort,
                'direction' => $direction,
            ],
        ]);
    }

    /**
     * Restore the specified court.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restore($id)
    {
        $user = Auth::user();
        $business = $user->business;

        $court = Court::onlyTrashed()
            ->where('id', $id)
            ->where('business_id', $business->id)
            ->firstOrFail();

        $court->restore();

        return redirect()->route('business.trash.index')
            ->with('message', __('courts.court_restored_successfully'));
    }

    /**
     * Permanently delete the specified court.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function forceDelete($id)
    {
        $user = Auth::user();
        $business = $user->business;

        $court = Court::onlyTrashed()
            ->where('id', $id)
            ->where('business_id', $business->id)
            ->firstOrFail();
        $court->forceDelete();

        return redirect()->route('business.trash.index')
            ->with('message', __('courts.court_deleted_permanently'));
    }

    /**
     * Restore all trashed courts.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreAll()
    {
        $user = Auth::user();
        $business = $user->business;

        Court::onlyTrashed()
            ->where('business_id', $business->id)
            ->restore();

        return redirect()->route('business.trash.index')
            ->with('message', __('courts.all_courts_restored'));
    }

    /**
     * Permanently delete all trashed courts.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function emptyTrash()
    {
        $user = Auth::user();
        $business = $user->business;

        Court::onlyTrashed()
            ->where('business_id', $business->id)
            ->forceDelete();

        return redirect()->route('business.trash.index')
            ->with('message', __('courts.trash_emptied'));
    }
}