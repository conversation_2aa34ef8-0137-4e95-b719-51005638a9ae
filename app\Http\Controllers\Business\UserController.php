<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the users belonging to the current business.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('error', __('business.no_business'));
        }

        $search = $request->search;
        $status = $request->status;
        $specificRoleFilter = $request->roleFilter;
        $branchFilter = $request->branchFilter;
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query = User::select('users.*')
            ->where('business_id', $businessId)
            ->whereHas('roles', function ($q) {
                $q->where('name', '!=', 'user');
            })
            ->distinct();

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.email', 'like', "%{$search}%")
                    ->orWhere('users.phone', 'like', "%{$search}%");
            });
        }

        if (!empty($status)) {
            $query->where('users.status', $status);
        }

        if (!empty($specificRoleFilter)) {
            $query->whereHas('roles', function ($q) use ($specificRoleFilter) {
                $q->where('roles.id', $specificRoleFilter);
            });
        }

        if (!empty($branchFilter)) {
            $query->where('branch_id', $branchFilter);
        }

        $query->orderBy("users.{$sortField}", $sortDirection);

        $users = $query->with(['branch'])->paginate(10)->withQueryString();

        $users->getCollection()->transform(function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            $user->role_names = $userRoles;

            $user->branch_name = $user->branch ? $user->branch->name : null;
            $user->branch_id = $user->branch ? $user->branch->id : null;

            return $user;
        });

        $roles = Role::whereIn('name', ['admin', 'manager', 'staff'])
            ->get(['id', 'name']);

        $branches = \App\Models\Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->get(['id', 'name']);

        return Inertia::render('Business/Users/<USER>', [
            'users' => $users,
            'roles' => $roles,
            'branches' => $branches,
            'filters' => [
                'search' => $search ?? '',
                'status' => $status ?? '',
                'roleFilter' => $specificRoleFilter ?? '',
                'branchFilter' => $branchFilter ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }


    /**
     * Show the form for creating a new user.
     *
     * @return \Inertia\Response
     */
    public function create(Request $request)
    {
        $businessId = $request->user()->business_id;
        $roles = Role::whereIn('name', ['admin', 'manager', 'staff'])
            ->get(['id', 'name']);

        $branches = \App\Models\Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->get(['id', 'name']);

        return Inertia::render('Business/Users/<USER>', [
            'roles' => $roles,
            'branches' => $branches,
            'defaultValues' => [
                'role_ids' => [],
                'branch_id' => '',
                'status' => 'active',
                'has_verified_email' => false
            ]
        ]);
    }

    /**
     * Store a newly created user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->back()->withErrors([
                'error' => __('business.no_business')
            ]);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
            'branch_id' => 'nullable|exists:branches,id',
            'has_verified_email' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'phone' => $validated['phone'] ?? null,
                'status' => $validated['status'],
                'email_verified_at' => $request->has('has_verified_email') ? now() : null,
            ]);

            $primaryRoleId = $validated['role_ids'][0];

            $role = Role::findById($primaryRoleId);

            $user->assignRole($role);

            DB::commit();

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_created_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_create_user') . ': ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Display the specified user.
     *
     * @param  \App\Models\User  $user
     * @return \Inertia\Response
     */
    public function show(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;

        $hasAccess = $user->business_id === $businessId;

        if (!$hasAccess) {
            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.permission_denied'));
        }

        $business = Business::find($businessId);

        $userRoles = $user->roles()->get();
        $branch = $user->branch;

        return Inertia::render('Business/Users/<USER>', [
            'user' => $user,
            'userRoles' => $userRoles,
            'branch' => $branch,
            'business' => $business,
        ]);
    }

    /**
     * Show the form for editing the specified user.
     *
     * @param  \App\Models\User  $user
     * @return \Inertia\Response
     */
    public function edit(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;

        $hasAccess = $user->business_id === $businessId;

        if (!$hasAccess) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        $roles = Role::whereIn('name', ['admin', 'manager', 'staff'])
            ->get(['id', 'name']);

        $branches = \App\Models\Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->get(['id', 'name']);

        $userRole = $user->roles()->first();

        $userRoles = [];
        if ($userRole) {
            $userRoles[] = [
                'role_id' => $userRole->id,
                'branch_id' => $user->branch_id
            ];
        }

        return Inertia::render('Business/Users/<USER>', [
            'user' => $user,
            'roles' => $roles,
            'branches' => $branches,
            'userRoles' => $userRoles
        ]);
    }

    /**
     * Update the specified user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;
        $currentUser = $request->user();
        $hasAccess = $user->business_id === $businessId;

        if (!$hasAccess) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        $currentUserRole = $currentUser->roles()->first();
        $isAdmin = $currentUserRole && $currentUserRole->name === 'admin';
        $isManager = $currentUserRole && $currentUserRole->name === 'manager';

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'has_verified_email' => 'sometimes|boolean',
            'phone' => 'required|string|max:20',
            'role_ids' => [
                Rule::requiredIf(function () use ($isAdmin, $isManager) {
                    return $isAdmin || $isManager;
                }),
                'array'
            ],
            'role_ids.*' => 'exists:roles,id',
            'branch_id' => [
                'nullable',
                'exists:branches,id'
            ],
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        try {
            DB::beginTransaction();

            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
            ];

            if (!empty($validated['password'])) {
                $userData['password'] = Hash::make($validated['password']);
            }

            if (isset($validated['has_verified_email'])) {
                $userData['email_verified_at'] = $validated['has_verified_email'] ? now() : null;
            }

            if ($isAdmin || $isManager) {
                $primaryRoleId = $validated['role_ids'][0];
                $selectedRole = Role::findById($primaryRoleId);

                if ($selectedRole && $selectedRole->name === 'admin' && !$isAdmin) {
                    throw new \Exception(__('business.role_not_allowed_for_your_permission'));
                }

                if ($selectedRole && ($selectedRole->name === 'manager' || $selectedRole->name === 'admin') && !$isAdmin && !$isManager) {
                    throw new \Exception(__('business.role_not_allowed_for_your_permission'));
                }

                // Add branch_id to user data if provided
                if (isset($validated['branch_id'])) {
                    $userData['branch_id'] = $validated['branch_id'];
                }

                // Make sure business_id is set
                $userData['business_id'] = $businessId;

                // Update user roles
                $user->syncRoles([$selectedRole]);
            }

            $user->update($userData);

            DB::commit();

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_updated_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_update_user') . ': ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Remove the specified user from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;
        $currentUser = $request->user();
        $hasAccess = $user->business_id === $businessId;

        if (!$hasAccess) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        if ($user->id === $currentUser->id) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.cannot_delete_own_account'));
        }

        try {
            DB::beginTransaction();

            // Remove roles
            $user->roles()->detach();

            // If user has no other business assignments, delete the user
            $otherBusinesses = User::where('id', $user->id)
                ->where('business_id', '!=', $businessId)
                ->count();

            if ($otherBusinesses === 0) {
                $user->delete();
            } else {
                // Just remove this business association
                $user->update([
                    'business_id' => null,
                    'branch_id' => null
                ]);
            }

            DB::commit();

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_deleted_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_delete_user') . ': ' . $e->getMessage()
            ]);
        }
    }
}