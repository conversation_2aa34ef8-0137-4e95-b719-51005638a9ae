<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\CourtPrice;
use App\Services\BookingEventService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Config;
use App\Services\MailService;

class BookingController extends Controller
{
    /**
     * Lưu đơn đặt sân mới vào hệ thống
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branches,id',
                'booking_date' => 'required|date|after_or_equal:today',
                'customer_name' => 'required|string|max:255',
                'customer_phone' => 'required|string|max:20',
                'customer_email' => 'nullable|email|max:255',
                'services' => 'nullable|array',
                'services.*.id' => 'exists:services,id',
                'services.*.quantity' => 'integer|min:1',
                'services.*.price' => 'numeric|min:0',
                'booking_courts' => 'required|array',
                'booking_courts.*.court_id' => 'required|exists:courts,id',
                'booking_courts.*.booking_slot' => 'required|array',
                'booking_courts.*.start_time' => 'required|string',
                'booking_courts.*.end_time' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi dữ liệu đầu vào',
                    'errors' => $validator->errors()
                ], 422);
            }

            return DB::transaction(function () use ($request) {
                $data = $request->all();
                $user = Auth::user();

                
                $referenceNumber = \App\Models\CourtBooking::generateReferenceNumber('online');
                $mainBooking = new \App\Models\Booking();
                $mainBooking->branch_id = $data['branch_id'];
                $mainBooking->user_id = $user ? $user->id : null;

                
                $customer_id = null;
                $isMember = false;
                if (!empty($data['customer_phone']) || !empty($data['customer_email'])) {
                    $customerQuery = \App\Models\Customer::query();

                    if (!empty($data['customer_phone'])) {
                        $customerQuery->where('phone', $data['customer_phone']);
                    }

                    if (!empty($data['customer_email'])) {
                        $customerQuery->orWhere('email', $data['customer_email']);
                    }

                    $customer = $customerQuery->first();
                    $customer_id = $customer->id ?? null;
                    $isMember = !is_null($customer);
                }

                $mainBooking->customer_id = $customer_id;
                $mainBooking->reference_number = $referenceNumber;
                $mainBooking->customer_name = $data['customer_name'];
                $mainBooking->customer_phone = $data['customer_phone'];
                $mainBooking->customer_email = $data['customer_email'] ?? null;
                $mainBooking->notes = $data['notes'] ?? null;
                $mainBooking->number_of_players = $data['number_of_players'] ?? null;
                $mainBooking->status = 'pending';
                $mainBooking->payment_status = 'unpaid';
                $mainBooking->booking_type = 'online';
                $mainBooking->booking_date = $data['booking_date'];

                
                $mainBooking->metadata = [
                    'created_by' => 'customer',
                    'user_id' => $user ? $user->id : null,
                    'created_at' => now()->toDateTimeString(),
                    'is_member' => $isMember,
                    'services' => !empty($data['services']) ? $data['services'] : [],
                    'number_of_courts' => count($data['booking_courts'] ?? [])
                ];

                $mainBooking->save();

                
                $data['reference_number'] = $referenceNumber;

                $createdBookings = [];

                $result = CourtBooking::processBookings(
                    $data,
                    $user,
                    'online',
                    $createdBookings
                );

                if (!$result['success']) {
                    
                    $mainBooking->delete();

                    return response()->json([
                        'success' => false,
                        'message' => $result['message']
                    ], $result['status_code'] ?? 400);
                }

                
                $mainBooking->total_price = $result['total_price'];
                $mainBooking->save();

                
                if (!empty($createdBookings)) {
                    foreach ($createdBookings as $booking) {
                        $courtBooking = CourtBooking::find($booking['id']);
                        if ($courtBooking) {
                            $courtBooking->booking_id = $mainBooking->id;
                            $courtBooking->save();
                        }
                    }

                    $firstBooking = CourtBooking::find($createdBookings[0]['id']);

                    if ($firstBooking && isset($data['branch_id'])) {
                        \App\Services\NotificationService::createBookingNotification(
                            $data['branch_id'],
                            $referenceNumber,
                            $data['customer_name'],
                            $data['customer_phone'],
                            $result['bookings']
                        );
                    }

                    if ($firstBooking && !empty($data['customer_email'])) {
                        $branchId = $firstBooking->court->branch_id;

                        
                        $branch = \App\Models\Branch::find($branchId);
                        $business = $branch ? $branch->business : null;

                        if (!$branch || !$business) {
                            Log::error('Missing branch or business information for email', [
                                'branch_id' => $branchId,
                                'has_branch' => !is_null($branch),
                                'has_business' => !is_null($business)
                            ]);
                        } else {
                            MailService::sendUsingBranchConfig(
                                $branchId,
                                $data['customer_email'],
                                new BookingConfirmation(
                                    $mainBooking,
                                    $result['bookings'],
                                    $branch,
                                    $business
                                )
                            );
                        }
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Đặt sân thành công!',
                    'reference_number' => $referenceNumber,
                    'total_price' => $result['total_price'],
                    'bookings' => $result['bookings'],
                    'email_sent' => !empty($data['customer_email'])
                ]);
            });
        } catch (\Exception $e) {
            Log::error('Booking error: ' . $e->getMessage(), ['exception' => $e]);

            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi khi đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booked time slots for a court on a specific date
     * 
     * @param int $courtId
     * @param string $date
     * @return array
     */
    protected function getBookedTimeSlots($courtId, $date)
    {
        $bookings = CourtBooking::where('court_id', $courtId)
            ->where('booking_date', $date)
            ->whereIn('status', ['pending', 'confirmed', 'completed'])
            ->get(['start_time', 'end_time']);

        $bookedSlots = [];

        foreach ($bookings as $booking) {
            $start = Carbon::parse($booking->start_time);
            $end = Carbon::parse($booking->end_time);

            while ($start < $end) {
                $bookedSlots[] = $start->format('H:i');
                $start->addMinutes(30);
            }
        }

        return $bookedSlots;
    }

    /**
     * Get booking details by reference number
     *
     * @param string $referenceNumber
     * @return \Illuminate\Http\Response
     */
    public function getByReference($referenceNumber)
    {
        try {
            $booking = CourtBooking::where('reference_number', $referenceNumber)
                ->with(['court', 'court.branch', 'services'])
                ->first();

            if (!$booking) {
                return redirect()->route('customer.branch.booking')
                    ->withErrors(['booking_error' => 'Booking not found']);
            }

            return Inertia::render('Customer/Bookings/Show', [
                'booking' => $booking
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching booking: ' . $e->getMessage());

            return redirect()->route('customer.branch.booking')
                ->withErrors(['booking_error' => 'An error occurred while fetching the booking details']);
        }
    }

    /**
     * Display booking confirmation page
     * 
     * @param int $id
     * @return \Inertia\Response
     */
    public function confirmation($id)
    {
        try {
            $booking = CourtBooking::with(['court', 'court.branch', 'services', 'payment'])
                ->findOrFail($id);

            return Inertia::render('Customer/Bookings/Confirmation', [
                'booking' => $booking
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching booking confirmation: ' . $e->getMessage());

            return redirect()->route('customer.branch.booking')
                ->withErrors(['booking_error' => 'Booking not found']);
        }
    }

    /**
     * Cancel a booking
     *
     * @param Request $request
     * @param string $referenceNumber
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, $referenceNumber)
    {
        try {
            $booking = CourtBooking::where('reference_number', $referenceNumber)->first();

            if (!$booking) {
                return redirect()->route('customer.bookings.history')
                    ->withErrors(['booking_error' => 'Booking not found']);
            }

            if (in_array($booking->status, ['completed', 'cancelled'])) {
                return redirect()->route('customer.bookings.history')
                    ->withErrors(['booking_error' => 'Booking cannot be cancelled in its current state']);
            }

            $booking->status = 'cancelled';
            $booking->cancellation_reason = $request->input('reason');
            $booking->cancelled_at = now();
            $booking->save();

            return redirect()->route('customer.bookings.history')
                ->with('success', 'Booking cancelled successfully');
        } catch (\Exception $e) {
            Log::error('Error cancelling booking: ' . $e->getMessage());

            return redirect()->route('customer.bookings.history')
                ->withErrors(['booking_error' => 'An error occurred while cancelling the booking']);
        }
    }

    /**
     * Display booking history for current user
     * 
     * @return \Inertia\Response
     */
    public function history()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $bookings = CourtBooking::where('user_id', $user->id)
            ->with(['court', 'court.branch', 'payment'])
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->get();

        return Inertia::render('Customer/Bookings/History', [
            'bookings' => $bookings
        ]);
    }
    public function timeline(Request $request, string $referenceNumber)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy người dùng'
            ], 401);
        }
        $booking = CourtBooking::where('reference_number', $referenceNumber)->first();

        if (!$booking) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy đơn đặt sân'
            ], 404);
        }

        $bookings = $booking->getGroupedBookings();
        $timeline = BookingEventService::getFormattedTimeline($referenceNumber);

        return response()->json([
            'success' => true,
            'data' => [
                'timeline' => $timeline,
                'bookings' => $bookings
            ]
        ]);
    }
}