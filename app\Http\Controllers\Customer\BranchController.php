<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\CourtBooking;
use App\Models\CourtPrice;
use App\Services\AddressService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use App\Models\Likelist;

class BranchController extends Controller
{
    protected $addressService;

    public function __construct(AddressService $addressService)
    {
        $this->addressService = $addressService;
    }



    public function index(Request $request)
    {
        $date = $request->input('date') ?? Carbon::today()->format('Y-m-d');
        $timeRange = $request->input('time');
        $duration = (int) $request->input('duration', 60);
        $startTime = null;
        $endTime = null;


        if ($timeRange && is_array($timeRange) && count($timeRange) > 0) {
            $startTime = Carbon::parse($date . ' ' . $timeRange[0]);
            $endTime = Carbon::parse($date . ' ' . $timeRange[count($timeRange) - 1])->addHour();
        }


        $cacheKey = md5(json_encode([
            'location' => $request->input('location'),
            'price_min' => $request->input('price_min'),
            'price_max' => $request->input('price_max'),
            'date' => $date,
        ]));


        $basicBranchData = Cache::remember("branch_basic_data_{$cacheKey}", 60, function () use ($request, $date) {
            $query = Branch::query()
                ->withCount([
                    'courts' => function ($query) {
                        $query->whereNull('deleted_at');
                    }
                ])
                ->where('status', 'active')
                ->where('min_price', '!=', 0)
                ->where('max_price', '!=', 0);


            if ($request->filled(['price_min', 'price_max'])) {
                $query->whereHas('prices', function ($query) use ($request) {
                    $query->where('price_type', 'normal')
                        ->where('is_active', true)
                        ->whereBetween('price_per_hour', [
                            $request->input('price_min', 0),
                            $request->input('price_max', 1000000)
                        ]);
                });
            }


            $query->with([
                'courts:id,branch_id,name,is_active,type',
                'prices' => function ($priceQuery) use ($date) {
                    $priceQuery->select('id', 'branch_id', 'price_type', 'price_per_hour', 'start_time', 'end_time', 'special_date', 'is_active')
                        ->where(function ($q) use ($date) {
                            $q->where(function ($normalPrice) {
                                $normalPrice->where('price_type', 'normal')
                                    ->where('is_active', true);
                            })->orWhere(function ($specialPrice) use ($date) {
                                $specialPrice->where('price_type', 'special_date')
                                    ->where('special_date', $date)
                                    ->where('is_active', true);
                            });
                        })
                        ->orderByRaw("CASE WHEN price_type = 'special_date' AND special_date = ? THEN 0 ELSE 1 END", [$date])
                        ->orderBy('start_time');
                },
                'images:id,branch_id,image_url,is_main'
            ]);


            if ($request->filled('location')) {
                $searchTerm = $request->input('location');
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', "%{$searchTerm}%")
                        ->orWhere('address', 'like', "%{$searchTerm}%")
                        ->orWhere('province_name', 'like', "%{$searchTerm}%")
                        ->orWhere('district_name', 'like', "%{$searchTerm}%")
                        ->orWhere('ward_name', 'like', "%{$searchTerm}%");
                });
            }

            return $query->get();
        });


        $bookings = CourtBooking::getBookingFields($date, 'pending', ['id', 'branch_id', 'court_id', 'start_time', 'end_time']);


        $branchs = $basicBranchData->map(function ($branch) {
            return clone $branch;
        });

        $likedBranchIds = [];
        if (Auth::check()) {
            $likedBranchIds = Likelist::where('user_id', Auth::id())
                ->where('is_active', true)
                ->pluck('branch_id')
                ->toArray();
        }

        $branchs->each(function ($branch) use ($date, $duration, $bookings, $startTime, $endTime, $likedBranchIds) {
            $branch->is_liked = in_array($branch->id, $likedBranchIds);
            $branch->full_address = implode(', ', array_filter([
                $branch->address,
                $branch->province_name,
                $branch->district_name,
                $branch->ward_name
            ]));

            $this->processDisplayPrice($branch, $date);

            $slots = [];
            $opening = Carbon::parse($branch->opening_hour);
            $closing = Carbon::parse($branch->closing_hour);

            while ($opening->lt($closing)) {
                $start = $opening->copy();
                $end = $opening->copy()->addMinutes(30);
                $timeStr = $start->format('H:i');
                $branchBookings = $bookings->where('branch_id', $branch->id);
                $isBooked = $branchBookings->contains(function ($booking) use ($start, $end) {
                    return Carbon::parse($booking->start_time)->lt($end) &&
                        Carbon::parse($booking->end_time)->gt($start);
                });

                $slots[] = [
                    'time' => $timeStr,
                    'is_booked' => $isBooked,
                ];

                $opening->addMinutes(30);
            }

            $branch->available_slots = $slots;


            if ($startTime && $endTime) {

                $branchCourtIds = $branch->courts->where('is_active', 1)->pluck('id')->toArray();
                $branchBookings = $bookings->where('branch_id', $branch->id);

                $hasAvailableCourts = false;

                foreach ($branchCourtIds as $courtId) {
                    $courtBookings = $branchBookings->where('court_id', $courtId);
                    $courtIsAvailable = true;

                    foreach ($courtBookings as $booking) {
                        $bookingStart = Carbon::parse($booking->start_time);
                        $bookingEnd = Carbon::parse($booking->end_time);

                        if ($bookingStart->lt($endTime) && $bookingEnd->gt($startTime)) {
                            $courtIsAvailable = false;
                            break;
                        }
                    }

                    if ($courtIsAvailable) {
                        $hasAvailableCourts = true;
                        break;
                    }
                }

                $branch->has_available_courts = $hasAvailableCourts;
            } else {
                $branch->has_available_courts = true;
            }
        });


        if ($startTime && $endTime) {
            $branchs = $branchs->filter(function ($branch) {
                return $branch->has_available_courts;
            })->values();
        }


        $filters = $request->all();
        if (isset($filters['time']) && !is_array($filters['time'])) {
            $filters['time'] = [$filters['time']];
        }


        return inertia('Customers/BranchSearch/index', [
            'branches' => $branchs,
            'filters' => $filters,
            'date' => $date,
            'duration' => $duration,
            'timeRange' => $timeRange ? [$startTime->format('H:i'), $endTime->format('H:i')] : null,
        ]);
    }


    /**
     * Xử lý giá hiển thị cho chi nhánh, ưu tiên giá special_date
     * 
     * @param \App\Models\Branch $branch
     * @param string $date
     * @return void
     */
    private function processDisplayPrice($branch, $date)
    {
        if (!$branch->prices || $branch->prices->isEmpty()) {
            return;
        }
        $specialDatePrices = $branch->prices->filter(function ($price) use ($date) {
            return $price->price_type === 'special_date'
                && $price->special_date === $date
                && $price->is_active;
        });

        if ($specialDatePrices->isNotEmpty()) {
            $minPrice = $specialDatePrices->min('price_per_hour');
            $maxPrice = $specialDatePrices->max('price_per_hour');
            $branch->display_min_price = $minPrice;
            $branch->display_max_price = $maxPrice;
            $branch->min_price = $minPrice;
            $branch->has_special_price = true;
            $branch->special_prices = $specialDatePrices;
            $timeRanges = $specialDatePrices->map(function ($price) {
                return [
                    'start_time' => $price->start_time,
                    'end_time' => $price->end_time,
                    'price' => $price->price_per_hour,
                    'member_price' => $price->member_price_per_hour
                ];
            });
            $branch->special_time_ranges = $timeRanges;
        } else {
            $normalPrices = $branch->prices->filter(function ($price) {
                return $price->price_type === 'normal' && $price->is_active;
            });

            if ($normalPrices->isNotEmpty()) {
                $minPrice = $normalPrices->min('price_per_hour');
                $maxPrice = $normalPrices->max('price_per_hour');

                $branch->display_min_price = $minPrice;
                $branch->display_max_price = $maxPrice;
                $branch->has_special_price = false;
            }
        }
    }

    public function show(Branch $branch, Request $request)
    {
        $date = Carbon::today()->format('Y-m-d');
        $branch->load([
            'images',
            'courts' => function ($query) {
                $query->whereNull('deleted_at');
            },
            'prices' => function ($priceQuery) use ($date, $branch) {
                // Đầu tiên kiểm tra xem có giá đặc biệt cho ngày này không
                $hasSpecialPrice = \App\Models\CourtPrice::where('branch_id', $branch->id)
                    ->where('is_active', true)
                    ->where('price_type', 'special_date')
                    ->where('special_date', $date)
                    ->exists();

                $priceQuery->where('is_active', true);

                if ($hasSpecialPrice) {
                    $priceQuery->where('price_type', 'special_date')
                        ->where('special_date', $date);
                } else {
                    $priceQuery->where('price_type', 'normal');
                }

                $priceQuery->orderBy('court_type')
                    ->orderBy('start_time');
            },
            'reviews' => function ($query) {
                $query->with('customer')
                    ->where('status', 'approved')
                    ->orderBy('created_at', 'desc')
                    ->take(10);
            },
            'amenities' => function ($query) {
                $query->where('status', true);
            }
        ]);
        // Check if JSON response is requested
        if ($request->wantsJson() || $request->get('format') === 'json' || $request->is('api/*')) {
            $jsonData = $this->getBranchJson($branch, $date);

            // Add review data
            $jsonData['average_rating'] = $branch->getAverageRating();
            $jsonData['review_count'] = $branch->getReviewCount();

            return response()->json($jsonData);
        }


        $bookings = CourtBooking::getBookingFields($date, ['pending', 'confirmed'], [
            'id',
            'branch_id',
            'court_id',
            'start_time',
            'end_time'
        ])->where('branch_id', $branch->id);
        $branch->full_address = implode(', ', array_filter([
            $branch->address,
            $branch->province_name,
            $branch->district_name,
            $branch->ward_name
        ]));
        $slots = [];
        $opening = Carbon::parse($branch->opening_hour);
        $closing = Carbon::parse($branch->closing_hour);

        while ($opening->lt($closing)) {
            $start = $opening->copy();
            $end = $opening->copy()->addMinutes(30);

            $timeStr = $start->format('H:i');
            $isBooked = $bookings->contains(function ($booking) use ($start, $end) {
                return Carbon::parse($booking->start_time)->lt($end) &&
                    Carbon::parse($booking->end_time)->gt($start);
            });

            $slots[] = [
                'time' => $timeStr,
                'is_booked' => $isBooked,
            ];

            $opening->addMinutes(30);
        }

        $branch->available_slots = $slots;
        $branch->courts->each(function ($court) use ($bookings, $date) {
            $courtSlots = [];
            $opening = Carbon::parse($court->branch->opening_hour);
            $closing = Carbon::parse($court->branch->closing_hour);

            while ($opening->lt($closing)) {
                $start = $opening->copy();
                $end = $opening->copy()->addMinutes(30);

                $timeStr = $start->format('H:i');
                $isBooked = $bookings->contains(function ($booking) use ($court, $start, $end) {
                    return $booking->court_id == $court->id &&
                        Carbon::parse($booking->start_time)->lt($end) &&
                        Carbon::parse($booking->end_time)->gt($start);
                });

                $courtSlots[] = [
                    'time' => $timeStr,
                    'is_booked' => $isBooked,
                ];

                $opening->addMinutes(30);
            }

            $court->available_slots = $courtSlots;
        });

        $normalPrices = $branch->prices->where('price_type', 'normal')->values();
        $specialPrices = $branch->prices->where('price_type', 'special_date')->values();
        $groupedNormalPrices = $normalPrices->groupBy('day_of_week');
        $formattedPrices = [
            'normal' => $groupedNormalPrices,
            'special' => $specialPrices,
            'today' => Carbon::today()->dayOfWeek,
            'todaySpecial' => $specialPrices->where('special_date', $date)->values(),
        ];

        $branch->formatted_prices = $formattedPrices;

        $formattedReviews = $branch->reviews->map(function ($review) {
            $customer = $review->customer;
            $nameParts = explode(' ', $customer->name);
            $initials = '';

            if (count($nameParts) > 1) {
                $initials = mb_substr($nameParts[0], 0, 1) . mb_substr($nameParts[count($nameParts) - 1], 0, 1);
            } else {
                $initials = mb_substr($customer->name, 0, 2);
            }

            return [
                'id' => $review->id,
                'initial' => strtoupper($initials),
                'name' => $customer->name,
                'date' => Carbon::parse($review->created_at)->format('d/m/Y'),
                'rating' => $review->rating,
                'content' => $review->content
            ];
        });

        $branch->formatted_reviews = $formattedReviews;
        $branch->review_count = $branch->getReviewCount();
        $branch->rating = $branch->getAverageRating() ?: 0;

        return Inertia::render('Customers/BranchSearch/show', [
            'branch' => $branch,
            'date' => $date,
        ]);
    }

    /**
     * Get branch data in JSON format
     * 
     * @param \App\Models\Branch $branch
     * @param string $date
     * @return \Illuminate\Http\JsonResponse
     */
    protected function getBranchJson(Branch $branch, $date)
    {
        $branch->load([
            'images',
            'courts' => function ($query) {
                $query->whereNull('deleted_at');
            },
            'prices' => function ($priceQuery) use ($date, $branch) {
                // Đầu tiên kiểm tra xem có giá đặc biệt cho ngày này không
                $hasSpecialPrice = \App\Models\CourtPrice::where('branch_id', $branch->id)
                    ->where('is_active', true)
                    ->where('price_type', 'special_date')
                    ->where('special_date', $date)
                    ->exists();

                $priceQuery->where('is_active', true);

                if ($hasSpecialPrice) {
                    $priceQuery->where('price_type', 'special_date')
                        ->where('special_date', $date);
                } else {
                    $priceQuery->where('price_type', 'normal');
                }

                $priceQuery->orderBy('court_type')
                    ->orderBy('start_time');
            },
            'reviews' => function ($query) {
                $query->with('customer')
                    ->where('status', 'approved')
                    ->orderBy('created_at', 'desc')
                    ->take(10);
            },
        ]);

        $bookings = CourtBooking::getBookingFields($date, ['pending', 'confirmed'], [
            'id',
            'branch_id',
            'court_id',
            'start_time',
            'end_time'
        ])->where('branch_id', $branch->id);

        $formattedCourts = $branch->courts->where('is_active', 1)->map(function ($court) use ($bookings) {
            $courtBookings = $bookings->where('court_id', $court->id);
            $bookedSlots = [];

            foreach ($courtBookings as $booking) {
                $startTime = Carbon::parse($booking->start_time)->format('H:i');
                $endTime = Carbon::parse($booking->end_time)->format('H:i');
                $bookedSlots[] = [
                    'start_time' => $startTime,
                    'end_time' => $endTime
                ];
            }

            return [
                'id' => $court->id,
                'name' => $court->name,
                'type' => $court->type,
                'booked_slots' => $bookedSlots
            ];
        })->values();

        $formattedPrices = $branch->prices->map(function ($price) {
            return [
                'id' => $price->id,
                'court_type' => $price->court_type,
                'price_per_hour' => $price->price_per_hour,
                'member_price_per_hour' => $price->member_price_per_hour,
                'start_time' => Carbon::parse($price->start_time)->format('H:i'),
                'end_time' => Carbon::parse($price->end_time)->format('H:i'),
                'day_of_week' => $price->day_of_week,
                'price_type' => $price->price_type,
                'special_date' => $price->special_date
            ];
        });

        $formattedReviews = $branch->reviews->map(function ($review) {
            $customer = $review->customer;
            $nameParts = explode(' ', $customer->name);
            $initials = '';

            if (count($nameParts) > 1) {
                $initials = mb_substr($nameParts[0], 0, 1) . mb_substr($nameParts[count($nameParts) - 1], 0, 1);
            } else {
                $initials = mb_substr($customer->name, 0, 2);
            }

            return [
                'id' => $review->id,
                'initial' => strtoupper($initials),
                'name' => $customer->name,
                'date' => Carbon::parse($review->created_at)->format('d/m/Y'),
                'rating' => $review->rating,
                'content' => $review->content
            ];
        });

        $amenities = $branch->amenities->map(function ($amenity) {
            return [
                'id' => $amenity->id,
                'name' => $amenity->name,
                'icon' => $amenity->icon,
                'notes' => $amenity->pivot->notes
            ];
        });

        $images = $branch->images->map(function ($image) {
            return [
                'id' => $image->id,
                'image_url' => $image->image_url,
                'is_main' => $image->is_main
            ];
        });

        $mainImage = $images->firstWhere('is_main', true) ?: ($images->first() ?: null);

        return [
            'id' => $branch->id,
            'name' => $branch->name,
            'description' => $branch->description,
            'address' => $branch->address,
            'full_address' => implode(', ', array_filter([
                $branch->address,
                $branch->ward_name,
                $branch->district_name,
                $branch->province_name
            ])),
            'contact_phone' => $branch->contact_phone,
            'contact_email' => $branch->contact_email,
            'opening_hour' => Carbon::parse($branch->opening_hour)->format('H:i'),
            'closing_hour' => Carbon::parse($branch->closing_hour)->format('H:i'),
            'latitude' => $branch->latitude,
            'longitude' => $branch->longitude,
            'status' => $branch->status,
            'main_image' => $mainImage ? $mainImage['image_url'] : null,
            'images' => $images,
            'courts' => $formattedCourts,
            'prices' => $formattedPrices,
            'reviews' => $formattedReviews,
            'review_count' => $branch->getReviewCount(),
            'average_rating' => $branch->getAverageRating(),
            'amenities' => $amenities
        ];
    }

    public function booking($id)
    {
        $branch = Branch::with(['business', 'images', 'courts'])->findOrFail($id);

        $branch->full_address = implode(', ', array_filter([
            $branch->address,
            $branch->ward_name,
            $branch->district_name,
            $branch->province_name
        ]));

        return Inertia::render('Customers/BranchBooking/Calendar', [
            'branch' => $branch,
            'courts' => $branch->courts,
            'currentDate' => now()->format('Y-m-d')
        ]);
    }

    public function getBookingsByDate($id, Request $request)
    {
        $date = $request->input('date', Carbon::today()->format('Y-m-d'));
        $timeInterval = 30;
        $timeInterval = intval($timeInterval);
        $branch = Branch::with(relations: ['courts', 'prices'])->findOrFail($id);


        $now = Carbon::now();
        // $expiredBookings = CourtBooking::where('status', '!=', 'cancelled')
        //     ->whereNotNull('payment_deadline')
        //     ->where('payment_deadline', '<', $now)
        //     ->get();


        // foreach ($expiredBookings as $booking) {
        //     $booking->update([
        //         'status' => 'cancelled',
        //         'cancelled_at' => $now,
        //         'cancellation_reason' => 'Payment due date'
        //     ]);
        // }


        $bookings = CourtBooking::getBookingFields($date, ['pending', 'confirmed'], ['id', 'branch_id', 'court_id', 'start_time', 'end_time', 'payment_deadline', 'status'])
            ->where('branch_id', $branch->id)
            ->filter(function ($booking) {

                return !$booking->payment_deadline || Carbon::parse($booking->payment_deadline)->isFuture();
            });

        $timeSlots = [];
        $courtTimeSlots = [];
        foreach ($branch->courts as $court) {
            $courtTimeSlots[$court->id] = [
                'court_id' => $court->id,
                'court_name' => $court->name,
                'court_type' => $court->type,
                'slots' => []
            ];
        }

        $opening = Carbon::parse($branch->opening_hour);
        $closing = Carbon::parse($branch->closing_hour);

        if ($opening->gt($closing)) {
            $closing->addDay();
        }

        while ($opening->lt($closing)) {
            $timeStr = $opening->format('H:i');
            $slotEndTime = $opening->copy()->addMinutes($timeInterval);

            if ($slotEndTime->gt($closing)) {
                $slotEndTime = $closing->copy();
            }

            $commonSlotIsAvailable = true;

            foreach ($branch->courts as $court) {
                $isAvailable = true;

                foreach ($bookings as $booking) {
                    if ($booking->court_id == $court->id) {
                        $bookingStart = Carbon::parse($booking->start_time);
                        $bookingEnd = Carbon::parse($booking->end_time);

                        if (
                            $bookingStart->format('H:i') < $slotEndTime->format('H:i') &&
                            $bookingEnd->format('H:i') > $timeStr
                        ) {
                            $isAvailable = false;
                            if ($commonSlotIsAvailable) {
                                $commonSlotIsAvailable = false;
                            }
                            break;
                        }
                    }
                }

                $courtTimeSlots[$court->id]['slots'][] = [
                    'time' => $timeStr,
                    'end_time' => $slotEndTime->format('H:i'),
                    'is_available' => $isAvailable
                ];
            }

            $timeSlots[] = [
                'time' => $timeStr,
                'end_time' => $slotEndTime->format('H:i'),
                'is_available' => $commonSlotIsAvailable
            ];

            $opening->addMinutes($timeInterval);
        }

        $prices = $branch->prices()
            ->where(function ($query) use ($date) {
                $query->where(function ($q) {
                    $q->where('price_type', 'normal')
                        ->where('is_active', true);
                })->orWhere(function ($q) use ($date) {
                    $q->where('price_type', 'special_date')
                        ->where('special_date', $date)
                        ->where('is_active', true);
                });
            })
            ->orderBy('start_time')
            ->get();

        $bookingsData = $bookings->map(function ($booking) {
            return [
                'id' => $booking->id,
                'court_id' => $booking->court_id,
                'start_time' => Carbon::parse($booking->start_time)->format('H:i'),
                'end_time' => Carbon::parse($booking->end_time)->format('H:i')
            ];
        });

        return response()->json([
            'timeSlots' => $timeSlots,
            'courtTimeSlots' => array_values($courtTimeSlots),
            'bookings' => $bookingsData,
            'prices' => $prices,
            'date' => $date,
            'opening_hour' => $branch->opening_hour,
            'closing_hour' => $branch->closing_hour,
            'timeInterval' => $timeInterval
        ]);
    }

    public function getPrice(Branch $branch)
    {
        $paramDate = request()->input('date', Carbon::today()->format('Y-m-d'));
        $prices = CourtPrice::getPriceListByBranchIdAndDate($branch->id, $paramDate);

        return response()->json([
            'prices' => $prices
        ]);
    }

}