<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\CourtPrice;
use App\Models\CourtService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Inertia\Inertia;
use App\Models\BranchSetting;

class CourtBookingController extends Controller
{
    /**
     * Display the court booking page.
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        // return Inertia::render('CourtBooking/CustomerBooking');
    }

    /**
     * Display the booking page for a specific court.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function show($id)
    {
        try {
            $court = Court::with('branch')->findOrFail($id);
            $date = request('date', now()->format('Y-m-d'));
            $availableSlots = $court->getAvailableTimeSlots($date);

            return Inertia::render('CourtBooking/Booking', [
                'court' => $court,
                'branch' => $court->branch,
                'date' => $date,
                'availableSlots' => $availableSlots,
                'auth' => [
                    'user' => Auth::user()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Court booking error: ' . $e->getMessage(), [
                'court_id' => $id,
                'date' => request('date'),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return Inertia::render('Error', [
                'status' => 500,
                'message' => 'There was an error loading the court booking page. Please try again later.'
            ]);
        }
    }

    /**
     * Store a newly created booking in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'court_id' => 'required|exists:courts,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'price' => 'required|numeric',
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'number_of_players' => 'nullable|integer|min:1|max:4',
            'additional_services' => 'nullable|array',
            'additional_services.*' => 'integer|exists:court_services,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $startDateTime = Carbon::parse($request->date . ' ' . $request->start_time);
        $endDateTime = Carbon::parse($request->date . ' ' . $request->end_time);

        $conflictingBookings = CourtBooking::where('court_id', $request->court_id)
            ->where('booking_date', $request->date)
            ->where(function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('start_time', '<=', $request->start_time)
                        ->where('end_time', '>', $request->start_time);
                })->orWhere(function ($q) use ($request) {
                    $q->where('start_time', '<', $request->end_time)
                        ->where('end_time', '>=', $request->end_time);
                })->orWhere(function ($q) use ($request) {
                    $q->where('start_time', '>=', $request->start_time)
                        ->where('end_time', '<=', $request->end_time);
                });
            })
            ->where('status', '!=', 'cancelled')
            ->count();

        if ($conflictingBookings > 0) {
            return redirect()->back()->withErrors([
                'time_conflict' => 'The selected time slot is no longer available. Please choose another time.'
            ])->withInput();
        }

        $court = Court::findOrFail($request->court_id);

        $weekday = strtolower(Carbon::parse($request->date)->format('l'));
        $start_time = $request->start_time;

        $price = CourtPrice::where('court_id', $request->court_id)
            ->where(function ($query) use ($weekday) {
                $query->where('day_of_week', $weekday)
                    ->orWhere('day_of_week', 'all');
            })
            ->where('start_time', '<=', $start_time)
            ->where('end_time', '>', $start_time)
            ->first();

        $correctPrice = $price ? $price->price : $court->price;

        $duration = $startDateTime->diffInMinutes($endDateTime) / 60;
        $totalCorrectPrice = $correctPrice * $duration;

        if (abs($request->price - $totalCorrectPrice) > ($totalCorrectPrice * 0.01)) {
            return redirect()->back()->withErrors([
                'price' => 'The price has changed. Please refresh and try again.'
            ])->withInput();
        }

        try {
            DB::beginTransaction();

            $booking = new CourtBooking();
            $booking->court_id = $request->court_id;
            $booking->booking_date = $request->date;
            $booking->start_time = $request->start_time;
            $booking->end_time = $request->end_time;
            $booking->total_price = $request->price;
            $booking->customer_name = $request->name;
            $booking->customer_phone = $request->phone;
            $booking->customer_email = $request->email;
            $booking->status = 'pending';
            $booking->reference_number = $this->generateReferenceNumber();
            $booking->number_of_players = $request->number_of_players ?? 2;

            if (Auth::check()) {
                $booking->user_id = Auth::id();
            }

            $booking->save();

            if (!empty($request->additional_services)) {
                $booking->services()->attach($request->additional_services);
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Your booking has been submitted successfully!',
                    'booking_id' => $booking->id,
                    'bookingReference' => $booking->reference_number
                ]);
            }

            return redirect()->route('customer.bookings.confirmation', $booking->id)
                ->with('success', 'Your booking has been submitted successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing your booking. Please try again.',
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()->withErrors([
                'error' => 'An error occurred while processing your booking. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Show booking confirmation page.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function confirmation($id)
    {
        $booking = CourtBooking::with('court.branch')->findOrFail($id);
        if (Auth::check() && Auth::id() != $booking->user_id && !$this->isAdmin()) {
            abort(403);
        }

        return Inertia::render('CourtBooking/BookingConfirmation', [
            'booking' => $booking
        ]);
    }

    /**
     * Display booking history for authenticated user.
     *
     * @return \Inertia\Response
     */
    public function history()
    {
        $bookings = CourtBooking::with('court.branch')
            ->where('user_id', Auth::id())
            ->orderBy('date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(10);

        return Inertia::render('CourtBooking/BookingHistory', [
            'bookings' => $bookings
        ]);
    }

    /**
     * Cancel a booking.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel($id)
    {
        $booking = CourtBooking::findOrFail($id);

        if (Auth::id() != $booking->user_id && !$this->isAdmin()) {
            abort(403);
        }

        if ($booking->status === 'cancelled') {
            return redirect()->back()->with('error', 'This booking has already been cancelled.');
        }

        $bookingStartTime = Carbon::parse($booking->date . ' ' . $booking->start_time);
        $now = Carbon::now();

        if ($now->diffInHours($bookingStartTime) < 24) {
            return redirect()->back()->with('error', 'Bookings can only be cancelled at least 24 hours before the start time.');
        }

        $booking->status = 'cancelled';
        $booking->cancelled_at = now();
        $booking->cancellation_reason = 'Cancelled by customer';
        $booking->save();

        return redirect()->back()->with('success', 'Your booking has been cancelled successfully.');
    }

    /**
     * Generate a unique reference number for booking.
     *
     * @return string
     */
    private function generateReferenceNumber()
    {
        $prefix = 'PB'; 
        $timestamp = Carbon::now()->format('YmdHis');
        $random = rand(100, 999);

        return $prefix . $timestamp . $random;
    }

    /**
     * Check if the current user is an admin.
     *
     * @return bool
     */
    private function isAdmin()
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        $roles = $user->roles ?? collect();
        $roleNames = $roles->pluck('name')->toArray();

        return in_array('admin', $roleNames) || in_array('super-admin', $roleNames);
    }
}