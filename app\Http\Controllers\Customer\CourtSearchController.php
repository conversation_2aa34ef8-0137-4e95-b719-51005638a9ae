<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Court;
use App\Models\Branch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CourtSearchController extends Controller
{
    /**
     * Display the branch search page.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $date = $request->input('date', Carbon::today()->format('Y-m-d'));
        $query = Branch::with(['courts', 'business', 'images'])
            ->where('status', 'active');
        if ($request->filled('location')) {
            $searchTerm = $request->input('location');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('address', 'like', "%{$searchTerm}%")
                    ->orWhere('province_name', 'like', "%{$searchTerm}%")
                    ->orWhere('district_name', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('province')) {
            $query->where('province_id', $request->input('province'));
        }

        if ($request->filled('district')) {
            $query->where('district_id', $request->input('district'));
        }

        $branches = $query->get();
        foreach ($branches as $branch) {
            $branch->courts = $branch->courts->where('status', 'active');
            if ($branch->courts->isEmpty()) {
                continue;
            }
            $minPrice = PHP_FLOAT_MAX;
            $maxPrice = 0;

            foreach ($branch->courts as $court) {
                $availableSlots = $court->getAvailableTimeSlots($date);
                $court->available_slots = $availableSlots;
                if ($availableSlots->isNotEmpty()) {
                    foreach ($availableSlots as $slot) {
                        if (isset($slot['price']) && $slot['price'] < $minPrice) {
                            $minPrice = $slot['price'];
                        }
                        if (isset($slot['price']) && $slot['price'] > $maxPrice) {
                            $maxPrice = $slot['price'];
                        }
                    }
                }
                if ($court->price_per_hour < $minPrice) {
                    $minPrice = $court->price_per_hour;
                }
                if ($court->price_per_hour > $maxPrice) {
                    $maxPrice = $court->price_per_hour;
                }
                $court->location = $branch->district_name . ', ' . $branch->province_name;
            }
            if ($minPrice < PHP_FLOAT_MAX) {
                $branch->min_price = $minPrice;
            } else {
                $branch->min_price = 0;
            }
            $branch->max_price = $maxPrice > 0 ? $maxPrice : 0;
            if ($branch->min_price < $branch->max_price) {
                $branch->price_range = number_format($branch->min_price, 0, ',', '.') . 'đ - ' .
                    number_format($branch->max_price, 0, ',', '.') . 'đ';
            } else {
                $branch->price_range = number_format($branch->min_price, 0, ',', '.') . 'đ';
            }
            $mainImage = $branch->images->firstWhere('is_main', true);
            $branch->main_image_url = $mainImage ? $mainImage->image_url : null;
            if ($request->filled('time')) {
                $requestedTime = $request->input('time');
                foreach ($branch->courts as $court) {
                    $court->has_requested_slot = $court->available_slots->contains(function ($slot) use ($requestedTime) {
                        return $slot['start_time'] == $requestedTime && $slot['available'] == true;
                    });
                }

                $branch->courts = $branch->courts->filter(function ($court) {
                    return $court->has_requested_slot;
                });
            }

            if ($request->filled('price_min') || $request->filled('price_max')) {
                $min = $request->input('price_min', 0);
                $max = $request->input('price_max', PHP_FLOAT_MAX);

                $branch->courts = $branch->courts->filter(function ($court) use ($min, $max) {
                    return $court->price_per_hour >= $min && $court->price_per_hour <= $max;
                });
            }

            if ($request->filled('type')) {
                $type = $request->input('type');
                $branch->courts = $branch->courts->filter(function ($court) use ($type) {
                    return $court->type == $type;
                });
            }

            if ($request->has('indoor') && $request->input('indoor') !== '') {
                $isIndoor = $request->input('indoor') == "1";
                $branch->courts = $branch->courts->filter(function ($court) use ($isIndoor) {
                    return $court->indoor == $isIndoor;
                });
            }
        }

        $branches = $branches->filter(function ($branch) {
            return $branch->courts->isNotEmpty();
        })->values();

        $provinces = Branch::select('province_id', 'province_name')
            ->whereNotNull('province_name')
            ->distinct()
            ->get();

        $districts = Branch::select('district_id', 'district_name', 'province_id')
            ->whereNotNull('district_name')
            ->distinct()
            ->get();

        return Inertia::render('Customers/CourtSearch/index', [
            'branches' => $branches,
            'provinces' => $provinces,
            'districts' => $districts,
            'filters' => [
                'date' => $date,
                'time' => $request->input('time'),
                'province' => $request->input('province'),
                'district' => $request->input('district'),
                'price_min' => $request->input('price_min'),
                'price_max' => $request->input('price_max'),
                'type' => $request->input('type'),
                'indoor' => $request->input('indoor'),
                'location' => $request->input('location'),
            ],
            'date' => $date,
        ]);
    }

    /**
     * Get available time slots for a specific court on a specific date.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableSlots(Request $request, $id)
    {
        $court = Court::findOrFail($id);
        $date = $request->input('date', Carbon::today()->format('Y-m-d'));
        $slots = $court->getAvailableTimeSlots($date);

        // Check if there are any bookings for this court on this date
        $hasBookings = \App\Models\CourtBooking::where('court_id', $court->id)
            ->whereDate('booking_date', $date)
            ->where('status', '!=', 'cancelled')
            ->exists();

        // If there are no bookings, mark all slots as available
        if (!$hasBookings) {
            $slots = $slots->map(function ($slot) {
                $slot['available'] = true;
                return $slot;
            });
        }

        $slotsWithDetails = $slots->map(function ($slot) use ($court, $date) {
            return [
                'start_time' => $slot['start_time'],
                'end_time' => $slot['end_time'],
                'price' => $slot['price'],
                'available' => $slot['available'],
                'formatted_price' => number_format($slot['price'], 0, ',', '.') . 'đ',
            ];
        });

        return response()->json([
            'slots' => $slotsWithDetails,
            'court_id' => $court->id,
            'court_name' => $court->name,
            'court_type' => $court->type,
            'branch_name' => $court->branch->name,
            'business_name' => $court->branch->business->name,
            'date' => $date,
        ]);
    }
}