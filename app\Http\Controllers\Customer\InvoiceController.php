<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Customer;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;

class InvoiceController extends Controller
{
    /**
     * View invoice for a booking
     *
     * @param string $referenceNumber
     * @return \Illuminate\Http\Response
     */
    public function viewInvoice($referenceNumber)
    {
        try {
            // Get all bookings with the same reference number
            $allBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->with(['court', 'branch', 'services', 'customer'])
                ->get();

            if ($allBookings->isEmpty()) {
                return redirect()->back()->with('flash.error', 'Đặt sân không tồn tại');
            }

            // Use the first booking as the main booking reference
            $booking = $allBookings->first();

            // Get staff information if booking type is offline
            $staff = null;
            if ($booking->booking_type === 'offline' && !empty($booking->user_id)) {
                $staff = \App\Models\User::find($booking->user_id);
            }

            // Get detailed branch information
            $branch = $booking->branch;
            $branchAddress = implode(', ', array_filter([
                $branch->address,
                $branch->ward_name,
                $branch->district_name,
                $branch->province_name
            ]));

            // Check if user has permission to view this booking's invoice
            $user = Auth::user();
            if ($user) {
                $customer = \App\Models\Customer::where('user_id', $user->id)->first();
                if ($customer && $booking->customer_id !== $customer->id) {
                    return redirect()->back()->with('flash.error', 'Bạn không có quyền xem hóa đơn này');
                }
            }

            // Get payments for this booking reference
            $payments = Payment::where('booking_reference', $referenceNumber)
                ->orderBy('created_at', 'desc')
                ->get();

            // Calculate total price of all bookings
            $totalBookingPrice = $allBookings->sum('total_price');

            // Calculate total paid amount
            $totalPaid = $payments
                ->where('status', 'completed')
                ->sum('amount');

            // Calculate the remaining balance
            $remainingBalance = max(0, $totalBookingPrice - $totalPaid);

            // Create courts array for all court bookings
            $courts = [];
            foreach ($allBookings as $courtBooking) {
                $courts[] = [
                    'court_name' => $courtBooking->court->name,
                    'start_time' => $courtBooking->start_time,
                    'end_time' => $courtBooking->end_time,
                    'duration' => $courtBooking->duration,
                    'price_per_hour' => $courtBooking->price_per_hour,
                    'total_price' => $courtBooking->total_price
                ];
            }

            // Prepare data for the PDF
            $data = [
                'booking' => $booking,
                'allBookings' => $allBookings,
                'branch' => $branch,
                'branchAddress' => $branchAddress,
                'business' => $branch->business,
                'courts' => $courts,
                'payments' => $payments,
                'total_price' => $totalBookingPrice,
                'total_paid' => $totalPaid,
                'remaining_balance' => $remainingBalance,
                'payment_status' => $totalPaid >= $totalBookingPrice ? 'paid' :
                    ($totalPaid > 0 ? 'partial' : 'unpaid'),
                'date' => now()->format('d/m/Y'),
                'invoice_number' => $referenceNumber,
                'staff' => $staff
            ];

            // Generate PDF
            $pdf = PDF::loadView('pdfs.invoice', $data);

            // Configure paper size for 45mm receipt width (convert mm to points where 1mm = 2.83 points)
            $pdf->setPaper([0, 0, 127.4, 1000], 'portrait');

            // Set filename
            $filename = "invoice-{$referenceNumber}.pdf";

            // Display PDF in browser
            return $pdf->stream($filename);

        } catch (\Exception $e) {
            Log::error('Error generating invoice: ' . $e->getMessage(), [
                'reference_number' => $referenceNumber,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with('flash.error', 'Đã xảy ra lỗi khi tạo hóa đơn. Vui lòng thử lại sau.');
        }
    }
}