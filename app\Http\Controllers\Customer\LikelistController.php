<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Likelist;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LikelistController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $likelists = Likelist::with('branch')
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $likelists
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if the likelist entry already exists
        $existingLike = Likelist::where('user_id', $user->id)
            ->where('branch_id', $request->branch_id)
            ->first();

        if ($existingLike) {
            // Toggle the is_active status if it already exists
            $existingLike->is_active = !$existingLike->is_active;
            $existingLike->save();

            return response()->json([
                'success' => true,
                'message' => $existingLike->is_active ? 'Added to favorites' : 'Removed from favorites',
                'data' => $existingLike
            ]);
        }

        // Create new likelist entry
        $likelist = Likelist::create([
            'user_id' => $user->id,
            'branch_id' => $request->branch_id,
            'is_active' => true
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Added to favorites',
            'data' => $likelist
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $likelist = Likelist::with('branch')
            ->where('user_id', $user->id)
            ->where('branch_id', $id)
            ->first();

        if (!$likelist) {
            return response()->json([
                'success' => false,
                'message' => 'Not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $likelist
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $likelist = Likelist::where('user_id', $user->id)
            ->where('branch_id', $id)
            ->first();

        if (!$likelist) {
            return response()->json([
                'success' => false,
                'message' => 'Not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $likelist->is_active = $request->is_active;
        $likelist->save();

        return response()->json([
            'success' => true,
            'message' => $request->is_active ? 'Added to favorites' : 'Removed from favorites',
            'data' => $likelist
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $likelist = Likelist::where('user_id', $user->id)
            ->where('branch_id', $id)
            ->first();

        if (!$likelist) {
            return response()->json([
                'success' => false,
                'message' => 'Not found'
            ], 404);
        }

        // Soft delete by setting is_active to false
        $likelist->is_active = false;
        $likelist->save();

        return response()->json([
            'success' => true,
            'message' => 'Removed from favorites'
        ]);
    }

    /**
     * Check if a branch is in the user's likelist.
     */
    public function check(string $id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $isLiked = Likelist::where('user_id', $user->id)
            ->where('branch_id', $id)
            ->where('is_active', true)
            ->exists();

        return response()->json([
            'success' => true,
            'is_liked' => $isLiked
        ]);
    }
}
