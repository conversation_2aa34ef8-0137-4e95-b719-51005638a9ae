<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\BusinessSetting;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Services\BookingEventService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use App\Models\CourtBooking as Booking;
use App\Services\MailService;
use App\Mail\PaymentConfirmation;
use App\Models\Review;
use Illuminate\Support\Facades\DB; // Added DB Facade

class PaymentController extends Controller
{
    /**
     * Get all enabled payment methods for a specific business
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentMethods(Request $request)
    {
        try {
            $businessId = null;
            $referenceNumber = $request->query('reference_number');

            if ($referenceNumber) {
                Log::info("Fetching payment methods for reference number: {$referenceNumber}");

                $booking = \App\Models\CourtBooking::where('reference_number', $referenceNumber)
                    ->with('branch')
                    ->first();

                if (!$booking) {
                    Log::warning("Booking not found for reference number: {$referenceNumber}");
                    return response()->json([
                        'success' => false,
                        'message' => 'Không tìm thấy thông tin đặt sân.'
                    ], 404);
                }

                if (!$booking->branch) {
                    Log::warning("Branch not found for booking: {$booking->id}");
                    return response()->json([
                        'success' => false,
                        'message' => 'Không tìm thấy thông tin chi nhánh.'
                    ], 404);
                }

                $businessId = $booking->branch->business_id;
                Log::info("Using business ID {$businessId} from branch {$booking->branch->id}");
            } else {
                $businessId = $request->query('business_id', 1);
                Log::info("Using business ID {$businessId} from request");
            }


            $bankSettings = \App\Models\BusinessSetting::getBankSettings($businessId);
            $momoSettings = \App\Models\BusinessSetting::getMomoSettings($businessId);
            $vnpaySettings = \App\Models\BusinessSetting::getVnPaySettings($businessId);

            $result = [];


            $methods = PaymentMethod::enabled()->orderBy('display_order')->get();

            foreach ($methods as $method) {

                if ($method->payment_code === 'bank_transfer') {
                    if (empty($bankSettings['accountNumber']) || empty($bankSettings['bankName'])) {
                        continue;
                    }

                    $method->custom_field1 = $bankSettings['accountNumber'] ?? '';
                    $method->custom_field2 = $bankSettings['bankName'] ?? '';
                    $method->custom_field3 = $bankSettings['accountName'] ?? '';
                    $method->custom_field4 = 'Thanh toán cho đơn hàng: [Mã đặt sân]';
                    $method->bank_details = [
                        'account_number' => $bankSettings['accountNumber'] ?? '',
                        'bank_name' => $bankSettings['bankName'] ?? '',
                        'account_name' => $bankSettings['accountName'] ?? '',
                        'bank_branch' => $bankSettings['bankBranch'] ?? '',
                        'description' => $bankSettings['description'] ?? '',
                    ];
                    $method->is_available = true;
                } else if ($method->payment_code === 'momo') {
                    $method->is_available = !empty($momoSettings['partnerCode']) &&
                        !empty($momoSettings['accessKey']) &&
                        !empty($momoSettings['secretKey']) &&
                        ($momoSettings['active'] ?? false);

                    if (!$method->is_available) {
                        continue;
                    }
                } else if ($method->payment_code === 'vnpay') {
                    $method->is_available = !empty($vnpaySettings['vnp_TmnCode']) &&
                        !empty($vnpaySettings['vnp_HashSecret']) &&
                        ($vnpaySettings['active'] ?? false);

                    if (!$method->is_available) {
                        continue;
                    }
                } else {

                    $specificSettings = \App\Models\BusinessSetting::getPaymentSettings(
                        $businessId,
                        $method->payment_code,
                        ['active' => false]
                    );

                    $method->is_available = ($specificSettings['active'] ?? false);

                    if (!$method->is_available) {
                        continue;
                    }
                }

                $result[] = $method;
            }

            $paymentMethods = collect($result)->sortBy('display_order')->values();

            Log::info("Returning " . count($paymentMethods) . " payment methods");

            return response()->json([
                'success' => true,
                'data' => $paymentMethods,
                'business_id' => $businessId,
                'reference_number' => $referenceNumber
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment methods: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching payment methods',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }

    /**
     * Process a payment
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function processPayment(Request $request)
    {
        try {
            $request->validate([
                'booking_reference' => 'required|string',
                'payment_method_id' => 'required|exists:payment_methods,id',
                'amount' => 'required|numeric|min:0',
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'nullable|email',
                'customer_phone' => 'required|string|max:20',
                'payment_details' => 'nullable|string',
                'notes' => 'nullable|string',
                'payment_proof' => 'nullable|file|image|max:5120',
            ]);

            $bookings = \App\Models\CourtBooking::where('reference_number', $request->booking_reference)
                ->with(['branch', 'branch.business'])
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin đặt sân.'
                ], 404);
            }

            $paymentMethod = PaymentMethod::findOrFail($request->payment_method_id);


            if (!$paymentMethod->is_enabled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phương thức thanh toán này đã bị vô hiệu hóa.'
                ], 400);
            }


            $primaryBooking = $bookings->first();
            $businessId = null;

            if ($primaryBooking->branch && $primaryBooking->branch->business_id) {
                $businessId = $primaryBooking->branch->business_id;
            }


            if ($paymentMethod->payment_code === 'bank_transfer' && $businessId) {
                $bankSettings = \App\Models\BusinessSetting::getBankSettings($businessId);

                if (empty($bankSettings['accountNumber']) || empty($bankSettings['bankName'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cấu hình thanh toán chuyển khoản chưa được thiết lập cho đơn vị này.'
                    ], 400);
                }
            }

            $payment = new Payment();
            $payment->booking_reference = $request->booking_reference;
            if ($bookings->isNotEmpty()) {
                $payment->court_booking_id = $bookings->first()->id;
            }
            $payment->payment_method_id = $request->payment_method_id;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = $request->amount;
            $payment->status = 'pending';
            $payment->customer_id = $request->user_id ?? null;
            $payment->user_id = $request->user_id ?? null;
            $payment->payment_details = $request->payment_details;
            $payment->notes = $request->notes;
            $payment->reference_number = 'P' . uniqid();
            $payment->transaction_date = now();

            if ($paymentMethod->payment_code === 'cod') {
                $payment->status = 'pending_confirmation';
                $payment->save();
                foreach ($bookings as $booking) {
                    $booking->payment_id = $payment->id;
                    $booking->payment_status = 'pending_confirmation';
                    $booking->save();
                }

                // Send email for COD payment
                $this->sendPaymentConfirmationEmail($payment, $bookings);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment recorded. Please pay when you arrive at the court.',
                    'payment' => [
                        'id' => $payment->id,
                        'reference_number' => $payment->reference_number,
                        'status' => $payment->status,
                        'amount' => $payment->amount,
                        'payment_method' => $payment->payment_method,
                    ]
                ]);
            }

            if ($paymentMethod->payment_code === 'bank_transfer') {

                $bankDetails = [];

                if ($businessId) {
                    $bankSettings = \App\Models\BusinessSetting::getBankSettings($businessId);


                    $paymentMethod->custom_field1 = $bankSettings['accountNumber'] ?? '';
                    $paymentMethod->custom_field2 = $bankSettings['bankName'] ?? '';
                    $paymentMethod->custom_field3 = $bankSettings['accountName'] ?? '';
                    $paymentMethod->custom_field4 = 'Thanh toán cho đơn hàng: [Mã đặt sân]';
                }

                if ($request->hasFile('payment_proof')) {
                    $proofFile = $request->file('payment_proof');
                    $proofFileName = 'payment_proof_' . $payment->reference_number . '.' . $proofFile->getClientOriginalExtension();
                    $proofPath = $proofFile->storeAs('payment_proofs', $proofFileName, 'public');
                    $payment->payment_details = json_encode([
                        'proof_file' => $proofPath,
                        'original_filename' => $proofFile->getClientOriginalName(),
                        'uploaded_at' => now()->format('Y-m-d H:i:s')
                    ]);
                    $payment->status = 'pending_approval';
                } else {
                    $payment->status = 'awaiting_payment';
                }

                $payment->save();

                foreach ($bookings as $booking) {
                    $booking->payment_id = $payment->id;
                    $booking->payment_status = $payment->status;
                    $booking->save();
                }

                // If payment proof was uploaded and status is pending_approval, send email notification
                if ($request->hasFile('payment_proof') && $payment->status === 'pending_approval') {
                    $this->sendPaymentConfirmationEmail($payment, $bookings);
                }

                $bankDetails = [
                    'account_number' => $paymentMethod->custom_field1 ?? '',
                    'bank_name' => $paymentMethod->custom_field2 ?? '',
                    'account_name' => $paymentMethod->custom_field3 ?? '',
                    'payment_note' => str_replace('[Mã đặt sân]', $payment->reference_number, $paymentMethod->custom_field4 ?? 'Mã đặt sân: {reference}')
                ];

                return response()->json([
                    'success' => true,
                    'message' => $request->hasFile('payment_proof')
                        ? 'Thanh toán đã được ghi nhận. Vui lòng đợi xác nhận từ quản trị viên.'
                        : 'Payment initiated. Please complete the bank transfer.',
                    'payment' => [
                        'id' => $payment->id,
                        'reference_number' => $payment->reference_number,
                        'status' => $payment->status,
                        'amount' => $payment->amount,
                        'payment_method' => $payment->payment_method,
                        'bank_details' => $bankDetails
                    ]
                ]);
            }


            $payment->save();
            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = 'pending';
                $booking->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment initiated. Redirecting to payment gateway.',
                'payment' => [
                    'id' => $payment->id,
                    'reference_number' => $payment->reference_number,
                    'status' => $payment->status,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your payment: ' . ($e->getMessage()),
            ], 500);
        }
    }

    /**
     * Get payment status
     *
     * @param string $referenceNumber
     * @return \Illuminate\Http\Response
     */
    public function getPaymentStatus($referenceNumber)
    {
        try {
            $payment = Payment::where('reference_number', $referenceNumber)
                ->with(['paymentMethod'])
                ->first();

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not found'
                ], 404);
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $payment->booking_reference)->get();

            $bookingsData = $bookings->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'reference_number' => $booking->reference_number,
                    'status' => $booking->status,
                    'total_price' => $booking->total_price
                ];
            });

            $totalPrice = $bookings->sum('total_price');

            return response()->json([
                'success' => true,
                'payment' => [
                    'id' => $payment->id,
                    'reference_number' => $payment->reference_number,
                    'status' => $payment->status,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'payment_details' => $payment->payment_details,
                    'created_at' => $payment->created_at,
                    'updated_at' => $payment->updated_at,
                    'transaction_date' => $payment->transaction_date,
                    'bookings' => $bookingsData,
                    'total_price' => $totalPrice
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment status: ' . $e->getMessage());

            return redirect()->back()->withErrors([
                'payment_error' => 'An error occurred while fetching payment status'
            ]);
        }
    }

    /**
     * Display payment confirmation page
     *
     * @param string $referenceNumber
     * @return \Inertia\Response
     */
    public function confirmation($referenceNumber, Request $request)
    {
        try {
            $isMomoRedirect = $request->has('resultCode') && $request->has('orderId');
            $isVnpayRedirect = $request->has('vnp_ResponseCode') && $request->has('vnp_TxnRef');
            $resultCode = $request->input('resultCode');
            $vnp_ResponseCode = $request->input('vnp_ResponseCode');

            if ($isMomoRedirect) {
                Log::info('MoMo redirect received', $request->all());

                if ($resultCode !== '0') {
                    $momoMessage = $request->input('message', 'Unknown error');
                    $extraData = $request->input('extraData');
                    $orderId = $request->input('orderId');
                    $transId = $request->input('transId', 'unknown');


                    Log::info('MoMo redirect data', [
                        'resultCode' => $resultCode,
                        'message' => $momoMessage,
                        'extraData' => $extraData,
                        'orderId' => $orderId,
                        'transId' => $transId
                    ]);

                    if ($extraData) {
                        $decodedExtraData = json_decode(base64_decode($extraData), true);
                        Log::info('Decoded extraData', ['data' => $decodedExtraData]);

                        $bookingReference = $decodedExtraData['booking_reference'] ?? null;
                        $errorMessage = "Thanh toán thất bại: " . $momoMessage;


                        Log::error('MoMo payment failed', [
                            'resultCode' => $resultCode,
                            'message' => $momoMessage,
                            'bookingReference' => $bookingReference,
                            'transId' => $transId
                        ]);


                        session()->flash('payment_error', $errorMessage);
                        session()->flash('payment_error_code', $resultCode);


                        return redirect()->route('customer.payments.failed', [
                            'reference' => $transId,
                            'provider' => 'momo',
                            'code' => $resultCode,
                            'message' => $momoMessage,
                            'booking' => $bookingReference
                        ]);
                    }
                }

                $waitCount = 0;
                $paymentFound = false;

                while ($waitCount < 3 && !$paymentFound) {
                    $payment = Payment::where('reference_number', $referenceNumber)
                        ->with(['paymentMethod'])
                        ->first();

                    if ($payment) {
                        $paymentFound = true;
                        break;
                    }

                    sleep(1);
                    $waitCount++;
                }

                if (!$paymentFound) {
                    $extraData = $request->input('extraData');
                    $decodedData = [];

                    if ($extraData) {
                        $decodedData = json_decode(base64_decode($extraData), true) ?? [];
                    }

                    $bookingReference = $decodedData['booking_reference'] ?? null;
                    $customerEmail = $decodedData['customer_email'] ?? null;

                    if ($bookingReference && $customerEmail) {
                        session(['verified_booking_' . $bookingReference => $customerEmail]);
                        session(['verified_payment_' . $referenceNumber => $customerEmail]);
                        session(['momo_payment_pending' => true]);

                        $this->createTemporaryMomoPayment($referenceNumber, $bookingReference, $request->all());
                    }
                }
            } elseif ($isVnpayRedirect) {
                Log::info('VNPay redirect received', $request->all());

                if ($vnp_ResponseCode != '00') {
                    $vnpayMessage = 'Thanh toán không thành công: Mã lỗi ' . $vnp_ResponseCode;
                    $sessionData = session('vnpay_payment_request_' . $referenceNumber);
                    $bookingReference = $sessionData['booking_reference'] ?? null;

                    if ($bookingReference) {
                        session(['vnpay_payment_failed' => true]);
                        session(['vnpay_payment_message' => $vnpayMessage]);

                        return redirect()->route('customer.bookings.details', [
                            'referenceNumber' => $bookingReference,
                            'fromVnpay' => true
                        ]);
                    }

                    return redirect()->route('customer.branch.booking', ['id' => 1])->withErrors([
                        'payment_error' => $vnpayMessage
                    ]);
                }

                $waitCount = 0;
                $paymentFound = false;

                while ($waitCount < 3 && !$paymentFound) {
                    $payment = Payment::where('reference_number', $referenceNumber)
                        ->with(['paymentMethod'])
                        ->first();

                    if ($payment) {
                        $paymentFound = true;
                        break;
                    }

                    sleep(1);
                    $waitCount++;
                }

                if (!$paymentFound) {
                    $sessionData = session('vnpay_payment_request_' . $referenceNumber);
                    $bookingReference = $sessionData['booking_reference'] ?? null;
                    $extraData = json_decode($sessionData['extra_data'] ?? '{}', true);
                    $customerEmail = $extraData['customer_email'] ?? null;

                    if ($bookingReference && $customerEmail) {
                        session(['verified_booking_' . $bookingReference => $customerEmail]);
                        session(['verified_payment_' . $referenceNumber => $customerEmail]);
                        session(['vnpay_payment_pending' => true]);

                        $this->createTemporaryVnpayPayment($referenceNumber, $bookingReference, $request->all());
                    }
                }
            }

            $payment = Payment::where('reference_number', $referenceNumber)
                ->with(['paymentMethod'])
                ->first();

            if (!$payment) {
                if ($isMomoRedirect) {
                    $extraData = $request->input('extraData');
                    $decodedData = [];

                    if ($extraData) {
                        $decodedData = json_decode(base64_decode($extraData), true) ?? [];
                    }

                    $bookingReference = $decodedData['booking_reference'] ?? null;

                    if ($bookingReference) {
                        return redirect()->route('customer.bookings.details', [
                            'referenceNumber' => $bookingReference,
                            'fromMomo' => true
                        ]);
                    }
                } elseif ($isVnpayRedirect) {
                    $sessionData = session('vnpay_payment_request_' . $referenceNumber);
                    $bookingReference = $sessionData['booking_reference'] ?? null;

                    if ($bookingReference) {
                        return redirect()->route('customer.bookings.details', [
                            'referenceNumber' => $bookingReference,
                            'fromVnpay' => true
                        ]);
                    }
                }

                throw new \Exception('Không tìm thấy thông tin thanh toán.');
            }

            $verifiedEmail = session('verified_payment_' . $referenceNumber);
            $hasVerified = !empty($verifiedEmail);

            $bookings = \App\Models\CourtBooking::where('reference_number', $payment->booking_reference)
                ->with(['court', 'court.branch'])
                ->get();

            if ($bookings->isEmpty()) {
                throw new \Exception('Không tìm thấy thông tin đặt sân.');
            }

            if (!$hasVerified) {
                return Inertia::render('Customers/Payment/VerifyBooking', [
                    'needsVerification' => true,
                    'referenceNumber' => $payment->booking_reference,
                    'paymentReferenceNumber' => $referenceNumber
                ]);
            }

            $totalPrice = $bookings->sum('total_price');

            $primaryBooking = $bookings->first();

            $momoSuccess = $isMomoRedirect && $resultCode == '0';
            $vnpaySuccess = $isVnpayRedirect && $vnp_ResponseCode == '00';

            return Inertia::render('Customers/Payment/Confirmation', [
                'payment' => $payment,
                'bookings' => $bookings,
                'primaryBooking' => $primaryBooking,
                'totalPrice' => $totalPrice,
                'momoSuccess' => $momoSuccess,
                'vnpaySuccess' => $vnpaySuccess
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment confirmation: ' . $e->getMessage());

            return redirect()->route('customer.branch.booking', ['id' => 1])->withErrors([
                'payment_error' => 'Không tìm thấy thông tin thanh toán: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create temporary payment record for MoMo redirects if IPN hasn't created it yet
     */
    private function createTemporaryMomoPayment($referenceNumber, $bookingReference, $momoData)
    {
        try {
            $paymentExists = Payment::where('reference_number', $referenceNumber)->exists();

            if ($paymentExists) {
                return;
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $bookingReference)->get();

            if ($bookings->isEmpty()) {
                return;
            }

            $paymentMethodId = null;
            $extraData = $momoData['extraData'] ?? '';

            if ($extraData) {
                $decodedData = json_decode(base64_decode($extraData), true) ?? [];
                $paymentMethodId = $decodedData['payment_method_id'] ?? null;
            }

            if (!$paymentMethodId) {
                $paymentMethod = PaymentMethod::where('payment_code', 'momo')->where('is_enabled', true)->first();

                if (!$paymentMethod) {
                    return;
                }

                $paymentMethodId = $paymentMethod->id;
            } else {
                $paymentMethod = PaymentMethod::find($paymentMethodId);

                if (!$paymentMethod) {
                    return;
                }
            }

            $payment = new Payment();
            $payment->booking_reference = $bookingReference;
            $payment->court_booking_id = $bookings->first()->id;
            $payment->payment_method_id = $paymentMethodId;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = $momoData['amount'] ?? $bookings->sum('total_price');
            $payment->reference_number = $referenceNumber;
            $payment->transaction_date = now();

            $resultCode = $momoData['resultCode'] ?? '';

            if ($resultCode == '0') {
                $payment->status = 'completed';
                $statusMessage = 'Thanh toán thành công';
            } elseif ($resultCode == '1005') {
                $payment->status = 'processing';
                $statusMessage = 'Thanh toán đang xử lý';
            } else {
                $payment->status = 'failed';
                $statusMessage = 'Thanh toán thất bại: ' . ($momoData['message'] ?? 'Unknown error');
            }

            $payment->payment_details = json_encode([
                'payment_type' => 'momo',
                'momo_response' => $momoData,
                'transaction_id' => $momoData['transId'] ?? null,
                'payment_status' => $statusMessage,
                'result_code' => $resultCode,
                'note' => 'Created from redirect (may be temporary)'
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = $payment->status;
                $booking->save();
            }

            Log::info('Created temporary payment record from MoMo redirect', [
                'payment_id' => $payment->id,
                'reference_number' => $payment->reference_number
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating temporary MoMo payment: ' . $e->getMessage());
        }
    }

    /**
     * Display payment method selection page
     *
     * @param string $referenceNumber
     * @return \Inertia\Response
     */
    public function selectPaymentMethod($referenceNumber)
    {
        try {
            $bookingsCourt = \App\Models\CourtBooking::where('reference_number', $referenceNumber)
                ->with(['court', 'court.branch'])
                ->get();

            if ($bookingsCourt->isEmpty()) {
                throw new \Exception('Không tìm thấy thông tin đặt sân.');
            }

            // Clear any stale payment sessions for this booking when user returns to payment selection
            $this->clearStalePaymentSessions($referenceNumber);

            $hasPaymentInProgress = $bookingsCourt->contains(function ($booking) {
                return $booking->hasPaymentInProgress();
            });

            if ($hasPaymentInProgress) {
                $payment = Payment::where('booking_reference', $referenceNumber)->first();

                if ($payment) {
                    return redirect()->route('customer.payments.confirmation', [
                        'referenceNumber' => $payment->reference_number
                    ]);
                }

                return redirect()->route('customer.branch.booking')->withErrors([
                    'payment_error' => 'Đơn hàng này đã được thanh toán hoặc đang chờ xử lý.'
                ]);
            }

            $totalPrice = $bookingsCourt->sum('total_price');


            $primaryBooking = $bookingsCourt->first();
            $paymentDeadline = null;

            if ($primaryBooking && $primaryBooking->payment_deadline) {
                $paymentDeadline = $primaryBooking->payment_deadline->format('Y-m-d H:i:s');
            }

            return Inertia::render('Customers/Payment/SelectPayment', [
                'bookingsCourt' => $bookingsCourt,
                'totalPrice' => $totalPrice,
                'referenceNumber' => $referenceNumber,
                'paymentDeadline' => $paymentDeadline
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting booking for payment selection: ' . $e->getMessage());
            return redirect()->route('customer.branch.booking')->withErrors([
                'booking_error' => 'Không tìm thấy thông tin đặt sân.'
            ]);
        }
    }

    /**
     * Display booking verification page
     *
     * @return \Inertia\Response
     */
    public function verifyBookingForm()
    {
        return Inertia::render('Customers/Payment/VerifyBooking');
    }

    /**
     * Verify booking by reference number and email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyBooking(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'reference_number' => 'required|string',
                'email' => 'required|email'
            ]);

            $bookings = \App\Models\CourtBooking::where('reference_number', $request->reference_number)
                ->where('customer_email', $request->email)
                ->with(['court', 'court.branch'])
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin đặt sân hoặc email không khớp.'
                ], 404);
            }

            $payment = Payment::where('booking_reference', $request->reference_number)
                ->with(['paymentMethod'])
                ->first();

            session(['verified_booking_' . $request->reference_number => $request->email]);

            if ($payment) {
                session(['verified_payment_' . $payment->reference_number => $request->email]);
            }

            $totalPrice = $bookings->sum('total_price');

            return response()->json([
                'success' => true,
                'bookings' => $bookings,
                'payment' => $payment,
                'totalPrice' => $totalPrice,
                'redirect_url' => route('customer.bookings.details', ['referenceNumber' => $request->reference_number])
            ]);
        } catch (\Exception $e) {
            Log::error('Error verifying booking: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xác minh đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display booking details page
     *
     * @param string $referenceNumber
     * @return \Inertia\Response
     */
    public function bookingDetails($referenceNumber, Request $request)
    {
        try {
            $fromMomoRedirect = $request->has('fromMomo') ||
                session('momo_payment_failed') ||
                session('momo_payment_pending') ||
                session()->has('momo_payment_message');

            $fromVnpayRedirect = $request->has('fromVnpay') ||
                session('vnpay_payment_failed') ||
                session('vnpay_payment_pending') ||
                session()->has('vnpay_payment_message');

            $verifiedEmail = session('verified_booking_' . $referenceNumber);
            $hasVerified = !empty($verifiedEmail) || $fromMomoRedirect || $fromVnpayRedirect;

            if (($fromMomoRedirect || $fromVnpayRedirect) && empty($verifiedEmail)) {
                $booking = \App\Models\CourtBooking::where('reference_number', $referenceNumber)
                    ->first();

                if ($booking && $booking->customer_email) {
                    session(['verified_booking_' . $referenceNumber => $booking->customer_email]);
                    $hasVerified = true;
                }
            }

            if (!$hasVerified) {
                return Inertia::render('Customers/Payment/VerifyBooking', [
                    'needsVerification' => true,
                    'referenceNumber' => $referenceNumber
                ]);
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $referenceNumber)
                ->with(['court', 'court.branch'])
                ->get();

            if ($bookings->isEmpty()) {
                throw new \Exception('Không tìm thấy thông tin đặt sân.');
            }

            $primaryBooking = $bookings->first();
            $review = Review::where('booking_reference', $referenceNumber)->first();
            $listPayments = Payment::where('booking_reference', $referenceNumber)
                ->with(['paymentMethod'])
                ->orderBy('created_at', 'desc')
                ->get();

            $totalPrice = $bookings->sum('total_price');
            $totalPaid = $listPayments
                ->where('status', 'completed')
                ->sum('amount');

            $remainingBalance = $totalPrice - $totalPaid;
            $paymentStatus = 'unpaid';
            if ($totalPaid >= $totalPrice) {
                $paymentStatus = 'paid';
            } elseif ($totalPaid > 0 && $totalPaid < $totalPrice) {
                $paymentStatus = 'partial';
            }

            $paymentMessage = null;

            if (session('momo_payment_failed')) {
                $paymentMessage = session('momo_payment_message', 'Thanh toán MoMo không thành công');
                session()->forget(['momo_payment_failed', 'momo_payment_message']);
            } elseif (session('momo_payment_pending')) {
                $paymentMessage = 'Thanh toán MoMo đang được xử lý. Chúng tôi sẽ cập nhật trạng thái đặt sân khi nhận được xác nhận.';
                session()->forget('momo_payment_pending');
            } elseif (session('vnpay_payment_failed')) {
                $paymentMessage = session('vnpay_payment_message', 'Thanh toán VNPay không thành công');
                session()->forget(['vnpay_payment_failed', 'vnpay_payment_message']);
            } elseif (session('vnpay_payment_pending')) {
                $paymentMessage = 'Thanh toán VNPay đang được xử lý. Chúng tôi sẽ cập nhật trạng thái đặt sân khi nhận được xác nhận.';
                session()->forget('vnpay_payment_pending');
            }

            $bookingEvent = BookingEventService::getFormattedTimeline($primaryBooking->reference_number);
            $primaryBooking['booking_events'] = $bookingEvent;
            $primaryBooking['review'] = $review;
            return Inertia::render('Customers/Booking/Details', [
                'booking' => $primaryBooking,
                'bookings' => $bookings,
                'listPayments' => $listPayments,
                'totalPrice' => $totalPrice,
                'totalPaid' => $totalPaid,
                'paymentStatus' => $paymentStatus,
                'momoPaymentMessage' => $paymentMessage,
                'fromMomoRedirect' => $fromMomoRedirect || $fromVnpayRedirect
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching booking details: ' . $e->getMessage());
            return redirect()->route('customer.branch.booking', ['id' => 1])->withErrors([
                'booking_error' => 'Không tìm thấy thông tin đặt sân: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create MoMo payment URL
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createMomoUrl(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'reference_number' => 'required|string',
                'amount' => 'required|numeric|min:1000|max:********',
                'payment_method_id' => 'required|exists:payment_methods,id'
            ]);

            $bookings = \App\Models\CourtBooking::where('reference_number', $request->reference_number)
                ->with(['court', 'court.branch', 'branch'])
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin đặt sân.'
                ], 404);
            }

            // Check if there's already a pending/processing payment for this booking
            $existingPayment = Payment::where('booking_reference', $request->reference_number)
                ->whereIn('status', ['pending', 'processing'])
                ->where('payment_method', 'like', '%momo%')
                ->where('created_at', '>', now()->subMinutes(30)) // Only check recent payments
                ->first();

            if ($existingPayment) {
                Log::warning('Attempted to create duplicate MoMo payment', [
                    'booking_reference' => $request->reference_number,
                    'existing_payment_id' => $existingPayment->id,
                    'existing_reference' => $existingPayment->reference_number
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Đã có giao dịch thanh toán đang được xử lý cho đơn đặt sân này. Vui lòng đợi hoặc liên hệ hỗ trợ.'
                ], 409);
            }

            $totalPrice = $bookings->sum('total_price');
            $primaryBooking = $bookings->first();

            $businessId = $primaryBooking->branch->business_id;

            $paymentMethod = PaymentMethod::findOrFail($request->payment_method_id);

            if ($paymentMethod->payment_code !== 'momo') {
                return response()->json([
                    'success' => false,
                    'message' => 'Phương thức thanh toán không hợp lệ.'
                ], 400);
            }

            $momoSettings = \App\Models\BusinessSetting::getMomoSettings($businessId);

            // Clear any existing session data for this booking
            $sessionKeys = collect(session()->all())
                ->keys()
                ->filter(fn($key) => str_starts_with($key, 'momo_payment_request_'))
                ->toArray();

            foreach ($sessionKeys as $key) {
                $sessionData = session($key);
                if (is_array($sessionData) && ($sessionData['booking_reference'] ?? null) === $request->reference_number) {
                    session()->forget($key);
                }
            }

            // Generate unique order ID with microseconds for better uniqueness
            $orderId = 'PB' . now()->format('YmdHis') . substr(microtime(), 2, 6) . rand(100, 999);

            $endpoint = $momoSettings['endPoint'] ?? 'https://test-payment.momo.vn/v2/gateway/api/create';
            $partnerCode = $momoSettings['partnerCode'];
            $accessKey = $momoSettings['accessKey'];
            $secretKey = $momoSettings['secretKey'];

            $orderInfo = $primaryBooking->reference_number;
            $redirectUrl = route('customer.payments.confirmation', ['referenceNumber' => $orderId]);
            $ipnUrl = route('customer.payments.momo-ipn');
            $amount = (string) $totalPrice;
            $requestId = time() . "";
            $requestType = "payWithMethod";
            $extraData = base64_encode(json_encode([
                'booking_reference' => $request->reference_number,
                'customer_name' => $primaryBooking->customer_name,
                'customer_email' => $primaryBooking->customer_email,
                'customer_phone' => $primaryBooking->customer_phone,
                'payment_method_id' => $request->payment_method_id,
                'business_id' => $businessId
            ]));

            $rawHash = "accessKey=" . $accessKey . "&amount=" . $amount . "&extraData=" . $extraData . "&ipnUrl=" . $ipnUrl . "&orderId=" . $orderId . "&orderInfo=" . $orderInfo . "&partnerCode=" . $partnerCode . "&redirectUrl=" . $redirectUrl . "&requestId=" . $requestId . "&requestType=" . $requestType;
            $signature = hash_hmac("sha256", $rawHash, $secretKey);

            $data = [
                'partnerCode' => $partnerCode,
                'partnerName' => "Đặt sân Pickleball",
                'storeId' => 'PickleballCourts',
                'requestId' => $requestId,
                'amount' => $amount,
                'orderId' => $orderId,
                'orderInfo' => $orderInfo,
                'redirectUrl' => $redirectUrl,
                'ipnUrl' => $ipnUrl,
                'lang' => 'vi',
                'extraData' => $extraData,
                'requestType' => $requestType,
                'signature' => $signature
            ];

            session([
                'momo_payment_request_' . $orderId => [
                    'booking_reference' => $request->reference_number,
                    'amount' => $request->amount,
                    'payment_method_id' => $request->payment_method_id,
                    'request_id' => $requestId,
                    'order_id' => $orderId,
                    'created_at' => now()->format('Y-m-d H:i:s'),
                    'business_id' => $businessId
                ]
            ]);

            Log::info('MoMo payment request created', [
                'order_id' => $orderId,
                'booking_reference' => $request->reference_number,
                'amount' => $request->amount,
                'business_id' => $businessId
            ]);

            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', $endpoint, [
                'json' => $data
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);

            if (isset($responseBody['payUrl'])) {
                return response()->json([
                    'success' => true,
                    'payUrl' => $responseBody['payUrl'],
                    'message' => 'Đã tạo URL thanh toán MoMo thành công.',
                    'orderId' => $orderId
                ]);
            } else {
                Log::error('MoMo payment URL creation failed', [
                    'order_id' => $orderId,
                    'response' => $responseBody
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Không thể tạo đường dẫn thanh toán MoMo.',
                    'error' => config('app.debug') ? $responseBody : 'Payment gateway error'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('MoMo payment URL creation error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo liên kết thanh toán MoMo: ' . ($e->getMessage()),
            ], 500);
        }
    }

    /**
     * Handle MoMo IPN (Instant Payment Notification)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function momoIpn(Request $request)
    {
        try {
            Log::info('MoMo IPN received', $request->all());

            $inputData = $request->all();
            $orderId = $inputData['orderId'] ?? null;
            $requestId = $inputData['requestId'] ?? null;
            $amount = $inputData['amount'] ?? 0;
            $orderInfo = $inputData['orderInfo'] ?? '';
            $partnerCode = $inputData['partnerCode'] ?? '';
            $resultCode = $inputData['resultCode'] ?? 99;
            $extraData = $inputData['extraData'] ?? '';

            if (!$orderId) {
                return response()->json(['message' => 'Invalid order ID'], 400);
            }

            $decodedExtraData = [];
            if ($extraData) {
                $decodedExtraData = json_decode(base64_decode($extraData), true) ?? [];
            }

            $existingPayment = Payment::where('reference_number', $orderId)->first();

            if ($existingPayment) {
                Log::warning('Duplicate MoMo IPN received for order ID: ' . $orderId);
                return response()->json(['message' => 'Payment already processed'], 200);
            }

            $paymentMethodId = $decodedExtraData['payment_method_id'] ?? null;
            $bookingReference = $decodedExtraData['booking_reference'] ?? null;

            if (!$paymentMethodId || !$bookingReference) {
                Log::error('Missing required data in MoMo IPN', [
                    'extraData' => $decodedExtraData,
                    'orderId' => $orderId
                ]);
                return response()->json(['message' => 'Missing required data'], 400);
            }

            $paymentMethod = PaymentMethod::find($paymentMethodId);

            if (!$paymentMethod) {
                return response()->json(['message' => 'Payment method not found'], 404);
            }

            if (app()->environment('production')) {
                $secretKey = $paymentMethod->secret_key;
                $rawHash = "accessKey=" . $inputData['accessKey'] . "&amount=" . $amount . "&extraData=" . $extraData . "&orderId=" . $orderId . "&orderInfo=" . $orderInfo . "&partnerCode=" . $partnerCode . "&requestId=" . $requestId . "&requestType=" . $inputData['requestType'];
                $signature = hash_hmac("sha256", $rawHash, $secretKey);

                if ($signature !== $inputData['signature']) {
                    Log::error('MoMo IPN signature verification failed', [
                        'received' => $inputData['signature'],
                        'calculated' => $signature
                    ]);
                    return response()->json(['message' => 'Invalid signature'], 400);
                }
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $bookingReference)->get();

            if ($bookings->isEmpty()) {
                Log::error('Bookings not found for reference number: ' . $bookingReference);
                return response()->json(['message' => 'Bookings not found'], 404);
            }

            $payment = new Payment();
            $payment->booking_reference = $bookingReference;
            $payment->court_booking_id = $bookings->first()->id;
            $payment->payment_method_id = $paymentMethodId;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = $amount;
            $payment->reference_number = $orderId;
            $payment->transaction_date = now();

            if ($resultCode == 0) {
                $payment->status = 'completed';
                $statusMessage = 'Thanh toán thành công';
            } elseif ($resultCode == 1005) {
                $payment->status = 'processing';
                $statusMessage = 'Thanh toán đang xử lý';
            } else {
                $payment->status = 'failed';
                $statusMessage = 'Thanh toán thất bại: ' . ($inputData['message'] ?? 'Unknown error');
            }

            $payment->payment_details = json_encode([
                'payment_type' => 'momo',
                'momo_response' => $inputData,
                'transaction_id' => $inputData['transId'] ?? null,
                'payment_status' => $statusMessage,
                'result_code' => $resultCode
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = $payment->status == 'completed' ? 'paid' : 'unpaid';
                $booking->save();
            }

            if ($payment->status === 'completed' && isset($decodedExtraData['customer_email'])) {
                session(['verified_payment_' . $orderId => $decodedExtraData['customer_email']]);
                session(['verified_booking_' . $bookingReference => $decodedExtraData['customer_email']]);

                // Send payment confirmation email
                $this->sendPaymentConfirmationEmail($payment, $bookings);
            }

            return response()->json([
                'message' => 'Payment processed: ' . $statusMessage
            ]);
        } catch (\Exception $e) {
            Log::error('MoMo IPN error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Internal server error',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }

    public function createVnpayUrl(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'reference_number' => 'required|string',
                'amount' => 'required|numeric|min:1000|max:********',
                'payment_method_id' => 'required|exists:payment_methods,id',
                'bank_code' => 'nullable|string'
            ]);

            $bookings = \App\Models\CourtBooking::where('reference_number', $request->reference_number)
                ->with(['court', 'court.branch', 'branch'])
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin đặt sân.'
                ], 404);
            }

            // Check if there's already a pending/processing payment for this booking
            $existingPayment = Payment::where('booking_reference', $request->reference_number)
                ->whereIn('status', ['pending', 'processing'])
                ->where('payment_method', 'like', '%vnpay%')
                ->where('created_at', '>', now()->subMinutes(30)) // Only check recent payments
                ->first();

            if ($existingPayment) {
                Log::warning('Attempted to create duplicate VNPay payment', [
                    'booking_reference' => $request->reference_number,
                    'existing_payment_id' => $existingPayment->id,
                    'existing_reference' => $existingPayment->reference_number
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Đã có giao dịch thanh toán đang được xử lý cho đơn đặt sân này. Vui lòng đợi hoặc liên hệ hỗ trợ.'
                ], 409);
            }

            $totalPrice = $bookings->sum('total_price');
            $primaryBooking = $bookings->first();

            $businessId = $primaryBooking->branch->business_id;

            $paymentMethod = PaymentMethod::findOrFail($request->payment_method_id);

            if ($paymentMethod->payment_code !== 'vnpay') {
                return response()->json([
                    'success' => false,
                    'message' => 'Phương thức thanh toán không hợp lệ.'
                ], 400);
            }

            $vnpaySettings = \App\Models\BusinessSetting::getVnPaySettings($businessId);

            // Clear any existing session data for this booking
            $sessionKeys = collect(session()->all())
                ->keys()
                ->filter(fn($key) => str_starts_with($key, 'vnpay_payment_request_'))
                ->toArray();

            foreach ($sessionKeys as $key) {
                $sessionData = session($key);
                if (is_array($sessionData) && ($sessionData['booking_reference'] ?? null) === $request->reference_number) {
                    session()->forget($key);
                }
            }

            $vnp_IpAddr = "*************";
            $vnp_Locale = 'vn';

            // Generate unique order ID with microseconds for better uniqueness
            $orderId = 'PB' . now()->format('YmdHis') . substr(microtime(), 2, 6) . rand(100, 999);
            $vnp_OrderInfo = $request->reference_number;
            $vnp_OrderType = 'billpayment';
            $vnp_ReturnUrl = route('customer.payments.confirmation', $orderId);
            $vnp_TxnRef = $orderId;

            $timezone = new \DateTimeZone('Asia/Ho_Chi_Minh');
            $currentDateTime = new \DateTime('now', $timezone);
            $vnp_CreateDate = $currentDateTime->format('YmdHis');

            $createDateTime = \DateTime::createFromFormat('YmdHis', $vnp_CreateDate, $timezone);
            $createDateTime->modify('+15 minutes');
            $vnp_ExpireDate = $createDateTime->format('YmdHis');

            $vnp_Url = $vnpaySettings['vnp_Url'] ?? 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html';
            $vnp_TmnCode = $vnpaySettings['vnp_TmnCode'];
            $vnp_HashSecret = $vnpaySettings['vnp_HashSecret'];

            $inputData = array(
                "vnp_Version" => $vnpaySettings['vnp_Version'] ?? "2.1.1",
                "vnp_TmnCode" => $vnp_TmnCode,
                "vnp_Amount" => $totalPrice * 100,
                "vnp_Command" => "pay",
                "vnp_CreateDate" => $vnp_CreateDate,
                "vnp_ExpireDate" => $vnp_ExpireDate,
                "vnp_CurrCode" => "VND",
                "vnp_IpAddr" => $vnp_IpAddr,
                "vnp_Locale" => $vnp_Locale,
                "vnp_OrderInfo" => $vnp_OrderInfo,
                "vnp_OrderType" => $vnp_OrderType,
                "vnp_ReturnUrl" => $vnp_ReturnUrl,
                "vnp_TxnRef" => $vnp_TxnRef
            );

            ksort($inputData);
            $query = "";
            $i = 0;
            $hashData = "";
            foreach ($inputData as $key => $value) {
                if ($i == 1) {
                    $hashData .= '&' . urlencode($key) . "=" . urlencode($value);
                } else {
                    $hashData .= urlencode($key) . "=" . urlencode($value);
                    $i = 1;
                }
                $query .= urlencode($key) . "=" . urlencode($value) . '&';
            }

            $query = rtrim($query, '&');
            $vnpSecureHash = hash_hmac('sha512', $hashData, $vnp_HashSecret);
            $vnp_Url = $vnp_Url . "?" . $query . '&vnp_SecureHash=' . $vnpSecureHash;

            $extraData = json_encode([
                'booking_reference' => $request->reference_number,
                'customer_name' => $primaryBooking->customer_name,
                'customer_email' => $primaryBooking->customer_email,
                'customer_phone' => $primaryBooking->customer_phone,
                'payment_method_id' => $request->payment_method_id,
                'business_id' => $businessId
            ]);

            session([
                'vnpay_payment_request_' . $vnp_TxnRef => [
                    'booking_reference' => $request->reference_number,
                    'amount' => $request->amount,
                    'payment_method_id' => $request->payment_method_id,
                    'txn_ref' => $vnp_TxnRef,
                    'created_at' => now()->format('Y-m-d H:i:s'),
                    'extra_data' => $extraData,
                    'business_id' => $businessId
                ]
            ]);

            Log::info('VNPay payment request created', [
                'txn_ref' => $vnp_TxnRef,
                'booking_reference' => $request->reference_number,
                'amount' => $request->amount,
                'business_id' => $businessId
            ]);

            return response()->json([
                'success' => true,
                'payUrl' => $vnp_Url,
                'message' => 'Đã tạo URL thanh toán VNPay thành công.',
                'orderId' => $vnp_TxnRef
            ]);

        } catch (\Exception $e) {
            Log::error('VNPay payment URL creation error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo liên kết thanh toán VNPay: ' . ($e->getMessage()),
            ], 500);
        }
    }

    public function vnpayIpn(Request $request)
    {
        try {
            Log::info('VNPay IPN received', $request->all());

            $inputData = $request->all();
            $vnp_TxnRef = $inputData['vnp_TxnRef'] ?? null;
            $vnp_Amount = $inputData['vnp_Amount'] ?? 0;
            $vnp_ResponseCode = $inputData['vnp_ResponseCode'] ?? '99';
            $vnp_TransactionStatus = $inputData['vnp_TransactionStatus'] ?? '99';
            $vnp_SecureHash = $inputData['vnp_SecureHash'] ?? '';
            $vnp_BankCode = $inputData['vnp_BankCode'] ?? '';

            if (!$vnp_TxnRef) {
                return response()->json(['message' => 'Invalid transaction reference'], 400);
            }

            $existingPayment = Payment::where('reference_number', $vnp_TxnRef)->first();

            if ($existingPayment) {
                Log::warning('Duplicate VNPay IPN received for transaction reference: ' . $vnp_TxnRef);
                return response()->json(['message' => 'Payment already processed'], 200);
            }

            $paymentSessionData = session('vnpay_payment_request_' . $vnp_TxnRef);

            if (!$paymentSessionData) {
                Log::error('Missing session data for VNPay transaction', [
                    'vnp_TxnRef' => $vnp_TxnRef
                ]);
                return response()->json(['message' => 'Missing payment session data'], 400);
            }

            $paymentMethodId = $paymentSessionData['payment_method_id'] ?? null;
            $bookingReference = $paymentSessionData['booking_reference'] ?? null;
            $extraData = $paymentSessionData['extra_data'] ?? null;

            if (!$paymentMethodId || !$bookingReference) {
                Log::error('Missing required data in VNPay IPN', [
                    'session_data' => $paymentSessionData,
                    'vnp_TxnRef' => $vnp_TxnRef
                ]);
                return response()->json(['message' => 'Missing required data'], 400);
            }

            $paymentMethod = PaymentMethod::find($paymentMethodId);

            if (!$paymentMethod) {
                return response()->json(['message' => 'Payment method not found'], 404);
            }

            if (app()->environment('production')) {
                $vnp_HashSecret = $paymentMethod->secret_key;
                $secureHash = $inputData['vnp_SecureHash'];

                unset($inputData['vnp_SecureHash']);
                ksort($inputData);

                $i = 0;
                $hashData = "";
                foreach ($inputData as $key => $value) {
                    if ($i == 1) {
                        $hashData .= '&' . urlencode($key) . "=" . urlencode($value);
                    } else {
                        $hashData .= urlencode($key) . "=" . urlencode($value);
                        $i = 1;
                    }
                }

                $calculatedHash = hash_hmac('sha512', $hashData, $vnp_HashSecret);

                if ($calculatedHash !== $secureHash) {
                    Log::error('VNPay IPN signature verification failed', [
                        'received' => $secureHash,
                        'calculated' => $calculatedHash
                    ]);
                    return response()->json(['message' => 'Invalid signature'], 400);
                }
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $bookingReference)->get();

            if ($bookings->isEmpty()) {
                Log::error('Bookings not found for reference number: ' . $bookingReference);
                return response()->json(['message' => 'Bookings not found'], 404);
            }

            $payment = new Payment();
            $payment->booking_reference = $bookingReference;
            $payment->court_booking_id = $bookings->first()->id;
            $payment->payment_method_id = $paymentMethodId;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = $vnp_Amount / 100;
            $payment->reference_number = $vnp_TxnRef;
            $payment->transaction_date = now();

            if ($vnp_ResponseCode == '00' && $vnp_TransactionStatus == '00') {
                $payment->status = 'completed';
                $statusMessage = 'Thanh toán thành công';
            } else {
                $payment->status = 'failed';
                $statusMessage = 'Thanh toán thất bại: Mã lỗi ' . $vnp_ResponseCode;
            }

            $payment->payment_details = json_encode([
                'payment_type' => 'vnpay',
                'vnpay_response' => $inputData,
                'bank_code' => $vnp_BankCode,
                'payment_status' => $statusMessage,
                'response_code' => $vnp_ResponseCode,
                'transaction_status' => $vnp_TransactionStatus
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = $payment->status;
                $booking->save();
            }

            $decodedData = json_decode($extraData, true) ?? [];
            $customerEmail = $decodedData['customer_email'] ?? null;

            if ($payment->status === 'completed' && $customerEmail) {
                session(['verified_payment_' . $vnp_TxnRef => $customerEmail]);
                session(['verified_booking_' . $bookingReference => $customerEmail]);

                // Send payment confirmation email
                $this->sendPaymentConfirmationEmail($payment, $bookings);
            }

            return response()->json([
                'message' => 'Payment processed: ' . $statusMessage,
                'RspCode' => '00',
                'Message' => 'Success'
            ]);

        } catch (\Exception $e) {
            Log::error('VNPay IPN error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Internal server error',
                'RspCode' => '99',
                'Message' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }

    private function createTemporaryVnpayPayment($reference_number, $booking_reference, $vnpay_data)
    {
        try {
            $paymentExists = Payment::where('reference_number', $reference_number)->exists();

            if ($paymentExists) {
                return;
            }

            $bookings = \App\Models\CourtBooking::where('reference_number', $booking_reference)->get();

            if ($bookings->isEmpty()) {
                return;
            }

            $sessionData = session('vnpay_payment_request_' . $reference_number);
            $paymentMethodId = $sessionData['payment_method_id'] ?? null;

            if (!$paymentMethodId) {
                $paymentMethod = PaymentMethod::where('payment_code', 'vnpay')->where('is_enabled', true)->first();

                if (!$paymentMethod) {
                    return;
                }

                $paymentMethodId = $paymentMethod->id;
            } else {
                $paymentMethod = PaymentMethod::find($paymentMethodId);

                if (!$paymentMethod) {
                    return;
                }
            }

            $payment = new Payment();
            $payment->booking_reference = $booking_reference;
            $payment->court_booking_id = $bookings->first()->id;
            $payment->payment_method_id = $paymentMethodId;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = ($vnpay_data['vnp_Amount'] ?? 0) / 100;
            $payment->reference_number = $reference_number;
            $payment->transaction_date = now();

            $responseCode = $vnpay_data['vnp_ResponseCode'] ?? '';
            $transactionStatus = $vnpay_data['vnp_TransactionStatus'] ?? '';

            if ($responseCode == '00' && $transactionStatus == '00') {
                $payment->status = 'completed';
                $statusMessage = 'Thanh toán thành công';
            } else {
                $payment->status = 'failed';
                $statusMessage = 'Thanh toán thất bại: Mã lỗi ' . $responseCode;
            }

            $payment->payment_details = json_encode([
                'payment_type' => 'vnpay',
                'vnpay_response' => $vnpay_data,
                'bank_code' => $vnpay_data['vnp_BankCode'] ?? null,
                'payment_status' => $statusMessage,
                'response_code' => $responseCode,
                'transaction_status' => $transactionStatus,
                'note' => 'Created from redirect (may be temporary)'
            ]);

            $payment->save();

            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = $payment->status;
                $booking->save();
            }

            Log::info('Created temporary payment record from VNPay redirect', [
                'payment_id' => $payment->id,
                'reference_number' => $payment->reference_number
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating temporary VNPay payment: ' . $e->getMessage());
        }
    }

    /**
     * Send payment confirmation email to customer
     *
     * @param Payment $payment
     * @param \Illuminate\Database\Eloquent\Collection|\App\Models\CourtBooking[] $bookings
     * @return bool
     */
    private function sendPaymentConfirmationEmail(Payment $payment, $bookings)
    {
        try {
            $primaryBooking = $bookings->first();

            if (!$primaryBooking) {
                Log::error('Cannot send payment confirmation email: No booking found', [
                    'payment_id' => $payment->id,
                    'booking_reference' => $payment->booking_reference
                ]);
                return false;
            }

            $businessId = null;
            if ($primaryBooking->branch && $primaryBooking->branch->business_id) {
                $businessId = $primaryBooking->branch->business_id;
            }

            $bookingsData = $bookings->map(function ($booking) {
                return [
                    'court_name' => $booking->court ? $booking->court->name : 'Sân',
                    'start_time' => $booking->start_time_formatted,
                    'end_time' => $booking->end_time_formatted,
                    'total_price' => $booking->total_price
                ];
            })->toArray();

            $totalPrice = $bookings->sum('total_price');

            $mailData = [
                'booking' => $primaryBooking,
                'bookings' => $bookingsData,
                'payment' => $payment,
                'totalPrice' => $totalPrice,
                'referenceNumber' => $payment->booking_reference
            ];

            $mailable = new PaymentConfirmation($mailData);

            // Ưu tiên sử dụng cấu hình email của branch nếu có
            if ($businessId && $primaryBooking->branch) {
                $sent = MailService::sendUsingBranchConfig(
                    $primaryBooking->branch->id,
                    $primaryBooking->customer_email,
                    $mailable
                );

                // Nếu gửi thành công hoặc có lỗi khác (không phải do không tìm thấy branch/business)
                if ($sent !== false) {
                    return $sent;
                }
                // Nếu false (không tìm thấy branch/business), tiếp tục sử dụng cấu hình mặc định
            }

            // Sử dụng cấu hình mặc định hoặc fallback nếu branch config thất bại
            return MailService::sendUsingBusinessConfig(
                $businessId ?? 1,
                $primaryBooking->customer_email,
                $mailable
            );
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation email: ' . $e->getMessage(), [
                'payment_id' => $payment->id,
                'booking_reference' => $payment->booking_reference,
                'exception' => $e
            ]);
            return false;
        }
    }

    /**
     * Display the payment failure page.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function paymentFailed(Request $request)
    {

        $reference = $request->query('reference');
        $provider = $request->query('provider', 'unknown');
        $errorCode = $request->query('code');
        $errorMessage = $request->query('message', 'Thanh toán không thành công, vui lòng thử lại sau');
        $bookingReference = $request->query('booking');

        $booking = null;


        if ($bookingReference) {
            $booking = \App\Models\CourtBooking::with(['court', 'court.branch'])
                ->where('reference_number', $bookingReference)
                ->first();

            if ($booking) {

                $booking->setAttribute('verified', true);
            }
        }


        if (session()->has('payment_error')) {
            $errorMessage = session('payment_error');
            $errorCode = session('payment_error_code', $errorCode);
        }


        if ($provider === 'momo' && session()->has('momo_payment_message')) {
            $errorMessage = session('momo_payment_message');
            $errorCode = session('momo_payment_code', $errorCode);
        } elseif ($provider === 'vnpay' && session()->has('vnpay_payment_message')) {
            $errorMessage = session('vnpay_payment_message');
            $errorCode = session('vnpay_payment_code', $errorCode);
        }


        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => $errorMessage,
                'error_code' => $errorCode,
                'reference' => $reference,
                'booking_reference' => $bookingReference,
                'provider' => $provider,
            ]);
        }






        return Inertia::render('Customers/Payment/Failed', [
            'reference' => $reference,
            'provider' => $provider,
            'errorCode' => $errorCode,
            'errorMessage' => $errorMessage,
            'booking' => $booking,
            'bookingReference' => $bookingReference,
        ]);
    }

    /**
     * Display payment method selection page for remaining balance
     *
     * @param string $referenceNumber
     * @return \Inertia\Response
     */
    public function selectRemainingPaymentMethod($referenceNumber)
    {
        try {
            $bookings = \App\Models\CourtBooking::where('reference_number', $referenceNumber)
                ->with(['court', 'court.branch', 'branch', 'payment'])
                ->get();

            if ($bookings->isEmpty()) {
                throw new \Exception('Không tìm thấy thông tin đặt sân.');
            }

            // Get all payments for this booking reference
            $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)->get();

            // Calculate total price of all bookings
            $totalPrice = $bookings->sum('total_price');

            // Calculate total paid amount from completed payments
            $totalPaid = $payments
                ->filter(function ($payment) {
                    return $payment->status === 'completed';
                })
                ->sum('amount');

            // Calculate remaining balance
            $remainingBalance = max(0, $totalPrice - $totalPaid);

            if ($remainingBalance <= 0) {
                return redirect()->route('customer.bookings.details', ['referenceNumber' => $referenceNumber])
                    ->with('message', 'Đơn hàng này đã được thanh toán đầy đủ.');
            }

            // Get the primary booking for display purposes
            $primaryBooking = $bookings->first();

            $paymentDeadline = null;
            if ($primaryBooking->payment_deadline) {
                $paymentDeadline = $primaryBooking->payment_deadline->format('Y-m-d H:i:s');
            }

            return Inertia::render('Customers/Payment/RemainingPayment', [
                'booking' => $primaryBooking,
                'bookings' => $bookings,
                'referenceNumber' => $referenceNumber,
                'totalPrice' => $totalPrice,
                'remainingBalance' => $remainingBalance,
                'paymentDeadline' => $paymentDeadline
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting booking for remaining payment selection: ' . $e->getMessage());
            return redirect()->route('user.profile')->with('tab', 'booking')->withErrors([
                'booking_error' => 'Không tìm thấy thông tin đặt sân: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process remaining balance payment
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function processRemainingPayment(Request $request)
    {
        DB::beginTransaction(); // Start transaction
        try {
            $request->validate([
                'booking_reference' => 'required|string',
                'payment_method_id' => 'required|exists:payment_methods,id',
                'amount' => 'required|numeric|min:0',
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'nullable|email',
                'customer_phone' => 'required|string|max:20',
                'payment_details' => 'nullable|string',
                'notes' => 'nullable|string',
                'payment_proof' => 'nullable|file|image|max:5120',
            ]);

            $bookings = \App\Models\CourtBooking::where('reference_number', $request->booking_reference)
                ->with(['branch', 'branch.business', 'payment'])
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin đặt sân.'
                ], 404);
            }

            // Get all payments for this booking reference
            $payments = \App\Models\Payment::where('booking_reference', $request->booking_reference)->get();

            // Calculate total price of all bookings
            $totalPrice = $bookings->sum('total_price');

            // Calculate total paid amount from completed payments
            $totalPaid = $payments
                ->filter(function ($payment) {
                    return $payment->status === 'completed';
                })
                ->sum('amount');

            // Calculate remaining balance
            $remainingBalance = max(0, $totalPrice - $totalPaid);

            if ($remainingBalance <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Đơn hàng này đã được thanh toán đầy đủ.'
                ], 400);
            }

            // Validate that the amount being paid doesn't exceed the remaining balance
            if ($request->amount > $remainingBalance) {
                return response()->json([
                    'success' => false,
                    'message' => 'Số tiền thanh toán vượt quá số tiền còn lại cần thanh toán.'
                ], 400);
            }

            $paymentMethod = PaymentMethod::findOrFail($request->payment_method_id);

            if (!$paymentMethod->is_enabled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phương thức thanh toán này đã bị vô hiệu hóa.'
                ], 400);
            }

            // Get the primary booking for business ID and other details
            $primaryBooking = $bookings->first();

            $businessId = null;
            if ($primaryBooking->branch && $primaryBooking->branch->business_id) {
                $businessId = $primaryBooking->branch->business_id;
            }

            if ($paymentMethod->payment_code === 'bank_transfer' && $businessId) {
                $bankSettings = \App\Models\BusinessSetting::getBankSettings($businessId);

                if (empty($bankSettings['accountNumber']) || empty($bankSettings['bankName'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cấu hình thanh toán chuyển khoản chưa được thiết lập cho đơn vị này.'
                    ], 400);
                }
            }

            $payment = new Payment();
            $payment->booking_reference = $request->booking_reference;
            $payment->court_booking_id = $primaryBooking->id;
            $payment->payment_method_id = $request->payment_method_id;
            $payment->payment_method = $paymentMethod->payment_name;
            $payment->amount = $request->amount;
            $payment->status = 'pending';
            $payment->customer_id = $request->user_id ?? null;
            $payment->user_id = $request->user_id ?? null;
            $payment->payment_details = $request->payment_details;
            $payment->notes = $request->notes;
            $payment->reference_number = 'P' . uniqid();
            $payment->transaction_date = now();
            $payment->payment_type = 'remaining'; // Ensure this line is present

            if ($paymentMethod->payment_code === 'bank_transfer') {
                $bankDetails = [];

                if ($businessId) {
                    $bankSettings = \App\Models\BusinessSetting::getBankSettings($businessId);

                    $paymentMethod->custom_field1 = $bankSettings['accountNumber'] ?? '';
                    $paymentMethod->custom_field2 = $bankSettings['bankName'] ?? '';
                    $paymentMethod->custom_field3 = $bankSettings['accountName'] ?? '';
                    $paymentMethod->custom_field4 = 'Thanh toán cho đơn hàng: [Mã đặt sân]';
                }

                if ($request->hasFile('payment_proof')) {
                    $proofFile = $request->file('payment_proof');
                    $proofFileName = 'payment_proof_' . $payment->reference_number . '.' . $proofFile->getClientOriginalExtension();
                    $proofPath = $proofFile->storeAs('payment_proofs', $proofFileName, 'public');
                    $payment->payment_details = json_encode([
                        'proof_file' => $proofPath,
                        'original_filename' => $proofFile->getClientOriginalName(),
                        'uploaded_at' => now()->format('Y-m-d H:i:s')
                    ]);
                    $payment->status = 'pending_approval';
                } else {
                    $payment->status = 'awaiting_payment';
                }

                $payment->save();

                // Update all bookings in the group
                foreach ($bookings as $booking) {
                    $booking->payment_id = $payment->id;
                    $booking->payment_status = 'partial';
                    $booking->save();
                }

                // if ($request->hasFile('payment_proof') && $payment->status === 'pending_approval') {
                //     $this->sendPaymentConfirmationEmail($payment, $bookings->all());
                // }

                $bankDetails = [
                    'account_number' => $paymentMethod->custom_field1 ?? '',
                    'bank_name' => $paymentMethod->custom_field2 ?? '',
                    'account_name' => $paymentMethod->custom_field3 ?? '',
                    'payment_note' => str_replace('[Mã đặt sân]', $payment->reference_number, $paymentMethod->custom_field4 ?? 'Mã đặt sân: {reference}')
                ];

                DB::commit(); // Commit transaction
                return response()->json([
                    'success' => true,
                    'message' => $request->hasFile('payment_proof')
                        ? 'Thanh toán đã được ghi nhận. Vui lòng đợi xác nhận từ quản trị viên.'
                        : 'Payment initiated. Please complete the bank transfer.',
                    'payment' => [
                        'id' => $payment->id,
                        'reference_number' => $payment->reference_number,
                        'status' => $payment->status,
                        'amount' => $payment->amount,
                        'payment_method' => $payment->payment_method,
                        'bank_details' => $bankDetails
                    ]
                ]);
            }

            $payment->save();

            // Update all bookings in the group
            foreach ($bookings as $booking) {
                $booking->payment_id = $payment->id;
                $booking->payment_status = 'partial';
                $booking->save();
            }

            DB::commit(); // Commit transaction


            NotificationService::createPaymentNotification(
                $primaryBooking->branch_id,
                $payment->reference_number,
                $request->customer_name,
                $request->customer_phone,
                $bookings->all(),
                $payment->payment_method,
                $payment->amount,
                $payment->status
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment initiated. Redirecting to payment gateway.',
                'payment' => [
                    'id' => $payment->id,
                    'reference_number' => $payment->reference_number,
                    'status' => $payment->status,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack(); // Rollback transaction on error
            Log::error('Remaining payment processing error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your payment: ' . ($e->getMessage()),
            ], 500);
        }
    }

    /**
     * Clear stale payment sessions for a specific booking reference
     *
     * @param string $bookingReference
     * @return void
     */
    private function clearStalePaymentSessions($bookingReference)
    {
        try {
            $sessionKeys = collect(session()->all())
                ->keys()
                ->filter(function($key) {
                    return str_starts_with($key, 'momo_payment_request_') ||
                           str_starts_with($key, 'vnpay_payment_request_');
                })
                ->toArray();

            $clearedCount = 0;
            foreach ($sessionKeys as $key) {
                $sessionData = session($key);
                if (is_array($sessionData) && ($sessionData['booking_reference'] ?? null) === $bookingReference) {
                    session()->forget($key);
                    $clearedCount++;
                }
            }

            if ($clearedCount > 0) {
                Log::info('Cleared stale payment sessions', [
                    'booking_reference' => $bookingReference,
                    'sessions_cleared' => $clearedCount
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error clearing stale payment sessions: ' . $e->getMessage(), [
                'booking_reference' => $bookingReference
            ]);
        }
    }
}
