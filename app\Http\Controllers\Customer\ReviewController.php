<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Review;
use App\Models\CourtBooking;
use App\Models\Customer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ReviewController extends Controller
{
    public function index($referenceNumber = null)
    {
        if ($referenceNumber) {
            $booking = CourtBooking::where('reference_number', $referenceNumber)
                ->with('court')->get();

            if ($booking->isEmpty()) {
                return redirect()->back()->with('error', 'Không tìm thấy đơn đặt sân với mã tham chiếu này.');
            }
            $primaryBooking = $booking->first();
            $branch = Branch::find($primaryBooking->branch_id);

            return Inertia::render('Customers/Review/index', [
                'reference_number' => $referenceNumber,
                'branch' => $branch,
                'booking' => $booking
            ]);
        }

        $user = Auth::user();
        if ($user->roles->contains('name', 'super-admin')) {
            return Inertia::render('SuperAdmin/Review/Index');
        } elseif ($user->roles->contains('name', 'admin')) {
            return Inertia::render('Business/Review/Index');
        } elseif ($user->branch_id) {
            return Inertia::render('Branchs/Review/Index');
        } else {
            return redirect()->route('dashboard')
                ->with('error', 'Bạn không có quyền truy cập trang này.');
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        $validator = Validator::make($request->all(), [
            'booking_reference' => 'required|string',
            'rating' => 'required|numeric|min:1|max:5',
            'content' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $bookings = CourtBooking::where('reference_number', $request->booking_reference)->get();
        $primaryBooking = $bookings->first();
        if ($bookings->isEmpty()) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy đơn đặt sân với mã tham chiếu này.'
            ], 404);
        }

        $customerId = $primaryBooking->customer_id;

        if (Auth::check() && Auth::user()->customer && Auth::user()->customer->id !== $customerId) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền đánh giá đơn đặt sân này.'
            ], 403);
        }

        $review = new Review();
        $review->branch_id = $primaryBooking->branch_id;
        $review->customer_id = $customerId;
        $review->rating = $request->rating;
        $review->content = $request->content;
        $review->status = 'pending';
        $review->booking_reference = $request->booking_reference;
        $review->save();

        CourtBooking::where('reference_number', $request->booking_reference)
            ->update(['is_reviewed' => true]);

        DB::commit();
        return response()->json([
            'success' => true,
            'message' => 'Đánh giá đã được gửi thành công!',
            'review' => $review
        ], 201);
    }

    public function update($id, Request $request)
    {
        $review = Review::findOrFail($id);
        $customerId = $review->customer_id;
        if (Auth::check() && Auth::user()->customer && Auth::user()->customer->id !== $customerId) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền sửa đánh giá này.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'rating' => 'required|numeric|min:1|max:5',
            'content' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $review->rating = $request->rating;
        $review->content = $request->content;
        $review->status = 'pending'; 
        $review->save();

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật đánh giá thành công!',
            'review' => $review
        ]);
    }

    public function getList(Request $request)
    {
        $user = Auth::user();
        $query = Review::with(['customer', 'branch'])
            ->orderByDesc('created_at');
        if ($user->roles->contains('name', 'super-admin')) {
        }
        elseif ($user->roles->contains('name', 'admin')) {
            if (!$user->business_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin business.'
                ], 404);
            }

            $branchIds = Branch::where('business_id', $user->business_id)
                ->pluck('id')
                ->toArray();

            if (empty($branchIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy chi nhánh nào thuộc business của bạn.'
                ], 404);
            }

            $query->whereIn('branch_id', $branchIds);
        }
        // Branch manager sees reviews from their branch only
        elseif ($user->branch_id) {
            $query->where('branch_id', $user->branch_id);
        }
        // Users with specific permission
        elseif ($user->permissions->contains('name', 'booking:read_review')) {
            if ($user->branch_id) {
                $query->where('branch_id', $user->branch_id);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn cần được gán vào một chi nhánh để xem đánh giá.'
                ], 403);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xem danh sách đánh giá.'
            ], 403);
        }

        // Apply optional filters if provided
        if ($request->filled('branch_id')) {
            // For admin, verify the branch belongs to their business
            if ($user->roles->contains('name', 'admin')) {
                $branchBelongsToBusiness = Branch::where('id', $request->branch_id)
                    ->where('business_id', $user->business_id)
                    ->exists();

                if (!$branchBelongsToBusiness) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Chi nhánh không thuộc quyền quản lý của bạn.'
                    ], 403);
                }
            }
            // For branch manager, verify it's their branch
            elseif ($user->branch_id && $user->branch_id != $request->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn chỉ có thể xem đánh giá của chi nhánh mình quản lý.'
                ], 403);
            }

            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Add this for debugging
        \Log::info('User Role: ' . implode(', ', $user->roles->pluck('name')->toArray()));
        \Log::info('User Business ID: ' . $user->business_id);
        \Log::info('User Branch ID: ' . $user->branch_id);
        \Log::info('SQL Query: ' . $query->toSql());
        \Log::info('Query Bindings: ', $query->getBindings());

        $perPage = $request->input('per_page', 20);
        $reviews = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $reviews
        ]);
    }

    public function approveReview($id, Request $request)
    {
        $user = Auth::user();
        if (
            !$user->roles->contains('name', 'admin') &&
            !$user->roles->contains('name', 'super-admin') &&
            !$user->permissions->contains('name', 'booking:update_review')
        ) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền duyệt đánh giá.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:approved,rejected',
            'reject_reason' => 'nullable|string|max:1000',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $review = Review::findOrFail($id);
        $review->status = $request->status;
        if ($request->status === 'rejected' && isset($review->reject_reason)) {
            $review->reject_reason = $request->reject_reason;
        }
        $review->save();

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật trạng thái đánh giá thành công!',
            'review' => $review
        ]);
    }

    public function destroy($id)
    {
        $user = Auth::user();
        $review = Review::findOrFail($id);
        $customer = Customer::find($review->customer_id);
        $isOwner = $customer->user_id === $user->id;
        if (
            !$isOwner &&
            !$user->roles->contains('name', 'admin') &&
            !$user->roles->contains('name', 'super-admin') &&
            !$user->permissions->contains('name', 'booking:delete_review')
        ) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xóa đánh giá này.'
            ], 403);
        }

        $review->delete();

        return response()->json([
            'success' => true,
            'message' => 'Xóa đánh giá thành công!'
        ]);
    }
}
