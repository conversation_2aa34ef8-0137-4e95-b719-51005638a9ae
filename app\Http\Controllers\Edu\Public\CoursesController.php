<?php

namespace App\Http\Controllers\Edu\Public;

use App\Http\Controllers\Controller;
use App\Models\EduCourse;
use App\Models\EduLecturer;
use App\Models\EduReview;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class CoursesController extends Controller
{
    public function index(Request $request)
    {
        $query = EduCourse::with(['lecturer.user'])
            ->where('status', 'active');

        if ($request->filled('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('short_description', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('instructor')) {
            $query->whereHas('lecturer', function ($q) use ($request) {
                $q->whereHas('user', function ($userQuery) use ($request) {
                    $userQuery->where('id', $request->instructor);
                });
            });
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        switch ($request->get('sort_by', 'popular')) {
            case 'price-asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'popular':
            default:
                $query->orderBy('enrolled_students', 'desc')
                      ->orderBy('rating', 'desc');
                break;
        }

        $courses = $query->paginate(6)->withQueryString();

        $courses->getCollection()->transform(function ($course) {
            return [
                'id' => $course->id,
                'title' => $course->title,
                'slug' => $course->slug,
                'category' => $course->category,
                'level' => $course->level,
                'price' => $course->price,
                'original_price' => $course->original_price,
                'lessons' => $course->total_lessons,
                'duration' => $course->duration_hours,
                'rating' => $course->rating,
                'enrolled_students' => $course->enrolled_students,
                'instructor' => [
                    'id' => $course->lecturer->id,
                    'name' => $course->lecturer->user->name,
                    'avatar' => $course->lecturer->profile_image_url,
                    'slug' => $course->lecturer->id,
                ],
                'image' => $course->thumbnail_url,
                'badge' => $this->getBadge($course),
                'discount_percentage' => $course->discount_percentage,
                'discount_end_date' => $course->discount_end_date,
            ];
        });

        $dbCategoriesWithCounts = EduCourse::where('status', 'active')
            ->whereNotNull('category')
            ->selectRaw('category, count(*) as count')
            ->groupBy('category')
            ->orderBy('category')
            ->get();

        $dbCategories = $dbCategoriesWithCounts->map(function ($item) {
            return [
                'id' => $item->category,
                'label' => $this->getCategoryLabel($item->category),
                'count' => $item->count
            ];
        });

        $totalCourses = $dbCategoriesWithCounts->sum('count');

        $categories = collect([
            ['id' => 'all', 'label' => 'Tất cả khóa học', 'count' => $totalCourses],
        ])->concat($dbCategories)->all();

        $levelOptions = [
            ['value' => '', 'label' => 'Tất cả cấp độ'],
            ['value' => 'beginner', 'label' => 'Người mới bắt đầu'],
            ['value' => 'intermediate', 'label' => 'Trung cấp'],
            ['value' => 'advanced', 'label' => 'Nâng cao'],
            ['value' => 'professional', 'label' => 'Chuyên nghiệp'],
        ];

        $dbInstructors = EduLecturer::with('user')
            ->where('status', 'active')
            ->get()
            ->map(function ($lecturer) {
                return [
                    'value' => $lecturer->user->id,
                    'label' => $lecturer->user->name,
                ];
            });

        $instructors = collect([
            ['value' => '', 'label' => 'Tất cả huấn luyện viên']
        ])->concat($dbInstructors)->all();

        return Inertia::render('Edu/Public/Courses/Courses', [
            'courses' => $courses,
            'categories' => $categories,
            'levelOptions' => $levelOptions,
            'instructorOptions' => $instructors,
            'filters' => $request->only(['category', 'search', 'level', 'instructor', 'min_price', 'max_price', 'sort_by']),
        ]);
    }

    public function show(Request $request, $slug)
    {

        $course = EduCourse::where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();

        $course->increment('views_count');

        $course = EduCourse::with([
            'lecturer.user',
            'reviews' => function($query) {
                $query->with('student.user')
                      ->where('is_published', true)
                      ->orderBy('created_at', 'desc')
                      ->limit(10);
            }
        ])
        ->where('slug', $slug)
        ->where('status', 'active')
        ->firstOrFail();

        $relatedCourses = EduCourse::with(['lecturer.user'])
            ->where('status', 'active')
            ->where('id', '!=', $course->id)
            ->where(function($query) use ($course) {
                $query->where('category', $course->category)
                      ->orWhere('lecturer_id', $course->lecturer_id);
            })
            ->orderBy('enrolled_students', 'desc')
            ->orderBy('rating', 'desc')
            ->limit(3)
            ->get();

        $allReviews = EduReview::where('edu_course_id', $course->id)
            ->where('is_published', true)
            ->whereNotNull('rating')
            ->where('rating', '>', 0)
            ->get();

        $averageRating = $allReviews->avg('rating') ?? 0;
        $totalReviews = $allReviews->count();

        $ratingBreakdown = [];
        for ($i = 1; $i <= 5; $i++) {
            $ratingBreakdown[$i] = $allReviews->where('rating', $i)->count();
        }

        $courseData = [
            'id' => $course->id,
            'title' => $course->title,
            'subtitle' => $course->short_description,
            'description' => $course->description,
            'level' => $this->getLevelLabel($course->level),
            'rating' => round($averageRating, 1),
            'reviewCount' => $totalReviews,
            'studentCount' => $course->enrolled_students,
            'lessons' => $course->total_lessons,
            'duration' => $course->duration_hours . ' giờ',
            'lastUpdated' => $this->formatLastUpdated($course->updated_at),
            'price' => $course->price,
            'originalPrice' => $course->original_price,
            'discount' => $course->discount_percentage,
            'discountEndDate' => $course->discount_end_date,
            'image' => $course->thumbnail_url,
            'curriculum' => $course->curriculum ?? [],
            'requirements' => $course->requirements ?? [],
            'outcomes' => $course->outcomes ?? [],
            'tags' => $course->tags ?? [],
            'instructor' => [
                'id' => $course->lecturer->id,
                'name' => $course->lecturer->user->name,
                'title' => $course->lecturer->title ?? 'Huấn luyện viên Pickleball',
                'avatar' => $course->lecturer->profile_image_url,
                'courses' => $course->lecturer->total_courses,
                'students' => $course->lecturer->total_students,
                'rating' => $course->lecturer->rating,
                'description' => $course->lecturer->description,
            ]
        ];

        $reviewsData = $course->reviews->map(function ($review) {
            return [
                'id' => $review->id,
                'name' => $review->student->user->name,
                'avatar' => $review->student->user->profile_photo_url ?? null,
                'date' => $review->created_at->format('d/m/Y'),
                'rating' => $review->rating,
                'content' => $review->comment,
            ];
        });

        $relatedCoursesData = $relatedCourses->map(function ($relatedCourse) {
            return [
                'id' => $relatedCourse->id,
                'title' => $relatedCourse->title,
                'slug' => $relatedCourse->slug,
                'instructor' => $relatedCourse->lecturer->user->name,
                'instructorAvatar' => $relatedCourse->lecturer->profile_image_url,
                'price' => $relatedCourse->price,
                'image' => $relatedCourse->thumbnail_url,
                'rating' => $relatedCourse->rating,
                'students' => $relatedCourse->enrolled_students,
            ];
        });

        return Inertia::render('Edu/Public/CourseDetail/CourseDetail', [
            'course' => $courseData,
            'reviews' => $reviewsData,
            'relatedCourses' => $relatedCoursesData,
            'ratingStats' => [
                'average' => round($averageRating, 1),
                'total' => $totalReviews,
                'breakdown' => $ratingBreakdown,
            ],
        ]);
    }

    /**
     * Get level label in Vietnamese
     */
    private function getLevelLabel($level)
    {
        $labels = [
            'beginner' => 'Người mới bắt đầu',
            'intermediate' => 'Trung cấp',
            'advanced' => 'Nâng cao',
            'professional' => 'Chuyên nghiệp',
        ];

        return $labels[$level] ?? $level;
    }

    /**
     * Get course badge based on course properties
     */
    private function getBadge($course)
    {
        if ($course->is_featured) {
            return 'Nổi bật';
        }

        if ($course->created_at->diffInDays(now()) <= 30) {
            return 'Mới';
        }

        if ($course->enrolled_students > 500) {
            return 'Bán chạy';
        }

        if ($course->level === 'professional') {
            return 'Chuyên nghiệp';
        }

        if ($course->level === 'advanced') {
            return 'Nâng cao';
        }

        if ($course->level === 'beginner') {
            return 'Cơ bản';
        }

        if ($course->is_free) {
            return 'Miễn phí';
        }

        return null;
    }

    /**
     * Format last updated date in Vietnamese format
     *
     * @param Carbon $date
     * @return string
     */
    private function formatLastUpdated($date)
    {
        if (!$date) {
            return 'Chưa cập nhật';
        }

        try {

            return 'tháng ' . $date->format('m/Y');
        } catch (\Exception $e) {
            Log::error('Error formatting course update date: ' . $e->getMessage());
            return 'Chưa cập nhật';
        }
    }

    /**
     * Get Vietnamese label for a category
     *
     * @param string $category
     * @return string
     */
    private function getCategoryLabel($category)
    {
        $labels = [
            'basic' => 'Kỹ thuật cơ bản',
            'advanced' => 'Kỹ thuật nâng cao',
            'strategy' => 'Chiến thuật',
            'fitness' => 'Thể lực',
            'mental' => 'Tâm lý thi đấu',
            'kids' => 'Trẻ em',
            'seniors' => 'Người cao tuổi',
            'recovery' => 'Phục hồi chấn thương',
            'coaching' => 'Đào tạo HLV',
            'tournament' => 'Chuẩn bị thi đấu',
        ];

        return $labels[$category] ?? $category;
    }
}
