<?php

namespace App\Http\Controllers\Edu\Public;

use App\Http\Controllers\Controller;
use App\Models\EduLecturer;
use App\Models\EduCourse;
use App\Models\EduReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class LecturerProfileController extends Controller
{
    public function show(Request $request, $id)
    {
        $lecturer = EduLecturer::with(['user', 'courses' => function($query) {
            $query->where('status', 'active')->orderBy('enrolled_students', 'desc');
        }])
        ->where('id', $id)
        ->where('status', 'active')
        ->firstOrFail();

        $query = $lecturer->courses()->where('status', 'active');

        $activeTab = $request->get('tab', 'popular');
        switch ($activeTab) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'all':

                $query->orderBy('enrolled_students', 'desc');
                break;
            case 'popular':
            default:
                $query->orderBy('enrolled_students', 'desc');
                break;
        }

        $courses = $query->paginate(6, ['*'], 'coursePage')
            ->withQueryString();

        $courses->getCollection()->transform(function ($course) {
            return [
                'id' => $course->id,
                'title' => $course->title,
                'slug' => $course->slug,
                'level' => $course->level,
                'duration' => $course->duration_hours . ' giờ',
                'students' => $course->enrolled_students,
                'price' => $course->price,
                'image' => $course->thumbnail_url,
            ];
        });

        $lecturerCourseIdsForReviews = $lecturer->courses()->pluck('id');
        $reviews = EduReview::with(['student.user', 'course'])
            ->whereIn('edu_course_id', $lecturerCourseIdsForReviews)
            ->where('is_published', true)
            ->orderBy('created_at', 'desc')
            ->paginate(8, ['*'], 'reviewPage')
            ->withQueryString();

        $reviews->getCollection()->transform(function ($review) {
            return [
                'id' => $review->id,
                'name' => $review->student->user->name,
                'avatar' => $review->student->user->profile_photo_url,
                'course' => $review->course->title,
                'date' => $review->created_at->format('d/m/Y'),
                'rating' => $review->rating,
                'content' => $review->comment,
            ];
        });

        $lecturerCourseIds = $lecturer->courses()->pluck('id');
        $allReviews = EduReview::whereIn('edu_course_id', $lecturerCourseIds)
            ->where('is_published', true)
            ->whereNotNull('rating')
            ->where('rating', '>', 0)
            ->get();

        $averageRating = $allReviews->count() > 0 ? $allReviews->avg('rating') : 0;
        $totalReviews = $allReviews->count();

        $ratingStats = [
            'average' => $averageRating > 0 ? round($averageRating, 1) : 0,
            'total' => $totalReviews,
            'breakdown' => [
                5 => $allReviews->where('rating', 5)->count(),
                4 => $allReviews->where('rating', 4)->count(),
                3 => $allReviews->where('rating', 3)->count(),
                2 => $allReviews->where('rating', 2)->count(),
                1 => $allReviews->where('rating', 1)->count(),
            ]
        ];

        $lecturerData = [
            'id' => $lecturer->id,
            'name' => $lecturer->user->name,
            'title' => $lecturer->title,
            'avatar' => $lecturer->profile_image_url,
            'brief' => $lecturer->short_description,
            'description' => $lecturer->description,
            'stats' => [
                'courses' => $lecturer->courses()->where('status', 'active')->count(),
                'students' => $lecturer->students_count,
                'rating' => $ratingStats['average'],
                'experience' => $lecturer->experience_years,
            ],
            'email' => $lecturer->user->email,
            'phone' => $lecturer->user->phone ?? 'Chưa cung cấp',
            'achievements' => $lecturer->achievements ?: [],
            'certifications' => $lecturer->certifications ?: [],
            'social_links' => $this->formatSocialLinks($lecturer->social_links),
        ];

        return Inertia::render('Edu/Public/LecturerProfile/LecturerProfile', [
            'lecturer' => $lecturerData,
            'courses' => $courses,
            'reviews' => $reviews,
            'ratingStats' => $ratingStats,
        ]);
    }

    public function courses(Request $request, $id)
    {
        $lecturer = EduLecturer::with('user')
            ->where('id', $id)
            ->where('status', 'active')
            ->firstOrFail();

        $query = $lecturer->courses()->where('status', 'active');

        switch ($request->get('tab', 'popular')) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'popular':
            default:
                $query->orderBy('enrolled_students', 'desc')
                      ->orderBy('rating', 'desc');
                break;
        }

        $pageParam = $request->has('coursePage') ? 'coursePage' : 'page';
        $courses = $query->paginate(6, ['*'], $pageParam)->withQueryString();

        $courses->getCollection()->transform(function ($course) {
            return [
                'id' => $course->id,
                'title' => $course->title,
                'slug' => $course->slug,
                'level' => $course->level,
                'duration' => $course->duration_hours . ' giờ',
                'students' => $course->enrolled_students,
                'price' => $course->price,
                'image' => $course->thumbnail_url,
            ];
        });

        return response()->json([
            'courses' => $courses,
        ]);
    }

    public function reviews(Request $request, $id)
    {
        $lecturer = EduLecturer::where('id', $id)
            ->where('status', 'active')
            ->firstOrFail();

        $lecturerCourseIds = $lecturer->courses()->pluck('id');

        $pageParam = $request->has('reviewPage') ? 'reviewPage' : 'page';

        $reviews = EduReview::with(['student.user', 'course'])
            ->whereIn('edu_course_id', $lecturerCourseIds)
            ->where('is_published', true)
            ->orderBy('created_at', 'desc')
            ->paginate(10, ['*'], $pageParam)
            ->withQueryString();

        $reviews->getCollection()->transform(function ($review) {
            return [
                'id' => $review->id,
                'name' => $review->student->user->name,
                'avatar' => $review->student->user->profile_photo_url,
                'course' => $review->course->title,
                'date' => $review->created_at->format('d/m/Y'),
                'rating' => $review->rating,
                'content' => $review->comment,
            ];
        });

        $lecturerCourseIds = $lecturer->courses()->pluck('id');
        $allReviews = EduReview::whereIn('edu_course_id', $lecturerCourseIds)
            ->where('is_published', true)
            ->whereNotNull('rating')
            ->where('rating', '>', 0)
            ->get();

        $averageRating = $allReviews->avg('rating') ?? 0;
        $totalReviews = $allReviews->count();

        $ratingStats = [
            'average' => round($averageRating, 1),
            'total' => $totalReviews,
            'breakdown' => [
                5 => $allReviews->where('rating', 5)->count(),
                4 => $allReviews->where('rating', 4)->count(),
                3 => $allReviews->where('rating', 3)->count(),
                2 => $allReviews->where('rating', 2)->count(),
                1 => $allReviews->where('rating', 1)->count(),
            ]
        ];

        return response()->json([
            'reviews' => $reviews,
            'ratingStats' => $ratingStats,
        ]);
    }

    /**
     * Format social links to ensure consistent structure
     *
     * @param mixed $socialLinks
     * @return array
     */
    private function formatSocialLinks($socialLinks)
    {
        if (empty($socialLinks)) {
            return [];
        }

        if (is_array($socialLinks) && isset($socialLinks[0]) && isset($socialLinks[0]['type'])) {
            return array_filter($socialLinks, function($link) {
                return !empty($link['url']) && !empty($link['type']);
            });
        }

        $formattedLinks = [];

        $links = is_array($socialLinks) ? $socialLinks : (array)$socialLinks;

        foreach ($links as $type => $url) {
            if (!empty($url)) {

                if (is_string($url) || is_string($type)) {
                    $formattedLinks[] = [
                        'type' => is_numeric($type) ? 'unknown' : $type,
                        'url' => $url
                    ];
                }
            }
        }

        return $formattedLinks;
    }
}
