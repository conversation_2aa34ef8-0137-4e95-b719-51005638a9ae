<?php

namespace App\Http\Controllers\Edu\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\EduCourse;
use App\Models\EduLecturer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'category', 'level', 'lecturer_id', 'is_featured', 'is_free', 'sort', 'direction']);

        $query = EduCourse::query()
            ->with(['lecturer.user'])
            ->withCount(['students as enrolled_students', 'reviews as total_reviews']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%")
                  ->orWhereHas('lecturer.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['category']) && $filters['category']) {
            $query->where('category', $filters['category']);
        }

        if (isset($filters['level']) && $filters['level']) {
            $query->where('level', $filters['level']);
        }

        if (isset($filters['lecturer_id']) && $filters['lecturer_id']) {
            $query->where('lecturer_id', $filters['lecturer_id']);
        }

        if (isset($filters['is_featured']) && $filters['is_featured'] !== '') {
            $query->where('is_featured', (bool)$filters['is_featured']);
        }

        if (isset($filters['is_free']) && $filters['is_free'] !== '') {
            $query->where('is_free', (bool)$filters['is_free']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'lecturer_name') {
            $query->join('edu_lecturers', 'edu_courses.lecturer_id', '=', 'edu_lecturers.id')
                  ->join('users', 'edu_lecturers.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_courses.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $courses = $query->paginate(10)->withQueryString();

        $courses->getCollection()->transform(function ($course) {
            $course->thumbnail_url = $this->getThumbnailUrl($course->thumbnail);
            return $course;
        });

        $lecturers = EduLecturer::with('user')->where('status', 'active')->get();

        $categories = EduCourse::distinct()->pluck('category')->filter()->sort()->values();
        $levels = ['beginner', 'intermediate', 'advanced'];

        return Inertia::render('Edu/Courses/Index', [
            'courses' => $courses,
            'lecturers' => $lecturers,
            'categories' => $categories,
            'levels' => $levels,
            'filters' => $filters,
        ]);
    }

    public function apiIndex(Request $request)
    {
        $filters = $request->only(['search', 'status', 'category', 'level', 'lecturer_id', 'is_featured', 'is_free', 'sort', 'direction']);

        $query = EduCourse::query()
            ->with(['lecturer.user'])
            ->withCount(['students as enrolled_students', 'reviews as total_reviews']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%")
                  ->orWhereHas('lecturer.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['category']) && $filters['category']) {
            $query->where('category', $filters['category']);
        }

        if (isset($filters['level']) && $filters['level']) {
            $query->where('level', $filters['level']);
        }

        if (isset($filters['lecturer_id']) && $filters['lecturer_id']) {
            $query->where('lecturer_id', $filters['lecturer_id']);
        }

        if (isset($filters['is_featured']) && $filters['is_featured'] !== '') {
            $query->where('is_featured', (bool)$filters['is_featured']);
        }

        if (isset($filters['is_free']) && $filters['is_free'] !== '') {
            $query->where('is_free', (bool)$filters['is_free']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'lecturer_name') {
            $query->join('edu_lecturers', 'edu_courses.lecturer_id', '=', 'edu_lecturers.id')
                  ->join('users', 'edu_lecturers.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_courses.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $courses = $query->paginate(10)->withQueryString();

        $courses->getCollection()->transform(function ($course) {
            $course->thumbnail_url = $this->getThumbnailUrl($course->thumbnail);
            return $course;
        });

        return response()->json([
            'data' => $courses
        ]);
    }

    public function create()
    {
        $lecturers = EduLecturer::with('user')->where('status', 'active')->orderBy('id', 'desc')->get();

        return Inertia::render('Edu/Courses/Create', [
            'lecturers' => $lecturers
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lecturer_id' => 'required|exists:edu_lecturers,id',
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'level' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'nullable|integer|min:0',
            'total_lessons' => 'nullable|integer|min:0',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'max_students' => 'nullable|integer|min:1',
            'curriculum' => 'nullable|array',
            'requirements' => 'nullable|array',
            'outcomes' => 'nullable|array',
            'tags' => 'nullable|array',
            'thumbnail' => 'nullable|image|max:2048',
            'is_featured' => 'boolean',
            'is_free' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'status' => 'required|in:pending_approval,active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $course = new EduCourse();
            $course->lecturer_id = $request->lecturer_id;
            $course->title = $request->title;
            $course->short_description = $request->short_description;
            $course->description = $request->description;
            $course->category = $request->category;
            $course->level = $request->level;
            $course->duration_hours = $request->duration_hours ?? 0;
            $course->total_lessons = $request->total_lessons ?? 0;
            $course->price = $request->is_free ? 0 : $request->price;
            $course->original_price = $request->original_price;
            $course->max_students = $request->max_students;
            $course->curriculum = $request->curriculum ?? [];
            $course->requirements = $request->requirements ?? [];
            $course->outcomes = $request->outcomes ?? [];
            $course->tags = $request->tags ?? [];
            $course->is_featured = $request->is_featured ?? false;
            $course->is_free = $request->is_free ?? false;
            $course->start_date = $request->start_date;
            $course->end_date = $request->end_date;
            $course->status = $request->status;
            $course->slug = $this->generateUniqueSlug($request->title);

            if ($request->hasFile('thumbnail')) {
                $path = $request->file('thumbnail')->store('edu/courses', 'public');
                $course->thumbnail = $path;
            }

            if ($request->status === 'active') {
                $course->published_at = now();
                $course->approved_by = Auth::id();
            }

            $course->save();

            DB::commit();

            return redirect()->route('superadmin.edu.courses.index')
                ->with('success', __('edu.messages.course_created'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('edu.messages.course_create_failed', ['error' => $e->getMessage()]))
                ->withInput();
        }
    }

    public function show($id)
    {
        $course = EduCourse::with(['lecturer.user', 'students', 'reviews.student.user'])
            ->withCount(['students as enrolled_students', 'reviews as total_reviews'])
            ->findOrFail($id);

        $course->thumbnail_url = $this->getThumbnailUrl($course->thumbnail);

        return Inertia::render('Edu/Courses/Show', [
            'course' => $course
        ]);
    }

    public function edit($id)
    {
        $course = EduCourse::with('lecturer.user')->findOrFail($id);
        $course->thumbnail_url = $this->getThumbnailUrl($course->thumbnail);

        $lecturers = EduLecturer::with('user')->where('status', 'active')->orderBy('id', 'desc')->get();

        return Inertia::render('Edu/Courses/Edit', [
            'course' => $course,
            'lecturers' => $lecturers
        ]);
    }

    public function update(Request $request, $id)
    {
        $course = EduCourse::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'lecturer_id' => 'required|exists:edu_lecturers,id',
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'level' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'nullable|integer|min:0',
            'total_lessons' => 'nullable|integer|min:0',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'max_students' => 'nullable|integer|min:1',
            'curriculum' => 'nullable|array',
            'requirements' => 'nullable|array',
            'outcomes' => 'nullable|array',
            'tags' => 'nullable|array',
            'thumbnail' => 'nullable|image|max:2048',
            'is_featured' => 'boolean',
            'is_free' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'status' => 'required|in:pending_approval,active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $course->lecturer_id = $request->lecturer_id;
            $course->title = $request->title;
            $course->short_description = $request->short_description;
            $course->description = $request->description;
            $course->category = $request->category;
            $course->level = $request->level;
            $course->duration_hours = $request->duration_hours ?? 0;
            $course->total_lessons = $request->total_lessons ?? 0;
            $course->price = $request->is_free ? 0 : $request->price;
            $course->original_price = $request->original_price;
            $course->max_students = $request->max_students;
            $course->curriculum = $request->curriculum ?? [];
            $course->requirements = $request->requirements ?? [];
            $course->outcomes = $request->outcomes ?? [];
            $course->tags = $request->tags ?? [];
            $course->is_featured = $request->is_featured ?? false;
            $course->is_free = $request->is_free ?? false;
            $course->start_date = $request->start_date;
            $course->end_date = $request->end_date;
            $course->status = $request->status;

            if ($course->isDirty('title')) {
                $course->slug = $this->generateUniqueSlug($request->title, $course->id);
            }

            if ($request->hasFile('thumbnail')) {

                if ($course->thumbnail) {
                    Storage::disk('public')->delete($course->thumbnail);
                }
                $path = $request->file('thumbnail')->store('edu/courses', 'public');
                $course->thumbnail = $path;
            }

            if ($request->status === 'active' && $course->status !== 'active') {
                $course->published_at = now();
                $course->approved_by = Auth::id();
            }

            $course->save();

            DB::commit();

            return redirect()->route('superadmin.edu.courses.index')
                ->with('success', __('edu.messages.course_updated'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('edu.messages.course_update_failed', ['error' => $e->getMessage()]))
                ->withInput();
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $course = EduCourse::findOrFail($id);

            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }

            $course->delete();

            DB::commit();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.course_deleted')
                ]);
            }

            return redirect()->route('superadmin.edu.courses.index')
                ->with('success', __('edu.messages.course_deleted'));

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.course_delete_failed', ['error' => $e->getMessage()])
                ], 500);
            }

            return redirect()->back()
                ->with('error', __('edu.messages.course_delete_failed', ['error' => $e->getMessage()]));
        }
    }

    /**
     * Helper function to generate thumbnail URL
     */
    private function getThumbnailUrl($imagePath)
    {
        if ($imagePath) {
            return asset('storage/' . $imagePath);
        }
        return null;
    }

    /**
     * Generate a unique slug for the course
     */
    private function generateUniqueSlug($title, $excludeId = null)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        while (true) {
            $query = EduCourse::where('slug', $slug);

            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        return $slug;
    }
}
