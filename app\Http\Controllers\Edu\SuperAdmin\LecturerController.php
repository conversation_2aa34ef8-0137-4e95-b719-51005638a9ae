<?php

namespace App\Http\Controllers\Edu\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\EduLecturer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class LecturerController extends Controller
{
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'sort', 'direction']);

        $query = EduLecturer::query()
            ->with('user')
            ->withCount(['courses as total_courses']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'user.name') {
            $query->join('users', 'edu_lecturers.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_lecturers.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $lecturers = $query->paginate(10)->withQueryString();

        $lecturers->getCollection()->transform(function ($lecturer) {
            $lecturer->profile_image_url = $this->getProfileImageUrl($lecturer->profile_image);

            $lecturer->total_students = $lecturer->students_count;
            $lecturer->total_reviews = $lecturer->reviews_count;

            return $lecturer;
        });

        return Inertia::render('Edu/Lecturers/Index', [
            'lecturers' => $lecturers,
            'filters' => $filters,
        ]);
    }

    public function apiIndex(Request $request)
    {
        $filters = $request->only(['search', 'status', 'sort', 'direction']);

        $query = EduLecturer::query()
            ->with('user')
            ->withCount(['courses as total_courses']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'user.name') {
            $query->join('users', 'edu_lecturers.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_lecturers.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $lecturers = $query->paginate(10)->withQueryString();

        $lecturers->getCollection()->transform(function ($lecturer) {
            $lecturer->profile_image_url = $this->getProfileImageUrl($lecturer->profile_image);

            $lecturer->total_students = $lecturer->students_count;
            $lecturer->total_reviews = $lecturer->reviews_count;

            return $lecturer;
        });

        return response()->json([
            'data' => $lecturers
        ]);
    }

    public function create()
    {
        $users = User::whereDoesntHave('eduLecturer')
            ->orderBy('name')
            ->get(['id', 'name', 'email']);

        return Inertia::render('Edu/Lecturers/Create', [
            'users' => $users
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id|unique:edu_lecturers,user_id',
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'nullable|string',
            'achievements' => 'nullable|array',
            'certifications' => 'nullable|array',
            'social_links' => 'nullable|array',
            'profile_image' => 'nullable|image|max:2048',
            'experience_years' => 'nullable|integer|min:0',
            'status' => 'required|in:pending_approval,active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $lecturer = new EduLecturer();
            $lecturer->user_id = $request->user_id;
            $lecturer->title = $request->title;
            $lecturer->short_description = $request->short_description;
            $lecturer->description = $request->description;
            $lecturer->achievements = $request->achievements ?? [];
            $lecturer->certifications = $request->certifications ?? [];
            $lecturer->social_links = $request->social_links ?? [];
            $lecturer->experience_years = $request->experience_years;
            $lecturer->status = $request->status;

            if ($request->hasFile('profile_image')) {

                $path = $request->file('profile_image')->store('edu/lecturers', 'public');
                $lecturer->profile_image = $path;
            }

            $lecturer->save();

            $user = User::findOrFail($request->user_id);

            $lecturerRole = Role::firstOrCreate(['name' => 'lecturer']);

            if (!$user->hasRole('lecturer')) {
                $user->assignRole('lecturer');
            }

            DB::commit();

            return redirect()->route('superadmin.edu.lecturers.index')
                ->with('success', __('edu.messages.lecturer_created'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('edu.messages.lecturer_create_failed', ['error' => $e->getMessage()]))
                ->withInput();
        }
    }

    public function show($id)
    {
        $lecturer = EduLecturer::with(['user', 'courses'])
            ->withCount(['courses as total_courses'])
            ->findOrFail($id);

        if ($lecturer->profile_image) {
            $lecturer->profile_image_url = $this->getProfileImageUrl($lecturer->profile_image);
        }

        $lecturer->total_students = $lecturer->students_count;
        $lecturer->total_reviews = $lecturer->reviews_count;

        return Inertia::render('Edu/Lecturers/Show', [
            'lecturer' => $lecturer
        ]);
    }

    public function edit($id)
    {
        $lecturer = EduLecturer::with('user')->findOrFail($id);

        if ($lecturer->profile_image) {
            $lecturer->profile_image_url = $this->getProfileImageUrl($lecturer->profile_image);
        }

        $users = User::where(function($query) use ($lecturer) {
                $query->whereDoesntHave('eduLecturer')
                      ->orWhere('id', $lecturer->user_id);
            })
            ->orderBy('name')
            ->get(['id', 'name', 'email']);

        return Inertia::render('Edu/Lecturers/Edit', [
            'lecturer' => $lecturer,
            'users' => $users
        ]);
    }

    public function update(Request $request, $id)
    {
        $lecturer = EduLecturer::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id|unique:edu_lecturers,user_id,' . $lecturer->id,
            'title' => 'required|string|max:255',
            'short_description' => 'required|string|max:500',
            'description' => 'nullable|string',
            'achievements' => 'nullable|array',
            'certifications' => 'nullable|array',
            'social_links' => 'nullable|array',
            'profile_image' => 'nullable|image|max:2048',
            'experience_years' => 'nullable|integer|min:0',
            'status' => 'required|in:pending_approval,active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $oldUserId = $lecturer->user_id;
            $newUserId = $request->user_id;

            $lecturer->user_id = $newUserId;
            $lecturer->title = $request->title;
            $lecturer->short_description = $request->short_description;
            $lecturer->description = $request->description;
            $lecturer->achievements = $request->achievements ?? [];
            $lecturer->certifications = $request->certifications ?? [];
            $lecturer->social_links = $request->social_links ?? [];
            $lecturer->experience_years = $request->experience_years;
            $lecturer->status = $request->status;

            if ($request->hasFile('profile_image')) {

                if ($lecturer->profile_image) {
                    Storage::disk('public')->delete($lecturer->profile_image);
                }

                $path = $request->file('profile_image')->store('edu/lecturers', 'public');
                $lecturer->profile_image = $path;
            }

            $lecturer->save();

            if ($oldUserId != $newUserId) {

                $oldUser = User::find($oldUserId);
                if ($oldUser && $oldUser->hasRole('lecturer') && !EduLecturer::where('user_id', $oldUserId)->where('id', '!=', $lecturer->id)->exists()) {
                    $oldUser->removeRole('lecturer');
                }

                $newUser = User::find($newUserId);
                if ($newUser && !$newUser->hasRole('lecturer')) {
                    $newUser->assignRole('lecturer');
                }
            }

            DB::commit();

            return redirect()->route('superadmin.edu.lecturers.index')
                ->with('success', __('edu.messages.lecturer_updated'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('edu.messages.lecturer_update_failed', ['error' => $e->getMessage()]))
                ->withInput();
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $lecturer = EduLecturer::findOrFail($id);
            $userId = $lecturer->user_id;

            if ($lecturer->profile_image) {
                Storage::disk('public')->delete($lecturer->profile_image);
            }

            $lecturer->delete();

            $user = User::find($userId);
            if ($user && $user->hasRole('lecturer') && !EduLecturer::where('user_id', $userId)->exists()) {
                $user->removeRole('lecturer');
            }

            DB::commit();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.lecturer_deleted')
                ]);
            }

            return redirect()->route('superadmin.edu.lecturers.index')
                ->with('success', __('edu.messages.lecturer_deleted'));

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.lecturer_delete_failed', ['error' => $e->getMessage()])
                ], 500);
            }

            return redirect()->back()
                ->with('error', __('edu.messages.lecturer_delete_failed', ['error' => $e->getMessage()]));
        }
    }

    /**
     * Helper function to generate profile image URL
     */
    private function getProfileImageUrl($imagePath)
    {
        if ($imagePath) {
            return asset('storage/' . $imagePath);
        }
        return null;
    }
}
