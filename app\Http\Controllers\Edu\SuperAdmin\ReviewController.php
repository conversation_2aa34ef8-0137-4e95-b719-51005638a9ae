<?php

namespace App\Http\Controllers\Edu\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\EduReview;
use App\Models\EduCourse;
use App\Models\EduLecturer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ReviewController extends Controller
{
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'rating', 'course_id', 'lecturer_id', 'is_published', 'sort', 'direction']);

        $query = EduReview::query()
            ->with(['course.lecturer.user', 'student.user']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('course', function ($courseQuery) use ($search) {
                      $courseQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['rating']) && $filters['rating']) {
            $query->where('rating', $filters['rating']);
        }

        if (isset($filters['course_id']) && $filters['course_id']) {
            $query->where('edu_course_id', $filters['course_id']);
        }

        if (isset($filters['lecturer_id']) && $filters['lecturer_id']) {
            $query->whereHas('course', function ($courseQuery) use ($filters) {
                $courseQuery->where('lecturer_id', $filters['lecturer_id']);
            });
        }

        if (isset($filters['is_published']) && $filters['is_published'] !== '') {
            $query->where('is_published', (bool)$filters['is_published']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'student_name') {
            $query->join('edu_students', 'edu_reviews.edu_student_id', '=', 'edu_students.id')
                  ->join('users', 'edu_students.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_reviews.*');
        } elseif ($sortField === 'course_title') {
            $query->join('edu_courses', 'edu_reviews.edu_course_id', '=', 'edu_courses.id')
                  ->orderBy('edu_courses.title', $direction)
                  ->select('edu_reviews.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $reviews = $query->paginate(10)->withQueryString();

        $courses = EduCourse::with('lecturer.user')->where('status', 'active')->orderBy('title')->get();
        $lecturers = EduLecturer::with('user')->where('status', 'active')->orderBy('id', 'desc')->get();

        return Inertia::render('Edu/Reviews/Index', [
            'reviews' => $reviews,
            'courses' => $courses,
            'lecturers' => $lecturers,
            'filters' => $filters,
        ]);
    }

    public function apiIndex(Request $request)
    {
        $filters = $request->only(['search', 'rating', 'course_id', 'lecturer_id', 'is_published', 'sort', 'direction']);

        $query = EduReview::query()
            ->with(['course.lecturer.user', 'student.user']);

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('course', function ($courseQuery) use ($search) {
                      $courseQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        if (isset($filters['rating']) && $filters['rating']) {
            $query->where('rating', $filters['rating']);
        }

        if (isset($filters['course_id']) && $filters['course_id']) {
            $query->where('edu_course_id', $filters['course_id']);
        }

        if (isset($filters['lecturer_id']) && $filters['lecturer_id']) {
            $query->whereHas('course', function ($courseQuery) use ($filters) {
                $courseQuery->where('lecturer_id', $filters['lecturer_id']);
            });
        }

        if (isset($filters['is_published']) && $filters['is_published'] !== '') {
            $query->where('is_published', (bool)$filters['is_published']);
        }

        $sortField = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';

        if ($sortField === 'student_name') {
            $query->join('edu_students', 'edu_reviews.edu_student_id', '=', 'edu_students.id')
                  ->join('users', 'edu_students.user_id', '=', 'users.id')
                  ->orderBy('users.name', $direction)
                  ->select('edu_reviews.*');
        } elseif ($sortField === 'course_title') {
            $query->join('edu_courses', 'edu_reviews.edu_course_id', '=', 'edu_courses.id')
                  ->orderBy('edu_courses.title', $direction)
                  ->select('edu_reviews.*');
        } else {
            $query->orderBy($sortField, $direction);
        }

        $reviews = $query->paginate(10)->withQueryString();

        return response()->json([
            'data' => $reviews
        ]);
    }

    public function show($id)
    {
        $review = EduReview::with(['course.lecturer.user', 'student.user'])->findOrFail($id);

        return Inertia::render('Edu/Reviews/Show', [
            'review' => $review
        ]);
    }

    public function edit($id)
    {
        $review = EduReview::with(['course.lecturer.user', 'student.user'])->findOrFail($id);

        return Inertia::render('Edu/Reviews/Edit', [
            'review' => $review
        ]);
    }

    public function update(Request $request, $id)
    {
        $review = EduReview::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'is_published' => 'required|boolean',
            'comment' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $review->is_published = $request->is_published;
            if ($request->has('comment')) {
                $review->comment = $request->comment;
            }

            $review->save();

            $this->updateCourseRatingStats($review->edu_course_id);

            DB::commit();

            return redirect()->route('superadmin.edu.reviews.index')
                ->with('success', __('edu.messages.review_updated'));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', __('edu.messages.review_update_failed', ['error' => $e->getMessage()]))
                ->withInput();
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $review = EduReview::findOrFail($id);
            $courseId = $review->edu_course_id;

            $review->delete();

            $this->updateCourseRatingStats($courseId);

            DB::commit();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.review_deleted')
                ]);
            }

            return redirect()->route('superadmin.edu.reviews.index')
                ->with('success', __('edu.messages.review_deleted'));

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.review_delete_failed', ['error' => $e->getMessage()])
                ], 500);
            }

            return redirect()->back()
                ->with('error', __('edu.messages.review_delete_failed', ['error' => $e->getMessage()]));
        }
    }

    /**
     * Toggle review published status
     */
    public function togglePublished(Request $request, $id)
    {
        try {
            $review = EduReview::findOrFail($id);
            $review->is_published = !$review->is_published;
            $review->save();

            $this->updateCourseRatingStats($review->edu_course_id);

            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.review_status_updated'),
                    'is_published' => $review->is_published
                ]);
            }

            return redirect()->back()
                ->with('success', __('edu.messages.review_status_updated'));

        } catch (\Exception $e) {
            if ($request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'message' => __('edu.messages.review_status_update_failed', ['error' => $e->getMessage()])
                ], 500);
            }

            return redirect()->back()
                ->with('error', __('edu.messages.review_status_update_failed', ['error' => $e->getMessage()]));
        }
    }

    /**
     * Bulk update review statuses
     */
    public function bulkUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:publish,unpublish,delete',
            'review_ids' => 'required|array',
            'review_ids.*' => 'exists:edu_reviews,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            $reviews = EduReview::whereIn('id', $request->review_ids)->get();
            $courseIds = $reviews->pluck('edu_course_id')->unique();

            switch ($request->action) {
                case 'publish':
                    EduReview::whereIn('id', $request->review_ids)->update(['is_published' => true]);
                    $message = __('edu.messages.reviews_published');
                    break;
                case 'unpublish':
                    EduReview::whereIn('id', $request->review_ids)->update(['is_published' => false]);
                    $message = __('edu.messages.reviews_unpublished');
                    break;
                case 'delete':
                    EduReview::whereIn('id', $request->review_ids)->delete();
                    $message = __('edu.messages.reviews_deleted');
                    break;
            }

            foreach ($courseIds as $courseId) {
                $this->updateCourseRatingStats($courseId);
            }

            DB::commit();

            return response()->json(['message' => $message]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => __('edu.messages.bulk_update_failed', ['error' => $e->getMessage()])
            ], 500);
        }
    }

    /**
     * Update course rating statistics
     */
    private function updateCourseRatingStats($courseId)
    {
        $course = EduCourse::findOrFail($courseId);

        $publishedReviews = EduReview::where('edu_course_id', $courseId)
            ->where('is_published', true)
            ->get();

        $course->rating = $publishedReviews->avg('rating') ?? 0;
        $course->total_reviews = $publishedReviews->count();
        $course->save();

        $lecturer = $course->lecturer;
        if ($lecturer) {
            $lecturerCourseIds = $lecturer->courses()->pluck('id');
            $lecturerReviews = EduReview::whereIn('edu_course_id', $lecturerCourseIds)
                ->where('is_published', true)
                ->get();

            $lecturer->rating = $lecturerReviews->avg('rating') ?? 0;
            $lecturer->total_reviews = $lecturerReviews->count();
            $lecturer->save();
        }
    }
}
