<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class ErrorController extends Controller
{
    /**
     * Display the unauthorized error page.
     */
    public function unauthorized(Request $request)
    {
        $role = session('unauthorized_role', 'trang này');
        $message = session('unauthorized_message');

        return Inertia::render('Errors/Unauthorized', [
            'role' => $role,
            'message' => $message,
        ]);
    }

    /**
     * Redirect to unauthorized page after setting session values.
     */
    public function redirectToUnauthorized(Request $request)
    {
        $role = $request->input('role', 'trang này');
        $message = $request->input('message');

        session([
            'unauthorized_role' => $role,
            'unauthorized_message' => $message,
        ]);

        return redirect()->route('unauthorized');
    }
}