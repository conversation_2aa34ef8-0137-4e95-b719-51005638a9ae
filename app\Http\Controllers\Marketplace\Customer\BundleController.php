<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Models\ProductBundle;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Routing\Controller;

class BundleController extends Controller
{
    /**
     * Display all active bundles.
     */
    public function index(Request $request)
    {
        $query = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');


        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }


        if ($request->filled('category')) {
            $categorySlug = $request->get('category');
            $query->whereHas('items.product.category', function ($q) use ($categorySlug) {
                $q->where('slug', $categorySlug);
            });
        }


        if ($request->filled('min_price')) {
            $query->where('bundle_price', '>=', $request->get('min_price'));
        }
        if ($request->filled('max_price')) {
            $query->where('bundle_price', '<=', $request->get('max_price'));
        }


        switch ($request->get('sort', 'featured')) {
            case 'price_low':
                $query->orderBy('bundle_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('bundle_price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('is_featured', 'desc')
                    ->orderBy('sort_order', 'asc');
                break;
        }

        $bundles = $query->paginate(12)->withQueryString();


        $categories = Category::whereHas('products.bundleItems.bundle', function ($q) {
            $q->where('is_active', true);
        })->orderBy('name')->get();


        $allParentCategories = Category::with([
            'children' => function ($q) {
                $q->where('status', true);
            }
        ])
            ->whereNull('parent_id')
            ->where('status', true)
            ->orderBy('name')
            ->get();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Bundle/BundleList', [
            'bundles' => $bundles,
            'categories' => $categories,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'filters' => $request->only(['search', 'category', 'min_price', 'max_price', 'sort']),
        ]);
    }

    /**
     * Display featured bundles.
     */
    public function featured(Request $request)
    {
        $bundles = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate(12)
            ->withQueryString();


        $allParentCategories = Category::with([
            'children' => function ($q) {
                $q->where('status', true);
            }
        ])
            ->whereNull('parent_id')
            ->where('status', true)
            ->orderBy('name')
            ->get();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Bundle/FeaturedBundles', [
            'bundles' => $bundles,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
        ]);
    }

    /**
     * Display a specific bundle detail.
     */
    public function show(Request $request, $slug)
    {
        $bundle = ProductBundle::with([
            'items.product.category'
        ])
            ->where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();


        $categoryIds = $bundle->items->pluck('product.category_id')->unique()->filter();
        $relatedBundles = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->where('id', '!=', $bundle->id)
            ->whereHas('items.product', function ($q) use ($categoryIds) {
                $q->whereIn('category_id', $categoryIds);
            })
            ->orderBy('is_featured', 'desc')
            ->take(6)
            ->get();


        $allParentCategories = Category::with([
            'children' => function ($q) {
                $q->where('status', true);
            }
        ])
            ->whereNull('parent_id')
            ->where('status', true)
            ->orderBy('name')
            ->get();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Bundle/BundleDetail', [
            'bundle' => $bundle,
            'relatedBundles' => $relatedBundles,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
        ]);
    }

    /**
     * Search bundles.
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (empty($query)) {
            return redirect()->route('marketplace.bundles.index');
        }

        $bundles = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhereHas('items.product', function ($subQ) use ($query) {
                        $subQ->where('name', 'like', "%{$query}%")
                            ->orWhere('description', 'like', "%{$query}%");
                    });
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order', 'asc')
            ->paginate(12)
            ->withQueryString();


        $allParentCategories = Category::with([
            'children' => function ($q) {
                $q->where('status', true);
            }
        ])
            ->whereNull('parent_id')
            ->where('status', true)
            ->orderBy('name')
            ->get();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Bundle/BundleSearch', [
            'bundles' => $bundles,
            'query' => $query,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
        ]);
    }

    /**
     * Get bundle data for AJAX requests.
     */
    public function getBundleData(Request $request, $id)
    {
        $bundle = ProductBundle::with(['items.product.category'])
            ->where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'bundle' => $bundle,
        ]);
    }
}
