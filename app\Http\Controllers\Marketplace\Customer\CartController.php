<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Cart;
use App\Models\Product;
use App\Models\ProductBundle;
use App\Models\Category;
use App\Models\PaymentMethod;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\MarketCoupon;
use App\Models\MarketOrder;
use Illuminate\Support\Facades\DB;
use App\Services\ShippingService;

class CartController extends Controller
{
    public function index()
    {
        $cartItems = [];
        $cartCount = 0;
        $firstTimeDiscount = null;
        $upsellProducts = [];
        $crossSellProducts = [];

        if (Auth::check()) {
            $cartItems = Cart::where('user_id', Auth::id())
                ->with(['product' => function($query) {
                    $query->with('category');
                }, 'bundle' => function($query) {
                    $query->with(['items.product.category']);
                }])
                ->get()
                ->map(function($cart) {
                    if ($cart->item_type === 'bundle') {

                        if (!$cart->bundle) {
                            return [
                                'id' => $cart->id,
                                'bundle_id' => $cart->bundle_id,
                                'name' => 'Gói sản phẩm không tồn tại',
                                'slug' => '',
                                'image' => null,
                                'price' => $cart->price,
                                'current_price' => 0,
                                'quantity' => $cart->quantity,
                                'item_type' => 'bundle',
                                'bundle_items' => [],
                                'subtotal' => $cart->quantity * $cart->price,
                                'is_available' => false,
                                'error' => 'Bundle không tồn tại'
                            ];
                        }

                        return [
                            'id' => $cart->id,
                            'bundle_id' => $cart->bundle_id,
                            'name' => $cart->bundle->name,
                            'slug' => $cart->bundle->slug,
                            'image' => $cart->bundle->image_url,
                            'price' => $cart->price,
                            'current_price' => $cart->bundle->bundle_price,
                            'quantity' => $cart->quantity,
                            'item_type' => 'bundle',
                            'bundle_items' => $cart->bundle->items ? $cart->bundle->items->map(function($item) {
                                return [
                                    'product_name' => $item->product ? $item->product->name : 'Sản phẩm không tồn tại',
                                    'quantity' => $item->quantity,
                                    'category' => $item->product && $item->product->category ? $item->product->category->name : '',
                                ];
                            }) : [],
                            'subtotal' => $cart->quantity * $cart->price,
                            'is_available' => $cart->bundle->is_active,
                        ];
                    } else {

                        if (!$cart->product) {
                            return [
                                'id' => $cart->id,
                                'product_id' => $cart->product_id,
                                'name' => 'Sản phẩm không tồn tại',
                                'slug' => '',
                                'image' => null,
                                'price' => $cart->price,
                                'current_price' => 0,
                                'quantity' => $cart->quantity,
                                'item_type' => 'product',
                                'category' => '',
                                'stock_quantity' => 0,
                                'subtotal' => $cart->quantity * $cart->price,
                                'status' => false,
                                'is_available' => false,
                                'error' => 'Sản phẩm không tồn tại'
                            ];
                        }

                        return [
                            'id' => $cart->id,
                            'product_id' => $cart->product_id,
                            'name' => $cart->product->name,
                            'slug' => $cart->product->slug,
                            'image' => $cart->product->image_url_formatted ?? $cart->product->image_url,
                            'price' => $cart->price,
                            'current_price' => $cart->product->sale_price,
                            'quantity' => $cart->quantity,
                            'item_type' => 'product',
                            'category' => $cart->product->category ? $cart->product->category->name : '',
                            'stock_quantity' => $cart->product->quantity,
                            'subtotal' => $cart->quantity * $cart->price,
                            'status' => $cart->product->status,
                            'is_available' => $cart->product->status && $cart->product->quantity > 0,
                        ];
                    }
                });
            $cartCount = $cartItems->count();

            // Get first-time discount info
            $firstTimeDiscount = $this->getFirstTimeDiscountInfo();

            // Get product recommendations
            if ($cartItems->count() > 0) {
                $productIds = $cartItems->filter(function($item) {
                    return $item['item_type'] === 'product';
                })->pluck('product_id')->toArray();

                $categoryIds = $cartItems->filter(function($item) {
                    return !empty($item['category']) && $item['item_type'] === 'product';
                })->pluck('product_id')->toArray();

                if (count($productIds) > 0) {
                    $crossSellProducts = $this->getRelatedProducts($productIds, $categoryIds, 'cross_sell');
                    $upsellProducts = $this->getRelatedProducts($productIds, $categoryIds, 'upsell');
                }
            }
        }

        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        $paymentMethods = $this->getEnabledPaymentMethods();

        return Inertia::render('Marketplace/Public/Cart/Cart', [
            'cartItems' => $cartItems,
            'cartCount' => $cartCount,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'paymentMethods' => $paymentMethods,
            'csrf_token' => csrf_token(),
            'firstTimeDiscount' => $firstTimeDiscount,
            'upsellProducts' => $upsellProducts,
            'crossSellProducts' => $crossSellProducts,
        ]);
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'nullable|exists:products,id',
            'bundle_id' => 'nullable|exists:product_bundles,id',
            'quantity' => 'required|integer|min:1',
        ]);


        if ((!$request->product_id && !$request->bundle_id) || ($request->product_id && $request->bundle_id)) {
            return response()->json(['error' => 'Vui lòng chọn sản phẩm hoặc gói sản phẩm'], 400);
        }

        $userId = Auth::id();
        $itemType = $request->product_id ? 'product' : 'bundle';
        $itemId = $request->product_id ?: $request->bundle_id;
        $cacheKey = "cart_add_{$userId}_{$itemType}_{$itemId}";

        if (Cache::has($cacheKey)) {
            return response()->json(['error' => 'Yêu cầu đang được xử lý'], 429);
        }

        Cache::put($cacheKey, true, 2);

        try {
            if ($itemType === 'product') {
                return $this->addProduct($request);
            } else {
                return $this->addBundle($request);
            }
        } finally {
            Cache::forget($cacheKey);
        }
    }

    private function addProduct(Request $request)
    {
        $product = Product::findOrFail($request->product_id);

        if (!$product->status) {
            return response()->json(['error' => 'Sản phẩm không khả dụng'], 400);
        }

        if ($request->quantity > $product->quantity) {
            return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
        }

        $quantityToAdd = 1;

        if (Auth::check()) {
            $cart = Cart::where('user_id', Auth::id())
                ->where('product_id', $request->product_id)
                ->where('item_type', 'product')
                ->first();

            if ($cart) {
                $newQuantity = $cart->quantity + $quantityToAdd;
                if ($newQuantity > $product->quantity) {
                    return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
                }
                $cart->update(['quantity' => $newQuantity]);
            } else {
                Cart::create([
                    'user_id' => Auth::id(),
                    'product_id' => $request->product_id,
                    'quantity' => $quantityToAdd,
                    'price' => $product->sale_price,
                    'item_type' => 'product',
                ]);
            }

            $cartCount = Cart::where('user_id', Auth::id())->count();
        } else {
            $cartCount = 0;
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã thêm sản phẩm vào giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    private function addBundle(Request $request)
    {
        $bundle = ProductBundle::findOrFail($request->bundle_id);

        if (!$bundle->is_active) {
            return response()->json(['error' => 'Gói sản phẩm không khả dụng'], 400);
        }

        $quantityToAdd = 1;

        if (Auth::check()) {
            $cart = Cart::where('user_id', Auth::id())
                ->where('bundle_id', $request->bundle_id)
                ->where('item_type', 'bundle')
                ->first();

            if ($cart) {
                $newQuantity = $cart->quantity + $quantityToAdd;
                $cart->update(['quantity' => $newQuantity]);
            } else {
                Cart::create([
                    'user_id' => Auth::id(),
                    'bundle_id' => $request->bundle_id,
                    'quantity' => $quantityToAdd,
                    'price' => $bundle->bundle_price,
                    'item_type' => 'bundle',
                ]);
            }

            $cartCount = Cart::where('user_id', Auth::id())->count();
        } else {
            $cartCount = 0;
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã thêm gói sản phẩm vào giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $cart = Cart::where('user_id', Auth::id())->findOrFail($id);


        if ($cart->item_type === 'product') {
            $product = $cart->product;
            if ($request->quantity > $product->quantity) {
                return response()->json(['error' => 'Số lượng vượt quá tồn kho'], 400);
            }
        }

        $cart->update(['quantity' => $request->quantity]);

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật giỏ hàng'
        ]);
    }

    public function remove($id)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $cart = Cart::where('user_id', Auth::id())->findOrFail($id);
        $cart->delete();

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa khỏi giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    public function getCount()
    {
        if (Auth::check()) {
            $count = Cart::where('user_id', Auth::id())->count();
        } else {
            $count = 0;
        }

        return response()->json(['count' => $count]);
    }

    public function syncFromStorage(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        foreach ($request->items as $item) {
            $product = Product::find($item['product_id']);
            if (!$product || !$product->status) continue;

            $cart = Cart::where('user_id', Auth::id())
                ->where('product_id', $item['product_id'])
                ->first();

            if ($cart) {
                $cart->update([
                    'quantity' => min($item['quantity'], $product->quantity),
                    'price' => $product->sale_price
                ]);
            } else {
                Cart::create([
                    'user_id' => Auth::id(),
                    'product_id' => $item['product_id'],
                    'quantity' => min($item['quantity'], $product->quantity),
                    'price' => $product->sale_price,
                ]);
            }
        }

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => 'Đã đồng bộ giỏ hàng',
            'cart_count' => $cartCount
        ]);
    }

    public function removeSelected(Request $request)
    {
        $request->validate([
            'item_ids' => 'required|array',
            'item_ids.*' => 'required|integer'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $deletedCount = Cart::where('user_id', Auth::id())
            ->whereIn('id', $request->item_ids)
            ->delete();

        $cartCount = Cart::where('user_id', Auth::id())->count();

        return response()->json([
            'success' => true,
            'message' => "Đã xóa {$deletedCount} sản phẩm khỏi giỏ hàng",
            'cart_count' => $cartCount
        ]);
    }

    public function updateSelected(Request $request)
    {
        $request->validate([
            'updates' => 'required|array',
            'updates.*.id' => 'required|integer',
            'updates.*.quantity' => 'required|integer|min:1'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $errors = [];
        $updated = 0;

        foreach ($request->updates as $update) {
            $cart = Cart::where('user_id', Auth::id())->find($update['id']);
            if (!$cart) {
                $errors[] = "Cart item {$update['id']} not found";
                continue;
            }

            $product = $cart->product;
            if ($update['quantity'] > $product->quantity) {
                $errors[] = "Số lượng vượt quá tồn kho cho sản phẩm: {$product->name}";
                continue;
            }

            $cart->update(['quantity' => $update['quantity']]);
            $updated++;
        }

        return response()->json([
            'success' => true,
            'message' => "Đã cập nhật {$updated} sản phẩm",
            'errors' => $errors
        ]);
    }

    private function getEnabledPaymentMethods()
    {
        $methods = [];

        $cashSettings = SystemSettingService::get('market_cash_payment', ['active' => false]);
        if ($cashSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'cash')->value('logo_url');
            $methods[] = [
                'id' => 'cash',
                'payment_code' => 'cash',
                'payment_name' => 'Thanh toán khi nhận hàng (COD)',
                'description' => 'Thanh toán bằng tiền mặt khi nhận hàng',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Banknote',
                'provider' => 'cash'
            ];
        }

        $momoSettings = SystemSettingService::get('market_momo_payment', ['active' => false]);
        if ($momoSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'momo')->value('logo_url');
            $methods[] = [
                'id' => 'momo',
                'payment_code' => 'momo',
                'payment_name' => 'Ví điện tử MoMo',
                'description' => 'Thanh toán qua ví điện tử MoMo',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Smartphone',
                'provider' => 'momo'
            ];
        }

        $vnpaySettings = SystemSettingService::get('market_vnpay_payment', ['active' => false]);
        if ($vnpaySettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'vnpay')->value('logo_url');
            $methods[] = [
                'id' => 'vnpay',
                'payment_code' => 'vnpay',
                'payment_name' => 'VNPay',
                'description' => 'Thanh toán qua cổng VNPay',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'CreditCard',
                'provider' => 'vnpay'
            ];
        }

        $bankSettings = SystemSettingService::get('market_bank_transfer', ['active' => false]);
        if ($bankSettings['active'] ?? false) {
            $logoUrl = PaymentMethod::where('payment_code', 'bank_transfer')->value('logo_url');
            $methods[] = [
                'id' => 'bank_transfer',
                'payment_code' => 'bank_transfer',
                'payment_name' => 'Chuyển khoản ngân hàng',
                'description' => 'Chuyển khoản trực tiếp đến tài khoản ngân hàng',
                'logo_url' => $this->getValidLogoUrl($logoUrl),
                'icon' => 'Building2',
                'provider' => 'bank_transfer'
            ];
        }

        return $methods;
    }

    private function getValidLogoUrl($logoUrl)
    {
        if (!$logoUrl) {
            return null;
        }

        $fullPath = public_path('storage/' . ltrim($logoUrl, '/'));

        if (!file_exists($fullPath) || !is_readable($fullPath)) {
            return null;
        }

        return '/storage/' . ltrim($logoUrl, '/');
    }

    private function getFirstTimeDiscountInfo()
    {
        if (!Auth::check()) {
            return null;
        }

        $user = Auth::user();

        $hasOrders = MarketOrder::where('user_id', $user->id)->exists();
        if ($hasOrders) {
            return null;
        }

        $coupon = MarketCoupon::where('is_active', true)
            ->where('first_order_only', true)
            ->first();

        if (!$coupon || !$coupon->isValid()) {
            return null;
        }

        // Return discount data without auto-apply flag
        return [
            'code' => $coupon->code,
            'name' => $coupon->name,
            'description' => $coupon->description,
            'type' => $coupon->type,
            'value' => $coupon->value,
            'discountText' => $coupon->type === 'percentage' ? "{$coupon->value}%" : number_format($coupon->value, 0, ',', '.') . 'đ',
            'auto_apply' => false, // Set to false to prevent automatic application
        ];
    }

    /**
     * Automatically apply first time discount
     */
    public function autoApplyFirstTimeDiscount(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $firstTimeDiscount = $this->getFirstTimeDiscountInfo();

        if (!$firstTimeDiscount) {
            return response()->json([
                'success' => false,
                'message' => 'Không có mã giảm giá khả dụng cho khách hàng mới'
            ]);
        }

        // Calculate subtotal from cart
        $cartItems = Cart::where('user_id', Auth::id())->get();
        $subtotal = $cartItems->sum(function($item) {
            return $item->price * $item->quantity;
        });

        // Validate and calculate discount
        $coupon = MarketCoupon::where('code', $firstTimeDiscount['code'])->first();
        $discount = 0;

        if ($coupon->type === 'percentage') {
            $discount = ($subtotal * $coupon->value) / 100;
        } else {
            $discount = $coupon->value;
        }

        if ($discount > $subtotal) {
            $discount = $subtotal;
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã áp dụng mã giảm giá dành cho khách hàng mới',
            'discount' => $discount,
            'coupon' => [
                'code' => $coupon->code,
                'type' => $coupon->type,
                'value' => $coupon->value
            ]
        ]);
    }

    /**
     * Get related products for cross-sell or upsell
     *
     * @param array $productIds
     * @param array $categoryIds
     * @param string $type 'cross_sell' or 'upsell'
     * @return array
     */
    private function getRelatedProducts($productIds, $categoryIds, $type = 'cross_sell')
    {
        $query = Product::where('status', true)
            ->where('quantity', '>', 0)
            ->whereNotIn('id', $productIds)
            ->limit(4);

        // For cross-selling, suggest complementary products in the same categories
        if ($type === 'cross_sell') {
            $query->whereIn('category_id', function($q) use ($productIds) {
                $q->select('category_id')
                  ->from('products')
                  ->whereIn('id', $productIds)
                  ->groupBy('category_id');
            });
        }

        // For upselling, suggest higher-priced products in the same category
        if ($type === 'upsell') {
            // Get average price of cart items
            $avgPrice = Product::whereIn('id', $productIds)->avg('sale_price');

            // Get products with higher price in similar categories
            $query->whereIn('category_id', function($q) use ($productIds) {
                $q->select('category_id')
                  ->from('products')
                  ->whereIn('id', $productIds)
                  ->groupBy('category_id');
            })
            ->where('sale_price', '>', $avgPrice)
            ->orderBy('sale_price', 'asc');
        }

        return $query->with('category')
            ->get()
            ->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'image' => $product->image_url_formatted ?? $product->image_url,
                    'price' => $product->sale_price,
                    'category' => $product->category ? $product->category->name : '',
                    'stock_quantity' => $product->quantity,
                ];
            });
    }

    /**
     * Extract weight in grams from weight string
     *
     * @param string|null $weightString
     * @return int Weight in grams
     */
    private function extractWeightInGrams($weightString)
    {
        if (empty($weightString)) {
            return 200; // Default weight if not specified
        }

        // Convert to lowercase for consistent handling
        $weightString = strtolower($weightString);

        // Check if it contains "kg" for kilograms
        if (strpos($weightString, 'kg') !== false) {
            // Extract numeric value and convert to grams
            $kg = (float) preg_replace('/[^0-9\.]/', '', $weightString);
            return (int) ($kg * 1000);
        }

        // Default case: assume grams or just a number
        return (int) preg_replace('/[^0-9]/', '', $weightString);
    }

    /**
     * Calculate total weight from cart items
     *
     * @param \Illuminate\Database\Eloquent\Collection $cartItems
     * @return int Total weight in grams
     */
    private function calculateTotalWeight($cartItems)
    {
        $totalWeight = 0;

        foreach ($cartItems as $item) {
            if ($item->item_type === 'product' && $item->product) {
                // Extract weight in grams
                $productWeight = $this->extractWeightInGrams($item->product->weight);
                $itemWeight = $productWeight * $item->quantity;
                $totalWeight += $itemWeight;

                // Log for debugging
                Log::debug("Product: {$item->product->name}, Weight: {$productWeight}g, Quantity: {$item->quantity}, Total: {$itemWeight}g");
            }
            elseif ($item->item_type === 'bundle' && $item->bundle) {
                // For bundles, calculate weight of all included products
                $bundleWeight = 0;
                foreach ($item->bundle->items as $bundleItem) {
                    if ($bundleItem->product) {
                        $productWeight = $this->extractWeightInGrams($bundleItem->product->weight);
                        $itemWeight = $productWeight * $bundleItem->quantity * $item->quantity;
                        $bundleWeight += $itemWeight;

                        // Log for debugging
                        Log::debug("Bundle Item: {$bundleItem->product->name}, Weight: {$productWeight}g, Quantity: {$bundleItem->quantity}x{$item->quantity}, Total: {$itemWeight}g");
                    }
                }
                $totalWeight += $bundleWeight;

                // Log for debugging
                Log::debug("Bundle: {$item->bundle->name}, Total Weight: {$bundleWeight}g");
            }
        }

        // Ensure minimum weight of 100 grams
        $finalWeight = max(100, $totalWeight);

        // Log final weight
        Log::debug("Final calculated weight: {$finalWeight}g");

        return $finalWeight;
    }

    /**
     * Prepare cart items for detailed shipping calculation
     *
     * @param \Illuminate\Database\Eloquent\Collection $cartItems
     * @return array Items formatted for shipping API
     */
    private function prepareItemsForShipping($cartItems)
    {
        $items = [];

        foreach ($cartItems as $item) {
            if ($item->item_type === 'product' && $item->product) {
                $items[] = [
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'weight' => $this->extractWeightInGrams($item->product->weight)
                ];
            }
            elseif ($item->item_type === 'bundle' && $item->bundle) {
                // For bundles, add each product as a separate item
                foreach ($item->bundle->items as $bundleItem) {
                    if ($bundleItem->product) {
                        $items[] = [
                            'name' => $bundleItem->product->name,
                            'quantity' => $bundleItem->quantity * $item->quantity,
                            'weight' => $this->extractWeightInGrams($bundleItem->product->weight)
                        ];
                    }
                }
            }
        }

        return $items;
    }

    /**
     * Calculate shipping cost using GHN API or fallback to static pricing
     * Adds caching to reduce API calls
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateShipping(Request $request)
    {
        if (!$request->has(['province_id', 'district_id', 'ward_code'])) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        $request->validate([
            'province_id' => 'required|integer',
            'district_id' => 'required|integer',
            'ward_code' => 'required|string',
            'weight' => 'nullable|integer',
            'subtotal' => 'nullable|numeric'
        ]);

        $subtotal = $request->subtotal ?? 0;

        // Generate cache key based on shipping parameters
        $cacheKey = "shipping_calc_{$request->province_id}_{$request->district_id}_{$request->ward_code}_{$subtotal}";

        // Try to get response from cache (cache for 5 minutes)
        $cachedResponse = Cache::get($cacheKey);
        if ($cachedResponse) {
            Log::info("Using cached shipping calculation: {$cacheKey}");
            return response()->json($cachedResponse);
        }

        // Check free shipping threshold
        $freeShippingThreshold = SystemSettingService::get('free_shipping_threshold', 0);
        if ($freeShippingThreshold > 0 && $subtotal >= $freeShippingThreshold) {
            $freeShippingResponse = [
                'success' => true,
                'fee' => 0,
                'message' => 'Free shipping applied',
                'is_free' => true,
                'dynamic' => false
            ];

            // Cache the free shipping response
            Cache::put($cacheKey, $freeShippingResponse, now()->addMinutes(5));
            return response()->json($freeShippingResponse);
        }

        // Get cart items with product details to calculate accurate weight
        $cartItems = [];
        if (Auth::check()) {
            $cartItems = Cart::where('user_id', Auth::id())
                ->with(['product' => function($query) {
                    $query->select('id', 'name', 'weight');
                }, 'bundle' => function($query) {
                    $query->with(['items.product' => function($q) {
                        $q->select('id', 'name', 'weight');
                    }]);
                }])
                ->get();
        }

        // Calculate total weight based on products in cart
        $totalWeight = $this->calculateTotalWeight($cartItems);

        // Use provided weight or calculated weight, with fallback to default
        $weight = $request->weight ?? $totalWeight ?? 500;

        // Log the weight being used
        Log::info("Shipping calculation using weight: {$weight}g (request weight: " . ($request->weight ?? 'null') . ", calculated weight: {$totalWeight}g)");

        // Format cart items for detailed shipping calculation
        $itemsForShipping = $this->prepareItemsForShipping($cartItems);

        // Calculate shipping cost
        $shippingData = [
            'province_id' => $request->province_id,
            'district_id' => $request->district_id,
            'ward_code' => $request->ward_code,
            'weight' => $weight,
            'items' => $itemsForShipping
        ];

        $result = ShippingService::calculateShippingFee($shippingData);

        $response = [
            'success' => $result['success'] ?? false,
            'fee' => $result['fee'] ?? 0,
            'message' => $result['message'] ?? 'Shipping fee calculated',
            'is_free' => false,
            'dynamic' => $result['dynamic'] ?? false,
            'extra_data' => $result['extra_data'] ?? []
        ];

        // Include available services if provided
        if (isset($result['services']) && !empty($result['services'])) {
            $response['services'] = $result['services'];
        }

        // Cache the result for 5 minutes
        if ($response['success']) {
            Cache::put($cacheKey, $response, now()->addMinutes(5));
        }

        return response()->json($response);
    }
}
