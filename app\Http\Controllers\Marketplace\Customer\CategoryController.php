<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CategoryController extends Controller
{
    public function publicIndex(Request $request)
    {

        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {

                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();


                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }


                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();


        $allCategories = Category::where('status', true)
            ->withCount(['products' => function($query) {
                $query->where('status', true);
            }])
            ->orderBy('name', 'asc')
            ->get();


        $topCategoriesForHeader = $allParentCategories->take(5);
        $moreCategoriesForHeader = $allParentCategories->slice(5);


        $brands = Product::where('status', true)
            ->whereNotNull('brand')
            ->where('brand', '!=', '')
            ->select('brand')
            ->distinct()
            ->take(12)
            ->pluck('brand')
            ->map(function($brand) {
                return [
                    'id' => md5($brand),
                    'name' => $brand,
                    'image' => "https://picsum.photos/200/100?random=" . rand(71, 82) // Placeholder images
                ];
            })
            ->toArray();

        return Inertia::render('Marketplace/Public/Categories/Categories', [
            'parentCategories' => $allParentCategories,
            'allCategories' => $allCategories,
            'topCategories' => $topCategoriesForHeader,
            'moreCategories' => $moreCategoriesForHeader,
            'brands' => $brands
        ]);
    }
}
