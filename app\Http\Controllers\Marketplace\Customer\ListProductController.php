<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\MarketProductReview;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ListProductController extends Controller
{
    private function convertToGrams($weight)
    {
        if (empty($weight)) return null;

        $weight = trim($weight);
        if (preg_match('/^([\d.]+)\s*(g|kg|gram|kilogram|grams|kilograms)?$/i', $weight, $matches)) {
            $value = floatval($matches[1]);
            $unit = strtolower($matches[2] ?? 'g');

            switch ($unit) {
                case 'kg':
                case 'kilogram':
                case 'kilograms':
                    return $value * 1000;
                default:
                    return $value;
            }
        }

        return null;
    }

    private function formatWeight($grams)
    {
        if ($grams >= 1000) {
            return number_format($grams/1000, 1) . ' kg';
        }
        return number_format($grams) . ' g';
    }

    public function index(Request $request, $slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {

                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();


                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }


                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();


        $categoryIds = [$category->id];
        if ($category->parent_id === null) {
            $childrenIds = Category::where('parent_id', $category->id)
                ->where('status', true)
                ->pluck('id')
                ->toArray();
            $categoryIds = array_merge($categoryIds, $childrenIds);
        }


        $query = Product::whereIn('category_id', $categoryIds)
            ->where('status', true)
            ->with(['category']);


        $activeFilters = [];


        $brand = $request->filled('brand') ? trim($request->brand) : null;
        $weightRange = $request->filled('weight_range') ? trim($request->weight_range) : null;


        $minPrice = null;
        $maxPrice = null;

        if ($request->filled('min_price')) {
            $minPrice = (float)$request->min_price;
            if ($minPrice < 0) $minPrice = null;
        }

        if ($request->filled('max_price')) {
            $maxPrice = (float)$request->max_price;
            if ($maxPrice < 0) $maxPrice = null;
        }


        if ($minPrice !== null && $maxPrice !== null && $minPrice > $maxPrice) {
            $temp = $minPrice;
            $minPrice = $maxPrice;
            $maxPrice = $temp;
        }

        $sort = $request->filled('sort') ? trim($request->sort) : 'featured';


        if ($brand) {
            $query->where('brand', $brand);
            $activeFilters[] = [
                'type' => 'brand',
                'label' => __('product.brand') . ': ' . $brand,
                'value' => $brand
            ];
        }


        $brands = Product::whereIn('category_id', $categoryIds)
            ->where('status', true)
            ->whereNotNull('brand')
            ->where('brand', '!=', '')
            ->select('brand')
            ->distinct()
            ->orderBy('brand')
            ->pluck('brand')
            ->toArray();


        $allProducts = Product::whereIn('category_id', $categoryIds)
            ->where('status', true)
            ->whereNotNull('weight')
            ->where('weight', '!=', '')
            ->get();

        $weights = $allProducts
            ->map(function ($product) {
                return $this->convertToGrams($product->weight);
            })
            ->filter()
            ->unique()
            ->sort()
            ->values()
            ->toArray();

        $weightRanges = [];
        if (!empty($weights)) {
            $minWeight = min($weights);
            $maxWeight = max($weights);


            if (count($weights) <= 4) {

                $uniqueWeights = array_unique($weights);
                sort($uniqueWeights);

                foreach ($uniqueWeights as $index => $weight) {
                    $count = collect($weights)->filter(function($w) use ($weight) {
                        return $w == $weight;
                    })->count();

                    if ($count > 0) {
                        $weightRanges[] = [
                            'id' => "exact_$index",
                            'name' => $this->formatWeight($weight),
                            'min' => $weight,
                            'max' => $weight,
                            'count' => $count
                        ];
                    }
                }
            } else {

                $step = ($maxWeight - $minWeight) / 4;


                if ($step <= 0) {

                    $count = count($weights);
                    $weightRanges[] = [
                        'id' => "single_range",
                        'name' => $this->formatWeight($minWeight),
                        'min' => $minWeight,
                        'max' => $maxWeight,
                        'count' => $count
                    ];
                } else {
                    $createdRanges = [];

                    for ($i = 0; $i < 4; $i++) {
                        $rangeMin = $minWeight + ($i * $step);
                        $rangeMax = $i === 3 ? $maxWeight : $minWeight + (($i + 1) * $step);


                        $count = collect($weights)->filter(function($weight) use ($rangeMin, $rangeMax) {
                            return $weight >= $rangeMin && $weight <= $rangeMax;
                        })->count();


                        if ($count > 0) {
                            $rangeName = $this->formatWeight($rangeMin) . ' - ' . $this->formatWeight($rangeMax);


                            $isDuplicate = false;
                            foreach ($createdRanges as $existingRange) {
                                if ($existingRange['name'] === $rangeName) {
                                    $isDuplicate = true;
                                    break;
                                }
                            }

                            if (!$isDuplicate) {
                                $newRange = [
                                    'id' => "range_$i",
                                    'name' => $rangeName,
                                    'min' => $rangeMin,
                                    'max' => $rangeMax,
                                    'count' => $count
                                ];

                                $createdRanges[] = $newRange;
                            }
                        }
                    }

                    $weightRanges = $createdRanges;
                }
            }
        }


        if ($weightRange) {
            $selectedRange = collect($weightRanges)->firstWhere('id', $weightRange);
            if ($selectedRange) {
                $query->where(function($q) use ($selectedRange) {
                    $min = $selectedRange['min'];
                    $max = $selectedRange['max'];


                    if ($min == $max) {

                        $q->where(function($subQ) use ($min) {

                            $subQ->whereRaw("CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2)) = ?", [$min])
                                 ->whereRaw("weight NOT LIKE '%kg%'");
                        })->orWhere(function($subQ) use ($min) {

                            $minKg = $min / 1000;
                            $subQ->whereRaw("CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2)) = ?", [$minKg])
                                 ->whereRaw("weight LIKE '%kg%'");
                        });
                    } else {

                        $q->where(function($subQ) use ($min, $max) {

                            $subQ->whereRaw("CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2)) BETWEEN ? AND ?", [$min, $max])
                                 ->whereRaw("weight NOT LIKE '%kg%'");
                        })->orWhere(function($subQ) use ($min, $max) {

                            $minKg = $min / 1000;
                            $maxKg = $max / 1000;
                            $subQ->whereRaw("CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2)) BETWEEN ? AND ?", [$minKg, $maxKg])
                                 ->whereRaw("weight LIKE '%kg%'");
                        });
                    }
                });

                $activeFilters[] = [
                    'type' => 'weight_range',
                    'label' => __('product.weight') . ': ' . $selectedRange['name'],
                    'value' => $weightRange
                ];
            }
        }


        if ($minPrice !== null || $maxPrice !== null) {
            if ($minPrice !== null && $maxPrice !== null) {
                $query->whereBetween('sale_price', [$minPrice, $maxPrice]);
            } elseif ($minPrice !== null) {
                $query->where('sale_price', '>=', $minPrice);
            } elseif ($maxPrice !== null) {
                $query->where('sale_price', '<=', $maxPrice);
            }


            $priceLabel = __('product.price_range') . ': ';
            if ($minPrice !== null) {
                $priceLabel .= number_format($minPrice, 0, '.', ',') . 'đ';
            } else {
                $priceLabel .= '0đ';
            }
            $priceLabel .= ' - ';
            if ($maxPrice !== null) {
                $priceLabel .= number_format($maxPrice, 0, '.', ',') . 'đ';
            } else {
                $priceLabel .= '∞';
            }

            $activeFilters[] = [
                'type' => 'price_range',
                'label' => $priceLabel,
                'value' => ['min' => $minPrice, 'max' => $maxPrice]
            ];
        }


        switch ($sort) {
            case 'price-asc':
                $query->orderBy('sale_price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('sale_price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'name-asc':
                $query->orderBy('name', 'asc');
                break;
            case 'name-desc':
                $query->orderBy('name', 'desc');
                break;
            case 'featured':
            default:
                $query->orderBy('is_featured', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
        }


        $products = $query->paginate(12)->withQueryString();

        $products->getCollection()->transform(function ($product) {

            if (!$product->image && $product->image_url) {
                $product->image = asset('storage/' . $product->image_url);
            }


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $product->rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $product->review_count = $reviews->count();

            return $product;
        });


        $priceRange = Product::whereIn('category_id', $categoryIds)
            ->where('status', true)
            ->selectRaw('MIN(sale_price) as min_price, MAX(sale_price) as max_price')
            ->first();


        if (!$priceRange || $priceRange->min_price === null) {
            $priceRange = (object)[
                'min_price' => 0,
                'max_price' => 10000000
            ];
        }


        $category->load(['children' => function($query) {
            $query->where('status', true)->withCount('products');
        }]);

        return Inertia::render('Marketplace/Public/ListProduct/ListProduct', [
            'category' => $category,
            'products' => $products,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'brands' => $brands,
            'weightRanges' => $weightRanges,
            'filters' => [
                'brand' => $brand,
                'weight_range' => $weightRange,
                'min_price' => $minPrice,
                'max_price' => $maxPrice,
                'sort' => $sort
            ],
            'activeFilters' => $activeFilters,
            'priceRange' => [
                'min' => (int)round($priceRange->min_price),
                'max' => (int)round($priceRange->max_price),
            ],
            'csrfToken' => csrf_token(),
        ]);
    }

    public function featured(Request $request)
    {

        $featuredProducts = Product::where('status', true)
            ->where('is_featured', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->get();

        $featuredProducts->transform(function ($product) {
            if (!$product->image && $product->image_url) {
                $product->image = asset('storage/' . $product->image_url);
            }


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $product->rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $product->review_count = $reviews->count();

            return $product;
        });


        $newArrivals = Product::where('status', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->take(12)
            ->get();

        $newArrivals->transform(function ($product) {
            if (!$product->image && $product->image_url) {
                $product->image = asset('storage/' . $product->image_url);
            }


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $product->rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $product->review_count = $reviews->count();

            return $product;
        });


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {

                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();


                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }


                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5);
        $moreCategories = $allParentCategories->slice(5);

        return Inertia::render('Marketplace/Public/ListProduct/SpecialProducts', [
            'featuredProducts' => $featuredProducts,
            'newArrivals' => $newArrivals,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'initialTab' => 'featured',
            'csrfToken' => csrf_token(),
        ]);
    }

    public function newArrivals(Request $request)
    {

        $newArrivals = Product::where('status', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->take(12)
            ->get();

        $newArrivals->transform(function ($product) {
            if (!$product->image && $product->image_url) {
                $product->image = asset('storage/' . $product->image_url);
            }


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $product->rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $product->review_count = $reviews->count();

            return $product;
        });


        $featuredProducts = Product::where('status', true)
            ->where('is_featured', true)
            ->with(['category'])
            ->orderBy('created_at', 'desc')
            ->get();

        $featuredProducts->transform(function ($product) {
            if (!$product->image && $product->image_url) {
                $product->image = asset('storage/' . $product->image_url);
            }


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $product->rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $product->review_count = $reviews->count();

            return $product;
        });


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {

                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();


                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }


                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5);
        $moreCategories = $allParentCategories->slice(5);

        return Inertia::render('Marketplace/Public/ListProduct/SpecialProducts', [
            'featuredProducts' => $featuredProducts,
            'newArrivals' => $newArrivals,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'initialTab' => 'new',
            'csrfToken' => csrf_token(),
        ]);
    }

    public function search(Request $request)
    {

        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();


        $searchQuery = $request->filled('q') ? trim($request->q) : '';

        if (empty($searchQuery)) {
            return Inertia::render('Marketplace/Public/ListProduct/SearchResults', [
                'products' => [],
                'searchQuery' => '',
                'topCategories' => $topCategories,
                'moreCategories' => $moreCategories,
                'totalProducts' => 0,
                'csrfToken' => csrf_token(),
            ]);
        }


        $query = Product::where('status', true)
            ->with(['category']);


        $query->where(function($q) use ($searchQuery) {
            $q->where('name', 'LIKE', '%' . $searchQuery . '%')
              ->orWhere('description', 'LIKE', '%' . $searchQuery . '%')
              ->orWhere('sku', 'LIKE', '%' . $searchQuery . '%')
              ->orWhere('brand', 'LIKE', '%' . $searchQuery . '%')
              ->orWhereHas('category', function($categoryQuery) use ($searchQuery) {
                  $categoryQuery->where('name', 'LIKE', '%' . $searchQuery . '%');
              });
        });


        $activeFilters = [];


        $brand = $request->filled('brand') ? trim($request->brand) : null;
        if ($brand) {
            $query->where('brand', $brand);
            $activeFilters[] = [
                'type' => 'brand',
                'label' => __('common.brand') . ': ' . $brand,
                'value' => $brand
            ];
        }


        $minPrice = null;
        $maxPrice = null;

        if ($request->filled('min_price')) {
            $minPrice = (float)$request->min_price;
            if ($minPrice < 0) $minPrice = null;
        }

        if ($request->filled('max_price')) {
            $maxPrice = (float)$request->max_price;
            if ($maxPrice < 0) $maxPrice = null;
        }

        if ($minPrice !== null && $maxPrice !== null && $minPrice > $maxPrice) {
            $temp = $minPrice;
            $minPrice = $maxPrice;
            $maxPrice = $temp;
        }

        if ($minPrice !== null || $maxPrice !== null) {
            if ($minPrice !== null && $maxPrice !== null) {
                $query->whereBetween('price', [$minPrice, $maxPrice]);
                $activeFilters[] = [
                    'type' => 'price',
                    'label' => __('product.price') . ': $' . number_format($minPrice) . ' - $' . number_format($maxPrice),
                    'value' => $minPrice . '-' . $maxPrice
                ];
            } elseif ($minPrice !== null) {
                $query->where('price', '>=', $minPrice);
                $activeFilters[] = [
                    'type' => 'price',
                    'label' => __('product.price') . ': ' . __('product.from') . ' $' . number_format($minPrice),
                    'value' => $minPrice . '-'
                ];
            } elseif ($maxPrice !== null) {
                $query->where('price', '<=', $maxPrice);
                $activeFilters[] = [
                    'type' => 'price',
                    'label' => __('product.price') . ': ' . __('product.up_to') . ' $' . number_format($maxPrice),
                    'value' => '-' . $maxPrice
                ];
            }
        }


        $weightRange = $request->filled('weight_range') ? trim($request->weight_range) : null;
        if ($weightRange) {
            if (str_contains($weightRange, 'exact_')) {
                $index = str_replace('exact_', '', $weightRange);
                $allProducts = Product::where('status', true)
                    ->whereNotNull('weight')
                    ->where('weight', '!=', '')
                    ->get();

                $weights = $allProducts->map(function ($product) {
                    return $this->convertToGrams($product->weight);
                })->filter()->unique()->sort()->values()->toArray();

                if (isset($weights[$index])) {
                    $targetWeight = $weights[$index];
                    $query->whereRaw('? = ?', [
                        $targetWeight,
                        DB::raw("CASE
                            WHEN weight REGEXP '^[0-9]+(\\.[0-9]+)?\\s*(kg|kilogram|kilograms)$' THEN
                                CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2)) * 1000
                            ELSE
                                CAST(REGEXP_REPLACE(weight, '[^0-9.]', '') AS DECIMAL(10,2))
                        END")
                    ]);

                    $activeFilters[] = [
                        'type' => 'weight',
                        'label' => __('product.weight') . ': ' . $this->formatWeight($targetWeight),
                        'value' => $weightRange
                    ];
                }
            } else {
                $parts = explode('-', $weightRange);
                if (count($parts) === 2) {
                    $minWeight = floatval($parts[0]);
                    $maxWeight = floatval($parts[1]);

                    $query->whereRaw('CASE
                        WHEN weight REGEXP "^[0-9]+(\\.[0-9]+)?\\s*(kg|kilogram|kilograms)$" THEN
                            CAST(REGEXP_REPLACE(weight, "[^0-9.]", "") AS DECIMAL(10,2)) * 1000
                        ELSE
                            CAST(REGEXP_REPLACE(weight, "[^0-9.]", "") AS DECIMAL(10,2))
                    END BETWEEN ? AND ?', [$minWeight, $maxWeight]);

                    $activeFilters[] = [
                        'type' => 'weight',
                        'label' => __('product.weight') . ': ' . $this->formatWeight($minWeight) . ' - ' . $this->formatWeight($maxWeight),
                        'value' => $weightRange
                    ];
                }
            }
        }


        $sort = $request->filled('sort') ? trim($request->sort) : 'relevance';
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'name_asc':
                $query->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $query->orderBy('name', 'desc');
                break;
            case 'relevance':
            default:

                $query->orderByRaw("
                    CASE
                        WHEN name LIKE ? THEN 1
                        WHEN description LIKE ? THEN 2
                        WHEN sku LIKE ? THEN 3
                        WHEN brand LIKE ? THEN 4
                        ELSE 5
                    END,
                    name ASC
                ", [
                    '%' . $searchQuery . '%',
                    '%' . $searchQuery . '%',
                    '%' . $searchQuery . '%',
                    '%' . $searchQuery . '%'
                ]);
                break;
        }


        $totalProducts = $query->count();


        $products = $query->paginate(12)->withQueryString();


        $products->getCollection()->transform(function ($product) {
            $product->image_url = $product->image ? Storage::url($product->image) : '/images/default-product.png';


            $avgRating = MarketProductReview::where('product_id', $product->id)->avg('rating');
            $product->average_rating = $avgRating ? round($avgRating, 1) : 0;
            $product->review_count = MarketProductReview::where('product_id', $product->id)->count();

            return $product;
        });


        $brands = Product::where('status', true)
            ->where(function($q) use ($searchQuery) {
                $q->where('name', 'LIKE', '%' . $searchQuery . '%')
                  ->orWhere('description', 'LIKE', '%' . $searchQuery . '%')
                  ->orWhere('sku', 'LIKE', '%' . $searchQuery . '%')
                  ->orWhere('brand', 'LIKE', '%' . $searchQuery . '%')
                  ->orWhereHas('category', function($categoryQuery) use ($searchQuery) {
                      $categoryQuery->where('name', 'LIKE', '%' . $searchQuery . '%');
                  });
            })
            ->whereNotNull('brand')
            ->where('brand', '!=', '')
            ->select('brand')
            ->distinct()
            ->orderBy('brand')
            ->pluck('brand')
            ->toArray();


        $weightRanges = $this->getWeightRanges();


        $filters = [
            'sort' => $sort,
            'q' => $searchQuery
        ];

        if ($brand) {
            $filters['brand'] = $brand;
        }

        if ($minPrice !== null) {
            $filters['min_price'] = $minPrice;
        }

        if ($maxPrice !== null) {
            $filters['max_price'] = $maxPrice;
        }

        if ($weightRange) {
            $filters['weight_range'] = $weightRange;
        }


        $priceRange = $this->getPriceRange();

        return Inertia::render('Marketplace/Public/ListProduct/SearchResults', [
            'products' => $products,
            'searchQuery' => $searchQuery,
            'activeFilters' => $activeFilters,
            'brands' => $brands,
            'weightRanges' => $weightRanges,
            'filters' => $filters,
            'priceRange' => $priceRange,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'csrfToken' => csrf_token(),
        ]);
    }

    /**
     * Get weight ranges for all products
     */
    private function getWeightRanges()
    {
        $allProducts = Product::where('status', true)
            ->whereNotNull('weight')
            ->where('weight', '!=', '')
            ->get();

        $weights = $allProducts
            ->map(function ($product) {
                return $this->convertToGrams($product->weight);
            })
            ->filter()
            ->unique()
            ->sort()
            ->values()
            ->toArray();

        $weightRanges = [];
        if (!empty($weights)) {
            $minWeight = min($weights);
            $maxWeight = max($weights);


            if (count($weights) <= 4) {
                
                $uniqueWeights = array_unique($weights);
                sort($uniqueWeights);

                foreach ($uniqueWeights as $index => $weight) {
                    $count = collect($weights)->filter(function($w) use ($weight) {
                        return $w == $weight;
                    })->count();

                    if ($count > 0) {
                        $weightRanges[] = [
                            'id' => "exact_$index",
                            'name' => $this->formatWeight($weight),
                            'min' => $weight,
                            'max' => $weight,
                            'count' => $count
                        ];
                    }
                }
            } else {

                $step = ($maxWeight - $minWeight) / 4;


                if ($step <= 0) {

                    $count = count($weights);
                    $weightRanges[] = [
                        'id' => "single_range",
                        'name' => $this->formatWeight($minWeight),
                        'min' => $minWeight,
                        'max' => $maxWeight,
                        'count' => $count
                    ];
                } else {
                    $createdRanges = [];

                    for ($i = 0; $i < 4; $i++) {
                        $rangeMin = $minWeight + ($i * $step);
                        $rangeMax = $i === 3 ? $maxWeight : $minWeight + (($i + 1) * $step);


                        $count = collect($weights)->filter(function($weight) use ($rangeMin, $rangeMax) {
                            return $weight >= $rangeMin && $weight <= $rangeMax;
                        })->count();


                        if ($count > 0) {
                            $rangeName = $this->formatWeight($rangeMin) . ' - ' . $this->formatWeight($rangeMax);


                            $isDuplicate = false;
                            foreach ($createdRanges as $existingRange) {
                                if ($existingRange['name'] === $rangeName) {
                                    $isDuplicate = true;
                                    break;
                                }
                            }

                            if (!$isDuplicate) {
                                $newRange = [
                                    'id' => "range_$i",
                                    'name' => $rangeName,
                                    'min' => $rangeMin,
                                    'max' => $rangeMax,
                                    'count' => $count
                                ];

                                $createdRanges[] = $newRange;
                            }
                        }
                    }

                    $weightRanges = $createdRanges;
                }
            }
        }

        return $weightRanges;
    }

    /**
     * Get price range for all products
     */
    private function getPriceRange()
    {
        $priceRange = Product::where('status', true)
            ->selectRaw('MIN(sale_price) as min_price, MAX(sale_price) as max_price')
            ->first();


        if (!$priceRange || $priceRange->min_price === null) {
            $priceRange = (object)[
                'min_price' => 0,
                'max_price' => 10000000
            ];
        }

        return [
            'min' => (int)round($priceRange->min_price),
            'max' => (int)round($priceRange->max_price),
        ];
    }
}
