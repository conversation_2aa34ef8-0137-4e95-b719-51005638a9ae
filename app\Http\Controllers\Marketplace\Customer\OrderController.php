<?php

namespace App\Http\Controllers\Marketplace\Customer;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use App\Models\MarketOrder;
use App\Models\MarketOrderDetail;
use App\Models\MarketPayment;
use App\Models\MarketCoupon;
use App\Models\Cart;
use App\Models\Product;
use App\Models\ProductBundle;
use App\Models\Category;
use App\Models\Customer;
use App\Models\MarketProductReview;
use App\Services\MarketCouponService;
use App\Services\SystemSettingService;
use App\Services\Marketplace\MarketplaceMailService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    protected $couponService;

    public function __construct(MarketCouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    public function validateCoupon(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
            'cart_total' => 'required|numeric|min:0'
        ]);

        if (!Auth::check()) {
            return response()->json([
                'valid' => false,
                'message' => 'Vui lòng đăng nhập để sử dụng mã giảm giá'
            ]);
        }

        $result = $this->couponService->validateCoupon(
            $request->code,
            Auth::user(),
            $request->cart_total,
            $request->cart_items ?? []
        );

        return response()->json($result);
    }

    public function checkout(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'shipping_address' => 'required|string',
            'province_name' => 'required|string',
            'district_name' => 'required|string',
            'ward_name' => 'required|string',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer',
            'ward_id' => 'required|integer',
            'shipping_fee' => 'required|numeric|min:0',
            'coupon_code' => 'nullable|string',
            'notes' => 'nullable|string|max:500',
            'payment_method_id' => 'required|string',
            'cart_items' => 'required|array|min:1',
            'cart_items.*.item_type' => 'required|in:product,bundle',
            'cart_items.*.product_id' => 'required_if:cart_items.*.item_type,product|nullable|exists:products,id,deleted_at,NULL',
            'cart_items.*.bundle_id' => 'required_if:cart_items.*.item_type,bundle|nullable|exists:product_bundles,id,deleted_at,NULL',
            'cart_items.*.quantity' => 'required|integer|min:1',
            'cart_items.*.price' => 'required|numeric|min:0'
        ]);

        $user = Auth::user();
        $cartItems = $request->cart_items;


        $paymentMethod = $this->validatePaymentMethod($request->payment_method_id);
        if (!$paymentMethod) {
            return response()->json([
                'success' => false,
                'message' => 'Phương thức thanh toán không khả dụng'
            ], 400);
        }


        $subtotal = 0;
        $validatedItems = [];

        foreach ($cartItems as $item) {
            $itemType = $item['item_type'] ?? 'product';

            if ($itemType === 'product') {

                $product = Product::find($item['product_id']);
                if (!$product || !$product->status) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Sản phẩm không khả dụng: ' . ($product->name ?? 'Unknown')
                    ], 400);
                }

                if ($item['quantity'] > $product->quantity) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Số lượng vượt quá tồn kho cho sản phẩm: ' . $product->name
                    ], 400);
                }

                $validatedItems[] = [
                    'type' => 'product',
                    'product' => $product,
                    'bundle' => null,
                    'quantity' => $item['quantity'],
                    'unit_price' => $product->sale_price,
                    'total_price' => $product->sale_price * $item['quantity']
                ];

                $subtotal += $product->sale_price * $item['quantity'];
            } else {

                $bundle = ProductBundle::find($item['bundle_id']);
                if (!$bundle || !$bundle->is_active || !$bundle->isCurrentlyActive()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Gói sản phẩm không khả dụng: ' . ($bundle->name ?? 'Unknown')
                    ], 400);
                }

                if (!$bundle->hasStock($item['quantity'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Số lượng vượt quá tồn kho cho gói sản phẩm: ' . $bundle->name
                    ], 400);
                }

                $validatedItems[] = [
                    'type' => 'bundle',
                    'product' => null,
                    'bundle' => $bundle,
                    'quantity' => $item['quantity'],
                    'unit_price' => $bundle->bundle_price,
                    'total_price' => $bundle->bundle_price * $item['quantity']
                ];

                $subtotal += $bundle->bundle_price * $item['quantity'];
            }
        }


        $discountAmount = 0;
        $coupon = null;
        if ($request->coupon_code) {
            $couponResult = $this->couponService->validateCoupon(
                $request->coupon_code,
                $user,
                $subtotal,
                $cartItems
            );

            if (!$couponResult['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $couponResult['message']
                ], 400);
            }

            $coupon = $couponResult['coupon'];
            $discountAmount = $couponResult['discount'];
        }

        $totalAmount = $subtotal + $request->shipping_fee - $discountAmount;

        try {
            DB::beginTransaction();

            $customer = $user->customer;
            if (!$customer) {
                $customer = Customer::create([
                    'user_id' => $user->id,
                    'name' => $request->customer_name,
                    'phone' => $request->customer_phone,
                    'email' => $request->customer_email ?: $user->email,
                    'address' => $request->shipping_address,
                    'is_active' => true,
                ]);
            }

            $order = MarketOrder::create([
                'order_number' => MarketOrder::generateOrderNumber(),
                'user_id' => $user->id ?? null,
                'customer_id' => $customer->id ?? null,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'customer_email' => $request->customer_email,
                'shipping_address' => $request->shipping_address,
                'province_name' => $request->province_name,
                'district_name' => $request->district_name,
                'ward_name' => $request->ward_name,
                'province_id' => $request->province_id,
                'district_id' => $request->district_id,
                'ward_id' => $request->ward_id,
                'payment_method' => $request->payment_method_id,
                'subtotal' => $subtotal,
                'shipping_fee' => $request->shipping_fee,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'notes' => $request->notes
            ]);


            foreach ($validatedItems as $index => $item) {
                if ($item['type'] === 'product') {

                    MarketOrderDetail::create([
                        'market_order_id' => $order->id,
                        'reference_code' => $order->order_number . '-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                        'product_id' => $item['product']->id,
                        'bundle_id' => null,
                        'item_type' => 'product',
                        'product_name' => $item['product']->name,
                        'product_sku' => $item['product']->sku,
                        'product_image_url' => $item['product']->image_url,
                        'unit_price' => $item['unit_price'],
                        'quantity' => $item['quantity'],
                        'total_price' => $item['total_price']
                    ]);


                    $item['product']->decrement('quantity', $item['quantity']);
                } else {

                    MarketOrderDetail::create([
                        'market_order_id' => $order->id,
                        'reference_code' => $order->order_number . '-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                        'product_id' => null,
                        'bundle_id' => $item['bundle']->id,
                        'item_type' => 'bundle',
                        'product_name' => $item['bundle']->name,
                        'product_sku' => 'BDL-' . $item['bundle']->id,
                        'product_image_url' => $item['bundle']->image_url,
                        'unit_price' => $item['unit_price'],
                        'quantity' => $item['quantity'],
                        'total_price' => $item['total_price']
                    ]);


                    if ($item['bundle']->stock_quantity > 0) {
                        $item['bundle']->decrement('stock_quantity', $item['quantity']);
                    }


                    foreach ($item['bundle']->bundleItems as $bundleItem) {
                        if ($bundleItem->product) {
                            $bundleItem->product->decrement(
                                'quantity',
                                $bundleItem->quantity * $item['quantity']
                            );
                        }
                    }
                }
            }


            if ($coupon) {
                $this->couponService->applyCoupon($coupon, $user, $order->id, $discountAmount);
            }


            MarketPayment::create([
                'market_order_id' => $order->id,
                'user_id' => $user->id ?? null,
                'customer_id' => $customer->id ?? null,
                'payment_method' => $request->payment_method_id,
                'gateway' => $paymentMethod['provider'],
                'amount' => $totalAmount,
                'status' => 'pending',
                'transaction_date' => now(),
                'transaction_id' => $order->generateTransactionId($request->payment_method_id)
            ]);


            if (Auth::check()) {

                $productIds = collect($cartItems)->where('item_type', 'product')->pluck('product_id')->toArray();
                $bundleIds = collect($cartItems)->where('item_type', 'bundle')->pluck('bundle_id')->toArray();


                if (!empty($productIds)) {
                    Cart::where('user_id', $user->id)
                        ->where('item_type', 'product')
                        ->whereIn('product_id', $productIds)
                        ->delete();
                }


                if (!empty($bundleIds)) {
                    Cart::where('user_id', $user->id)
                        ->where('item_type', 'bundle')
                        ->whereIn('bundle_id', $bundleIds)
                        ->delete();
                }
            }

            DB::commit();

            try {
                MarketplaceMailService::sendOrderConfirmation($order);
            } catch (\Exception $e) {
                Log::warning('Failed to send order confirmation email', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage()
                ]);
            }


            if (in_array($request->payment_method_id, ['vnpay', 'momo'])) {
                $paymentRoute = $request->payment_method_id === 'vnpay'
                    ? 'marketplace.payment.vnpay.create'
                    : 'marketplace.payment.momo.create';

                return response()->json([
                    'success' => true,
                    'message' => 'Đặt hàng thành công',
                    'redirect_to_payment' => true,
                    'payment_url' => route($paymentRoute, ['order_id' => $order->id]),
                    'order' => [
                        'id' => $order->id,
                        'order_number' => $order->order_number,
                        'total_amount' => $order->total_amount,
                        'payment_method' => $order->payment_method
                    ]
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Đặt hàng thành công',
                'order' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_amount' => $order->total_amount,
                    'payment_method' => $order->payment_method
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order creation failed: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.'
            ], 500);
        }
    }

    private function validatePaymentMethod($paymentMethodId)
    {
        $enabledMethods = [
            'cash' => SystemSettingService::get('market_cash_payment', ['active' => false]),
            'momo' => SystemSettingService::get('market_momo_payment', ['active' => false]),
            'vnpay' => SystemSettingService::get('market_vnpay_payment', ['active' => false]),
            'bank_transfer' => SystemSettingService::get('market_bank_transfer', ['active' => false])
        ];

        if (!isset($enabledMethods[$paymentMethodId]) || !($enabledMethods[$paymentMethodId]['active'] ?? false)) {
            return null;
        }

        return [
            'id' => $paymentMethodId,
            'provider' => $paymentMethodId,
            'settings' => $enabledMethods[$paymentMethodId]
        ];
    }

    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $orders = MarketOrder::where('user_id', Auth::id())
            ->with(['orderDetails.product', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Orders/Index', [
            'orders' => $orders,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories
        ]);
    }

    public function show($id)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $order = MarketOrder::where('user_id', Auth::id())
            ->where('id', $id)
            ->with([
                'orderDetails.product',
                'orderDetails.review',
                'payments'
            ])
            ->firstOrFail();


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Orders/Show', [
            'order' => $order,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories
        ]);
    }

    public function checkoutSuccess(Request $request)
    {
        $orderId = $request->query('order_id');
        $order = null;

        if ($orderId && Auth::check()) {
            $order = MarketOrder::where('user_id', Auth::id())
                ->where('id', $orderId)
                ->with(['orderDetails.product', 'payments'])
                ->first();
        }


        if (!$order && Auth::check()) {
            return redirect()->route('marketplace.orders.index')
                ->with('info', 'Đơn hàng không tồn tại hoặc bạn không có quyền truy cập.');
        }


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Checkout/Success', [
            'order' => $order,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories
        ]);
    }

    public function checkoutFailure(Request $request)
    {
        $orderId = $request->query('order_id');
        $errorMessage = $request->query('message', 'Thanh toán không thành công');
        $order = null;

        if ($orderId && Auth::check()) {
            $order = MarketOrder::where('user_id', Auth::id())
                ->where('id', $orderId)
                ->with(['orderDetails.product', 'payments'])
                ->first();
        }


        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(5)->values();
        $moreCategories = $allParentCategories->slice(5)->values();

        return Inertia::render('Marketplace/Public/Checkout/Failure', [
            'order' => $order,
            'errorMessage' => $errorMessage,
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories
        ]);
    }

    /**
     * Cancel an order
     */
    public function cancel(Request $request, $id)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $order = MarketOrder::where('user_id', Auth::id())
            ->where('id', $id)
            ->firstOrFail();

        if (!$order->canBeCancelled()) {
            return response()->json([
                'error' => 'Đơn hàng này không thể hủy. Chỉ có thể hủy đơn hàng đang chờ xử lý và chưa thanh toán.'
            ], 400);
        }

        $reason = $request->input('reason', 'Khách hàng yêu cầu hủy');

        if ($order->cancel($reason)) {
            return response()->json([
                'message' => 'Đã hủy đơn hàng thành công'
            ]);
        } else {
            return response()->json([
                'error' => 'Không thể hủy đơn hàng. Vui lòng thử lại.'
            ], 500);
        }
    }

    /**
     * Submit a product review
     */
    public function submitReview(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'market_order_detail_id' => 'required|exists:market_order_details,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'url'
        ]);

        $user = Auth::user();
        $customer = $user->customer;

        $orderDetail = MarketOrderDetail::whereHas('order', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->findOrFail($request->market_order_detail_id);

        $order = $orderDetail->order;

        if (!$order->canBeReviewed()) {
            return response()->json([
                'error' => 'Chỉ có thể đánh giá sản phẩm sau khi đơn hàng hoàn thành'
            ], 400);
        }

        if ($orderDetail->hasReview()) {
            return response()->json([
                'error' => 'Sản phẩm này đã được đánh giá'
            ], 400);
        }

        try {
            $review = MarketProductReview::create([
                'user_id' => $user->id,
                'customer_id' => $customer->id ?? null,
                'product_id' => $orderDetail->product_id,
                'market_order_id' => $order->id,
                'market_order_detail_id' => $orderDetail->id,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'images' => $request->images,
                'is_verified_purchase' => true,
                'reviewed_at' => now()
            ]);

            return response()->json([
                'message' => 'Đánh giá đã được gửi thành công',
                'review' => $review
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to submit review', [
                'user_id' => $user->id,
                'order_detail_id' => $request->market_order_detail_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Không thể gửi đánh giá. Vui lòng thử lại.'
            ], 500);
        }
    }

    /**
     * Get reviewable items for an order
     */
    public function getReviewableItems($orderId)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $order = MarketOrder::where('user_id', Auth::id())
            ->where('id', $orderId)
            ->with(['orderDetails.product', 'orderDetails.review'])
            ->firstOrFail();

        if (!$order->canBeReviewed()) {
            return response()->json([
                'error' => 'Đơn hàng này chưa thể đánh giá'
            ], 400);
        }

        $reviewableItems = $order->getReviewableItems();

        return response()->json([
            'reviewable_items' => $reviewableItems->load('product')
        ]);
    }
}
