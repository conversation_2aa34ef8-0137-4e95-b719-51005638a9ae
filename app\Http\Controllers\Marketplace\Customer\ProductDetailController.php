<?php

namespace App\Http\Controllers\Marketplace\Customer;
use App\Http\Controllers\Controller;
use Inertia\Inertia;
use App\Models\Product;
use App\Models\Category;
use App\Models\MarketProductReview;
use Illuminate\Http\Request;

class ProductDetailController extends Controller
{
    public function index(Request $request, $slug)
    {
        try {
            $product = Product::where('slug', $slug)
                ->where('status', true)
                ->with(['category'])
                ->firstOrFail();

            if (!$product->image && $product->image_url) {
                $product->image = $product->image_url_formatted;
            }

            $reviews = MarketProductReview::where('product_id', $product->id)
                ->with(['user', 'customer', 'order', 'orderDetail'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'title' => $review->title,
                        'comment' => $review->comment,
                        'reviewer_name' => $review->reviewer_name ?: ($review->user ? $review->user->name : ($review->customer ? $review->customer->name : 'Anonymous')),
                        'created_at' => $review->created_at->format('d/m/Y'),
                        'helpful_count' => $review->helpful_count ?? 0,
                        'unhelpful_count' => $review->unhelpful_count ?? 0,
                        'verified_purchase' => $review->market_order_id ? true : false,
                    ];
                });

            $totalReviews = $reviews->count();
            $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

            $ratingDistribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
            foreach ($reviews as $review) {
                $rating = (int) $review['rating'];
                if (isset($ratingDistribution[$rating])) {
                    $ratingDistribution[$rating]++;
                }
            }

            $product->rating = round($averageRating, 1);
            $product->review_count = $totalReviews;
            $product->rating_distribution = $ratingDistribution;
            $product->reviews_data = $reviews;

            $relatedProducts = Product::where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->where('status', true)
                ->with(['category'])
                ->take(8)
                ->get();

            $relatedProducts->transform(function ($relatedProduct) {
                if (!$relatedProduct->image && $relatedProduct->image_url) {
                    $relatedProduct->image = $relatedProduct->image_url_formatted;
                }
                return $relatedProduct;
            });

            $allParentCategories = Category::with(['children' => function($q) {
                $q->where('status', true)
                  ->withCount(['products' => function($query) {
                      $query->where('status', true);
                  }]);
            }])
                ->whereNull('parent_id')
                ->where('status', true)
                ->get()
                ->map(function($category) {
                    $directProductCount = Product::where('category_id', $category->id)
                        ->where('status', true)
                        ->count();

                    $childProductCount = 0;
                    if ($category->children && $category->children->count() > 0) {
                        $childCategoryIds = $category->children->pluck('id');
                        $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                            ->where('status', true)
                            ->count();
                    }

                    $category->products_count = $directProductCount + $childProductCount;
                    return $category;
                })
                ->sortByDesc('products_count')
                ->values();

            $topCategories = $allParentCategories->take(5)->values();
            $moreCategories = $allParentCategories->slice(5)->values()->all();

            return Inertia::render('Marketplace/Public/ProductDetail/ProductDetail', [
                'product' => $product,
                'relatedProducts' => $relatedProducts,
                'topCategories' => $topCategories,
                'moreCategories' => $moreCategories,
                'csrfToken' => csrf_token(),
            ]);
        } catch (\Exception $e) {
            return redirect()->route('marketplace.home')->with('error', 'Không tìm thấy sản phẩm');
        }
    }
}
