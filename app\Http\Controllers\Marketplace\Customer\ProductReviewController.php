<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use App\Models\MarketProductReview;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductReviewController extends Controller
{
    /**
     * Submit a review for a product
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|max:1000',
            'images' => 'nullable|array',
            'images.*' => 'nullable|string'
        ]);

        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => __('marketplace.login_required')
            ], 401);
        }


        $customer = Customer::where('user_id', $user->id)->first();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'error' => __('marketplace.customer_profile_not_found')
            ], 404);
        }

        
        $existingReview = MarketProductReview::where('user_id', $user->id)
            ->where('product_id', $request->product_id)
            ->first();

        if ($existingReview) {
            return response()->json([
                'success' => false,
                'error' => __('marketplace.already_reviewed_product')
            ], 400);
        }

        DB::beginTransaction();

        try {
            $review = new MarketProductReview([
                'user_id' => $user->id,
                'customer_id' => $customer->id,
                'product_id' => $request->product_id,
                'market_order_id' => null,
                'market_order_detail_id' => null,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'images' => $request->images,
                'is_verified_purchase' => false,
                'is_published' => true,
                'reviewed_at' => now()
            ]);

            $review->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('marketplace.review_submitted_success'),
                'review' => $review
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'error' => __('marketplace.review_submission_failed') . ': ' . $e->getMessage()
            ], 500);
        }
    }
}
