<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use App\Models\MarketProductReview;
use App\Models\MarketProductReviewVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductReviewVoteController extends Controller
{
    /**
     * Vote on a product review as helpful or unhelpful
     */
    public function vote(Request $request, $reviewId)
    {
        $request->validate([
            'vote_type' => 'required|in:helpful,unhelpful'
        ]);

        $review = MarketProductReview::findOrFail($reviewId);
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => __('marketplace.login_to_vote')
            ], 401);
        }


        $existingVote = MarketProductReviewVote::where('market_product_review_id', $reviewId)
            ->where('user_id', $user->id)
            ->first();

        DB::beginTransaction();
        try {
            if ($existingVote) {

                if ($existingVote->vote_type === $request->vote_type) {

                    $existingVote->delete();


                    if ($request->vote_type === 'helpful') {
                        $review->decrement('helpful_count');
                    } else {
                        $review->decrement('unhelpful_count');
                    }

                    DB::commit();
                    return response()->json([
                        'success' => true,
                        'message' => __('marketplace.vote_removed_successfully'),
                        'action' => 'removed',
                        'user_vote' => null,
                        'helpful_count' => $review->fresh()->helpful_count,
                        'unhelpful_count' => $review->fresh()->unhelpful_count
                    ]);
                } else {

                    $oldVoteType = $existingVote->vote_type;
                    $existingVote->vote_type = $request->vote_type;
                    $existingVote->save();


                    if ($oldVoteType === 'helpful') {
                        $review->decrement('helpful_count');
                        $review->increment('unhelpful_count');
                    } else {
                        $review->decrement('unhelpful_count');
                        $review->increment('helpful_count');
                    }

                    DB::commit();
                    return response()->json([
                        'success' => true,
                        'message' => __('marketplace.vote_updated_successfully'),
                        'action' => 'updated',
                        'user_vote' => $request->vote_type,
                        'helpful_count' => $review->fresh()->helpful_count,
                        'unhelpful_count' => $review->fresh()->unhelpful_count
                    ]);
                }
            } else {

                MarketProductReviewVote::create([
                    'user_id' => $user->id,
                    'customer_id' => $user->customer ? $user->customer->id : null,
                    'market_product_review_id' => $reviewId,
                    'vote_type' => $request->vote_type
                ]);

                
                if ($request->vote_type === 'helpful') {
                    $review->increment('helpful_count');
                } else {
                    $review->increment('unhelpful_count');
                }

                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => __('marketplace.vote_added_successfully'),
                    'action' => 'added',
                    'user_vote' => $request->vote_type,
                    'helpful_count' => $review->fresh()->helpful_count,
                    'unhelpful_count' => $review->fresh()->unhelpful_count
                ]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'error' => __('marketplace.vote_error')
            ], 500);
        }
    }

    /**
     * Get the current user's vote status for a review
     */
    public function getVoteStatus($reviewId)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => true,
                'user_vote' => null,
                'helpful_count' => 0,
                'unhelpful_count' => 0
            ]);
        }

        $review = MarketProductReview::findOrFail($reviewId);
        $vote = MarketProductReviewVote::where('market_product_review_id', $reviewId)
            ->where('user_id', $user->id)
            ->first();

        return response()->json([
            'success' => true,
            'user_vote' => $vote ? $vote->vote_type : null,
            'helpful_count' => $review->helpful_count,
            'unhelpful_count' => $review->unhelpful_count
        ]);
    }
}
