<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductBundle;
use App\Models\MarketCoupon;
use App\Models\MarketProductReview;
use Carbon\Carbon;

class SpecialOfferController extends Controller
{
    public function index(Request $request)
    {

        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;
                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(4)->values();
        $moreCategories = $allParentCategories->slice(5)->values()->all();


        $discountedProducts = Product::where('status', true)
            ->whereColumn('sale_price', '<', 'import_price')
            ->with(['category'])
            ->orderByRaw('((import_price - sale_price) / import_price) DESC')
            ->take(12)
            ->get()
            ->map(function($product) {
                $reviews = MarketProductReview::where('product_id', $product->id)->get();
                $totalReviews = $reviews->count();
                $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

                $product->rating = round($averageRating, 1);
                $product->review_count = $totalReviews;


                if ($product->import_price > 0) {
                    $product->discount_percentage = round(((($product->import_price - $product->sale_price) / $product->import_price) * 100), 0);
                } else {
                    $product->discount_percentage = 0;
                }

                return $product;
            });


        $featuredBundles = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->where('is_featured', true)
            ->whereHas('items.product', function($query) {
                $query->whereColumn('sale_price', '<', 'import_price');
            })
            ->orderBy('sort_order', 'asc')
            ->take(6)
            ->get()
            ->map(function($bundle) {

                $totalOriginalPrice = $bundle->items->sum(function($item) {
                    return $item->product->import_price * $item->quantity;
                });

                if ($totalOriginalPrice > 0 && $bundle->total_price < $totalOriginalPrice) {
                    $bundle->discount_percentage = round((($totalOriginalPrice - $bundle->total_price) / $totalOriginalPrice) * 100, 0);
                } else {
                    $bundle->discount_percentage = 0;
                }

                return $bundle;
            });


        $activeCoupons = MarketCoupon::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', Carbon::now());
            })
            ->where(function($query) {
                $query->whereNull('starts_at')
                      ->orWhere('starts_at', '<=', Carbon::now());
            })
            ->orderBy('value', 'desc')
            ->take(6)
            ->get()
            ->map(function($coupon) {

                if ($coupon->type === 'percentage') {
                    $coupon->display_value = $coupon->value . '%';
                } else {
                    $coupon->display_value = number_format($coupon->value, 0, ',', '.') . 'đ';
                }

                return $coupon;
            });


        $flashSaleProducts = Product::where('status', true)
            ->whereColumn('sale_price', '<', 'import_price')
            ->where('created_at', '>', Carbon::now()->subDays(7))
            ->with(['category'])
            ->orderByRaw('((import_price - sale_price) / import_price) DESC')
            ->take(8)
            ->get()
            ->map(function($product) {
                $reviews = MarketProductReview::where('product_id', $product->id)->get();
                $totalReviews = $reviews->count();
                $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

                $product->rating = round($averageRating, 1);
                $product->review_count = $totalReviews;

                if ($product->import_price > 0) {
                    $product->discount_percentage = round(((($product->import_price - $product->sale_price) / $product->import_price) * 100), 0);
                } else {
                    $product->discount_percentage = 0;
                }

                return $product;
            });

        
        $comboDeals = ProductBundle::with(['items.product.category'])
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get()
            ->map(function($bundle) {
                $totalOriginalPrice = $bundle->items->sum(function($item) {
                    return $item->product->import_price * $item->quantity;
                });

                if ($totalOriginalPrice > 0 && $bundle->total_price < $totalOriginalPrice) {
                    $bundle->discount_percentage = round((($totalOriginalPrice - $bundle->total_price) / $totalOriginalPrice) * 100, 0);
                    $bundle->savings_amount = $totalOriginalPrice - $bundle->total_price;
                } else {
                    $bundle->discount_percentage = 0;
                    $bundle->savings_amount = 0;
                }

                return $bundle;
            })
            ->where('discount_percentage', '>', 0)
            ->sortByDesc('discount_percentage')
            ->values();

        return Inertia::render('Marketplace/Public/SpecialOffers/SpecialOffers', [
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
            'discountedProducts' => $discountedProducts,
            'featuredBundles' => $featuredBundles,
            'activeCoupons' => $activeCoupons,
            'flashSaleProducts' => $flashSaleProducts,
            'comboDeals' => $comboDeals,
            'csrfToken' => csrf_token(),
        ]);
    }
}
