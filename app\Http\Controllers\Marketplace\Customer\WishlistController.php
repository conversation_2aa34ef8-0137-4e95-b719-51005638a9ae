<?php

namespace App\Http\Controllers\Marketplace\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Product;
use App\Models\MarketProductReview;
use App\Models\Category;

class WishlistController extends Controller
{
    public function index()
    {
        $allParentCategories = Category::with(['children' => function($q) {
            $q->where('status', true)
              ->withCount(['products' => function($query) {
                  $query->where('status', true);
              }]);
        }])
            ->whereNull('parent_id')
            ->where('status', true)
            ->get()
            ->map(function($category) {
                $directProductCount = Product::where('category_id', $category->id)
                    ->where('status', true)
                    ->count();

                $childProductCount = 0;
                if ($category->children && $category->children->count() > 0) {
                    $childCategoryIds = $category->children->pluck('id');
                    $childProductCount = Product::whereIn('category_id', $childCategoryIds)
                        ->where('status', true)
                        ->count();
                }

                $category->products_count = $directProductCount + $childProductCount;

                return $category;
            })
            ->sortByDesc('products_count')
            ->values();

        $topCategories = $allParentCategories->take(4)->values();

        $moreCategories = $allParentCategories->slice(5)->values()->all();

        return Inertia::render('Marketplace/Public/Wishlist/Wishlist', [
            'pageTitle' => __('marketplace.wishlist'),
            'topCategories' => $topCategories,
            'moreCategories' => $moreCategories,
        ]);
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        try {
            $product = Product::with('category')->findOrFail($request->product_id);


            $reviews = MarketProductReview::where('product_id', $product->id)->get();
            $rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
            $reviewCount = $reviews->count();

            return response()->json([
                'success' => true,
                'message' => __('marketplace.added_to_wishlist'),
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'image' => $product->image_url_formatted,
                    'sale_price' => $product->sale_price,
                    'import_price' => $product->import_price,
                    'category' => $product->category ? $product->category->name : null,
                    'rating' => $rating,
                    'review_count' => $reviewCount,
                    'is_featured' => $product->is_featured
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('marketplace.add_to_wishlist_failed')
            ], 400);
        }
    }

    public function remove($id)
    {
        try {
            return response()->json([
                'success' => true,
                'message' => __('marketplace.removed_from_wishlist')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('marketplace.remove_from_wishlist_failed')
            ], 400);
        }
    }

    public function getCount()
    {
        return response()->json([
            'count' => 0
        ]);
    }

    public function syncFromStorage(Request $request)
    {
        try {
            return response()->json([
                'success' => true,
                'message' => __('marketplace.wishlist_synced')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('marketplace.wishlist_sync_failed')
            ], 400);
        }
    }

    public function getItems(Request $request)
    {
        try {
            $productIds = $request->input('product_ids', []);

            if (empty($productIds)) {
                return response()->json([
                    'success' => true,
                    'items' => []
                ]);
            }

            $products = Product::with('category')
                ->whereIn('id', $productIds)
                ->get()
                ->map(function($product) {

                    $reviews = MarketProductReview::where('product_id', $product->id)->get();
                    $rating = $reviews->count() > 0 ? round($reviews->avg('rating'), 1) : 0;
                    $reviewCount = $reviews->count();

                    return [
                        'id' => $product->id,
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'slug' => $product->slug,
                        'image' => $product->image_url_formatted,
                        'sale_price' => $product->sale_price,
                        'import_price' => $product->import_price,
                        'category' => $product->category ? $product->category->name : null,
                        'rating' => $rating,
                        'review_count' => $reviewCount,
                        'is_featured' => $product->is_featured
                    ];
                });

            return response()->json([
                'success' => true,
                'items' => $products
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('marketplace.wishlist_load_failed'),
                'items' => []
            ], 400);
        }
    }
}
