<?php

namespace App\Http\Controllers\Marketplace\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MarketOrder;
use App\Models\MarketPayment;
use App\Services\SystemSettingService;
use App\Services\Marketplace\MarketplaceMailService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;

class MomoController extends Controller
{    private function getMomoConfig()
    {
        $settings = SystemSettingService::get('market_momo_payment', []);

        $accessKey = $settings['accessKey'] ?? $settings['accesskey'] ?? '';
        $secretKey = $settings['secretKey'] ?? '';

        // Try to decrypt the accessKey if it appears to be encrypted
        if (!empty($accessKey)) {
            try {
                // Check if the key is already decrypted by attempting to decrypt it
                $decryptedKey = Crypt::decryptString($accessKey);
                $accessKey = $decryptedKey; // Use decrypted key if successful
            } catch (\Exception $e) {
                // If decryption fails, assume the key is already in plaintext format
                Log::info('Using plaintext accessKey - decryption failed');
            }
        }

        // Try to decrypt the secretKey if it appears to be encrypted
        if (!empty($secretKey)) {
            try {
                // Check if the key is already decrypted by attempting to decrypt it
                $decryptedKey = Crypt::decryptString($secretKey);
                $secretKey = $decryptedKey; // Use decrypted key if successful
            } catch (\Exception $e) {
                // If decryption fails, assume the key is already in plaintext format
                Log::info('Using plaintext secretKey - decryption failed');
            }
        }

        return [
            'partnerCode' => $settings['partnerCode'] ?? '',
            'accessKey' => $accessKey,
            'secretKey' => $secretKey,
            'endPoint' => $settings['endPoint'] ?? 'https://test-payment.momo.vn/v2/gateway/api/create',
            'ipnUrl' => route('marketplace.payment.momo.ipn'),
            'redirectUrl' => route('marketplace.payment.momo.return'),
        ];
    }    private function execPostRequest($url, $data)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $result = curl_exec($ch);

        if ($result === false) {
            $error = curl_error($ch);
            $errno = curl_errno($ch);
            curl_close($ch);
            Log::error('cURL error in MoMo request', ['error' => $error, 'code' => $errno, 'url' => $url]);
            throw new \Exception("cURL Error: $error ($errno)");
        }

        curl_close($ch);
        return $result;
    }

    public function createPayment(Request $request)
    {        $request->validate([
            'order_id' => 'required|exists:market_orders,id',
            'return_url' => 'nullable|url'
        ]);

        $order = MarketOrder::where('id', $request->order_id)->first();

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy đơn hàng'
            ], 404);
        }


        if (!Auth::check() || $order->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập đơn hàng này'
            ], 403);
        }


        if ($order->payment_status !== 'unpaid') {
            return response()->json([
                'success' => false,
                'message' => 'Đơn hàng đã được thanh toán hoặc không thể thanh toán'
            ], 400);
        }


        if ($order->payment_url_created_at && $order->momo_payment_url) {
            $createdAt = Carbon::parse($order->payment_url_created_at);
            if ($createdAt->diffInMinutes(now()) < 30) {

                return response()->json([
                    'success' => true,
                    'payment_url' => $order->momo_payment_url,
                    'message' => 'Sử dụng liên kết thanh toán MoMo đã có',
                    'is_existing_url' => true
                ]);
            }
        }

        $config = $this->getMomoConfig();

        if (empty($config['partnerCode']) || empty($config['accessKey']) || empty($config['secretKey'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cấu hình MoMo chưa được thiết lập'
            ], 500);
        }        try {

            $paymentUrl = $this->buildMomoPaymentUrl($order, $config);

            if (!$paymentUrl) {
                Log::error('Failed to create MoMo payment URL: No URL returned');
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể tạo liên kết thanh toán MoMo'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create MoMo payment URL: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'config' => [
                    'partnerCode' => $config['partnerCode'] ? substr($config['partnerCode'], 0, 3) . '***' : 'empty',
                    'accessKey' => $config['accessKey'] ? substr($config['accessKey'], 0, 3) . '***' : 'empty',
                    'secretKey' => 'hidden',
                    'endPoint' => $config['endPoint']
                ],
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Không thể tạo liên kết thanh toán MoMo: ' . $e->getMessage()
            ], 500);
        }

        return response()->json([
            'success' => true,
            'payment_url' => $paymentUrl,
            'message' => 'Chuyển hướng đến MoMo để thanh toán'
        ]);
    }    private function buildMomoPaymentUrl(MarketOrder $order, array $config)
    {        $partnerCode = $config['partnerCode'];
        $accessKey = $config['accessKey'];
        $secretKey = $config['secretKey'];
        $orderInfo = "Thanh toan don hang {$order->order_number}";

        $amount = (int) $order->total_amount;


        $timestamp = time();
        $microtime = round(microtime(true) * 1000);
        $randomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 8);
        $orderId = $order->order_number . '_' . $timestamp . '_' . $randomStr;

        $redirectUrl = $config['redirectUrl'];
        $ipnUrl = $config['ipnUrl'];
        $extraData = "";
        $requestId = $timestamp . "";
        $requestType = "payWithMethod";


        $rawHash = "accessKey=" . $accessKey . "&amount=" . $amount . "&extraData=" . $extraData . "&ipnUrl=" . $ipnUrl . "&orderId=" . $orderId . "&orderInfo=" . $orderInfo . "&partnerCode=" . $partnerCode . "&redirectUrl=" . $redirectUrl . "&requestId=" . $requestId . "&requestType=" . $requestType;

        $signature = hash_hmac("sha256", $rawHash, $secretKey);        $data = array(
            'partnerCode' => $partnerCode,
            'partnerName' => $config['partnerName'] ?? "PIBA Marketplace",
            'storeId' => $config['storeId'] ?? $partnerCode,
            'requestId' => $requestId,
            'amount' => $amount,
            'orderId' => $orderId,
            'orderInfo' => $orderInfo,
            'redirectUrl' => $redirectUrl,
            'ipnUrl' => $ipnUrl,
            'lang' => 'vi',
            'extraData' => $extraData,
            'requestType' => $requestType,
            'signature' => $signature
        );

        try {
            $result = $this->execPostRequest($config['endPoint'], json_encode($data));
            $jsonResult = json_decode($result, true);

            Log::info('MoMo payment request', [
                'order_id' => $order->id,
                'request_data' => $data,
                'response' => $jsonResult
            ]);

            if (isset($jsonResult['payUrl']) && !empty($jsonResult['payUrl'])) {

                $order->momo_payment_url = $jsonResult['payUrl'];
                $order->momo_order_id = $orderId;
                $order->payment_url_created_at = now();
                $order->save();

                return $jsonResult['payUrl'];
            }

            Log::error('MoMo payment URL creation failed', [
                'response' => $jsonResult
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('MoMo payment request failed: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'data' => $data
            ]);
            return null;
        }
    }

    public function handleReturn(Request $request)
    {
        $config = $this->getMomoConfig();

        $partnerCode = $request->partnerCode;
        $orderId = $request->orderId;
        $requestId = $request->requestId;
        $amount = $request->amount;
        $orderInfo = $request->orderInfo;
        $orderType = $request->orderType;
        $transId = $request->transId;
        $resultCode = $request->resultCode;
        $message = $request->message;
        $payType = $request->payType;
        $responseTime = $request->responseTime;
        $extraData = $request->extraData;
        $signature = $request->signature;


        $rawHash = "accessKey=" . $config['accessKey'] . "&amount=" . $amount . "&extraData=" . $extraData . "&message=" . $message . "&orderId=" . $orderId . "&orderInfo=" . $orderInfo .
            "&orderType=" . $orderType . "&partnerCode=" . $partnerCode . "&payType=" . $payType . "&requestId=" . $requestId . "&responseTime=" . $responseTime .
            "&resultCode=" . $resultCode . "&transId=" . $transId;

        $partnerSignature = hash_hmac("sha256", $rawHash, $config['secretKey']);

        Log::info('MoMo Return Data', [
            'request_data' => $request->all(),
            'calculated_signature' => $partnerSignature,
            'received_signature' => $signature,
            'signature_valid' => $signature == $partnerSignature
        ]);

        if ($signature == $partnerSignature) {


            $order = MarketOrder::where('momo_order_id', $orderId)->first();


            if (!$order) {
                $order = MarketOrder::where('order_number', $orderId)->first();


                Log::info('MoMo Fallback Order Search', [
                    'momo_order_id' => $orderId,
                    'found_by_order_number' => $order ? true : false
                ]);
            }

            if ($order) {
                if ($resultCode == '0') {

                    $order->update([
                        'payment_status' => 'paid',
                        'status' => 'confirmed'
                    ]);


                    $payment = MarketPayment::where('market_order_id', $order->id)
                        ->where('payment_method', 'momo')
                        ->first();

                    if ($payment) {
                        $payment->update([
                            'status' => 'completed',
                            'transaction_id' => $transId,
                            'payment_details' => $request->all(),
                            'reference_number' => $orderId,
                            'transaction_date' => now(),
                            'approved_by' => 'MoMo',
                            'approved_at' => now()
                        ]);
                    }

                    Log::info('MoMo payment successful via return URL', [
                        'order_number' => $orderId,
                        'transaction_id' => $transId
                    ]);


                    try {
                        MarketplaceMailService::sendPaymentConfirmation($order, $payment);
                    } catch (\Exception $e) {
                        Log::warning('Failed to send payment confirmation email', [
                            'order_id' => $order->id,
                            'payment_id' => $payment ? $payment->id : null,
                            'error' => $e->getMessage()
                        ]);
                    }

                    return redirect()->route('marketplace.checkout.success', ['order_id' => $order->id]);
                } else {

                    $payment = MarketPayment::where('market_order_id', $order->id)
                        ->where('payment_method', 'momo')
                        ->first();

                    if ($payment) {
                        $payment->update([
                            'status' => 'failed',
                            'payment_details' => $request->all(),
                            'reference_number' => $orderId,
                            'transaction_date' => now()
                        ]);
                    }

                    Log::warning('MoMo payment failed via return URL', [
                        'order_number' => $orderId,
                        'result_code' => $resultCode,
                        'message' => $message
                    ]);

                    return redirect()->route('marketplace.checkout.failure', [
                        'order_id' => $order->id,
                        'message' => $this->getMomoErrorMessage($resultCode)
                    ]);
                }
            } else {
                return redirect()->route('marketplace.checkout.failure', [
                    'message' => 'Không tìm thấy đơn hàng: ' . $orderId
                ]);
            }
        } else {
            return redirect()->route('marketplace.checkout.failure', [
                'message' => 'Chữ ký không hợp lệ từ MoMo'
            ]);
        }
    }

    public function handleIPN(Request $request)
    {
        $config = $this->getMomoConfig();
        $returnData = [];

        try {
            $partnerCode = $request->partnerCode;
            $orderId = $request->orderId;
            $requestId = $request->requestId;
            $amount = $request->amount;
            $orderInfo = $request->orderInfo;
            $orderType = $request->orderType;
            $transId = $request->transId;
            $resultCode = $request->resultCode;
            $message = $request->message;
            $payType = $request->payType;
            $responseTime = $request->responseTime;
            $extraData = $request->extraData;
            $signature = $request->signature;


            $rawHash = "accessKey=" . $config['accessKey'] . "&amount=" . $amount . "&extraData=" . $extraData . "&message=" . $message . "&orderId=" . $orderId . "&orderInfo=" . $orderInfo .
                "&orderType=" . $orderType . "&partnerCode=" . $partnerCode . "&payType=" . $payType . "&requestId=" . $requestId . "&responseTime=" . $responseTime .
                "&resultCode=" . $resultCode . "&transId=" . $transId;

            $partnerSignature = hash_hmac("sha256", $rawHash, $config['secretKey']);

            if ($signature == $partnerSignature) {


                $order = MarketOrder::where('momo_order_id', $orderId)->first();


                if (!$order) {
                    $order = MarketOrder::where('order_number', $orderId)->first();


                    Log::info('MoMo IPN Fallback Order Search', [
                        'momo_order_id' => $orderId,
                        'found_by_order_number' => $order ? true : false
                    ]);
                }

                if ($order) {
                    if ($order->total_amount == $amount) {
                        if ($order->payment_status == 'unpaid') {
                            if ($resultCode == '0') {

                                $order->update([
                                    'payment_status' => 'paid',
                                    'status' => 'confirmed'
                                ]);


                                $payment = MarketPayment::where('market_order_id', $order->id)
                                    ->where('payment_method', 'momo')
                                    ->first();

                                if ($payment) {
                                    $payment->update([
                                        'status' => 'completed',
                                        'transaction_id' => $transId,
                                        'payment_details' => $request->all(),
                                        'reference_number' => $orderId,
                                        'transaction_date' => now(),
                                        'approved_by' => 'MoMo',
                                        'approved_at' => now()
                                    ]);
                                }

                                Log::info('MoMo payment successful', [
                                    'order_number' => $orderId,
                                    'transaction_id' => $transId,
                                    'amount' => $amount
                                ]);


                                try {
                                    MarketplaceMailService::sendPaymentConfirmation($order, $payment);
                                } catch (\Exception $e) {
                                    Log::warning('Failed to send payment confirmation email', [
                                        'order_id' => $order->id,
                                        'payment_id' => $payment ? $payment->id : null,
                                        'error' => $e->getMessage()
                                    ]);
                                }

                                $returnData['RspCode'] = '00';
                                $returnData['Message'] = 'Confirm Success';
                            } else {

                                $payment = MarketPayment::where('market_order_id', $order->id)
                                    ->where('payment_method', 'momo')
                                    ->first();

                                if ($payment) {
                                    $payment->update([
                                        'status' => 'failed',
                                        'payment_details' => $request->all(),
                                        'reference_number' => $orderId,
                                        'transaction_date' => now()
                                    ]);
                                }

                                Log::warning('MoMo payment failed', [
                                    'order_number' => $orderId,
                                    'result_code' => $resultCode,
                                    'amount' => $amount
                                ]);

                                $returnData['RspCode'] = '00';
                                $returnData['Message'] = 'Payment failed but confirmed';
                            }
                        } else {
                            $returnData['RspCode'] = '02';
                            $returnData['Message'] = 'Order already confirmed';
                        }
                    } else {
                        $returnData['RspCode'] = '04';
                        $returnData['Message'] = 'Invalid amount';
                    }
                } else {
                    $returnData['RspCode'] = '01';
                    $returnData['Message'] = 'Order not found';
                }
            } else {
                $returnData['RspCode'] = '97';
                $returnData['Message'] = 'Invalid signature';
            }
        } catch (\Exception $e) {
            Log::error('MoMo IPN error: ' . $e->getMessage(), [
                'request' => $request->all()
            ]);

            $returnData['RspCode'] = '99';
            $returnData['Message'] = 'Unknown error';
        }

        return response()->json($returnData);
    }

    private function getMomoErrorMessage($resultCode)
    {
        $errorMessages = [
            '9000' => 'Giao dịch được khởi tạo, chờ người dùng xác nhận thanh toán.',
            '8000' => 'Giao dịch được cập nhật trạng thái thành công.',
            '7000' => 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).',
            '9001' => 'Giao dịch thất bại do người dùng từ chối xác nhận thanh toán.',
            '9002' => 'Giao dịch thất bại do người dùng không xác nhận thanh toán trong thời gian quy định.',
            '9004' => 'Giao dịch thất bại do số tiền vượt quá hạn mức thanh toán của người dùng.',
            '9005' => 'Giao dịch thất bại do url hoặc QR code đã hết hạn.',
            '9006' => 'Giao dịch thất bại do người dùng đã từng thực hiện giao dịch thành công trước đó.',
            '9007' => 'Giao dịch thất bại do tài khoản người dùng đang bị khóa.',
            '9010' => 'Giao dịch thất bại do chưa đăng ký tài khoản định danh ví MoMo.',
            '9012' => 'Giao dịch thất bại do tài khoản người dùng bị phong tỏa.',
            '99' => 'Lỗi chưa được định nghĩa.'
        ];

        return $errorMessages[$resultCode] ?? 'Giao dịch thất bại';
    }

    public function refund(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:market_orders,id',
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255'
        ]);



        return response()->json([
            'success' => true,
            'message' => 'Yêu cầu hoàn tiền đã được gửi'
        ]);
    }
}
