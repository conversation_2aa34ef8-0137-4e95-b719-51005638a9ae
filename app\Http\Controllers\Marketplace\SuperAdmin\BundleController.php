<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductBundle;
use App\Models\ProductBundleItem;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class BundleController extends Controller
{
    public function index(Request $request)
    {
        $query = ProductBundle::with(['bundleItems.product'])
            ->withCount('bundleItems');


        if ($request->has('search') && $request->search !== null) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            });
        }


        if ($request->has('status') && $request->status !== null) {
            $status = $request->status === 'true' || $request->status === '1' ? 1 : 0;
            $query->where('is_active', $status);
        }


        if ($request->has('is_featured') && $request->is_featured !== null) {
            $featured = $request->is_featured === 'true' || $request->is_featured === '1' ? 1 : 0;
            $query->where('is_featured', $featured);
        }


        if ($request->has('date_from') && $request->date_from) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }


        $sortField = $request->sort ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($sortField, $direction);

        $bundles = $query->paginate(10)->withQueryString();

        return Inertia::render('Marketplace/Bundles/Index', [
            'bundles' => $bundles,
            'filters' => $request->only(['search', 'status', 'is_featured', 'date_from', 'date_to', 'sort', 'direction'])
        ]);
    }

    public function create()
    {
        $products = Product::where('status', 1)
            ->where('quantity', '>', 0)
            ->select('id', 'name', 'sale_price', 'quantity', 'image_url', 'category_id')
            ->with('category:id,name')
            ->get();

        return Inertia::render('Marketplace/Bundles/Create', [
            'products' => $products
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:product_bundles,name',
            'description' => 'nullable|string',
            'bundle_price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after_or_equal:starts_at',
            'stock_quantity' => 'required|integer|min:0',
            'image_url' => 'nullable|image|max:2048',
            'products' => 'required|array|min:2',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.product_options' => 'nullable|array',
        ], [
            'name.unique' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.',
            'products.required' => 'Trường sản phẩm là bắt buộc.',
            'products.array' => 'Dữ liệu sản phẩm phải là mảng.',
            'products.min' => 'Bundle phải có ít nhất 2 sản phẩm.',
            'products.*.product_id.required' => 'ID sản phẩm là bắt buộc.',
            'products.*.product_id.exists' => 'Sản phẩm được chọn không tồn tại.',
            'products.*.quantity.required' => 'Số lượng sản phẩm là bắt buộc.',
            'products.*.quantity.min' => 'Số lượng sản phẩm phải lớn hơn 0.',
            'products.*.product_options.array' => 'Tùy chọn sản phẩm phải là một mảng.',
        ]);

        DB::beginTransaction();

        try {
            $originalPrice = 0;
            foreach ($request->products as $productData) {
                $product = Product::findOrFail($productData['product_id']);
                $originalPrice += $product->sale_price * $productData['quantity'];
            }

            $baseSlug = Str::slug($request->name);
            $slug = $baseSlug;
            $counter = 1;

            while (ProductBundle::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $bundle = ProductBundle::create([
                'name' => $request->name,
                'description' => $request->description,
                'slug' => $slug,
                'image_url' => $request->hasFile('image_url') ? $request->file('image_url')->store('bundles', 'public') : null,
                'original_price' => $originalPrice,
                'bundle_price' => $request->bundle_price,
                'discount_amount' => $originalPrice - $request->bundle_price,
                'discount_percentage' => $originalPrice > 0 ? round((($originalPrice - $request->bundle_price) / $originalPrice) * 100, 2) : 0,
                'is_active' => $request->is_active ?? true,
                'is_featured' => $request->is_featured ?? false,
                'starts_at' => $request->starts_at,
                'expires_at' => $request->expires_at,
                'stock_quantity' => $request->stock_quantity,
                'sort_order' => ProductBundle::max('sort_order') + 1,
            ]);


            foreach ($request->products as $index => $productData) {
                $product = Product::findOrFail($productData['product_id']);

                ProductBundleItem::create([
                    'bundle_id' => $bundle->id,
                    'product_id' => $productData['product_id'],
                    'quantity' => $productData['quantity'],
                    'item_price' => $product->sale_price,
                    'sort_order' => $index + 1,
                    'product_options' => $productData['product_options'] ?? null,
                ]);
            }

            DB::commit();

            return redirect()->route('superadmin.marketplace.bundles.index')
                ->with('success', __('Bundle created successfully'));

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollback();

            if ($e->errorInfo[1] == 1062) {
                if (strpos($e->getMessage(), 'product_bundles_slug_unique') !== false) {
                    return back()->withErrors(['name' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.'])->withInput();
                }
                if (strpos($e->getMessage(), 'product_bundles_name_unique') !== false) {
                    return back()->withErrors(['name' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.'])->withInput();
                }
                return back()->withErrors(['error' => 'Dữ liệu bị trùng lặp. Vui lòng kiểm tra lại thông tin.'])->withInput();
            }

            Log::error('Bundle creation failed with query exception:', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return back()->withErrors(['error' => 'Có lỗi xảy ra khi tạo bundle. Vui lòng thử lại.'])->withInput();

        } catch (\Exception $e) {
            DB::rollback();

            Log::error('Bundle creation failed:', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return back()->withErrors(['error' => 'Có lỗi xảy ra khi tạo bundle. Vui lòng thử lại.'])->withInput();
        }
    }

    public function show($id)
    {
        try {
            $bundle = ProductBundle::with(['bundleItems.product.category'])->findOrFail($id);

            return Inertia::render('Marketplace/Bundles/Show', [
                'bundle' => $bundle
            ]);
        } catch (\Exception $e) {
            return redirect()->route('superadmin.marketplace.bundles.index')
                ->with('error', 'Bundle not found: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        try {
            $bundle = ProductBundle::with(['bundleItems.product'])->findOrFail($id);

            $products = Product::where('status', 1)
                ->select('id', 'name', 'sale_price', 'quantity', 'image_url', 'category_id')
                ->with('category:id,name')
                ->get();

            return Inertia::render('Marketplace/Bundles/Edit', [
                'bundle' => $bundle,
                'products' => $products
            ]);
        } catch (\Exception $e) {
            return redirect()->route('superadmin.marketplace.bundles.index')
                ->with('error', 'Bundle not found: ' . $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:product_bundles,name,' . $id,
                'description' => 'nullable|string',
                'bundle_price' => 'required|numeric|min:0',
                'is_active' => 'boolean',
                'is_featured' => 'boolean',
                'starts_at' => 'nullable|date',
                'expires_at' => 'nullable|date|after_or_equal:starts_at',
                'stock_quantity' => 'required|integer|min:0',
                'image_url' => 'nullable|image|max:2048',
                'products' => 'required|array|min:2',
                'products.*.product_id' => 'required|exists:products,id',
                'products.*.quantity' => 'required|integer|min:1',
                'products.*.product_options' => 'nullable|array',
            ], [
                'products.required' => 'Trường sản phẩm là bắt buộc.',
                'products.min' => 'Bundle phải có ít nhất 2 sản phẩm.',
                'products.*.product_id.required' => 'ID sản phẩm là bắt buộc.',
                'products.*.product_id.exists' => 'Sản phẩm được chọn không tồn tại.',
                'products.*.quantity.required' => 'Số lượng sản phẩm là bắt buộc.',
                'products.*.quantity.min' => 'Số lượng sản phẩm phải lớn hơn 0.',
                'products.*.product_options.array' => 'Tùy chọn sản phẩm phải là một mảng.',
                'name.unique' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.',
            ]);

            DB::beginTransaction();

            $bundle = ProductBundle::findOrFail($id);

            $originalPrice = 0;
            foreach ($request->products as $productData) {
                $product = Product::findOrFail($productData['product_id']);
                $originalPrice += $product->sale_price * $productData['quantity'];
            }

            $slug = $bundle->slug;
            if ($bundle->name !== $request->name) {
                $baseSlug = Str::slug($request->name);
                $slug = $baseSlug;
                $counter = 1;

                while (ProductBundle::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                    $slug = $baseSlug . '-' . $counter;
                    $counter++;
                }
            }

            $updateData = [
                'name' => $request->name,
                'description' => $request->description,
                'slug' => $slug,
                'original_price' => $originalPrice,
                'bundle_price' => $request->bundle_price,
                'discount_amount' => $originalPrice - $request->bundle_price,
                'discount_percentage' => $originalPrice > 0 ? round((($originalPrice - $request->bundle_price) / $originalPrice) * 100, 2) : 0,
                'is_active' => $request->is_active ?? true,
                'is_featured' => $request->is_featured ?? false,
                'starts_at' => $request->starts_at,
                'expires_at' => $request->expires_at,
                'stock_quantity' => $request->stock_quantity,
            ];

            if ($request->hasFile('image_url')) {
                if ($bundle->image_url && Storage::disk('public')->exists($bundle->image_url)) {
                    Storage::disk('public')->delete($bundle->image_url);
                }
                $updateData['image_url'] = $request->file('image_url')->store('bundles', 'public');
            }

            $bundle->update($updateData);


            $bundle->bundleItems()->delete();


            foreach ($request->products as $index => $productData) {
                $product = Product::findOrFail($productData['product_id']);

                ProductBundleItem::create([
                    'bundle_id' => $bundle->id,
                    'product_id' => $productData['product_id'],
                    'quantity' => $productData['quantity'],
                    'item_price' => $product->sale_price,
                    'sort_order' => $index + 1,
                    'product_options' => $productData['product_options'] ?? null,
                ]);
            }

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Bundle updated successfully',
                    'bundle' => $bundle->fresh(['bundleItems.product'])
                ]);
            }

            return redirect()->route('superadmin.marketplace.bundles.index')
                ->with('success', 'Bundle updated successfully');

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollback();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->errors()
                ], 422);
            }

            return back()->withErrors($e->errors())->withInput();

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollback();

            if ($e->errorInfo[1] == 1062) {
                if (strpos($e->getMessage(), 'product_bundles_slug_unique') !== false) {
                    $error = ['name' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.'];
                } elseif (strpos($e->getMessage(), 'product_bundles_name_unique') !== false) {
                    $error = ['name' => 'Tên bundle này đã tồn tại. Vui lòng chọn tên khác.'];
                } else {
                    $error = ['error' => 'Dữ liệu bị trùng lặp. Vui lòng kiểm tra lại thông tin.'];
                }

                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Duplicate data',
                        'errors' => $error
                    ], 422);
                }

                return back()->withErrors($error)->withInput();
            }

            $errorMessage = 'Có lỗi xảy ra khi cập nhật bundle. Vui lòng thử lại.';

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }

            return back()->withErrors(['error' => $errorMessage])->withInput();

        } catch (\Exception $e) {
            DB::rollback();

            $errorMessage = 'Có lỗi xảy ra khi cập nhật bundle. Vui lòng thử lại.';

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }

            return back()->withErrors(['error' => $errorMessage]);
        }
    }

    public function destroy($id)
    {
        try {
            $bundle = ProductBundle::findOrFail($id);

            if ($bundle->image_url && Storage::disk('public')->exists($bundle->image_url)) {
                Storage::disk('public')->delete($bundle->image_url);
            }

            $bundle->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'message' => __('marketplace.bundle_deleted_successfully')
                ]);
            }

            return redirect()->route('superadmin.marketplace.bundles.index')
                ->with('success', __('marketplace.bundle_deleted_successfully'));
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'Failed to delete bundle: ' . $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'Failed to delete bundle: ' . $e->getMessage()]);
        }
    }

    public function toggleActive($id)
    {
        try {
            $bundle = ProductBundle::findOrFail($id);
            $bundle->update([
                'is_active' => !$bundle->is_active
            ]);

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'is_active' => $bundle->is_active,
                    'message' => __('Bundle status updated successfully')
                ]);
            }

            return back()->with('success', __('Bundle status updated successfully'));
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update bundle status: ' . $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'Failed to update bundle status: ' . $e->getMessage()]);
        }
    }

    public function toggleFeatured($id)
    {
        try {
            $bundle = ProductBundle::findOrFail($id);
            $bundle->update([
                'is_featured' => !$bundle->is_featured
            ]);

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'is_featured' => $bundle->is_featured,
                    'message' => __('Bundle featured status updated successfully')
                ]);
            }

            return back()->with('success', __('Bundle featured status updated successfully'));
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update bundle featured status: ' . $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'Failed to update bundle featured status: ' . $e->getMessage()]);
        }
    }

    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'bundles' => 'required|array',
            'bundles.*.id' => 'required|exists:product_bundles,id',
            'bundles.*.sort_order' => 'required|integer|min:0',
        ]);

        DB::beginTransaction();

        try {
            foreach ($request->bundles as $bundleData) {
                ProductBundle::where('id', $bundleData['id'])
                    ->update(['sort_order' => $bundleData['sort_order']]);
            }

            DB::commit();

            return back()->with('success', __('Bundle order updated successfully'));

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update bundle order: ' . $e->getMessage()]);
        }
    }

    public function refreshPricing($id)
    {
        try {
            $bundle = ProductBundle::findOrFail($id);
            $bundle->updatePricing();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => __('Bundle pricing refreshed successfully')
                ]);
            }

            return back()->with('success', __('Bundle pricing refreshed successfully'));
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to refresh pricing: ' . $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'Failed to refresh pricing: ' . $e->getMessage()]);
        }
    }
}
