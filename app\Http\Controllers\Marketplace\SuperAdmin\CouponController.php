<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;
use App\Http\Controllers\Controller;

use App\Models\MarketCoupon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Carbon\Carbon;

class CouponController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $status = $request->input('status');
        $type = $request->input('type');
        $sort = $request->input('sort', 'created_at');
        $direction = $request->input('direction', 'desc');

        $query = MarketCoupon::query();


        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('code', 'like', "%{$search}%")
                    ->orWhere('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }


        if (in_array($status, ['0', '1'], true)) {
            $query->where('is_active', $status === '1');
        }


        if ($type) {
            $query->where('type', $type);
        }


        $query->orderBy($sort, $direction);


        $coupons = $query->paginate(10)->withQueryString();


        foreach ($coupons as $coupon) {
            $coupon->starts_at_formatted = $coupon->starts_at ? Carbon::parse($coupon->starts_at)->format('d/m/Y H:i') : null;
            $coupon->expires_at_formatted = $coupon->expires_at ? Carbon::parse($coupon->expires_at)->format('d/m/Y H:i') : null;
        }


        if ($request->wantsJson()) {
            return response()->json([
                'coupons' => $coupons,
                'filters' => [
                    'search' => $search,
                    'status' => $status,
                    'type' => $type,
                    'sort' => $sort,
                    'direction' => $direction,
                ]
            ]);
        }

        return Inertia::render('Marketplace/Coupons/Index', [
            'coupons' => $coupons,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'type' => $type,
                'sort' => $sort,
                'direction' => $direction,
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Marketplace/Coupons/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|unique:market_coupon,code|max:50',
            'name' => 'required|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,amount',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after_or_equal:starts_at',
            'applicable_products' => 'nullable|array',
            'applicable_categories' => 'nullable|array',
            'excluded_products' => 'nullable|array',
            'first_order_only' => 'boolean',
            'is_active' => 'boolean',
        ]);


        if (!in_array($validated['type'], ['percentage', 'amount'])) {
            return redirect()->back()
                ->with('flash.error', __('coupon.coupon_creation_failed') . ': Invalid type value')
                ->withInput();
        }


        if (isset($validated['starts_at'])) {
            $validated['starts_at'] = Carbon::parse($validated['starts_at']);
        }

        if (isset($validated['expires_at'])) {
            $validated['expires_at'] = Carbon::parse($validated['expires_at']);
        }

        try {

            $coupon = MarketCoupon::create($validated);

            return redirect()->route('superadmin.marketplace.coupons.index')
                ->with('flash.success', __('coupon.coupon_created_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('flash.error', __('coupon.coupon_creation_failed') . ': ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $coupon = MarketCoupon::findOrFail($id);


        $coupon->starts_at_formatted = $coupon->starts_at ? Carbon::parse($coupon->starts_at)->format('d/m/Y H:i') : null;
        $coupon->expires_at_formatted = $coupon->expires_at ? Carbon::parse($coupon->expires_at)->format('d/m/Y H:i') : null;

        return Inertia::render('Marketplace/Coupons/Show', [
            'coupon' => $coupon,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $coupon = MarketCoupon::findOrFail($id);

        return Inertia::render('Marketplace/Coupons/Edit', [
            'coupon' => $coupon,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $coupon = MarketCoupon::findOrFail($id);

        $validated = $request->validate([
            'code' => 'required|max:50|unique:market_coupon,code,' . $id,
            'name' => 'required|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,amount',
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:0',
            'usage_limit_per_user' => 'nullable|integer|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after_or_equal:starts_at',
            'applicable_products' => 'nullable|array',
            'applicable_categories' => 'nullable|array',
            'excluded_products' => 'nullable|array',
            'first_order_only' => 'boolean',
            'is_active' => 'boolean',
        ]);


        if (!in_array($validated['type'], ['percentage', 'amount'])) {
            return redirect()->back()
                ->with('flash.error', __('coupon.coupon_update_failed') . ': Invalid type value')
                ->withInput();
        }


        if (isset($validated['starts_at'])) {
            $validated['starts_at'] = Carbon::parse($validated['starts_at']);
        }

        if (isset($validated['expires_at'])) {
            $validated['expires_at'] = Carbon::parse($validated['expires_at']);
        }

        try {

            $coupon->update($validated);

            return redirect()->route('superadmin.marketplace.coupons.index')
                ->with('flash.success', __('coupon.coupon_updated_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('flash.error', __('coupon.coupon_update_failed') . ': ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $coupon = MarketCoupon::findOrFail($id);
            $coupon->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'message' => __('coupon.coupon_deleted_successfully')
                ]);
            }

            return redirect()->route('superadmin.marketplace.coupons.index')
                ->with('flash.success', __('coupon.coupon_deleted_successfully'));
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => __('coupon.coupon_deletion_failed') . ': ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('flash.error', __('coupon.coupon_deletion_failed') . ': ' . $e->getMessage());
        }
    }

    /**
     * Toggle coupon active status.
     */
    public function toggleStatus($id)
    {
        try {
            $coupon = MarketCoupon::findOrFail($id);
            $coupon->update([
                'is_active' => !$coupon->is_active
            ]);

            $message = $coupon->is_active ?
                __('coupon.coupon_activated_successfully') :
                __('coupon.coupon_deactivated_successfully');

            if (request()->wantsJson()) {
                return response()->json([
                    'is_active' => $coupon->is_active,
                    'message' => $message
                ]);
            }

            return redirect()->back()->with('flash.success', $message);
        } catch (\Exception $e) {
            $errorMessage = __('coupon.status_update_failed') . ': ' . $e->getMessage();

            if (request()->wantsJson()) {
                return response()->json([
                    'message' => $errorMessage
                ], 500);
            }

            return redirect()->back()->with('flash.error', $errorMessage);
        }
    }
}
