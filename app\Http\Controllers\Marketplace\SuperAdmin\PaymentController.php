<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;
use App\Http\Controllers\Controller;
use App\Models\MarketPayment;
use App\Models\MarketOrder;
use App\Services\Marketplace\MarketplaceMailService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'payment_method', 'from_date', 'to_date', 'sort', 'direction']);

        $query = MarketPayment::with(['order:id,order_number', 'user', 'customer']);


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('order', function($query) use ($search) {
                      $query->where('order_number', 'like', "%{$search}%");
                  });
            });
        }


        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }


        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }


        if ($request->has('from_date') && !empty($request->from_date)) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->has('to_date') && !empty($request->to_date)) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }


        $sortField = $filters['sort'] ?? 'created_at';
        $sortDirection = $filters['direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $payments = $query->paginate(15)->withQueryString();


        $this->ensureTransactionIds();

        $stats = $this->getPaymentStatistics();

        return Inertia::render('Marketplace/Payments/Index', [
            'payments' => $payments,
            'filters' => $filters,
            'stats' => $stats,
            'statuses' => $this->getPaymentStatuses(),
            'paymentMethods' => $this->getPaymentMethods(),
        ]);
    }

    public function show(MarketPayment $payment)
    {
        $payment->load([
            'order:id,order_number',
            'user:id,name,email',
            'customer:id,name,email'
        ]);

        return Inertia::render('Marketplace/Payments/Show', [
            'payment' => $payment
        ]);
    }

    public function generateTransactionId(MarketPayment $payment)
    {
        if (empty($payment->transaction_id)) {
            $payment->transaction_id = MarketPayment::generateTransactionId();
            $payment->save();

            return redirect()->back()->with('success', __('payments.transaction_id_generated'));
        }

        return redirect()->back()->with('info', __('payments.transaction_id_already_exists'));
    }

    public function generateAllTransactionIds()
    {
        $count = 0;
        $paymentsWithoutTransactionId = MarketPayment::whereNull('transaction_id')
            ->orWhere('transaction_id', '')
            ->get();

        foreach ($paymentsWithoutTransactionId as $payment) {

            if ($payment->order) {
                $payment->transaction_id = $payment->order->generateTransactionId($payment->payment_method);
            } else {
                $payment->transaction_id = MarketPayment::generateTransactionId();
            }
            $payment->save();
            $count++;
        }

        if ($count > 0) {
            return redirect()->back()->with('success', __('payments.transaction_ids_generated', ['count' => $count]));
        }

        return redirect()->back()->with('info', __('payments.no_missing_transaction_ids'));
    }

    public function updateStatus(Request $request, MarketPayment $payment)
    {

        if ($payment->status === 'completed') {
            return redirect()->back()->with('error', __('payments.completed_payment_edit_not_allowed'));
        }

        $request->validate([
            'status' => 'required|string|in:pending,processing,completed,failed,refunded,cancelled',
            'notes' => 'nullable|string|max:500'
        ]);

        $oldStatus = $payment->status;
        $newStatus = (string) $request->input('status');

        DB::transaction(function () use ($payment, $request, $oldStatus, $newStatus) {
            $updateData = [
                'status' => $newStatus,
                'notes' => $request->input('notes')
            ];


            if ($newStatus === 'completed' && $oldStatus !== 'completed') {
                $updateData['approved_by'] = Auth::user()->name ?? 'Admin';
                $updateData['approved_at'] = now();
            }

            $payment->update($updateData);


            if ($payment->market_order_id) {
                $order = MarketOrder::find($payment->market_order_id);
                if ($order) {
                    $this->updateOrderPaymentStatus($order);
                }
            }
        });


        try {
            $failureReason = ($newStatus === 'failed') ? $request->input('notes') : null;
            $refundReason = ($newStatus === 'refunded') ? $request->input('notes') : null;

            $order = MarketOrder::find($payment->market_order_id);
            if ($order && $order instanceof MarketOrder) {
                MarketplaceMailService::sendPaymentStatusUpdate(
                    $order,
                    $payment,
                    $oldStatus,
                    $failureReason,
                    $refundReason
                );
            }
        } catch (\Exception $e) {
            Log::warning('Failed to send payment status update email', [
                'payment_id' => $payment->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }

        return redirect()->back()->with('success', __('payments.status_updated'));
    }

    public function export(Request $request)
    {
        $format = $request->input('format', 'excel');


        $query = MarketPayment::with(['order:id,order_number', 'user', 'customer', 'approver']);


        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('order', function($query) use ($search) {
                      $query->where('order_number', 'like', "%{$search}%");
                  });
            });
        }


        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }


        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }


        if ($request->has('from_date') && !empty($request->from_date)) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->has('to_date') && !empty($request->to_date)) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }


        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);


        $payments = $query->get();


        $exportData = $payments->map(function ($payment) {
            return [
                'Mã giao dịch' => $payment->transaction_id ?: '-',
                'Mã tham chiếu' => $payment->reference_number ?: '-',
                'Số đơn hàng' => $payment->order ? $payment->order->order_number : '#' . $payment->market_order_id,
                'Số tiền' => number_format($payment->amount, 0, ',', '.') . ' ₫',
                'Phương thức thanh toán' => $this->getPaymentMethodDisplayName($payment->payment_method),
                'Trạng thái' => $this->getPaymentStatusDisplayName($payment->status),
                'Khách hàng' => $payment->customer ? $payment->customer->name : ($payment->user ? $payment->user->name : '-'),
                'Email khách hàng' => $payment->customer ? $payment->customer->email : ($payment->user ? $payment->user->email : '-'),
                'Người duyệt' => $payment->approver ? $payment->approver->name : '-',
                'Ngày duyệt' => $payment->approved_at ? $payment->approved_at->format('d/m/Y H:i') : '-',
                'Ngày tạo' => $payment->created_at->format('d/m/Y H:i'),
                'Ghi chú' => $payment->notes ?: '-',
            ];
        });


        $filename = 'payments_export';
        if ($request->has('from_date') && $request->has('to_date')) {
            $filename .= '_' . $request->from_date . '_to_' . $request->to_date;
        } else {
            $filename .= '_' . now()->format('Y-m-d');
        }


        if ($format === 'pdf') {
            return $this->exportPDF($exportData, $filename);
        } else {
            return $this->exportExcel($exportData, $filename);
        }
    }

    private function getPaymentStatistics()
    {
        return [
            'total_count' => MarketPayment::count(),
            'total_amount' => MarketPayment::sum('amount'),
            'completed_amount' => MarketPayment::where('status', 'completed')->sum('amount'),
            'pending_amount' => MarketPayment::where('status', 'pending')->sum('amount'),
        ];
    }

    private function getPaymentStatuses()
    {
        return [
            'pending',
            'processing',
            'completed',
            'failed',
            'refunded',
            'cancelled'
        ];
    }

    private function getPaymentMethods()
    {
        return MarketPayment::distinct('payment_method')
            ->whereNotNull('payment_method')
            ->pluck('payment_method')
            ->toArray();
    }

    private function updateOrderPaymentStatus($order)
    {
        $totalPaid = $order->payments()->where('status', 'completed')->sum('amount');
        $totalOrderAmount = $order->total_amount;

        if ($totalPaid >= $totalOrderAmount) {
            $order->payment_status = 'paid';
        } elseif ($totalPaid > 0) {
            $order->payment_status = 'partially_paid';
        } else {
            $order->payment_status = 'unpaid';
        }

        $order->save();
    }

    private function getPaymentMethodDisplayName($method)
    {
        $methodNames = [
            'cash' => 'Thanh toán khi nhận hàng (COD)',
            'momo' => 'Ví điện tử MoMo',
            'vnpay' => 'VNPay',
            'bank_transfer' => 'Chuyển khoản ngân hàng'
        ];

        return $methodNames[$method] ?? $method;
    }

    private function getPaymentStatusDisplayName($status)
    {
        $statusNames = [
            'pending' => 'Chờ xử lý',
            'processing' => 'Đang xử lý',
            'completed' => 'Hoàn thành',
            'failed' => 'Thất bại',
            'refunded' => 'Đã hoàn tiền',
            'cancelled' => 'Đã hủy'
        ];

        return $statusNames[$status] ?? $status;
    }

    /**
     * Export data to Excel
     */
    private function exportExcel($data, $filename)
    {

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();


        if ($data->count() > 0) {
            $headers = array_keys($data->first());
            $columnIndex = 1;
            foreach ($headers as $header) {
                $sheet->setCellValue([$columnIndex, 1], $header);
                $columnIndex++;
            }


            $headerRange = 'A1:' . chr(64 + count($headers)) . '1';
            $sheet->getStyle($headerRange)->getFont()->setBold(true);
            $sheet->getStyle($headerRange)->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFE2E8F0');
        }


        if ($data->count() > 0) {
            $rowIndex = 2;
            foreach ($data as $row) {
                $columnIndex = 1;
                foreach ($row as $value) {
                    $sheet->setCellValue([$columnIndex, $rowIndex], $value);
                    $columnIndex++;
                }
                $rowIndex++;
            }
        }


        foreach (range('A', chr(64 + count(array_keys($data->first())))) as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }


        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);


        return response()->download($tempFile, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export data to PDF
     */
    private function exportPDF($data, $filename)
    {

        $columnCount = $data->count() > 0 ? count($data->first()) : 0;
        $columnWidths = $this->calculateColumnWidths($columnCount);


        $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $html .= '<style>
            body { font-family: DejaVu Sans, sans-serif; font-size: 10px; }
            .container { margin: 10px; }
            h1 { color: #333; text-align: center; margin-bottom: 15px; font-size: 16px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; table-layout: fixed; }
            th, td { border: 1px solid #ddd; padding: 5px; text-align: left; font-size: 8px; overflow: hidden; word-wrap: break-word; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }';


        foreach ($columnWidths as $index => $width) {
            $html .= '.col-' . $index . ' { width: ' . $width . '%; }';
        }

        $html .= '</style>';
        $html .= '</head><body>';

        $html .= '<div class="container">';
        $html .= '<h1>Báo cáo thanh toán</h1>';


        $html .= '<p class="text-right">Ngày xuất: ' . date('d/m/Y H:i') . '</p>';

        $html .= '<table>';


        $columnClasses = [];
        if ($data->count() > 0) {
            $headers = array_keys($data->first());
            foreach ($headers as $index => $header) {

                $columnClasses[$index] = 'col-' . $index;
            }
        }


        if ($data->count() > 0) {
            $html .= '<thead><tr>';
            foreach (array_keys($data->first()) as $index => $header) {
                $class = $columnClasses[$index] ?? '';
                $html .= '<th class="' . $class . '">' . htmlspecialchars($header) . '</th>';
            }
            $html .= '</tr></thead>';
        }


        $html .= '<tbody>';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $index => $value) {
                $class = $columnClasses[$index] ?? '';
                $html .= '<td class="' . $class . '">' . htmlspecialchars($value) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';

        $html .= '</table>';
        $html .= '</div>';
        $html .= '</body></html>';


        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);


        if ($columnCount > 8) {
            $options->set('defaultPaperSize', 'A3');
        } else {
            $options->set('defaultPaperSize', 'A4');
        }
        $options->set('defaultPaperOrientation', 'landscape');

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->render();


        $pdfContent = $dompdf->output();
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);


        return response()->download($tempFile, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Calculate optimal column widths based on the number of columns
     *
     * @param int $columnCount
     * @return array
     */
    private function calculateColumnWidths($columnCount)
    {
        $widths = [];

        if ($columnCount <= 0) {
            return $widths;
        }


        $baseWidth = 100 / $columnCount;

        for ($i = 0; $i < $columnCount; $i++) {
            $widths[$i] = $baseWidth;
        }

        return $widths;
    }

    /**
     * Ensure all payments have transaction IDs
     */
    protected function ensureTransactionIds()
    {
        $paymentsWithoutTransactionId = MarketPayment::whereNull('transaction_id')
            ->orWhere('transaction_id', '')
            ->get();

        foreach ($paymentsWithoutTransactionId as $payment) {
            $payment->transaction_id = MarketPayment::generateTransactionId();
            $payment->save();
        }
    }
}
