<?php

namespace App\Http\Controllers\Marketplace\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Crypt;

class SettingController extends Controller
{
    public function index()
    {
        // Get GHN settings and decrypt API key if it exists
        $ghnSettings = SystemSettingService::get('market_ghn_setting', [
            'active' => false,
            'shop_id' => '',
            'use_dynamic_pricing' => false,
            'environment' => 'dev'
        ]);

        // Only decrypt if api_key is not empty
        if (!empty($ghnSettings['api_key'])) {
            try {
                $ghnSettings['api_key'] = Crypt::decryptString($ghnSettings['api_key']);
            } catch (\Exception $e) {
                // If decryption fails, set empty string
                $ghnSettings['api_key'] = '';
            }
        }

        // Get payment settings and decrypt sensitive fields
        $payment = [
            'cash_enabled' => SystemSettingService::get('market_cash_payment', ['active' => true])['active'] ?? true,
            'momo' => SystemSettingService::get('market_momo_payment', []),
            'vnpay' => SystemSettingService::get('market_vnpay_payment', []),
            'bank_transfer' => SystemSettingService::get('market_bank_transfer', [])
        ];

        // Decrypt sensitive Momo keys
        if (!empty($payment['momo']['accessKey'])) {
            try {
                $payment['momo']['accessKey'] = Crypt::decryptString($payment['momo']['accessKey']);
            } catch (\Exception $e) {
                $payment['momo']['accessKey'] = '';
            }
        }
        if (!empty($payment['momo']['secretKey'])) {
            try {
                $payment['momo']['secretKey'] = Crypt::decryptString($payment['momo']['secretKey']);
            } catch (\Exception $e) {
                $payment['momo']['secretKey'] = '';
            }
        }

        // Decrypt sensitive VNPay key
        if (!empty($payment['vnpay']['vnp_HashSecret'])) {
            try {
                $payment['vnpay']['vnp_HashSecret'] = Crypt::decryptString($payment['vnpay']['vnp_HashSecret']);
            } catch (\Exception $e) {
                $payment['vnpay']['vnp_HashSecret'] = '';
            }
        }

        // Decrypt email password
        $emailSettings = SystemSettingService::get('market_email_setting', []);
        if (!empty($emailSettings['email_password'])) {
            try {
                $emailSettings['email_password'] = Crypt::decryptString($emailSettings['email_password']);
            } catch (\Exception $e) {
                $emailSettings['email_password'] = '';
            }
        }

        return Inertia::render('Marketplace/Settings/Index', [
            'settings' => [
                'general' => [
                    'marketplace_name' => SystemSettingService::get('marketplace_name', 'Pickleball Marketplace'),
                    'marketplace_description' => SystemSettingService::get('marketplace_description', ''),
                    'marketplace_logo' => SystemSettingService::get('marketplace_logo', ''),
                    'maintenance_mode' => SystemSettingService::get('maintenance_mode', false),
                    'allow_guest_checkout' => SystemSettingService::get('allow_guest_checkout', true),
                    'default_currency' => SystemSettingService::get('default_currency', 'VND'),
                    'tax_rate' => SystemSettingService::get('tax_rate', 0),
                ],
                'email' => $emailSettings,
                'payment' => $payment,
                'shipping' => [
                    'ghn' => $ghnSettings,
                ]
            ]
        ]);
    }

    public function updateGeneral(Request $request)
    {
        $request->validate([
            'marketplace_name' => 'required|string|max:255',
            'marketplace_description' => 'nullable|string',
            'default_currency' => 'required|string|max:3',
            'tax_rate' => 'required|numeric|min:0|max:100',
        ]);

        SystemSettingService::set('marketplace_name', $request->marketplace_name, 'marketplace_general', 'Tên Marketplace');
        SystemSettingService::set('marketplace_description', $request->marketplace_description, 'marketplace_general', 'Mô tả Marketplace');
        SystemSettingService::set('default_currency', $request->default_currency, 'marketplace_general', 'Tiền tệ mặc định');
        SystemSettingService::set('tax_rate', $request->tax_rate, 'marketplace_general', 'Thuế VAT');
        SystemSettingService::set('maintenance_mode', $request->maintenance_mode ?? false, 'marketplace_general', 'Chế độ bảo trì');
        SystemSettingService::set('allow_guest_checkout', $request->allow_guest_checkout ?? false, 'marketplace_general', 'Cho phép mua hàng không đăng ký');

        return back()->with('success', 'Cài đặt chung đã được cập nhật thành công.');
    }

    public function updateEmail(Request $request)
    {
        $request->validate([
            'email_username' => 'required|email',
            'email_host' => 'required|string',
            'email_send_port' => 'required|integer',
        ]);

        $emailSettings = [
            'email_username' => $request->email_username,
            'email_password' => $request->email_password,
            'email_host' => $request->email_host,
            'email_send_method' => $request->email_send_method ?? 'smtp',
            'email_send_port' => $request->email_send_port,
            'email_encryption' => $request->email_encryption ?? 'tls',
        ];

        // Encrypt email_password if provided
        if (!empty($request->email_password)) {
            $emailSettings['email_password'] = Crypt::encryptString($request->email_password);
        }

        SystemSettingService::set('market_email_setting', $emailSettings, 'marketplace_email', 'Cài đặt email', 'json');

        return back()->with('success', 'Cài đặt email đã được cập nhật thành công.');
    }

    public function updatePayment(Request $request)
    {
        $request->validate([
            'cash_enabled' => 'boolean',
            'momo.active' => 'boolean',
            'momo.partnerCode' => 'nullable|string',
            'momo.accessKey' => 'nullable|string',
            'momo.secretKey' => 'nullable|string',
            'momo.endPoint' => 'nullable|url',
            'vnpay.active' => 'boolean',
            'vnpay.vnp_TmnCode' => 'nullable|string',
            'vnpay.vnp_HashSecret' => 'nullable|string',
            'vnpay.vnp_Version' => 'nullable|string',
            'vnpay.endPoint' => 'nullable|url',
            'bank_transfer.active' => 'boolean',
            'bank_transfer.accountNumber' => 'nullable|string',
            'bank_transfer.bankName' => 'nullable|string',
            'bank_transfer.accountName' => 'nullable|string',
        ]);


        $cashSettings = ['active' => $request->cash_enabled ?? false];
        SystemSettingService::set('market_cash_payment', $cashSettings, 'marketplace_payment', 'Thanh toán tiền mặt', 'json');


        if ($request->has('momo')) {
            $momoSettings = array_merge([
                'provider' => 'momo',
                'partnerCode' => '',
                'accessKey' => '',
                'secretKey' => '',
                'endPoint' => 'https://test-payment.momo.vn/v2/gateway/api/create',
                'storeId' => '',
                'partnerName' => 'PIBA Marketplace',
                'active' => false
            ], $request->momo);

            // Encrypt sensitive keys before saving
            if (!empty($momoSettings['accessKey'])) {
                $momoSettings['accessKey'] = Crypt::encryptString($momoSettings['accessKey']);
            }
            if (!empty($momoSettings['secretKey'])) {
                $momoSettings['secretKey'] = Crypt::encryptString($momoSettings['secretKey']);
            }

            SystemSettingService::set('market_momo_payment', $momoSettings, 'marketplace_payment', 'Thanh toán MoMo', 'json');
        }


        if ($request->has('vnpay')) {
            $vnpaySettings = array_merge([
                'provider' => 'vnpay',
                'vnp_TmnCode' => '',
                'vnp_HashSecret' => '',
                'vnp_Version' => '2.1.1',
                'endPoint' => 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
                'active' => false
            ], $request->vnpay);

            // Encrypt sensitive VNPay key
            if (!empty($vnpaySettings['vnp_HashSecret'])) {
                $vnpaySettings['vnp_HashSecret'] = Crypt::encryptString($vnpaySettings['vnp_HashSecret']);
            }

            SystemSettingService::set('market_vnpay_payment', $vnpaySettings, 'marketplace_payment', 'Thanh toán VNPay', 'json');
        }


        if ($request->has('bank_transfer')) {
            $bankSettings = array_merge([
                'accountNumber' => '',
                'bankName' => '',
                'accountName' => '',
                'active' => false
            ], $request->bank_transfer);

            SystemSettingService::set('market_bank_transfer', $bankSettings, 'marketplace_payment', 'Chuyển khoản ngân hàng', 'json');
        }

        return back()->with('success', 'Cài đặt thanh toán đã được cập nhật thành công.');
    }

    public function updateShipping(Request $request)
    {
        $request->validate([
            'ghn.active' => 'boolean',
            'ghn.api_key' => 'nullable|string|required_if:ghn.active,true',
            'ghn.shop_id' => 'nullable|string|required_if:ghn.active,true',
            'ghn.use_dynamic_pricing' => 'boolean',
            'ghn.environment' => 'nullable|in:dev,prod',
        ]);

        // Update GHN settings
        if ($request->has('ghn')) {
            $ghnSettings = [
                'active' => $request->ghn['active'] ?? false,
                'shop_id' => $request->ghn['shop_id'] ?? '',
                'use_dynamic_pricing' => $request->ghn['use_dynamic_pricing'] ?? false,
                'environment' => $request->ghn['environment'] ?? 'dev',
            ];

            // Only encrypt and store API key if it's provided
            if (!empty($request->ghn['api_key'])) {
                $ghnSettings['api_key'] = Crypt::encryptString($request->ghn['api_key']);
            }

            SystemSettingService::set('market_ghn_setting', $ghnSettings, 'marketplace_shipping', 'Cài đặt GHN API', 'json');
        }

        return back()->with('success', 'Cài đặt vận chuyển đã được cập nhật thành công.');
    }

    public function getPaymentConfig($provider)
    {
        $configs = [
            'momo' => SystemSettingService::get('market_momo_payment'),
            'vnpay' => SystemSettingService::get('market_vnpay_payment'),
            'bank_transfer' => SystemSettingService::get('market_bank_transfer'),
            'cash' => SystemSettingService::get('market_cash_payment')
        ];

        return response()->json($configs[$provider] ?? null);
    }
}
