<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Business;
use App\Models\Notification;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     * Checks user role and routes to appropriate handler
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('super-admin', $userRoles)) {
            return $this->superAdminIndex($request);
        } elseif (count(array_intersect(['admin'], $userRoles)) > 0) {
            return $this->businessIndex($request);
        } else {
            return $this->branchIndex($request);
        }
    }

    /**
     * Display notifications for super admin users
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    private function superAdminIndex(Request $request)
    {

        $query = Notification::query()
            ->with(['user', 'business', 'branch'])
            ->orderBy('created_at', 'desc');


        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('read_status')) {
            $query->where('is_read', $request->read_status === 'read');
        }

        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->paginate(10)->withQueryString();
        $unreadCount = Notification::where('is_read', false)->count();


        $businesses = Business::select('id', 'name')->get();
        $branches = Branch::select('id', 'name', 'business_id')->get();

        return Inertia::render('SuperAdmin/notifications/Index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'filters' => $request->only(['type', 'read_status', 'business_id', 'branch_id', 'search']),
            'businesses' => $businesses,
            'branches' => $branches,
        ]);
    }

    /**
     * Display notifications for business users
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    private function businessIndex(Request $request)
    {
        $business = $request->user()->business;


        $branchIds = $business->branches->pluck('id')->toArray();


        $query = Notification::query()
            ->where(function ($q) use ($business, $branchIds) {

                $q->where('business_id', $business->id);
                $q->orWhere(function ($subq) use ($branchIds) {
                    $subq->whereIn('branch_id', $branchIds)
                        ->whereNull('business_id');
                });
            })
            ->with(['branch'])
            ->orderBy('created_at', 'desc');


        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('read_status')) {
            $query->where('is_read', $request->read_status === 'read');
        }


        if ($request->filled('branch_id')) {
            $query->where(function ($q) use ($request, $business) {
                $q->where('branch_id', $request->branch_id)
                    ->where('business_id', $business->id);
            });
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->paginate(10)->withQueryString();


        $unreadCount = Notification::where(function ($q) use ($business, $branchIds) {
            $q->where('business_id', $business->id);
            $q->orWhere(function ($subq) use ($branchIds) {
                $subq->whereIn('branch_id', $branchIds)
                    ->whereNull('business_id');
            });
        })
            ->where('is_read', false)
            ->count();

        $branches = Branch::where('business_id', $business->id)
            ->where('status', 'active')
            ->get(['id', 'name']);

        return Inertia::render('Business/notifications/Index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'filters' => $request->only(['type', 'read_status', 'branch_id', 'search']),
            'branches' => $branches,
            'branchIds' => $branchIds,
            'businessId' => $business->id
        ]);
    }

    /**
     * Display notifications for branch users
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    private function branchIndex(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->input('per_page', 10);


        $query = Notification::query()
            ->where('branch_id', $user->branch_id)
            ->where('business_id', $user->business_id)
            ->orderBy('created_at', 'desc');


        if ($request->has('read_status')) {
            if ($request->input('read_status') === 'read') {
                $query->where('is_read', true);
            } elseif ($request->input('read_status') === 'unread') {
                $query->where('is_read', false);
            }
        }
        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }
        $notifications = $query->paginate($perPage);
        $unreadCount = Notification::query()
            ->where('branch_id', $user->branch_id)
            ->where('business_id', $user->business_id)
            ->where('is_read', false)
            ->count();
        return Inertia::render('Branchs/notifications/Index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'filters' => $request->only(['read_status', 'type', 'search']),
        ]);
    }

    /**
     * Get notifications for dropdown menu.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDropdownNotifications(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();
        $limit = $request->input('limit', 5);
        $query = Notification::query();


        if (in_array('super-admin', $userRoles)) {
            $query->with(['user', 'business', 'branch']);
        } elseif (in_array('admin', $userRoles)) {
            $business = $user->business;
            if (!$business) {
                return response()->json([
                    'data' => [],
                    'unreadCount' => 0
                ]);
            }

            $businessId = $business->id;
            $branchIds = $business->branches->pluck('id')->toArray();

            $query->where(function ($q) use ($businessId, $branchIds) {
                $q->where('business_id', $businessId)
                    ->orWhereIn('branch_id', $branchIds);
            })
                ->with(['user', 'branch']);


            if ($request->filled('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }
        } elseif (in_array('manager', $userRoles)) {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId) {
                return response()->json([
                    'data' => [],
                    'unreadCount' => 0
                ]);
            }

            $query->where('branch_id', $branchId)
                ->with(['user']);
        } else {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId || !$businessId) {
                return response()->json([
                    'data' => [],
                    'unreadCount' => 0
                ]);
            }

            $query->where('branch_id', $branchId)
                ->where('business_id', $businessId);
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        $unreadCount = $this->getUnreadCountForUser($user, $request->input('branch_id'));

        return response()->json([
            'data' => $notifications,
            'unreadCount' => $unreadCount
        ]);
    }

    /**
     * Get the count of unread notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount(Request $request)
    {
        $user = Auth::user();
        $branch_id = $request->input('branch_id');
        $count = $this->getUnreadCountForUser($user, $branch_id);
        return response()->json(['count' => $count]);
    }

    /**
     * Helper method to get unread notification count for a user
     *
     * @param  \App\Models\User|null  $user
     * @param  int|null $branch_id
     * @return int
     */
    private function getUnreadCountForUser(?User $user, $branch_id = null): int
    {
        if (!$user) {
            return 0;
        }

        $userRoles = $user->roles->pluck('name')->toArray();
        $query = Notification::where('is_read', false);


        if (in_array('super-admin', $userRoles)) {

        } elseif (in_array('admin', $userRoles)) {
            $business = $user->business;
            if (!$business) {
                return 0;
            }

            $branchIds = $business->branches->pluck('id')->toArray();
            $businessId = $business->id;

            $query->where(function ($q) use ($businessId, $branchIds, $branch_id) {
                if ($branch_id) {

                    $q->where('branch_id', $branch_id)
                        ->where('business_id', $businessId);
                } else {

                    $q->where('business_id', $businessId)
                        ->orWhereIn('branch_id', $branchIds);
                }
            });
        } elseif (in_array('manager', $userRoles)) {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId) {
                return 0;
            }

            $query->where('branch_id', $branchId);


            if ($branch_id && $branch_id == $branchId) {
                $query->where('branch_id', $branch_id);
            }
        } else {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId || !$businessId) {
                return 0;
            }

            $query->where('branch_id', $branchId)
                ->where('business_id', $businessId);
        }

        return $query->count();
    }

    /**
     * Filter notifications based on criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filter(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();
        $query = Notification::query();


        if (in_array('super-admin', $userRoles)) {
            $query->with(['user', 'business', 'branch']);
        } elseif (in_array('admin', $userRoles)) {

            $business = $user->business;
            if ($business) {
                $businessId = $business->id;
                $branchIds = $business->branches->pluck('id')->toArray();

                $query->where(function ($q) use ($businessId, $branchIds) {
                    $q->where('business_id', $businessId)
                        ->orWhereIn('branch_id', $branchIds);
                })
                    ->with(['user', 'branch']);


                if ($request->filled('branch_id')) {
                    $query->where('branch_id', $request->branch_id);
                }
            }
        } elseif (in_array('manager', $userRoles)) {

            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if ($branchId) {
                $query->where('branch_id', $branchId)
                    ->with(['user']);


                if ($request->filled('branch_id') && $request->branch_id == $branchId) {
                    $query->where('branch_id', $request->branch_id);
                }
            }
        } else {

            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if ($branchId && $businessId) {
                $query->where('branch_id', $branchId)
                    ->where('business_id', $businessId);
            } else {

                return response()->json([
                    'data' => [],
                    'current_page' => 1,
                    'last_page' => 1,
                    'per_page' => $request->input('per_page', 10),
                    'total' => 0
                ]);
            }
        }


        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        if ($request->filled('read_status')) {
            if ($request->input('read_status') === 'read') {
                $query->where('is_read', true);
            } elseif ($request->input('read_status') === 'unread') {
                $query->where('is_read', false);
            }
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }


        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);


        $perPage = $request->input('per_page', 10);
        $notifications = $query->paginate($perPage)->withQueryString();

        return response()->json($notifications);
    }

    /**
     * Get the most recent notifications since a specific ID
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecentNotifications(Request $request)
    {
        $user = Auth::user();
        $limit = $request->input('limit', 5);
        $sinceId = $request->input('since_id', 0);
        $query = Notification::query();


        $userRoles = $user->roles->pluck('name')->toArray();


        if (in_array('super-admin', $userRoles)) {
            $query->with(['user', 'business', 'branch']);
        } elseif (in_array('admin', $userRoles)) {

            $business = $user->business;
            if ($business) {
                $businessId = $business->id;
                $branchIds = $business->branches->pluck('id')->toArray();

                $query->where(function ($q) use ($businessId, $branchIds) {
                    $q->where('business_id', $businessId)
                        ->orWhereIn('branch_id', $branchIds);
                })
                    ->with(['user', 'branch']);


                if ($request->filled('branch_id')) {
                    $query->where('branch_id', $request->branch_id);
                }
            }
        } elseif (in_array('manager', $userRoles)) {

            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if ($branchId) {
                $query->where('branch_id', $branchId)
                    ->with(['user']);


                if ($request->filled('branch_id') && $request->branch_id == $branchId) {
                    $query->where('branch_id', $request->branch_id);
                }
            }
        } else {

            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if ($branchId && $businessId) {
                $query->where('branch_id', $branchId)
                    ->where('business_id', $businessId);
            } else {

                return response()->json([
                    'success' => true,
                    'data' => [],
                    'unreadCount' => 0,
                    'newest_id' => $sinceId
                ]);
            }
        }


        if ($sinceId > 0) {
            $query->where('id', '>', $sinceId);
        }

        $notifications = $query->orderBy('id', 'desc')
            ->limit($limit)
            ->get();

        $newestId = $notifications->isNotEmpty() ? $notifications->first()->id : $sinceId;


        $unreadCount = $this->getUnreadCountForUser($user, $request->branch_id);

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'unreadCount' => $unreadCount,
            'newest_id' => $newestId
        ]);
    }

    /**
     * Mark a notification as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, $notification)
    {
        $user = Auth::user();


        $notificationModel = Notification::findOrFail($notification);


        if (!$this->canAccessNotification($user, $notificationModel)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $notificationModel->is_read = true;
        $notificationModel->read_at = now();
        $notificationModel->save();

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();
        $query = Notification::where('is_read', false);


        if (in_array('super-admin', $userRoles)) {

        } elseif (in_array('admin', $userRoles)) {
            $business = $user->business;
            if (!$business) {
                return response()->json(['success' => false, 'message' => 'Business not found'], 404);
            }

            $businessId = $business->id;
            $branchIds = $business->branches->pluck('id')->toArray();

            $query->where(function ($q) use ($businessId, $branchIds) {
                $q->where('business_id', $businessId)
                    ->orWhereIn('branch_id', $branchIds);
            });


            if ($request->filled('branch_id')) {
                $query->where('branch_id', $request->branch_id);
            }
        } elseif (in_array('manager', $userRoles)) {
            $branchId = $user->branch_id;

            if (!$branchId) {
                return response()->json(['success' => false, 'message' => 'Branch not found'], 404);
            }

            $query->where('branch_id', $branchId);


            if ($request->filled('branch_id') && $request->branch_id == $branchId) {
                $query->where('branch_id', $request->branch_id);
            }
        } else {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId || !$businessId) {
                return response()->json(['success' => false, 'message' => 'Branch or business not found'], 404);
            }

            $query->where('branch_id', $branchId)
                ->where('business_id', $businessId);
        }

        $query->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Delete a notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $notification)
    {
        $user = Auth::user();


        $notificationModel = Notification::findOrFail($notification);


        if (!$this->canAccessNotification($user, $notificationModel)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $notificationModel->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Helper method to check if a user can access a notification
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Notification  $notification
     * @return bool
     */
    private function canAccessNotification(User $user, Notification $notification): bool
    {
        $userRoles = $user->roles->pluck('name')->toArray();


        if (in_array('super-admin', $userRoles)) {
            return true;
        } elseif (in_array('admin', $userRoles)) {
            $business = $user->business;
            if (!$business) {
                return false;
            }

            $businessId = $business->id;
            $branchIds = $business->branches->pluck('id')->toArray();


            if ($notification->business_id === $businessId) {
                return true;
            }


            if ($notification->branch_id && in_array($notification->branch_id, $branchIds)) {
                return true;
            }

            return false;
        } elseif (in_array('manager', $userRoles)) {
            $branchId = $user->branch_id;

            if (!$branchId) {
                return false;
            }

            return $notification->branch_id === $branchId;
        } else {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId || !$businessId) {
                return false;
            }

            return $notification->branch_id === $branchId &&
                $notification->business_id === $businessId;
        }
    }

    /**
     * Get new booking notifications based on user role
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNewBookingNoti(Request $request)
    {
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        
        $sevenDaysAgo = now()->subDays(7);

        $query = Notification::query()
            ->where('type', 'booking')
            ->where('is_read', false)
            ->where('title', 'Đặt sân mới')
            ->where('created_at', '>=', $sevenDaysAgo);

        
        if (in_array('super-admin', $userRoles)) {
            $query->with(['user', 'business', 'branch']);
        }
        
        elseif (in_array('admin', $userRoles)) {
            $business = $user->business;
            if (!$business) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'Business not found'
                ]);
            }

            $businessId = $business->id;
            $branchIds = $business->branches->pluck('id')->toArray();

            $query->where(function ($q) use ($businessId, $branchIds) {
                $q->where('business_id', $businessId)
                    ->orWhereIn('branch_id', $branchIds);
            })
                ->with(['user', 'branch']);
        }
        
        elseif (in_array('manager', $userRoles)) {
            $branchId = $user->branch_id;
            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'Branch not found'
                ]);
            }

            $query->where('branch_id', $branchId)
                ->with(['user']);
        }
        
        else {
            $branchId = $user->branch_id;
            $businessId = $user->business_id;

            if (!$branchId || !$businessId) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'Branch or business not found'
                ]);
            }

            $query->where('branch_id', $branchId)
                ->where('business_id', $businessId);
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $unreadCount = $this->getUnreadCountForUser($user);

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'unreadCount' => $unreadCount
        ]);
    }
}
