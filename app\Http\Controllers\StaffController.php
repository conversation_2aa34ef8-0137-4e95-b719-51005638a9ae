<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Business;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class StaffController extends Controller
{
    /**
     * Display staff assignment page for superadmin
     */
    public function assignPage(Request $request, $business_id)
    {
        $business = Business::findOrFail($business_id);

        return Inertia::render('SuperAdmin/Business/AssignStaff', [
            'business' => $business,
        ]);
    }

    /**
     * Get business staff for assignment
     */
    public function getListStaff(Request $request)
    {
        $staff = User::whereHas('roles', function ($query) {
            $query->where('name', '=', 'admin');
        })
            ->with([
                'roles',
                'business' => function ($query) {
                    $query->select('id', 'name');
                }
            ])
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->roles->first() ? $user->roles->first()->name : null,
                    'role_name' => $user->roles->first() ? $user->roles->first()->name : null,
                    'business' => $user->business,
                ];
            });

        return response()->json([
            'success' => true,
            'staff' => $staff
        ]);
    }

    /**
     * Assign staff to a branch
     */
    public function assign(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $business = Business::findOrFail($validated['business_id']);
        if (!$business) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy doanh nghiệp'
            ]);
        }

        User::whereIn('id', $validated['user_ids'])
            ->update(['business_id' => $business->id]);

        return response()->json([
            'success' => true,
            'message' => 'Người dùng đã được gán thành công'
        ]);
    }

    public function unassign(Request $request, $user_id)
    {
        $user = User::findOrFail($user_id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy người dùng'
            ]);
        }

        $user->business_id = null;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Người dùng đã được xóa thành công'
        ]);
    }
}