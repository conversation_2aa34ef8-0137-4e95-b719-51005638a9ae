<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Mail\BookingConfirmation;
use App\Mail\BookingRejection;
use App\Models\Branch;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Services\BookingEventService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Carbon\Carbon;

class BookingOnlineController extends Controller
{
    /**
     * Display a listing of online bookings for the superadmin.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $userBranchIds = [];
        $branch = null;
        $branchId = null;
        $businessId = null;

        if ($user->hasRole(['super-admin'])) {
            // Super admins can view all online bookings across all branches
            $query = \App\Models\Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
                ->where('booking_type', 'online');

            // Only apply branch filter if specifically requested
            if ($request->has('branch_id') && !empty($request->branch_id)) {
                $branchId = $request->input('branch_id');
                $query->where('branch_id', $branchId);
                $branch = Branch::with('business')->find($branchId);
                if ($branch) {
                    $businessId = $branch->business_id;
                }
            }
        } elseif ($user->hasRole(['admin'])) {
            // Admin role can view all branches in their business
            $businessId = $user->business_id;
            $userBranchIds = Branch::where('business_id', $businessId)->pluck('id')->toArray();
            $branchId = $request->input('branch_id', null);

            if (!$branchId || !in_array($branchId, $userBranchIds)) {
                $branch = Branch::where('business_id', $businessId)->first();
                $branchId = $branch ? $branch->id : null;
            } else {
                $branch = Branch::with('business')->find($branchId);
            }

            if (!$branchId) {
                return $this->unauthorizedResponse();
            }

            // Query bookings for this business
            $query = \App\Models\Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
                ->where('booking_type', 'online')
                ->whereIn('branch_id', $userBranchIds);

            if ($branchId) {
                $query->where('branch_id', $branchId);
            }
        } else {
            // Branch manager and staff can only view their assigned branch
            $branchId = $user->branch_id;
            if ($branchId) {
                $userBranchIds = [$branchId];
                $branch = Branch::with('business')->find($branchId);
                if ($branch) {
                    $businessId = $branch->business_id;
                }
            }

            if (!$branchId) {
                return $this->unauthorizedResponse();
            }

            // Query bookings for this branch
            $query = \App\Models\Booking::with(['courtBookings.court', 'branch', 'user', 'customer'])
                ->where('booking_type', 'online')
                ->where('branch_id', $branchId);
        }

        // Apply search filters
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                    ->orWhere('customer_phone', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->has('status') && !empty($request->status) && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->has('time') && !empty($request->time) && $request->time !== 'all') {
            // Join to court_bookings to filter by booking_date
            $query->whereHas('courtBookings', function ($q) use ($request) {
                switch ($request->time) {
                    case 'today':
                        $q->whereDate('booking_date', Carbon::today());
                        break;
                    case 'tomorrow':
                        $q->whereDate('booking_date', Carbon::tomorrow());
                        break;
                    case 'thisweek':
                        $q->whereBetween('booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                        break;
                    case 'nextweek':
                        $q->whereBetween('booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()]);
                        break;
                }
            });
        }

        // Court filter
        if ($request->has('court_id') && !empty($request->court_id) && $request->court_id !== 'all') {
            $courtId = $request->court_id;
            $query->whereHas('courtBookings', function ($q) use ($courtId) {
                $q->where('court_id', $courtId);
            });
        }

        // Make sure we're selecting all the needed fields
        $query->select('bookings.*');
        $query->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'));

        $bookings = $query->paginate(10);

        $referenceNumbers = collect($bookings->items())->pluck('reference_number')->unique()->toArray();

        $bankSettings = null;
        if ($businessId) {
            $bankSettings = \App\Models\BusinessSetting::getBankSettingsFormatted($businessId);
        }

        $payments = \App\Models\Payment::whereIn('booking_reference', $referenceNumbers)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $paymentsByReference = $payments->groupBy('booking_reference');

        $formattedBookings = collect($bookings->items())->map(function ($booking) use ($paymentsByReference) {
            $courtBookings = $booking->courtBookings;
            $totalPrice = $booking->total_price;
            $referenceNumber = $booking->reference_number;

            $bookingPayments = isset($paymentsByReference[$referenceNumber])
                ? $paymentsByReference[$referenceNumber]
                : collect();

            $paidAmount = $bookingPayments->sum('amount');
            $paymentStatus = 'pending';

            if ($paidAmount >= $totalPrice) {
                $paymentStatus = 'completed';
            } elseif ($paidAmount > 0) {
                $paymentStatus = 'partial';
            }

            $hasBankTransfer = $bookingPayments->contains('payment_method_id', 2);
            $hasProof = $bookingPayments->contains('has_proof', true);
            $latestPayment = $bookingPayments->sortByDesc('created_at')->first();
            $paymentMethods = $bookingPayments->pluck('payment_method')->unique()->implode(', ');

            // Get first court booking for display purposes
            $firstCourtBooking = $courtBookings->first();
            $bookingDate = $firstCourtBooking ? $firstCourtBooking->booking_date->format('Y-m-d') : null;
            $startTime = $firstCourtBooking ? $firstCourtBooking->start_time->format('H:i') : null;
            $endTime = $firstCourtBooking ? $firstCourtBooking->end_time->format('H:i') : null;

            return [
                'id' => $booking->id,
                'reference_number' => $referenceNumber,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'customer_email' => $booking->customer_email,
                'status' => $booking->status,
                'created_at' => $booking->created_at->format('Y-m-d H:i:s'),
                'booking_date' => $bookingDate,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'total_price' => $totalPrice,
                'formatted_total_price' => number_format($totalPrice, 0, ',', '.') . 'đ',
                'court_bookings' => $courtBookings->map(function ($courtBooking) {
                    return [
                        'id' => $courtBooking->id,
                        'court' => $courtBooking->court,
                        'booking_date' => $courtBooking->booking_date->format('Y-m-d'),
                        'start_time' => $courtBooking->start_time->format('H:i'),
                        'end_time' => $courtBooking->end_time->format('H:i'),
                        'price' => $courtBooking->total_price,
                        'formatted_price' => number_format($courtBooking->total_price, 0, ',', '.') . 'đ',
                    ];
                })->values(),

                'payments' => $bookingPayments->values(),
                'payment_summary' => [
                    'total_paid' => $paidAmount,
                    'payment_count' => $bookingPayments->count(),
                    'has_bank_transfer' => $hasBankTransfer,
                    'has_proof' => $hasProof,
                    'payment_methods' => $paymentMethods,
                    'latest_payment' => $latestPayment,
                    'payment_status' => $paymentStatus
                ],
                'branch' => [
                    'id' => $booking->branch_id,
                    'name' => $booking->branch ? $booking->branch->name : null,
                ]
            ];
        });

        $formattedPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $formattedBookings,
            $bookings->total(),
            $bookings->perPage(),
            $bookings->currentPage(),
            [
                'path' => $bookings->path(),
                'query' => $request->query(),
            ]
        );

        // Get stats based on user's branch access
        $statsQuery = \App\Models\Booking::where('booking_type', 'online');

        // If a specific branch is being viewed, filter stats by that branch
        if ($user->hasRole(['super-admin'])) {
            if ($branchId) {
                $statsQuery->where('branch_id', $branchId);
            }
        } elseif ($user->hasRole(['admin'])) {
            $statsQuery->whereIn('branch_id', $userBranchIds);
            if ($branchId) {
                $statsQuery->where('branch_id', $branchId);
            }
        } else {
            $statsQuery->where('branch_id', $branchId);
        }

        $allBranches = [];
        if ($user->hasRole(['super-admin'])) {
            $allBranches = Branch::orderBy('name')->get(['id', 'name']);
        } elseif ($user->hasRole(['admin']) && count($userBranchIds) > 1) {
            $allBranches = Branch::whereIn('id', $userBranchIds)->orderBy('name')->get(['id', 'name']);
        }

        $courts = [];
        if ($branchId) {
            $courts = Court::where('branch_id', $branchId)->get(['id', 'name']);
        }

        return Inertia::render('SuperAdmin/BookingOnline/index', [
            'bookings' => $formattedPaginator,
            'branch' => $branch,
            'bank_settings' => $bankSettings,
            'courts' => $courts,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? 'pending',
                'court_id' => $request->court_id ?? '',
                'time' => $request->time ?? 'all',
                'sort' => $request->input('sort', 'created_at'),
                'direction' => $request->input('direction', 'desc'),
            ],
            'stats' => [
                'pending' => $statsQuery->where('status', 'pending')->count(),
                'approved' => $statsQuery->where('status', 'confirmed')
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'rejected' => $statsQuery->where('status', 'cancelled')
                    ->whereDate('created_at', Carbon::today())
                    ->count(),
                'total' => $statsQuery->whereDate('created_at', Carbon::today())
                    ->count(),
            ],
            'statuses' => [
                'pending' => 'Chờ xác nhận',
                'confirmed' => 'Đã xác nhận',
                'cancelled' => 'Đã hủy',
                'completed' => 'Đã hoàn thành',
            ],
            'userBranches' => $allBranches,
        ]);
    }

    /**
     * Add payment information to a booking
     *
     * @param array $booking
     * @return array
     */
    private function addPaymentInfo($booking)
    {
        $referenceNumber = $booking['reference_number'];

        $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $booking['payments'] = $payments;

        $totalPrice = $booking['total_price'];
        $paidAmount = $payments->sum('amount');
        $paymentStatus = 'pending';

        if ($paidAmount >= $totalPrice) {
            $paymentStatus = 'completed';
        } elseif ($paidAmount > 0) {
            $paymentStatus = 'partial';
        }

        $hasBankTransfer = $payments->contains('payment_method_id', 2);
        $hasProof = $payments->contains('has_proof', true);
        $latestPayment = $payments->sortByDesc('created_at')->first();
        $paymentMethods = $payments->pluck('payment_method')->unique()->implode(', ');

        $booking['payment_summary'] = [
            'total_paid' => $paidAmount,
            'payment_count' => $payments->count(),
            'has_bank_transfer' => $hasBankTransfer,
            'has_proof' => $hasProof,
            'payment_methods' => $paymentMethods,
            'latest_payment' => $latestPayment,
            'payment_status' => $paymentStatus
        ];

        return $booking;
    }

    /**
     * Approve a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(Request $request, $id)
    {
        $user = $request->user();

        // Check user permissions
        if (!$user->hasRole(['super-admin', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $booking = CourtBooking::with(['booking'])->findOrFail($id);

        $pendingPayments = \App\Models\Payment::where('booking_reference', $booking->reference_number)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->with('paymentMethod')
            ->get();

        if ($pendingPayments->count() > 0 && !$request->force_approve) {
            foreach ($pendingPayments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            return response()->json([
                'warning' => true,
                'message' => 'Đơn đặt sân này có thanh toán chưa được xác nhận.',
                'requires_confirmation' => true,
                'pending_payments' => $pendingPayments,
                'pending_payment_count' => $pendingPayments->count()
            ]);
        }

        DB::beginTransaction();

        try {
            $referenceNumber = $booking->reference_number;
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();

            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes,
                'approved_at' => now()->toDateTimeString(),
            ];

            foreach ($relatedBookings as $relatedBooking) {
                $relatedBooking->status = 'confirmed';
                $relatedBooking->notes = $request->notes;
                $relatedBooking->metadata = array_merge($relatedBooking->metadata ?? [], $approvalMetadata);
                $relatedBooking->save();

                // Update parent Booking if it exists
                if ($relatedBooking->booking_id) {
                    $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'confirmed';
                        $parentBooking->save();
                    }
                }

                BookingEventService::bookingApproved($relatedBooking, Auth::id());
            }

            // Update payment status if there are completed payments
            $completedPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->where('status', 'completed')
                ->get();

            if ($completedPayments->count() > 0) {
                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $relatedBookings->sum('total_price');

                $paymentStatus = ($totalPaid >= $totalPrice) ? 'paid' :
                    ($totalPaid > 0 ? 'partial' : 'pending');

                foreach ($relatedBookings as $relatedBooking) {
                    $relatedBooking->payment_status = $paymentStatus;
                    $relatedBooking->save();

                    // Update parent Booking payment status if it exists
                    if ($relatedBooking->booking_id) {
                        $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                        if ($parentBooking) {
                            $parentBooking->payment_status = $paymentStatus;
                            $parentBooking->save();
                        }
                    }
                }
            }

            DB::commit();

            // Send email confirmation
            try {
                if ($booking->customer_email) {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($booking->customer_email)->send(
                        new BookingConfirmation(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving booking: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject(Request $request, $id)
    {
        $user = $request->user();

        // Check user permissions
        if (!$user->hasRole(['super-admin', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $booking = CourtBooking::with(['booking'])->findOrFail($id);

        if (!$request->has('reason') || empty($request->reason)) {
            return response()->json([
                'error' => 'Vui lòng nhập lý do từ chối.'
            ], 422);
        }

        DB::beginTransaction();

        try {
            $referenceNumber = $booking->reference_number;
            $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();

            $rejectionMetadata = [
                'rejected_by' => $user->id,
                'rejected_by_name' => $user->name,
                'rejected_at' => now()->toDateTimeString(),
                'rejection_reason' => $request->reason
            ];

            $cancelledAt = now();

            foreach ($relatedBookings as $relatedBooking) {
                $relatedBooking->status = 'cancelled';
                $relatedBooking->cancellation_reason = $request->reason;
                $relatedBooking->cancelled_at = $cancelledAt;
                $relatedBooking->metadata = array_merge($relatedBooking->metadata ?? [], $rejectionMetadata);
                $relatedBooking->save();

                // Update parent Booking if it exists
                if ($relatedBooking->booking_id) {
                    $parentBooking = \App\Models\Booking::find($relatedBooking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'cancelled';
                        $parentBooking->payment_status = 'cancelled';
                        $parentBooking->cancelled_at = $cancelledAt;
                        $parentBooking->cancellation_reason = $request->reason;
                        $parentBooking->save();
                    }
                }

                // Remove Google Calendar event if exists
                BookingEventService::bookingCancelled($relatedBooking, $request->reason, null, Auth::id());
            }

            // Cancel any pending payments
            $pendingPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                ->whereIn('status', ['pending', 'pending_approval'])
                ->get();

            foreach ($pendingPayments as $payment) {
                $payment->status = 'cancelled';
                $payment->payment_details = array_merge(
                    is_array($payment->payment_details) ? $payment->payment_details : [],
                    [
                        'cancelled_by' => $user->id,
                        'cancelled_by_name' => $user->name,
                        'cancelled_at' => now()->toDateTimeString(),
                        'reason' => $request->reason
                    ]
                );
                $payment->save();
            }

            DB::commit();

            // Send email notification
            try {
                if ($booking->customer_email) {
                    $relatedBookings = CourtBooking::where('reference_number', $booking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($booking->customer_email)->send(
                        new BookingRejection(
                            $booking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $booking,
                            $booking->cancellation_reason
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking rejection email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được từ chối'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error rejecting booking: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi từ chối đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve a payment
     *
     * @param Request $request
     * @param int $paymentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvePayment(Request $request, $paymentId)
    {
        $user = $request->user();

        // Check user permissions
        if (!$user->hasRole(['super-admin', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $payment = \App\Models\Payment::findOrFail($paymentId);

        DB::beginTransaction();

        try {
            $payment->status = 'completed';
            $payment->approved_at = now();
            $payment->approved_by = Auth::id();

            // Add approval details to payment_details
            $paymentDetails = is_array($payment->payment_details)
                ? $payment->payment_details
                : [];

            $paymentDetails['approved_by'] = $user->id;
            $paymentDetails['approved_by_name'] = $user->name;
            $paymentDetails['approved_at'] = now()->toDateTimeString();

            $payment->payment_details = $paymentDetails;
            $payment->save();

            // Update payment status for related bookings
            $referenceNumber = $payment->booking_reference;
            $bookings = CourtBooking::where('reference_number', $referenceNumber)->get();

            if ($bookings->isNotEmpty()) {
                $totalBookingAmount = $bookings->sum('total_price');

                $approvedPayments = \App\Models\Payment::where('booking_reference', $referenceNumber)
                    ->where('status', 'completed')
                    ->get();

                $totalPaidAmount = $approvedPayments->sum('amount');

                $paymentStatus = ($totalPaidAmount >= $totalBookingAmount) ? 'paid' : 'partial';

                foreach ($bookings as $booking) {
                    $booking->payment_status = $paymentStatus;
                    $booking->save();

                    // Update parent Booking payment status if it exists
                    if ($booking->booking_id) {
                        $parentBooking = \App\Models\Booking::find($booking->booking_id);
                        if ($parentBooking) {
                            $parentBooking->payment_status = $paymentStatus;
                            $parentBooking->save();
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Thanh toán đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving payment: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận thanh toán: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment information for a booking
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentInfo(Request $request, $id)
    {
        $booking = CourtBooking::findOrFail($id);
        $referenceNumber = $booking->reference_number;

        $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->with('paymentMethod')
            ->get();

        foreach ($payments as $payment) {
            if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                $details = is_array($payment->payment_details)
                    ? $payment->payment_details
                    : json_decode($payment->payment_details, true);

                if (isset($details['proof_file'])) {
                    $payment->proof_url = asset('storage/' . $details['proof_file']);
                    $payment->original_filename = $details['original_filename'] ?? null;
                    $payment->uploaded_at = $details['uploaded_at'] ?? null;
                    $payment->has_proof = true;
                }
            }
        }

        $relatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();
        $totalPrice = $relatedBookings->sum('total_price');
        $paidAmount = $payments->sum('amount');

        return response()->json([
            'success' => true,
            'payments' => $payments,
            'total_price' => $totalPrice,
            'paid_amount' => $paidAmount,
            'remaining_amount' => $totalPrice - $paidAmount,
        ]);
    }

    /**
     * Approve a booking by reference number
     *
     * @param Request $request
     * @param string $reference_number
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveByReference(Request $request, $reference_number)
    {
        $user = $request->user();

        // Check user permissions
        if (!$user->hasRole(['super-admin', 'admin'])) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $bookings = CourtBooking::where('reference_number', $reference_number)->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'error' => 'Không tìm thấy đơn đặt sân với mã tham chiếu này.'
            ], 404);
        }

        $firstBooking = $bookings->first();

        $pendingPayments = \App\Models\Payment::where('booking_reference', $reference_number)
            ->whereIn('status', ['pending', 'pending_approval'])
            ->with('paymentMethod')
            ->get();

        if ($pendingPayments->count() > 0 && !$request->force_approve) {
            foreach ($pendingPayments as $payment) {
                if ($payment->payment_method_id == 2 && !empty($payment->payment_details)) {
                    $details = is_array($payment->payment_details)
                        ? $payment->payment_details
                        : json_decode($payment->payment_details, true);

                    if (isset($details['proof_file'])) {
                        $payment->proof_url = asset('storage/' . $details['proof_file']);
                        $payment->original_filename = $details['original_filename'] ?? null;
                        $payment->uploaded_at = $details['uploaded_at'] ?? null;
                        $payment->has_proof = true;
                    }
                }
            }

            return response()->json([
                'warning' => true,
                'message' => 'Đơn đặt sân này có thanh toán chưa được xác nhận.',
                'requires_confirmation' => true,
                'pending_payments' => $pendingPayments,
                'pending_payment_count' => $pendingPayments->count()
            ]);
        }

        DB::beginTransaction();

        try {
            $approvalMetadata = [
                'approved_by' => $user->id,
                'approved_by_name' => $user->name,
                'approval_notes' => $request->notes,
                'approved_at' => now()->toDateTimeString(),
            ];

            foreach ($bookings as $booking) {
                $booking->status = 'confirmed';
                $booking->notes = $request->notes;
                $booking->metadata = array_merge($booking->metadata ?? [], $approvalMetadata);
                $booking->save();

                // Update parent Booking if it exists
                if ($booking->booking_id) {
                    $parentBooking = \App\Models\Booking::find($booking->booking_id);
                    if ($parentBooking) {
                        $parentBooking->status = 'confirmed';
                        $parentBooking->save();
                    }
                }

                BookingEventService::bookingApproved($booking, Auth::id());
            }

            // Update payment status if there are completed payments
            $completedPayments = \App\Models\Payment::where('booking_reference', $reference_number)
                ->where('status', 'completed')
                ->get();

            if ($completedPayments->count() > 0) {
                $totalPaid = $completedPayments->sum('amount');
                $totalPrice = $bookings->sum('total_price');

                $paymentStatus = ($totalPaid >= $totalPrice) ? 'paid' :
                    ($totalPaid > 0 ? 'partial' : 'pending');

                foreach ($bookings as $booking) {
                    $booking->payment_status = $paymentStatus;
                    $booking->save();

                    // Update parent Booking payment status if it exists
                    if ($booking->booking_id) {
                        $parentBooking = \App\Models\Booking::find($booking->booking_id);
                        if ($parentBooking) {
                            $parentBooking->payment_status = $paymentStatus;
                            $parentBooking->save();
                        }
                    }
                }
            }

            DB::commit();

            // Send email confirmation
            try {
                if ($firstBooking->customer_email) {
                    $relatedBookings = CourtBooking::where('reference_number', $firstBooking->reference_number)->get();
                    $totalPrice = $relatedBookings->sum('total_price');
                    $bookingsArray = $relatedBookings->map(function ($b) {
                        return [
                            'id' => $b->id,
                            'court_name' => $b->court ? $b->court->name : '',
                            'booking_date' => $b->booking_date->format('Y-m-d'),
                            'start_time' => $b->start_time->format('H:i'),
                            'end_time' => $b->end_time->format('H:i'),
                            'total_price' => $b->total_price
                        ];
                    })->toArray();

                    Mail::to($firstBooking->customer_email)->send(
                        new BookingConfirmation(
                            $firstBooking->reference_number,
                            $totalPrice,
                            $bookingsArray,
                            $firstBooking
                        )
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Đơn đặt sân đã được xác nhận thành công'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error approving booking by reference: ' . $e->getMessage());

            return response()->json([
                'error' => 'Có lỗi xảy ra khi xác nhận đơn đặt sân: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Return unauthorized response
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    private function unauthorizedResponse()
    {
        return redirect()->route('redirect.to.unauthorized', [
            'message' => 'Bạn không có quyền truy cập trang này',
            'redirect_url' => route('dashboard')
        ]);
    }
}