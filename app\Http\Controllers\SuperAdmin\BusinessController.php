<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessSetting;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;

class BusinessController extends Controller
{
    /**
     * Display a listing of businesses.
     */
    public function index(Request $request)
    {
        $businesses = Business::query()
            ->when($request->input('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('contact_email', 'like', "%{$search}%")
                    ->orWhere('contact_phone', 'like', "%{$search}%");
            })
            ->when($request->input('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'))
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('SuperAdmin/Business/Index', [
            'businesses' => $businesses,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new business.
     */
    public function create()
    {
        return Inertia::render('SuperAdmin/Business/Create');
    }

    /**
     * Store a newly created business in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'province_id' => 'nullable|numeric',
            'district_id' => 'nullable|numeric',
            'ward_id' => 'nullable|numeric',
            'province' => 'nullable|string',
            'district' => 'nullable|string',
            'ward' => 'nullable|string',
            'complete_address' => 'nullable|string',
            'tax_code' => 'nullable|string|max:50',
            'status' => 'required|in:active,inactive,pending',
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|unique:users,email',
            'admin_password' => 'required|min:8|confirmed',
            'settings' => 'nullable|array'
        ], __('validation.business'));
        try {
            DB::beginTransaction();
            $logoPath = null;
            $bannerPath = null;

            if ($request->hasFile('logo')) {
                $logo = $request->file('logo');
                $timestamp = now()->format('YmdHis');
                $logoFileName = $timestamp . '_logo_' . Str::slug($request->name) . '.' . $logo->getClientOriginalExtension();
                $logoPath = $logo->storeAs('business/logos', $logoFileName, 'public');
            }

            if ($request->hasFile('banner')) {
                $banner = $request->file('banner');
                $timestamp = now()->format('YmdHis');
                $bannerFileName = $timestamp . '_banner_' . Str::slug($request->name) . '.' . $banner->getClientOriginalExtension();
                $bannerPath = $banner->storeAs('business/banners', $bannerFileName, 'public');
            }

            $business = Business::create([
                'name' => $request->name,
                'description' => $request->description,
                'logo_url' => $logoPath ? '/storage/' . $logoPath : '/storage/logo.jpg',
                'banner_url' => $bannerPath ? '/storage/' . $bannerPath : '/storage/logo.jpg',
                'contact_email' => $request->contact_email,
                'contact_phone' => $request->contact_phone,
                'address' => $request->address,
                'province_id' => $request->province_id,
                'district_id' => $request->district_id,
                'ward_id' => $request->ward_id,
                'province' => $request->province,
                'district' => $request->district,
                'ward' => $request->ward,
                'complete_address' => $request->complete_address,
                'tax_code' => $request->tax_code,
                'status' => $request->status,
            ]);

            $admin = User::create([
                'name' => $request->admin_name,
                'business_id' => $business->id,
                'email' => $request->admin_email,
                'password' => Hash::make($request->admin_password),
                // 'active' => true,
                'is_verified' => true,
                'status' => 'active',
            ]);

            $adminRole = Role::where('name', 'admin')->first();
            if ($adminRole) {
                $admin->roles()->attach($adminRole->id, ['business_id' => $business->id]);
            }

            if ($request->settings && is_array($request->settings)) {
                foreach ($request->settings as $key => $value) {
                    
                    if ($key === 'momo_payment') {
                        BusinessSetting::setMomoSettings($business->id, $value);
                    } elseif ($key === 'vnpay_payment') {
                        BusinessSetting::setVnPaySettings($business->id, $value);
                    } elseif (strpos($key, '_payment') !== false) {
                        
                        $provider = str_replace('_payment', '', $key);
                        BusinessSetting::setPaymentSettings($business->id, $provider, $value);
                    } else {
                        
                        $business->setSetting($key, $value);
                    }
                }
            }

            NotificationService::create(
                null,
                $business->id,
                null,
                'system',
                'Doanh nghiệp mới',
                "Doanh nghiệp mới '{$business->name}' đã được tạo bởi SUPERADMIN",
                [
                    'business_id' => $business->id,
                    'business_name' => $business->name,
                    'business_email' => $business->contact_email,
                    'business_phone' => $business->contact_phone,
                    'admin_name' => $admin->name,
                    'admin_email' => $admin->email,
                    'created_by' => 'SUPERADMIN: ' . $request->user()->name . ' - ' . $request->user()->id
                ]
            );

            DB::commit();

            return redirect()->route('superadmin.business.index')
                ->with('message', 'Business created successfully with an admin user');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to create business: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified business.
     */
    public function show(Business $business)
    {
        $business->load('settings');


        $admins = User::where('business_id', $business->id)
            ->whereHas('roles', function ($query) {
                $query->where('name', 'admin');
            })
            ->with('roles:id,name')
            ->get();

        $branches = $business->branches()
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Business/Show', [
            'business' => $business,
            'admins' => $admins,
            'branches' => $branches,
        ]);
    }

    /**
     * Show the form for editing the specified business.
     */
    public function edit(Business $business)
    {
        $business->load('settings');


        $admins = User::where('business_id', $business->id)
            ->whereHas('roles', function ($query) {
                $query->where('name', 'admin');
            })
            ->with('roles:id,name')
            ->get();

        return Inertia::render('SuperAdmin/Business/Edit', [
            'business' => $business,
            'admins' => $admins,
            'settings' => $business->settings
        ]);
    }

    /**
     * Update the specified business in storage.
     */
    public function update(Request $request, Business $business)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'province_id' => 'nullable|numeric',
            'district_id' => 'nullable|numeric',
            'ward_id' => 'nullable|numeric',
            'province' => 'nullable|string',
            'district' => 'nullable|string',
            'ward' => 'nullable|string',
            'complete_address' => 'nullable|string',
            'tax_code' => 'nullable|string|max:50',
            'status' => 'required|in:active,inactive,pending',
            'settings' => 'nullable|array'
        ], __('validation.business'));

        try {
            DB::beginTransaction();

            
            $changedFields = [];
            $fields = [
                'name',
                'description',
                'contact_email',
                'contact_phone',
                'address',
                'province_id',
                'district_id',
                'ward_id',
                'province',
                'district',
                'ward',
                'complete_address',
                'tax_code',
                'status'
            ];

            foreach ($fields as $field) {
                if ($request->has($field) && $business->$field != $request->$field) {
                    $changedFields[] = $field;
                }
            }

            $data = [
                'name' => $request->name,
                'description' => $request->description,
                'contact_email' => $request->contact_email,
                'contact_phone' => $request->contact_phone,
                'address' => $request->address,
                'province_id' => $request->province_id,
                'district_id' => $request->district_id,
                'ward_id' => $request->ward_id,
                'province' => $request->province,
                'district' => $request->district,
                'ward' => $request->ward,
                'complete_address' => $request->complete_address,
                'tax_code' => $request->tax_code,
                'status' => $request->status,
            ];

            $business->update($data);

            if ($request->settings && is_array($request->settings)) {
                foreach ($request->settings as $key => $value) {
                    if ($key === 'momo_payment') {
                        BusinessSetting::setMomoSettings($business->id, $value);
                    } elseif ($key === 'vnpay_payment') {
                        BusinessSetting::setVnPaySettings($business->id, $value);
                    } elseif (strpos($key, '_payment') !== false) {
                        $provider = str_replace('_payment', '', $key);
                        BusinessSetting::setPaymentSettings($business->id, $provider, $value);
                    } else {
                        $business->setSetting($key, $value);
                    }
                }
            }

            if (!empty($changedFields)) {
                NotificationService::create(
                    null,
                    $business->id,
                    null,
                    'system',
                    'Cập nhật doanh nghiệp',
                    "Doanh nghiệp '{$business->name}' đã được cập nhật thông tin",
                    [
                        'business_id' => $business->id,
                        'business_name' => $business->name,
                        'changed_fields' => $changedFields,
                        'updated_by' => 'SUPERADMIN: ' . $request->user()->name . ' - ' . $request->user()->id
                    ]
                );
            }

            DB::commit();

            return redirect()->route('superadmin.business.index')
                ->with('message', 'Business updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to update business: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified business from storage.
     */
    public function destroy(Request $request, Business $business)
    {
        try {
            DB::beginTransaction();
            $businessId = $business->id;
            $businessName = $business->name;
            $business->settings()->delete();
            $business->delete();

            NotificationService::create(
                null,
                $business->id,
                null,
                'system',
                'Xóa doanh nghiệp',
                "Doanh nghiệp '{$businessName}' đã bị xóa",
                [
                    'business_id' => $businessId,
                    'business_name' => $businessName,
                    'deleted_by' => 'SUPERADMIN: ' . $request->user()->name . ' - ' . $request->user()->id,
                    'action' => 'delete'
                ]
            );

            DB::commit();
            return redirect()->route('superadmin.business.index')
                ->with('message', 'Business deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to delete business: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Add a new admin user to the business.
     */
    public function addAdmin(Request $request, Business $business)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8|confirmed',
        ], __('validation.business'));

        try {
            DB::beginTransaction();
            $admin = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'active' => true,
            ]);
            $adminRole = Role::where('name', 'admin')->first();
            if ($adminRole) {
                $admin->roles()->attach($adminRole->id, ['business_id' => $business->id]);
            }

            DB::commit();

            return redirect()->route('superadmin.business.show', $business->id)
                ->with('message', 'Admin user added successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to add admin user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update business settings.
     */
    public function updateSettings(Request $request, Business $business)
    {
        $request->validate([
            'settings' => 'required|array',
        ], __('validation.business'));

        try {
            DB::beginTransaction();

            foreach ($request->settings as $key => $value) {
                
                if ($key === 'momo_payment') {
                    BusinessSetting::setMomoSettings($business->id, $value);
                } elseif ($key === 'vnpay_payment') {
                    BusinessSetting::setVnPaySettings($business->id, $value);
                } elseif (strpos($key, '_payment') !== false) {
                    
                    $provider = str_replace('_payment', '', $key);
                    BusinessSetting::setPaymentSettings($business->id, $provider, $value);
                } else {
                    
                    $business->setSetting($key, $value);
                }
            }

            DB::commit();

            return redirect()->route('superadmin.business.show', $business->id)
                ->with('message', 'Business settings updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to update business settings: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update business images (logo and banner).
     */
    public function updateImages(Request $request, Business $business)
    {
        $request->validate([
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], __('validation.business'));

        try {
            DB::beginTransaction();

            $data = [];
            $imagesChanged = [];

            if ($request->hasFile('logo')) {
                $logo = $request->file('logo');
                $timestamp = now()->format('YmdHis');
                $logoFileName = $timestamp . '_logo_' . Str::slug($business->name) . '.' . $logo->getClientOriginalExtension();
                $logoPath = $logo->storeAs('business/logos', $logoFileName, 'public');
                $data['logo_url'] = $logoPath;
                $imagesChanged[] = 'logo';
            }

            if ($request->hasFile('banner')) {
                $banner = $request->file('banner');
                $timestamp = now()->format('YmdHis');
                $bannerFileName = $timestamp . '_banner_' . Str::slug($business->name) . '.' . $banner->getClientOriginalExtension();
                $bannerPath = $banner->storeAs('business/banners', $bannerFileName, 'public');
                $data['banner_url'] = $bannerPath;
                $imagesChanged[] = 'banner';
            }

            if (!empty($data)) {
                $business->update($data);

                NotificationService::create(
                    null,
                    $business->id,
                    null,
                    'system',
                    'Cập nhật hình ảnh doanh nghiệp',
                    "Hình ảnh của doanh nghiệp '{$business->name}' đã được cập nhật", 
                    [
                        'business_id' => $business->id,
                        'business_name' => $business->name,
                        'images_changed' => $imagesChanged,
                        'updated_by' => 'SUPERADMIN: ' . $request->user()->name . ' - ' . $request->user()->id
                    ]
                );
            }

            DB::commit();

            return redirect()->back()
                ->with('message', 'Business images updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Failed to update business images: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get business admins via API
     */
    public function getAdmins(Business $business)
    {
        try {
            $admins = User::where('business_id', $business->id)
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'admin');
                })
                ->with('roles:id,name')
                ->get();

            return response()->json([
                'success' => true,
                'admins' => $admins
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch admins: ' . $e->getMessage()
            ], 500);
        }
    }
}