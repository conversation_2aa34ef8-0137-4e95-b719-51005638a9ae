<?php

declare(strict_types=1);

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Payment;
use App\Models\PaymentMethod;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class PaymentHistoryController extends Controller
{
    /**
     * Display the payment history page
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {

        $perPage = $request->input('per_page', 10);
        $currentPage = $request->input('page', 1);
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');


        $query = Payment::query()
            ->select(
                'payments.*',
                'court_bookings.booking_date',
                'court_bookings.start_time as booking_start',
                'court_bookings.end_time as booking_end',
                'court_bookings.status as booking_status',
                'court_bookings.customer_name as booking_customer_name',
                'court_bookings.customer_phone as booking_customer_phone',
                'courts.name as court_name',
                'branches.name as branch_name'
            )
            ->leftJoin('court_bookings', 'payments.court_booking_id', '=', 'court_bookings.id')
            ->leftJoin('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->leftJoin('branches', 'court_bookings.branch_id', '=', 'branches.id')
            ->with(['user:id,name,email', 'customer:id,name,phone,email']);


        if ($request->has('from_date') && $request->has('to_date')) {
            $from = Carbon::parse($request->input('from_date'))->startOfDay();
            $to = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('payments.created_at', [$from, $to]);
        } else {

            $to = Carbon::now();
            $from = Carbon::now()->subDays(30);
        }


        if ($request->has('payment_method') && $request->input('payment_method') !== 'all') {
            $query->where('payments.payment_method', $request->input('payment_method'));
        }


        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('payments.status', $request->input('status'));
        }


        if ($request->has('search') && $request->input('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('payments.reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('payments.booking_reference', 'like', "%{$searchTerm}%")
                    ->orWhere('payments.transaction_id', 'like', "%{$searchTerm}%")
                    ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('customer', function ($customerQuery) use ($searchTerm) {
                        $customerQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%")
                            ->orWhere('phone', 'like', "%{$searchTerm}%");
                    });
            });
        }


        $bookingReferences = $query->pluck('booking_reference')->unique();


        $groupedPayments = collect();
        foreach ($bookingReferences as $bookingRef) {

            $payments = Payment::where('booking_reference', $bookingRef)
                ->with(['user:id,name,email', 'customer:id,name,phone,email'])
                ->get();

            if ($payments->isEmpty())
                continue;

            $firstPayment = $payments->first();
            $totalAmount = $payments->sum('amount');


            $allBookings = CourtBooking::where('reference_number', $bookingRef)
                ->with(['court:id,name', 'branch:id,name'])
                ->get();

            $booking = $allBookings->first();


            $groupedPayment = [
                'booking_reference' => $bookingRef,
                'payment_count' => $payments->count(),
                'transaction_date' => $firstPayment->transaction_date ? Carbon::parse($firstPayment->transaction_date)->format('d/m/Y H:i') : null,
                'created_at' => $firstPayment->created_at->format('d/m/Y H:i'),
                'updated_at' => $firstPayment->updated_at->format('d/m/Y H:i'),
                'user_name' => $firstPayment->user ? $firstPayment->user->name : 'N/A',
                'user_email' => $firstPayment->user ? $firstPayment->user->email : 'N/A',
                'customer_name' => $firstPayment->customer ? $firstPayment->customer->name : ($booking && $booking->customer_name ? $booking->customer_name : 'N/A'),
                'customer_contact' => $firstPayment->customer ? ($firstPayment->customer->phone ?? $firstPayment->customer->email) : ($booking && $booking->customer_phone ? $booking->customer_phone : 'N/A'),
                'court_name' => $booking && $booking->court ? $booking->court->name : 'N/A',
                'branch_name' => $booking && $booking->branch ? $booking->branch->name : 'N/A',
                'booking_date' => $booking ? Carbon::parse($booking->booking_date)->format('d/m/Y') : 'N/A',
                'booking_time' => $booking ? Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i') : 'N/A',
                'payment_methods' => $payments->pluck('payment_method')->unique()->map(function ($method) {
                    return $this->getPaymentMethodName($method);
                })->implode(', '),
                'amount' => $totalAmount,
                'amount_formatted' => number_format((float) $totalAmount, 0, ',', '.') . ' ₫',
                'status' => $payments->last()->status,
                'status_display' => $this->getPaymentStatusDisplay($payments->last()->status),
                'status_class' => $this->getPaymentStatusClass($payments->last()->status),
                'booking_id' => $firstPayment->court_booking_id,
                'booking_type' => $booking ? $booking->booking_type : null,
                'booking_status' => $booking ? $booking->status : null,
                'booking_status_display' => $booking ? $this->getBookingStatusDisplay($booking->status) : null,
                'related_bookings' => $allBookings->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'reference_number' => $booking->reference_number,
                        'court_name' => $booking->court ? $booking->court->name : 'N/A',
                        'branch_name' => $booking->branch ? $booking->branch->name : 'N/A',
                        'booking_date' => Carbon::parse($booking->booking_date)->format('d/m/Y'),
                        'booking_time' => Carbon::parse($booking->start_time)->format('H:i') . ' - ' . Carbon::parse($booking->end_time)->format('H:i'),
                        'status' => $booking->status,
                        'status_display' => $this->getBookingStatusDisplay($booking->status),
                        'total_price' => $booking->total_price,
                        'booking_type' => $booking->booking_type,
                    ];
                })->values()->toArray(),
                'payments' => $payments->map(function ($payment) {
                    return [
                        'id' => $payment->id,
                        'reference_number' => $payment->reference_number,
                        'transaction_id' => $payment->transaction_id,
                        'payment_type' => $payment->payment_type,
                        'payment_method' => $payment->payment_method,
                        'amount' => $payment->amount,
                        'status' => $payment->status,
                        'status_display' => $this->getPaymentStatusDisplay($payment->status),
                        'created_at' => $payment->created_at->format('d/m/Y H:i'),
                        'notes' => $payment->notes,
                    ];
                })->values()->toArray(),
            ];

            $groupedPayments->push($groupedPayment);
        }


        if ($sortDirection === 'desc') {
            $groupedPayments = $groupedPayments->sortByDesc($sortField);
        } else {
            $groupedPayments = $groupedPayments->sortBy($sortField);
        }


        $offset = ($currentPage - 1) * $perPage;
        $paginatedPayments = $groupedPayments->slice($offset, $perPage)->values();
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedPayments,
            $groupedPayments->count(),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );


        $paymentMethods = Cache::remember('payment_methods', now()->addMinutes(30), function () {
            return PaymentMethod::where('is_enabled', true)
                ->select('payment_code', 'payment_name')
                ->get()
                ->map(function ($method) {
                    return [
                        'value' => $method->payment_code,
                        'label' => $method->payment_name
                    ];
                })
                ->toArray();
        });


        if (empty($paymentMethods)) {
            $paymentMethods = Payment::select('payment_method')
                ->distinct()
                ->pluck('payment_method')
                ->filter()
                ->map(function ($method) {
                    return [
                        'value' => $method,
                        'label' => $this->getPaymentMethodName($method)
                    ];
                })
                ->toArray();
        }


        array_unshift($paymentMethods, ['value' => 'all', 'label' => 'Tất cả phương thức']);


        $statuses = [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Đang xử lý'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'failed', 'label' => 'Thất bại'],
            ['value' => 'refunded', 'label' => 'Hoàn tiền'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
        ];


        $cacheKey = "payment_summary_{$from->format('Y-m-d')}_{$to->format('Y-m-d')}";
        $summaryStats = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($from, $to) {
            return $this->getSummaryStats($from, $to);
        });


        $fromDateFilter = $request->has('from_date') ? $request->input('from_date') : null;
        $toDateFilter = $request->has('to_date') ? $request->input('to_date') : null;

        return Inertia::render('SuperAdmin/PaymentHistory', [
            'payments' => $paginator,
            'filters' => [
                'search' => $request->input('search', ''),
                'from_date' => $fromDateFilter,
                'to_date' => $toDateFilter,
                'payment_method' => $request->input('payment_method', 'all'),
                'status' => $request->input('status', 'all'),
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
                'per_page' => $perPage,
            ],
            'payment_methods' => $paymentMethods,
            'statuses' => $statuses,
            'summary' => $summaryStats,
        ]);
    }

    /**
     * Get payment method display name
     *
     * @param string|null $methodCode
     * @return string
     */
    private function getPaymentMethodName($methodCode)
    {

        $paymentMethod = PaymentMethod::where('payment_code', $methodCode)->first();
        if ($paymentMethod) {
            return $paymentMethod->payment_name;
        }


        $methods = [
            'cash' => 'Tiền mặt',
            'bank_transfer' => 'Chuyển khoản',
            'credit_card' => 'Thẻ tín dụng',
            'momo' => 'Ví MoMo',
            'vnpay' => 'VNPAY',
            'zalopay' => 'ZaloPay',
        ];

        return $methods[$methodCode] ?? 'Khác';
    }

    /**
     * Get payment status display name
     *
     * @param string $status
     * @return string
     */
    private function getPaymentStatusDisplay($status)
    {
        $statuses = [
            'pending' => 'Đang xử lý',
            'completed' => 'Hoàn thành',
            'failed' => 'Thất bại',
            'refunded' => 'Hoàn tiền',
            'cancelled' => 'Đã hủy',
        ];

        return $statuses[$status] ?? 'Không xác định';
    }

    /**
     * Get payment status CSS class
     *
     * @param string $status
     * @return string
     */
    private function getPaymentStatusClass($status)
    {
        $classes = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'completed' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            'refunded' => 'bg-purple-100 text-purple-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
        ];

        return $classes[$status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Get summary statistics
     *
     * @param Carbon $from
     * @param Carbon $to
     * @return array
     */
    private function getSummaryStats(Carbon $from, Carbon $to)
    {

        $query = Payment::query();


        if ($this->isDateFilterActive()) {
            $query->whereBetween('created_at', [$from, $to]);
        }


        $statusCounts = (clone $query)
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();


        $completedStats = (clone $query)
            ->where('status', 'completed')
            ->select(
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(*) as total_count')
            )
            ->first();

        $totalAmount = $completedStats ? $completedStats->total_amount : 0;
        $totalCompletedCount = $completedStats ? $completedStats->total_count : 0;

        return [
            'total_amount' => $totalAmount,
            'completed_count' => $statusCounts['completed'] ?? 0,
            'pending_count' => $statusCounts['pending'] ?? 0,
            'failed_count' => $statusCounts['failed'] ?? 0,
            'refunded_count' => $statusCounts['refunded'] ?? 0,
            'cancelled_count' => $statusCounts['cancelled'] ?? 0,
            'total_count' => array_sum($statusCounts),
        ];
    }

    /**
     * Check if date filtering is active
     *
     * @return bool
     */
    private function isDateFilterActive()
    {
        return request()->has('from_date') && request()->has('to_date');
    }

    /**
     * Get booking status display name
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusDisplay($status)
    {
        $statuses = [
            'pending' => 'Chờ xác nhận',
            'confirmed' => 'Đã xác nhận',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            'no_show' => 'Không đến',
        ];

        return $statuses[$status] ?? 'Không xác định';
    }

    /**
     * Export payments as Excel or PDF
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'excel');


        $query = Payment::select(
            'payments.*',
            'court_bookings.booking_date',
            'court_bookings.start_time',
            'court_bookings.end_time',
            'court_bookings.status as booking_status',
            'court_bookings.customer_name as booking_customer_name',
            'court_bookings.customer_phone as booking_customer_phone',
            'courts.name as court_name',
            'branches.name as branch_name'
        )
            ->leftJoin('court_bookings', 'payments.court_booking_id', '=', 'court_bookings.id')
            ->leftJoin('courts', 'court_bookings.court_id', '=', 'courts.id')
            ->leftJoin('branches', 'court_bookings.branch_id', '=', 'branches.id')
            ->with(['user:id,name,email', 'customer:id,name,phone,email']);


        if ($request->has('from_date') && $request->has('to_date')) {
            $from = Carbon::parse($request->input('from_date'))->startOfDay();
            $to = Carbon::parse($request->input('to_date'))->endOfDay();
            $query->whereBetween('payments.created_at', [$from, $to]);
        } else {

            $to = Carbon::now();
            $from = Carbon::now()->subDays(30);
        }


        if ($request->has('payment_method') && $request->input('payment_method') !== 'all') {
            $query->where('payments.payment_method', $request->input('payment_method'));
        }


        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('payments.status', $request->input('status'));
        }


        if ($request->has('search') && $request->input('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('payments.reference_number', 'like', "%{$searchTerm}%")
                    ->orWhere('payments.transaction_id', 'like', "%{$searchTerm}%")
                    ->orWhere('payments.booking_reference', 'like', "%{$searchTerm}%")
                    ->orWhereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('customer', function ($customerQuery) use ($searchTerm) {
                        $customerQuery->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%")
                            ->orWhere('phone', 'like', "%{$searchTerm}%");
                    });
            });
        }


        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);


        $exportData = collect();
        $query->chunk(100, function ($payments) use (&$exportData) {
            foreach ($payments as $payment) {
                $exportData->push([
                    'ID' => $payment->id,
                    'Reference Number' => $payment->reference_number,
                    'Transaction ID' => $payment->transaction_id,
                    'Booking Reference' => $payment->booking_reference,
                    'Date' => $payment->created_at->format('d/m/Y H:i'),
                    'Customer' => $payment->customer ? $payment->customer->name :
                        ($payment->booking_customer_name ?: 'N/A'),
                    'Contact' => $payment->customer ? ($payment->customer->phone ?? $payment->customer->email) :
                        ($payment->booking_customer_phone ?: 'N/A'),
                    'Court' => $payment->court_name ?: 'N/A',
                    'Branch' => $payment->branch_name ?: 'N/A',
                    'Booking Date' => $payment->booking_date ? Carbon::parse($payment->booking_date)->format('d/m/Y') : 'N/A',
                    'Booking Time' => ($payment->start_time && $payment->end_time) ?
                        Carbon::parse($payment->start_time)->format('H:i') . ' - ' . Carbon::parse($payment->end_time)->format('H:i') : 'N/A',
                    'Payment Method' => $this->getPaymentMethodName($payment->payment_method),
                    'Amount' => number_format((float) $payment->amount, 0, ',', '.') . ' ₫',
                    'Status' => $this->getPaymentStatusDisplay($payment->status),
                    'Notes' => $payment->notes,
                ]);
            }
        });


        $filename = 'payment_history';
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = Carbon::parse($request->input('from_date'));
            $toDate = Carbon::parse($request->input('to_date'));
            $filename .= '_' . $fromDate->format('Ymd') . '_to_' . $toDate->format('Ymd');
        }


        if ($format === 'pdf') {
            return $this->exportPDF($exportData, $filename);
        } else {
            return $this->exportExcel($exportData, $filename);
        }
    }

    /**
     * Export data to Excel
     *
     * @param \Illuminate\Support\Collection $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportExcel($data, $filename)
    {

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();


        if ($data->count() > 0) {
            $columnIndex = 'A';
            foreach (array_keys($data->first()) as $header) {
                $sheet->setCellValue($columnIndex . '1', $header);

                $sheet->getStyle($columnIndex . '1')->getFont()->setBold(true);
                $columnIndex++;
            }
        }


        if ($data->count() > 0) {
            $rowIndex = 2;
            foreach ($data as $row) {
                $columnIndex = 'A';
                foreach ($row as $cellValue) {
                    $sheet->setCellValue($columnIndex . $rowIndex, $cellValue);
                    $columnIndex++;
                }
                $rowIndex++;
            }
        }


        foreach (range('A', chr(64 + count(array_keys($data->first())))) as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }


        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);


        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);


        return response()->download($tempFile, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export data to PDF
     *
     * @param \Illuminate\Support\Collection $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportPDF($data, $filename)
    {

        $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $html .= '<style>
            body {
                font-family: DejaVu Sans, sans-serif;
                font-size: 9px;
                margin: 0;
                padding: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 8px;
            }
            th, td {
                padding: 4px;
                text-align: left;
                border: 1px solid #ddd;
                word-break: break-word;
                max-width: 200px;
                overflow: hidden;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
                color: #333;
                position: sticky;
                top: 0;
            }
            h1 {
                text-align: center;
                margin-bottom: 10px;
                font-size: 14px;
            }
            .container {
                max-width: 100%;
                margin: 0 auto;
            }
            .text-center {
                text-align: center;
            }
            .text-right {
                text-align: right;
            }
            .header-row {
                background-color: #4a5568;
                color: white;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            tr:hover {
                background-color: #f1f1f1;
            }
        </style>';
        $html .= '</head><body>';

        $html .= '<div class="container">';
        $html .= '<h1>Lịch sử thanh toán</h1>';


        $html .= '<p class="text-right">Ngày xuất: ' . date('d/m/Y H:i') . '</p>';

        $html .= '<table>';


        if ($data->count() > 0) {
            $html .= '<tr class="header-row">';
            foreach (array_keys($data->first()) as $header) {
                $html .= '<th>' . $header . '</th>';
            }
            $html .= '</tr>';
        }


        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . $cell . '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '</table>';
        $html .= '</div>';
        $html .= '</body></html>';


        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($html);
        $dompdf->render();


        $pdfContent = $dompdf->output();
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);


        return response()->download($tempFile, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
        ])->deleteFileAfterSend(true);
    }
}
