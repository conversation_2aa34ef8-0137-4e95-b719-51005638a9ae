<?php

declare(strict_types=1);

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Illuminate\Validation\Rule;

class PermissionController extends Controller
{

    public function index()
    {
        $permissions = Permission::all();

        return Inertia::render('SuperAdmin/Permissions/Index', [
            'permissions' => $permissions
        ]);
    }

    public function create()
    {
        return Inertia::render('SuperAdmin/Permissions/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions'],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        Permission::create($validated);

        return redirect()->route('superadmin.permissions.index')
            ->with('flash.success', 'Permission created successfully');
    }

    public function edit(Permission $permission)
    {
        return Inertia::render('SuperAdmin/Permissions/Edit', [
            'permission' => $permission
        ]);
    }

    public function update(Request $request, Permission $permission)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('permissions')->ignore($permission->id)],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        $permission->update($validated);

        return redirect()->route('superadmin.permissions.index')
            ->with('flash.success', 'Permission updated successfully');
    }

    public function destroy(Permission $permission)
    {
        $permission->delete();

        return redirect()->route('superadmin.permissions.index')
            ->with('flash.success', 'Permission deleted successfully');
    }
}