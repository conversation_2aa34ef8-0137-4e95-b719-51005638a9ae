<?php

declare(strict_types=1);

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    public function index()
    {
        $roles = Role::with('permissions')->get();

        return Inertia::render('SuperAdmin/Roles/Index', [
            'roles' => $roles
        ]);
    }

    public function create()
    {
        return Inertia::render('SuperAdmin/Roles/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles'],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        Role::create($validated);

        return redirect()->route('superadmin.roles.index')
            ->with('flash.success', 'Role created successfully');
    }

    public function edit(Role $role)
    {
        $role->load('permissions');

        return Inertia::render('SuperAdmin/Roles/Edit', [
            'role' => $role
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('roles')->ignore($role->id)],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        $role->update($validated);

        return redirect()->route('superadmin.roles.index')
            ->with('flash.success', 'Role updated successfully');
    }

    public function destroy(Role $role)
    {
        if ($role->name === 'super-admin') {
            return redirect()->route('superadmin.roles.index')
                ->with('flash.error', 'Cannot delete the super-admin role');
        }

        $role->delete();

        return redirect()->route('superadmin.roles.index')
            ->with('flash.success', 'Role deleted successfully');
    }
}