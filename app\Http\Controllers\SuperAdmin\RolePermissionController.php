<?php

declare(strict_types=1);

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionController extends Controller
{
    public function index()
    {
        $roles = Role::with([
            'permissions' => function ($query) {
                $query->where('name', 'like', 'booking:%');
            }
        ])->get();

        $permissions = Permission::where('name', 'like', 'booking:%')->get();

        return Inertia::render('SuperAdmin/RolePermissions/Index', [
            'roles' => $roles,
            'permissions' => $permissions
        ]);
    }

    public function syncPermissions(Request $request)
    {
        $validated = $request->validate([
            'role_id' => ['required', 'exists:roles,id'],
            'permissions' => ['required', 'array'],
            'permissions.*' => ['exists:permissions,id']
        ]);

        $role = Role::findById($validated['role_id']);

        if ($role->name === 'super-admin' && $request->user()->hasRole('super-admin')) {
            $role->syncPermissions($validated['permissions']);
        } elseif ($role->name !== 'super-admin') {
            $role->syncPermissions($validated['permissions']);
        } else {
            return redirect()->back()->with('error', 'Bạn không thể sửa đổi quyền của vai trò super-admin');
        }

        return redirect()->route('superadmin.role-permissions.index')
            ->with('flash.success', 'Cập nhật quyền thành công');
    }
}