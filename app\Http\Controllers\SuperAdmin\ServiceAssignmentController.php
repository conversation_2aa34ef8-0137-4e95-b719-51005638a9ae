<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\Branch;
use App\Models\CourtService;
use App\Models\CourtServiceAssign;
use App\Models\BranchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ServiceAssignmentController extends Controller
{

    public function index(Request $request)
    {
        $services = CourtService::query()
            ->when($request->input('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->input('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->when($request->input('business_id'), function ($query, $businessId) use ($request) {
                // Filter by services assigned to the selected business
                $serviceIds = CourtServiceAssign::where('business_id', $businessId)
                    ->select('court_service_id')
                    ->distinct()
                    ->pluck('court_service_id');

                $query->whereIn('id', $serviceIds);
            })
            // Load service assignments with their related business and branch info
            ->with([
                'serviceAssigns' => function ($query) use ($request) {
                    $query->with('business:id,name', 'branch:id,name,business_id');

                    if ($request->input('business_id')) {
                        $query->where('business_id', $request->input('business_id'));
                    }
                }
            ])
            ->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'))
            ->paginate(10)
            ->withQueryString();

        $businesses = Business::where('status', 'active')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Services/Assignment/Index', [
            'services' => $services,
            'businesses' => $businesses,
            'filters' => $request->only(['search', 'status', 'business_id', 'sort', 'direction']),
        ]);
    }


    public function assign($serviceId)
    {
        $service = CourtService::findOrFail($serviceId);

        $businesses = Business::where('status', 'active')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        $branches = Branch::when(request('business_id'), function ($query, $businessId) {
            $query->where('business_id', $businessId);
        })
            ->select('id', 'name', 'business_id')
            ->with('business:id,name')
            ->orderBy('name')
            ->get();

        $assignments = BranchService::where('court_service_id', $serviceId)
            ->with('branch:id,name,business_id')
            ->get();

        return Inertia::render('SuperAdmin/Services/Assignment/Assign', [
            'service' => $service,
            'businesses' => $businesses,
            'branches' => $branches,
            'assignments' => $assignments,
            'filters' => request()->only(['business_id']),
        ]);
    }


    public function storeAssignment(Request $request, $serviceId)
    {
        $validator = Validator::make($request->all(), [
            'branches' => 'required|array',
            'branches.*.branch_id' => 'required|exists:branches,id',
            'branches.*.price' => 'nullable|numeric|min:0',
            'branches.*.member_price' => 'nullable|numeric|min:0',
            'branches.*.is_active' => 'boolean',
            'branches.*.description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $service = CourtService::findOrFail($serviceId);

        foreach ($request->branches as $branchData) {
            $branch = Branch::findOrFail($branchData['branch_id']);


            BranchService::updateOrCreate(
                [
                    'branch_id' => $branch->id,
                    'court_service_id' => $service->id,
                ],
                [
                    'price' => $branchData['price'] ?? $service->price,
                    'member_price' => $branchData['member_price'] ?? null,
                    'is_active' => $branchData['is_active'] ?? true,
                    'description' => $branchData['description'] ?? null,
                ]
            );
        }

        return redirect()->route('superadmin.services.assignments.index')
            ->with('message', __('service.assignment_success'));
    }


    public function destroyAssignment($serviceId, $branchId)
    {
        $assignment = BranchService::where('court_service_id', $serviceId)
            ->where('branch_id', $branchId)
            ->firstOrFail();

        $assignment->delete();

        return back()->with('message', __('service.assignment_deleted'));
    }


    public function updateAssignment(Request $request, $serviceId, $branchId)
    {
        $validator = Validator::make($request->all(), [
            'price' => 'nullable|numeric|min:0',
            'member_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $assignment = BranchService::where('court_service_id', $serviceId)
            ->where('branch_id', $branchId)
            ->firstOrFail();

        $assignment->update([
            'price' => $request->price,
            'member_price' => $request->member_price,
            'is_active' => $request->is_active,
            'description' => $request->description,
        ]);

        return back()->with('message', __('service.assignment_updated'));
    }


    public function getBranchesByBusiness($businessId)
    {
        $branches = Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json($branches);
    }
}
