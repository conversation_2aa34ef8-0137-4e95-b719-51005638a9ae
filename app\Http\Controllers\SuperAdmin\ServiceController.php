<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\CourtService;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ServiceController extends Controller
{

    public function index(Request $request)
    {
        $services = CourtService::query()
            ->when($request->input('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->input('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'))
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('SuperAdmin/Services/Index', [
            'services' => $services,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }


    public function create()
    {
        return Inertia::render('SuperAdmin/Services/Create');
    }


    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        $service = CourtService::create($validated);

        return redirect()->route('superadmin.services.index')
            ->with('message', __('service.service_created'));
    }


    public function show(CourtService $service)
    {
        $businessesUsingService = $service->businesses()->with('branches')->get();

        return Inertia::render('SuperAdmin/Services/Show', [
            'service' => $service,
            'businessesUsingService' => $businessesUsingService
        ]);
    }


    public function edit(CourtService $service)
    {
        return Inertia::render('SuperAdmin/Services/Edit', [
            'service' => $service
        ]);
    }


    public function update(Request $request, CourtService $service)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        $service->update($validated);

        return redirect()->route('superadmin.services.index')
            ->with('message', __('service.service_updated'));
    }


    public function destroy(CourtService $service)
    {

        if ($service->bookings()->count() > 0) {
            return back()->withErrors([
                'error' => __('service.service_delete_error')
            ]);
        }

        $service->delete();

        return redirect()->route('superadmin.services.index')
            ->with('message', __('service.service_deleted'));
    }
}
