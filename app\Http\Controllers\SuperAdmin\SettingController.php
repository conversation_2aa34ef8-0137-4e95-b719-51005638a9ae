<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    public function index()
    {
        $aboutSettings = SystemSetting::getByGroup('about');
        $termsSettings = SystemSetting::getByGroup('terms');
        $generalSettings = SystemSetting::getByGroup('general');
        $emailSettings = SystemSetting::getByGroup('email');
        $seoSettings = SystemSetting::getByGroup('seo');
        $socialSettings = SystemSetting::getByGroup('social');
        $paymentSettings = SystemSetting::getByGroup('payment');
        $landingPageSettings = SystemSetting::getByGroup('landing_page');
        $uiSettings = SystemSetting::getByGroup('ui');

        return Inertia::render('SuperAdmin/Settings/Index', [
            'aboutSettings' => $aboutSettings,
            'termsSettings' => $termsSettings,
            'generalSettings' => $generalSettings,
            'emailSettings' => $emailSettings,
            'seoSettings' => $seoSettings,
            'socialSettings' => $socialSettings,
            'paymentSettings' => $paymentSettings,
            'landingPageSettings' => $landingPageSettings,
            'uiSettings' => $uiSettings,
        ]);
    }

    /*
     * Update system settings
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $settings = $request->validate([
            'settings' => 'required|array',
            'settings.*.id' => 'sometimes|numeric',
            'settings.*.key_setting' => 'required|string',
            'settings.*.setting_value' => 'nullable|string',
            'settings.*.group' => 'required|string',
            'settings.*.display_name' => 'required|string',
            'settings.*.type' => 'required|string',
        ]);

        foreach ($settings['settings'] as $setting) {
            SystemSettingService::set(
                $setting['key_setting'],
                $setting['setting_value'],
                $setting['group'],
                $setting['display_name'],
                $setting['type']
            );
        }

        return redirect()->back()->with('flash.success', 'Cài đặt đã được cập nhật thành công.');
    }


    /**
     * Upload image for settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|file|max:2048|mimes:jpeg,png,jpg,gif,svg',
                'key' => 'required|string',
            ]);
            if (!$request->hasFile('image') || !$request->file('image')->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file upload'
                ], 400);
            }
            $file = $request->file('image');
            $originalName = $file->getClientOriginalName();
            $filename = pathinfo($originalName, PATHINFO_FILENAME) . '_' . time() . '.' . $file->getClientOriginalExtension();
            $directory = 'settings';
            $storagePath = public_path('storage/' . $directory);
            if (!file_exists($storagePath)) {
                mkdir($storagePath, 0755, true);
            }
            $file->move($storagePath, $filename);
            $url = '/storage/' . $directory . '/' . $filename;
            $existingSetting = SystemSetting::where('key_setting', $request->key)->first();
            $group = $existingSetting ? $existingSetting->group : 'general';
            $displayName = $existingSetting ? $existingSetting->display_name : $request->key;
            $setting = SystemSettingService::set(
                $request->key,
                $url,
                $group,
                $displayName,
                'image'
            );

            return response()->json([
                'success' => true,
                'url' => $url,
                'setting' => $setting
            ]);
        } catch (\Exception $e) {
            Log::error('Image upload failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload HTML file for landing page
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadHtmlFile(Request $request)
    {
        try {
            $request->validate([
                'html_file' => 'required|file|mimes:html,htm|max:5120',
            ]);
            if (!$request->hasFile('html_file') || !$request->file('html_file')->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file upload'
                ], 400);
            }
            $file = $request->file('html_file');
            $htmlContent = file_get_contents($file->getRealPath());
            SystemSettingService::set(
                'landing_custom_html',
                $htmlContent,
                'landing_page',
                'HTML tùy chỉnh',
                'textarea'
            );
            SystemSettingService::set(
                'landing_page_type',
                'custom',
                'landing_page',
                'Kiểu trang chủ',
                'select'
            );

            return response()->json([
                'success' => true,
                'message' => 'File HTML đã được tải lên thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('HTML file upload failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload HTML file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete image for settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteImage(Request $request)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);
            $existingSetting = SystemSetting::where('key_setting', $request->key)->first();

            if (!$existingSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            if ($existingSetting->setting_value) {
                $filePath = str_replace('/storage/', '', $existingSetting->setting_value);
                $fullPath = public_path('storage/' . $filePath);

                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
            }
            $setting = SystemSettingService::set(
                $request->key,
                null,
                $existingSetting->group,
                $existingSetting->display_name,
                'image'
            );

            return response()->json([
                'success' => true,
                'message' => 'Hình ảnh đã được xóa thành công',
                'setting' => $setting
            ]);
        } catch (\Exception $e) {
            Log::error('Image deletion failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }
}
