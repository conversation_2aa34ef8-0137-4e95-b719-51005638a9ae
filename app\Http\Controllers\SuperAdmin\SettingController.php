<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    public function index()
    {
        $aboutSettings = SystemSetting::getByGroup('about');
        $termsSettings = SystemSetting::getByGroup('terms');
        $generalSettings = SystemSetting::getByGroup('general');
        $emailSettings = SystemSetting::getByGroup('email');
        $seoSettings = SystemSetting::getByGroup('seo');
        $socialSettings = SystemSetting::getByGroup('social');
        $paymentSettings = SystemSetting::getByGroup('payment');
        $landingPageSettings = SystemSetting::getByGroup('landing_page');
        $uiSettings = SystemSetting::getByGroup('ui');

        if ($aboutSettings->isEmpty()) {
            $this->createDefaultAboutSettings();
            $aboutSettings = SystemSetting::getByGroup('about');
        }

        if ($termsSettings->isEmpty()) {
            $this->createDefaultTermsSettings();
            $termsSettings = SystemSetting::getByGroup('terms');
        }

        if ($generalSettings->isEmpty()) {
            $this->createDefaultGeneralSettings();
            $generalSettings = SystemSetting::getByGroup('general');
        }

        if ($emailSettings->isEmpty()) {
            $this->createDefaultEmailSettings();
            $emailSettings = SystemSetting::getByGroup('email');
        }

        if ($seoSettings->isEmpty()) {
            $this->createDefaultSeoSettings();
            $seoSettings = SystemSetting::getByGroup('seo');
        }

        if ($socialSettings->isEmpty()) {
            $this->createDefaultSocialSettings();
            $socialSettings = SystemSetting::getByGroup('social');
        }

        if ($paymentSettings->isEmpty()) {
            $this->createDefaultPaymentSettings();
            $paymentSettings = SystemSetting::getByGroup('payment');
        }

        if ($landingPageSettings->isEmpty()) {
            $this->createDefaultLandingPageSettings();
            $landingPageSettings = SystemSetting::getByGroup('landing_page');
        }

        if ($uiSettings->isEmpty()) {
            $this->createDefaultUiSettings();
            $uiSettings = SystemSetting::getByGroup('ui');
        }

        return Inertia::render('SuperAdmin/Settings/Index', [
            'aboutSettings' => $aboutSettings,
            'termsSettings' => $termsSettings,
            'generalSettings' => $generalSettings,
            'emailSettings' => $emailSettings,
            'seoSettings' => $seoSettings,
            'socialSettings' => $socialSettings,
            'paymentSettings' => $paymentSettings,
            'landingPageSettings' => $landingPageSettings,
            'uiSettings' => $uiSettings,
        ]);
    }

    /*
     * Update system settings
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $settings = $request->validate([
            'settings' => 'required|array',
            'settings.*.id' => 'sometimes|numeric',
            'settings.*.key_setting' => 'required|string',
            'settings.*.setting_value' => 'nullable|string',
            'settings.*.group' => 'required|string',
            'settings.*.display_name' => 'required|string',
            'settings.*.type' => 'required|string',
        ]);

        foreach ($settings['settings'] as $setting) {
            SystemSettingService::set(
                $setting['key_setting'],
                $setting['setting_value'],
                $setting['group'],
                $setting['display_name'],
                $setting['type']
            );
        }

        return redirect()->back()->with('flash.success', 'Cài đặt đã được cập nhật thành công.');
    }

    /**
     * Create default about page settings
     */
    private function createDefaultAboutSettings()
    {
        $settings = [
            [
                'key' => 'about_title',
                'value' => 'Giới thiệu về Pickleball',
                'display_name' => 'Tiêu đề trang giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_image',
                'value' => '/storage/about.jpg',
                'display_name' => 'Hình ảnh trang giới thiệu',
                'type' => 'image'
            ],
            [
                'key' => 'about_description_1',
                'value' => 'Pickleball là một môn thể thao kết hợp giữa tennis, bóng bàn và cầu lông.',
                'display_name' => 'Mô tả 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_description_2',
                'value' => 'Pickleball được phát triển vào năm 1965 và ngày càng phổ biến trên toàn thế giới.',
                'display_name' => 'Mô tả 2',
                'type' => 'textarea'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'about',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default terms page settings
     */
    private function createDefaultTermsSettings()
    {
        $settings = [
            [
                'key' => 'terms_title',
                'value' => 'Điều khoản sử dụng',
                'display_name' => 'Tiêu đề trang điều khoản',
                'type' => 'text'
            ],
            [
                'key' => 'terms_content',
                'value' => 'Mục đích và phạm vi thu thập thông tin
Việc thu thập thông tin của khách hàng mục đích quản lý Người Tiêu Dùng liên quan đến các hoạt động mua sản phẩm trên website và kịp thời xử lý các tình huống phát sinh (nếu có). Thông tin cá nhân thu thập được sẽ chỉ được sử dụng trong việc kinh doanh bán sản phẩm chúng tôi và nhằm hỗ trợ khách hàng khi gặp những vẫn đề liên quan đến việc mua sản phẩm.

Thông tin cá nhân mà PIBA thu thập bao gồm:

Họ và tên
Địa chỉ
Điện thoại
Email
Ngoài thông tin cá nhân là các thông tin về dịch vụ:

Tên sản phẩm
Số lượng
Đặc tính các sản phẩm
Công ty thu thập các thông tin trên tại các trang: đăng ký / đăng nhập và trang liên hệ

Phạm vi sử dụng thông tin
Thông tin cá nhân thu thập được sẽ chỉ được PIBA sử dụng trong nội bộ công ty và cho một hoặc tất cả các mục đích sau đây:

Hỗ trợ khách hàng
Cung cấp thông tin liên quan đến dịch vụ
Xử lý đơn đặt hàng và cung cấp dịch vụ và thông tin qua trang web của chúng tôi theo yêu cầu của bạn
Chúng tôi có thể sẽ gửi thông tin sản phẩm, dịch vụ mới, thông tin về các sự kiện sắp tới hoặc thông tin tuyển dụng nếu quý khách đăng kí nhận email thông báo.

Ngoài ra, chúng tôi sẽ sử dụng thông tin bạn cung cấp để hỗ trợ quản lý tài khoản khách hàng; xác nhận và thực hiện các giao dịch tài chính liên quan đến các khoản thanh toán trực tuyến của bạn.

Thời gian lưu trữ thông tin
Đối với thông tin cá nhân, PIBA chỉ xóa đi dữ liệu này nếu khách hàng có yêu cầu.

Những người hoặc tổ chức có thể được tiếp cận với thông tin cá nhân
Đối tượng được tiếp cận với thông tin cá nhân của khách hàng thuộc một trong những trường hợp sau:

Công ty PIBA là đơn vị chủ thể của sản phẩm PIBA / PIBA.vn
Cung cấp cho cơ quan quản lý nhà nước có thẩm quyền khi có yêu cầu.
Địa chỉ của đơn vị thu thập và quản lý thông tin cá nhân
Công ty TNHH PIBA

Địa chỉ:
Abc Street, Xyz City, 123456, Country

Điện thoại:
+841234567890

Website:
piba.vn

Email:
<EMAIL>

Phương tiện và công cụ để người dùng tiếp cận và chỉnh sửa dữ liệu cá nhân của mình
PIBA không thu thập thông tin khách hàng qua trang web, thông tin cá nhân khách hàng được thực hiện thu thập qua email liên hệ đặt mua sản phẩm, dịch vụ gửi về hộp mail của chúng tôi: <EMAIL>. Bạn có thể liên hệ địa chỉ email cùng số điện thoại trên để yêu cầu PIBA chỉnh sửa dữ liệu cá nhân của mình.

Cơ chế tiếp nhận và giải quyết khiếu nại của người tiêu dùng
Tại HiSport, việc bảo vệ thông tin cá nhân của bạn là rất quan trọng, bạn được đảm bảo rằng thông tin cung cấp cho chúng tôi sẽ được mật HiSport cam kết không chia sẻ, bán hoặc cho thuê thông tin cá nhân của bạn cho bất kỳ người nào khác. HiSport cam kết chỉ sử dụng các thông tin của bạn vào các trường hợp sau:

Nâng cao chất lượng dịch vụ dành cho khách hàng
Giải quyết các tranh chấp, khiếu nại
Khi cơ quan pháp luật có yêu cầu
PIBA hiểu rằng quyền lợi của bạn trong việc bảo vệ thông tin cá nhân cũng chính là trách nhiệm của chúng tôi nên trong bất kỳ trường hợp có thắc mắc, góp ý nào liên quan đến chính sách bảo mật của PIBA, và liên quan đến việc thông tin cá nhân bị sử dụng sai mục đích hoặc phạm vi đã thông báo vui lòng liên hệ qua số hotline +841234567890 hoặc email: <EMAIL>',
                'display_name' => 'Nội dung điều khoản',
                'type' => 'richtext'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'terms',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default general settings
     */
    private function createDefaultGeneralSettings()
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'Pickleball',
                'display_name' => 'Tên trang web',
                'type' => 'text'
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Đặt sân Pickleball dễ dàng',
                'display_name' => 'Slogan/Tagline',
                'type' => 'text'
            ],
            [
                'key' => 'site_logo',
                'value' => '/storage/logo.png',
                'display_name' => 'Logo chính',
                'type' => 'image'
            ],
            [
                'key' => 'show_logo_text',
                'value' => 'off',
                'display_name' => 'Bật logo chữ bên cạnh logo chính',
                'type' => 'select'
            ],
            [
                'key' => 'logo_text',
                'value' => 'Pickleball',
                'display_name' => 'Tên logo',
                'type' => 'text'
            ],
            [
                'key' => 'logo_text_color',
                'value' => '#333333',
                'display_name' => 'Màu chữ logo',
                'type' => 'color'
            ],
            [
                'key' => 'site_favicon',
                'value' => '/storage/favicon.ico',
                'display_name' => 'Logo thu nhỏ/Favicon',
                'type' => 'image'
            ],
            [
                'key' => 'primary_color',
                'value' => '#ee0033',
                'display_name' => 'Màu chủ đạo',
                'type' => 'color'
            ],
            [
                'key' => 'secondary_color',
                'value' => '#333333',
                'display_name' => 'Màu phụ',
                'type' => 'color'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'display_name' => 'Email liên hệ chính',
                'type' => 'email'
            ],
            [
                'key' => 'contact_phone',
                'value' => '+84 123 456 789',
                'display_name' => 'Số điện thoại hỗ trợ',
                'type' => 'text'
            ],
            [
                'key' => 'contact_address',
                'value' => 'Số 123, Đường ABC, Quận XYZ, TP.HCM',
                'display_name' => 'Địa chỉ văn phòng',
                'type' => 'textarea'
            ],
            [
                'key' => 'working_hours',
                'value' => 'Thứ 2 - Thứ 6: 8:00 - 17:30, Thứ 7: 8:00 - 12:00, Chủ nhật: Nghỉ',
                'display_name' => 'Giờ làm việc',
                'type' => 'textarea'
            ],
            [
                'key' => 'timezone',
                'value' => 'Asia/Ho_Chi_Minh',
                'display_name' => 'Múi giờ mặc định',
                'type' => 'select',
            ],
            [
                'key' => 'default_language',
                'value' => 'vi',
                'display_name' => 'Ngôn ngữ mặc định',
                'type' => 'select',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => 'off',
                'display_name' => 'Chế độ bảo trì',
                'type' => 'select',
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'Hệ thống đang được bảo trì. Vui lòng quay lại sau.',
                'display_name' => 'Thông báo bảo trì',
                'type' => 'textarea'
            ],
            [
                'key' => 'email_verification',
                'value' => 'on',
                'display_name' => 'Xác thực email khi đăng ký',
                'type' => 'select',
            ],

            [
                'key' => 'clear_cache',
                'value' => '',
                'display_name' => 'Xóa cache',
                'type' => 'button'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'general',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default email settings
     */
    private function createDefaultEmailSettings()
    {
        $settings = [
            [
                'key' => 'mail_driver',
                'value' => 'smtp',
                'display_name' => 'Driver Email',
                'type' => 'select'
            ],
            [
                'key' => 'mail_host',
                'value' => 'smtp.gmail.com',
                'display_name' => 'SMTP Host',
                'type' => 'text'
            ],
            [
                'key' => 'mail_port',
                'value' => '587',
                'display_name' => 'SMTP Port',
                'type' => 'text'
            ],
            [
                'key' => 'mail_username',
                'value' => '<EMAIL>',
                'display_name' => 'SMTP Username',
                'type' => 'text'
            ],
            [
                'key' => 'mail_password',
                'value' => '',
                'display_name' => 'SMTP Password',
                'type' => 'password'
            ],
            [
                'key' => 'mail_encryption',
                'value' => 'tls',
                'display_name' => 'SMTP Encryption',
                'type' => 'select'
            ],
            [
                'key' => 'mail_from_address',
                'value' => '<EMAIL>',
                'display_name' => 'Địa chỉ email gửi',
                'type' => 'email'
            ],
            [
                'key' => 'mail_from_name',
                'value' => 'Pickleball',
                'display_name' => 'Tên người gửi',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'email',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default SEO settings
     */
    private function createDefaultSeoSettings()
    {
        $settings = [
            [
                'key' => 'meta_title',
                'value' => 'Pickleball - Đặt sân chơi Pickleball trực tuyến',
                'display_name' => 'Meta Title',
                'type' => 'text'
            ],
            [
                'key' => 'meta_description',
                'value' => 'Đặt sân chơi Pickleball trực tuyến, tìm kiếm và đặt sân một cách dễ dàng.',
                'display_name' => 'Meta Description',
                'type' => 'textarea'
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'pickleball, đặt sân, sân chơi, thể thao',
                'display_name' => 'Meta Keywords',
                'type' => 'text'
            ],
            [
                'key' => 'google_analytics',
                'value' => '',
                'display_name' => 'Google Analytics ID',
                'type' => 'text'
            ],
            [
                'key' => 'google_tag_manager',
                'value' => '',
                'display_name' => 'Google Tag Manager ID',
                'type' => 'text'
            ],
            [
                'key' => 'google_site_verification',
                'value' => '',
                'display_name' => 'Google Site Verification',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'seo',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default social media settings
     */
    private function createDefaultSocialSettings()
    {
        $settings = [
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/',
                'display_name' => 'Facebook URL',
                'type' => 'url'
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'display_name' => 'Twitter URL',
                'type' => 'url'
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/',
                'display_name' => 'Instagram URL',
                'type' => 'url'
            ],
            [
                'key' => 'youtube_url',
                'value' => 'https://youtube.com/',
                'display_name' => 'YouTube URL',
                'type' => 'url'
            ],
            [
                'key' => 'linkedin_url',
                'value' => '',
                'display_name' => 'LinkedIn URL',
                'type' => 'url'
            ],
            [
                'key' => 'tiktok_url',
                'value' => '',
                'display_name' => 'TikTok URL',
                'type' => 'url'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'social',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default payment settings
     */
    private function createDefaultPaymentSettings()
    {
        $settings = [
            [
                'key' => 'payment_currency',
                'value' => 'VND',
                'display_name' => 'Đơn vị tiền tệ',
                'type' => 'select'
            ],
            [
                'key' => 'payment_deadline',
                'value' => '60',
                'display_name' => 'Thời gian thanh toán',
                'type' => 'number'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'payment',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default landing page settings
     */
    private function createDefaultLandingPageSettings()
    {
        $settings = [
            [
                'key' => 'landing_page_type',
                'value' => 'default',
                'display_name' => 'Kiểu trang chủ',
                'type' => 'select'
            ],
            [
                'key' => 'landing_custom_html',
                'value' => '',
                'display_name' => 'HTML tùy chỉnh',
                'type' => 'textarea'
            ],


            [
                'key' => 'hero_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Hero',
                'type' => 'select'
            ],
            [
                'key' => 'hero_title',
                'value' => 'Đặt sân Pickleball dễ dàng',
                'display_name' => 'Tiêu đề Hero',
                'type' => 'text'
            ],
            [
                'key' => 'hero_subtitle',
                'value' => 'Tìm và đặt sân Pickleball gần bạn chỉ với vài thao tác đơn giản',
                'display_name' => 'Mô tả Hero',
                'type' => 'textarea'
            ],
            [
                'key' => 'hero_image',
                'value' => '/storage/landing/hero.jpg',
                'display_name' => 'Hình ảnh Hero',
                'type' => 'image'
            ],
            [
                'key' => 'hero_button_text',
                'value' => 'Đặt sân ngay',
                'display_name' => 'Nút Hero',
                'type' => 'text'
            ],
            [
                'key' => 'hero_button_url',
                'value' => '/search',
                'display_name' => 'Liên kết nút Hero',
                'type' => 'text'
            ],


            [
                'key' => 'features_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Tính năng',
                'type' => 'select'
            ],
            [
                'key' => 'features_title',
                'value' => 'Tính năng nổi bật',
                'display_name' => 'Tiêu đề Tính năng',
                'type' => 'text'
            ],
            [
                'key' => 'features_subtitle',
                'value' => 'Khám phá những tính năng tuyệt vời của chúng tôi',
                'display_name' => 'Mô tả Tính năng',
                'type' => 'textarea'
            ],
            [
                'key' => 'modules_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Modules',
                'type' => 'select'
            ],
            [
                'key' => 'modules_title',
                'value' => 'Các phần của hệ thống',
                'display_name' => 'Tiêu đề Modules',
                'type' => 'text'
            ],
            [
                'key' => 'modules_subtitle',
                'value' => 'Hệ thống của chúng tôi bao gồm nhiều phần khác nhau',
                'display_name' => 'Mô tả Modules',
                'type' => 'textarea'
            ],


            [
                'key' => 'testimonials_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Đánh giá',
                'type' => 'select'
            ],
            [
                'key' => 'testimonials_title',
                'value' => 'Khách hàng nói gì về chúng tôi',
                'display_name' => 'Tiêu đề Đánh giá',
                'type' => 'text'
            ],
            [
                'key' => 'testimonials_subtitle',
                'value' => 'Những đánh giá từ khách hàng của chúng tôi',
                'display_name' => 'Mô tả Đánh giá',
                'type' => 'textarea'
            ],


            [
                'key' => 'contact_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Liên hệ',
                'type' => 'select'
            ],
            [
                'key' => 'contact_title',
                'value' => 'Liên hệ với chúng tôi',
                'display_name' => 'Tiêu đề Liên hệ',
                'type' => 'text'
            ],
            [
                'key' => 'contact_subtitle',
                'value' => 'Gửi thông tin liên hệ cho chúng tôi',
                'display_name' => 'Mô tả Liên hệ',
                'type' => 'textarea'
            ],
            [
                'key' => 'cta_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần CTA',
                'type' => 'select'
            ],
            [
                'key' => 'cta_title',
                'value' => 'Bắt đầu ngay hôm nay',
                'display_name' => 'Tiêu đề CTA',
                'type' => 'text'
            ],
            [
                'key' => 'cta_subtitle',
                'value' => 'Đăng ký tài khoản để trải nghiệm dịch vụ của chúng tôi',
                'display_name' => 'Mô tả CTA',
                'type' => 'textarea'
            ],
            [
                'key' => 'cta_button_text',
                'value' => 'Đăng ký ngay',
                'display_name' => 'Nút CTA',
                'type' => 'text'
            ],
            [
                'key' => 'cta_button_url',
                'value' => '/register',
                'display_name' => 'Liên kết nút CTA',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'landing_page',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Create default UI settings
     */
    private function createDefaultUiSettings()
    {
        $settings = [
            // Login Page Settings
            [
                'key' => 'login_background_image',
                'value' => '/storage/ui/login-bg.jpg',
                'display_name' => 'Ảnh nền trang đăng nhập',
                'type' => 'image'
            ],
            [
                'key' => 'login_main_title',
                'value' => 'Chào mừng bạn trở lại',
                'display_name' => 'Tiêu đề chính trang đăng nhập',
                'type' => 'text'
            ],
            [
                'key' => 'login_subtitle',
                'value' => 'Đăng nhập để trải nghiệm dịch vụ của chúng tôi',
                'display_name' => 'Tiêu đề phụ trang đăng nhập',
                'type' => 'text'
            ],

            // About Page Settings
            [
                'key' => 'about_hero_title',
                'value' => 'Tận hưởng trải nghiệm Pickleball tuyệt vời',
                'display_name' => 'Tiêu đề chính trang Giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_hero_subtitle',
                'value' => 'Nền tảng đặt sân thông minh giúp bạn tìm kiếm, so sánh và đặt sân Pickleball một cách nhanh chóng, dễ dàng',
                'display_name' => 'Tiêu đề phụ trang Giới thiệu',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_hero_button_text',
                'value' => 'Tìm sân ngay',
                'display_name' => 'Nút trang Giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_hero_image',
                'value' => '/storage/about.jpg',
                'display_name' => 'Ảnh chính trang Giới thiệu',
                'type' => 'image'
            ],
            [
                'key' => 'about_features_title',
                'value' => 'Tính năng nổi bật',
                'display_name' => 'Tiêu đề phần tính năng',
                'type' => 'text'
            ],
            [
                'key' => 'about_features_subtitle',
                'value' => 'Khám phá những điều tuyệt vời mà PicklePlay mang lại cho bạn',
                'display_name' => 'Tiêu đề phụ phần tính năng',
                'type' => 'textarea'
            ],

            // Feature 1
            [
                'key' => 'about_feature_1_title',
                'value' => 'Tìm kiếm thông minh',
                'display_name' => 'Tiêu đề tính năng 1',
                'type' => 'text'
            ],
            [
                'key' => 'about_feature_1_description',
                'value' => 'Dễ dàng tìm kiếm sân gần bạn với bản đồ trực quan và bộ lọc đa dạng',
                'display_name' => 'Mô tả tính năng 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_feature_1_icon',
                'value' => 'Search',
                'display_name' => 'Icon tính năng 1',
                'type' => 'text'
            ],

            // Feature 2
            [
                'key' => 'about_feature_2_title',
                'value' => 'Đặt sân theo thời gian thực',
                'display_name' => 'Tiêu đề tính năng 2',
                'type' => 'text'
            ],
            [
                'key' => 'about_feature_2_description',
                'value' => 'Xem lịch trống và đặt sân ngay lập tức với xác nhận tức thì',
                'display_name' => 'Mô tả tính năng 2',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_feature_2_icon',
                'value' => 'Clock',
                'display_name' => 'Icon tính năng 2',
                'type' => 'text'
            ],

            // Feature 3
            [
                'key' => 'about_feature_3_title',
                'value' => 'Thanh toán đa dạng',
                'display_name' => 'Tiêu đề tính năng 3',
                'type' => 'text'
            ],
            [
                'key' => 'about_feature_3_description',
                'value' => 'Hỗ trợ nhiều phương thức thanh toán an toàn, nhanh chóng',
                'display_name' => 'Mô tả tính năng 3',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_feature_3_icon',
                'value' => 'CreditCard',
                'display_name' => 'Icon tính năng 3',
                'type' => 'text'
            ],

            // Feature 4
            [
                'key' => 'about_feature_4_title',
                'value' => 'Ưu đãi hấp dẫn',
                'display_name' => 'Tiêu đề tính năng 4',
                'type' => 'text'
            ],
            [
                'key' => 'about_feature_4_description',
                'value' => 'Nhận khuyến mãi đặc biệt khi đặt sân vào giờ thấp điểm và giới thiệu bạn bè',
                'display_name' => 'Mô tả tính năng 4',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_feature_4_icon',
                'value' => 'Gift',
                'display_name' => 'Icon tính năng 4',
                'type' => 'text'
            ],

            // How It Works Section
            [
                'key' => 'about_how_title',
                'value' => 'Cách thức hoạt động',
                'display_name' => 'Tiêu đề phần cách thức',
                'type' => 'text'
            ],
            [
                'key' => 'about_how_subtitle',
                'value' => 'Chỉ với 3 bước đơn giản để bắt đầu trận đấu Pickleball của bạn',
                'display_name' => 'Tiêu đề phụ phần cách thức',
                'type' => 'textarea'
            ],

            // Step 1
            [
                'key' => 'about_step_1_title',
                'value' => 'Tìm kiếm',
                'display_name' => 'Tiêu đề bước 1',
                'type' => 'text'
            ],
            [
                'key' => 'about_step_1_description',
                'value' => 'Tìm sân phù hợp với thời gian và vị trí của bạn',
                'display_name' => 'Mô tả bước 1',
                'type' => 'textarea'
            ],

            // Step 2
            [
                'key' => 'about_step_2_title',
                'value' => 'Đặt sân',
                'display_name' => 'Tiêu đề bước 2',
                'type' => 'text'
            ],
            [
                'key' => 'about_step_2_description',
                'value' => 'Chọn thời gian và xác nhận đặt sân',
                'display_name' => 'Mô tả bước 2',
                'type' => 'textarea'
            ],

            // Step 3
            [
                'key' => 'about_step_3_title',
                'value' => 'Thanh toán',
                'display_name' => 'Tiêu đề bước 3',
                'type' => 'text'
            ],
            [
                'key' => 'about_step_3_description',
                'value' => 'Hoàn tất thanh toán và nhận xác nhận đặt sân',
                'display_name' => 'Mô tả bước 3',
                'type' => 'textarea'
            ],

            // About CTA Section
            [
                'key' => 'about_cta_title',
                'value' => 'Sẵn sàng trải nghiệm?',
                'display_name' => 'Tiêu đề phần CTA',
                'type' => 'text'
            ],
            [
                'key' => 'about_cta_subtitle',
                'value' => 'Đặt sân ngay hôm nay và tận hưởng trải nghiệm Pickleball tuyệt vời',
                'display_name' => 'Tiêu đề phụ phần CTA',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_cta_button_text',
                'value' => 'Tham gia ngay',
                'display_name' => 'Nút CTA',
                'type' => 'text'
            ],
            [
                'key' => 'about_cta_button_url',
                'value' => '/search',
                'display_name' => 'Liên kết nút CTA',
                'type' => 'text'
            ],

            // Landing Page Hero Settings
            [
                'key' => 'landing_hero_title',
                'value' => 'Đặt sân Pickleball dễ dàng',
                'display_name' => 'Tiêu đề chính Hero',
                'type' => 'text'
            ],
            [
                'key' => 'landing_hero_subtitle',
                'value' => 'Tìm và đặt sân Pickleball gần bạn chỉ với vài thao tác đơn giản',
                'display_name' => 'Tiêu đề phụ Hero',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_hero_image',
                'value' => '/storage/landing/hero.jpg',
                'display_name' => 'Ảnh Hero',
                'type' => 'image'
            ],

            // Features Section Settings
            [
                'key' => 'landing_features_title',
                'value' => 'Tính năng nổi bật',
                'display_name' => 'Tiêu đề chính phần Features',
                'type' => 'text'
            ],
            [
                'key' => 'landing_features_subtitle',
                'value' => 'Khám phá những tính năng tuyệt vời của chúng tôi',
                'display_name' => 'Tiêu đề phụ phần Features',
                'type' => 'textarea'
            ],

            // Feature 1
            [
                'key' => 'landing_feature_1_title',
                'value' => 'Tất cả trong một ứng dụng',
                'display_name' => 'Tiêu đề Feature 1',
                'type' => 'text'
            ],
            [
                'key' => 'landing_feature_1_description',
                'value' => 'Tích hợp đầy đủ các tính năng cần thiết cho người chơi Pickleball trong một nền tảng duy nhất.',
                'display_name' => 'Mô tả Feature 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_feature_1_icon',
                'value' => 'mobile-alt',
                'display_name' => 'Icon Feature 1',
                'type' => 'text'
            ],

            // Feature 2
            [
                'key' => 'landing_feature_2_title',
                'value' => 'Trải nghiệm cá nhân hóa',
                'display_name' => 'Tiêu đề Feature 2',
                'type' => 'text'
            ],
            [
                'key' => 'landing_feature_2_description',
                'value' => 'Gợi ý và nội dung tùy chỉnh dựa trên sở thích và trình độ của từng người dùng.',
                'display_name' => 'Mô tả Feature 2',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_feature_2_icon',
                'value' => 'user-cog',
                'display_name' => 'Icon Feature 2',
                'type' => 'text'
            ],

            // Feature 3
            [
                'key' => 'landing_feature_3_title',
                'value' => 'Kết nối trực tuyến & ngoại tuyến',
                'display_name' => 'Tiêu đề Feature 3',
                'type' => 'text'
            ],
            [
                'key' => 'landing_feature_3_description',
                'value' => 'Kết nối cộng đồng người chơi và tạo điều kiện cho các hoạt động ngoại tuyến.',
                'display_name' => 'Mô tả Feature 3',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_feature_3_icon',
                'value' => 'network-wired',
                'display_name' => 'Icon Feature 3',
                'type' => 'text'
            ],

            // Feature 4
            [
                'key' => 'landing_feature_4_title',
                'value' => 'Thanh toán an toàn',
                'display_name' => 'Tiêu đề Feature 4',
                'type' => 'text'
            ],
            [
                'key' => 'landing_feature_4_description',
                'value' => 'Hệ thống thanh toán bảo mật, đa dạng phương thức và minh bạch giao dịch.',
                'display_name' => 'Mô tả Feature 4',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_feature_4_icon',
                'value' => 'shield-alt',
                'display_name' => 'Icon Feature 4',
                'type' => 'text'
            ],

            // Modules Section Settings
            [
                'key' => 'landing_modules_title',
                'value' => 'Các phần của hệ thống',
                'display_name' => 'Tiêu đề chính phần Modules',
                'type' => 'text'
            ],
            [
                'key' => 'landing_modules_subtitle',
                'value' => 'Hệ thống của chúng tôi bao gồm nhiều phần khác nhau',
                'display_name' => 'Tiêu đề phụ phần Modules',
                'type' => 'textarea'
            ],

            // Module 1
            [
                'key' => 'landing_module_1_tag',
                'value' => 'Module 1',
                'display_name' => 'Tag Module 1',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_1_title',
                'value' => 'Đặt Sân Pickleball',
                'display_name' => 'Tiêu đề Module 1',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_1_description',
                'value' => 'Tìm kiếm và đặt sân Pickleball một cách nhanh chóng, dễ dàng tại các địa điểm gần bạn.',
                'display_name' => 'Mô tả Module 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_1_image',
                'value' => 'https://images.unsplash.com/photo-1594737625785-a6cbdabd333c?auto=format&fit=crop&w=500&q=60',
                'display_name' => 'Ảnh Module 1',
                'type' => 'image'
            ],
            [
                'key' => 'landing_module_1_features',
                'value' => 'Tìm kiếm sân theo vị trí, giá cả, tiện ích
Đặt sân trước và quản lý lịch đặt
Đánh giá và xem review về các sân
Thanh toán trực tuyến an toàn',
                'display_name' => 'Tính năng Module 1 (mỗi dòng là 1 tính năng)',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_1_link',
                'value' => '/search',
                'display_name' => 'Liên kết Module 1',
                'type' => 'text'
            ],

            // Module 2
            [
                'key' => 'landing_module_2_tag',
                'value' => 'Module 2',
                'display_name' => 'Tag Module 2',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_2_title',
                'value' => 'Mạng Xã Hội Pickleball',
                'display_name' => 'Tiêu đề Module 2',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_2_description',
                'value' => 'Kết nối với cộng đồng người chơi Pickleball, chia sẻ kinh nghiệm, tìm đối thủ và bạn chơi cùng.',
                'display_name' => 'Mô tả Module 2',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_2_image',
                'value' => 'https://images.unsplash.com/photo-1556761175-b413da4baf72?auto=format&fit=crop&w=500&q=60',
                'display_name' => 'Ảnh Module 2',
                'type' => 'image'
            ],
            [
                'key' => 'landing_module_2_features',
                'value' => 'Hồ sơ người chơi với thống kê và thành tích
Tìm kiếm và kết nối với người chơi khác
Chia sẻ video, hình ảnh và kỹ thuật
Tạo và tham gia các sự kiện, giải đấu',
                'display_name' => 'Tính năng Module 2 (mỗi dòng là 1 tính năng)',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_2_link',
                'value' => '/under-development',
                'display_name' => 'Liên kết Module 2',
                'type' => 'text'
            ],

            // Module 3
            [
                'key' => 'landing_module_3_tag',
                'value' => 'Module 3',
                'display_name' => 'Tag Module 3',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_3_title',
                'value' => 'Marketplace Pickleball',
                'display_name' => 'Tiêu đề Module 3',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_3_description',
                'value' => 'Cửa hàng trực tuyến cung cấp sản phẩm chất lượng cao: vợt, bóng, giày và phụ kiện.',
                'display_name' => 'Mô tả Module 3',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_3_image',
                'value' => 'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?auto=format&fit=crop&w=500&q=60',
                'display_name' => 'Ảnh Module 3',
                'type' => 'image'
            ],
            [
                'key' => 'landing_module_3_features',
                'value' => 'Sản phẩm chính hãng, đảm bảo chất lượng
So sánh giá và đọc đánh giá sản phẩm
Giao hàng nhanh chóng toàn quốc
Chính sách đổi trả linh hoạt',
                'display_name' => 'Tính năng Module 3 (mỗi dòng là 1 tính năng)',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_3_link',
                'value' => '/marketplace',
                'display_name' => 'Liên kết Module 3',
                'type' => 'text'
            ],

            // Module 4
            [
                'key' => 'landing_module_4_tag',
                'value' => 'Module 4',
                'display_name' => 'Tag Module 4',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_4_title',
                'value' => 'Affiliate Pickleball',
                'display_name' => 'Tiêu đề Module 4',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_4_description',
                'value' => 'Chương trình tiếp thị liên kết giúp người dùng kiếm thu nhập từ giới thiệu sản phẩm.',
                'display_name' => 'Mô tả Module 4',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_4_image',
                'value' => 'https://images.unsplash.com/photo-1553729459-efe14ef6055d?auto=format&fit=crop&w=500&q=60',
                'display_name' => 'Ảnh Module 4',
                'type' => 'image'
            ],
            [
                'key' => 'landing_module_4_features',
                'value' => 'Hoa hồng hấp dẫn cho mỗi lượt giới thiệu thành công
Công cụ tiếp thị và theo dõi hiệu quả
Thanh toán đúng hạn, minh bạch
Hỗ trợ và tư vấn chiến lược tiếp thị',
                'display_name' => 'Tính năng Module 4 (mỗi dòng là 1 tính năng)',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_4_link',
                'value' => '/under-development',
                'display_name' => 'Liên kết Module 4',
                'type' => 'text'
            ],

            // Module 5
            [
                'key' => 'landing_module_5_tag',
                'value' => 'Module 5',
                'display_name' => 'Tag Module 5',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_5_title',
                'value' => 'Study Pickleball',
                'display_name' => 'Tiêu đề Module 5',
                'type' => 'text'
            ],
            [
                'key' => 'landing_module_5_description',
                'value' => 'Nền tảng học tập trực tuyến với các khóa học, video hướng dẫn và tài liệu chuyên sâu.',
                'display_name' => 'Mô tả Module 5',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_5_image',
                'value' => 'https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=500&q=60',
                'display_name' => 'Ảnh Module 5',
                'type' => 'image'
            ],
            [
                'key' => 'landing_module_5_features',
                'value' => 'Khóa học từ cơ bản đến nâng cao
Video phân tích kỹ thuật, chiến thuật
Tư vấn 1:1 với HLV chuyên nghiệp
Theo dõi tiến độ và thành tích học tập',
                'display_name' => 'Tính năng Module 5 (mỗi dòng là 1 tính năng)',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_module_5_link',
                'value' => '/under-development',
                'display_name' => 'Liên kết Module 5',
                'type' => 'text'
            ],

            // Testimonials Section Settings
            [
                'key' => 'landing_testimonials_title',
                'value' => 'Khách hàng nói gì về chúng tôi',
                'display_name' => 'Tiêu đề phần Testimonials',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonials_subtitle',
                'value' => 'Những đánh giá từ khách hàng của chúng tôi',
                'display_name' => 'Tiêu đề phụ phần Testimonials',
                'type' => 'textarea'
            ],

            // Testimonial 1
            [
                'key' => 'landing_testimonial_1_name',
                'value' => 'Nguyễn Văn A',
                'display_name' => 'Tên người đánh giá 1',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_1_role',
                'value' => 'Người chơi Pickleball 3 năm',
                'display_name' => 'Vai trò người đánh giá 1',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_1_quote',
                'value' => 'PIBA đã giúp tôi tìm thấy cộng đồng Pickleball tại thành phố mới. Tôi đã kết nối với nhiều người chơi cùng đam mê và tham gia các giải đấu thú vị.',
                'display_name' => 'Nội dung đánh giá 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_testimonial_1_avatar',
                'value' => 'https://randomuser.me/api/portraits/men/32.jpg',
                'display_name' => 'Ảnh đại diện người đánh giá 1',
                'type' => 'image'
            ],

            // Testimonial 2
            [
                'key' => 'landing_testimonial_2_name',
                'value' => 'Trần Thị B',
                'display_name' => 'Tên người đánh giá 2',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_2_role',
                'value' => 'Huấn luyện viên Pickleball',
                'display_name' => 'Vai trò người đánh giá 2',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_2_quote',
                'value' => 'Là một HLV, PIBA giúp tôi quản lý lịch dạy và kết nối với học viên hiệu quả. Nền tảng này còn giúp tôi chia sẻ kiến thức và kỹ thuật với cộng đồng.',
                'display_name' => 'Nội dung đánh giá 2',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_testimonial_2_avatar',
                'value' => 'https://randomuser.me/api/portraits/women/44.jpg',
                'display_name' => 'Ảnh đại diện người đánh giá 2',
                'type' => 'image'
            ],

            // Testimonial 3
            [
                'key' => 'landing_testimonial_3_name',
                'value' => 'Lê Văn C',
                'display_name' => 'Tên người đánh giá 3',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_3_role',
                'value' => 'Người mới chơi Pickleball',
                'display_name' => 'Vai trò người đánh giá 3',
                'type' => 'text'
            ],
            [
                'key' => 'landing_testimonial_3_quote',
                'value' => 'Mới làm quen với Pickleball, tôi đã học được rất nhiều qua các video hướng dẫn trên PIBA. Việc đặt sân và tìm bạn chơi cùng cũng trở nên dễ dàng hơn bao giờ hết.',
                'display_name' => 'Nội dung đánh giá 3',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_testimonial_3_avatar',
                'value' => 'https://randomuser.me/api/portraits/men/67.jpg',
                'display_name' => 'Ảnh đại diện người đánh giá 3',
                'type' => 'image'
            ],

            // CTA Section Settings
            [
                'key' => 'landing_cta_title',
                'value' => 'Bắt đầu trải nghiệm Pickleball ngay hôm nay',
                'display_name' => 'Tiêu đề phần CTA',
                'type' => 'text'
            ],
            [
                'key' => 'landing_cta_subtitle',
                'value' => 'Tham gia cùng hàng nghìn người chơi Pickleball khác và khám phá cách Pickleball có thể nâng tầm trải nghiệm của bạn.',
                'display_name' => 'Tiêu đề phụ phần CTA',
                'type' => 'textarea'
            ],
            [
                'key' => 'landing_cta_button_text',
                'value' => 'Tham gia ngay',
                'display_name' => 'Nút CTA',
                'type' => 'text'
            ],
            [
                'key' => 'landing_cta_button_url',
                'value' => '/register',
                'display_name' => 'Liên kết nút CTA',
                'type' => 'text'
            ],
            [
                'key' => 'landing_cta_background',
                'value' => '/storage/landing/cta-bg.jpg',
                'display_name' => 'Ảnh nền CTA',
                'type' => 'image'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'ui',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    /**
     * Upload image for settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|file|max:2048|mimes:jpeg,png,jpg,gif,svg',
                'key' => 'required|string',
            ]);
            if (!$request->hasFile('image') || !$request->file('image')->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file upload'
                ], 400);
            }
            $file = $request->file('image');
            $originalName = $file->getClientOriginalName();
            $filename = pathinfo($originalName, PATHINFO_FILENAME) . '_' . time() . '.' . $file->getClientOriginalExtension();
            $directory = 'settings';
            $storagePath = public_path('storage/' . $directory);
            if (!file_exists($storagePath)) {
                mkdir($storagePath, 0755, true);
            }
            $file->move($storagePath, $filename);
            $url = '/storage/' . $directory . '/' . $filename;
            $existingSetting = SystemSetting::where('key_setting', $request->key)->first();
            $group = $existingSetting ? $existingSetting->group : 'general';
            $displayName = $existingSetting ? $existingSetting->display_name : $request->key;
            $setting = SystemSettingService::set(
                $request->key,
                $url,
                $group,
                $displayName,
                'image'
            );

            return response()->json([
                'success' => true,
                'url' => $url,
                'setting' => $setting
            ]);
        } catch (\Exception $e) {
            Log::error('Image upload failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload HTML file for landing page
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadHtmlFile(Request $request)
    {
        try {
            $request->validate([
                'html_file' => 'required|file|mimes:html,htm|max:5120',
            ]);
            if (!$request->hasFile('html_file') || !$request->file('html_file')->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file upload'
                ], 400);
            }
            $file = $request->file('html_file');
            $htmlContent = file_get_contents($file->getRealPath());
            SystemSettingService::set(
                'landing_custom_html',
                $htmlContent,
                'landing_page',
                'HTML tùy chỉnh',
                'textarea'
            );
            SystemSettingService::set(
                'landing_page_type',
                'custom',
                'landing_page',
                'Kiểu trang chủ',
                'select'
            );

            return response()->json([
                'success' => true,
                'message' => 'File HTML đã được tải lên thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('HTML file upload failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload HTML file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete image for settings
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteImage(Request $request)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);
            $existingSetting = SystemSetting::where('key_setting', $request->key)->first();

            if (!$existingSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            if ($existingSetting->setting_value) {
                $filePath = str_replace('/storage/', '', $existingSetting->setting_value);
                $fullPath = public_path('storage/' . $filePath);

                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
            }
            $setting = SystemSettingService::set(
                $request->key,
                null,
                $existingSetting->group,
                $existingSetting->display_name,
                'image'
            );

            return response()->json([
                'success' => true,
                'message' => 'Hình ảnh đã được xóa thành công',
                'setting' => $setting
            ]);
        } catch (\Exception $e) {
            Log::error('Image deletion failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }
}
