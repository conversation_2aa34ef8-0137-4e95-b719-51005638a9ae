<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Business;
use App\Models\Court;
use App\Models\CourtBooking;
use App\Models\Booking;
use App\Models\Branch;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatisticController extends Controller
{
    /**
     * Get dashboard statistics for the SuperAdmin
     *
     * @return array
     */
    public function getDashboardStats(Request $request = null)
    {
        $now = Carbon::now();
        $previousMonth = $now->copy()->subMonth();
        $currentMonth = $now->copy()->startOfMonth();
        $period = $request ? $request->input('period', 'monthly') : 'monthly';

        $totalUsers = User::count();
        $previousMonthUsers = User::where('created_at', '<', $currentMonth)->count();
        $newUsers = $totalUsers - $previousMonthUsers;
        $userGrowth = $previousMonthUsers > 0 ? round(($newUsers / $previousMonthUsers) * 100, 1) : 0;

        $totalBusinesses = Business::count();
        $previousMonthBusinesses = Business::where('created_at', '<', $currentMonth)->count();
        $newBusinesses = $totalBusinesses - $previousMonthBusinesses;
        $businessGrowth = $previousMonthBusinesses > 0 ? round(($newBusinesses / $previousMonthBusinesses) * 100, 1) : 0;

        $totalCourts = Court::count();
        $activeCourts = Court::where('is_active', 1)->count();
        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;

        // Get total revenue from completed bookings
        $totalRevenue = Booking::where('status', 'completed')->sum('total_price');

        $currentMonthRevenue = Booking::where('status', 'completed')
            ->whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->sum('total_price');

        $previousMonthRevenue = Booking::where('status', 'completed')
            ->whereYear('booking_date', $previousMonth->year)
            ->whereMonth('booking_date', $previousMonth->month)
            ->sum('total_price');

        $revenueGrowth = $previousMonthRevenue > 0 ? round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1) : 0;
        $chartData = $this->getChartDataByPeriod($period, $now);
        $labels = $chartData['labels'];
        $counts = $chartData['counts'];
        $revenue = $chartData['revenue'];

        $weeklyData = [];
        $startOfMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        $daysInMonth = $startOfMonth->daysInMonth;
        $weekSizes = $this->calculateWeekSizes($daysInMonth);

        $currentDate = $startOfMonth->copy();

        for ($weekIndex = 0; $weekIndex < count($weekSizes); $weekIndex++) {
            $weekSize = $weekSizes[$weekIndex];

            $weekStart = $currentDate->copy();
            $weekEnd = $currentDate->copy()->addDays($weekSize - 1);

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $weeklyBookings = Booking::where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->count();

            $weeklyRevenue = Booking::where('status', 'completed')
                ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('total_price');

            $weeklyData[] = [
                'week' => __('dashboard.week') . ' ' . ($weekIndex + 1),
                'start_date' => $weekStart->format('d/m/Y'),
                'end_date' => $weekEnd->format('d/m/Y'),
                'bookings' => $weeklyBookings,
                'revenue' => $weeklyRevenue,
                'date_range' => $weekStart->format('d/m') . ' - ' . $weekEnd->format('d/m/Y')
            ];

            $currentDate->addDays($weekSize);
        }

        $topBusinesses = Business::select('businesses.id', 'businesses.name')
            ->join('branches', 'businesses.id', '=', 'branches.business_id')
            ->join('bookings', 'branches.id', '=', 'bookings.branch_id')
            ->where('bookings.status', 'completed')
            ->groupBy('businesses.id', 'businesses.name')
            ->orderByRaw('COUNT(DISTINCT bookings.reference_number) DESC')
            ->limit(10)
            ->get()
            ->map(function ($business) {
                $bookingCount = Booking::whereHas('branch', function ($q) use ($business) {
                    $q->where('business_id', $business->id);
                })
                    ->where('status', 'completed')
                    ->distinct('reference_number')
                    ->count('reference_number');

                return [
                    'name' => $business->name,
                    'bookings' => $bookingCount
                ];
            });

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        return [
            'users' => [
                'total' => $totalUsers,
                'growth' => $userGrowth
            ],
            'businesses' => [
                'total' => $totalBusinesses,
                'growth' => $businessGrowth
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $counts,
                'revenue' => $revenue
            ],
            'weekly' => $weeklyData,
            'top_businesses' => $topBusinesses
        ];
    }

    /**
     * Calculate the number of days for each week in a month
     *
     * @param int $daysInMonth
     * @return array
     */
    private function calculateWeekSizes($daysInMonth)
    {
        switch ($daysInMonth) {
            case 28:
                return [7, 7, 7, 7];
            case 29:
                return [8, 7, 7, 7];
            case 30:
                return [8, 7, 8, 7];
            case 31:
            default:
                return [8, 8, 8, 7];
        }
    }

    /**
     * Advanced analytics page for SuperAdmin
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function analytics(Request $request)
    {
        $now = Carbon::now();
        $period = $request->input('period', 'monthly');
        $isCustomRange = false;
        $customRange = null;

        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');
            $stats = $this->getCustomRangeStats($fromDate, $toDate);
            $isCustomRange = true;
            $customRange = $stats['date_range'] ?? null;
        } else {
            $stats = $this->getDashboardStats($request);
        }

        $topPerformingCourts = $this->getTopPerformingCourts();
        $bookingTrends = $this->getBookingTrends($period);
        $topPerformingBranches = $this->getTopPerformingBranches();
        $topPerformingBusinesses = $this->getTopPerformingBusinesses();
        $userMetrics = $this->getUserMetrics();
        $top10NewBookings = $this->getTop10NewBookings();

        $topUsersByBookings = $this->getTopUsersByBookings();
        $recentPayments = $this->getRecentPayments();
        $popularBookingTimes = $this->getPopularBookingTimes();

        return inertia('SuperAdmin/Analytics', [
            'stats' => $stats,
            'top_courts' => $topPerformingCourts,
            'top_10_new_bookings' => $top10NewBookings,
            'booking_trends' => $bookingTrends,
            'top_branches' => $topPerformingBranches,
            'top_businesses' => $topPerformingBusinesses,
            'user_metrics' => $userMetrics,
            'top_users' => $topUsersByBookings,
            'recent_payments' => $recentPayments,
            'popular_times' => $popularBookingTimes,
            'last_updated' => $now->toIso8601String(),
            'current_period' => $period,
            'is_custom_range' => $isCustomRange,
            'custom_range' => $customRange
        ]);
    }

    /**
     * Get top performing courts (most bookings)
     *
     * @return array
     */
    private function getTopPerformingCourts()
    {
        return Court::select('courts.id', 'courts.name', 'branches.name as branch_name')
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->join('branches', 'courts.branch_id', '=', 'branches.id')
            ->join('bookings', 'court_bookings.booking_id', '=', 'bookings.id')
            ->where('bookings.status', 'completed')
            ->groupBy('courts.id', 'courts.name', 'branches.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(10)
            ->get()
            ->map(function ($court) {
                $bookingCount = CourtBooking::where('court_id', $court->id)
                    ->whereHas('booking', function ($q) {
                        $q->where('status', 'completed');
                    })
                    ->count();

                // Get revenue from bookings related to this court
                $courtBookings = CourtBooking::where('court_id', $court->id)->pluck('booking_id');
                $revenue = Booking::whereIn('id', $courtBookings)
                    ->where('status', 'completed')
                    ->sum('total_price');

                return [
                    'id' => $court->id,
                    'name' => $court->name,
                    'branch' => $court->branch_name,
                    'bookings' => $bookingCount,
                    'revenue' => $revenue
                ];
            })->toArray();
    }

    /**
     * Get booking trends based on time periods
     *
     * @param string $period
     * @return array
     */
    private function getBookingTrends($period)
    {
        $now = Carbon::now();
        $comparisonData = [];

        $today = Booking::whereDate('booking_date', $now->format('Y-m-d'))->where('status', 'completed')->count();
        $yesterday = Booking::whereDate('booking_date', $now->copy()->subDay()->format('Y-m-d'))->where('status', 'completed')->count();
        $dailyGrowth = $yesterday > 0 ? round((($today - $yesterday) / $yesterday) * 100, 1) : 0;

        $thisWeekStart = $now->copy()->startOfWeek();
        $thisWeekEnd = $now->copy()->endOfWeek();
        $lastWeekStart = $now->copy()->subWeek()->startOfWeek();
        $lastWeekEnd = $now->copy()->subWeek()->endOfWeek();

        $thisWeek = Booking::whereBetween('booking_date', [$thisWeekStart->format('Y-m-d'), $thisWeekEnd->format('Y-m-d')])
            ->where('status', 'completed')->count();
        $lastWeek = Booking::whereBetween('booking_date', [$lastWeekStart->format('Y-m-d'), $lastWeekEnd->format('Y-m-d')])
            ->where('status', 'completed')->count();
        $weeklyGrowth = $lastWeek > 0 ? round((($thisWeek - $lastWeek) / $lastWeek) * 100, 1) : 0;

        $thisMonth = Booking::whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->where('status', 'completed')->count();
        $lastMonth = Booking::whereYear('booking_date', $now->copy()->subMonth()->year)
            ->whereMonth('booking_date', $now->copy()->subMonth()->month)
            ->where('status', 'completed')->count();
        $monthlyGrowth = $lastMonth > 0 ? round((($thisMonth - $lastMonth) / $lastMonth) * 100, 1) : 0;

        $thisYear = Booking::whereYear('booking_date', $now->year)
            ->where('status', 'completed')->count();
        $lastYear = Booking::whereYear('booking_date', $now->year - 1)
            ->where('status', 'completed')->count();
        $yearlyGrowth = $lastYear > 0 ? round((($thisYear - $lastYear) / $lastYear) * 100, 1) : 0;

        return [
            'daily' => [
                'current' => $today,
                'previous' => $yesterday,
                'growth' => $dailyGrowth
            ],
            'weekly' => [
                'current' => $thisWeek,
                'previous' => $lastWeek,
                'growth' => $weeklyGrowth
            ],
            'monthly' => [
                'current' => $thisMonth,
                'previous' => $lastMonth,
                'growth' => $monthlyGrowth
            ],
            'yearly' => [
                'current' => $thisYear,
                'previous' => $lastYear,
                'growth' => $yearlyGrowth
            ]
        ];
    }


    private function getTopPerformingBusinesses()
    {
        return Business::select('businesses.id', 'businesses.name')
            ->join('branches', 'businesses.id', '=', 'branches.business_id')
            ->join('bookings', 'branches.id', '=', 'bookings.branch_id')
            ->where('bookings.status', 'completed')
            ->groupBy('businesses.id', 'businesses.name')
            ->orderByRaw('COUNT(bookings.id) DESC')
            ->limit(10)
            ->get()
            ->map(function ($business) {
                $bookingCount = Booking::whereHas('branch', function ($query) use ($business) {
                    $query->where('business_id', $business->id);
                })
                    ->where('status', 'completed')
                    ->count();

                $revenue = Booking::whereHas('branch', function ($query) use ($business) {
                    $query->where('business_id', $business->id);
                })
                    ->where('status', 'completed')
                    ->sum('total_price');

                return [
                    'id' => $business->id,
                    'name' => $business->name,
                    'bookings' => $bookingCount,
                    'revenue' => $revenue
                ];
            })->toArray();
    }

    /**
     * Get top performing branches
     *
     * @return array
     */
    private function getTopPerformingBranches()
    {
        return Branch::select('branches.id', 'branches.name', 'businesses.name as business_name')
            ->join('bookings', 'branches.id', '=', 'bookings.branch_id')
            ->join('businesses', 'branches.business_id', '=', 'businesses.id')
            ->where('bookings.status', 'completed')
            ->groupBy('branches.id', 'branches.name', 'businesses.name')
            ->orderByRaw('COUNT(bookings.id) DESC')
            ->limit(10)
            ->get()
            ->map(function ($branch) {
                $bookingCount = Booking::where('branch_id', $branch->id)
                    ->where('status', 'completed')
                    ->count();

                $revenue = Booking::where('branch_id', $branch->id)
                    ->where('status', 'completed')
                    ->sum('total_price');

                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'business' => $branch->business_name,
                    'bookings' => $bookingCount,
                    'revenue' => $revenue
                ];
            })->toArray();
    }

    private function getTop10NewBookings()
    {
        return Booking::where('status', 'completed')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get user metrics and statistics
     *
     * @return array
     */
    private function getUserMetrics()
    {
        $now = Carbon::now();
        $startOfMonth = $now->copy()->startOfMonth();
        $newUsersThisMonth = User::whereBetween('created_at', [$startOfMonth, $now])->count();

        $roleDistribution = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->select('roles.name', DB::raw('count(*) as count'))
            ->groupBy('roles.name')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($role) {
                return [
                    'role' => $role->name,
                    'count' => $role->count
                ];
            });

        $activeUsers = Booking::select('user_id')
            ->where('created_at', '>=', $now->copy()->subDays(30))
            ->distinct('user_id')
            ->count();

        $activityByDayOfWeek = [];
        $startOfWeek = $now->copy()->startOfWeek();
        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $day = $i + 1;
            $count = Booking::whereDate('booking_date', $date->format('Y-m-d'))->count();

            $activityByDayOfWeek[] = [
                'day' => $this->getDayName($day),
                'count' => $count,
                'date' => $date->format('d/m')
            ];
        }

        return [
            'new_users' => $newUsersThisMonth,
            'active_users' => $activeUsers,
            'role_distribution' => $roleDistribution,
            'activity_by_day' => $activityByDayOfWeek
        ];
    }

    /**
     * Helper function to get day name
     *
     * @param int $day
     * @return string
     */
    private function getDayName($day)
    {
        $days = [
            1 => 'Thứ 2',
            2 => 'Thứ 3',
            3 => 'Thứ 4',
            4 => 'Thứ 5',
            5 => 'Thứ 6',
            6 => 'Thứ 7',
            7 => 'Chủ nhật',
        ];

        return $days[$day] ?? '';
    }

    /**
     * Lấy thống kê cho business cụ thể
     *
     * @param int $businessId
     * @param string $period
     * @return array
     */
    public function getBusinessStats($businessId, $period = 'monthly')
    {
        $now = Carbon::now();

        $branchCount = Branch::where('business_id', $businessId)->count();
        $courtCount = Court::whereHas('branch', function ($q) use ($businessId) {
            $q->where('business_id', $businessId);
        })->count();

        $totalRevenue = Booking::whereHas('courtBookings.court.branch', function ($q) use ($businessId) {
            $q->where('business_id', $businessId);
        })
            ->where('status', 'completed')
            ->sum('total_price');

        $chartData = $this->getChartDataByBusinessAndPeriod($businessId, $period, $now);

        return [
            'branch_count' => $branchCount,
            'court_count' => $courtCount,
            'total_revenue' => $totalRevenue,
            'chart' => $chartData,
        ];
    }

    public function getChartDataByBusinessAndPeriod($businessId, $period, Carbon $now)
    {
        $labels = [];
        $counts = [];
        $revenue = [];

        switch ($period) {
            case 'daily':
                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $count = Booking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->sum('total_price');

                    $labels[] = $hour->format('H:i');
                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':
                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    $count = Booking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':
                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    $count = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:
                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    $count = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'counts' => $counts,
            'revenue' => $revenue
        ];
    }

    /**
     * Get statistics data for a custom date range
     * Uses the detailed daily data to calculate ranges
     *
     * @param string $fromDate Format: Y-m-d
     * @param string $toDate Format: Y-m-d
     * @return array
     */
    public function getCustomRangeStats($fromDate, $toDate)
    {
        $from = Carbon::parse($fromDate)->startOfDay();
        $to = Carbon::parse($toDate)->endOfDay();

        $diffInDays = $from->diffInDays($to) + 1;
        $previousFrom = $from->copy()->subDays($diffInDays);
        $previousTo = $from->copy()->subDay();

        $totalUsers = User::count();
        $newUsers = User::whereBetween('created_at', [$from, $to])->count();
        $previousPeriodUsers = User::whereBetween('created_at', [$previousFrom, $previousTo])->count();
        $userGrowth = $previousPeriodUsers > 0 ? round((($newUsers - $previousPeriodUsers) / $previousPeriodUsers) * 100, 1) : 0;

        $totalBusinesses = Business::count();
        $newBusinesses = Business::whereBetween('created_at', [$from, $to])->count();
        $previousPeriodBusinesses = Business::whereBetween('created_at', [$previousFrom, $previousTo])->count();
        $businessGrowth = $previousPeriodBusinesses > 0 ? round((($newBusinesses - $previousPeriodBusinesses) / $previousPeriodBusinesses) * 100, 1) : 0;

        $totalCourts = Court::count();
        $activeCourts = Court::where('is_active', 1)->count();
        $courtUtilization = $totalCourts > 0 ? round(($activeCourts / $totalCourts) * 100, 1) : 0;

        $rangeRevenue = Booking::where('status', 'completed')
            ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->sum('total_price');

        $previousRangeRevenue = Booking::where('status', 'completed')
            ->whereBetween('booking_date', [$previousFrom->format('Y-m-d'), $previousTo->format('Y-m-d')])
            ->sum('total_price');

        $revenueGrowth = $previousRangeRevenue > 0 ? round((($rangeRevenue - $previousRangeRevenue) / $previousRangeRevenue) * 100, 1) : 0;

        $totalRevenue = Booking::where('status', 'completed')->sum('total_price');
        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        $labels = [];
        $counts = [];
        $revenue = [];

        if ($diffInDays <= 31) {
            for ($i = 0; $i < $diffInDays; $i++) {
                $day = $from->copy()->addDays($i);
                $labels[] = $day->format('d/m');

                $count = Booking::where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->count();

                $rev = Booking::where('status', 'completed')
                    ->whereDate('booking_date', $day)
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;
            }
        } else if ($diffInDays <= 90) {
            $currentDate = $from->copy();
            while ($currentDate->lte($to)) {
                $weekStart = $currentDate->copy()->startOfWeek();
                $weekEnd = $currentDate->copy()->endOfWeek();

                if ($weekEnd->gt($to)) {
                    $weekEnd = $to->copy();
                }

                $labels[] = $weekStart->format('d/m') . '-' . $weekEnd->format('d/m');

                $count = Booking::where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->count();

                $rev = Booking::where('status', 'completed')
                    ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addWeek();
            }
        } else {
            $currentDate = $from->copy()->startOfMonth();
            $endDate = $to->copy()->endOfMonth();

            while ($currentDate->lte($endDate)) {
                $monthStart = $currentDate->copy()->startOfMonth();
                $monthEnd = $currentDate->copy()->endOfMonth();

                if ($monthEnd->gt($to)) {
                    $monthEnd = $to->copy();
                }

                $labels[] = $currentDate->format('m/Y');

                $count = Booking::where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->count();

                $rev = Booking::where('status', 'completed')
                    ->whereYear('booking_date', $currentDate->year)
                    ->whereMonth('booking_date', $currentDate->month)
                    ->sum('total_price');

                $counts[] = $count;
                $revenue[] = $rev / 1000000;

                $currentDate->addMonth();
            }
        }

        $topBusinesses = Business::select('businesses.id', 'businesses.name')
            ->join('branches', 'businesses.id', '=', 'branches.business_id')
            ->join('courts', 'branches.id', '=', 'courts.branch_id')
            ->join('bookings', 'courts.id', '=', 'bookings.court_id')
            ->where('bookings.status', 'completed')
            ->whereBetween('bookings.booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
            ->groupBy('businesses.id', 'businesses.name')
            ->orderByRaw('COUNT(bookings.id) DESC')
            ->limit(2)
            ->get()
            ->map(function ($business) use ($from, $to) {
                $bookingCount = Booking::whereHas('courtBookings.court', function ($query) use ($business) {
                    $query->whereHas('branch', function ($q) use ($business) {
                        $q->where('business_id', $business->id);
                    });
                })
                    ->where('status', 'completed')
                    ->whereBetween('booking_date', [$from->format('Y-m-d'), $to->format('Y-m-d')])
                    ->count();

                return [
                    'name' => $business->name,
                    'bookings' => $bookingCount
                ];
            });

        return [
            'users' => [
                'total' => $totalUsers,
                'new' => $newUsers,
                'growth' => $userGrowth
            ],
            'businesses' => [
                'total' => $totalBusinesses,
                'new' => $newBusinesses,
                'growth' => $businessGrowth
            ],
            'courts' => [
                'total' => $totalCourts,
                'active' => $activeCourts,
                'utilization' => $courtUtilization
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'range_revenue' => $rangeRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => [
                'labels' => $labels,
                'bookings' => $counts,
                'revenue' => $revenue
            ],
            'top_businesses' => $topBusinesses,
            'date_range' => [
                'from' => $from->format('Y-m-d'),
                'to' => $to->format('Y-m-d'),
                'formatted' => $from->format('d/m/Y') . ' - ' . $to->format('d/m/Y')
            ]
        ];
    }

    /**
     * Get statistics for a specific branch
     *
     * @param int $branchId
     * @param string $period
     * @return array
     */
    public function getBranchStats($branchId, $period = 'monthly')
    {
        $now = Carbon::now();
        $branch = Branch::findOrFail($branchId);

        $courtCount = Court::where('branch_id', $branchId)->count();
        $activeCourtCount = Court::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->count();

        $courtUtilization = $courtCount > 0 ? round(($activeCourtCount / $courtCount) * 100, 1) : 0;

        // Get bookings for courts in this branch
        $courtIds = Court::where('branch_id', $branchId)->pluck('id');
        $bookingIds = CourtBooking::whereIn('court_id', $courtIds)->pluck('booking_id');

        $totalRevenue = Booking::whereIn('id', $bookingIds)
            ->where('status', 'completed')
            ->sum('total_price');

        $totalRevenueFormatted = number_format($totalRevenue / 1000000, 1) . 'M';

        $previousMonth = $now->copy()->subMonth();
        $currentMonth = $now->copy()->startOfMonth();

        $currentMonthBookingIds = CourtBooking::whereIn('court_id', $courtIds)
            ->whereYear('booking_date', $now->year)
            ->whereMonth('booking_date', $now->month)
            ->pluck('booking_id');

        $currentMonthRevenue = Booking::whereIn('id', $currentMonthBookingIds)
            ->where('status', 'completed')
            ->sum('total_price');

        $previousMonthBookingIds = CourtBooking::whereIn('court_id', $courtIds)
            ->whereYear('booking_date', $previousMonth->year)
            ->whereMonth('booking_date', $previousMonth->month)
            ->pluck('booking_id');

        $previousMonthRevenue = Booking::whereIn('id', $previousMonthBookingIds)
            ->where('status', 'completed')
            ->sum('total_price');

        $revenueGrowth = $previousMonthRevenue > 0
            ? round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1)
            : 0;

        $totalBookings = Booking::whereIn('id', $bookingIds)
            ->where('status', 'completed')
            ->count();

        $currentMonthBookings = Booking::whereIn('id', $currentMonthBookingIds)
            ->where('status', 'completed')
            ->count();

        $previousMonthBookings = Booking::whereIn('id', $previousMonthBookingIds)
            ->where('status', 'completed')
            ->count();

        $bookingGrowth = $previousMonthBookings > 0
            ? round((($currentMonthBookings - $previousMonthBookings) / $previousMonthBookings) * 100, 1)
            : 0;

        $chartData = $this->getChartDataByBranchAndPeriod($branchId, $period, $now);

        $topCourts = Court::where('courts.branch_id', $branchId)
            ->select('courts.id', 'courts.name')
            ->join('court_bookings', 'courts.id', '=', 'court_bookings.court_id')
            ->join('bookings', 'court_bookings.booking_id', '=', 'bookings.id')
            ->where('bookings.status', 'completed')
            ->groupBy('courts.id', 'courts.name')
            ->orderByRaw('COUNT(court_bookings.id) DESC')
            ->limit(5)
            ->get()
            ->map(function ($court) {
                // Get booking count for this court
                $bookingCount = CourtBooking::where('court_id', $court->id)
                    ->whereHas('booking', function ($q) {
                    $q->where('status', 'completed');
                })
                    ->count();

                return [
                    'name' => $court->name,
                    'bookings' => $bookingCount
                ];
            });

        return [
            'branch' => [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->address
            ],
            'courts' => [
                'total' => $courtCount,
                'active' => $activeCourtCount,
                'utilization' => $courtUtilization
            ],
            'bookings' => [
                'total' => $totalBookings,
                'current_month' => $currentMonthBookings,
                'growth' => $bookingGrowth
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'formatted' => $totalRevenueFormatted,
                'growth' => $revenueGrowth
            ],
            'chart' => $chartData,
            'top_courts' => $topCourts
        ];
    }

    /**
     * Get chart data for a specific branch
     */
    private function getChartDataByBranchAndPeriod($branchId, $period, Carbon $now)
    {
        $labels = [];
        $counts = [];
        $revenue = [];

        // Get courts in this branch
        $courtIds = Court::where('branch_id', $branchId)->pluck('id');

        switch ($period) {
            case 'daily':
                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $labels[] = $hour->format('H:i');

                    // Get bookings for this hour
                    $bookingIds = CourtBooking::whereIn('court_id', $courtIds)
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->pluck('booking_id');

                    $count = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->count();

                    $rev = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':
                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    // Get bookings for this day
                    $bookingIds = CourtBooking::whereIn('court_id', $courtIds)
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->pluck('booking_id');

                    $count = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->count();

                    $rev = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':
                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    // Get bookings for this month
                    $bookingIds = CourtBooking::whereIn('court_id', $courtIds)
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->pluck('booking_id');

                    $count = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->count();

                    $rev = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:
                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    // Get bookings for this month
                    $bookingIds = CourtBooking::whereIn('court_id', $courtIds)
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->pluck('booking_id');

                    $count = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->count();

                    $rev = Booking::whereIn('id', $bookingIds)
                        ->where('status', 'completed')
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'counts' => $counts,
            'revenue' => $revenue
        ];
    }

    /**
     * Get chart data based on selected period
     *
     * @param string $period
     * @param Carbon $now
     * @return array
     */
    private function getChartDataByPeriod($period, Carbon $now)
    {
        $labels = [];
        $counts = [];
        $revenue = [];

        switch ($period) {
            case 'daily':
                for ($i = 23; $i >= 0; $i--) {
                    $hour = $now->copy()->subHours($i);
                    $hourStart = $hour->copy()->startOfHour();
                    $hourEnd = $hour->copy()->endOfHour();

                    $labels[] = $hour->format('H:i');

                    $count = Booking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereBetween('created_at', [$hourStart, $hourEnd])
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'weekly':
                for ($i = 6; $i >= 0; $i--) {
                    $day = $now->copy()->subDays($i);
                    $labels[] = $day->format('D');

                    $count = Booking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereDate('booking_date', $day->format('Y-m-d'))
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'yearly':
                for ($i = 11; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M Y');

                    $count = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;

            case 'monthly':
            default:
                for ($i = 5; $i >= 0; $i--) {
                    $month = $now->copy()->subMonths($i);
                    $labels[] = $month->format('M');

                    $count = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->count();

                    $rev = Booking::where('status', 'completed')
                        ->whereYear('booking_date', $month->year)
                        ->whereMonth('booking_date', $month->month)
                        ->sum('total_price');

                    $counts[] = $count;
                    $revenue[] = $rev / 1000000;
                }
                break;
        }

        return [
            'labels' => $labels,
            'counts' => $counts,
            'revenue' => $revenue
        ];
    }

    /**
     * Get top users by number of bookings and spending
     *
     * @return array
     */
    private function getTopUsersByBookings()
    {
        return User::select('users.id', 'users.name', 'users.email')
            ->join('bookings', 'users.id', '=', 'bookings.user_id')
            ->where('bookings.status', 'completed')
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByRaw('COUNT(bookings.id) DESC')
            ->limit(10)
            ->get()
            ->map(function ($user, $index) {
                $bookingCount = Booking::where('user_id', $user->id)
                    ->where('status', 'completed')
                    ->count();

                $totalSpending = Booking::where('user_id', $user->id)
                    ->where('status', 'completed')
                    ->sum('total_price');

                $latestBooking = Booking::where('user_id', $user->id)
                    ->where('status', 'completed')
                    ->latest('booking_date')
                    ->first();

                $lastBookingDate = $latestBooking ? Carbon::parse($latestBooking->booking_date)->format('d/m/Y') : 'N/A';

                return [
                    'id' => $user->id,
                    'index' => $index + 1,
                    'name' => $user->name,
                    'email' => $user->email,
                    'booking_count' => $bookingCount,
                    'total_spending' => $totalSpending,
                    'formatted_spending' => number_format($totalSpending, 0, ',', '.') . ' ₫',
                    'last_booking_date' => $lastBookingDate
                ];
            })->toArray();
    }

    /**
     * Get recent payment transactions
     *
     * @param int $limit
     * @return array
     */
    private function getRecentPayments($limit = 5)
    {
        return Payment::with(['courtBooking.user', 'courtBooking.court.branch'])
            ->latest('created_at')
            ->limit($limit)
            ->get()
            ->map(function ($payment) {
                $booking = $payment->courtBooking;
                $user = $booking ? $booking->user : null;
                $court = $booking ? $booking->court : null;
                $branch = $court ? $court->branch : null;

                $courtName = $court ? $court->name : 'N/A';
                $branchName = $branch ? $branch->name : 'N/A';
                $courtInfo = $courtName . ' - ' . $branchName;

                return [
                    'id' => $payment->id,
                    'transaction_id' => '#TRX-' . $payment->id,
                    'user_name' => $user ? $user->name : 'N/A',
                    'court_info' => $courtInfo,
                    'booking_date' => $booking ? Carbon::parse($booking->booking_date)->format('d/m/Y') : 'N/A',
                    'payment_method' => $payment->payment_method,
                    'amount' => $payment->amount,
                    'formatted_amount' => number_format($payment->amount, 0, ',', '.') . ' ₫',
                    'status' => $payment->status,
                    'created_at' => $payment->created_at->format('d/m/Y H:i')
                ];
            })->toArray();
    }

    /**
     * Get popular booking time slots with statistics
     *
     * @return array
     */
    private function getPopularBookingTimes()
    {
        $totalBookings = Booking::where('status', 'completed')->count();
        if ($totalBookings === 0) {
            return [];
        }

        // Use court bookings to analyze time slots
        $bookingsByHour = CourtBooking::join('bookings', 'court_bookings.booking_id', '=', 'bookings.id')
            ->where('bookings.status', 'completed')
            ->select(DB::raw('HOUR(court_bookings.start_time) as hour'), DB::raw('COUNT(*) as count'), DB::raw('SUM(bookings.total_price) as revenue'))
            ->groupBy(DB::raw('HOUR(court_bookings.start_time)'))
            ->orderBy('count', 'desc')
            ->get();

        $maxCount = $bookingsByHour->max('count');

        return $bookingsByHour->map(function ($item) use ($totalBookings, $maxCount) {
            $hour = (int) $item->hour;
            $nextHour = ($hour + 1) % 24;
            $timeSlot = sprintf('%02d:00 - %02d:00', $hour, $nextHour);

            $percentage = ($item->count / $totalBookings) * 100;

            $popularityWidth = ($item->count / $maxCount) * 100;

            return [
                'time_slot' => $timeSlot,
                'count' => $item->count,
                'percentage' => round($percentage, 1),
                'formatted_percentage' => round($percentage, 1) . '%',
                'revenue' => $item->revenue,
                'formatted_revenue' => number_format($item->revenue, 0, ',', '.') . ' ₫',
                'popularity_width' => round($popularityWidth)
            ];
        })->take(6)->toArray();
    }

    /**
     * Export statistics data in various formats (Excel, PDF)
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\Response
     */
    public function exportStatistics(Request $request)
    {
        $dataType = $request->input('data_type', 'all');
        $format = $request->input('format', 'excel');
        $period = $request->input('period', 'monthly');

        $data = $this->getExportData($dataType, $period, $request);
        $filename = $this->generateExportFilename($dataType, $format);
        $title = $this->getTitleForDataType($dataType);

        $exportService = new \App\Services\ExportService();
        $exportData = collect($this->formatDataForExport($data, $dataType));

        switch ($format) {
            case 'excel':
                return $exportService->exportExcel($exportData, $filename);
            case 'pdf':
                $subtitle = '';
                if (isset($data['date_range'])) {
                    $subtitle = 'Từ ' . $data['date_range']['formatted'];
                } else {
                    $subtitle = 'Thời gian: ' . now()->format('d/m/Y');
                }
                return $exportService->exportPDF($exportData, $filename, $title, $subtitle);
            default:
                return response()->json(['error' => 'Unsupported export format'], 400);
        }
    }

    /**
     * Format data for export based on data type
     * 
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function formatDataForExport($data, $dataType)
    {
        if ($dataType === 'all') {
            // For "all" data type, we need to format each section separately
            $formattedData = [];

            if (isset($data['top_users'])) {
                $headers = $this->getTranslatedHeaders('top_users');
                foreach ($data['top_users'] as $index => $user) {
                    $formattedData[] = [
                        $headers[0] => $user['index'],
                        $headers[1] => $user['name'],
                        $headers[2] => $user['email'],
                        $headers[3] => $user['booking_count'],
                        $headers[4] => $user['formatted_spending'],
                        $headers[5] => $user['last_booking_date']
                    ];
                }
            }

            return $formattedData;
        } else {
            // For specific data types
            $headers = $this->getHeadersForDataType($data, $dataType);
            $columns = $this->getColumnsForDataType($data, $dataType);

            $formattedData = [];
            foreach ($data as $item) {
                $row = [];
                foreach ($columns as $index => $column) {
                    $header = $headers[$index] ?? $column;
                    $row[$header] = isset($item[$column]) ? $item[$column] : '';
                }
                $formattedData[] = $row;
            }

            return $formattedData;
        }
    }

    /**
     * Get the data to be exported based on type
     *
     * @param string $dataType
     * @param string $period
     * @param Request $request
     * @return array
     */
    private function getExportData($dataType, $period, Request $request)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {
            $stats = $this->getCustomRangeStats($fromDate, $toDate);
            $isCustomRange = true;
        } else {
            $stats = $this->getDashboardStats($request);
            $isCustomRange = false;
        }

        switch ($dataType) {
            case 'top_users':
                return $this->getTopUsersByBookings();
            case 'recent_payments':
                return $this->getRecentPayments(50);
            case 'popular_times':
                return $this->getPopularBookingTimes();
            case 'top_courts':
                return $this->getTopPerformingCourts();
            case 'top_branches':
                return $this->getTopPerformingBranches();
            case 'all':
            default:
                return [
                    'top_users' => $this->getTopUsersByBookings(),
                    'recent_payments' => $this->getRecentPayments(50),
                    'popular_times' => $this->getPopularBookingTimes(),
                    'top_courts' => $this->getTopPerformingCourts(),
                    'top_branches' => $this->getTopPerformingBranches(),
                    'stats' => $stats,
                    'period' => $period,
                    'is_custom_range' => $isCustomRange,
                    'date_range' => $isCustomRange ? $stats['date_range'] : null
                ];
        }
    }

    /**
     * Generate a filename for the export
     *
     * @param string $dataType
     * @param string $format
     * @return string
     */
    private function generateExportFilename($dataType, $format)
    {
        $date = Carbon::now()->format('Y-m-d');

        $dataTypeNames = [
            'top_users' => 'top-users',
            'recent_payments' => 'recent-payments',
            'popular_times' => 'popular-time-slots',
            'top_courts' => 'top-courts',
            'top_branches' => 'top-branches',
            'all' => 'full-statistics'
        ];

        $name = $dataTypeNames[$dataType] ?? $dataType;

        return "pickleball-statistics-{$name}-{$date}";
    }

    /**
     * Get translated headers for export files
     *
     * @param string $dataType
     * @return array
     */
    private function getTranslatedHeaders($dataType)
    {
        $headerMappings = [
            'top_users' => [
                __('common.no'),
                __('common.full_name'),
                __('common.email'),
                __('common.booking_count'),
                __('common.total_spending'),
                __('common.last_booking')
            ],
            'recent_payments' => [
                __('common.transaction_id'),
                __('common.user'),
                __('common.court'),
                __('common.booking_date'),
                __('common.payment_method'),
                __('common.amount'),
                __('common.status')
            ],
            'popular_times' => [
                __('common.time_slot'),
                __('common.booking_count'),
                __('common.percentage'),
                __('common.revenue')
            ],
            'top_courts' => [
                __('common.court_name'),
                __('common.branch'),
                __('common.booking_count'),
                __('common.revenue')
            ],
            'top_branches' => [
                __('common.branch_name'),
                __('common.business'),
                __('common.booking_count'),
                __('common.revenue')
            ]
        ];

        return $headerMappings[$dataType] ?? [];
    }

    /**
     * Get headers for a data type
     *
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function getHeadersForDataType($data, $dataType)
    {
        if (strpos($dataType, 'top-users') !== false || $dataType === 'top_users') {
            return $this->getTranslatedHeaders('top_users');
        } elseif (strpos($dataType, 'recent-payments') !== false || $dataType === 'recent_payments') {
            return $this->getTranslatedHeaders('recent_payments');
        } elseif (strpos($dataType, 'popular-time-slots') !== false || $dataType === 'popular_times') {
            return $this->getTranslatedHeaders('popular_times');
        } elseif (strpos($dataType, 'top-courts') !== false || $dataType === 'top_courts') {
            return $this->getTranslatedHeaders('top_courts');
        } elseif (strpos($dataType, 'top-branches') !== false || $dataType === 'top_branches') {
            return $this->getTranslatedHeaders('top_branches');
        }

        if (!empty($data) && is_array($data) && !empty($data[0])) {
            return array_keys($data[0]);
        }

        return ['Data'];
    }

    /**
     * Get column keys for a data type
     *
     * @param array $data
     * @param string $dataType
     * @return array
     */
    private function getColumnsForDataType($data, $dataType)
    {
        if (strpos($dataType, 'top-users') !== false || $dataType === 'top_users') {
            return ['index', 'name', 'email', 'booking_count', 'formatted_spending', 'last_booking_date'];
        } elseif (strpos($dataType, 'recent-payments') !== false || $dataType === 'recent_payments') {
            return ['transaction_id', 'user_name', 'court_info', 'booking_date', 'payment_method', 'formatted_amount', 'status'];
        } elseif (strpos($dataType, 'popular-time-slots') !== false || $dataType === 'popular_times') {
            return ['time_slot', 'count', 'formatted_percentage', 'formatted_revenue'];
        } elseif (strpos($dataType, 'top-courts') !== false || $dataType === 'top_courts') {
            return ['name', 'branch', 'bookings', 'revenue'];
        } elseif (strpos($dataType, 'top-branches') !== false || $dataType === 'top_branches') {
            return ['name', 'business', 'bookings', 'revenue'];
        }

        if (!empty($data) && is_array($data) && !empty($data[0])) {
            return array_keys($data[0]);
        }

        return ['data'];
    }

    /**
     * Get a title for a data type
     *
     * @param string $dataType
     * @return string
     */
    private function getTitleForDataType($dataType)
    {
        $titleMappings = [
            'top_users' => __('common.top_10_users_by_bookings'),
            'recent_payments' => __('common.recent_payments'),
            'popular_times' => __('common.popular_booking_times'),
            'top_courts' => __('common.top_10_most_booked_courts'),
            'top_branches' => __('common.top_10_highest_revenue_branches'),
            'all' => __('common.analytics') . ' - ' . __('common.detailed_analysis')
        ];

        return $titleMappings[$dataType] ?? __('common.analytics');
    }
}
