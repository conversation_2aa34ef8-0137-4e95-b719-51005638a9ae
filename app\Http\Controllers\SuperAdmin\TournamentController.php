<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Tournament;
use App\Models\Business;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class TournamentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Tournament::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhere('organizer', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('business_id')) {
            if ($request->business_id === 'system') {
                $query->whereNull('business_id');
            } else {
                $query->where('business_id', $request->business_id);
            }
        }

        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $tournaments = $query->paginate(15)->withQueryString();

        $businesses = Business::select('id', 'name')->get();

        return Inertia::render('SuperAdmin/Tournament/Index', [
            'tournaments' => $tournaments,
            'businesses' => $businesses,
            'filters' => $request->only(['search', 'status', 'business_id']),
            'statuses' => Tournament::getStatuses(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $businesses = Business::select('id', 'name')->get();

        return Inertia::render('SuperAdmin/Tournament/Create', [
            'businesses' => $businesses,
            'statuses' => Tournament::getStatuses(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'nullable|exists:businesses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'start_date' => 'required|date|after:today',
            'end_date' => 'required|date|after:start_date',
            'location' => 'required|string|max:255',
            'participants_limit' => 'required|integer|min:1',
            'registration_deadline' => 'required|date|before:start_date',
            'entry_fee' => 'required|numeric|min:0',
            'prize_money' => 'required|numeric|min:0',
            'status' => ['required', Rule::in(array_keys(Tournament::getStatuses()))],
            'categories' => 'required|array|min:1',
            'categories.*' => 'string',
            'organizer' => 'required|string|max:255',
            'featured' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_url'] = $request->file('image')->store('tournaments', 'public');
        }

        // Remove the image field from validated data
        unset($validated['image']);

        $tournament = Tournament::create($validated);

        return redirect()->route('superadmin.tournaments.index')
            ->with('flash.success', 'Tournament created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Tournament $tournament)
    {
        $tournament->load('business');

        return Inertia::render('SuperAdmin/Tournament/Show', [
            'tournament' => $tournament,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tournament $tournament)
    {
        $businesses = Business::select('id', 'name')->get();

        return Inertia::render('SuperAdmin/Tournament/Edit', [
            'tournament' => $tournament,
            'businesses' => $businesses,
            'statuses' => Tournament::getStatuses(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Tournament $tournament)
    {
        $validated = $request->validate([
            'business_id' => 'nullable|exists:businesses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'location' => 'required|string|max:255',
            'participants_limit' => 'required|integer|min:1',
            'registration_deadline' => 'required|date|before:start_date',
            'entry_fee' => 'required|numeric|min:0',
            'prize_money' => 'required|numeric|min:0',
            'status' => ['required', Rule::in(array_keys(Tournament::getStatuses()))],
            'categories' => 'required|array|min:1',
            'categories.*' => 'string',
            'organizer' => 'required|string|max:255',
            'featured' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($tournament->image_url) {
                Storage::disk('public')->delete($tournament->image_url);
            }
            $validated['image_url'] = $request->file('image')->store('tournaments', 'public');
        }

        // Remove the image field from validated data
        unset($validated['image']);

        $tournament->update($validated);

        return redirect()->route('superadmin.tournaments.index')
            ->with('flash.success', 'Tournament updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tournament $tournament)
    {
        // Delete associated image if exists
        if ($tournament->image_url) {
            Storage::disk('public')->delete($tournament->image_url);
        }

        $tournament->delete();

        return redirect()->route('superadmin.tournaments.index')
            ->with('flash.success', 'Tournament deleted successfully.');
    }
}
