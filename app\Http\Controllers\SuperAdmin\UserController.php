<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use App\Models\Business;
use App\Models\Court;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $filters = request()->only(['search', 'role', 'business', 'branch']);
        $users = User::withFilteredSearch($filters);
        $roles = Role::all();
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'users' => $users,
            'filters' => $filters,
            'roles' => $roles,
            'businesses' => $businesses
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'roles' => $roles,
            'businesses' => $businesses,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => 'nullable|array',
            'status' => 'required|in:active,inactive',
            'business_id' => 'nullable|exists:businesses,id',
            'branch_id' => 'nullable|exists:branches,id',
        ], __('validation.users'));

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'status' => $request->status,
            'business_id' => $request->business_id,
            'branch_id' => $request->branch_id,
        ]);

        if ($request->roles && count($request->roles) > 0) {
            $user->assignRole($request->roles);
        }

        return redirect()->route('superadmin.users.index')
            ->with('flash.message', __('users.user_created_successfully'))
            ->with('flash.type', 'success');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        
        $user->load(['roles', 'business', 'branch']);

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'user' => $user
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        
        $user->load(['roles', 'business', 'branch']);

        $roles = Role::all();

        
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'user' => $user,
            'roles' => $roles,
            'businesses' => $businesses,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'nullable|array',
            'status' => 'required|in:active,inactive',
            'business_id' => 'nullable|exists:businesses,id',
            'branch_id' => 'nullable|exists:branches,id',
        ];

        if ($request->filled('password')) {
            $rules['password'] = ['required', 'confirmed', Rules\Password::defaults()];
        }

        $validated = $request->validate($rules, __('validation.users'));

        
        if (!$request->filled('password')) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($request->password);
        }

        
        $roles = $validated['roles'] ?? null;
        unset($validated['roles']);

        $user->update($validated);

        
        if ($roles !== null) {
            $user->syncRoles($roles);
        }

        
        return redirect()->route('superadmin.users.index')
            ->with('flash.message', __('users.user_updated_successfully'))
            ->with('flash.type', 'success');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();

        return back()
            ->with('flash.message', __('users.user_deleted_successfully'))
            ->with('flash.type', 'success');
    }
}