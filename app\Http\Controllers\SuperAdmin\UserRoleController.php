<?php

declare(strict_types=1);

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;
use App\Models\User;

class UserRoleController extends Controller
{
    public function index()
    {
        $users = User::with('roles')->get();
        $roles = Role::all();

        return Inertia::render('SuperAdmin/UserRoles/Index', [
            'users' => $users,
            'roles' => $roles
        ]);
    }

    public function syncRoles(Request $request)
    {
        $validated = $request->validate([
            'user_id' => ['required', 'exists:users,id'],
            'roles' => ['required', 'array'],
            'roles.*' => ['exists:roles,id']
        ]);

        $user = User::findOrFail($validated['user_id']);
        $requestingUser = $request->user();

        if (
            !$requestingUser->hasRole('super-admin') &&
            in_array(Role::findByName('super-admin')->id, $validated['roles'])
        ) {
            return redirect()->back()->with('flash.error', 'You cannot assign the super-admin role');
        }

        if (
            $user->hasRole('super-admin') &&
            !in_array(Role::findByName('super-admin')->id, $validated['roles']) &&
            User::role('super-admin')->count() <= 1
        ) {
            return redirect()->back()->with('flash.error', 'Cannot remove the last super-admin user');
        }

        $user->syncRoles($validated['roles']);

        return redirect()->route('superadmin.user-roles.index')
            ->with('flash.success', 'User roles updated successfully');
    }
}