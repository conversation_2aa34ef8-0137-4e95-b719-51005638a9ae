<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class TestRolesController extends Controller
{
    public function index()
    {
        /** @var User $user */
        $user = Auth::user();
        if (!$user) {
            return 'Not logged in';
        }

        try {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getPermissionsViaRoles()->pluck('name'),
                'is_super_admin' => $user->hasRole('super-admin'),
                'is_admin' => $user->hasRole('admin'),
                'is_manager' => $user->hasRole('manager'),
                'is_user' => $user->hasRole('user'),
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage(),
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email
            ];
        }
    }
}