<?php

namespace App\Http\Controllers;

use App\Events\TestSocketConnection;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TestSocketController extends Controller
{
    /**
     * Show the socket test page
     */
    public function index()
    {
        return Inertia::render('SocketTest');
    }

    /**
     * Broadcast a test event
     */
    public function broadcast(Request $request)
    {
        $message = $request->input('message', 'Test message from server at ' . now());

        
        event(new TestSocketConnection($message));

        return response()->json([
            'success' => true,
            'message' => 'Event broadcasted successfully',
            'data' => [
                'message' => $message,
                'timestamp' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }
}