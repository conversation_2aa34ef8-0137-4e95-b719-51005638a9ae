<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Business;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Display a listing of users based on user role
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();

        if ($user->hasRole('super-admin')) {
            return $this->superAdminIndex($request);
        }

        if ($user->hasRole(['admin']) && $user->business_id) {
            return $this->businessIndex($request);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * SuperAdmin index view - shows all users
     */
    private function superAdminIndex(Request $request)
    {
        $filters = $request->only(['search', 'role', 'business', 'branch']);
        $users = User::withFilteredSearch($filters);
        $roles = Role::all();
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'users' => $users,
            'filters' => $filters,
            'roles' => $roles,
            'businesses' => $businesses
        ]);
    }

    /**
     * Business Admin index view - shows only users for the specific business
     */
    private function businessIndex(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            return redirect()->route('dashboard')
                ->with('flash.error', __('business.no_business'));
        }

        $search = $request->search;
        $status = $request->status;
        $specificRoleFilter = $request->roleFilter;
        $branchFilter = $request->branchFilter;
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');


        $query = User::where('business_id', $businessId);


        $query->whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin', 'manager', 'staff']);
        });


        if (!empty($specificRoleFilter)) {
            $query->whereHas('roles', function ($q) use ($specificRoleFilter) {
                $q->where('roles.id', $specificRoleFilter);
            });
        }

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.email', 'like', "%{$search}%")
                    ->orWhere('users.phone', 'like', "%{$search}%");
            });
        }

        if (!empty($status)) {
            $query->where('users.status', $status);
        }

        if (!empty($branchFilter)) {
            $query->where('users.branch_id', $branchFilter);
        }

        $query->orderBy("users.{$sortField}", $sortDirection);

        $users = $query->with(['branch', 'roles'])->paginate(10)->withQueryString();

        $users->getCollection()->transform(function ($user) {
            $userRoles = $user->roles->pluck('name')->toArray();
            $user->role_names = $userRoles;

            if ($user->branch) {
                $user->branch_name = $user->branch->name;
            } else {
                $user->branch_name = null;
            }

            return $user;
        });

        $roles = Role::whereIn('name', ['manager', 'staff'])
            ->get(['id', 'name']);

        $branches = Branch::where('business_id', $businessId)
            ->get(['id', 'name']);

        return Inertia::render('Business/Users/<USER>', [
            'users' => $users,
            'roles' => $roles,
            'branches' => $branches,
            'filters' => [
                'search' => $search ?? '',
                'status' => $status ?? '',
                'roleFilter' => $specificRoleFilter ?? '',
                'branchFilter' => $branchFilter ?? '',
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    /**
     * Show the form for creating a new user
     */
    public function create(Request $request)
    {
        $user = $request->user();

        if ($user->hasRole('super-admin')) {
            return $this->superAdminCreate($request);
        }

        if ($user->hasRole(['admin']) && $user->business_id) {
            return $this->businessCreate($request);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * SuperAdmin create form
     */
    private function superAdminCreate(Request $request)
    {
        $roles = Role::all();
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'roles' => $roles,
            'businesses' => $businesses,
        ]);
    }

    /**
     * Business Admin create form
     */
    private function businessCreate(Request $request)
    {
        $businessId = $request->user()->business_id;
        $roles = Role::whereIn('name', ['admin', 'manager', 'staff'])
            ->get(['id', 'name']);

        $branches = \App\Models\Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->get(['id', 'name']);

        return Inertia::render('Business/Users/<USER>', [
            'roles' => $roles,
            'branches' => $branches,
            'defaultValues' => [
                'role_ids' => [],
                'branch_id' => '',
                'status' => 'active',
                'has_verified_email' => false
            ]
        ]);
    }

    /**
     * Store a newly created user
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $wantsJson = $request->expectsJson() || $request->header('Accept') === 'application/json';

        try {
            if ($user->hasRole('super-admin')) {
                $result = $this->superAdminStore($request);
                if ($wantsJson) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Người dùng đã được tạo thành công',
                        'user' => $result instanceof User ? [
                            'id' => $result->id,
                            'name' => $result->name,
                            'email' => $result->email,
                            'roles' => $result->roles->pluck('name'),
                        ] : null
                    ], 201);
                }

                return $result;
            }

            if ($user->hasRole(['admin']) && $user->business_id) {
                $result = $this->businessStore($request);
                if ($wantsJson) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Người dùng đã được tạo thành công',
                        'user' => $result instanceof User ? [
                            'id' => $result->id,
                            'name' => $result->name,
                            'email' => $result->email,
                            'roles' => $result->roles->pluck('name'),
                        ] : null
                    ], 201);
                }

                return $result;
            }

            if ($wantsJson) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không có quyền tạo người dùng'
                ], 403);
            }

            return redirect()->route('dashboard')
                ->with('flash.error', __('permissions.not_authorized'));
        } catch (\Exception $e) {
            if ($wantsJson) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể tạo người dùng',
                    'error' => $e->getMessage()
                ], 200);
            }

            return redirect()->back()->withErrors([
                'error' => 'Không thể tạo người dùng: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Store user as SuperAdmin
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\App\Models\User
     */
    private function superAdminStore(Request $request)
    {

        $requestData = $request->all();
        if (isset($requestData['branch_id']) && $requestData['branch_id'] === 'null') {
            $requestData['branch_id'] = null;
        }
        if (isset($requestData['business_id']) && $requestData['business_id'] === 'null') {
            $requestData['business_id'] = null;
        }

        $validated = Validator::make($requestData, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => 'nullable|array',
            'status' => 'required|in:active,inactive',
            'business_id' => 'nullable|exists:businesses,id',
            'branch_id' => 'nullable|exists:branches,id',
        ], __('validation.users'))->validate();

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'status' => $validated['status'],
            'business_id' => $validated['business_id'] ?? null,
            'branch_id' => $validated['branch_id'] ?? null,
        ]);

        if (isset($validated['roles']) && count($validated['roles']) > 0) {
            $user->assignRole($validated['roles']);
        }

        if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
            return $user;
        }

        return redirect()->route('superadmin.users.index')
            ->with('flash.success', __('users.user_created_successfully'));
    }

    /**
     * Store user as Business Admin
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\App\Models\User
     */
    private function businessStore(Request $request)
    {
        $businessId = $request->user()->business_id;

        if (!$businessId) {
            if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
                throw new \Exception(__('business.no_business'));
            }

            return redirect()->back()->withErrors([
                'error' => __('business.no_business')
            ]);
        }


        $requestData = $request->all();
        if (isset($requestData['branch_id']) && $requestData['branch_id'] === 'null') {
            $requestData['branch_id'] = null;
        }

        $validated = Validator::make($requestData, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
            'branch_id' => 'nullable|exists:branches,id',
            'has_verified_email' => 'boolean',
        ], __('validation.users'))->validate();

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $validated['name'],
                'business_id' => $businessId,
                'branch_id' => $validated['branch_id'] ?? null,
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'phone' => $validated['phone'] ?? null,
                'status' => $validated['status'],
                'email_verified_at' => isset($validated['has_verified_email']) && $validated['has_verified_email'] ? now() : null,
            ]);


            if (!empty($request->role_ids)) {

                $primaryRole = $request->role_ids[0];


                if (is_numeric($primaryRole)) {

                    $role = Role::findById($primaryRole);
                } else {

                    $role = Role::findByName($primaryRole);
                }

                if ($role) {
                    $user->assignRole($role);
                }
            }

            DB::commit();

            if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
                return $user;
            }

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_created_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson() || $request->header('Accept') === 'application/json') {
                throw $e;
            }

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_create_user') . ': ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Show user details
     */
    public function show(Request $request, User $user)
    {
        $currentUser = $request->user();

        if ($currentUser->hasRole('super-admin')) {
            return $this->superAdminShow($user);
        }

        if ($currentUser->hasRole(['admin']) && $currentUser->business_id) {
            return $this->businessShow($request, $user);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * Show user details for SuperAdmin
     */
    private function superAdminShow(User $user)
    {
        $user->load(['roles.permissions', 'business', 'branch']);

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'user' => $user
        ]);
    }

    /**
     * Show user details for Business Admin
     */
    private function businessShow(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;


        if ($user->business_id != $businessId) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        $business = Business::find($businessId);


        $userRoles = $user->roles;


        $branch = $user->branch;

        return Inertia::render('Business/Users/<USER>', [
            'user' => $user,
            'userRoles' => $userRoles,
            'branch' => $branch,
            'business' => $business,
        ]);
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(Request $request, User $user)
    {
        $currentUser = $request->user();

        if ($currentUser->hasRole('super-admin')) {
            return $this->superAdminEdit($user);
        }

        if ($currentUser->hasRole(['admin']) && $currentUser->business_id) {
            return $this->businessEdit($request, $user);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * Edit form for SuperAdmin
     */
    private function superAdminEdit(User $user)
    {
        $user->load(['roles', 'business', 'branch']);
        $roles = Role::all();
        $businesses = Business::select('id', 'name')
            ->with([
                'branches' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ])
            ->orderBy('name')
            ->get();

        return Inertia::render('SuperAdmin/Users/<USER>', [
            'user' => $user,
            'roles' => $roles,
            'businesses' => $businesses,
        ]);
    }

    /**
     * Edit form for Business Admin
     */
    private function businessEdit(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;


        if ($user->business_id != $businessId) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        $roles = Role::whereIn('name', ['admin', 'manager', 'staff'])
            ->get(['id', 'name']);

        $branches = Branch::where('business_id', $businessId)
            ->where('status', 'active')
            ->get(['id', 'name']);


        $userRoles = [];
        foreach ($user->roles as $role) {
            $userRoles[] = [
                'role_id' => $role->id,
                'branch_id' => $user->branch_id
            ];
        }

        return Inertia::render('Business/Users/<USER>', [
            'user' => $user,
            'roles' => $roles,
            'branches' => $branches,
            'userRoles' => $userRoles
        ]);
    }

    /**
     * Update the specified user in storage
     */
    public function update(Request $request, User $user)
    {
        $currentUser = $request->user();

        if ($currentUser->hasRole('super-admin')) {
            return $this->superAdminUpdate($request, $user);
        }

        if ($currentUser->hasRole(['admin']) && $currentUser->business_id) {
            return $this->businessUpdate($request, $user);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * Update user as SuperAdmin
     */
    private function superAdminUpdate(Request $request, User $user)
    {
        if ($request->filled('password')) {
            $rules['password'] = ['required', 'confirmed', Rules\Password::defaults()];
        }


        $requestData = $request->all();
        if (isset($requestData['branch_id']) && $requestData['branch_id'] === 'null') {
            $requestData['branch_id'] = null;
        }
        if (isset($requestData['business_id']) && $requestData['business_id'] === 'null') {
            $requestData['business_id'] = null;
        }

        $validated = Validator::make($requestData, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'nullable|array',
            'status' => 'required|in:active,inactive',
            'business_id' => 'nullable|exists:businesses,id',
            'branch_id' => 'nullable|exists:branches,id',
            'has_verified_email' => 'sometimes|boolean',
        ], __('validation.users'))->validate();

        if (!$request->filled('password')) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($request->password);
        }

        $roles = $validated['roles'] ?? null;
        unset($validated['roles']);


        if (isset($validated['has_verified_email'])) {
            $user->email_verified_at = $validated['has_verified_email'] ? now() : null;
            unset($validated['has_verified_email']);
        }

        $user->update($validated);

        if ($roles !== null) {
            $user->syncRoles($roles);
        }

        return redirect()->route('superadmin.users.index')
            ->with('flash.success', __('users.user_updated_successfully'));
    }

    /**
     * Update user as Business Admin
     */
    private function businessUpdate(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;
        $currentUser = $request->user();


        if ($user->business_id != $businessId) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        $isAdmin = $currentUser->hasRole('admin');
        $isManager = $currentUser->hasRole('manager');


        $requestData = $request->all();
        if (isset($requestData['branch_id']) && $requestData['branch_id'] === 'null') {
            $requestData['branch_id'] = null;
        }

        $validated = Validator::make($requestData, [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'has_verified_email' => 'sometimes|boolean',
            'role_ids' => [
                Rule::requiredIf(function () use ($isAdmin, $isManager) {
                    return $isAdmin || $isManager;
                }),
                'array'
            ],
            'branch_id' => [
                'nullable',
                'exists:branches,id',
            ],
            'password' => 'nullable|string|min:8|confirmed',
        ])->validate();

        try {
            DB::beginTransaction();

            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $request->phone,
                'branch_id' => $request->has('branch_id') ? $validated['branch_id'] : $user->branch_id,
                'status' => $request->status,
            ];


            if ($request->has('branch_id') && ($request->branch_id === null || $request->branch_id === '' || $request->branch_id === 'null')) {
                $userData['branch_id'] = null;
            }

            if (!empty($validated['password'])) {
                $userData['password'] = Hash::make($validated['password']);
            }
            if ($request->has('has_verified_email')) {
                $user->email_verified_at = $request->boolean('has_verified_email') ? now() : null;
                unset($validated['has_verified_email']);
            }


            $user->update($userData);

            if (($isAdmin || $isManager) && !empty($validated['role_ids'])) {
                $roleId = $validated['role_ids'][0];
                $role = null;


                if (is_numeric($roleId)) {
                    $role = Role::findById($roleId);
                } else {

                    $role = Role::findByName($roleId);
                }

                if (!$role) {
                    throw new \Exception(__('business.invalid_role'));
                }


                if ($role->name === 'admin' && !$isAdmin) {
                    throw new \Exception(__('business.role_not_allowed_for_your_permission'));
                }

                if (($role->name === 'manager' || $role->name === 'admin') && !$isAdmin && !$isManager) {
                    throw new \Exception(__('business.role_not_allowed_for_your_permission'));
                }


                $user->syncRoles([$role->name]);
            }

            DB::commit();

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_updated_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_update_user') . ': ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Remove the specified user from storage
     */
    public function destroy(Request $request, User $user)
    {
        $currentUser = $request->user();

        if ($currentUser->hasRole('super-admin')) {
            return $this->superAdminDestroy($user);
        }

        if ($currentUser->hasRole(['admin']) && $currentUser->business_id) {
            return $this->businessDestroy($request, $user);
        }

        return redirect()->route('dashboard')
            ->with('flash.error', __('permissions.not_authorized'));
    }

    /**
     * Delete user as SuperAdmin
     */
    private function superAdminDestroy(User $user)
    {
        $user->delete();

        return back()
            ->with('flash.success', __('users.user_deleted_successfully'));
    }

    /**
     * Delete user as Business Admin
     */
    private function businessDestroy(Request $request, User $user)
    {
        $businessId = $request->user()->business_id;
        $currentUser = $request->user();


        if ($user->business_id != $businessId) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.permission_denied'));
        }

        if ($user->id === $currentUser->id) {
            return redirect()->route('business.users.index')
                ->with('flash.error', __('business.cannot_delete_own_account'));
        }

        try {
            DB::beginTransaction();


            $user->roles()->detach();


            $user->delete();

            DB::commit();

            return redirect()->route('business.users.index')
                ->with('flash.success', __('business.user_deleted_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors([
                'error' => __('business.failed_to_delete_user') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new user for a branch and return JSON response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createForBranch(Request $request)
    {

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'business_id' => 'required|exists:businesses,id',
            'role' => 'required|string',
        ]);

        try {
            DB::beginTransaction();


            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'business_id' => $request->business_id,
                'status' => 'active',
                'is_verified' => true,
            ]);


            $role = Role::where('name', $request->role)->first();
            if ($role) {
                $user->roles()->attach($role->id, ['business_id' => $request->business_id]);

            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Người dùng đã được tạo thành công',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'roles' => [$request->role],
                ]
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Không thể tạo người dùng',
                'error' => $e->getMessage()
            ], 200);
        }
    }

    /**
     * Get users by business ID with specific roles (manager and staff)
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsersByBusiness(Request $request)
    {
        try {
            $request->validate([
                'business_id' => 'required|exists:businesses,id',
            ]);

            $businessId = $request->business_id;

            $users = User::where('business_id', $businessId)
                ->whereHas('roles', function ($query) {
                    $query->whereIn('name', ['manager', 'staff']);
                })
                ->get();


            $users->map(function ($user) {
                $user->roles = $user->getRoleNames()->toArray();
                return $user;
            });

            return response()->json([
                'success' => true,
                'users' => $users
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting users by business: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Không thể lấy danh sách người dùng: ' . $e->getMessage()
            ], 500);
        }
    }
}
