<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;

class UserPermissionController extends Controller
{
    /**
     * Show the form for editing user permissions.
     *
     * @param  \App\Models\User  $user
     * @return \Inertia\Response
     */
    public function edit(User $user)
    {
        // Get all available permissions
        $allPermissions = Permission::all();

        // Get user's direct permissions (not from roles)
        $userDirectPermissions = $user->getDirectPermissions()->pluck('id')->toArray();

        // Get permissions from user's roles
        $rolePermissions = $user->getPermissionsViaRoles()->pluck('name', 'id')->toArray();

        return Inertia::render('SuperAdmin/UserPermissions/Edit', [
            'user' => $user,
            'allPermissions' => $allPermissions,
            'userDirectPermissions' => $userDirectPermissions,
            'rolePermissions' => $rolePermissions,
        ]);
    }

    /**
     * Update the user's permissions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'permissions' => 'array',
        ]);

        // Sync the direct permissions
        $user->syncPermissions($request->permissions);

        return redirect()->route('superadmin.users.show', $user->id)
            ->with('flash.success', __('users.permissions_updated_successfully'));
    }
}