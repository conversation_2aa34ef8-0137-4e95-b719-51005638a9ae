<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\CourtBooking;
use App\Models\Review;
use App\Services\BookingEventService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Booking;

class BookingController extends Controller
{
    /**
     * Display a listing of the user's bookings.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $customer = \App\Models\Customer::where('user_id', $user->id)->first();
        $currentTab = $request->input('tab', 'upcoming');

        // Return empty data if no customer found
        if (!$customer) {
            $statuses = [
                ['value' => 'all', 'label' => 'Tất cả trạng thái'],
                ['value' => 'pending', 'label' => 'Chờ xác nhận'],
                ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
                ['value' => 'completed', 'label' => 'Hoàn thành'],
                ['value' => 'cancelled', 'label' => 'Đã hủy'],
            ];

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'summary' => [
                        'total_bookings' => 0,
                        'total_amount' => 0,
                        'total_amount_formatted' => '0 ₫',
                        'upcoming_bookings' => 0,
                        'completed_bookings' => 0,
                        'cancelled_bookings' => 0,
                    ],
                    'statuses' => $statuses,
                ]);
            }

            return Inertia::render('User/Bookings', [
                'bookings' => new \Illuminate\Pagination\LengthAwarePaginator(
                    [],
                    0,
                    10,
                    1,
                    ['path' => $request->url(), 'query' => $request->query()]
                ),
                'filters' => [
                    'search' => null,
                    'from_date' => null,
                    'to_date' => null,
                    'status' => 'all',
                    'per_page' => 10
                ],
                'summary' => [
                    'total_bookings' => 0,
                    'total_amount' => 0,
                    'total_amount_formatted' => '0 ₫',
                    'upcoming_bookings' => 0,
                    'completed_bookings' => 0,
                    'cancelled_bookings' => 0,
                ],
                'statuses' => $statuses,
                'currentTab' => $currentTab,
            ]);
        }

        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $status = $request->input('status', 'all');
        $perPage = (int) $request->input('per_page', 10);
        $page = (int) $request->input('page', 1);

        // Build the query with eager loading to reduce N+1 problems
        $query = Booking::with([
            'courtBookings' => function ($q) {
                $q->orderBy('start_time');
            },
            'courtBookings.court',
            'courtBookings.court.branch',
            'courtBookings.payment'
        ])
            ->where('customer_id', $customer->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhereHas('courtBookings.court', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }
        if ($status !== 'all') {
            $query->where('status', $status);
        } else {
            switch ($currentTab) {
                case 'upcoming':

                    $query->whereIn('status', ['pending', 'confirmed'])
                        ->where('booking_date', '>=', Carbon::today());
                    break;
                case 'cancelled':
                    $query->where('status', 'cancelled');
                    break;
                case 'completed':
                    $query->where('status', 'completed');
                    break;
            }
        }

        $totalCount = $query->count();
        $bookings = $query->orderBy('booking_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        $formattedBookings = $bookings->map(function ($booking) {
            $courtBookings = $booking->courtBookings;
            $firstCourtBooking = $courtBookings->first();

            if (!$firstCourtBooking) {
                return null;
            }

            $formattedCourtBookings = $courtBookings->map(function ($courtBooking) {
                return [
                    'id' => $courtBooking->id,
                    'court_id' => $courtBooking->court_id,
                    'court_name' => $courtBooking->court ? $courtBooking->court->name : 'N/A',
                    'branch_name' => $courtBooking->court && $courtBooking->court->branch ? $courtBooking->court->branch->name : 'N/A',
                    'start_time' => $courtBooking->start_time ? $courtBooking->start_time->format('H:i') : null,
                    'end_time' => $courtBooking->end_time ? $courtBooking->end_time->format('H:i') : null,
                    'total_price' => (float) $courtBooking->total_price,
                    'status' => $courtBooking->status,
                ];
            });

            return [
                'id' => $booking->id,
                'reference_number' => $booking->reference_number,
                'booking_date' => $booking->booking_date ? $booking->booking_date->format('d/m/Y') : null,
                'formatted_date' => $booking->booking_date ? $booking->booking_date->format('d/m/Y') : null,
                'start_time' => $firstCourtBooking->start_time ? $firstCourtBooking->start_time->format('H:i') : null,
                'end_time' => $firstCourtBooking->end_time ? $firstCourtBooking->end_time->format('H:i') : null,
                'formatted_start_time' => $firstCourtBooking->start_time ? $firstCourtBooking->start_time->format('H:i') : null,
                'formatted_end_time' => $firstCourtBooking->end_time ? $firstCourtBooking->end_time->format('H:i') : null,
                'customer_name' => $booking->customer_name ?? null,
                'customer_contact' => $booking->customer_phone ?? null,
                'court' => $firstCourtBooking->court ?? null,
                'branch' => $firstCourtBooking->court && $firstCourtBooking->court->branch ? $firstCourtBooking->court->branch : null,
                'total_price' => $booking->total_price ? (float) $booking->total_price : 0,
                'combined_total_price' => $booking->total_price ? (float) $booking->total_price : 0,
                'status' => $booking->status ?? 'unknown',
                'created_at' => $booking->created_at ? $booking->created_at->format('d/m/Y H:i') : null,
                'booking_type' => $booking->booking_type ?? 'standard',
                'notes' => $booking->notes ?? null,
                'related_courts_count' => $courtBookings ? $courtBookings->count() : 0,
                'payment' => $firstCourtBooking->payment ?? null,
                'court_bookings' => $formattedCourtBookings,
            ];
        })
            ->filter()
            ->values();

        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $formattedBookings,
            $totalCount,
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        $totalAmount = $formattedBookings->sum('combined_total_price');

        $referenceNumbersCount = Booking::where('customer_id', $customer->id)
            ->distinct('reference_number')
            ->count('reference_number');

        $upcomingCount = Booking::where('customer_id', $customer->id)
            ->whereIn('status', ['pending', 'confirmed'])
            ->where('booking_date', '>=', Carbon::today())
            ->distinct('reference_number')
            ->count('reference_number');

        $completedCount = Booking::where('customer_id', $customer->id)
            ->where('status', 'completed')
            ->distinct('reference_number')
            ->count('reference_number');

        $cancelledCount = Booking::where('customer_id', $customer->id)
            ->where('status', 'cancelled')
            ->distinct('reference_number')
            ->count('reference_number');

        $summary = [
            'total_bookings' => $referenceNumbersCount,
            'total_amount' => $totalAmount,
            'total_amount_formatted' => number_format($totalAmount, 0, ',', '.') . ' ₫',
            'upcoming_bookings' => $upcomingCount,
            'completed_bookings' => $completedCount,
            'cancelled_bookings' => $cancelledCount,
        ];

        $statuses = [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xác nhận'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
        ];

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => $formattedBookings,
                'pagination' => [
                    'total' => $totalCount,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => ceil($totalCount / $perPage),
                ],
                'summary' => $summary,
                'statuses' => $statuses,
                'currentTab' => $currentTab,
            ]);
        }

        return Inertia::render('User/Bookings', [
            'bookings' => $paginator,
            'filters' => [
                'search' => $search,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'status' => $status,
                'per_page' => $perPage
            ],
            'summary' => $summary,
            'statuses' => $statuses,
            'currentTab' => $currentTab,
        ]);
    }

    /**
     * Display the specified booking.
     *
     * @param Request $request
     * @param string $referenceNumber
     * @return \Inertia\Response
     */
    public function show(Request $request, $referenceNumber)
    {
        $user = Auth::user();

        if (!$user) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }
            return redirect()->route('login');
        }


        $customer = \App\Models\Customer::where('id', $user->id)->first();

        if (!$customer) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer profile not found'
                ], 404);
            }
            abort(404, 'Customer profile not found');
        }

        $wasCancelled = \App\Models\Payment::checkAndCancelExpiredBookings($referenceNumber);

        if ($wasCancelled) {
            \Illuminate\Support\Facades\Log::info('Booking was cancelled due to payment deadline when viewing details', [
                'user_id' => $user->id,
                'reference_number' => $referenceNumber
            ]);
        }

        $bookings = \App\Models\CourtBooking::with(['court.branch', 'payment'])
            ->where('customer_id', $customer->id)
            ->where('reference_number', $referenceNumber)
            ->orderBy('court_id')
            ->get();

        $bookingEvent = BookingEventService::getFormattedTimeline($referenceNumber);


        if ($bookings->isEmpty()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }
            abort(404, 'Booking not found');
        }

        $mainBooking = $bookings->first();

        $review = Review::where('booking_reference', $referenceNumber)->first();

        $payments = \App\Models\Payment::where('booking_reference', $referenceNumber)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => (float) $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'status' => $payment->status,
                    'transaction_date' => $payment->transaction_date ? $payment->transaction_date->format('d/m/Y H:i') : null,
                    'payment_details' => $payment->payment_details,
                    'reference_number' => $payment->reference_number,
                ];
            });

        $totalPrice = (float) $bookings->sum(function ($booking) {
            return (float) $booking->total_price;
        });


        $formattedCourts = [];
        foreach ($bookings as $booking) {

            if (!$booking->court) {
                continue;
            }

            $formattedCourts[] = [
                'id' => $booking->id,
                'court_id' => $booking->court_id,
                'court_name' => $booking->court->name,
                'branch_name' => $booking->court->branch ? $booking->court->branch->name : 'N/A',
                'booking_date' => $booking->booking_date->format('d/m/Y'),
                'booking_time' => $booking->start_time->format('H:i') . ' - ' . $booking->end_time->format('H:i'),
                'status' => $booking->status,
                'status_display' => $this->getBookingStatusDisplay($booking->status),
                'status_class' => $this->getBookingStatusClass($booking->status),
                'total_price' => (float) $booking->total_price,
                'total_price_formatted' => number_format((float) $booking->total_price, 0, ',', '.') . ' ₫',
            ];
        }

        if ($mainBooking->court->branch) {
            $address = $mainBooking->court->branch->address;
            $ward = $mainBooking->court->branch->ward_name;
            $district = $mainBooking->court->branch->district_name;
            $province = $mainBooking->court->branch->province_name;

            $fullAddress = $address . ', ' . $ward . ', ' . $district . ', ' . $province;
        } else {
            $address = null;
            $fullAddress = null;
        }

        $paymentDeadline = $mainBooking->payment_deadline;
        $paymentDeadlineExpired = $paymentDeadline && $paymentDeadline->isPast();
        $paymentDeadlineFormatted = $paymentDeadline ? $paymentDeadline->format('d/m/Y H:i') : null;
        $totalPaid = $payments->where('status', 'completed')->sum('amount');
        $totalUnpaid = $totalPrice - $totalPaid;
        $paymentStatus = $totalUnpaid > 0 ? 'partial' : ($totalPaid > 0 ? 'paid' : 'unpaid');
        $bookingData = [
            'id' => $mainBooking->id,
            'reference_number' => $mainBooking->reference_number,
            'booking_date' => $mainBooking->booking_date->format('d/m/Y'),
            'formatted_date' => $mainBooking->booking_date->format('d/m/Y'),
            'start_time' => $mainBooking->start_time->format('H:i'),
            'end_time' => $mainBooking->end_time->format('H:i'),
            'formatted_start_time' => $mainBooking->start_time->format('H:i'),
            'formatted_end_time' => $mainBooking->end_time->format('H:i'),
            'customer_name' => $mainBooking->customer_name,
            'customer_contact' => $mainBooking->customer_phone ?? $mainBooking->customer_contact ?? '',
            'court' => $mainBooking->court,
            'branch' => $mainBooking->court->branch ?? null,
            'status' => $mainBooking->status,
            'notes' => $mainBooking->notes,
            'full_address' => $fullAddress,
            'total_price' => (float) $totalPrice,
            'total_price_formatted' => number_format($totalPrice, 0, ',', '.') . ' ₫',
            'created_at' => $mainBooking->created_at->format('d/m/Y H:i'),
            'booking_type' => $mainBooking->booking_type ?? 'standard',
            'payment' => $mainBooking->payment,
            'payments' => $payments,
            'payment_status' => $paymentStatus,
            'can_cancel' => in_array($mainBooking->status, ['pending', 'confirmed']) &&
                $mainBooking->booking_date->isAfter(Carbon::today()),
            'cancelled_at' => $mainBooking->cancelled_at ? $mainBooking->cancelled_at->format('d/m/Y H:i') : null,
            'cancelled_at_formatted' => $mainBooking->cancelled_at ? $mainBooking->cancelled_at->format('d/m/Y H:i') : null,
            'cancellation_reason' => $mainBooking->cancellation_reason,
            'number_of_players' => $mainBooking->number_of_players ?? 2,
            'courts' => $formattedCourts,
            'payment_deadline' => $paymentDeadlineFormatted,
            'payment_deadline_expired' => $paymentDeadlineExpired,
            'was_auto_cancelled' => $wasCancelled,
            'is_reviewed' => $mainBooking->is_reviewed,
            'review' => $review,
        ];
        $bookingData['booking_events'] = $bookingEvent;
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'booking' => $bookingData
            ]);
        }

        if ($wasCancelled) {
            return Inertia::render('User/BookingDetails', [
                'booking' => $bookingData,
                'flash' => [
                    'warning' => 'Đơn đặt sân đã bị hủy tự động do quá hạn thanh toán.'
                ]
            ]);
        }



        return Inertia::render('User/BookingDetails', [
            'booking' => $bookingData,
        ]);
    }

    /**
     * Cancel the specified booking.
     *
     * @param Request $request
     * @param string $referenceNumber
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $referenceNumber)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }


        $customer = \App\Models\Customer::where('id', $user->id)->first();

        if (!$customer) {
            return redirect()->back()->withErrors([
                'error' => 'Customer profile not found'
            ]);
        }

        $bookings = CourtBooking::where('customer_id', $customer->id)
            ->where('reference_number', $referenceNumber)
            ->get();

        if ($bookings->isEmpty()) {
            abort(404, 'Booking not found');
        }

        $invalidBookings = $bookings->filter(function ($booking) {
            return in_array($booking->status, ['completed', 'cancelled']);
        });

        if ($invalidBookings->isNotEmpty()) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot cancel bookings that are already completed or cancelled.'
            ]);
        }

        foreach ($bookings as $booking) {
            $booking->status = 'cancelled';
            $booking->cancellation_reason = $request->input('reason', 'Cancelled by user');
            $booking->cancelled_at = now();
            $booking->save();
        }

        return redirect()->route('user.bookings')
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Get booking status display text
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusDisplay($status)
    {
        switch ($status) {
            case 'pending':
                return 'Chờ xác nhận';
            case 'confirmed':
                return 'Đã xác nhận';
            case 'completed':
                return 'Hoàn thành';
            case 'cancelled':
                return 'Đã hủy';
            case 'no_show':
                return 'Không đến';
            default:
                return ucfirst($status);
        }
    }

    /**
     * Get booking status CSS class
     *
     * @param string $status
     * @return string
     */
    private function getBookingStatusClass($status)
    {
        switch ($status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'no_show':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Get all bookings for the authenticated user with payments.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserBookings(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
            ], 401);
        }

        $customer = \App\Models\Customer::where('id', $user->id)->first();

        if (!$customer) {
            return response()->json([
                'success' => true,
                'data' => [],
                'summary' => [
                    'total_bookings' => 0,
                    'total_amount' => 0,
                    'total_amount_formatted' => '0 ₫',
                    'upcoming_bookings' => 0,
                    'completed_bookings' => 0,
                    'cancelled_bookings' => 0,
                ],
            ]);
        }

        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $status = $request->input('status', 'all');
        $perPage = $request->input('per_page', 10);
        $page = (int) $request->input('page', 1);

        $query = Booking::with(['courtBookings', 'courtBookings.court', 'courtBookings.court.branch', 'courtBookings.payment'])
            ->where('customer_id', $customer->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhereHas('courtBookings.court', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $referenceNumbers = $query->pluck('reference_number')->unique()->values()->toArray();

        $cancelledReferences = [];
        foreach ($referenceNumbers as $refNumber) {
            $wasCancelled = \App\Models\Payment::checkAndCancelExpiredBookings($refNumber);
            if ($wasCancelled) {
                $cancelledReferences[] = $refNumber;
            }
        }

        if (!empty($cancelledReferences)) {
            \Illuminate\Support\Facades\Log::info('Cancelled expired bookings for user', [
                'user_id' => $user->id,
                'cancelled_references' => $cancelledReferences
            ]);
        }

        $bookingsQuery = Booking::with(['courtBookings.court', 'courtBookings.court.branch'])
            ->whereIn('reference_number', $referenceNumbers)
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc');

        $allBookings = $bookingsQuery->get();

        $paymentsByReference = \App\Models\Payment::whereIn('booking_reference', $referenceNumbers)
            ->get()
            ->groupBy('booking_reference');

        $groupedBookings = $allBookings->groupBy('reference_number')
            ->map(function ($group, $referenceNumber) use ($paymentsByReference) {
                $firstBooking = $group->first();

                $combinedTotalPrice = $group->sum(function ($booking) {
                    return (float) $booking->total_price;
                });

                $courts = $group->map(function ($booking) {
                    return [
                        'id' => $booking->courtBookings->first()->id,
                        'court_id' => $booking->courtBookings->first()->court_id,
                        'court_name' => $booking->courtBookings->first()->court->name ?? 'Unknown Court',
                        'start_time' => $booking->start_time->format('H:i'),
                        'end_time' => $booking->end_time->format('H:i'),
                        'total_price' => (float) $booking->total_price,
                        'status' => $booking->status,
                    ];
                })->values();

                $payments = $paymentsByReference->get($referenceNumber, collect())->map(function ($payment) {
                    return [
                        'id' => $payment->id,
                        'amount' => (float) $payment->amount,
                        'payment_method' => $payment->payment_method,
                        'status' => $payment->status,
                        'transaction_date' => $payment->transaction_date ? $payment->transaction_date->format('d/m/Y H:i') : null,
                        'payment_details' => $payment->payment_details,
                    ];
                })->values();
                $totalPaid = $payments->where('status', 'completed')->sum('amount');

                $totalUnpaid = $combinedTotalPrice - $totalPaid;

                if ($totalPaid > 0) {
                    if ($totalUnpaid > 0) {
                        $paymentStatus = 'partial';
                    } else {
                        $paymentStatus = 'paid';
                    }
                } else {
                    $paymentStatus = 'unpaid';
                }

                return [
                    'id' => $firstBooking->id,
                    'reference_number' => $referenceNumber,
                    'booking_date' => $firstBooking->booking_date->format('d/m/Y'),
                    'formatted_date' => $firstBooking->booking_date->format('d/m/Y'),
                    'start_time' => $firstBooking->start_time->format('H:i'),
                    'end_time' => $firstBooking->end_time->format('H:i'),
                    'customer_name' => $firstBooking->customer_name,
                    'customer_contact' => $firstBooking->customer_phone ?? $firstBooking->customer_contact ?? null,
                    'branch' => $firstBooking->courtBookings->first()->court->branch ?? null,
                    'total_price' => (float) $combinedTotalPrice,
                    'status' => $firstBooking->status,
                    'created_at' => $firstBooking->created_at->format('d/m/Y H:i'),
                    'booking_type' => $firstBooking->booking_type ?? 'standard',
                    'notes' => $firstBooking->notes,
                    'courts' => $courts,
                    'courts_count' => $courts->count(),
                    'payments' => $payments,
                    'payment_status' => $paymentStatus,
                ];
            })
            ->values();

        $total = $groupedBookings->count();
        $pageStart = ($page - 1) * $perPage;
        $pageItems = $groupedBookings->slice($pageStart, $perPage)->values();

        $totalAmount = $groupedBookings->sum('total_price');

        $summary = [
            'total_bookings' => $groupedBookings->count(),
            'total_amount' => $totalAmount,
            'total_amount_formatted' => number_format($totalAmount, 0, ',', '.') . ' ₫',
            'upcoming_bookings' => Booking::where('customer_id', $customer->id)
                ->whereIn('status', ['pending', 'confirmed'])
                ->where('booking_date', '>=', Carbon::today())
                ->count(),
            'completed_bookings' => $groupedBookings->where('status', 'completed')->count(),
            'cancelled_bookings' => $groupedBookings->where('status', 'cancelled')->count(),
        ];

        // Debug the upcoming bookings query in getUserBookings
        $upcomingBookingsQuery = Booking::where('customer_id', $customer->id)
            ->whereIn('status', ['pending', 'confirmed'])
            ->where('booking_date', '>=', Carbon::today());

        \Illuminate\Support\Facades\Log::info('getUserBookings upcoming bookings query', [
            'sql' => $upcomingBookingsQuery->toSql(),
            'bindings' => $upcomingBookingsQuery->getBindings(),
            'count' => $upcomingBookingsQuery->count()
        ]);

        return response()->json([
            'success' => true,
            'data' => $pageItems,
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
            ],
            'summary' => $summary,
        ]);
    }


}
