<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\CourtBooking;
use Carbon\Carbon;

class BookingHistoryController extends Controller
{
    public function show()
    {
        return Inertia::render('User/Booking', [
            'user' => Auth::user(),
        ]);
    }

    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $customer = \App\Models\Customer::where('user_id', $user->id)->first();

        if (!$customer) {
            $statuses = [
                ['value' => 'all', 'label' => 'Tất cả trạng thái'],
                ['value' => 'pending', 'label' => 'Chờ xác nhận'],
                ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
                ['value' => 'completed', 'label' => 'Hoàn thành'],
                ['value' => 'cancelled', 'label' => 'Đã hủy'],
            ];

            // Check if this is an API request
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'summary' => [
                        'total_bookings' => 0,
                        'total_amount' => 0,
                        'total_amount_formatted' => '0 ₫',
                        'upcoming_bookings' => 0,
                        'completed_bookings' => 0,
                        'cancelled_bookings' => 0,
                    ],
                    'statuses' => $statuses,
                ]);
            }

            return Inertia::render('User/BookingHistory', [
                'bookings' => new \Illuminate\Pagination\LengthAwarePaginator(
                    [],
                    0,
                    10,
                    1,
                    ['path' => $request->url(), 'query' => $request->query()]
                ),
                'filters' => [
                    'search' => null,
                    'from_date' => null,
                    'to_date' => null,
                    'status' => 'all',
                    'per_page' => 10
                ],
                'summary' => [
                    'total_bookings' => 0,
                    'total_amount' => 0,
                    'total_amount_formatted' => '0 ₫',
                    'upcoming_bookings' => 0,
                    'completed_bookings' => 0,
                    'cancelled_bookings' => 0,
                ],
                'statuses' => $statuses,
                'user' => $user
            ]);
        }

        $search = $request->input('search');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $status = $request->input('status', 'all');
        $perPage = $request->input('per_page', 10);

        $query = CourtBooking::with(['court', 'court.branch', 'payment'])
            ->where('customer_id', $customer->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhereHas('court', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('booking_date', [$fromDate, $toDate]);
        }

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $allBookings = $query->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->get()
            ->groupBy('reference_number')
            ->map(function ($group) {
                $firstBooking = $group->first();

                $combinedTotalPrice = $group->sum(function ($booking) {
                    return (float) $booking->total_price;
                });

                return [
                    'id' => $firstBooking->id,
                    'reference_number' => $firstBooking->reference_number,
                    'booking_date' => $firstBooking->booking_date->format('d/m/Y'),
                    'formatted_date' => $firstBooking->booking_date->format('d/m/Y'),
                    'start_time' => $firstBooking->start_time->format('H:i'),
                    'end_time' => $firstBooking->end_time->format('H:i'),
                    'formatted_start_time' => $firstBooking->start_time->format('H:i'),
                    'formatted_end_time' => $firstBooking->end_time->format('H:i'),
                    'customer_name' => $firstBooking->customer_name,
                    'customer_contact' => $firstBooking->customer_phone ?? $firstBooking->customer_contact ?? null,
                    'court' => $firstBooking->court,
                    'branch' => $firstBooking->court->branch ?? null,
                    'total_price' => (float) $firstBooking->total_price,
                    'combined_total_price' => (float) $combinedTotalPrice,
                    'status' => $firstBooking->status,
                    'created_at' => $firstBooking->created_at->format('d/m/Y H:i'),
                    'booking_type' => $firstBooking->booking_type ?? 'standard',
                    'notes' => $firstBooking->notes,
                    'related_courts_count' => $group->count(),
                    'payment' => $firstBooking->payment,
                ];
            })
            ->values();

        $total = $allBookings->count();
        $page = (int) $request->input('page', 1);
        $perPage = (int) $request->input('per_page', 10);
        $pageStart = ($page - 1) * $perPage;
        $pageItems = $allBookings->slice($pageStart, $perPage)->values();

        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $pageItems,
            $total,
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        $totalAmount = 0;
        foreach ($allBookings as $booking) {
            $totalAmount += (float) $booking['combined_total_price'];
        }

        $summary = [
            'total_bookings' => $allBookings->count(),
            'total_amount' => $totalAmount,
            'total_amount_formatted' => number_format($totalAmount, 0, ',', '.') . ' ₫',
            'upcoming_bookings' => $allBookings->filter(function ($booking) {
                return in_array($booking['status'], ['pending', 'confirmed']) &&
                    Carbon::createFromFormat('d/m/Y', $booking['booking_date'])->isAfter(Carbon::today());
            })->count(),
            'completed_bookings' => $allBookings->where('status', 'completed')->count(),
            'cancelled_bookings' => $allBookings->where('status', 'cancelled')->count(),
        ];

        $statuses = [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xác nhận'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
        ];

        // Check if this is an API request
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => $allBookings,
                'pagination' => [
                    'total' => $total,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => ceil($total / $perPage),
                ],
                'summary' => $summary,
                'statuses' => $statuses,
            ]);
        }

        return Inertia::render('User/BookingHistory', [
            'bookings' => $paginator,
            'filters' => [
                'search' => $search,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'status' => $status,
                'per_page' => $perPage
            ],
            'summary' => $summary,
            'statuses' => $statuses,
            'user' => $user
        ]);
    }
}