<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Likelist;

class FavoritesController extends Controller
{
    public function show()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $likelists = Likelist::with('branch')
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->get();
            
        return Inertia::render('User/Favorites', [
            'likelists' => $likelists,
            'user' => Auth::user(),
        ]);
    }
}