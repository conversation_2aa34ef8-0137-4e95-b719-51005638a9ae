<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Display the user's profile.
     *
     * @return \Inertia\Response
     */
    public function show()
    {
        $user = Auth::user();

        return Inertia::render('User/Profile', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar_url' => $user->avatar_url,
                'profile_photo_url' => $user->profile_photo_url,
                'provider' => $user->provider,
                'provider_avatar' => $user->provider_avatar,
            ]
        ]);
    }

    /**
     * Update the user's profile information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . Auth::id()],
            'phone' => ['nullable', 'string', 'max:20'],
        ]);


        $userId = Auth::id();
        User::where('id', $userId)->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
        ]);

        return redirect()->route('user.profile')
            ->with('flash.success', 'Thông tin tài khoản đã được cập nhật thành công.');
    }

    /**
     * Update the user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required', 'string', 'current_password'],
            'password' => ['required', 'string', Rules\Password::defaults(), 'confirmed'],
        ]);

        $userId = Auth::id();
        User::where('id', $userId)->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()->route('user.profile')
            ->with('flash.success', 'Mật khẩu đã được cập nhật thành công.');
    }

    /**
     * Update the user's avatar.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAvatar(Request $request)
    {
        // Check if we're updating with an image file or a URL
        if ($request->hasFile('avatar')) {
            $request->validate([
                'avatar' => ['required', 'image', 'max:2048'], // validate image, max 2MB
            ]);

            $user = Auth::user();

            // Delete old avatar if exists and it's a local file (not a social provider avatar)
            if (
                $user->avatar_url && !$user->provider_avatar &&
                Storage::disk('public')->exists(str_replace('/storage', '', $user->avatar_url))
            ) {
                Storage::disk('public')->delete(str_replace('/storage', '', $user->avatar_url));
            }

            // Store new image
            $path = $request->file('avatar')->store('avatars/' . $user->id, 'public');

            // Update user avatar_url
            User::where('id', $user->id)->update([
                'avatar_url' => '/storage/' . $path,
                'provider_avatar' => null, // Clear provider avatar when uploading custom one
            ]);

            // Get fresh user data with updated profile_photo_url
            $user = User::find($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Ảnh đại diện đã được cập nhật thành công.',
                'avatar_url' => $user->profile_photo_url
            ]);
        } elseif ($request->has('avatar_url')) {
            // Update from URL (e.g., for social providers)
            $request->validate([
                'avatar_url' => ['required', 'url'],
            ]);

            $user = Auth::user();

            User::where('id', $user->id)->update([
                'provider_avatar' => $request->avatar_url,
            ]);

            // Get fresh user data with updated profile_photo_url
            $user = User::find($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Ảnh đại diện đã được cập nhật thành công.',
                'avatar_url' => $user->profile_photo_url
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Vui lòng chọn ảnh đại diện.'
        ], 400);
    }

    /**
     * Use provider avatar as profile photo.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function useProviderAvatar(Request $request)
    {
        $user = Auth::user();

        if (!$user->provider_avatar) {
            return response()->json([
                'success' => false,
                'message' => 'Không có ảnh đại diện từ nhà cung cấp đăng nhập.'
            ], 400);
        }

        // Delete old custom avatar if exists
        if ($user->avatar_url && Storage::disk('public')->exists(str_replace('/storage', '', $user->avatar_url))) {
            Storage::disk('public')->delete(str_replace('/storage', '', $user->avatar_url));
        }

        // Set provider_avatar as the active avatar
        User::where('id', $user->id)->update([
            'avatar_url' => null,
        ]);

        // Get fresh user data with updated profile_photo_url
        $user = User::find($user->id);

        return response()->json([
            'success' => true,
            'message' => 'Đã sử dụng ảnh đại diện từ tài khoản xã hội.',
            'avatar_url' => $user->profile_photo_url
        ]);
    }
}
