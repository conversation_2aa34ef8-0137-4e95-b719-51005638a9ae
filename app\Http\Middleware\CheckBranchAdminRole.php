<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class CheckBranchAdminRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has any of the allowed roles using spatie/laravel-permission
        if ($user->hasAnyRole(['super-admin', 'admin', 'manager', 'staff'])) {
            // Super-admin and admin can access all branches
            if ($user->hasRole(['super-admin', 'admin'])) {
                return $next($request);
            }

            // For manager and staff, check if they have a branch assigned
            if ($user->branch_id) {
                return $next($request);
            }

            // No branch assigned
            return redirect()->route('unauthorized')->with([
                'unauthorized_role' => 'trang quản lý chi nhánh',
                'unauthorized_message' => 'Bạn cần phải được gán cho một chi nhánh để truy cập khu vực này.',
            ]);
        }

        // User doesn't have required roles
        return redirect()->route('unauthorized')->with([
            'unauthorized_role' => 'trang quản lý chi nhánh',
            'unauthorized_message' => 'Bạn cần phải có vai trò quản lý hoặc nhân viên chi nhánh để truy cập khu vực này.',
        ]);
    }
}