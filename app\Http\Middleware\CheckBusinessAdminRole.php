<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckBusinessAdminRole
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $roles = $user->roles ?? collect();
        $roleNames = $roles->pluck('name')->toArray();
        $businessId = $user->business_id;

        if (in_array('super-admin', $roleNames)) {
            return $next($request);
        }
        if (empty($businessId)) {
            session([
                'unauthorized_role' => 'trang quản lý business',
                'unauthorized_message' => 'Bạn cần phải được gán cho một doanh nghiệp để truy cập khu vực này.',
            ]);
            return redirect()->route('unauthorized');
        }
        return $next($request);
    }
}
