<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckStrictAdminRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm tra xem người dùng có vai trò admin hoặc super-admin không
        $user = $request->user();
        $roles = $user->roles ?? collect();
        $roleNames = isset($user->roles) ? $roles->pluck('name')->toArray() : [];

        if (!in_array('admin', $roleNames) && !in_array('super-admin', $roleNames)) {
            session([
                'unauthorized_role' => 'trang quản trị business',
                'unauthorized_message' => 'Bạn không có quyền truy cập vào khu vực quản trị business.',
            ]);
            return redirect()->route('unauthorized');
        }

        return $next($request);
    }
}