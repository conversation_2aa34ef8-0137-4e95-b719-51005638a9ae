<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSuperAdminRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $isSuperAdmin = $user->hasRoleInContext('super-admin');

        if (!$isSuperAdmin) {
            session([
                'unauthorized_role' => 'trang quản trị hệ thống',
                'unauthorized_message' => 'Chỉ tài khoản có quyền Super Admin mới có thể truy cập vào khu vực quản trị hệ thống.',
            ]);
            return redirect()->route('unauthorized');
        }

        return $next($request);
    }
}