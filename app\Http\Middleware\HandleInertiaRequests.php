<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;
use Closure;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'appName' => config('app.name'),
            'auth' => [
                'user' => $request->user() ? [
                    'id' => $request->user()->id,
                    'avatar_url' => $request->user()->avatar_url,
                    'name' => $request->user()->name,
                    'email' => $request->user()->email,
                    'phone' => $request->user()->phone,
                    'roles' => $request->user()->getRoleNames()->toArray(),
                    'business' => $request->user()->business,
                    'branch' => $request->user()->branch,
                    'permissions' => $request->user()->getAllPermissions()->pluck('name')->toArray(),
                ] : null,
            ],
            'csrf_token' => csrf_token(),
            'flash' => [
                'message' => fn() => $request->session()->get('flash.message'),
                'type' => fn() => $request->session()->get('flash.type'),
                'error' => fn() => $request->session()->get('flash.error'),
                'success' => fn() => $request->session()->get('flash.success'),
                'warning' => fn() => $request->session()->get('flash.warning'),
                'info' => fn() => $request->session()->get('flash.info'),
            ],
        ];
    }

    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request, Closure $next)
    {
        // Capture unauthorized headers if present and store in session
        if ($request->hasHeader('X-Unauthorized-Role')) {
            session(['unauthorized_role' => $request->header('X-Unauthorized-Role')]);
        }

        if ($request->hasHeader('X-Unauthorized-Message')) {
            session(['unauthorized_message' => $request->header('X-Unauthorized-Message')]);
        }

        return parent::handle($request, $next);
    }
}
