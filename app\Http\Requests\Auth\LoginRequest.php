<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
            'redirectAfterLogin' => ['nullable', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $startTime = microtime(true);

        $this->ensureIsNotRateLimited();
        $rateLimitTime = microtime(true);
        Log::info('Rate limit check time: ' . ($rateLimitTime - $startTime) . ' seconds');

        $userLookupStartTime = microtime(true);
        $user = \App\Models\User::where('email', $this->input('email'))->first();
        $userLookupEndTime = microtime(true);
        Log::info('User lookup time: ' . ($userLookupEndTime - $userLookupStartTime) . ' seconds');

        if (!$user) {
            RateLimiter::hit($this->throttleKey());
            throw ValidationException::withMessages([
                'email' => 'Tài khoản không tồn tại.',
            ]);
        }

        if ($user->status === 'inactive') {
            RateLimiter::hit($this->throttleKey());
            throw ValidationException::withMessages([
                'email' => 'Tài khoản đã bị khóa. Vui lòng liên hệ quản trị viên.',
            ]);
        }

        $authStartTime = microtime(true);
        if (!Auth::attempt($this->only('email', 'password'), $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            $authFailTime = microtime(true);
            Log::info('Auth attempt fail time: ' . ($authFailTime - $authStartTime) . ' seconds');

            throw ValidationException::withMessages([
                'password' => 'Mật khẩu không chính xác.',
            ]);
        }
        $authEndTime = microtime(true);
        Log::info('Auth attempt success time: ' . ($authEndTime - $authStartTime) . ' seconds');

        $clearRateStartTime = microtime(true);
        RateLimiter::clear($this->throttleKey());
        $clearRateEndTime = microtime(true);
        Log::info('Clear rate limit time: ' . ($clearRateEndTime - $clearRateStartTime) . ' seconds');

        Log::info('Total authenticate method time: ' . ($clearRateEndTime - $startTime) . ' seconds');
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->string('email')) . '|' . $this->ip());
    }
}
