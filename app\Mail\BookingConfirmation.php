<?php

namespace App\Mail;

use App\Models\Booking;
use App\Models\Branch;
use App\Models\Business;
use App\Models\CourtBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class BookingConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The booking instance.
     *
     * @var Booking
     */
    public $booking;

    /**
     * The court bookings collection.
     *
     * @var Collection
     */
    public $courtBookings;

    /**
     * The branch information.
     *
     * @var Branch
     */
    public $branch;

    /**
     * The business information.
     *
     * @var Business
     */
    public $business;

    /**
     * Create a new message instance.
     *
     * @param Booking $booking
     * @param Collection $courtBookings
     * @param Branch $branch
     * @param Business $business
     * @return void
     */
    public function __construct(Booking $booking, $courtBookings, Branch $branch, Business $business)
    {
        $this->booking = $booking;
        $this->courtBookings = $courtBookings;
        $this->branch = $branch;
        $this->business = $business;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Booking Confirmation - Reference: ' . $this->booking->reference_number,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.booking-confirmation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}