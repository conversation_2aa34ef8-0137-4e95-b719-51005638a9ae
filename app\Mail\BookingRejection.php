<?php

namespace App\Mail;

use App\Models\CourtBooking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BookingRejection extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The booking instance.
     *
     * @var CourtBooking
     */
    public $booking;

    /**
     * The reference number for related bookings.
     *
     * @var string
     */
    public $referenceNumber;

    /**
     * The total price of all related bookings.
     *
     * @var float
     */
    public $totalPrice;

    /**
     * The array of bookings created.
     *
     * @var array
     */
    public $bookings;

    /**
     * The reason for rejection.
     *
     * @var string
     */
    public $rejectionReason;

    /**
     * Create a new message instance.
     *
     * @param string $referenceNumber
     * @param float $totalPrice
     * @param array $bookings
     * @param CourtBooking|null $booking
     * @param string $rejectionReason
     * @return void
     */
    public function __construct(
        string $referenceNumber,
        float $totalPrice,
        array $bookings,
        ?CourtBooking $booking = null,
        string $rejectionReason = ''
    ) {
        $this->referenceNumber = $referenceNumber;
        $this->totalPrice = $totalPrice;
        $this->bookings = $bookings;
        $this->booking = $booking;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Thông báo hủy đặt sân - Mã đơn: ' . $this->referenceNumber,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.booking-rejection',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}