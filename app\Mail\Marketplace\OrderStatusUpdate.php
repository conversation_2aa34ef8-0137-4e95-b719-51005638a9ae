<?php

namespace App\Mail\Marketplace;

use App\Models\MarketOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdate extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The order instance.
     *
     * @var MarketOrder
     */
    public $order;

    /**
     * The old status.
     *
     * @var string|null
     */
    public $oldStatus;

    /**
     * The cancel reason.
     *
     * @var string|null
     */
    public $cancelReason;

    /**
     * Create a new message instance.
     *
     * @param MarketOrder $order
     * @param string|null $oldStatus
     * @param string|null $cancelReason
     * @return void
     */
    public function __construct(MarketOrder $order, ?string $oldStatus = null, ?string $cancelReason = null)
    {
        $this->order = $order;
        $this->oldStatus = $oldStatus;
        $this->cancelReason = $cancelReason;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        $statusText = $this->getStatusText($this->order->status);

        return new Envelope(
            subject: 'Cập nhật trạng thái đơn hàng #' . $this->order->order_number . ' - ' . $statusText,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.marketplace.order-status-update',
            with: [
                'order' => $this->order,
                'oldStatus' => $this->oldStatus,
                'cancelReason' => $this->cancelReason,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }

    /**
     * Get status text in Vietnamese.
     *
     * @param string $status
     * @return string
     */
    private function getStatusText(string $status): string
    {
        return match($status) {
            'pending' => 'Đang chờ xử lý',
            'confirmed' => 'Đã xác nhận',
            'shipped' => 'Đang giao hàng',
            'delivered' => 'Đã giao hàng',
            'cancelled' => 'Đã hủy',
            default => ucfirst($status)
        };
    }
}
