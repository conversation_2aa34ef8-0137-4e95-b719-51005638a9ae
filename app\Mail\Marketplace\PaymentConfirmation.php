<?php

namespace App\Mail\Marketplace;

use App\Models\MarketOrder;
use App\Models\MarketPayment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The order instance.
     *
     * @var MarketOrder
     */
    public $order;

    /**
     * The payment instance.
     *
     * @var MarketPayment
     */
    public $payment;

    /**
     * Create a new message instance.
     *
     * @param MarketOrder $order
     * @param MarketPayment $payment
     * @return void
     */
    public function __construct(MarketOrder $order, MarketPayment $payment)
    {
        $this->order = $order;
        $this->payment = $payment;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Xác nhận thanh toán đơn hàng #' . $this->order->order_number,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.marketplace.payment-confirmation',
            with: [
                'order' => $this->order,
                'payment' => $this->payment,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
