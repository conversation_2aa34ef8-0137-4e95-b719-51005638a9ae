<?php

namespace App\Mail\Marketplace;

use App\Models\MarketOrder;
use App\Models\MarketPayment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentStatusUpdate extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The order instance.
     *
     * @var MarketOrder
     */
    public $order;

    /**
     * The payment instance.
     *
     * @var MarketPayment
     */
    public $payment;

    /**
     * The old payment status.
     *
     * @var string|null
     */
    public $oldPaymentStatus;

    /**
     * The failure reason.
     *
     * @var string|null
     */
    public $failureReason;

    /**
     * The refund reason.
     *
     * @var string|null
     */
    public $refundReason;

    /**
     * Create a new message instance.
     *
     * @param MarketOrder $order
     * @param MarketPayment $payment
     * @param string|null $oldPaymentStatus
     * @param string|null $failureReason
     * @param string|null $refundReason
     * @return void
     */
    public function __construct(
        MarketOrder $order,
        MarketPayment $payment,
        ?string $oldPaymentStatus = null,
        ?string $failureReason = null,
        ?string $refundReason = null
    ) {
        $this->order = $order;
        $this->payment = $payment;
        $this->oldPaymentStatus = $oldPaymentStatus;
        $this->failureReason = $failureReason;
        $this->refundReason = $refundReason;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        $statusText = $this->getPaymentStatusText($this->payment->status);

        return new Envelope(
            subject: 'Cập nhật thanh toán đơn hàng #' . $this->order->order_number . ' - ' . $statusText,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.marketplace.payment-status-update',
            with: [
                'order' => $this->order,
                'payment' => $this->payment,
                'oldPaymentStatus' => $this->oldPaymentStatus,
                'failureReason' => $this->failureReason,
                'refundReason' => $this->refundReason,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }

    /**
     * Get payment status text in Vietnamese.
     *
     * @param string $status
     * @return string
     */
    private function getPaymentStatusText(string $status): string
    {
        return match($status) {
            'completed' => 'Đã thanh toán',
            'pending' => 'Đang xử lý',
            'failed' => 'Thất bại',
            'refunded' => 'Đã hoàn tiền',
            default => ucfirst($status)
        };
    }
}
