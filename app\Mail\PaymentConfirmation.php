<?php

namespace App\Mail;

use App\Models\CourtBooking;
use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The booking instance.
     *
     * @var CourtBooking|null
     */
    public $booking;

    /**
     * The payment instance.
     *
     * @var Payment|null
     */
    public $payment;

    /**
     * The reference number for related bookings.
     *
     * @var string
     */
    public $referenceNumber;

    /**
     * The total price of all related bookings.
     *
     * @var float
     */
    public $totalPrice;

    /**
     * The array of bookings created.
     *
     * @var array
     */
    public $bookings;

    /**
     * Create a new message instance.
     *
     * @param array $data
     * @return void
     */
    public function __construct(array $data)
    {
        $this->booking = $data['booking'] ?? null;
        $this->payment = $data['payment'] ?? null;
        $this->referenceNumber = $data['referenceNumber'] ?? '';
        $this->totalPrice = $data['totalPrice'] ?? 0;
        $this->bookings = $data['bookings'] ?? [];
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Xác nhận thanh toán thành công - Mã đơn: ' . $this->referenceNumber,
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.payment-confirmation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}