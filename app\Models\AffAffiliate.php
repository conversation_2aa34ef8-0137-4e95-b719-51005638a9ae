<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AffAffiliate extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aff_affiliates';

    protected $fillable = [
        'user_id',
        'referral_code',
        'status',
        'tier',
        'commission_rate',
        'total_earnings',
        'available_balance',
        'pending_balance',
        'total_clicks',
        'total_conversions',
        'conversion_rate',
        'payment_method',
        'payment_details',
        'social_profiles',
        'bio',
        'website_url',
        'approved_at',
        'approved_by',
        'rejection_reason',
        'rejected_at',
        'rejected_by',
        'last_activity_at',
        'metadata',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'conversion_rate' => 'decimal:2',
        'payment_details' => 'array',
        'social_profiles' => 'array',
        'metadata' => 'array',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the user that owns the affiliate.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who approved this affiliate.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who rejected this affiliate.
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    /**
     * Get all campaigns for this affiliate.
     */
    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(AffCampaign::class, 'aff_affiliate_campaigns', 'affiliate_id', 'campaign_id')
            ->withPivot(['status', 'custom_commission_rate', 'applied_at', 'approved_at', 'approved_by', 'rejection_reason', 'clicks', 'conversions', 'revenue', 'commissions', 'performance_metrics'])
            ->withTimestamps();
    }

    /**
     * Get all links for this affiliate.
     */
    public function links(): HasMany
    {
        return $this->hasMany(AffLink::class, 'affiliate_id');
    }

    /**
     * Get all clicks for this affiliate.
     */
    public function clicks(): HasMany
    {
        return $this->hasMany(AffClick::class, 'affiliate_id');
    }

    /**
     * Get all conversions for this affiliate.
     */
    public function conversions(): HasMany
    {
        return $this->hasMany(AffConversion::class, 'affiliate_id');
    }

    /**
     * Get all commissions for this affiliate.
     */
    public function commissions(): HasMany
    {
        return $this->hasMany(AffCommission::class, 'affiliate_id');
    }

    /**
     * Get all withdrawals for this affiliate.
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(AffWithdrawal::class, 'affiliate_id');
    }

    /**
     * Get KOL profile if exists.
     */
    public function kol(): HasMany
    {
        return $this->hasMany(AffKol::class, 'affiliate_id');
    }

    /**
     * Check if affiliate is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if affiliate is approved.
     */
    public function isApproved(): bool
    {
        return in_array($this->status, ['active', 'inactive']);
    }

    /**
     * Get tier color for UI.
     */
    public function getTierColorAttribute(): string
    {
        return match ($this->tier) {
            'bronze' => '#CD7F32',
            'silver' => '#C0C0C0',
            'gold' => '#FFD700',
            'platinum' => '#E5E4E2',
            default => '#6B7280'
        };
    }

    /**
     * Get status badge color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'green',
            'inactive' => 'gray',
            'pending' => 'yellow',
            'suspended' => 'red',
            default => 'gray'
        };
    }

    /**
     * Calculate conversion rate.
     */
    public function calculateConversionRate(): float
    {
        if ($this->total_clicks == 0) {
            return 0;
        }
        return round(($this->total_conversions / $this->total_clicks) * 100, 2);
    }

    /**
     * Update affiliate statistics.
     */
    public function updateStats(): void
    {
        $this->total_clicks = $this->clicks()->count();
        $this->total_conversions = $this->conversions()->where('status', 'approved')->count();
        $this->conversion_rate = $this->calculateConversionRate();
        $this->total_earnings = $this->commissions()->where('status', 'paid')->sum('amount');
        $this->available_balance = $this->commissions()->where('status', 'approved')->sum('amount');
        $this->pending_balance = $this->commissions()->where('status', 'pending')->sum('amount');
        $this->last_activity_at = now();
        $this->save();
    }
}
