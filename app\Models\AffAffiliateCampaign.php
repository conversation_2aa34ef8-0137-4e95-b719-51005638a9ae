<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffAffiliateCampaign extends Model
{
    use HasFactory;

    protected $table = 'aff_affiliate_campaigns';

    protected $fillable = [
        'affiliate_id',
        'campaign_id',
        'status',
        'custom_commission_rate',
        'applied_at',
        'approved_at',
        'approved_by',
        'rejection_reason',
        'clicks',
        'conversions',
        'revenue',
        'commissions',
        'performance_metrics',
    ];

    protected $casts = [
        'custom_commission_rate' => 'decimal:2',
        'revenue' => 'decimal:2',
        'commissions' => 'decimal:2',
        'performance_metrics' => 'array',
        'applied_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the affiliate that owns the campaign relationship.
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(AffAffiliate::class, 'affiliate_id');
    }

    /**
     * Get the campaign that owns the affiliate relationship.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(AffCampaign::class, 'campaign_id');
    }

    /**
     * Get the user who approved this relationship.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
}
