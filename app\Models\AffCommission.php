<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffCommission extends Model
{
    use HasFactory;

    protected $table = 'aff_commissions';

    protected $fillable = [
        'affiliate_id',
        'conversion_id',
        'amount',
        'rate',
        'status',
        'type',
        'due_date',
        'approved_at',
        'paid_at',
        'approved_by',
        'paid_by',
        'payment_reference',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'rate' => 'decimal:2',
        'due_date' => 'date',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the affiliate that owns this commission.
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(AffAffiliate::class, 'affiliate_id');
    }

    /**
     * Get the conversion that generated this commission.
     */
    public function conversion(): BelongsTo
    {
        return $this->belongsTo(AffConversion::class, 'conversion_id');
    }

    /**
     * Get the user who approved this commission.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who paid this commission.
     */
    public function payer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'paid_by');
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'paid' => 'green',
            'approved' => 'blue',
            'pending' => 'yellow',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get type color for UI.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'sale' => 'green',
            'lead' => 'blue',
            'signup' => 'purple',
            'recurring' => 'orange',
            'bonus' => 'pink',
            default => 'gray'
        };
    }

    /**
     * Check if commission is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if commission is approved.
     */
    public function isApproved(): bool
    {
        return in_array($this->status, ['approved', 'paid']);
    }

    /**
     * Check if commission is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date < now()->toDateString() && !$this->isPaid();
    }

    /**
     * Mark commission as paid.
     */
    public function markAsPaid(int $paidBy, string $paymentReference = null): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'paid_by' => $paidBy,
            'payment_reference' => $paymentReference,
        ]);

        // Update affiliate balance
        $affiliate = $this->affiliate;
        $affiliate->available_balance -= $this->amount;
        $affiliate->save();
    }

    /**
     * Cancel commission.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);

        // Update affiliate balance if it was approved
        if ($this->isApproved()) {
            $affiliate = $this->affiliate;
            $affiliate->available_balance -= $this->amount;
            $affiliate->pending_balance += $this->amount;
            $affiliate->save();
        }
    }

    /**
     * Scope for paid commissions.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for approved commissions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending commissions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for overdue commissions.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now()->toDateString())
                    ->whereNotIn('status', ['paid', 'cancelled']);
    }

    /**
     * Scope for commissions by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for commissions within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
