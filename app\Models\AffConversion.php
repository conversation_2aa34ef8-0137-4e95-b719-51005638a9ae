<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AffConversion extends Model
{
    use HasFactory;

    protected $table = 'aff_conversions';

    protected $fillable = [
        'affiliate_id',
        'campaign_id',
        'link_id',
        'click_id',
        'conversion_type',
        'order_id',
        'customer_id',
        'order_value',
        'commission_rate',
        'commission_amount',
        'status',
        'converted_at',
        'approved_at',
        'approved_by',
        'rejection_reason',
        'currency',
        'conversion_data',
        'metadata',
    ];

    protected $casts = [
        'order_value' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'converted_at' => 'datetime',
        'approved_at' => 'datetime',
        'conversion_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the affiliate that owns this conversion.
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(AffAffiliate::class, 'affiliate_id');
    }

    /**
     * Get the campaign this conversion belongs to.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(AffCampaign::class, 'campaign_id');
    }

    /**
     * Get the link this conversion belongs to.
     */
    public function link(): BelongsTo
    {
        return $this->belongsTo(AffLink::class, 'link_id');
    }

    /**
     * Get the click that led to this conversion.
     */
    public function click(): BelongsTo
    {
        return $this->belongsTo(AffClick::class, 'click_id');
    }

    /**
     * Get the user who approved this conversion.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the commission record for this conversion.
     */
    public function commission(): HasOne
    {
        return $this->hasOne(AffCommission::class, 'conversion_id');
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'approved' => 'green',
            'pending' => 'yellow',
            'rejected' => 'red',
            'cancelled' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get conversion type color for UI.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->conversion_type) {
            'sale' => 'green',
            'lead' => 'blue',
            'signup' => 'purple',
            'download' => 'orange',
            default => 'gray'
        };
    }

    /**
     * Check if conversion is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if conversion is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Approve this conversion.
     */
    public function approve(int $approvedBy): void
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy,
        ]);

        // Create commission record
        $this->commission()->create([
            'affiliate_id' => $this->affiliate_id,
            'amount' => $this->commission_amount,
            'rate' => $this->commission_rate,
            'status' => 'approved',
            'type' => $this->conversion_type,
            'approved_at' => now(),
            'approved_by' => $approvedBy,
        ]);

        // Update affiliate stats
        $this->affiliate->updateStats();
        
        // Update campaign stats
        if ($this->campaign) {
            $this->campaign->updateStats();
        }

        // Update link stats
        if ($this->link) {
            $this->link->updateStats();
        }
    }

    /**
     * Reject this conversion.
     */
    public function reject(string $reason, int $rejectedBy): void
    {
        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason,
            'approved_by' => $rejectedBy,
        ]);

        // Delete commission if exists
        $this->commission()?->delete();

        // Update stats
        $this->affiliate->updateStats();
        if ($this->campaign) {
            $this->campaign->updateStats();
        }
        if ($this->link) {
            $this->link->updateStats();
        }
    }

    /**
     * Cancel this conversion.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);

        // Update commission status
        $this->commission()?->update(['status' => 'cancelled']);

        // Update stats
        $this->affiliate->updateStats();
        if ($this->campaign) {
            $this->campaign->updateStats();
        }
        if ($this->link) {
            $this->link->updateStats();
        }
    }

    /**
     * Scope for approved conversions.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending conversions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for conversions within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('converted_at', [$startDate, $endDate]);
    }

    /**
     * Scope for conversions by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('conversion_type', $type);
    }
}
