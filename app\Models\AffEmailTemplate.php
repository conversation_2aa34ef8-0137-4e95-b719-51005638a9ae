<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AffEmailTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aff_email_templates';

    protected $fillable = [
        'name',
        'slug',
        'type',
        'subject',
        'body',
        'variables',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this template.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get templates by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Replace variables in template content.
     */
    public function replaceVariables($content, $data = [])
    {
        foreach ($data as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        return $content;
    }

    /**
     * Get rendered subject with variables replaced.
     */
    public function getRenderedSubject($data = [])
    {
        return $this->replaceVariables($this->subject, $data);
    }

    /**
     * Get rendered body with variables replaced.
     */
    public function getRenderedBody($data = [])
    {
        return $this->replaceVariables($this->body, $data);
    }
}
