<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AffKol extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aff_kols';

    protected $fillable = [
        'affiliate_id',
        'stage_name',
        'category',
        'niches',
        'followers_count',
        'engagement_rate',
        'social_stats',
        'base_rate',
        'preferred_content_type',
        'content_samples',
        'media_kit_url',
        'is_verified',
        'tier',
        'audience_demographics',
        'average_views',
        'average_likes',
        'average_comments',
        'average_shares',
        'verified_at',
        'verified_by',
        'metadata',
    ];

    protected $casts = [
        'niches' => 'array',
        'engagement_rate' => 'decimal:2',
        'social_stats' => 'array',
        'base_rate' => 'decimal:2',
        'content_samples' => 'array',
        'is_verified' => 'boolean',
        'audience_demographics' => 'array',
        'average_views' => 'decimal:0',
        'average_likes' => 'decimal:0',
        'average_comments' => 'decimal:0',
        'average_shares' => 'decimal:0',
        'verified_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the affiliate that owns this KOL profile.
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(AffAffiliate::class, 'affiliate_id');
    }

    /**
     * Get the user who verified this KOL.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get all contracts for this KOL.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(AffKolContract::class, 'kol_id');
    }

    /**
     * Get category color for UI.
     */
    public function getCategoryColorAttribute(): string
    {
        return match($this->category) {
            'influencer' => 'blue',
            'blogger' => 'green',
            'youtuber' => 'red',
            'tiktoker' => 'pink',
            'celebrity' => 'purple',
            'expert' => 'orange',
            default => 'gray'
        };
    }

    /**
     * Get tier color for UI.
     */
    public function getTierColorAttribute(): string
    {
        return match($this->tier) {
            'micro' => 'green',
            'macro' => 'blue',
            'mega' => 'purple',
            'celebrity' => 'gold',
            default => 'gray'
        };
    }

    /**
     * Check if KOL is verified.
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * Get follower tier based on count.
     */
    public function getFollowerTierAttribute(): string
    {
        if ($this->followers_count < 10000) {
            return 'nano';
        } elseif ($this->followers_count < 100000) {
            return 'micro';
        } elseif ($this->followers_count < 1000000) {
            return 'macro';
        } else {
            return 'mega';
        }
    }

    /**
     * Get engagement tier based on rate.
     */
    public function getEngagementTierAttribute(): string
    {
        if ($this->engagement_rate >= 6) {
            return 'excellent';
        } elseif ($this->engagement_rate >= 3) {
            return 'good';
        } elseif ($this->engagement_rate >= 1) {
            return 'average';
        } else {
            return 'low';
        }
    }

    /**
     * Format followers count for display.
     */
    public function getFormattedFollowersAttribute(): string
    {
        if ($this->followers_count >= 1000000) {
            return round($this->followers_count / 1000000, 1) . 'M';
        } elseif ($this->followers_count >= 1000) {
            return round($this->followers_count / 1000, 1) . 'K';
        }
        return number_format($this->followers_count);
    }

    /**
     * Calculate estimated reach.
     */
    public function getEstimatedReachAttribute(): int
    {
        // Estimated reach is typically 10-30% of followers
        return (int) ($this->followers_count * 0.2);
    }

    /**
     * Verify KOL profile.
     */
    public function verify(int $verifiedBy): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => $verifiedBy,
        ]);
    }

    /**
     * Unverify KOL profile.
     */
    public function unverify(): void
    {
        $this->update([
            'is_verified' => false,
            'verified_at' => null,
            'verified_by' => null,
        ]);
    }

    /**
     * Scope for verified KOLs.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for KOLs by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for KOLs by tier.
     */
    public function scopeByTier($query, string $tier)
    {
        return $query->where('tier', $tier);
    }

    /**
     * Scope for KOLs with minimum followers.
     */
    public function scopeMinFollowers($query, int $minFollowers)
    {
        return $query->where('followers_count', '>=', $minFollowers);
    }

    /**
     * Scope for KOLs with minimum engagement rate.
     */
    public function scopeMinEngagement($query, float $minEngagement)
    {
        return $query->where('engagement_rate', '>=', $minEngagement);
    }
}
