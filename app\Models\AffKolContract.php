<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AffKolContract extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aff_kol_contracts';

    protected $fillable = [
        'kol_id',
        'campaign_id',
        'contract_number',
        'title',
        'description',
        'type',
        'status',
        'total_value',
        'commission_rate',
        'fixed_fee',
        'required_posts',
        'completed_posts',
        'deliverables',
        'content_requirements',
        'start_date',
        'end_date',
        'deadline',
        'payment_schedule',
        'terms_conditions',
        'contract_file_url',
        'sent_at',
        'signed_at',
        'completed_at',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'total_value' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'fixed_fee' => 'decimal:2',
        'deliverables' => 'array',
        'content_requirements' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'deadline' => 'date',
        'payment_schedule' => 'array',
        'sent_at' => 'datetime',
        'signed_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the KOL that owns this contract.
     */
    public function kol(): BelongsTo
    {
        return $this->belongsTo(AffKol::class, 'kol_id');
    }

    /**
     * Get the campaign this contract belongs to.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(AffCampaign::class, 'campaign_id');
    }

    /**
     * Get the user who created this contract.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'green',
            'completed' => 'blue',
            'signed' => 'cyan',
            'sent' => 'yellow',
            'draft' => 'gray',
            'cancelled' => 'red',
            'expired' => 'orange',
            default => 'gray'
        };
    }

    /**
     * Get type color for UI.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'exclusive' => 'purple',
            'non_exclusive' => 'blue',
            'recurring' => 'green',
            'one_time' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Check if contract is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->start_date <= now()->toDateString() && 
               ($this->end_date === null || $this->end_date >= now()->toDateString());
    }

    /**
     * Check if contract is expired.
     */
    public function isExpired(): bool
    {
        return ($this->end_date && $this->end_date < now()->toDateString()) ||
               ($this->deadline && $this->deadline < now()->toDateString() && $this->status !== 'completed');
    }

    /**
     * Check if contract is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed' || $this->completed_posts >= $this->required_posts;
    }

    /**
     * Get completion percentage.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->required_posts == 0) {
            return 0;
        }
        return round(($this->completed_posts / $this->required_posts) * 100, 2);
    }

    /**
     * Get remaining posts.
     */
    public function getRemainingPostsAttribute(): int
    {
        return max(0, $this->required_posts - $this->completed_posts);
    }

    /**
     * Get days until deadline.
     */
    public function getDaysUntilDeadlineAttribute(): ?int
    {
        if (!$this->deadline) {
            return null;
        }
        return now()->diffInDays($this->deadline, false);
    }

    /**
     * Check if contract is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->deadline && $this->deadline < now()->toDateString() && !$this->isCompleted();
    }

    /**
     * Send contract to KOL.
     */
    public function send(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark contract as signed.
     */
    public function markAsSigned(): void
    {
        $this->update([
            'status' => 'signed',
            'signed_at' => now(),
        ]);
    }

    /**
     * Activate contract.
     */
    public function activate(): void
    {
        $this->update(['status' => 'active']);
    }

    /**
     * Complete contract.
     */
    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Cancel contract.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Add completed post.
     */
    public function addCompletedPost(): void
    {
        $this->increment('completed_posts');
        
        if ($this->completed_posts >= $this->required_posts) {
            $this->complete();
        }
    }

    /**
     * Scope for active contracts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for completed contracts.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for overdue contracts.
     */
    public function scopeOverdue($query)
    {
        return $query->where('deadline', '<', now()->toDateString())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope for contracts by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for contracts within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate]);
    }
}
