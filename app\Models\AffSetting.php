<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AffSetting extends Model
{
    use HasFactory;

    protected $table = 'aff_settings';

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'group',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get the parsed value based on type.
     */
    public function getParsedValueAttribute()
    {
        return match($this->type) {
            'json' => json_decode($this->value, true),
            'boolean' => (bool) $this->value,
            'number' => is_numeric($this->value) ? (float) $this->value : 0,
            'integer' => (int) $this->value,
            default => $this->value
        };
    }

    /**
     * Set value with automatic type detection.
     */
    public function setValue($value): void
    {
        if (is_array($value) || is_object($value)) {
            $this->value = json_encode($value);
            $this->type = 'json';
        } elseif (is_bool($value)) {
            $this->value = $value ? '1' : '0';
            $this->type = 'boolean';
        } elseif (is_numeric($value)) {
            $this->value = (string) $value;
            $this->type = is_int($value) ? 'integer' : 'number';
        } else {
            $this->value = (string) $value;
            $this->type = 'string';
        }
    }

    /**
     * Get setting by key.
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->parsed_value : $default;
    }

    /**
     * Set setting by key.
     */
    public static function set(string $key, $value, string $group = 'general', string $description = null): self
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'group' => $group,
                'description' => $description,
            ]
        );
        
        $setting->setValue($value);
        $setting->save();
        
        return $setting;
    }

    /**
     * Get all settings by group.
     */
    public static function getByGroup(string $group): array
    {
        return static::where('group', $group)
            ->get()
            ->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->parsed_value];
            })
            ->toArray();
    }

    /**
     * Get all public settings.
     */
    public static function getPublicSettings(): array
    {
        return static::where('is_public', true)
            ->get()
            ->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->parsed_value];
            })
            ->toArray();
    }

    /**
     * Scope for settings by group.
     */
    public function scopeByGroup($query, string $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Scope for public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for private settings.
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }
}
