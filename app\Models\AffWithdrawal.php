<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffWithdrawal extends Model
{
    use HasFactory;

    protected $table = 'aff_withdrawals';

    protected $fillable = [
        'affiliate_id',
        'amount',
        'fee',
        'net_amount',
        'status',
        'payment_method',
        'payment_details',
        'requested_at',
        'approved_at',
        'processed_at',
        'completed_at',
        'approved_by',
        'processed_by',
        'transaction_id',
        'rejection_reason',
        'processing_notes',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'payment_details' => 'array',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the affiliate that owns this withdrawal.
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(AffAffiliate::class, 'affiliate_id');
    }

    /**
     * Get the user who approved this withdrawal.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who processed this withdrawal.
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'completed' => 'green',
            'processing' => 'blue',
            'approved' => 'cyan',
            'pending' => 'yellow',
            'rejected' => 'red',
            'cancelled' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get payment method color for UI.
     */
    public function getPaymentMethodColorAttribute(): string
    {
        return match($this->payment_method) {
            'bank_transfer' => 'blue',
            'paypal' => 'indigo',
            'crypto' => 'purple',
            'momo' => 'pink',
            'zalopay' => 'green',
            default => 'gray'
        };
    }

    /**
     * Check if withdrawal is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if withdrawal is approved.
     */
    public function isApproved(): bool
    {
        return in_array($this->status, ['approved', 'processing', 'completed']);
    }

    /**
     * Check if withdrawal is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if withdrawal is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Approve withdrawal.
     */
    public function approve(int $approvedBy): void
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy,
        ]);
    }

    /**
     * Reject withdrawal.
     */
    public function reject(string $reason, int $rejectedBy): void
    {
        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason,
            'approved_by' => $rejectedBy,
        ]);

        // Return amount to affiliate's available balance
        $affiliate = $this->affiliate;
        $affiliate->available_balance += $this->amount;
        $affiliate->save();
    }

    /**
     * Mark as processing.
     */
    public function markAsProcessing(int $processedBy, string $transactionId = null): void
    {
        $this->update([
            'status' => 'processing',
            'processed_at' => now(),
            'processed_by' => $processedBy,
            'transaction_id' => $transactionId,
        ]);
    }

    /**
     * Mark as completed.
     */
    public function markAsCompleted(string $transactionId = null, string $notes = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'transaction_id' => $transactionId ?? $this->transaction_id,
            'processing_notes' => $notes,
        ]);
    }

    /**
     * Cancel withdrawal.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);

        // Return amount to affiliate's available balance if not yet processed
        if (!$this->isCompleted()) {
            $affiliate = $this->affiliate;
            $affiliate->available_balance += $this->amount;
            $affiliate->save();
        }
    }

    /**
     * Calculate processing time in hours.
     */
    public function getProcessingTimeAttribute(): ?float
    {
        if (!$this->completed_at || !$this->requested_at) {
            return null;
        }
        
        return $this->requested_at->diffInHours($this->completed_at);
    }

    /**
     * Scope for pending withdrawals.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved withdrawals.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for completed withdrawals.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for withdrawals by payment method.
     */
    public function scopeByPaymentMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope for withdrawals within date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('requested_at', [$startDate, $endDate]);
    }
}
