<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;

class Booking extends Model
{
    use HasFactory;

    /**
     * Payment status constants
     */
    const PAYMENT_STATUS_UNPAID = 'unpaid';
    const PAYMENT_STATUS_PAID = 'paid';
    const PAYMENT_STATUS_PARTIAL = 'partial';
    const PAYMENT_STATUS_CANCELLED = 'cancelled';
    const PAYMENT_STATUS_REFUNDED = 'refunded';

    protected $fillable = [
        'branch_id',
        'user_id',
        'customer_id',
        'reference_number',
        'customer_name',
        'customer_phone',
        'customer_email',
        'notes',
        'number_of_players',
        'status',
        'payment_status',
        'booking_type',
        'total_price',
        'payment_deadline',
        'cancelled_at',
        'cancellation_reason',
        'metadata',
        'booking_date'
    ];

    protected $casts = [
        'payment_deadline' => 'datetime',
        'cancelled_at' => 'datetime',
        'metadata' => 'array',
        'total_price' => 'decimal:2',
        'booking_date' => 'date'
    ];

    /**
     * Get all court bookings for this booking.
     */
    public function courtBookings(): HasMany
    {
        return $this->hasMany(CourtBooking::class);
    }

    /**
     * Get the branch that owns the booking.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the user that owns the booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the customer that owns the booking.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the booking events associated with this booking's reference number
     */
    public function bookingEvents(): HasMany
    {
        return $this->hasMany(BookingEvent::class, 'reference_number', 'reference_number');
    }

    /**
     * Get the timeline of events for this booking
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTimeline()
    {
        return BookingEvent::byReference($this->reference_number)
            ->orderBy('event_time')
            ->get();
    }

    /**
     * Log an event for this booking
     * 
     * @param string $eventType
     * @param array $eventData
     * @param int|null $userId
     * @param int|null $staffId
     * @param string|null $notes
     * @return \App\Models\BookingEvent
     */
    public function logEvent(string $eventType, array $eventData = [], ?int $userId = null, ?int $staffId = null, ?string $notes = null)
    {
        return BookingEvent::create([
            'reference_number' => $this->reference_number,
            'event_type' => $eventType,
            'event_data' => $eventData,
            'user_id' => $userId,
            'staff_id' => $staffId,
            'notes' => $notes,
            'event_time' => now(),
        ]);
    }

    /**
     * Generate a unique reference number for the booking
     */
    public static function generateReferenceNumber(string $bookingType = 'offline'): string
    {
        $prefix = $bookingType === 'online' ? 'ON' : 'OF';
        do {
            $randomNumber = mt_rand(10000000, 99999999);
            $referenceNumber = $prefix . $randomNumber;
            $exists = self::where('reference_number', $referenceNumber)->exists();
        } while ($exists);

        return $referenceNumber;
    }

    /**
     * Check if the booking is partially paid
     * 
     * @return bool
     */
    public function isPartiallyPaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_PARTIAL;
    }

    /**
     * Check if the booking is fully paid
     * 
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_PAID;
    }

    /**
     * Check if the booking is unpaid
     * 
     * @return bool
     */
    public function isUnpaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_UNPAID;
    }

    /**
     * Synchronize status and payment_status between Booking and its CourtBookings using reference number
     * 
     * @param string $referenceNumber The booking reference number
     * @param string|null $status Status to update (if null, won't update status)
     * @param string|null $paymentStatus Payment status to update (if null, won't update payment_status)
     * @param array|null $metadata Additional metadata to merge with existing metadata
     * @return void
     */
    public static function syncBookingStatusByReference($referenceNumber, $status = null, $paymentStatus = null, $metadata = null)
    {
        // Find main booking with this reference number
        $booking = self::where('reference_number', $referenceNumber)->first();

        // Update the main Booking record if exists
        if ($booking) {
            $updated = false;

            if ($status !== null) {
                $booking->status = $status;
                $updated = true;
            }

            if ($paymentStatus !== null) {
                $booking->payment_status = $paymentStatus;
                $updated = true;
            }

            if ($metadata !== null && is_array($metadata)) {
                $booking->metadata = array_merge($booking->metadata ?? [], $metadata);
                $updated = true;
            }

            if ($updated) {
                $booking->save();
            }
        }

        // Update all court bookings with this reference number
        $courtBookings = CourtBooking::where('reference_number', $referenceNumber)->get();

        foreach ($courtBookings as $courtBooking) {
            $courtUpdated = false;

            if ($status !== null) {
                $courtBooking->status = $status;
                $courtUpdated = true;
            }

            if ($paymentStatus !== null) {
                $courtBooking->payment_status = $paymentStatus;
                $courtUpdated = true;
            }

            if (
                $metadata !== null && is_array($metadata) &&
                Schema::hasColumn('court_bookings', 'metadata')
            ) {
                $courtBooking->metadata = array_merge($courtBooking->metadata ?? [], $metadata);
                $courtUpdated = true;
            }

            if ($courtUpdated) {
                $courtBooking->save();
            }
        }
    }

    /**
     * Synchronize booking_date from court_bookings to the main booking
     *
     * @param string $referenceNumber The booking reference number
     * @return bool True if synchronized, false otherwise
     */
    public static function syncBookingDate($referenceNumber)
    {
        // Find main booking with this reference number
        $booking = self::where('reference_number', $referenceNumber)->first();

        if (!$booking) {
            return false;
        }

        // Get earliest court booking date for this reference number
        $earliestCourtBooking = CourtBooking::where('reference_number', $referenceNumber)
            ->orderBy('booking_date', 'asc')
            ->first();

        if ($earliestCourtBooking) {
            $booking->booking_date = $earliestCourtBooking->booking_date;
            return $booking->save();
        }

        return false;
    }
}