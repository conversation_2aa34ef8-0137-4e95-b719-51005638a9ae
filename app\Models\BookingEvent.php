<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BookingEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference_number',
        'event_type',
        'event_data',
        'user_id',
        'staff_id',
        'notes',
        'event_time',
    ];

    protected $casts = [
        'event_data' => 'array',
        'event_time' => 'datetime',
    ];

    /**
     * Get the booking associated with this event
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, 'reference_number', 'reference_number');
    }

    /**
     * Get the court bookings associated with this event's reference number
     */
    public function courtBookings()
    {
        return $this->hasMany(CourtBooking::class, 'reference_number', 'reference_number');
    }

    /**
     * Get the user who performed this event
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the staff member who performed this event
     */
    public function staff()
    {
        return $this->belongsTo(User::class, 'staff_id');
    }

    /**
     * Scope to filter events by reference number
     */
    public function scopeByReference($query, $referenceNumber)
    {
        return $query->where('reference_number', $referenceNumber);
    }

    /**
     * Scope to filter events by event type
     */
    public function scopeOfType($query, $eventType)
    {
        if (is_array($eventType)) {
            return $query->whereIn('event_type', $eventType);
        }

        return $query->where('event_type', $eventType);
    }

    /**
     * Get all events for a booking reference in chronological order
     */
    public static function getBookingTimeline($referenceNumber)
    {
        return self::byReference($referenceNumber)
            ->orderBy('event_time')
            ->get();
    }
}