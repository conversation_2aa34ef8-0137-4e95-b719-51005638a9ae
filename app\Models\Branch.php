<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Branch extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'name',
        'description',
        'address',
        'province_id',
        'district_id',
        'ward_id',
        'province_name',
        'district_name',
        'ward_name',
        'latitude',
        'longitude',
        'contact_phone',
        'contact_email',
        'opening_hour',
        'closing_hour',
        'status',
        'main_image_url',
        'min_price',
        'max_price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'opening_hour' => 'datetime:H:i',
        'closing_hour' => 'datetime:H:i',
        'latitude' => 'float',
        'longitude' => 'float',
        'province_id' => 'integer',
        'district_id' => 'integer',
        'ward_id' => 'integer',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['formatted_main_image_url'];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['images'];

    /**
     * Get the business that owns the branch.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function dailyRevenues(): HasMany
    {
        return $this->hasMany(DailyRevenue::class);
    }

    public function hourlyRevenues(): HasMany
    {
        return $this->hasMany(HourlyRevenue::class);
    }

    public function monthlyStatistics(): HasMany
    {
        return $this->hasMany(MonthlyStatistic::class);
    }

    /**
     * Get the images for the branch.
     */
    public function images(): HasMany
    {
        return $this->hasMany(BranchImage::class);
    }

    /**
     * Get the main image for the branch.
     */
    public function mainImage()
    {
        return $this->images()->where('is_main', true)->first();
    }
    public function disableMainImage($imageId)
    {
        $this->images()->where('id', $imageId)->update(['is_main' => false]);
    }

    /**
     * Set main image for the branch.
     *
     * @param int $imageId
     * @return bool
     */
    public function setMainImage(int $imageId): bool
    {
        $this->images()->update(['is_main' => false]);
        $image = $this->images()->find($imageId);
        if ($image) {
            $image->is_main = true;
            if ($image->image_url) {
                $this->main_image_url = $image->image_url;
            }
            $this->save();
            return $image->save();
        }

        return false;
    }
    public function setMainImageById($imageId)
    {
        $this->images()->update(['is_main' => false]);
        $image = $this->images()->find($imageId);
        $image->is_main = true;
        return $image->save();
    }

    /**
     * Get the users for the branch.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function courts()
    {
        return $this->hasMany(Court::class);
    }

    public function prices()
    {
        return $this->hasMany(CourtPrice::class);
    }

    /**
     * Get all the court bookings for the branch.
     */
    public function courtBookings()
    {
        return $this->hasMany(CourtBooking::class);
    }


    /**
     * Get users assigned to this branch with specific roles, using the new relationship model.
     */
    public function getUsersByRole($roleId)
    {
        return User::whereHas('businessBranchRoles', function ($query) use ($roleId) {
            $query->where('branch_id', $this->id)
                ->where('role_id', $roleId);
        })->get();
    }

    /**
     * Get all staff users for this branch.
     */
    public function getAllStaff()
    {
        return User::whereHas('businessBranchRoles', function ($query) {
            $query->where('branch_id', $this->id);
        })->get();
    }

    /**
     * Get all staff users for a specific branch ID with their branch information and roles,
     * excluding users that only have the basic "user" role.
     * 
     * @param int $branchId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllStaffByBranch($branchId)
    {
        // Get users with roles other than just "user"
        return User::where('branch_id', $branchId)
            ->whereHas('roles', function ($query) {
                $query->where('name', '!=', 'user');
            })
            ->with([
                'branch:id,name,business_id',
                'roles:id,name',
                'branch.business:id,name'
            ])
            ->get();
    }

    /**
     * Get the reviews for the branch.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get only approved reviews for the branch.
     */
    public function approvedReviews()
    {
        return $this->reviews()->where('status', 'approved');
    }

    /**
     * Calculate the average rating for this branch.
     *
     * @return float|null
     */
    public function getAverageRating()
    {
        $approvedReviews = $this->approvedReviews;

        if ($approvedReviews->count() === 0) {
            return null;
        }

        return round($approvedReviews->avg('rating'), 1);
    }

    /**
     * Get the number of reviews for this branch.
     *
     * @return int
     */
    public function getReviewCount()
    {
        return $this->approvedReviews()->count();
    }

    /**
     * Get the amenities for the branch.
     */
    public function amenities()
    {
        return $this->belongsToMany(Amenity::class, 'branch_amenities')
            ->withPivot('notes')
            ->withTimestamps();
    }

    /**
     * Get the branch services for this branch (intermediate table between branches and services).
     */
    public function branchServices()
    {
        return $this->hasMany(BranchService::class);
    }

    /**
     * Get all court services available for this branch.
     */
    public function courtServices()
    {
        return $this->belongsToMany(CourtService::class, 'branch_services')
            ->withPivot('price', 'member_price', 'is_active', 'description', 'settings')
            ->withTimestamps();
    }

    /**
     * Get only active court services for this branch.
     */
    public function activeCourtServices()
    {
        return $this->belongsToMany(CourtService::class, 'branch_services')
            ->withPivot('price', 'member_price', 'is_active', 'description', 'settings')
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Update min and max prices for this branch based on active court prices.
     *
     * @return bool
     */
    public function updatePriceRange(): bool
    {
        $prices = $this->prices()
            ->where('status', 'active')
            ->where('is_active', true)
            ->where('price_type', 'normal')
            ->pluck('price_per_hour')
            ->toArray();

        if (!empty($prices)) {
            $this->min_price = min($prices);
            $this->max_price = max($prices);
            return $this->save();
        }

        return false;
    }

    /**
     * Get the formatted main image URL attribute.
     *
     * @return string
     */
    public function getFormattedMainImageUrlAttribute()
    {
        if (!$this->main_image_url) {
            $mainImage = $this->mainImage();
            if ($mainImage && $mainImage->image_url) {
                return $mainImage->image_url;
            }
            // Fallback to a placeholder image
            return '/images/branch-placeholder.jpg';
        }

        // Ensure the main_image_url is properly formatted with /storage/ prefix
        if (strpos($this->main_image_url, '/storage/') === 0) {
            return $this->main_image_url;
        }

        return '/storage/' . ltrim($this->main_image_url, '/');
    }

    /**
     * Get the court service assignments for this branch.
     */
    public function courtServiceAssigns()
    {
        return $this->hasMany(CourtServiceAssign::class);
    }

    /**
     * Get court services assigned to this branch.
     */
    public function assignedCourtServices()
    {
        return $this->belongsToMany(CourtService::class, 'court_service_assigns')
            ->withPivot(
                'id',
                'business_id',
                'status',
                'price',
                'unit',
                'discount_type',
                'discount_person',
                'discount_amount',
                'settings'
            )
            ->withTimestamps();
    }
}