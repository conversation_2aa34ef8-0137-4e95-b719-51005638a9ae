<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BranchService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'court_service_id',
        'price',
        'member_price',
        'is_active',
        'description',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'member_price' => 'decimal:2',
        'is_active' => 'boolean',
        'settings' => 'json',
    ];

    /**
     * Get the branch that the service belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the court service that is associated with this branch.
     */
    public function courtService(): BelongsTo
    {
        return $this->belongsTo(CourtService::class);
    }

    /**
     * Get the effective price for the service.
     * Returns the branch-specific price if set, otherwise defaults to the service price.
     * 
     * @return float
     */
    public function getEffectivePrice(): float
    {
        return $this->price ?? $this->courtService->price;
    }

    /**
     * Get the effective member price for the service.
     * Returns the branch-specific member price if set, otherwise defaults to the regular price.
     * 
     * @return float
     */
    public function getEffectiveMemberPrice(): float
    {
        return $this->member_price ?? $this->getEffectivePrice();
    }

    /**
     * Get the effective description for the service.
     * Returns the branch-specific description if set, otherwise defaults to the service description.
     * 
     * @return string
     */
    public function getEffectiveDescription(): string
    {
        return $this->description ?? $this->courtService->description;
    }
}
