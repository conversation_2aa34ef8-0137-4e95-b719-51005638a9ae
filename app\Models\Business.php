<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class Business extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'logo_url',
        'banner_url',
        'contact_email',
        'contact_phone',
        'address',
        'province_id',
        'district_id',
        'ward_id',
        'province',
        'district',
        'ward',
        'complete_address',
        'tax_code',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'string',
        'province_id' => 'integer',
        'district_id' => 'integer',
        'ward_id' => 'integer',
    ];

    /**
     * Get the settings for the business.
     */
    public function settings(): HasMany
    {
        return $this->hasMany(BusinessSetting::class);
    }

    /**
     * Get a specific setting value.
     *
     * @param string $key The setting key
     * @param mixed $default The default value to return if setting not found
     * 
     * @return mixed
     */
    public function getSetting(string $key, $default = null)
    {
        $setting = $this->settings()->where('setting_key', $key)->first();

        if (!$setting) {
            return $default;
        }

        return $setting->setting_value;
    }

    /**
     * Set a specific setting value.
     *
     * @param string $key The setting key
     * @param mixed $value The setting value
     * 
     * @return BusinessSetting
     */
    public function setSetting(string $key, $value)
    {
        return $this->settings()->updateOrCreate(
            ['setting_key' => $key],
            ['setting_value' => $value]
        );
    }

    /**
     * Get the branches for the business.
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get the users for the business.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the tournaments for the business.
     */
    public function tournaments(): HasMany
    {
        return $this->hasMany(Tournament::class);
    }


    public function dailyRevenues(): HasMany
    {
        return $this->hasMany(DailyRevenue::class);
    }

    public function hourlyRevenues(): HasMany
    {
        return $this->hasMany(HourlyRevenue::class);
    }

    public function monthlyStatistics(): HasMany
    {
        return $this->hasMany(MonthlyStatistic::class);
    }

    /**
     * Get users with specific roles for this business.
     *
     * @param array|string $roleNames Array of role names or single role name
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUsersWithRoles($roleNames = ['manager', 'staff'])
    {
        // Convert single role name to array
        if (!is_array($roleNames)) {
            $roleNames = [$roleNames];
        }

        // Get the role IDs for the specified role names
        $roleIds = \App\Models\Role::whereIn('name', $roleNames)->pluck('id');

        // Get users with these roles for this business
        return \App\Models\User::whereHas('roles', function ($query) use ($roleIds) {
            $query->whereIn('id', $roleIds)
                ->whereHas('users', function ($q) {
                    $q->where('business_id', $this->id);
                });
        })->orWhereExists(function ($query) use ($roleIds) {
            $query->select(DB::raw(1))
                ->from('model_has_roles')
                ->whereColumn('model_has_roles.model_id', 'users.id')
                ->where('model_has_roles.model_type', 'App\\Models\\User')
                ->whereIn('model_has_roles.role_id', $roleIds)
                ->where('model_has_roles.business_id', $this->id);
        })->get(['id', 'name', 'email', 'phone', 'status']);
    }

    /**
     * Get manager users for this business.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function managers()
    {
        return $this->getUsersWithRoles('manager');
    }

    /**
     * Get staff users for this business.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function staff()
    {
        return $this->getUsersWithRoles('staff');
    }

    /**
     * Get all staff users (including managers) for this business.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function allStaff()
    {
        return $this->getUsersWithRoles(['manager', 'staff']);
    }


    /**
     * Get users assigned to this business with specific roles, using the new relationship model.
     */
    public function getUsersByRole($roleId)
    {
        return User::whereHas('businessBranchRoles', function ($query) use ($roleId) {
            $query->where('business_id', $this->id)
                ->where('role_id', $roleId)
                ->whereNull('branch_id');
        })->get();
    }

    /**
     * Get the full URL for the logo if it exists
     * 
     * @return string|null
     */
    public function getLogoUrlAttribute($value)
    {
        if (empty($value)) {
            return null;
        }

        // If the URL already starts with http:// or https://, return it as is
        if (str_starts_with($value, 'http://') || str_starts_with($value, 'https://')) {
            return $value;
        }

        // If the URL starts with /storage, add the app URL
        if (str_starts_with($value, '/storage')) {
            return asset($value);
        }

        // Otherwise, assume it's a relative path in storage
        return asset('storage/' . $value);
    }

    /**
     * Get the full URL for the banner if it exists
     * 
     * @return string|null
     */
    public function getBannerUrlAttribute($value)
    {
        if (empty($value)) {
            return null;
        }

        // If the URL already starts with http:// or https://, return it as is
        if (str_starts_with($value, 'http://') || str_starts_with($value, 'https://')) {
            return $value;
        }

        // If the URL starts with /storage, add the app URL
        if (str_starts_with($value, '/storage')) {
            return asset($value);
        }

        // Otherwise, assume it's a relative path in storage
        return asset('storage/' . $value);
    }

    /**
     * Get the court service assignments for this business.
     */
    public function courtServiceAssigns()
    {
        return $this->hasMany(CourtServiceAssign::class);
    }

    /**
     * Get court services assigned to this business.
     */
    public function assignedCourtServices()
    {
        // Get the ids of court services assigned to this business
        $serviceIds = $this->courtServiceAssigns()
            ->select('court_service_id')
            ->distinct()
            ->pluck('court_service_id');

        // Return the court services with their assignments for this business
        return CourtService::whereIn('id', $serviceIds)
            ->with([
                'serviceAssigns' => function ($query) {
                    $query->where('business_id', $this->id);
                }
            ]);
    }
}