<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

class BusinessSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'setting_key',
        'setting_value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'setting_value' => 'json',
    ];

    /**
     * Get the business that owns the setting.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get a business setting value by its business_id and setting_key
     *
     * @param int $businessId The business ID
     * @param string $key The setting key to retrieve
     * @param mixed $default The default value to return if setting not found
     * @return mixed The setting value or default value if not found
     */
    public static function get(int $businessId, string $key, $default = null)
    {
        $setting = static::where('business_id', $businessId)
            ->where('setting_key', $key)
            ->first();

        if (!$setting) {
            return $default;
        }

        return $setting->setting_value;
    }

    /**
     * Get VNPay payment settings for a business
     *
     * @param int $businessId The business ID
     * @return array The VNPay settings
     */
    public static function getVnPaySettings(int $businessId)
    {
        $settings = static::get($businessId, 'vnpay_payment', []);

        if (is_string($settings) && !is_array($settings)) {
            try {
                $decrypted = Crypt::decrypt($settings);
                $decodedSettings = json_decode($decrypted, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            } catch (\Exception $e) {
                $decodedSettings = json_decode($settings, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            }
        }

        $defaults = [
            'provider' => 'vnpay',
            'vnp_TmnCode' => '',
            'vnp_HashSecret' => '',
            'vnp_Url' => 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
            'vnp_Version' => '2.1.1',
            'vnp_ReturnUrl' => '',
            'active' => true
        ];

        return array_merge($defaults, $settings ?? []);
    }

    /**
     * Set VNPay payment settings for a business with encryption
     *
     * @param int $businessId The business ID
     * @param array $settings The VNPay settings
     * @return BusinessSetting The updated or created setting
     */
    public static function setVnPaySettings(int $businessId, array $settings)
    {
        $encryptedValue = Crypt::encrypt(json_encode($settings));

        return static::updateOrCreate(
            [
                'business_id' => $businessId,
                'setting_key' => 'vnpay_payment'
            ],
            [
                'setting_value' => $encryptedValue
            ]
        );
    }

    /**
     * Get payment method settings for a specific payment type
     *
     * @param int $businessId The business ID
     * @param string $paymentType The payment type (e.g., 'vnpay', 'momo')
     * @return array The payment method settings
     */
    public static function getPaymentMethodKey(int $businessId, string $paymentType)
    {
        $settings = null;

        switch (strtolower($paymentType)) {
            case 'vnpay':
                return self::getVnPaySettings($businessId);

            case 'momo':
                return self::getMomoSettings($businessId);

            default:
                $settings = static::get($businessId, $paymentType . '_payment', []);
                if (is_string($settings) && !is_array($settings)) {
                    try {
                        $decrypted = Crypt::decrypt($settings);
                        $decodedSettings = json_decode($decrypted, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $settings = $decodedSettings;
                        }
                    } catch (\Exception $e) {
                        $decodedSettings = json_decode($settings, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $settings = $decodedSettings;
                        }
                    }
                }

                return $settings ?? [];
        }
    }

    /**
     * Get Momo payment settings for a business
     *
     * @param int $businessId The business ID
     * @return array The Momo settings
     */
    public static function getMomoSettings(int $businessId)
    {
        $settings = static::get($businessId, 'momo_payment', []);
        if (is_string($settings) && !is_array($settings)) {
            try {
                $decrypted = Crypt::decrypt($settings);
                $decodedSettings = json_decode($decrypted, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            } catch (\Exception $e) {
                $decodedSettings = json_decode($settings, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            }
        }

        $defaults = [
            'provider' => 'momo',
            'partnerCode' => '',
            'accessKey' => '',
            'secretKey' => '',
            'endPoint' => 'https://test-payment.momo.vn/v2/gateway/api/create',
            'active' => true
        ];

        return array_merge($defaults, $settings ?? []);
    }

    /**
     * Set Momo payment settings for a business with encryption
     *
     * @param int $businessId The business ID
     * @param array $settings The Momo settings
     * @return BusinessSetting The updated or created setting
     */
    public static function setMomoSettings(int $businessId, array $settings)
    {
        $encryptedValue = Crypt::encrypt(json_encode($settings));

        return static::updateOrCreate(
            [
                'business_id' => $businessId,
                'setting_key' => 'momo_payment'
            ],
            [
                'setting_value' => $encryptedValue
            ]
        );
    }

    /**
     * Get payment settings for any payment provider
     *
     * @param int $businessId The business ID
     * @param string $provider The payment provider (e.g., 'vnpay', 'momo', 'paypal')
     * @param array $defaults Default values to use if settings not found
     * @return array The payment settings
     */
    public static function getPaymentSettings(int $businessId, string $provider, array $defaults = [])
    {
        $settingKey = strtolower($provider) . '_payment';
        $settings = static::get($businessId, $settingKey, []);

        if (is_string($settings) && !is_array($settings)) {
            try {
                $decrypted = Crypt::decrypt($settings);
                $decodedSettings = json_decode($decrypted, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            } catch (\Exception $e) {
                $decodedSettings = json_decode($settings, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $settings = $decodedSettings;
                }
            }
        }

        // Ensure we have an array
        if (!is_array($settings)) {
            $settings = [];
        }

        // Add provider key if not present
        if (!isset($settings['provider'])) {
            $settings['provider'] = $provider;
        }

        return array_merge($defaults, $settings);
    }

    /**
     * Set payment settings for any payment provider with encryption
     *
     * @param int $businessId The business ID
     * @param string $provider The payment provider (e.g., 'vnpay', 'momo', 'paypal')
     * @param array $settings The settings to save
     * @return BusinessSetting The updated or created setting
     */
    public static function setPaymentSettings(int $businessId, string $provider, array $settings)
    {
        $settingKey = strtolower($provider) . '_payment';
        $encryptedValue = Crypt::encrypt(json_encode($settings));

        return static::updateOrCreate(
            [
                'business_id' => $businessId,
                'setting_key' => $settingKey
            ],
            [
                'setting_value' => $encryptedValue
            ]
        );
    }

    /**
     * Get bank account settings for a business
     *
     * @param int $businessId The business ID
     * @return array The bank account settings
     */
    public static function getBankSettings(int $businessId)
    {
        $settings = static::get($businessId, 'bank_transfer', []);

        // If settings is a JSON string, decode it
        if (is_string($settings) && !is_array($settings)) {
            $decodedSettings = json_decode($settings, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $settings = $decodedSettings;
            }
        }

        $defaults = [
            'accountNumber' => '',
            'bankName' => '',
            'accountName' => '',
            'bankBranch' => '',
            'description' => ''
        ];

        return array_merge($defaults, $settings ?? []);
    }

    /**
     * Get bank account settings for a business, formatted for display
     *
     * @param int $businessId The business ID
     * @return array The formatted bank account settings
     */
    public static function getBankSettingsFormatted(int $businessId)
    {
        $settings = self::getBankSettings($businessId);

        return [
            'accountNumber' => $settings['accountNumber'] ?? '',
            'bankName' => $settings['bankName'] ?? '',
            'accountName' => $settings['accountName'] ?? '',
            'bankBranch' => $settings['bankBranch'] ?? '',
            'formattedDetails' => implode(' | ', array_filter([
                !empty($settings['accountNumber']) ? 'STK: ' . $settings['accountNumber'] : '',
                !empty($settings['bankName']) ? $settings['bankName'] : '',
                !empty($settings['accountName']) ? 'Chủ TK: ' . $settings['accountName'] : ''
            ])),
            'htmlDetails' => implode('<br>', array_filter([
                !empty($settings['accountNumber']) ? '<strong>Số tài khoản:</strong> ' . $settings['accountNumber'] : '',
                !empty($settings['bankName']) ? '<strong>Ngân hàng:</strong> ' . $settings['bankName'] : '',
                !empty($settings['accountName']) ? '<strong>Chủ tài khoản:</strong> ' . $settings['accountName'] : '',
                !empty($settings['bankBranch']) ? '<strong>Chi nhánh:</strong> ' . $settings['bankBranch'] : ''
            ]))
        ];
    }
}