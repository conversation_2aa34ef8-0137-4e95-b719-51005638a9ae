<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Carbon\CarbonPeriod;
class Court extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'branch_id',
        'business_id',
        'type',
        'surface',
        'status',
        'location',
        'description',
        'image_url',
        'price_per_hour',
        'width',
        'length',
        'indoor',
        'amenities',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price_per_hour' => 'float',
        'width' => 'float',
        'length' => 'float',
        'indoor' => 'boolean',
        'amenities' => 'array',
    ];

    /**
     * Get the branch that owns the court.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
    public function dailyRevenues(): HasMany
    {
        return $this->hasMany(DailyRevenue::class);
    }
    /**
     * Get the business that owns the court.
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the reservations for the court.
     */
    public function reservations()
    {
        return $this->hasMany(CourtBooking::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(CourtBooking::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(CourtPrice::class);
    }

    public function availabilityExceptions(): HasMany
    {
        return $this->hasMany(CourtAvailabilityException::class);
    }

    public function getPriceForTime($time)
    {
        return $this->prices()
            ->where('is_active', true)
            ->where('start_time', '<=', $time)
            ->where('end_time', '>', $time)
            ->first();
    }

    public function isAvailableForDateTime($date, $time)
    {
        $cacheKey = "court_{$this->id}_availability_{$date}_{$time}";

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($date, $time) {
            // Check for exceptions first
            $exception = CourtAvailabilityException::getExceptionForDate($this->branch_id, $this->type, $date);
            if ($exception) {
                if ($exception->type === 'closed') {
                    return false;
                }
                if ($exception->start_time && $exception->end_time) {
                    $timeCarbon = Carbon::parse($time);
                    if ($timeCarbon->between($exception->start_time, $exception->end_time)) {
                        return false;
                    }
                }
            }

            // Check branch opening hours
            $timeCarbon = Carbon::parse($time);
            $branch = $this->branch;
            $openTime = Carbon::parse($branch->opening_hour ?? '06:00:00');
            $closeTime = Carbon::parse($branch->closing_hour ?? '22:00:00');

            if (!$timeCarbon->between($openTime, $closeTime)) {
                return false;
            }

            // Check for existing bookings
            $booking = $this->bookings()
                ->where('booking_date', $date)
                ->where('start_time', '<=', $time)
                ->where('end_time', '>', $time)
                ->whereIn('status', ['pending', 'confirmed'])
                ->first();

            return !$booking;
        });
    }

    /**
     * Get available time slots for a specific date
     *
     * @param string $date The date in Y-m-d format
     * @param int $duration Duration of each slot in minutes (default 30)
     * @param bool $useMemberPrice Whether to use member pricing (default false)
     * @return \Illuminate\Support\Collection
     */
    public function getAvailableTimeSlots($date, $duration = 30, $useMemberPrice = false)
    {
        $duration = (int) $duration;
        $timeSlots = collect();

        $branch = $this->branch;
        $defaultOpenTime = $branch && $branch->opening_hour ? $branch->opening_hour : '06:00';
        $defaultCloseTime = $branch && $branch->closing_hour ? $branch->closing_hour : '22:00';


        $now = Carbon::now();
        $expiredBookings = $this->bookings()
            ->where('status', '!=', 'cancelled')
            ->whereNotNull('payment_deadline')
            ->where('payment_deadline', '<', $now)
            ->get();


        foreach ($expiredBookings as $booking) {
            $booking->update([
                'status' => 'cancelled',
                'cancelled_at' => $now,
                'cancellation_reason' => 'Payment due date'
            ]);
        }


        $existingBookings = $this->bookings()
            ->where('booking_date', $date)
            ->whereIn('status', ['pending', 'confirmed'])
            ->where(function ($query) use ($now) {
                $query->whereNull('payment_deadline')
                    ->orWhere('payment_deadline', '>=', $now);
            })
            ->get(['id', 'start_time', 'end_time', 'status', 'customer_name', 'customer_phone', 'reference_number', 'total_price', 'payment_status']);


        \Illuminate\Support\Facades\Log::debug("Court {$this->id} bookings for {$date}:", $existingBookings->toArray());


        $specialDatePrice = CourtPrice::query()
            ->where('branch_id', $this->branch_id)
            ->where('court_type', $this->type)
            ->where('price_type', 'special_date')
            ->where('special_date', $date)
            ->where('is_active', true)
            ->first();

        $courtPrices = $specialDatePrice
            ? collect([$specialDatePrice])
            : CourtPrice::query()
                ->where('branch_id', $this->branch_id)
                ->where('court_type', $this->type)
                ->where('price_type', 'normal')
                ->where('is_active', true)
                ->get();


        $startTime = Carbon::parse($defaultOpenTime);
        $endTime = Carbon::parse($defaultCloseTime);


        $currentDate = Carbon::today();
        $selectedDate = Carbon::parse($date);
        $isPastDate = $selectedDate->lt($currentDate);
        $isToday = $selectedDate->isSameDay($currentDate);

        while ($startTime->copy()->addMinutes($duration) <= $endTime) {
            $slotStart = $startTime->format('H:i');
            $slotEnd = $startTime->copy()->addMinutes($duration)->format('H:i');

            $slotStartObj = Carbon::parse($slotStart);
            $slotEndObj = Carbon::parse($slotEnd);


            $isPastSlot = $isPastDate || ($isToday && $slotStartObj->lt($now));


            $isBooked = false;
            $bookingInfo = null;

            foreach ($existingBookings as $booking) {
                $bookingStart = Carbon::parse($booking->start_time);
                $bookingEnd = Carbon::parse($booking->end_time);


                if (

                    ($slotStartObj >= $bookingStart && $slotStartObj < $bookingEnd) ||

                    ($slotEndObj > $bookingStart && $slotEndObj <= $bookingEnd) ||

                    ($slotStartObj <= $bookingStart && $slotEndObj >= $bookingEnd)
                ) {
                    $isBooked = true;
                    $bookingInfo = [
                        'id' => $booking->id,
                        'status' => $booking->status,
                        'customer_name' => $booking->customer_name,
                        'customer_phone' => $booking->customer_phone,
                        'reference_number' => $booking->reference_number,
                        'total_price' => (float) $booking->total_price,
                        'payment_status' => $booking->payment_status ?? 'pending'
                    ];
                    break;
                }
            }


            $slotPrice = null;
            $memberPrice = null;
            foreach ($courtPrices as $price) {
                $priceStart = Carbon::parse($price->start_time)->format('H:i');
                $priceEnd = Carbon::parse($price->end_time)->format('H:i');

                if ($priceStart <= $slotStart && $priceEnd > $slotStart) {
                    $slotPrice = (float) $price->price_per_hour;
                    $memberPrice = (float) $price->member_price_per_hour;
                    break;
                }
            }

            if (!$slotPrice) {
                $slotPrice = (float) ($this->price_per_hour ?? 0);
                $memberPrice = (float) ($this->member_price_per_hour ?? $slotPrice);
            }


            $finalPrice = $useMemberPrice ? $memberPrice : $slotPrice;


            if ($duration != 60) {
                $finalPrice = ($finalPrice / 60) * $duration;
            }


            $status = 'available';
            if ($isPastSlot) {
                $status = 'past';
            } else if ($isBooked) {
                $status = 'booked';
            }

            $timeSlots->push([
                'start_time' => $slotStart,
                'end_time' => $slotEnd,
                'display_time' => $slotStart . ' - ' . $slotEnd,
                'price' => $finalPrice,
                'regular_price' => $slotPrice,
                'member_price' => $memberPrice,
                'is_member_price' => $useMemberPrice,
                'status' => $status,
                'available' => !$isBooked && !$isPastSlot,
                'is_past' => $isPastSlot,
                'is_booked' => $isBooked,
                'booking_info' => $bookingInfo,
                'formatted_price' => number_format($finalPrice, 0, ',', '.') . '₫',
                'court_id' => $this->id,
                'court_name' => $this->name,
            ]);

            $startTime->addMinutes($duration);
        }

        return $timeSlots;
    }


    public function getBookedTimeSlots($date, $duration = 60)
    {

        $duration = (int) $duration;

        $bookedSlots = collect();

        $bookings = $this->bookings()
            ->where('booking_date', $date)
            ->whereIn('status', ['pending', 'confirmed'])
            ->get(['start_time', 'end_time']);

        foreach ($bookings as $booking) {
            $start = Carbon::parse($booking->start_time);
            $end = Carbon::parse($booking->end_time);


            $period = CarbonPeriod::create($start, $duration . " minutes", $end->subMinutes(1));

            foreach ($period as $slot) {
                $bookedSlots->push($slot->format('H:i'));
            }
        }

        return $bookedSlots->unique()->sort()->values();
    }

    /**
     * Get courts with availability for the schedule view
     *
     * @param int $branchId The branch ID
     * @param string $date Date in Y-m-d format
     * @param int $duration Slot duration in minutes (default 30)
     * @param bool $useMemberPrice Whether to use member pricing (default false)
     * @return \Illuminate\Support\Collection
     */
    public static function getCourtsForSchedule($branchId, $date, $duration = 30, $useMemberPrice = false)
    {

        $duration = (int) $duration;

        $courts = self::where('branch_id', $branchId)
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();

        $result = $courts->map(function ($court) use ($date, $duration, $useMemberPrice) {
            $timeSlots = $court->getAvailableTimeSlots($date, $duration, $useMemberPrice);

            return [
                'id' => $court->id,
                'name' => $court->name,
                'type' => $court->type,
                'time_slots' => $timeSlots,
            ];
        });

        return $result;
    }

    /**
     * Get the formatted image URL attribute.
     *
     * @return string|null
     */
    public function getFormattedImageUrlAttribute()
    {
        if (!$this->image_url) {

            return '/images/court-placeholder.jpg';
        }


        if (str_starts_with($this->image_url, 'http://') || str_starts_with($this->image_url, 'https://')) {
            return $this->image_url;
        }


        if (str_starts_with($this->image_url, '/storage')) {
            return asset($this->image_url);
        }


        return asset('storage/' . $this->image_url);
    }
}