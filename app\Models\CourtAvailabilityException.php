<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CourtAvailabilityException extends Model
{
    use HasFactory;

    protected $fillable = [
        'court_id',
        'date',
        'start_time',
        'end_time',
        'type',
        'reason',
        'is_active'
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'is_active' => 'boolean'
    ];

    public function court(): BelongsTo
    {
        return $this->belongsTo(Court::class);
    }

    public static function getExceptionForDate($courtId, $date)
    {
        return self::where('court_id', $courtId)
            ->where('date', $date)
            ->where('is_active', true)
            ->first();
    }
}