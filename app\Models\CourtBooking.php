<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Customer;
use App\Models\Court;
use App\Models\CourtPrice;
use App\Models\CourtService;
use App\Models\Payment;
use App\Models\User;
use App\Models\BookingEvent;
use App\Services\SystemSettingService;
use App\Services\BookingEventService;
use App\Models\Booking;

class CourtBooking extends Model
{
    use HasFactory;

    /**
     * Payment status constants
     */
    const PAYMENT_STATUS_UNPAID = 'unpaid';
    const PAYMENT_STATUS_PAID = 'paid';
    const PAYMENT_STATUS_PARTIAL = 'partial';
    const PAYMENT_STATUS_CANCELLED = 'cancelled';
    const PAYMENT_STATUS_REFUNDED = 'refunded';

    protected $fillable = [
        'booking_id',
        'court_id',
        'branch_id',
        'user_id',
        'customer_id',
        'booking_date',
        'start_time',
        'end_time',
        'total_price',
        'status',
        'notes',
        'customer_name',
        'customer_phone',
        'customer_email',
        'reference_number',
        'cancelled_at',
        'cancellation_reason',
        'is_member_price',
        'metadata',
        'booking_type',
        'reference_payment',
        'payment_deadline',
        'checkin_status',
        'checkin_time',
        'checkout_time',
        'overtime_fee',
        'overtime_minutes',
        'overtime_calculated',
        'overtime_fee_paid',
        'is_reviewed'
    ];

    protected $casts = [
        'booking_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'total_price' => 'decimal:2',
        'overtime_fee' => 'decimal:2',
        'overtime_minutes' => 'integer',
        'overtime_calculated' => 'boolean',
        'overtime_fee_paid' => 'boolean',
        'is_reviewed' => 'boolean',
        'cancelled_at' => 'datetime',
        'is_member_price' => 'boolean',
        'metadata' => 'array',
        'booking_type' => 'string',
        'payment_deadline' => 'datetime',
        'checkin_time' => 'datetime',
        'checkout_time' => 'datetime'
    ];

    /**
     * Scope a query to filter bookings by date range or specific date.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $date  Can be a single date (string/Carbon) or an array with 'start' and 'end' keys
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDate(Builder $query, $date): Builder
    {
        if (is_array($date) && isset($date['start']) && isset($date['end'])) {
            $startDate = Carbon::parse($date['start'])->startOfDay();
            $endDate = Carbon::parse($date['end'])->endOfDay();

            return $query->whereBetween('booking_date', [$startDate, $endDate]);
        } elseif (is_string($date) || $date instanceof Carbon) {
            $parsedDate = $date instanceof Carbon ? $date : Carbon::parse($date);

            return $query->whereDate('booking_date', $parsedDate);
        }

        return $query;
    }

    /**
     * Scope a query to filter bookings by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string|array  $status  Single status or array of statuses
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus(Builder $query, $status): Builder
    {
        if (is_array($status)) {
            return $query->whereIn('status', $status);
        }

        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include upcoming bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('booking_date', '>=', Carbon::today())
            ->whereIn('status', ['pending', 'confirmed']);
    }

    /**
     * Scope a query to only include past bookings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePast(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->where('booking_date', '<', Carbon::today())
                ->orWhere(function ($query) {
                    $query->where('booking_date', Carbon::today())
                        ->whereIn('status', ['completed', 'cancelled']);
                });
        });
    }

    /**
     * Get bookings by date range/single date and optional status filter.
     *
     * @param  mixed  $date  Can be a single date (string/Carbon) or an array with 'start' and 'end' keys
     * @param  string|array|null  $status  Single status, array of statuses, or null for all statuses
     * @param  int|null  $branchId  Optional branch ID filter
     * @param  int|null  $courtId  Optional court ID filter
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBookings($date, $status = null, $branchId = null, $courtId = null)
    {
        $query = self::with(['court', 'branch', 'customer', 'user']);

        $query->byDate($date);

        if ($status !== null) {
            $query->byStatus($status);
        }

        if ($branchId !== null) {
            $query->where('branch_id', $branchId);
        }

        if ($courtId !== null) {
            $query->where('court_id', $courtId);
        }

        return $query->orderBy('booking_date')
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Get bookings for the schedule view, formatted for easier rendering
     *
     * @param  mixed  $date  Can be a single date (string/Carbon) or an array with 'start' and 'end' keys
     * @param  int  $branchId  Branch ID filter
     * @param  int|null  $courtId  Optional court ID filter
     * @return \Illuminate\Support\Collection
     */
    public static function getBookingsForSchedule($date, $branchId, $courtId = null)
    {
        $statuses = ['pending', 'confirmed'];
        $query = self::with(['court'])
            ->where('branch_id', $branchId)
            ->byDate($date)
            ->byStatus($statuses);

        if ($courtId !== null && $courtId !== 'all') {
            $query->where('court_id', $courtId);
        }

        $bookings = $query->orderBy('start_time')->get();


        return $bookings->map(function ($booking) {
            $startTime = Carbon::parse($booking->start_time);
            $endTime = Carbon::parse($booking->end_time);

            return [
                'id' => $booking->id,
                'court_id' => $booking->court_id,
                'court_name' => $booking->court->name,
                'booking_date' => $booking->booking_date->format('Y-m-d'),
                'start_time' => $startTime->format('H:i'),
                'end_time' => $endTime->format('H:i'),
                'duration_minutes' => $startTime->diffInMinutes($endTime),
                'customer_name' => $booking->customer_name,
                'total_price' => $booking->total_price,
                'status' => $booking->status,
                'payment_status' => $booking->payment_status ?? 'pending',
                'reference_number' => $booking->reference_number,
            ];
        });
    }

    /**
     * Get specific fields of bookings by date range/single date and optional status filter.
     *
     * @param  mixed  $date  Can be a single date (string/Carbon) or an array with 'start' and 'end' keys
     * @param  string|array|null  $status  Single status, array of statuses, or null for all statuses
     * @param  array  $fields  Array of fields to select (e.g. ['id', 'start_time', 'end_time'])
     * @param  array  $relations  Array of relations to load (e.g. ['court', 'customer'])
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBookingFields($date, $status = null, array $fields = [], array $relations = [])
    {
        $query = self::query();
        if (!empty($fields)) {
            if (!in_array('id', $fields, true)) {
                $fields[] = 'id';
            }
            $query->select($fields);
        }

        if (!empty($relations)) {
            $query->with($relations);
        }
        $query->byDate($date);
        if ($status !== null) {
            if (is_array($status)) {
                $query->whereIn('status', $status);
            } else {
                $query->where('status', $status);
            }
        }
        if (empty($fields) || in_array('booking_date', $fields, true)) {
            $query->orderBy('booking_date');
        }
        if (empty($fields) || in_array('start_time', $fields, true)) {
            $query->orderBy('start_time');
        }
        return $query->get();
    }

    public function court(): BelongsTo
    {
        return $this->belongsTo(Court::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the payment associated with the booking.
     */
    public function payment(): HasOne
    {
        return $this->hasOne(Payment::class, 'court_booking_id');
    }

    /**
     * Get payment by booking reference.
     *
     * This uses the booking reference instead of the foreign key.
     */
    public function paymentByReference()
    {
        return Payment::where('booking_reference', $this->reference_number)->first();
    }

    /**
     * Generate a unique reference number for the booking
     *
     * @param string $bookingType 'online' or 'offline'
     * @return string
     */
    public static function generateReferenceNumber(string $bookingType = 'offline'): string
    {
        $prefix = $bookingType === 'online' ? 'ON' : 'OF';
        do {
            $randomNumber = mt_rand(10000000, 99999999);
            $referenceNumber = $prefix . $randomNumber;
            $exists = self::where('reference_number', $referenceNumber)->exists();
        } while ($exists);

        return $referenceNumber;
    }

    public static function processBookings(array $data, $user, string $bookingType = 'offline', array &$createdBookings = [])
    {
        $branchId = $data['branch_id'];
        $referenceNumber = $data['reference_number'] ?? self::generateReferenceNumber($bookingType);
        $isMember = false;
        $customer_id = null;
        $user_id = null;
        if (!empty($data['customer_phone']) || !empty($data['customer_email'])) {
            $customerQuery = Customer::query();

            if (!empty($data['customer_phone'])) {
                $customerQuery->where('phone', $data['customer_phone']);
            }

            if (!empty($data['customer_email'])) {
                $customerQuery->orWhere('email', $data['customer_email']);
            }

            $customer = $customerQuery->first();
            $isMember = !is_null($customer);
            $customer_id = $customer->id ?? null;
            $user_id = $customer->user_id ?? null;
        }

        if (empty($data['booking_courts']) || !is_array($data['booking_courts'])) {
            return [
                'success' => false,
                'message' => 'Không có sân nào được đặt. Vui lòng chọn ít nhất một khung giờ cho một sân.',
                'status_code' => 400
            ];
        }

        foreach ($data['booking_courts'] as $courtBookingData) {
            if (empty($courtBookingData['court_id']) || empty($courtBookingData['booking_slot'])) {
                continue;
            }

            $courtId = $courtBookingData['court_id'];
            $bookingSlots = $courtBookingData['booking_slot'];
            $startTime = $courtBookingData['start_time'];
            $endTime = $courtBookingData['end_time'];

            try {
                $court = Court::findOrFail($courtId);
            } catch (\Exception $e) {
                return [
                    'success' => false,
                    'message' => "Không tìm thấy sân với ID: {$courtId}. Vui lòng kiểm tra lại.",
                    'status_code' => 404
                ];
            }

            if (!$court->is_active) {
                return [
                    'success' => false,
                    'message' => "Sân {$court->name} đã đóng cửa hoặc không còn hoạt động.",
                    'status_code' => 400
                ];
            }

            $conflictingBookings = self::where('court_id', $courtId)
                ->where('booking_date', $data['booking_date'])
                ->where(function ($query) use ($startTime, $endTime) {
                    $query->where(function ($q) use ($startTime, $endTime) {
                        $start = is_string($startTime) ? $startTime : Carbon::parse($startTime)->format('H:i:s');
                        $end = is_string($endTime) ? $endTime : Carbon::parse($endTime)->format('H:i:s');

                        $q->where('start_time', '<', $end)
                            ->where('end_time', '>', $start);
                    });
                })
                ->whereIn('status', ['pending', 'confirmed'])
                ->count();

            if ($conflictingBookings > 0) {
                return [
                    'success' => false,
                    'message' => "Khung giờ đã được đặt cho sân {$court->name}. Vui lòng chọn thời gian khác.",
                    'status_code' => 400
                ];
            }

            $timeInterval = 30;
            $courtBookingPrice = 0;

            foreach ($bookingSlots as $slot) {
                $price = CourtPrice::where('branch_id', $branchId)
                    ->where(function ($query) use ($court) {
                        $query->where('court_type', $court->type)
                            ->orWhereNull('court_type');
                    })
                    ->get()
                    ->filter(function ($price) use ($slot) {
                        $slotTime = is_string($slot) ? Carbon::createFromFormat('H:i', $slot) : Carbon::parse($slot);
                        $startTime = Carbon::parse($price->start_time);
                        $endTime = Carbon::parse($price->end_time);

                        return $slotTime->format('H:i') >= $startTime->format('H:i') &&
                            $slotTime->format('H:i') < $endTime->format('H:i');
                    })
                    ->first();

                if ($price) {
                    $hourlyRate = $isMember ? $price->member_price_per_hour : $price->price_per_hour;
                    $intervalRate = $timeInterval / 60;
                    $courtBookingPrice += $hourlyRate * $intervalRate;
                } else {
                    $hourlyRate = $court->price_per_hour;
                    if ($isMember) {
                        $hourlyRate = $hourlyRate * 0.85;
                    }
                    $intervalRate = $timeInterval / 60;
                    $courtBookingPrice += $hourlyRate * $intervalRate;
                }
            }

            $newBooking = new self();
            $newBooking->branch_id = $branchId;
            $newBooking->user_id = $user_id;
            $newBooking->court_id = $courtId;
            $newBooking->booking_date = $data['booking_date'];


            $formattedStartTime = is_string($startTime) ?
                Carbon::parse($data['booking_date'] . ' ' . $startTime)->format('H:i:s') :
                Carbon::parse($startTime)->format('H:i:s');

            $formattedEndTime = is_string($endTime) ?
                Carbon::parse($data['booking_date'] . ' ' . $endTime)->format('H:i:s') :
                Carbon::parse($endTime)->format('H:i:s');

            $newBooking->start_time = $formattedStartTime;
            $newBooking->end_time = $formattedEndTime;

            $newBooking->customer_name = $data['customer_name'];
            $newBooking->customer_phone = $data['customer_phone'];
            $newBooking->customer_email = $data['customer_email'] ?? null;
            $newBooking->reference_number = $referenceNumber;
            $newBooking->status = $bookingType === 'offline' ? 'confirmed' : 'pending';
            $newBooking->is_member_price = $isMember;
            $newBooking->booking_type = $bookingType;
            $newBooking->payment_status = $data['payment_status'] ?? 'unpaid';
            $newBooking->customer_id = $customer_id;


            $paymentDeadline = SystemSettingService::get('payment_deadline');
            $paymentDeadline = is_numeric($paymentDeadline) ? (int) $paymentDeadline : 60;
            $newBooking->payment_deadline = $bookingType === 'online' ? now()->addMinutes($paymentDeadline) : null;

            $servicesData = [];
            $servicesTotal = 0;

            if (count($createdBookings) === 0 && !empty($data['services']) && is_array($data['services'])) {
                foreach ($data['services'] as $serviceData) {
                    if (isset($serviceData['id'])) {
                        $service = CourtService::find($serviceData['id']);
                        if ($service) {
                            $price = $serviceData['price'] ?? $service->price;
                            $quantity = $serviceData['quantity'] ?? 1;
                            $serviceItemTotal = $price * $quantity;
                            $servicesTotal += $serviceItemTotal;

                            $servicesData[] = [
                                'id' => $service->id,
                                'name' => $service->name,
                                'price' => $price,
                                'quantity' => $quantity,
                                'total' => $serviceItemTotal
                            ];
                        }
                    }
                }
            }

            $totalPriceForCourt = $courtBookingPrice;
            if (count($createdBookings) === 0) {
                $totalPriceForCourt += $servicesTotal;
            }

            $newBooking->total_price = $totalPriceForCourt;



            $creatorType = $bookingType === 'offline' ? 'branch_staff' : 'customer';
            $creatorId = $user->id ?? null;
            $creatorName = $user->name ?? $data['customer_name'];

            $newBooking->metadata = [
                'court_name' => $court->name,
                'court_price' => $courtBookingPrice,
                'time_slots' => $bookingSlots,
                'services_total' => count($createdBookings) === 0 ? $servicesTotal : 0,
                'services' => count($createdBookings) === 0 ? $servicesData : [],
                'payment_method' => $data['payment_method'] ?? null,
                'payment_status' => $data['payment_status'] ?? 'pending',
                'booking_type' => $bookingType,
                'created_by' => $creatorType,
                'staff_id' => $creatorType === 'branch_staff' ? $creatorId : null,
                'staff_name' => $creatorType === 'branch_staff' ? $creatorName : null,
                'user_id' => $creatorType === 'customer' ? $creatorId : null,
                'created_at' => now()->toDateTimeString(),
                'is_member' => $isMember,
                'court_group' => [
                    'total_courts' => count($data['booking_courts']),
                    'court_index' => count($createdBookings) + 1
                ],
                'payment_deadline' => $bookingType === 'online' ? $newBooking->payment_deadline->toDateTimeString() : null
            ];

            $newBooking->save();

            if (count($createdBookings) === 0 && !empty($data['services']) && is_array($data['services'])) {
                foreach ($data['services'] as $serviceData) {
                    if (isset($serviceData['id'])) {
                        $service = CourtService::find($serviceData['id']);
                        if ($service) {
                            $newBooking->services()->attach($service->id, [
                                'quantity' => $serviceData['quantity'] ?? 1,
                                'price' => $serviceData['price'] ?? $service->price,
                            ]);
                        }
                    }
                }
            }

            if ($bookingType !== 'online') {
                if (
                    ($data['payment_status'] === 'paid' || $data['payment_status'] === 'completed')
                    && count($createdBookings) === 0
                    && !empty($data['payment_methods'])
                    && is_array($data['payment_methods'])
                ) {
                    foreach ($data['payment_methods'] as $paymentMethod) {
                        if (!isset($paymentMethod['method']) || !isset($paymentMethod['amount']) || $paymentMethod['amount'] <= 0) {
                            continue;
                        }

                        $payment = new \App\Models\Payment();
                        $payment->court_booking_id = $newBooking->id;
                        $payment->booking_reference = $referenceNumber;
                        $payment->user_id = $user_id ?? null;
                        $payment->amount = $paymentMethod['amount'];
                        $payment->payment_method = $paymentMethod['method'];
                        $payment->status = 'completed';
                        $payment->transaction_date = now();
                        $payment->reference_number = $referenceNumber;
                        $payment->reference_payment = $newBooking->reference_payment;
                        $payment->notes = "Thanh toán đặt sân " . ($bookingType === 'offline' ? 'tại chỗ' : 'trực tuyến');

                        $paymentDetails = [
                            'method' => $paymentMethod['method'],
                            'amount' => $paymentMethod['amount'],
                            'created_by' => $creatorType,
                        ];

                        if ($creatorType === 'branch_staff') {
                            $paymentDetails['staff_id'] = $user->id;
                            $paymentDetails['staff_name'] = $user->name;
                        } else {
                            $paymentDetails['user_id'] = $user_id ?? null;
                            $paymentDetails['user_name'] = $user->name ?? $data['customer_name'];
                        }

                        if ($paymentMethod['method'] === 'cash' && isset($paymentMethod['change_amount'])) {
                            $paymentDetails['change_amount'] = $paymentMethod['change_amount'];
                        }

                        if (
                            in_array($paymentMethod['method'], ['card', 'transfer', 'e-wallet'])
                            && isset($paymentMethod['reference_code'])
                        ) {
                            $paymentDetails['reference_code'] = $paymentMethod['reference_code'];
                        }

                        $payment->payment_details = $paymentDetails;
                        $payment->save();
                    }
                }
            }

            $createdBookings[] = [
                'id' => $newBooking->id,
                'court_id' => $courtId,
                'court_name' => $court->name,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'total_price' => $totalPriceForCourt,
                'is_member_price' => $isMember
            ];
        }
        $paymentMethodsData = [];
        if (!empty($data['payment_methods']) && is_array($data['payment_methods'])) {
            $paymentMethodsData = $data['payment_methods'];
        }
        if (empty($createdBookings)) {
            return [
                'success' => false,
                'message' => 'Không có sân nào được đặt. Vui lòng chọn ít nhất một khung giờ cho một sân.',
                'status_code' => 400
            ];
        }

        $totalOrderPrice = array_sum(array_column($createdBookings, 'total_price'));


        return [
            'success' => true,
            'message' => 'Đã tạo đơn đặt sân ' . ($bookingType === 'offline' ? 'offline' : 'online') . ' thành công với mã tham chiếu: ' . $referenceNumber,
            'reference_number' => $referenceNumber,
            'total_price' => $totalOrderPrice,
            'bookings' => $createdBookings,
            'status_code' => 200
        ];
    }

    /**
     * Check if the booking has a payment that is in progress or complete.
     *
     * @return bool
     */
    public function hasPaymentInProgress()
    {
        $inProgressStatuses = [
            'pending_approval',
            'completed',
            'processing',
            'approved',

        ];

        if (in_array($this->payment_status, $inProgressStatuses)) {
            return true;
        }

        $payment = $this->paymentByReference();

        if ($payment && in_array($payment->status, $inProgressStatuses)) {
            return true;
        }

        return false;
    }

    /**
     * The services that belong to the booking.
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(CourtService::class, 'court_booking_services', 'court_booking_id', 'court_service_id')
            ->withPivot('price', 'quantity')
            ->withTimestamps();
    }

    /**
     * Get the booking events associated with this booking's reference number
     */
    public function bookingEvents()
    {
        return $this->hasMany(BookingEvent::class, 'reference_number', 'reference_number');
    }

    /**
     * Get the timeline of events for this booking
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTimeline()
    {
        return BookingEvent::byReference($this->reference_number)
            ->orderBy('event_time')
            ->get();
    }

    /**
     * Get a formatted timeline for display
     * 
     * @return array
     */
    public function getFormattedTimeline()
    {
        return BookingEventService::getFormattedTimeline($this->reference_number);
    }

    /**
     * Log an event for this booking
     * 
     * @param string $eventType
     * @param array $eventData
     * @param int|null $userId
     * @param int|null $staffId
     * @param string|null $notes
     * @return \App\Models\BookingEvent
     */
    public function logEvent(string $eventType, array $eventData = [], ?int $userId = null, ?int $staffId = null, ?string $notes = null)
    {
        return BookingEventService::createEvent(
            $this->reference_number,
            $eventType,
            $eventData,
            $userId,
            $staffId,
            $notes
        );
    }



    /**
     * Check in khách hàng vào sân
     * 
     * @param int|null $userId ID của nhân viên thực hiện check-in
     * @return bool
     */
    public function checkIn(?int $userId = null): bool
    {
        if ($this->status !== 'confirmed') {
            return false;
        }

        $this->checkin_status = 'checked_in';
        $this->checkin_time = now();

        $metadata = $this->metadata ?? [];
        $metadata['checkin'] = [
            'time' => now()->toDateTimeString(),
            'staff_id' => $userId,
        ];
        $this->metadata = $metadata;

        $saved = $this->save();

        if ($saved) {
            BookingEventService::checkIn($this, $userId);
        }

        return $saved;
    }

    /**
     * Check out khách hàng ra khỏi sân
     * 
     * @param int|null $userId ID của nhân viên thực hiện check-out
     * @return bool
     */
    public function checkOut(?int $userId = null): bool
    {
        if ($this->checkin_status !== 'checked_in') {
            return false;
        }

        $this->checkin_status = 'checked_out';
        $this->checkout_time = now();

        $checkoutTime = now();
        $endTimeDate = $this->booking_date->format('Y-m-d');

        $endTimeParts = explode(':', $this->end_time->format('H:i'));
        $endTime = \Carbon\Carbon::parse($endTimeDate . ' ' . $this->end_time->format('H:i'));

        if ($checkoutTime->gt($endTime)) {
            $overtimeMinutes = $checkoutTime->diffInMinutes($endTime);
            $this->overtime_minutes = $overtimeMinutes;
            $this->overtime_calculated = true;
            $this->overtime_fee = 0;

            $priceModel = CourtPrice::getPriceForDateTime($this->branch_id, $this->court_id, $this->booking_date, $this->end_time);
            if ($priceModel) {
                if ($this->is_member_price && $priceModel->member_price_per_hour) {
                    $this->overtime_fee = $priceModel->member_price_per_hour * $overtimeMinutes / 60;
                } else {
                    $this->overtime_fee = $priceModel->price_per_hour * $overtimeMinutes / 60;
                }
            } else {
                $this->overtime_fee = 0;
            }

            $metadata = $this->metadata ?? [];
            $metadata['checkout'] = [
                'time' => now()->toDateTimeString(),
                'staff_id' => $userId,
                'duration_minutes' => $this->getPlayDurationInMinutes(),
                'overtime_minutes' => $overtimeMinutes,
                'checkout_overdue' => true
            ];
            $this->metadata = $metadata;
        } else {
            $this->overtime_minutes = 0;
            $this->overtime_calculated = true;

            $metadata = $this->metadata ?? [];
            $metadata['checkout'] = [
                'time' => now()->toDateTimeString(),
                'staff_id' => $userId,
                'duration_minutes' => $this->getPlayDurationInMinutes(),
                'overtime_minutes' => 0,
                'checkout_overdue' => false
            ];
            $this->metadata = $metadata;
        }

        $saved = $this->save();

        if ($saved) {

            BookingEventService::checkOut(
                $this,
                $this->getPlayDurationInMinutes(),
                $this->overtime_minutes,
                $userId
            );
        }

        return $saved;
    }

    /**
     * Tính thời gian khách đã sử dụng sân (tính bằng phút)
     * 
     * @return int|null
     */
    public function getPlayDurationInMinutes(): ?int
    {
        if ($this->checkin_time && $this->checkout_time) {
            return $this->checkin_time->diffInMinutes($this->checkout_time);
        }

        if ($this->checkin_time) {
            return $this->checkin_time->diffInMinutes(now());
        }

        return null;
    }

    /**
     * Kiểm tra trạng thái check-in của booking
     * 
     * @return string
     */
    public function getCheckinStatusAttribute($value)
    {
        if ($value === null) {
            return 'not_checked_in';
        }

        return $value;
    }

    /**
     * Scope truy vấn các booking đã check-in nhưng chưa check-out
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCurrentlyCheckedIn(Builder $query): Builder
    {
        return $query->where('checkin_status', 'checked_in')
            ->whereNotNull('checkin_time')
            ->whereNull('checkout_time');
    }

    /**
     * Scope truy vấn các booking hoàn thành (đã check-out)
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('checkin_status', 'checked_out')
            ->whereNotNull('checkin_time')
            ->whereNotNull('checkout_time');
    }

    /**
     * Tính toán phí chênh lệch dựa trên số phút quá giờ và giá sân
     * 
     * @param float $hourlyRate Giá theo giờ của sân
     * @return bool
     */
    public function calculateOvertimeFee(float $hourlyRate): bool
    {
        if (!$this->overtime_minutes || $this->overtime_minutes <= 0 || $this->overtime_fee_paid) {
            return false;
        }


        $overtimeHours = $this->overtime_minutes / 60;
        $this->overtime_fee = round($hourlyRate * $overtimeHours, 2);


        $metadata = $this->metadata ?? [];
        $metadata['overtime_fee'] = [
            'calculated_at' => now()->toDateTimeString(),
            'hourly_rate' => $hourlyRate,
            'overtime_hours' => $overtimeHours,
            'amount' => $this->overtime_fee
        ];
        $this->metadata = $metadata;

        return $this->save();
    }

    /**
     * Mark overtime fee as paid and log the event
     * 
     * @param int|null $paymentId ID của giao dịch thanh toán
     * @param string $paymentMethod Phương thức thanh toán
     * @param int|null $staffId ID của nhân viên thực hiện
     * @return bool
     */
    public function markOvertimeFeePaid(?int $paymentId = null, string $paymentMethod = 'cash', ?int $staffId = null): bool
    {
        if (!$this->overtime_fee || $this->overtime_fee <= 0) {
            return false;
        }

        $this->overtime_fee_paid = true;

        $metadata = $this->metadata ?? [];
        $metadata['overtime_fee']['paid'] = [
            'paid_at' => now()->toDateTimeString(),
            'payment_id' => $paymentId,
            'payment_method' => $paymentMethod
        ];
        $this->metadata = $metadata;

        $saved = $this->save();

        if ($saved) {

            BookingEventService::overtimePayment(
                $this,
                $this->overtime_fee,
                $paymentMethod,
                $this->overtime_minutes,
                $staffId
            );
        }

        return $saved;
    }

    /**
     * Get all bookings with the same reference number
     * (grouped booking)
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getGroupedBookings(string $referenceNumber)
    {
        return self::where('reference_number', $referenceNumber)
            ->orderBy('id')
            ->get();
    }

    /**
     * Approve bookings - change status from pending to confirmed for all bookings with the same reference number
     * 
     * @param string $referenceNumber The reference number of the bookings to approve
     * @param int|null $staffId ID of the staff member who approved the booking
     * @return array Result with success status and message
     */
    public static function approveBookings(string $referenceNumber, ?int $staffId = null)
    {
        try {
            $bookings = self::where('reference_number', $referenceNumber)
                ->where('status', 'pending')
                ->get();

            if ($bookings->isEmpty()) {
                return [
                    'success' => false,
                    'message' => 'Không tìm thấy đơn đặt sân cần xác nhận hoặc đơn đã được xác nhận trước đó.',
                    'status_code' => 404
                ];
            }

            $count = 0;
            foreach ($bookings as $booking) {
                $booking->status = 'confirmed';
                $metadata = $booking->metadata ?? [];
                $metadata['approval'] = [
                    'approved_at' => now()->toDateTimeString(),
                    'approved_by' => $staffId,
                ];
                $booking->metadata = $metadata;

                if ($booking->save()) {
                    $count++;
                    BookingEventService::bookingApproved(
                        $booking,
                        $staffId
                    );
                }
            }

            if ($count > 0) {
                return [
                    'success' => true,
                    'message' => "Đã xác nhận {$count} đơn đặt sân thành công.",
                    'count' => $count,
                    'status_code' => 200
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Không thể xác nhận đơn đặt sân. Vui lòng thử lại.',
                    'status_code' => 500
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage(),
                'status_code' => 500
            ];
        }
    }


    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Check if the booking is partially paid
     * 
     * @return bool
     */
    public function isPartiallyPaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_PARTIAL;
    }

    /**
     * Check if the booking is fully paid
     * 
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_PAID;
    }

    /**
     * Check if the booking is unpaid
     * 
     * @return bool
     */
    public function isUnpaid(): bool
    {
        return $this->payment_status === self::PAYMENT_STATUS_UNPAID;
    }
}