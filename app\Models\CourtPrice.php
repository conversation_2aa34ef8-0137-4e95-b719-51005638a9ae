<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CourtPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'court_type',
        'start_time',
        'end_time',
        'price_per_hour',
        'member_price_per_hour',
        'is_active',
        'status',
        'price_type',
        'special_date',
        'days',
        'mode',
        'notes',
        'description'
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'price_per_hour' => 'decimal:2',
        'member_price_per_hour' => 'decimal:2',
        'is_active' => 'boolean',
        'special_date' => 'date',
        'days' => 'array'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        // When a price is created/updated/deleted, update the branch's price range
        static::saved(function ($courtPrice) {
            if ($courtPrice->isNormalPrice() && $courtPrice->isActive()) {
                $courtPrice->branch->updatePriceRange();
            }
        });

        static::deleted(function ($courtPrice) {
            $courtPrice->branch->updatePriceRange();
        });
    }

    /**
     * Check if this is a normal price (not special date or holiday).
     *
     * @return bool
     */
    public function isNormalPrice(): bool
    {
        return $this->price_type === 'normal';
    }

    /**
     * Check if this price is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->is_active && $this->status === 'active';
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function scopeNormalPrices($query)
    {
        return $query->where('price_type', 'normal');
    }

    public function scopeHolidayPrices($query)
    {
        return $query->where('price_type', 'holiday');
    }

    public function scopeSpecialDatePrices($query)
    {
        return $query->where('price_type', 'special_date');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeNotStarted($query)
    {
        return $query->where('status', 'not_started');
    }

    public function scopeForDate($query, $date)
    {
        return $query->where(function ($q) use ($date) {
            $q->whereNull('special_date')
                ->orWhere('special_date', $date);
        });
    }

    public static function getPriceForDateTime($branchId, $courtType, $date, $time)
    {
        $carbonDate = Carbon::parse($date);
        $carbonTime = Carbon::parse($time);
        $specialPrice = self::where('branch_id', (int) $branchId)
            ->where('special_date', $carbonDate->format('Y-m-d'))
            ->where('start_time', '<=', $carbonTime->format('H:i'))
            ->where('end_time', '>=', $carbonTime->format('H:i'))
            ->where('is_active', true)
            ->where('status', 'active')
            ->first();

        if ($specialPrice) {
            return $specialPrice;
        }

        $price = CourtPrice::where('branch_id', (int) $branchId)
            ->where('start_time', '<=', $carbonTime->format('H:i'))
            ->where('end_time', '>=', $carbonTime->format('H:i'))
            ->where('is_active', true)
            ->where('status', 'active')
            ->whereNull('special_date')
            ->first();

        return $price;
    }

    public static function getPriceListByBranchId($branchId)
    {
        $date = Carbon::today()->format('Y-m-d');
        $specialPrices = self::where('branch_id', (int) $branchId)
            ->where('is_active', true)
            ->where('status', 'active')
            ->whereNotNull('special_date')
            ->where('special_date', $date)
            ->get();

        if ($specialPrices->count() > 0) {
            $priceList = $specialPrices;
        } else {
            $priceList = self::where('branch_id', (int) $branchId)
                ->where('is_active', true)
                ->where('status', 'active')
                ->whereNull('special_date')
                ->get();
        }

        return $priceList;
    }
    public static function getPriceListByBranchIdAndDate($branchId, $date)
    {
        $date = Carbon::parse($date)->format('Y-m-d');
        $specialPrices = self::where('branch_id', (int) $branchId)
            ->where('is_active', true)
            ->where('status', 'active')
            ->whereNotNull('special_date')
            ->where('special_date', $date)
            ->get();

        if ($specialPrices->count() > 0) {
            $priceList = $specialPrices;
        } else {
            $priceList = self::where('branch_id', (int) $branchId)
                ->where('is_active', true)
                ->where('status', 'active')
                ->whereNull('special_date')
                ->get();
        }

        return $priceList;
    }
}