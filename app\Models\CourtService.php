<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CourtService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'price',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * The bookings that belong to the service.
     */
    public function bookings(): BelongsToMany
    {
        return $this->belongsToMany(CourtBooking::class, 'court_booking_services')
            ->withPivot('price')
            ->withTimestamps();
    }

    /**
     * Get the service assignments for this court service.
     */
    public function serviceAssigns(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(CourtServiceAssign::class);
    }

    /**
     * Get the active service assignments for this court service.
     */
    public function activeServiceAssigns(): Has<PERSON>any
    {
        return $this->hasMany(CourtServiceAssign::class)
            ->where('status', 'active');
    }

    /**
     * Get the branch service entries for this court service.
     */
    public function branchServices()
    {
        return $this->hasMany(BranchService::class);
    }

    /**
     * Get all branches that have this service available.
     */
    public function branches()
    {
        return $this->belongsToMany(Branch::class, 'branch_services')
            ->withPivot('price', 'member_price', 'is_active', 'description', 'settings')
            ->withTimestamps();
    }

    /**
     * Get only branches that have this service active.
     */
    public function activeBranches()
    {
        return $this->belongsToMany(Branch::class, 'branch_services')
            ->withPivot('price', 'member_price', 'is_active', 'description', 'settings')
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Get all businesses using this service through service assignments.
     */
    public function businesses()
    {
        return $this->belongsToMany(Business::class, 'court_service_assigns')
            ->distinct();
    }
}