<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CourtServiceAssign extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'court_service_id',
        'business_id',
        'branch_id',
        'status',
        'price',
        'unit',
        'discount_type',
        'discount_person',
        'discount_amount',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'settings' => 'array',
    ];

    /**
     * Get the court service that is assigned.
     */
    public function courtService(): BelongsTo
    {
        return $this->belongsTo(CourtService::class);
    }

    /**
     * Get the business that owns the service assignment.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the branch that the service is assigned to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Scope for active service assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Calculate final price after discount.
     */
    public function getFinalPriceAttribute()
    {
        if (!$this->discount_amount || !$this->discount_type) {
            return $this->price;
        }

        if ($this->discount_type === 'fixed') {
            return max(0, $this->price - $this->discount_amount);
        }

        if ($this->discount_type === 'percentage') {
            $discountValue = ($this->price * $this->discount_amount) / 100;
            return max(0, $this->price - $discountValue);
        }

        return $this->price;
    }
}
