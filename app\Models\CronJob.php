<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CronJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'command',
        'cron_expression',
        'description',
        'status',
        'last_run_at',
        'next_run_at',
        'execution_time',
        'memory_usage',
        'success_count',
        'failure_count',
        'timeout',
        'max_retries',
        'retry_count',
        'environment',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'execution_time' => 'decimal:3',
        'memory_usage' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'timeout' => 'integer',
        'max_retries' => 'integer',
        'retry_count' => 'integer'
    ];

    /**
     * Status constants
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_FAILED = 'failed';
    const STATUS_RUNNING = 'running';

    /**
     * Environment constants
     */
    const ENV_PRODUCTION = 'production';
    const ENV_STAGING = 'staging';
    const ENV_DEVELOPMENT = 'development';

    /**
     * Get the logs for this cron job
     */
    public function logs(): HasMany
    {
        return $this->hasMany(CronJobLog::class);
    }

    /**
     * Get recent logs (last 100)
     */
    public function recentLogs()
    {
        return $this->logs()->latest()->limit(100);
    }

    /**
     * Get error logs only
     */
    public function errorLogs()
    {
        return $this->logs()->where('level', 'error');
    }

    /**
     * Get the user who created this job
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this job
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Calculate success rate percentage
     */
    public function getSuccessRateAttribute(): float
    {
        $total = $this->success_count + $this->failure_count;
        if ($total === 0) {
            return 0;
        }
        return round(($this->success_count / $total) * 100, 2);
    }

    /**
     * Check if job is currently running
     */
    public function isRunning(): bool
    {
        return $this->status === self::STATUS_RUNNING;
    }

    /**
     * Check if job is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if job has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Mark job as running
     */
    public function markAsRunning(): void
    {
        $this->update([
            'status' => self::STATUS_RUNNING,
            'retry_count' => 0
        ]);
    }

    /**
     * Mark job as completed successfully
     */
    public function markAsCompleted(float $executionTime, int $memoryUsage): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'last_run_at' => now(),
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'success_count' => $this->success_count + 1,
            'retry_count' => 0
        ]);
    }

    /**
     * Mark job as failed
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'failure_count' => $this->failure_count + 1,
            'retry_count' => $this->retry_count + 1
        ]);

        if ($errorMessage) {
            $this->logError($errorMessage);
        }
    }

    /**
     * Log an event for this job
     */
    public function logEvent(string $level, string $message, array $context = []): CronJobLog
    {
        return $this->logs()->create([
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'created_at' => now()
        ]);
    }

    /**
     * Log an error
     */
    public function logError(string $message, array $context = []): CronJobLog
    {
        return $this->logEvent('error', $message, $context);
    }

    /**
     * Log a warning
     */
    public function logWarning(string $message, array $context = []): CronJobLog
    {
        return $this->logEvent('warning', $message, $context);
    }

    /**
     * Log an info message
     */
    public function logInfo(string $message, array $context = []): CronJobLog
    {
        return $this->logEvent('info', $message, $context);
    }

    /**
     * Log a success message
     */
    public function logSuccess(string $message, array $context = []): CronJobLog
    {
        return $this->logEvent('success', $message, $context);
    }

    /**
     * Calculate next run time based on cron expression
     */
    public function calculateNextRun(): Carbon
    {
        // This would use a cron expression parser library like mtdowling/cron-expression
        // For now, we'll return a simple calculation
        return now()->addHour();
    }

    /**
     * Scope for active jobs
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for failed jobs
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope for running jobs
     */
    public function scopeRunning($query)
    {
        return $query->where('status', self::STATUS_RUNNING);
    }
}
