<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CronJobLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'cron_job_id',
        'level',
        'message',
        'context',
        'execution_time',
        'memory_usage',
        'created_at'
    ];

    protected $casts = [
        'context' => 'array',
        'execution_time' => 'decimal:3',
        'memory_usage' => 'integer',
        'created_at' => 'datetime'
    ];

    /**
     * Log level constants
     */
    const LEVEL_INFO = 'info';
    const LEVEL_SUCCESS = 'success';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';

    /**
     * Get the cron job that owns this log
     */
    public function cronJob(): BelongsTo
    {
        return $this->belongsTo(CronJob::class);
    }

    /**
     * Scope for error logs
     */
    public function scopeErrors($query)
    {
        return $query->where('level', self::LEVEL_ERROR);
    }

    /**
     * Scope for warning logs
     */
    public function scopeWarnings($query)
    {
        return $query->where('level', self::LEVEL_WARNING);
    }

    /**
     * Scope for success logs
     */
    public function scopeSuccess($query)
    {
        return $query->where('level', self::LEVEL_SUCCESS);
    }

    /**
     * Scope for info logs
     */
    public function scopeInfo($query)
    {
        return $query->where('level', self::LEVEL_INFO);
    }

    /**
     * Get formatted timestamp
     */
    public function getFormattedTimestampAttribute(): string
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    /**
     * Get log level color for UI
     */
    public function getLevelColorAttribute(): string
    {
        return match ($this->level) {
            self::LEVEL_ERROR => '#EE0033',
            self::LEVEL_WARNING => '#FF9800',
            self::LEVEL_SUCCESS => '#4CAF50',
            self::LEVEL_INFO => '#2196F3',
            default => '#666666'
        };
    }
}
