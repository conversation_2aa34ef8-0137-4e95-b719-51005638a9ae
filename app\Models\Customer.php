<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'phone',
        'email',
        'address',
        'birth_date',
        'gender',
        'is_active',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {

    }

    /**
     * Get the user associated with the customer.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the court bookings for the customer.
     */
    public function courtBookings(): Has<PERSON>any
    {
        return $this->hasMany(CourtBooking::class);
    }

    /**
     * Alias for courtBookings() to maintain consistency with User model.
     */
    public function bookings(): HasMany
    {
        return $this->courtBookings();
    }

    /**
     * Get the reviews submitted by the customer.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the product reviews submitted by the customer.
     */
    public function marketProductReviews(): HasMany
    {
        return $this->hasMany(MarketProductReview::class);
    }

    /**
     * Get the market orders for the customer.
     */
    public function marketOrders(): HasMany
    {
        return $this->hasMany(MarketOrder::class);
    }

    /**
     * Get the market payments for the customer.
     */
    public function marketPayments(): HasMany
    {
        return $this->hasMany(MarketPayment::class);
    }
}
