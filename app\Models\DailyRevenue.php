<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DailyRevenue extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'branch_id',
        'court_id',
        'revenue_date',
        'total_bookings',
        'confirmed_bookings',
        'cancelled_bookings',
        'completed_bookings',
        'gross_revenue',
        'commission_amount',
        'net_revenue',
        'peak_hour_revenue',
        'discount_amount',
        'refund_amount'
    ];

    protected $casts = [
        'revenue_date' => 'date',
        'gross_revenue' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'net_revenue' => 'decimal:2',
        'peak_hour_revenue' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2'
    ];

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function court(): BelongsTo
    {
        return $this->belongsTo(Court::class);
    }

    /**
     * Scope a query to only include branch-level revenues (no court-specific)
     */
    public function scopeBranchLevel($query)
    {
        return $query->whereNull('court_id');
    }

    /**
     * Scope a query to only include court-level revenues
     */
    public function scopeCourtLevel($query)
    {
        return $query->whereNotNull('court_id');
    }

    /**
     * Scope a query to filter by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('revenue_date', [$startDate, $endDate]);
    }

    /**
     * Get revenue data for a specific date range
     *
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param bool $groupByDate Group results by date
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRevenueByDateRange($startDate, $endDate, $businessId = null, $branchId = null, $groupByDate = false)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate])
            ->whereNull('court_id'); // Branch level only

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        if ($groupByDate) {
            return $query->select(
                'revenue_date',
                DB::raw('SUM(gross_revenue) as gross_revenue'),
                DB::raw('SUM(net_revenue) as net_revenue'),
                DB::raw('SUM(total_bookings) as total_bookings'),
                DB::raw('SUM(completed_bookings) as completed_bookings')
            )
                ->groupBy('revenue_date')
                ->orderBy('revenue_date')
                ->get();
        }

        return $query->orderBy('revenue_date')->get();
    }

    /**
     * Calculate revenue growth between two periods
     *
     * @param string $currentStartDate Current period start date
     * @param string $currentEndDate Current period end date
     * @param string $previousStartDate Previous period start date
     * @param string $previousEndDate Previous period end date
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @return array
     */
    public static function calculateGrowth($currentStartDate, $currentEndDate, $previousStartDate, $previousEndDate, $businessId = null, $branchId = null)
    {
        // Get current period data
        $currentPeriod = self::getRevenueByDateRange($currentStartDate, $currentEndDate, $businessId, $branchId)
            ->whereNull('court_id');

        // Get previous period data
        $previousPeriod = self::getRevenueByDateRange($previousStartDate, $previousEndDate, $businessId, $branchId)
            ->whereNull('court_id');

        // Calculate totals
        $currentGrossRevenue = $currentPeriod->sum('gross_revenue');
        $previousGrossRevenue = $previousPeriod->sum('gross_revenue');

        $currentBookings = $currentPeriod->sum('total_bookings');
        $previousBookings = $previousPeriod->sum('total_bookings');

        // Calculate growth percentages
        $revenueGrowth = $previousGrossRevenue > 0
            ? (($currentGrossRevenue - $previousGrossRevenue) / $previousGrossRevenue) * 100
            : ($currentGrossRevenue > 0 ? 100 : 0);

        $bookingGrowth = $previousBookings > 0
            ? (($currentBookings - $previousBookings) / $previousBookings) * 100
            : ($currentBookings > 0 ? 100 : 0);

        return [
            'current_period' => [
                'gross_revenue' => $currentGrossRevenue,
                'total_bookings' => $currentBookings,
                'start_date' => $currentStartDate,
                'end_date' => $currentEndDate
            ],
            'previous_period' => [
                'gross_revenue' => $previousGrossRevenue,
                'total_bookings' => $previousBookings,
                'start_date' => $previousStartDate,
                'end_date' => $previousEndDate
            ],
            'growth' => [
                'revenue' => round($revenueGrowth, 2),
                'bookings' => round($bookingGrowth, 2)
            ]
        ];
    }

    /**
     * Get top performing courts by revenue
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @param int $limit Number of results to return (default 10)
     * @return \Illuminate\Support\Collection
     */
    public static function getTopCourts($startDate, $endDate, $businessId = null, $branchId = null, $limit = 10)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate])
            ->whereNotNull('court_id')
            ->with(['court:id,name,branch_id', 'branch:id,name,business_id']);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->select(
            'court_id',
            'branch_id',
            DB::raw('SUM(gross_revenue) as total_revenue'),
            DB::raw('SUM(total_bookings) as total_bookings')
        )
            ->groupBy('court_id', 'branch_id')
            ->orderByDesc('total_revenue')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'court_id' => $item->court_id,
                    'court_name' => $item->court->name ?? 'Unknown Court',
                    'branch_id' => $item->branch_id,
                    'branch_name' => $item->branch->name ?? 'Unknown Branch',
                    'revenue' => $item->total_revenue,
                    'bookings' => $item->total_bookings
                ];
            });
    }

    /**
     * Get top performing branches by revenue
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int $limit Number of results to return (default 10)
     * @return \Illuminate\Support\Collection
     */
    public static function getTopBranches($startDate, $endDate, $businessId = null, $limit = 10)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate])
            ->whereNull('court_id')
            ->with(['branch:id,name,business_id', 'business:id,name']);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        return $query->select(
            'branch_id',
            'business_id',
            DB::raw('SUM(gross_revenue) as total_revenue'),
            DB::raw('SUM(total_bookings) as total_bookings')
        )
            ->groupBy('branch_id', 'business_id')
            ->orderByDesc('total_revenue')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'branch_id' => $item->branch_id,
                    'branch_name' => $item->branch->name ?? 'Unknown Branch',
                    'business_id' => $item->business_id,
                    'business_name' => $item->business->name ?? 'Unknown Business',
                    'revenue' => $item->total_revenue,
                    'bookings' => $item->total_bookings
                ];
            });
    }

    /**
     * Get revenue statistics by time period
     *
     * @param string $period One of: daily, weekly, monthly, yearly
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @return array
     */
    public static function getStatsByPeriod($period = 'monthly', $businessId = null, $branchId = null)
    {
        $now = Carbon::now();

        switch ($period) {
            case 'daily':
                $currentStart = $now->copy()->startOfDay();
                $currentEnd = $now->copy()->endOfDay();
                $previousStart = $now->copy()->subDay()->startOfDay();
                $previousEnd = $now->copy()->subDay()->endOfDay();
                break;

            case 'weekly':
                $currentStart = $now->copy()->startOfWeek();
                $currentEnd = $now->copy()->endOfWeek();
                $previousStart = $now->copy()->subWeek()->startOfWeek();
                $previousEnd = $now->copy()->subWeek()->endOfWeek();
                break;

            case 'yearly':
                $currentStart = $now->copy()->startOfYear();
                $currentEnd = $now->copy()->endOfYear();
                $previousStart = $now->copy()->subYear()->startOfYear();
                $previousEnd = $now->copy()->subYear()->endOfYear();
                break;

            case 'monthly':
            default:
                $currentStart = $now->copy()->startOfMonth();
                $currentEnd = $now->copy()->endOfMonth();
                $previousStart = $now->copy()->subMonth()->startOfMonth();
                $previousEnd = $now->copy()->subMonth()->endOfMonth();
                break;
        }

        return self::calculateGrowth(
            $currentStart->format('Y-m-d'),
            $currentEnd->format('Y-m-d'),
            $previousStart->format('Y-m-d'),
            $previousEnd->format('Y-m-d'),
            $businessId,
            $branchId
        );
    }
}
