<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class EduCourse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'lecturer_id',
        'title',
        'short_description',
        'description',
        'slug',
        'thumbnail',
        'category',
        'level',
        'duration_hours',
        'total_lessons',
        'price',
        'original_price',
        'max_students',
        'enrolled_students',
        'rating',
        'total_reviews',
        'curriculum',
        'requirements',
        'outcomes',
        'tags',
        'is_featured',
        'is_free',
        'start_date',
        'end_date',
        'status',
        'published_at',
        'approved_by',
        'views_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'curriculum' => 'array',
        'requirements' => 'array',
        'outcomes' => 'array',
        'tags' => 'array',
        'is_featured' => 'boolean',
        'is_free' => 'boolean',
        'price' => 'float',
        'original_price' => 'float',
        'rating' => 'float',
        'start_date' => 'date',
        'end_date' => 'date',
        'published_at' => 'datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::creating(function ($course) {
            if (empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });
    }

    /**
     * Get the lecturer that owns the course.
     */
    public function lecturer(): BelongsTo
    {
        return $this->belongsTo(EduLecturer::class, 'lecturer_id');
    }

    /**
     * Get the students enrolled in this course.
     */
    public function students(): BelongsToMany
    {
        return $this->belongsToMany(EduStudent::class, 'edu_course_student', 'edu_course_id', 'edu_student_id')
            ->withTimestamps()
            ->withPivot('status', 'progress', 'completed_at');
    }

    /**
     * Get the reviews for this course.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(EduReview::class, 'edu_course_id');
    }

    /**
     * Get the user who approved the course.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include active courses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include featured courses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('status', 'active')
                    ->where('is_featured', true);
    }

    /**
     * Scope a query to only include courses by category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $category
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include courses by level.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $level
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Get the thumbnail URL.
     *
     * @return string
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail) {
            return asset('storage/' . $this->thumbnail);
        }

        return asset('images/default-course.png');
    }

    /**
     * Get the discount percentage.
     *
     * @return int|null
     */
    public function getDiscountPercentageAttribute()
    {
        if ($this->original_price && $this->price < $this->original_price) {
            return (int)(100 - ($this->price / $this->original_price * 100));
        }

        return null;
    }

    /**
     * Check if the course has available seats.
     *
     * @return bool
     */
    public function getHasAvailableSeatsAttribute()
    {
        if ($this->max_students === null) {
            return true;
        }

        return $this->enrolled_students < $this->max_students;
    }

    /**
     * Get the discount end date.
     *
     * @return string|null
     */
    public function getDiscountEndDateAttribute()
    {
        if ($this->start_date && $this->end_date) {
            return $this->end_date->format('Y-m-d');
        }

        return null;
    }
}
