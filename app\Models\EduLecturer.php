<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class EduLecturer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'short_description',
        'description',
        'achievements',
        'certifications',
        'social_links',
        'profile_image',
        'rating',
        'total_students',
        'total_courses',
        'total_reviews',
        'experience_years',
        'status',
    ];

        protected $casts = [
        'rating' => 'float',
        'total_students' => 'integer',
        'total_courses' => 'integer',
        'total_reviews' => 'integer',
        'experience_years' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function courses(): HasMany
    {
        return $this->hasMany(EduCourse::class, 'lecturer_id');
    }

    /**
     * Get students enrolled in this lecturer's courses
     */
    public function getStudentsCountAttribute()
    {
        return DB::table('edu_course_student')
            ->join('edu_courses', 'edu_course_student.edu_course_id', '=', 'edu_courses.id')
            ->where('edu_courses.lecturer_id', $this->id)
            ->distinct('edu_course_student.edu_student_id')
            ->count();
    }

    /**
     * Get reviews count for this lecturer's courses
     */
    public function getReviewsCountAttribute()
    {
        return DB::table('edu_reviews')
            ->join('edu_courses', 'edu_reviews.edu_course_id', '=', 'edu_courses.id')
            ->where('edu_courses.lecturer_id', $this->id)
            ->count();
    }

    /**
     * Get reviews for this lecturer's courses
     */
    public function reviews()
    {
        return $this->hasManyThrough(
            EduReview::class,
            EduCourse::class,
            'lecturer_id',
            'edu_course_id',
            'id',
            'id'
        );
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeFeatured($query)
    {
        return $query->where('status', 'active')
                    ->orderBy('rating', 'desc')
                    ->orderBy('total_students', 'desc');
    }

    public function getAchievementsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    public function setAchievementsAttribute($value)
    {
        $this->attributes['achievements'] = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    public function getCertificationsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    public function setCertificationsAttribute($value)
    {
        $this->attributes['certifications'] = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    public function getSocialLinksAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    public function setSocialLinksAttribute($value)
    {
        $this->attributes['social_links'] = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    public function getProfileImageUrlAttribute()
    {
        if ($this->profile_image) {
            return asset('storage/' . $this->profile_image);
        }

        if ($this->user && $this->user->profile_photo_url) {
            return $this->user->profile_photo_url;
        }

        return asset('storage/public/logo.jpg');
    }
}
