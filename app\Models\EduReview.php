<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EduReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'edu_course_id',
        'edu_student_id',
        'rating',
        'comment',
        'is_published',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_published' => 'boolean',
    ];

    /**
     * Get the course that was reviewed.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(EduCourse::class, 'edu_course_id');
    }

    /**
     * Get the student who wrote the review.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(EduStudent::class, 'edu_student_id');
    }
}
