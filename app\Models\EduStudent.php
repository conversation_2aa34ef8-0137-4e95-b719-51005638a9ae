<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EduStudent extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bio',
        'interests',
        'enrolled_courses',
        'status',
    ];

    protected $casts = [
        'interests' => 'array',
        'enrolled_courses' => 'array',
    ];

    /**
     * Get the user that owns the student profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the reviews written by the student.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(EduReview::class);
    }

    /**
     * Get the courses enrolled by the student.
     */
    public function courses()
    {
        return $this->belongsToMany(EduCourse::class, 'edu_course_student', 'edu_student_id', 'edu_course_id')
            ->withTimestamps()
            ->withPivot('status', 'progress', 'completed_at');
    }
}
