<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\CourtBooking;
use App\Models\Branch;

class HourlyRevenue extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'branch_id',
        'revenue_date',
        'hour_slot',
        'total_bookings',
        'total_revenue'
    ];

    protected $casts = [
        'revenue_date' => 'date',
        'hour_slot' => 'integer',
        'total_bookings' => 'integer',
        'total_revenue' => 'decimal:2'
    ];

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Scope a query to filter by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('revenue_date', [$startDate, $endDate]);
    }

    /**
     * Get hourly revenue data for a specific date range
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getHourlyRevenueByDateRange($startDate, $endDate, $businessId = null, $branchId = null)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate]);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->orderBy('revenue_date')->orderBy('hour_slot')->get();
    }

    /**
     * Get peak hours (hours with highest revenue) for a specific date range
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @param int $limit Number of peak hours to return (default 5)
     * @return \Illuminate\Support\Collection
     */
    public static function getPeakHours($startDate, $endDate, $businessId = null, $branchId = null, $limit = 5)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate]);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->select(
            'hour_slot',
            DB::raw('SUM(total_bookings) as total_bookings'),
            DB::raw('SUM(total_revenue) as total_revenue'),
            DB::raw('COUNT(DISTINCT revenue_date) as day_count')
        )
            ->groupBy('hour_slot')
            ->orderByDesc('total_revenue')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'hour' => $item->hour_slot,
                    'formatted_hour' => sprintf('%02d:00', $item->hour_slot),
                    'total_bookings' => $item->total_bookings,
                    'total_revenue' => $item->total_revenue,
                    'average_bookings_per_day' => $item->day_count > 0 ? round($item->total_bookings / $item->day_count, 2) : 0,
                    'average_revenue_per_day' => $item->day_count > 0 ? round($item->total_revenue / $item->day_count, 2) : 0
                ];
            });
    }

    /**
     * Get hourly distribution data for charts
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @return array
     */
    public static function getHourlyDistributionForChart($startDate, $endDate, $businessId = null, $branchId = null)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate]);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        $hourlyData = $query->select(
            'hour_slot',
            DB::raw('SUM(total_bookings) as total_bookings'),
            DB::raw('SUM(total_revenue) as total_revenue')
        )
            ->groupBy('hour_slot')
            ->orderBy('hour_slot')
            ->get();

        // Initialize arrays for chart data
        $hours = [];
        $bookings = [];
        $revenue = [];

        // Fill in data for all hours (6 to 22)
        for ($hour = 6; $hour <= 22; $hour++) {
            $hourData = $hourlyData->firstWhere('hour_slot', $hour);

            $hours[] = sprintf('%02d:00', $hour);
            $bookings[] = $hourData ? $hourData->total_bookings : 0;
            $revenue[] = $hourData ? (float) $hourData->total_revenue : 0;
        }

        return [
            'labels' => $hours,
            'bookings' => $bookings,
            'revenue' => $revenue
        ];
    }

    /**
     * Get busiest days of the week based on hourly revenue data
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int|null $businessId Filter by business ID (optional)
     * @param int|null $branchId Filter by branch ID (optional)
     * @return \Illuminate\Support\Collection
     */
    public static function getBusiestDaysOfWeek($startDate, $endDate, $businessId = null, $branchId = null)
    {
        $query = self::whereBetween('revenue_date', [$startDate, $endDate]);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->select(
            DB::raw('DAYOFWEEK(revenue_date) as day_of_week'),
            DB::raw('SUM(total_bookings) as total_bookings'),
            DB::raw('SUM(total_revenue) as total_revenue'),
            DB::raw('COUNT(DISTINCT revenue_date) as day_count')
        )
            ->groupBy('day_of_week')
            ->orderBy('day_of_week')
            ->get()
            ->map(function ($item) {
                // Convert MySQL's DAYOFWEEK (1=Sunday, 2=Monday, ..., 7=Saturday) to day names
                $dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $dayIndex = $item->day_of_week - 1;

                return [
                    'day_of_week' => $item->day_of_week,
                    'day_name' => $dayNames[$dayIndex],
                    'total_bookings' => $item->total_bookings,
                    'total_revenue' => $item->total_revenue,
                    'average_bookings_per_day' => $item->day_count > 0 ? round($item->total_bookings / $item->day_count, 2) : 0,
                    'average_revenue_per_day' => $item->day_count > 0 ? round($item->total_revenue / $item->day_count, 2) : 0
                ];
            });
    }

    /**
     * Generate hourly revenue data for a specific branch and date
     *
     * @param Branch $branch
     * @param string $date
     * @return array
     */
    public static function generateForBranchAndDate(Branch $branch, string $date)
    {
        $recordsCreated = 0;
        $recordsUpdated = 0;

        // Get all bookings for this branch and date
        $bookings = CourtBooking::where('branch_id', $branch->id)
            ->whereDate('start_time', $date)
            ->where('status', 'completed')
            ->get();

        if ($bookings->isEmpty()) {
            return [
                'created' => $recordsCreated,
                'updated' => $recordsUpdated,
            ];
        }

        // Group bookings by hour
        $bookingsByHour = $bookings->groupBy(function ($booking) {
            return Carbon::parse($booking->start_time)->format('H');
        });

        foreach ($bookingsByHour as $hour => $hourlyBookings) {
            $totalRevenue = $hourlyBookings->sum('total_price');
            $bookingCount = $hourlyBookings->count();

            // Find or create hourly revenue record
            $hourlyRevenue = self::firstOrNew([
                'business_id' => $branch->business_id,
                'branch_id' => $branch->id,
                'date' => $date,
                'hour' => (int) $hour,
            ]);

            $isNew = !$hourlyRevenue->exists;

            // Update or set the revenue data
            $hourlyRevenue->total_revenue = $totalRevenue;
            $hourlyRevenue->booking_count = $bookingCount;
            $hourlyRevenue->average_booking_value = $bookingCount > 0 ? $totalRevenue / $bookingCount : 0;
            $hourlyRevenue->save();

            if ($isNew) {
                $recordsCreated++;
            } else {
                $recordsUpdated++;
            }
        }

        return [
            'created' => $recordsCreated,
            'updated' => $recordsUpdated,
        ];
    }
}
