<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MarketPayment extends Model
{
    use HasFactory;

    protected $table = 'market_payments';

    protected $fillable = [
        'market_order_id',
        'user_id',
        'customer_id',
        'payment_method',
        'payment_type',
        'gateway',
        'amount',
        'status',
        'transaction_id',
        'reference_number',
        'transaction_date',
        'payment_details',
        'notes',
        'approved_by',
        'approved_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_date' => 'datetime',
        'approved_at' => 'datetime',
        'payment_details' => 'array',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(MarketOrder::class, 'market_order_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function getPaymentMethodConfigAttribute()
    {
        if (!$this->payment_method) {
            return null;
        }

        $settingKey = "market_{$this->payment_method}_payment";
        return app(\App\Services\SystemSettingService::class)::get($settingKey, []);
    }

    public function getPaymentMethodNameAttribute()
    {
        $methodNames = [
            'cash' => 'Thanh toán khi nhận hàng (COD)',
            'momo' => 'Ví điện tử MoMo',
            'vnpay' => 'VNPay',
            'bank_transfer' => 'Chuyển khoản ngân hàng'
        ];

        return $methodNames[$this->payment_method] ?? $this->payment_method;
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public static function generateTransactionId(): string
    {
        $prefix = 'TXN';
        $timestamp = now()->format('ymdHis');

        do {
            $randomNumber = mt_rand(1000, 9999);
            $transactionId = $prefix . $timestamp . $randomNumber;
            $exists = self::where('transaction_id', $transactionId)->exists();
        } while ($exists);

        return $transactionId;
    }

    protected static function boot()
    {
        parent::boot();


        static::creating(function ($payment) {
            if (empty($payment->transaction_id)) {
                $payment->transaction_id = self::generateTransactionId();
            }
        });
    }

    public function getApproverNameAttribute()
    {

        if (is_numeric($this->approved_by)) {
            $user = \App\Models\User::find($this->approved_by);
            return $user ? $user->name : 'Unknown User';
        }


        return $this->approved_by;
    }

    public function isApprovedByUser()
    {
        return is_numeric($this->approved_by);
    }

    public function isApprovedByGateway()
    {
        return !is_numeric($this->approved_by) && !empty($this->approved_by);
    }
}
