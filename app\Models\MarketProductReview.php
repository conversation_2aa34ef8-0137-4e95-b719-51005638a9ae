<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketProductReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'customer_id',
        'product_id',
        'market_order_id',
        'market_order_detail_id',
        'rating',
        'comment',
        'images',
        'is_verified_purchase',
        'is_published',
        'helpful_count',
        'unhelpful_count',
        'reviewed_at',
    ];

    protected $casts = [
        'images' => 'array',
        'is_verified_purchase' => 'boolean',
        'is_published' => 'boolean',
        'reviewed_at' => 'datetime',
    ];

    protected $dates = [
        'reviewed_at',
    ];


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(MarketOrder::class, 'market_order_id');
    }

    public function orderDetail(): BelongsTo
    {
        return $this->belongsTo(MarketOrderDetail::class, 'market_order_detail_id');
    }

    public function votes(): HasMany
    {
        return $this->hasMany(MarketProductReviewVote::class);
    }

    public function helpfulVotes(): HasMany
    {
        return $this->hasMany(MarketProductReviewVote::class)->where('vote_type', 'helpful');
    }

    public function unhelpfulVotes(): HasMany
    {
        return $this->hasMany(MarketProductReviewVote::class)->where('vote_type', 'unhelpful');
    }


    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeVerifiedPurchase($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }


    public function setReviewedAtAttribute($value)
    {
        $this->attributes['reviewed_at'] = $value ?: now();
    }


    public function getStarRatingAttribute()
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    public function getIsEditableAttribute()
    {
        return $this->created_at->diffInHours(now()) <= 24;
    }
}
