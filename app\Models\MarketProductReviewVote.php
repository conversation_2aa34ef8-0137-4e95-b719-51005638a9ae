<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MarketProductReviewVote extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'customer_id',
        'market_product_review_id',
        'vote_type',
    ];

    protected $casts = [
        'vote_type' => 'string',
    ];

    /**
     * Get the user that owns the vote.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the customer that owns the vote.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the product review that was voted on.
     */
    public function marketProductReview(): BelongsTo
    {
        return $this->belongsTo(MarketProductReview::class);
    }
}
