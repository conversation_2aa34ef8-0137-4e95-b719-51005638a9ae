<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ModuleProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'module_id',
        'profile_data',
        'is_active',
    ];

    protected $casts = [
        'profile_data' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function module()
    {
        return $this->belongsTo(Module::class);
    }
} 