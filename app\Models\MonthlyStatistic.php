<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MonthlyStatistic extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'branch_id',
        'year',
        'month',
        'total_bookings',
        'total_revenue',
        'total_commission',
        'average_booking_value',
        'peak_day_revenue',
        'growth_rate'
    ];

    protected $casts = [
        'year' => 'integer',
        'month' => 'integer',
        'total_bookings' => 'integer',
        'total_revenue' => 'decimal:2',
        'total_commission' => 'decimal:2',
        'average_booking_value' => 'decimal:2',
        'peak_day_revenue' => 'decimal:2',
        'growth_rate' => 'decimal:2'
    ];

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }
}
