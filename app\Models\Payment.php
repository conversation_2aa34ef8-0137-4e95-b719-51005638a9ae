<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'court_booking_id',
        'booking_reference',
        'user_id',
        'customer_id',
        'amount',
        'payment_method_id',
        'payment_method',
        'payment_type',
        'status',
        'transaction_id',
        'transaction_date',
        'payment_details',
        'payment_response',
        'reference_number',
        'reference_payment',
        'approved_by',
        'approved_at',
        'notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'payment_response' => 'array',
        'transaction_date' => 'datetime',
    ];

    /**
     * Payment statuses
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_CANCELLED = 'cancelled';

    public function courtBooking(): BelongsTo
    {
        return $this->belongsTo(CourtBooking::class, 'court_booking_id');
    }


    public function booking(): BelongsTo
    {
        return $this->belongsTo(CourtBooking::class, 'court_booking_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function marketPayment(): BelongsTo
    {
        return $this->belongsTo(MarketPayment::class, 'market_payment_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include payments with specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        if (is_array($status)) {
            return $query->whereIn('status', $status);
        }

        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Kiểm tra và hủy các đơn đặt sân đã hết hạn thanh toán
     *
     * @param string $referenceNumber Mã tham chiếu cần kiểm tra
     * @return bool Trả về true nếu đơn đã bị hủy, false nếu không
     */
    public static function checkAndCancelExpiredBookings(string $referenceNumber)
    {
        try {

            $expiredBookings = CourtBooking::where('reference_number', $referenceNumber)
                ->where('status', 'pending')
                ->whereNotNull('payment_deadline')
                ->where('payment_deadline', '<', Carbon::now())
                ->where('payment_status', 'unpaid')
                ->get();

            if ($expiredBookings->isEmpty()) {
                return false;
            }


            $hasCompletedPayment = self::where('booking_reference', $referenceNumber)
                ->where('status', self::STATUS_COMPLETED)
                ->exists();

            if ($hasCompletedPayment) {
                return false;
            }

            DB::beginTransaction();


            $allRelatedBookings = CourtBooking::where('reference_number', $referenceNumber)->get();


            foreach ($allRelatedBookings as $booking) {
                $booking->status = 'cancelled';
                $booking->cancellation_reason = 'Payment due date';
                $booking->cancelled_at = Carbon::now();


                $metadata = $booking->metadata ?? [];
                $metadata['cancellation'] = [
                    'reason' => 'Payment due date',
                    'cancelled_at' => Carbon::now()->toDateTimeString(),
                    'cancelled_by' => 'system',
                    'note' => 'Đơn hàng bị hủy tự động do quá hạn thanh toán'
                ];
                $booking->metadata = $metadata;

                $booking->save();
            }


            self::where('booking_reference', $referenceNumber)
                ->whereIn('status', [self::STATUS_PENDING, 'pending_approval', 'pending_confirmation', 'awaiting_payment'])
                ->update(['status' => self::STATUS_CANCELLED]);

            Log::info('Cancelled booking due to payment deadline', [
                'reference_number' => $referenceNumber,
                'bookings_count' => $allRelatedBookings->count()
            ]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error cancelling expired booking: ' . $e->getMessage(), [
                'reference_number' => $referenceNumber,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
}
