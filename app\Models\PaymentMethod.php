<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_name',
        'payment_code',
        'description',
        'logo_url',
        'access_key',
        'public_key',
        'secret_key',
        'payment_endpoint',
        'custom_field1',
        'custom_field2',
        'custom_field3',
        'custom_field4',
        'is_enabled',
        'display_order',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * Get the payments associated with this payment method.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope a query to only include enabled payment methods.
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Get custom field by index (1-4)
     */
    public function getCustomField(int $index)
    {
        if ($index < 1 || $index > 4) {
            return null;
        }

        return $this->{"custom_field{$index}"};
    }
}
