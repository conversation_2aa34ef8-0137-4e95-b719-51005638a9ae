<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'category_id',
        'sku',
        'barcode',
        'unit',
        'import_price',
        'sale_price',
        'alert_quantity',
        'quantity',
        'brand',
        'image_url',
        'status',
        'is_featured',
        'weight',
        'dimensions',
        'slug',
    ];

    protected $casts = [
        'import_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'alert_quantity' => 'integer',
        'quantity' => 'integer',
        'status' => 'boolean',
        'is_featured' => 'boolean',
    ];

    protected $appends = ['image_url_formatted', 'image'];

    public function getImageUrlFormattedAttribute()
    {
        if (!$this->image_url) {
            return null;
        }

        return asset('storage/' . $this->image_url);
    }


    public function getImageAttribute()
    {
        if (!$this->image_url) {
            return null;
        }


        if (str_starts_with($this->image_url, 'http')) {
            return $this->image_url;
        }


        return asset('storage/' . $this->image_url);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class);
    }

    /**
     * Get the market order details for the product.
     */
    public function marketOrderDetails(): HasMany
    {
        return $this->hasMany(MarketOrderDetail::class);
    }


    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeByCategory($query, $categoryIds)
    {
        return $query->whereIn('category_id', is_array($categoryIds) ? $categoryIds : [$categoryIds]);
    }

    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeByPriceRange($query, $min = null, $max = null)
    {
        if ($min !== null && $min > 0) {
            $query->where('sale_price', '>=', $min);
        }
        if ($max !== null && $max > 0) {
            $query->where('sale_price', '<=', $max);
        }
        return $query;
    }

    public function scopeFeaturedFirst($query)
    {
        return $query->orderBy('is_featured', 'desc')->orderBy('created_at', 'desc');
    }

    /**
     * Get bundles that include this product
     */
    public function bundles()
    {
        return $this->belongsToMany(ProductBundle::class, 'product_bundle_items', 'product_id', 'bundle_id')
            ->withPivot(['quantity', 'item_price', 'sort_order', 'product_options'])
            ->withTimestamps();
    }

    /**
     * Get bundle items for this product
     */
    public function bundleItems()
    {
        return $this->hasMany(ProductBundleItem::class, 'product_id');
    }

    /**
     * Check if product is part of any active bundles
     */
    public function hasActiveBundles(): bool
    {
        return $this->bundles()
            ->where('is_active', true)
            ->whereNull('deleted_at')
            ->exists();
    }
}
