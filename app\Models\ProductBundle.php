<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ProductBundle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'slug',
        'image_url',
        'original_price',
        'bundle_price',
        'discount_amount',
        'discount_percentage',
        'is_active',
        'is_featured',
        'sort_order',
        'starts_at',
        'expires_at',
        'stock_quantity',
        'meta_data',
    ];

    protected $casts = [
        'original_price' => 'decimal:2',
        'bundle_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'meta_data' => 'array',
    ];

    protected $appends = ['items_count', 'savings', 'savings_percentage', 'image_url_formatted', 'image', 'total_price'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($bundle) {
            if (empty($bundle->slug)) {
                $bundle->slug = Str::slug($bundle->name);
            }
        });

        static::updating(function ($bundle) {
            if ($bundle->isDirty('name') && empty($bundle->slug)) {
                $bundle->slug = Str::slug($bundle->name);
            }
        });
    }

    /**
     * Get bundle items (products in this bundle)
     */
    public function bundleItems(): HasMany
    {
        return $this->hasMany(ProductBundleItem::class, 'bundle_id');
    }

    /**
     * Alias for bundleItems - for compatibility
     */
    public function items(): HasMany
    {
        return $this->bundleItems();
    }

    /**
     * Get products in this bundle through bundle items
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_bundle_items', 'bundle_id', 'product_id')
            ->withPivot(['quantity', 'item_price', 'sort_order', 'product_options'])
            ->withTimestamps()
            ->orderBy('product_bundle_items.sort_order');
    }

    /**
     * Check if bundle is currently active and valid
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        if ($this->starts_at && $now->lt($this->starts_at)) {
            return false;
        }

        if ($this->expires_at && $now->gt($this->expires_at)) {
            return false;
        }

        return true;
    }

    /**
     * Check if bundle has sufficient stock
     */
    public function hasStock(int $quantity = 1): bool
    {
        if ($this->stock_quantity <= 0) {
            return false;
        }

        return $this->stock_quantity >= $quantity;
    }

    /**
     * Calculate savings amount
     */
    public function getSavingsAttribute(): float
    {
        return $this->original_price - $this->bundle_price;
    }

    /**
     * Calculate savings percentage
     */
    public function getSavingsPercentageAttribute(): float
    {
        if ($this->original_price <= 0) {
            return 0;
        }

        return round((($this->original_price - $this->bundle_price) / $this->original_price) * 100, 2);
    }

    /**
     * Scope: Active bundles only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Featured bundles only
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope: Currently valid bundles (within date range)
     */
    public function scopeCurrentlyValid($query)
    {
        $now = now();

        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')
                  ->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>=', $now);
            });
    }

    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Calculate total quantity of all products in bundle
     */
    public function getTotalProductsCountAttribute(): int
    {
        return $this->bundleItems->sum('quantity');
    }

    /**
     * Get items count for compatibility
     */
    public function getItemsCountAttribute(): int
    {
        return $this->bundleItems->count();
    }

    /**
     * Get formatted image URL
     */
    public function getImageUrlFormattedAttribute()
    {
        if (!$this->image_url) {
            return null;
        }

        return asset('storage/' . $this->image_url);
    }

    /**
     * Get image accessor for compatibility
     */
    public function getImageAttribute()
    {
        if (!$this->image_url) {
            return null;
        }

        if (str_starts_with($this->image_url, 'http')) {
            return $this->image_url;
        }

        return asset('storage/' . $this->image_url);
    }

    public function getTotalPriceAttribute()
    {
        return $this->bundle_price;
    }

    public function updatePricing(): void
    {
        $totalOriginalPrice = 0;

        foreach ($this->bundleItems as $item) {
            $product = $item->product;
            if ($product) {
                $totalOriginalPrice += $product->sale_price * $item->quantity;
                $item->update(['item_price' => $product->sale_price]);
            }
        }

        $this->update([
            'original_price' => $totalOriginalPrice,
            'discount_amount' => $totalOriginalPrice - $this->bundle_price,
            'discount_percentage' => $totalOriginalPrice > 0
                ? round((($totalOriginalPrice - $this->bundle_price) / $totalOriginalPrice) * 100, 2)
                : 0,
        ]);
    }
}
