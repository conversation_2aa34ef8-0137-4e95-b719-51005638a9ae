<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductBundleItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'bundle_id',
        'product_id',
        'quantity',
        'item_price',
        'sort_order',
        'product_options',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'item_price' => 'decimal:2',
        'sort_order' => 'integer',
        'product_options' => 'array',
    ];

    /**
     * Get the bundle that owns this item
     */
    public function bundle(): BelongsTo
    {
        return $this->belongsTo(ProductBundle::class, 'bundle_id');
    }

    /**
     * Get the product for this bundle item
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Calculate total price for this item (item_price * quantity)
     */
    public function getTotalPriceAttribute(): float
    {
        return $this->item_price * $this->quantity;
    }

    /**
     * Get formatted product options
     */
    public function getFormattedOptionsAttribute(): string
    {
        if (empty($this->product_options)) {
            return '';
        }

        $options = [];
        foreach ($this->product_options as $key => $value) {
            $options[] = ucfirst($key) . ': ' . $value;
        }

        return implode(', ', $options);
    }

    /**
     * Check if this item has sufficient stock
     */
    public function hasStock(): bool
    {
        if (!$this->product) {
            return false;
        }

        return $this->product->quantity >= $this->quantity;
    }

    /**
     * Calculate savings for this specific item
     */
    public function getSavingsAttribute(): float
    {
        if (!$this->product) {
            return 0;
        }

        $currentPrice = $this->product->sale_price * $this->quantity;
        $bundlePrice = $this->item_price * $this->quantity;

        return max(0, $currentPrice - $bundlePrice);
    }

    /**
     * Scope: Items with available stock
     */
    public function scopeWithStock($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->where('quantity', '>', 0);
        });
    }

    /**
     * Scope: Items ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
