<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Province extends Model
{
    use HasFactory;

    protected $fillable = [
        'province_code',
        'name',
        'short_name',
        'code',
        'place_type',
        'country',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the wards for the province.
     */
    public function wards()
    {
        return $this->hasMany(Ward::class, 'province_code', 'province_code');
    }

    /**
     * Get province by province code
     */
    public static function findByCode($provinceCode)
    {
        return static::where('province_code', $provinceCode)->first();
    }

    /**
     * Scope to filter by country
     */
    public function scopeByCountry($query, $country = 'VN')
    {
        return $query->where('country', $country);
    }

    /**
     * Scope to filter by place type
     */
    public function scopeByPlaceType($query, $placeType)
    {
        return $query->where('place_type', $placeType);
    }
}