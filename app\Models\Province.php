<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Province extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'alias',
        'lat',
        'lng'
    ];

    /**
     * Get the districts for the province.
     */
    public function districts()
    {
        return $this->hasMany(District::class);
    }
}