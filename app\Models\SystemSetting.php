<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key_setting',
        'setting_value',
        'group',
        'display_name',
        'type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get a system setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue($key, $default = null)
    {
        $setting = self::where('key_setting', $key)->first();
        return $setting ? $setting->setting_value : $default;
    }

    /**
     * Set a system setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $group
     * @param string|null $displayName
     * @param string $type
     * @return SystemSetting
     */
    public static function setValue($key, $value, $group = null, $displayName = null, $type = 'text')
    {
        $setting = self::updateOrCreate(
            ['key_setting' => $key],
            [
                'setting_value' => $value,
                'group' => $group,
                'display_name' => $displayName,
                'type' => $type
            ]
        );

        return $setting;
    }

    /**
     * Get all settings by group
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup($group)
    {
        return self::where('group', $group)->get();
    }

    /**
     * Get setting value with proper type casting
     */
    public function getValueAttribute()
    {
        switch ($this->type) {
            case 'json':
                return json_decode($this->setting_value, true);
            case 'boolean':
                return filter_var($this->setting_value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($this->setting_value) ? (float) $this->setting_value : null;
            default:
                return $this->setting_value;
        }
    }

    /**
     * Set setting value with proper type conversion
     */
    public function setValueAttribute($value)
    {
        switch ($this->type) {
            case 'json':
                $this->setting_value = is_array($value) || is_object($value)
                    ? json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    : $value;
                break;
            case 'boolean':
                $this->setting_value = $value ? '1' : '0';
                break;
            case 'number':
                $this->setting_value = (string) $value;
                break;
            default:
                $this->setting_value = $value;
        }
    }
}
