<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Tournament extends Model
{
    use HasFactory;

    /**
     * Tournament status constants
     */
    public const STATUS_UPCOMING = 'upcoming';
    public const STATUS_ONGOING = 'ongoing';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_CANCELLED = 'cancelled';

    public const STATUS_PENDING_APPROVAL = 'pending_approval';  

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'title',
        'description',
        'image_url',
        'start_date',
        'end_date',
        'location',
        'participants_limit',
        'registration_deadline',
        'entry_fee',
        'prize_money',
        'status',
        'categories',
        'organizer',
        'rating',
        'review_count',
        'featured',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'registration_deadline' => 'date',
        'entry_fee' => 'decimal:2',
        'prize_money' => 'decimal:2',
        'rating' => 'decimal:2',
        'review_count' => 'integer',
        'participants_limit' => 'integer',
        'featured' => 'boolean',
        'categories' => 'array',
        'metadata' => 'array',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array
     */
    protected $appends = ['formatted_image_url', 'is_registration_open', 'days_until_start', 'is_system_wide', 'is_business_tournament', 'tournament_type'];

    /**
     * Get the formatted image URL.
     */
    public function getFormattedImageUrlAttribute(): string
    {
        if ($this->image_url) {
            return asset('storage/' . $this->image_url);
        }
        return asset('images/tournament-placeholder.jpg');
    }

    /**
     * Check if registration is still open.
     */
    public function getIsRegistrationOpenAttribute(): bool
    {
        return $this->registration_deadline >= Carbon::today() &&
            $this->status === self::STATUS_UPCOMING;
    }

    /**
     * Get days until tournament starts.
     */
    public function getDaysUntilStartAttribute(): int
    {
        if ($this->start_date <= Carbon::today()) {
            return 0;
        }
        return Carbon::today()->diffInDays($this->start_date);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter featured tournaments.
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope to filter upcoming tournaments.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('status', self::STATUS_UPCOMING)
            ->where('start_date', '>=', Carbon::today());
    }

    /**
     * Scope to filter ongoing tournaments.
     */
    public function scopeOngoing($query)
    {
        return $query->where('status', self::STATUS_ONGOING)
            ->where('start_date', '<=', Carbon::today())
            ->where('end_date', '>=', Carbon::today());
    }

    /**
     * Scope to filter completed tournaments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED)
            ->orWhere('end_date', '<', Carbon::today());
    }

    /**
     * Scope to search tournaments by title or location.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
                ->orWhere('location', 'like', "%{$search}%")
                ->orWhere('organizer', 'like', "%{$search}%");
        });
    }

    /**
     * Get all available tournament statuses.
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_UPCOMING => 'Sắp diễn ra',
            self::STATUS_ONGOING => 'Đang diễn ra',
            self::STATUS_COMPLETED => 'Đã kết thúc',
            self::STATUS_CANCELLED => 'Đã hủy',
            self::STATUS_PENDING_APPROVAL => 'Chờ duyệt',
        ];
    }

    /**
     * Get status label in Vietnamese.
     */
    public function getStatusLabelAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? $this->status;
    }

    /**
     * Get the business that owns the tournament.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Scope to filter tournaments by business.
     */
    public function scopeByBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter system-wide tournaments (no business).
     */
    public function scopeSystemWide($query)
    {
        return $query->whereNull('business_id');
    }

    /**
     * Scope to filter business tournaments (has business).
     */
    public function scopeBusinessTournaments($query)
    {
        return $query->whereNotNull('business_id');
    }

    /**
     * Check if tournament is system-wide.
     */
    public function getIsSystemWideAttribute(): bool
    {
        return is_null($this->business_id);
    }

    /**
     * Check if tournament is organized by a business.
     */
    public function getIsBusinessTournamentAttribute(): bool
    {
        return !is_null($this->business_id);
    }

    /**
     * Get tournament type label.
     */
    public function getTournamentTypeAttribute(): string
    {
        return $this->is_system_wide ? 'Giải đấu toàn hệ thống' : 'Giải đấu doanh nghiệp';
    }
}
