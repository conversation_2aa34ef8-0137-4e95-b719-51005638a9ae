<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Business;
use App\Models\Branch;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use App\Models\CourtBooking;
use App\Models\MarketOrder;
use App\Models\MarketPayment;
use App\Models\EduLecturer;
use App\Models\EduStudent;
use App\Services\SystemSettingService;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar_url',
        'bio',
        'is_verified',
        'notification_preferences',
        'status',
        'role',
        'active',
        'business_id',
        'branch_id',
        'provider',
        'provider_id',
        'provider_avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_verified' => 'boolean',
        'notification_preferences' => 'array',
        'last_login_at' => 'datetime',

    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {

    }

    /**
     * Get roles for a specific business.
     */
    public function businessRoles(int $business_id): BelongsToMany
    {
        return $this->roles()->wherePivot('business_id', $business_id);
    }

    /**
     * Get roles for a specific branch.
     */
    public function branchRoles(int $branch_id): BelongsToMany
    {
        return $this->roles()->wherePivot('branch_id', $branch_id);
    }

    /**
     * Check if user has the given role for specific business or branch.
     *
     * Note: This method extends Spatie's role checking functionality.
     * Use the standard hasRole() from Spatie for general role checks.
     */
    public function hasContextRole(string $role_name, ?int $business_id = null, ?int $branch_id = null): bool
    {
        $query = $this->roles()->where('name', $role_name);

        if ($business_id) {
            $query->wherePivot('business_id', $business_id);
        }

        if ($branch_id) {
            $query->wherePivot('branch_id', $branch_id);
        }

        return $query->exists();
    }

    /**
     * Check if user has any of the given roles for specific business or branch.
     *
     * Note: This method extends Spatie's role checking functionality.
     * Use the standard hasAnyRole() from Spatie for general role checks.
     */
    public function hasAnyContextRole(array $role_names, ?int $business_id = null, ?int $branch_id = null): bool
    {
        $query = $this->roles()->whereIn('name', $role_names);

        if ($business_id) {
            $query->wherePivot('business_id', $business_id);
        }

        if ($branch_id) {
            $query->wherePivot('branch_id', $branch_id);
        }

        return $query->exists();
    }

    public function moduleProfiles()
    {
        return $this->hasMany(ModuleProfile::class);
    }

    /**
     * Get the business that owns the user.
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the branch that owns the user.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the customer record associated with the user.
     */
    public function customer()
    {
        return $this->hasOne(Customer::class);
    }

    /**
     * Get the bookings associated with the user.
     */
    public function bookings()
    {
        return $this->hasMany(CourtBooking::class);
    }

    /**
     * Get the market orders for the user.
     */
    public function marketOrders()
    {
        return $this->hasMany(MarketOrder::class);
    }

    /**
     * Get the market payments for the user.
     */
    public function marketPayments()
    {
        return $this->hasMany(MarketPayment::class);
    }

    /**
     * User has many coupon usages.
     */
    public function couponUsages()
    {
        return $this->hasMany(MarketCouponUsage::class, 'user_id');
    }

    /**
     * Get the product reviews submitted by the user.
     */
    public function marketProductReviews()
    {
        return $this->hasMany(MarketProductReview::class);
    }

    /**
     * Get all businesses where the user has any role.
     */
    public function getBusinesses()
    {
        return Business::whereIn('id', $this->businessBranchRoles()->pluck('business_id')->unique());
    }

    /**
     * Get all branches where the user has any role.
     */
    public function getBranches(?int $business_id = null)
    {
        $query = $this->businessBranchRoles();

        if ($business_id) {
            $query->where('business_id', $business_id);
        }

        return Branch::whereIn('id', $query->whereNotNull('branch_id')->pluck('branch_id')->unique());
    }

    public function hasModuleAccess($moduleSlug)
    {
        return $this->moduleProfiles()
            ->whereHas('module', function ($query) use ($moduleSlug) {
                $query->where('slug', $moduleSlug)
                    ->where('is_active', true);
            })
            ->where('is_active', true)
            ->exists();
    }

    public function hasModuleRole($moduleSlug, $roleName)
    {
        return $this->roles()
            ->whereHas('module', function ($query) use ($moduleSlug) {
                $query->where('slug', $moduleSlug);
            })
            ->where('name', $roleName)
            ->exists();
    }

    public function hasModulePermission($moduleSlug, $permissionName)
    {
        return $this->roles()
            ->whereHas('module', function ($query) use ($moduleSlug) {
                $query->where('slug', $moduleSlug);
            })
            ->whereHas('permissions', function ($query) use ($permissionName) {
                $query->where('name', $permissionName);
            })
            ->exists();
    }

    /**
     * Scope a query to filter users with search, role, business, and branch filters
     * and include their business and branch information.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $filters Array of filters (search, role, business, branch)
     * @param int|null $perPage Number of results per page (null for no pagination)
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function scopeWithFilteredSearch($query, array $filters = [], $perPage = 10)
    {
        $query->when(isset($filters['search']) && $filters['search'], function ($query, $search) use ($filters) {
            $query->where(function ($q) use ($filters) {
                $search = $filters['search'];
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        })
            ->when(isset($filters['role']) && $filters['role'], function ($query) use ($filters) {
                $query->whereHas('roles', function ($q) use ($filters) {
                    $q->where('id', $filters['role']);
                });
            })
            ->when(isset($filters['business']) && $filters['business'], function ($query) use ($filters) {
                $query->where('business_id', $filters['business']);
            })
            ->when(isset($filters['branch']) && $filters['branch'], function ($query) use ($filters) {
                $query->where('branch_id', $filters['branch']);
            })
            ->with([
                'roles',
                'business' => function ($query) {
                    $query->select('id', 'name');
                },
                'branch' => function ($query) {
                    $query->select('id', 'name', 'business_id');
                }
            ]);

        $query->orderBy('created_at', 'desc');

        if ($perPage) {
            return $query->paginate($perPage)->withQueryString();
        }

        return $query->get();
    }

    /**
     * Add business and branch information to a paginated collection of users
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator $paginatedUsers
     * @return void
     */
    protected function addBusinessAndBranchInfo($paginatedUsers)
    {
        $paginatedUsers->getCollection()->transform(function ($user) {
            return $this->addBusinessAndBranchInfoToUser($user);
        });
    }

    /**
     * Add business and branch information to a collection of users
     *
     * @param \Illuminate\Database\Eloquent\Collection $users
     * @return void
     */
    protected function addBusinessAndBranchInfoToCollection($users)
    {
        $users->transform(function ($user) {
            return $this->addBusinessAndBranchInfoToUser($user);
        });
    }

    /**
     * Add business and branch information to a single user
     *
     * @param \App\Models\User $user
     * @return \App\Models\User
     */
    protected function addBusinessAndBranchInfoToUser($user)
    {

        if (!$user->roles || $user->roles->isEmpty()) {
            $user->businesses = [];
            $user->branches = [];
            return $user;
        }

        $hasBusinessInfo = isset($user->roles->first()->pivot->business_id);
        $hasBranchInfo = isset($user->roles->first()->pivot->branch_id);

        if ($hasBusinessInfo) {

            $businessIds = $user->roles->pluck('pivot.business_id')->filter()->unique()->values();

            $businessDetails = [];
            if ($businessIds->isNotEmpty()) {
                $businessDetails = Business::whereIn('id', $businessIds)
                    ->select('id', 'name')
                    ->get()
                    ->toArray();
            }
            $user->businesses = $businessDetails;
        } else {

            $user->businesses = [];
        }

        if ($hasBranchInfo) {

            $branchIds = $user->roles->pluck('pivot.branch_id')->filter()->unique()->values();

            $branchDetails = [];
            if ($branchIds->isNotEmpty()) {
                $branchDetails = Branch::whereIn('id', $branchIds)
                    ->select('id', 'name', 'business_id')
                    ->get()
                    ->toArray();
            }
            $user->branches = $branchDetails;
        } else {

            $user->branches = [];
        }

        return $user;
    }

    /**
     * Get the user's roles with business and branch context.
     *
     * @param int|null $businessId Filter by business ID
     * @param int|null $branchId Filter by branch ID
     * @return \Illuminate\Support\Collection
     */
    public function getContextRoles(?int $businessId = null, ?int $branchId = null)
    {
        $query = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_id', $this->id)
            ->where('model_has_roles.model_type', get_class($this))
            ->select([
                'roles.id',
                'roles.name',
                'roles.guard_name',
                'model_has_roles.business_id',
                'model_has_roles.branch_id'
            ]);

        if ($businessId !== null) {
            $query->where('model_has_roles.business_id', $businessId);
        }

        if ($branchId !== null) {
            $query->where('model_has_roles.branch_id', $branchId);
        }

        return $query->get();
    }

    /**
     * Get all businesses where the user has any role with business context.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getContextBusinesses()
    {
        $businessIds = DB::table('model_has_roles')
            ->where('model_id', $this->id)
            ->where('model_type', get_class($this))
            ->whereNotNull('business_id')
            ->pluck('business_id')
            ->unique();

        return Business::whereIn('id', $businessIds)->get();
    }

    /**
     * Get all branches where the user has any role with branch context.
     *
     * @param int|null $businessId Filter by business ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getContextBranches(?int $businessId = null)
    {
        $query = DB::table('model_has_roles')
            ->where('model_id', $this->id)
            ->where('model_type', get_class($this))
            ->whereNotNull('branch_id');

        if ($businessId !== null) {
            $query->where('business_id', $businessId);
        }

        $branchIds = $query->pluck('branch_id')->unique();

        return Branch::whereIn('id', $branchIds)->get();
    }

    /**
     * Check if the user has a specific role in a business/branch context.
     *
     * @param string $roleName The role name to check
     * @param int|null $businessId The business ID to check context for
     * @param int|null $branchId The branch ID to check context for
     * @return bool
     */
    public function hasRoleInContext(string $roleName, ?int $businessId = null, ?int $branchId = null): bool
    {
        $query = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_id', $this->id)
            ->where('model_has_roles.model_type', get_class($this))
            ->where('roles.name', $roleName);

        if ($businessId !== null) {
            $query->where('model_has_roles.business_id', $businessId);
        }

        if ($branchId !== null) {
            $query->where('model_has_roles.branch_id', $branchId);
        }

        return $query->exists();
    }

    /**
     * Get all role names for a user from user_business_branch.
     *
     * @return array
     */
    public function getUserRoles(): array
    {
        try {
            $roles = DB::table('user_business_branch')
                ->join('roles', 'user_business_branch.role_id', '=', 'roles.id')
                ->where('user_business_branch.user_id', $this->id)
                ->pluck('roles.name')
                ->toArray();

            return $roles;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get the profile photo URL attribute.
     *
     * @return string
     */
    public function getProfilePhotoUrlAttribute()
    {
        if ($this->provider_avatar) {
            return $this->provider_avatar;
        }

        if ($this->avatar_url) {
            return $this->avatar_url;
        }

        $name = trim($this->name);
        $firstLetter = $name ? strtoupper(substr($name, 0, 1)) : 'U';

        $hash = md5($name ?: 'user');
        $hue = hexdec(substr($hash, 0, 2)) % 360;
        $saturation = 80;
        $lightness = 45;

        $color = "hsl($hue, $saturation%, $lightness%)";

        return "https://ui-avatars.com/api/?name=$firstLetter&background=" . urlencode($color) . "&color=fff&size=256";
    }

    /**
     * Get the lecturer profile associated with the user.
     */
    public function eduLecturer()
    {
        return $this->hasOne(EduLecturer::class);
    }

    /**
     * Get the student profile associated with the user.
     */
    public function eduStudent()
    {
        return $this->hasOne(EduStudent::class);
    }

    /**
     * Check if user is a student.
     *
     * @return bool
     */
    public function isStudent()
    {
        return $this->eduStudent()->where('status', 'active')->exists();
    }

    /**
     * Check if user is a lecturer.
     *
     * @return bool
     */
    public function isLecturer()
    {
        return $this->eduLecturer()->where('status', 'active')->exists();
    }

    /**
     * Check if user has a pending lecturer profile.
     *
     * @return bool
     */
    public function hasPendingLecturerProfile()
    {
        return $this->eduLecturer()->where('status', 'pending_approval')->exists();
    }
}
