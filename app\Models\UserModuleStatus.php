<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserModuleStatus extends Model
{
    protected $fillable = [
        'user_id',
        'module',
        'is_active',
        'status',
        'reason',
        'suspended_until'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'suspended_until' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isSuspended()
    {
        if ($this->status !== 'suspended') {
            return false;
        }

        if (!$this->suspended_until) {
            return true;
        }

        return now()->lessThan($this->suspended_until);
    }

    public function isBanned()
    {
        return $this->status === 'banned';
    }

    public function isActive()
    {
        return $this->is_active && !$this->isSuspended() && !$this->isBanned();
    }
}