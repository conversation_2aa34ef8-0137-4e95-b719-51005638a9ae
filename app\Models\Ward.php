<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ward extends Model
{
    use HasFactory;

    protected $fillable = [
        'ward_code',
        'name',
        'province_code',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the province that owns the ward.
     */
    public function province()
    {
        return $this->belongsTo(Province::class, 'province_code', 'province_code');
    }

    /**
     * Get ward by ward code
     */
    public static function findByCode($wardCode)
    {
        return static::where('ward_code', $wardCode)->first();
    }

    /**
     * Scope to filter by province code
     */
    public function scopeByProvince($query, $provinceCode)
    {
        return $query->where('province_code', $provinceCode);
    }

    /**
     * Scope to search by name
     */
    public function scopeSearchByName($query, $name)
    {
        return $query->where('name', 'like', '%' . $name . '%');
    }
}