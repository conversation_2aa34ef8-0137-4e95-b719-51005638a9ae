<?php

namespace App\Observers;

use App\Models\BranchImage;
use Illuminate\Support\Facades\Storage;

class BranchImageObserver
{
    /**
     * Handle the BranchImage "created" event.
     */
    public function created(BranchImage $branchImage): void
    {
        $this->updateBranchMainImage($branchImage);
    }

    /**
     * Handle the BranchImage "updated" event.
     */
    public function updated(BranchImage $branchImage): void
    {
        $this->updateBranchMainImage($branchImage);
    }

    /**
     * Update branch main_image_url if image is marked as main
     */
    private function updateBranchMainImage(BranchImage $branchImage): void
    {
        if ($branchImage->is_main) {
            $branch = $branchImage->branch;
            $branch->main_image_url = Storage::url($branchImage->image_url);
            $branch->save();
        }
    }
}
