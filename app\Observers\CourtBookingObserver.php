<?php

namespace App\Observers;

use App\Models\CourtBooking;
use App\Models\Booking;

class CourtBookingObserver
{
    /**
     * Handle the CourtBooking "created" event.
     */
    public function created(CourtBooking $courtBooking): void
    {
        $this->syncBookingDate($courtBooking);
    }

    /**
     * Handle the CourtBooking "updated" event.
     */
    public function updated(CourtBooking $courtBooking): void
    {
        // Only sync if booking_date was changed
        if ($courtBooking->isDirty('booking_date')) {
            $this->syncBookingDate($courtBooking);
        }
    }

    /**
     * Handle the CourtBooking "deleted" event.
     */
    public function deleted(CourtBooking $courtBooking): void
    {
        $this->syncBookingDate($courtBooking);
    }

    /**
     * Handle the CourtBooking "restored" event.
     */
    public function restored(CourtBooking $courtBooking): void
    {
        $this->syncBookingDate($courtBooking);
    }

    /**
     * Handle the CourtBooking "force deleted" event.
     */
    public function forceDeleted(CourtBooking $courtBooking): void
    {
        $this->syncBookingDate($courtBooking);
    }

    /**
     * Synchronize booking_date from CourtBooking to Booking
     */
    private function syncBookingDate(CourtBooking $courtBooking): void
    {
        if ($courtBooking->reference_number) {
            Booking::syncBookingDate($courtBooking->reference_number);
        }
    }
}
