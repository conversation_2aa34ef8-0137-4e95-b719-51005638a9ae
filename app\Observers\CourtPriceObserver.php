<?php

namespace App\Observers;

use App\Models\CourtPrice;

class CourtPriceObserver
{
    protected function updateBranchPrices($courtPrice)
    {
        if ($courtPrice->branch_id) {
            $prices = CourtPrice::where('branch_id', $courtPrice->branch_id)
                ->where('price_type', 'normal')
                ->where('is_active', true)
                ->pluck('price_per_hour');

            $courtPrice->branch()->update([
                'min_price' => $prices->min(),
                'max_price' => $prices->max()
            ]);
        }
    }

    public function created(CourtPrice $courtPrice)
    {
        $this->updateBranchPrices($courtPrice);
    }

    public function updated(CourtPrice $courtPrice)
    {
        $this->updateBranchPrices($courtPrice);
    }

    public function deleted(CourtPrice $courtPrice)
    {
        $this->updateBranchPrices($courtPrice);
    }
}