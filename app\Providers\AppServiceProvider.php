<?php

namespace App\Providers;

use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;
use App\Models\BranchImage;
use App\Observers\BranchImageObserver;
use Illuminate\Support\Facades\App;
use Laravel\Socialite\Facades\Socialite;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Explicitly register the BroadcastServiceProvider to ensure it's loaded
        $this->app->register(BroadcastServiceProvider::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        BranchImage::observe(BranchImageObserver::class);

        Vite::prefetch(concurrency: 3);

        // Register custom Facebook provider with API v18.0
        $this->registerFacebookProvider();

        // Share translations with the frontend using the global window.translations object
        $locale = app()->getLocale();

        // Share translations with Inertia
        Inertia::share([
            'locale' => function () use ($locale) {
                return $locale;
            },
            'translations' => function () use ($locale) {
                $translations = [];

                $langPath = base_path("resources/lang/{$locale}");
                if (is_dir($langPath)) {
                    $langFiles = glob("{$langPath}/*.php");
                    foreach ($langFiles as $file) {
                        $name = basename($file, '.php');
                        $translations[$name] = require $file;
                    }
                }

                return $translations;
            }
        ]);

        // Conditionally enable email verification based on system settings
        $this->configureEmailVerification();
    }

    /**
     * Register the custom Facebook provider.
     *
     * @return void
     */
    protected function registerFacebookProvider(): void
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend('facebook', function ($app) use ($socialite) {
            $config = $app['config']['services.facebook'];
            return $socialite->buildProvider(FacebookProvider::class, $config);
        });
    }

    /**
     * Configure email verification based on system settings
     */
    private function configureEmailVerification(): void
    {
        try {
            // Check if email_verification setting is enabled (set to 'on')
            $emailVerificationEnabled = \App\Services\SystemSettingService::get('email_verification') === 'on';

            // If verification is not enabled, add a macro to the User model to skip verification
            if (!$emailVerificationEnabled) {
                \Illuminate\Foundation\Auth\User::macro('hasVerifiedEmail', function () {
                    return true;
                });
            }
        } catch (\Exception $e) {
            // If there's an error (e.g. settings table doesn't exist yet during migrations)
            // Default to not requiring verification
            \Illuminate\Foundation\Auth\User::macro('hasVerifiedEmail', function () {
                return true;
            });
        }
    }
}
