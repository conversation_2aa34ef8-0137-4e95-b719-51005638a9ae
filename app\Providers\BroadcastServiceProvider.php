<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Enable broadcasting với auth middleware thông thường
        Broadcast::routes(['middleware' => ['web', 'auth']]);

        // Register channel authorization
        require base_path('routes/channels.php');
    }
}