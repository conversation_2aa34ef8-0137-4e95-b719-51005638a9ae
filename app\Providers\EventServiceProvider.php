<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\CourtPrice;
use App\Observers\CourtPriceObserver;
use App\Models\CourtBooking;
use App\Observers\CourtBookingObserver;

class EventServiceProvider extends ServiceProvider
{
    public function boot()
    {
        CourtPrice::observe(CourtPriceObserver::class);
        CourtBooking::observe(CourtBookingObserver::class);
    }
}