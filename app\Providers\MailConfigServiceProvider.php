<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;
use App\Services\MailService;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Add a macro to send mail using business configuration
        Mail::macro('sendWithBusinessConfig', function ($businessId, $to, $mailable) {
            return MailService::sendUsingBusinessConfig($businessId, $to, $mailable);
        });

        // Add a macro to send mail using branch configuration
        Mail::macro('sendWithBranchConfig', function ($branchId, $to, $mailable) {
            return MailService::sendUsingBranchConfig($branchId, $to, $mailable);
        });
    }
}