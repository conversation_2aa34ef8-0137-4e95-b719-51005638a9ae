<?php

namespace App\Services;

use App\Models\Province;
use App\Models\District;
use App\Models\Ward;
use Illuminate\Support\Facades\Cache;

class AddressService
{
    /**
     * Thời gian lưu cache (1 ngày)
     */
    const CACHE_TTL = 86400;

    public function getProvinces(): array
    {
        return Cache::remember('provinces_list', self::CACHE_TTL, function () {
            return Province::select('id', 'name', 'alias', 'lat', 'lng')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }

    public function getDistrictsByProvince($provinceId): array
    {
        $cacheKey = "districts_by_province_{$provinceId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceId) {
            return District::where('province_id', $provinceId)
                ->select('id', 'name', 'alias', 'lat', 'lng')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }

    public function getWardsByDistrict($districtId): array
    {
        $cacheKey = "wards_by_district_{$districtId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($districtId) {
            return Ward::where('district_id', $districtId)
                ->select('id', 'name', 'alias', 'lat', 'lng')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }

    public function getProvinceNameById($provinceId): ?string
    {
        if (!$provinceId)
            return null;

        $cacheKey = "province_name_{$provinceId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceId) {
            $province = Province::find($provinceId);
            return $province ? $province->name : null;
        });
    }

    public function getDistrictNameById($districtId, $provinceId = null): ?string
    {
        if (!$districtId)
            return null;

        $cacheKey = "district_name_{$districtId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($districtId) {
            $district = District::find($districtId);
            return $district ? $district->name : null;
        });
    }

    public function getWardNameById($wardId, $districtId = null): ?string
    {
        if (!$wardId)
            return null;

        $cacheKey = "ward_name_{$wardId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($wardId) {
            $ward = Ward::find($wardId);
            return $ward ? $ward->name : null;
        });
    }
}
