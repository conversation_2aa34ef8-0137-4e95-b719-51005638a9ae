<?php

namespace App\Services;

use App\Models\Province;
use App\Models\Ward;
use Illuminate\Support\Facades\Cache;

class AddressService
{
    /**
     * Thời gian lưu cache (1 ngày)
     */
    const CACHE_TTL = 86400;

    public function getProvinces(): array
    {
        return Cache::remember('provinces_list', self::CACHE_TTL, function () {
            return Province::select('id', 'province_code', 'name', 'short_name', 'code', 'place_type')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }

    public function getWardsByProvince($provinceCode): array
    {
        $cacheKey = "wards_by_province_{$provinceCode}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceCode) {
            return Ward::where('province_code', $provinceCode)
                ->select('id', 'ward_code', 'name', 'province_code')
                ->orderBy('name')
                ->get()
                ->toArray();
        });
    }

    public function getProvinceNameById($provinceId): ?string
    {
        if (!$provinceId)
            return null;

        $cacheKey = "province_name_{$provinceId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceId) {
            $province = Province::find($provinceId);
            return $province ? $province->name : null;
        });
    }

    public function getProvinceNameByCode($provinceCode): ?string
    {
        if (!$provinceCode)
            return null;

        $cacheKey = "province_name_code_{$provinceCode}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($provinceCode) {
            $province = Province::where('province_code', $provinceCode)->first();
            return $province ? $province->name : null;
        });
    }

    public function getWardNameById($wardId): ?string
    {
        if (!$wardId)
            return null;

        $cacheKey = "ward_name_{$wardId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($wardId) {
            $ward = Ward::find($wardId);
            return $ward ? $ward->name : null;
        });
    }

    public function getWardNameByCode($wardCode): ?string
    {
        if (!$wardCode)
            return null;

        $cacheKey = "ward_name_code_{$wardCode}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($wardCode) {
            $ward = Ward::where('ward_code', $wardCode)->first();
            return $ward ? $ward->name : null;
        });
    }

    public function getFullAddress($provinceCode, $wardCode): string
    {
        $province = $this->getProvinceNameByCode($provinceCode);
        $ward = $this->getWardNameByCode($wardCode);

        return trim($ward . ', ' . $province, ', ');
    }

    public function clearAddressCache(): void
    {
        Cache::forget('provinces_list');

        // Clear all ward caches (you might want to use cache tags for better management)
        $provinces = Province::pluck('province_code');
        foreach ($provinces as $provinceCode) {
            Cache::forget("wards_by_province_{$provinceCode}");
        }
    }
}
