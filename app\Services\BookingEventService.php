<?php

namespace App\Services;

use App\Models\BookingEvent;
use App\Models\CourtBooking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class BookingEventService
{
    /**
     * Create a booking event
     *
     * @param string $referenceNumber Booking reference number
     * @param string $eventType Type of event
     * @param array $eventData Additional event data
     * @param int|null $userId User ID who performed the action
     * @param int|null $staffId Staff ID who performed the action
     * @param string|null $notes Event notes
     * @param \Carbon\Carbon|null $eventTime Event time (defaults to now)
     * @return \App\Models\BookingEvent
     */
    public static function createEvent(
        string $referenceNumber,
        string $eventType,
        array $eventData = [],
        ?int $userId = null,
        ?int $staffId = null,
        ?string $notes = null,
        ?Carbon $eventTime = null
    ): BookingEvent {
        // If user/staff not provided, try to get from authenticated user
        if (Auth::check() && (is_null($userId) && is_null($staffId))) {
            $user = Auth::user();
            // Check if user is staff based on a staff attribute (adjust as needed)
            if (isset($user->is_staff) && $user->is_staff) {
                $staffId = $user->id;
            } else {
                $userId = $user->id;
            }
        }

        return BookingEvent::create([
            'reference_number' => $referenceNumber,
            'event_type' => $eventType,
            'event_data' => $eventData,
            'user_id' => $userId,
            'staff_id' => $staffId,
            'notes' => $notes,
            'event_time' => $eventTime ?? now()
        ]);
    }

    /**
     * Create a booking created event
     *
     * @param \App\Models\CourtBooking $booking
     * @param int|null $userId
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function bookingCreated(array $groupedBookings, ?int $userId = null, ?int $staffId = null): BookingEvent
    {

        $primaryBooking = $groupedBookings[0];
        $eventData = [
            'booking_type' => $primaryBooking->booking_type,
            'booking_date' => $primaryBooking->booking_date->format('Y-m-d'),
            'total_price' => $primaryBooking->total_price,
            'customer_name' => $primaryBooking->customer_name,
            'courts' => $groupedBookings
        ];

        return self::createEvent(
            $primaryBooking->reference_number,
            'booking_created',
            $eventData,
            $userId,
            $staffId,
            'Đặt sân mới'
        );
    }

    /**
     * Create a payment event
     *
     * @param \App\Models\CourtBooking $booking
     * @param float $amount
     * @param string $paymentMethod
     * @param string $status
     * @param int|null $userId
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function payment(
        CourtBooking $booking,
        float $amount,
        string $paymentMethod,
        string $status = 'completed',
        ?int $userId = null,
        ?int $staffId = null
    ): BookingEvent {
        $eventData = [
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'payment_status' => $status
        ];

        return self::createEvent(
            $booking->reference_number,
            'payment',
            $eventData,
            $userId,
            $staffId,
            'Thanh toán đơn đặt sân'
        );
    }

    /**
     * Create a booking approved event
     *
     * @param \App\Models\CourtBooking $booking
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function bookingApproved(CourtBooking $booking, ?int $staffId = null): BookingEvent
    {
        $eventData = [
            'approved_by' => 'staff'
        ];

        return self::createEvent(
            $booking->reference_number,
            'booking_approved',
            $eventData,
            null,
            $staffId,
            'Duyệt đơn đặt sân'
        );
    }

    /**
     * Create a check-in event
     *
     * @param \App\Models\CourtBooking $booking
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function checkIn(CourtBooking $booking, ?int $staffId = null): BookingEvent
    {
        $eventData = [
            'checked_in_by' => 'staff',
            'court_id' => $booking->court_id,
            'court_name' => $booking->court->name ?? 'Unknown',
        ];

        return self::createEvent(
            $booking->reference_number,
            'check_in',
            $eventData,
            null,
            $staffId,
            'Check-in vào sân'
        );
    }

    /**
     * Create a check-out event
     *
     * @param \App\Models\CourtBooking $booking
     * @param int|null $durationMinutes
     * @param int|null $overtimeMinutes
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function checkOut(
        CourtBooking $booking,
        ?int $durationMinutes = null,
        ?int $overtimeMinutes = null,
        ?int $staffId = null
    ): BookingEvent {
        $eventData = [
            'checked_out_by' => 'staff',
            'court_id' => $booking->court_id,
            'court_name' => $booking->court->name ?? 'Unknown',
        ];

        if ($durationMinutes) {
            $eventData['duration_minutes'] = $durationMinutes;
        }

        if ($overtimeMinutes) {
            $eventData['overtime_minutes'] = $overtimeMinutes;
        }

        return self::createEvent(
            $booking->reference_number,
            'check_out',
            $eventData,
            null,
            $staffId,
            'Khách hàng check-out khỏi sân'
        );
    }

    /**
     * Create an overtime payment event
     *
     * @param \App\Models\CourtBooking $booking
     * @param float $amount
     * @param string $paymentMethod
     * @param int|null $overtimeMinutes
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function overtimePayment(
        CourtBooking $booking,
        float $amount,
        string $paymentMethod,
        ?int $overtimeMinutes = null,
        ?int $staffId = null
    ): BookingEvent {
        $eventData = [
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'overtime_minutes' => $overtimeMinutes ?? $booking->overtime_minutes,
            'court_id' => $booking->court_id,
            'court_name' => $booking->court->name ?? 'Unknown',
        ];

        return self::createEvent(
            $booking->reference_number,
            'overtime_payment',
            $eventData,
            null,
            $staffId,
            'Thanh toán phí phát sinh'
        );
    }

    /**
     * Create a booking completed event
     *
     * @param \App\Models\CourtBooking $booking
     * @param float|null $totalPaid
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function bookingCompleted(
        CourtBooking $booking,
        ?float $totalPaid = null,
        ?int $staffId = null
    ): BookingEvent {
        $eventData = [
            'total_paid' => $totalPaid ?? $booking->total_price,
        ];

        return self::createEvent(
            $booking->reference_number,
            'booking_completed',
            $eventData,
            null,
            $staffId,
            'Hoàn thành đơn đặt sân'
        );
    }

    /**
     * Create a booking cancelled event
     *
     * @param \App\Models\CourtBooking $booking
     * @param string|null $reason
     * @param int|null $userId
     * @param int|null $staffId
     * @return \App\Models\BookingEvent
     */
    public static function bookingCancelled(
        CourtBooking $booking,
        ?string $reason = null,
        ?int $userId = null,
        ?int $staffId = null
    ): BookingEvent {
        $cancelledBy = $staffId ? 'staff' : 'customer';
        $eventData = [
            'cancelled_by' => $cancelledBy,
            'reason' => $reason,
        ];

        return self::createEvent(
            $booking->reference_number,
            'booking_cancelled',
            $eventData,
            $userId,
            $staffId,
            'Hủy đơn đặt sân: ' . ($reason ?? 'Không có lý do')
        );
    }

    /**
     * Get the timeline of events for a booking
     *
     * @param string $referenceNumber
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBookingTimeline(string $referenceNumber)
    {
        return BookingEvent::byReference($referenceNumber)
            ->orderBy('event_time')
            ->get();
    }

    /**
     * Get a formatted timeline for display
     *
     * @param string $referenceNumber
     * @return array
     */
    public static function getFormattedTimeline(string $referenceNumber): array
    {
        $events = self::getBookingTimeline($referenceNumber);
        $timeline = [];

        foreach ($events as $event) {
            $eventData = $event->event_data ?? [];
            // Replace null or missing values in eventData with empty string
            foreach ($eventData as $key => $value) {
                if (!isset($eventData[$key]) || $eventData[$key] === null) {
                    $eventData[$key] = '';
                }
            }
            $actor = $event->staff ? $event->staff->name : ($event->user ? $event->user->name : 'System');

            $formattedEvent = [
                'id' => $event->id,
                'time' => $event->event_time->format('H:i d/m/Y'),
                'event_type' => $event->event_type,
                'actor' => $actor,
                'notes' => $event->notes,
                'data' => $eventData
            ];

            // Format based on event type
            switch ($event->event_type) {
                case 'booking_created':
                    $formattedEvent['title'] = 'Đặt đơn';
                    $formattedEvent['icon'] = 'calendar-plus';
                    $formattedEvent['color'] = 'primary';
                    break;

                case 'payment':
                    $formattedEvent['title'] = 'Thanh toán đơn';
                    $formattedEvent['description'] = 'Thanh toán ' . (isset($eventData['amount']) ? $eventData['amount'] : '') . ' bằng ' . (isset($eventData['payment_method']) ? $eventData['payment_method'] : '');
                    $formattedEvent['icon'] = 'credit-card';
                    $formattedEvent['color'] = 'success';
                    break;

                case 'booking_approved':
                    $formattedEvent['title'] = 'Duyệt đơn';
                    $formattedEvent['description'] = "Duyệt đơn đặt sân" . "bởi " . (isset($eventData['approved_by']) ? $eventData['approved_by'] : '');
                    $formattedEvent['icon'] = 'check-circle';
                    $formattedEvent['color'] = 'info';
                    break;

                case 'check_in':
                    $formattedEvent['title'] = 'Check-in vào sân';
                    $formattedEvent['description'] = "Vào sân";
                    $formattedEvent['icon'] = 'sign-in-alt';
                    $formattedEvent['color'] = 'primary';
                    break;

                case 'check_out':
                    $formattedEvent['title'] = 'Check-out khỏi sân';
                    $formattedEvent['description'] = "Rời sân";
                    if (!empty($eventData['overtime_minutes']) && $eventData['overtime_minutes'] > 0) {
                        $formattedEvent['description'] .= ". Phát sinh {$eventData['overtime_minutes']} phút";
                    }
                    $formattedEvent['icon'] = 'sign-out-alt';
                    $formattedEvent['color'] = 'warning';
                    break;

                case 'overtime_payment':
                    $formattedEvent['title'] = 'Thanh toán phí phát sinh';
                    $amount = isset($eventData['amount']) ? $eventData['amount'] : '';
                    $minutes = isset($eventData['overtime_minutes']) ? $eventData['overtime_minutes'] : '';
                    $formattedEvent['description'] = "Thanh toán phí phát sinh $amount cho $minutes phút";
                    $formattedEvent['icon'] = 'money-bill';
                    $formattedEvent['color'] = 'danger';
                    break;

                case 'booking_completed':
                    $formattedEvent['title'] = 'Hoàn thành';
                    $formattedEvent['description'] = "Hoàn thành đơn đặt sân";
                    $formattedEvent['icon'] = 'flag-checkered';
                    $formattedEvent['color'] = 'success';
                    break;

                case 'booking_cancelled':
                    $formattedEvent['title'] = 'Hủy đơn';
                    $reason = isset($eventData['reason']) && $eventData['reason'] !== null ? $eventData['reason'] : '';
                    $formattedEvent['description'] = "Hủy đơn: " . ($reason !== '' ? $reason : 'Không có lý do');
                    $formattedEvent['icon'] = 'times-circle';
                    $formattedEvent['color'] = 'danger';
                    break;

                default:
                    $formattedEvent['title'] = ucfirst(str_replace('_', ' ', $event->event_type));
                    $formattedEvent['description'] = $event->notes;
                    $formattedEvent['icon'] = 'info-circle';
                    $formattedEvent['color'] = 'secondary';
            }

            $timeline[] = $formattedEvent;
        }

        return $timeline;
    }
}