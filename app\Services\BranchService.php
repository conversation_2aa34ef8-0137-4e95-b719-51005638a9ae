<?php

namespace App\Services;

use App\Models\Branch;
use App\Models\BranchImage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BranchService
{
    /**
     * <PERSON><PERSON><PERSON> bị danh sách business với thông tin users
     */
    public function prepareBusinessesWithUsers($businesses, $activeUsers)
    {
        return $businesses->map(function ($business) use ($activeUsers) {
            $businessUsers = $activeUsers->filter(function ($user) use ($business) {
                $role = DB::table('model_has_roles')
                    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->where('model_has_roles.model_type', 'App\\Models\\User')
                    ->whereIn('model_has_roles.role_id', [3, 4])
                    ->where('model_has_roles.model_id', $user->id)
                    ->where('model_has_roles.business_id', $business->id)
                    ->select('roles.name')
                    ->first();

                if ($role) {
                    $user->role_name = $role->name;
                    return true;
                }

                return false;
            })->values();

            $business->users = $businessUsers;
            return $business;
        });
    }

    /**
     * Xử lý hình ảnh cho branch
     */
    public function handleBranchImages(Request $request, Branch $branch, $isMainImage = false)
    {
        if ($isMainImage) {
            $file = $request->file('branch_main_image');
            if (!$file)
                return null;

            return $this->saveImage($file, $branch, true);
        } else {
            $files = $request->file('branch_images');
            if (!$files)
                return;

            $files = is_array($files) ? $files : [$files];

            foreach ($files as $index => $file) {
                if ($file instanceof \Illuminate\Http\UploadedFile && $file->isValid()) {
                    $this->saveImage($file, $branch, false, $index);
                }
            }
        }
    }

    /**
     * Lưu một ảnh vào storage và tạo record trong database
     */
    private function saveImage($file, Branch $branch, bool $isMain = false, int $displayOrder = 0)
    {
        // Tạo thư mục nếu chưa tồn tại
        $directory = "branches/{$branch->id}";
        if (!Storage::disk('public')->exists($directory)) {
            Storage::disk('public')->makeDirectory($directory);
        }

        // Tạo tên file ngẫu nhiên: 5 ký tự ngẫu nhiên + timestamp
        $randomText = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 5);
        $timestamp = time();
        $extension = $file->getClientOriginalExtension();
        $filename = "{$randomText}_{$timestamp}.{$extension}";

        // Lưu file
        $path = $file->storeAs($directory, $filename, 'public');

        // Tạo record trong database
        if (!$isMain) {
            BranchImage::create([
                'branch_id' => $branch->id,
                'image_url' => '/storage/' . $path,
                'display_order' => $displayOrder,
                'is_main' => false,
            ]);
        }

        // Nếu là ảnh chính, cập nhật main_image_url cho branch
        if ($isMain) {
            $branch->main_image_url = '/storage/' . $path;
            $branch->save();
        }

        return $path;
    }


    /**
     * Xử lý hình ảnh đã xóa
     */
    public function handleDeletedImages(Branch $branch, array $deletedImages)
    {
        foreach ($deletedImages as $imageId) {
            $image = BranchImage::find($imageId);
            if ($image && $image->branch_id === $branch->id) {
                if ($image->image_url && Storage::disk('public')->exists($image->image_url)) {
                    Storage::disk('public')->delete($image->image_url);
                }
                $image->delete();
            }
        }
    }

    /**
     * Gán branch managers cho branch
     */
    public function assignBranchManagers(Branch $branch, array $managers)
    {
        foreach ($managers as $userId) {
            // Update the user's branch_id directly
            User::where('id', $userId)->update([
                'branch_id' => $branch->id
            ]);
        }
    }

    /**
     * Cập nhật branch managers cho branch
     */
    public function updateBranchManagers(Branch $branch, array $managers)
    {
        // Clear branch_id for users previously assigned to this branch but not in new managers list
        User::where('branch_id', $branch->id)
            ->whereNotIn('id', $managers)
            ->update(['branch_id' => null]);

        // Assign branch_id to new managers
        User::whereIn('id', $managers)
            ->update(['branch_id' => $branch->id]);
    }

    /**
     * Lấy danh sách nhân viên chi nhánh
     */
    public function getBranchStaff(Branch $branch, bool $isSuperAdmin)
    {
        return \App\Models\User::with(['roles'])
            ->where('branch_id', $branch->id)
            ->orWhere(function ($query) use ($branch) {
                $query->where('business_id', $branch->business_id)
                    ->whereNull('branch_id');
            })
            ->get(['id', 'name', 'email', 'phone', 'status', 'branch_id'])
            ->map(function ($user) use ($branch) {
                // Determine if the user is specifically assigned to this branch
                $user->is_branch_specific = $user->branch_id === $branch->id;

                // Get role name from the roles relationship
                $user->role_name = $user->roles->first() ? $user->roles->first()->name : null;
                $user->role_id = $user->roles->first() ? $user->roles->first()->id : null;

                return $user;
            });
    }
}

