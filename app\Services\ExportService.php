<?php

namespace App\Services;

use Illuminate\Support\Collection;

class ExportService
{
    /**
     * Export data to Excel
     *
     * @param \Illuminate\Support\Collection $data
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportExcel(Collection $data, string $filename)
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Add headers
        if ($data->count() > 0) {
            $columnIndex = 'A';
            foreach (array_keys($data->first()) as $header) {
                $sheet->setCellValue($columnIndex . '1', $header);
                $sheet->getStyle($columnIndex . '1')->getFont()->setBold(true);
                $columnIndex++;
            }
        }

        // Add data rows
        if ($data->count() > 0) {
            $rowIndex = 2;
            foreach ($data as $row) {
                $columnIndex = 'A';
                foreach ($row as $cellValue) {
                    $sheet->setCellValue($columnIndex . $rowIndex, $cellValue);
                    $columnIndex++;
                }
                $rowIndex++;
            }
        }

        // Auto-size columns
        if ($data->count() > 0) {
            foreach (range('A', chr(64 + count(array_keys($data->first())))) as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }
        } else {
            foreach (range('A', 'J') as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }
        }

        // Create the writer
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        // Save to temp file
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        // Return download response
        return response()->download($tempFile, $filename . '.xlsx', [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Export data to PDF
     *
     * @param \Illuminate\Support\Collection $data
     * @param string $filename
     * @param string $title
     * @param string $subtitle
     * @param string $orientation
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportPDF(Collection $data, string $filename, string $title = 'Báo cáo', string $subtitle = '', string $orientation = 'landscape')
    {
        $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $html .= '<style>
            body {
                font-family: DejaVu Sans, sans-serif;
                font-size: 9px;
                margin: 0;
                padding: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 8px;
            }
            th, td {
                padding: 4px;
                text-align: left;
                border: 1px solid #ddd;
                word-break: break-word;
                max-width: 200px;
                overflow: hidden;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
                color: #333;
                position: sticky;
                top: 0;
            }
            h1 {
                text-align: center;
                margin-bottom: 10px;
                font-size: 14px;
            }
            .container {
                max-width: 100%;
                margin: 0 auto;
            }
            .text-center {
                text-align: center;
            }
            .text-right {
                text-align: right;
            }
            .header-row {
                background-color: #4a5568;
                color: white;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            tr:hover {
                background-color: #f1f1f1;
            }
            .report-header {
                margin-bottom: 20px;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }
            .report-meta {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                font-size: 10px;
            }
        </style>';
        $html .= '</head><body>';

        $html .= '<div class="container">';

        // Enhanced header section
        $html .= '<div class="report-header">';
        $html .= '<h1>' . $title . '</h1>';

        // Add subtitle if provided
        if (!empty($subtitle)) {
            $html .= '<h2 class="text-center">' . $subtitle . '</h2>';
        }

        $html .= '<div class="report-meta">';
        $html .= '<div>Ngày xuất: ' . date('d/m/Y H:i') . '</div>';
        $html .= '<div>Số lượng: ' . $data->count() . ' dòng dữ liệu</div>';
        $html .= '</div>';
        $html .= '</div>';

        $html .= '<table>';

        if ($data->count() > 0) {
            $html .= '<tr class="header-row">';
            foreach (array_keys($data->first()) as $header) {
                $html .= '<th>' . $header . '</th>';
            }
            $html .= '</tr>';
        }

        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . $cell . '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '</table>';

        // Add footer with page numbers
        $html .= '<script type="text/php">
            if (isset($pdf)) {
                $text = "Trang {PAGE_NUM} / {PAGE_COUNT}";
                $size = 8;
                $font = $fontMetrics->getFont("DejaVu Sans");
                $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
                $x = ($pdf->get_width() - $width) / 2;
                $y = $pdf->get_height() - 35;
                $pdf->page_text($x, $y, $text, $font, $size);
            }
        </script>';

        $html .= '</div>';
        $html .= '</body></html>';

        // Configure PDF options
        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'DejaVu Sans');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);
        $options->set('defaultMediaType', 'all');
        $options->set('isFontSubsettingEnabled', true);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->setPaper('A4', $orientation);
        $dompdf->loadHtml($html);
        $dompdf->render();

        $pdfContent = $dompdf->output();
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);

        return response()->download($tempFile, $filename . '.pdf', [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.pdf"',
        ])->deleteFileAfterSend(true);
    }
}