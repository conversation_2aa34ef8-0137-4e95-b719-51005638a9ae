<?php

namespace App\Services;

use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Mail\Mailable;
use App\Models\Booking;
use App\Models\Branch;
use App\Models\Business;
use App\Mail\BookingConfirmation;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class MailService
{
    /**
     * Gửi email sử dụng cấu hình từ business_settings
     *
     * @param int $businessId ID của business
     * @param string|array $to Địa chỉ email người nhận
     * @param Mailable $mailable Instance của Mailable cần gửi
     * @param string|null $businessName Tên doanh nghiệp (nếu có)
     * @return bool Kết quả gửi email
     */
    public static function sendUsingBusinessConfig($businessId, $to, Mailable $mailable, $businessName = null)
    {
        try {
            $originalConfig = [
                'default' => Config::get('mail.default'),
                'host' => Config::get('mail.mailers.smtp.host'),
                'port' => Config::get('mail.mailers.smtp.port'),
                'encryption' => Config::get('mail.mailers.smtp.encryption'),
                'username' => Config::get('mail.mailers.smtp.username'),
                'password' => Config::get('mail.mailers.smtp.password'),
                'from_address' => Config::get('mail.from.address'),
                'from_name' => Config::get('mail.from.name'),
            ];

            $emailSettings = BusinessSetting::get($businessId, 'email_setting', []);


            \Illuminate\Support\Facades\Log::info('Email settings for business', [
                'business_id' => $businessId,
                'has_settings' => !empty($emailSettings),
                'has_username' => !empty($emailSettings) && !empty($emailSettings['email_username']),
                'settings' => $emailSettings
            ]);

            if (!empty($emailSettings) && !empty($emailSettings['email_username'])) {

                Config::set('mail.default', 'smtp');
                Config::set('mail.mailers.smtp.host', $emailSettings['email_host']);
                Config::set('mail.mailers.smtp.port', $emailSettings['email_send_port']);
                Config::set('mail.mailers.smtp.encryption', $emailSettings['email_encryption']);
                Config::set('mail.mailers.smtp.username', $emailSettings['email_username']);
                Config::set('mail.mailers.smtp.password', $emailSettings['email_password']);
                Config::set('mail.from.address', $emailSettings['email_username']);
                Config::set('mail.from.name', $businessName ?? config('app.name'));


                Mail::purge();

                Mail::to($to)->send($mailable);


                Config::set('mail.default', $originalConfig['default']);
                Config::set('mail.mailers.smtp.host', $originalConfig['host']);
                Config::set('mail.mailers.smtp.port', $originalConfig['port']);
                Config::set('mail.mailers.smtp.encryption', $originalConfig['encryption']);
                Config::set('mail.mailers.smtp.username', $originalConfig['username']);
                Config::set('mail.mailers.smtp.password', $originalConfig['password']);
                Config::set('mail.from.address', $originalConfig['from_address']);
                Config::set('mail.from.name', $originalConfig['from_name']);

                return true;
            } else {
                Mail::to($to)->send($mailable);
                return true;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send email: ' . $e->getMessage(), [
                'exception' => $e,
                'business_id' => $businessId,
                'to' => $to
            ]);
            return false;
        }
    }

    /**
     * Gửi email sử dụng cấu hình từ business của branch
     *
     * @param int $branchId ID của branch
     * @param string|array $to Địa chỉ email người nhận
     * @param Mailable $mailable Instance của Mailable cần gửi
     * @return bool Kết quả gửi email
     */
    public static function sendUsingBranchConfig($branchId, $to, Mailable $mailable)
    {
        try {
            $branch = \App\Models\Branch::find($branchId);

            if (!$branch || !$branch->business_id) {
                return false;
            }

            $business = $branch->business;
            return self::sendUsingBusinessConfig($business->id, $to, $mailable, $business->name);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send email using branch config: ' . $e->getMessage(), [
                'exception' => $e,
                'branch_id' => $branchId,
                'to' => $to
            ]);
            return false;
        }
    }

    /**
     * Send booking confirmation email
     *
     * @param Booking $booking The main booking record
     * @param Collection $courtBookings Collection of court bookings
     * @param Branch $branch Branch information
     * @param Business $business Business information
     * @return bool Result of sending email
     */
    public function sendBookingConfirmationEmail(Booking $booking, $courtBookings, Branch $branch, Business $business)
    {
        try {
            if (empty($booking->customer_email)) {
                return false;
            }

            $mailable = new BookingConfirmation($booking, $courtBookings, $branch, $business);
            return self::sendUsingBusinessConfig($business->id, $booking->customer_email, $mailable, $business->name);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                'exception' => $e,
                'booking_id' => $booking->id,
                'reference_number' => $booking->reference_number
            ]);
            return false;
        }
    }

    /**
     * Gửi email sử dụng cấu hình từ marketplace settings
     *
     * @param string|array $to Địa chỉ email người nhận
     * @param Mailable $mailable Instance của Mailable cần gửi
     * @return bool Kết quả gửi email
     */
    public static function sendUsingMarketplaceConfig($to, Mailable $mailable)
    {
        try {

            $originalConfig = [
                'default' => Config::get('mail.default'),
                'host' => Config::get('mail.mailers.smtp.host'),
                'port' => Config::get('mail.mailers.smtp.port'),
                'encryption' => Config::get('mail.mailers.smtp.encryption'),
                'username' => Config::get('mail.mailers.smtp.username'),
                'password' => Config::get('mail.mailers.smtp.password'),
                'from_address' => Config::get('mail.from.address'),
                'from_name' => Config::get('mail.from.name'),
            ];


            $emailSettings = \App\Services\SystemSettingService::get('market_email_setting');

            Log::info('Marketplace email settings', [
                'has_settings' => !empty($emailSettings),
                'has_username' => !empty($emailSettings) && !empty($emailSettings['email_username']),
                'settings' => $emailSettings
            ]);


            if (!empty($emailSettings) && !empty($emailSettings['email_username'])) {
                // Decrypt email password if it's encrypted
                $password = $emailSettings['email_password'] ?? '';
                if (!empty($password)) {
                    try {
                        $password = Crypt::decryptString($password);
                        Log::info('Successfully decrypted marketplace email password');
                    } catch (\Exception $e) {
                        // If decryption fails, assume it's already in plaintext
                        Log::info('Using plaintext marketplace email password - decryption failed: ' . $e->getMessage());
                    }
                }

                Config::set('mail.default', 'smtp');
                Config::set('mail.mailers.smtp.host', $emailSettings['email_host']);
                Config::set('mail.mailers.smtp.port', $emailSettings['email_send_port']);
                Config::set('mail.mailers.smtp.encryption', $emailSettings['email_encryption']);
                Config::set('mail.mailers.smtp.username', $emailSettings['email_username']);
                Config::set('mail.mailers.smtp.password', $password);
                Config::set('mail.from.address', $emailSettings['email_from_address'] ?? $emailSettings['email_username']);
                Config::set('mail.from.name', $emailSettings['email_from_name'] ?? config('app.name'));


                Mail::purge();

                Mail::to($to)->send($mailable);


                Config::set('mail.default', $originalConfig['default']);
                Config::set('mail.mailers.smtp.host', $originalConfig['host']);
                Config::set('mail.mailers.smtp.port', $originalConfig['port']);
                Config::set('mail.mailers.smtp.encryption', $originalConfig['encryption']);
                Config::set('mail.mailers.smtp.username', $originalConfig['username']);
                Config::set('mail.mailers.smtp.password', $originalConfig['password']);
                Config::set('mail.from.address', $originalConfig['from_address']);
                Config::set('mail.from.name', $originalConfig['from_name']);

                return true;
            } else {

                Mail::to($to)->send($mailable);
                return true;
            }
        } catch (\Exception $e) {
            Log::error('Failed to send marketplace email: ' . $e->getMessage(), [
                'exception' => $e,
                'to' => $to
            ]);
            return false;
        }
    }

}
