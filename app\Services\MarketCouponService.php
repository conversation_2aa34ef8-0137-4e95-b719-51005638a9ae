<?php

namespace App\Services;

use App\Models\MarketCoupon;
use App\Models\User;
use App\Models\MarketCouponUsage;
use Carbon\Carbon;

class MarketCouponService
{
    /**
     * Validate a coupon code for a given user and cart total.
     *
     * @param string $code
     * @param User $user
     * @param float $cartTotal
     * @param array $cartItems
     * @return array
     */
    public function validateCoupon(string $code, User $user, float $cartTotal, array $cartItems = [])
    {
        $coupon = MarketCoupon::where('code', $code)->first();

        if (!$coupon) {
            return [
                'valid' => false,
                'message' => __('marketplace.invalid_coupon_code'),
            ];
        }

        if (!$coupon->isValid()) {
            if (!$coupon->is_active) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_not_active'),
                ];
            }

            $now = now();

            if ($coupon->starts_at && $coupon->starts_at->gt($now)) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_not_started'),
                    'starts_at' => $coupon->starts_at,
                ];
            }

            if ($coupon->expires_at && $coupon->expires_at->lt($now)) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_expired'),
                    'expired_at' => $coupon->expires_at,
                ];
            }

            if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_usage_limit_reached'),
                ];
            }
        }


        if ($coupon->minimum_amount && $cartTotal < $coupon->minimum_amount) {
            return [
                'valid' => false,
                'message' => __('marketplace.minimum_amount_not_reached', [
                    'amount' => number_format($coupon->minimum_amount, 2)
                ]),
                'minimum_amount' => $coupon->minimum_amount,
            ];
        }


        if ($coupon->first_order_only) {

            $hasOrders = $user->marketOrders()->count() > 0;
            if ($hasOrders) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_first_order_only'),
                ];
            }
        }


        if ($coupon->usage_limit_per_user) {

            $userUsageCount = $user->couponUsages()->where('coupon_id', $coupon->id)->count();
            if ($userUsageCount >= $coupon->usage_limit_per_user) {
                return [
                    'valid' => false,
                    'message' => __('marketplace.coupon_usage_limit_per_user_reached'),
                ];
            }
        }





        $discount = $coupon->calculateDiscount($cartTotal);

        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => __('marketplace.coupon_applied_successfully'),
        ];
    }

    /**
     * Apply coupon to an order (increment usage count)
     *
     * @param MarketCoupon $coupon
     * @param User $user
     * @param int $orderId
     * @param float $discountAmount
     * @return bool
     */
    public function applyCoupon(MarketCoupon $coupon, User $user, int $orderId, float $discountAmount = null)
    {

        if ($discountAmount === null) {

            $discountAmount = 0;
        }


        $coupon->increment('used_count');


        MarketCouponUsage::create([
            'user_id' => $user->id,
            'coupon_id' => $coupon->id,
            'order_id' => $orderId,
            'discount_amount' => $discountAmount,
        ]);

        return true;
    }

    /**
     * Get coupon usage statistics
     *
     * @param MarketCoupon $coupon
     * @return array
     */
    public function getCouponStats(MarketCoupon $coupon)
    {
        $usages = MarketCouponUsage::where('coupon_id', $coupon->id)
            ->with(['user', 'order'])
            ->get();

        return [
            'total_uses' => $usages->count(),
            'unique_users' => $usages->pluck('user_id')->unique()->count(),
            'total_discount_given' => $usages->sum('discount_amount'),
            'recent_uses' => $usages->sortByDesc('created_at')->take(10)
        ];
    }

    /**
     * Check if user can use coupon
     *
     * @param MarketCoupon $coupon
     * @param User $user
     * @return bool
     */
    public function canUserUseCoupon(MarketCoupon $coupon, User $user)
    {
        if (!$coupon->usage_limit_per_user) {
            return true;
        }

        $userUsageCount = MarketCouponUsage::where('coupon_id', $coupon->id)
            ->where('user_id', $user->id)
            ->count();

        return $userUsageCount < $coupon->usage_limit_per_user;
    }
}
