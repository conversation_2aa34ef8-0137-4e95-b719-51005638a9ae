<?php

namespace App\Services\Marketplace;

use App\Services\SystemSettingService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class MarketplaceEmailConfigService
{
    private $emailSettings;

    public function __construct()
    {
        $this->emailSettings = SystemSettingService::get('market_email_setting');
    }

    /**
     * Configure mail settings dynamically for marketplace
     */
    public function configureMailSettings()
    {
        if (!$this->emailSettings) {
            return false;
        }

        // Decrypt email password if it's encrypted
        $password = $this->emailSettings['email_password'] ?? '';
        if (!empty($password)) {
            try {
                $password = Crypt::decryptString($password);
                Log::info('Successfully decrypted email password in MarketplaceEmailConfigService');
            } catch (\Exception $e) {
                // If decryption fails, assume it's already in plaintext
                Log::info('Using plaintext email password in MarketplaceEmailConfigService - decryption failed: ' . $e->getMessage());
            }
        }

        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp' => [
                'transport' => 'smtp',
                'host' => $this->emailSettings['email_host'] ?? '',
                'port' => $this->emailSettings['email_send_port'] ?? 587,
                'encryption' => $this->emailSettings['email_encryption'] ?? 'tls',
                'username' => $this->emailSettings['email_username'] ?? '',
                'password' => $password,
                'timeout' => null,
                'local_domain' => env('MAIL_EHLO_DOMAIN'),
            ],
            'mail.from' => [
                'address' => $this->emailSettings['email_from_address'] ?? $this->emailSettings['email_username'] ?? '',
                'name' => $this->emailSettings['email_from_name'] ?? config('app.name'),
            ],
        ]);

        return true;
    }

    /**
     * Check if email settings are configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->emailSettings) &&
               !empty($this->emailSettings['email_host']) &&
               !empty($this->emailSettings['email_username']);
    }

    /**
     * Get email settings
     */
    public function getSettings(): ?array
    {
        return $this->emailSettings;
    }

    /**
     * Get from email address
     */
    public function getFromAddress(): string
    {
        return $this->emailSettings['email_from_address'] ?? $this->emailSettings['email_username'] ?? config('mail.from.address');
    }

    /**
     * Get from name
     */
    public function getFromName(): string
    {
        return $this->emailSettings['email_from_name'] ?? config('mail.from.name');
    }
}
