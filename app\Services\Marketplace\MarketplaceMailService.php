<?php

namespace App\Services\Marketplace;

use App\Models\MarketOrder;
use App\Models\MarketPayment;
use App\Mail\Marketplace\OrderConfirmation;
use App\Mail\Marketplace\PaymentConfirmation;
use App\Mail\Marketplace\OrderStatusUpdate;
use App\Mail\Marketplace\PaymentStatusUpdate;
use App\Services\MailService;
use Illuminate\Support\Facades\Log;

class MarketplaceMailService
{
    /**
     * Send order confirmation email
     *
     * @param MarketOrder $order
     * @return bool
     */
    public static function sendOrderConfirmation(MarketOrder $order): bool
    {
        try {
            $email = $order->customer_email ?? $order->user->email ?? null;

            if (!$email) {
                Log::warning('No email address found for order', ['order_id' => $order->id]);
                return false;
            }

            $result = MailService::sendUsingMarketplaceConfig($email, new OrderConfirmation($order));

            if ($result) {
                Log::info('Order confirmation email sent', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'email' => $email
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send order confirmation email', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send payment confirmation email
     *
     * @param MarketOrder $order
     * @param MarketPayment $payment
     * @return bool
     */
    public static function sendPaymentConfirmation(MarketOrder $order, MarketPayment $payment): bool
    {
        try {
            $email = $order->customer_email ?? $order->user->email ?? null;

            if (!$email) {
                Log::warning('No email address found for payment confirmation', ['order_id' => $order->id]);
                return false;
            }

            $result = MailService::sendUsingMarketplaceConfig($email, new PaymentConfirmation($order, $payment));

            if ($result) {
                Log::info('Payment confirmation email sent', [
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'email' => $email
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation email', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send order status update email
     *
     * @param MarketOrder $order
     * @param string|null $oldStatus
     * @param string|null $cancelReason
     * @return bool
     */
    public static function sendOrderStatusUpdate(
        MarketOrder $order,
        ?string $oldStatus = null,
        ?string $cancelReason = null
    ): bool {
        try {
            $email = $order->customer_email ?? $order->user->email ?? null;

            if (!$email) {
                Log::warning('No email address found for order status update', ['order_id' => $order->id]);
                return false;
            }

            $result = MailService::sendUsingMarketplaceConfig($email, new OrderStatusUpdate($order, $oldStatus, $cancelReason));

            if ($result) {
                Log::info('Order status update email sent', [
                    'order_id' => $order->id,
                    'old_status' => $oldStatus,
                    'new_status' => $order->status,
                    'email' => $email
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send order status update email', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send payment status update email
     *
     * @param MarketOrder $order
     * @param MarketPayment $payment
     * @param string|null $oldPaymentStatus
     * @param string|null $failureReason
     * @param string|null $refundReason
     * @return bool
     */
    public static function sendPaymentStatusUpdate(
        MarketOrder $order,
        MarketPayment $payment,
        ?string $oldPaymentStatus = null,
        ?string $failureReason = null,
        ?string $refundReason = null
    ): bool {
        try {
            $email = $order->customer_email ?? $order->user->email ?? null;

            if (!$email) {
                Log::warning('No email address found for payment status update', ['order_id' => $order->id]);
                return false;
            }

            $result = MailService::sendUsingMarketplaceConfig($email, new PaymentStatusUpdate(
                $order,
                $payment,
                $oldPaymentStatus,
                $failureReason,
                $refundReason
            ));

            if ($result) {
                Log::info('Payment status update email sent', [
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'old_status' => $oldPaymentStatus,
                    'new_status' => $payment->status,
                    'email' => $email
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send payment status update email', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send multiple emails for order events
     *
     * @param MarketOrder $order
     * @param array $emailTypes Array of email types to send
     * @param array $additionalData Additional data for specific email types
     * @return array Results of email sending attempts
     */
    public static function sendMultipleEmails(
        MarketOrder $order,
        array $emailTypes,
        array $additionalData = []
    ): array {
        $results = [];

        foreach ($emailTypes as $emailType) {
            switch ($emailType) {
                case 'order_confirmation':
                    $results['order_confirmation'] = self::sendOrderConfirmation($order);
                    break;

                case 'payment_confirmation':
                    if (isset($additionalData['payment'])) {
                        $results['payment_confirmation'] = self::sendPaymentConfirmation(
                            $order,
                            $additionalData['payment']
                        );
                    }
                    break;

                case 'order_status_update':
                    $results['order_status_update'] = self::sendOrderStatusUpdate(
                        $order,
                        $additionalData['old_status'] ?? null,
                        $additionalData['cancel_reason'] ?? null
                    );
                    break;

                case 'payment_status_update':
                    if (isset($additionalData['payment'])) {
                        $results['payment_status_update'] = self::sendPaymentStatusUpdate(
                            $order,
                            $additionalData['payment'],
                            $additionalData['old_payment_status'] ?? null,
                            $additionalData['failure_reason'] ?? null,
                            $additionalData['refund_reason'] ?? null
                        );
                    }
                    break;

                default:
                    Log::warning('Unknown email type requested', ['email_type' => $emailType]);
                    $results[$emailType] = false;
            }
        }

        return $results;
    }
}
