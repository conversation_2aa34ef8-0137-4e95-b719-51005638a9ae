<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class NotificationService
{
    /**
     * Create a new notification and broadcast it.
     *
     * @param int|null $userId The user ID or null for system notifications
     * @param int|null $businessId The business ID or null if not business related
     * @param int|null $branchId The branch ID or null if not branch related
     * @param string $type The notification type (system, booking, payment, user, court)
     * @param string $title The notification title
     * @param string $message The notification message
     * @param array $data Additional data for the notification
     * @return Notification The created notification
     */
    public static function create(
        ?int $userId,
        ?int $businessId,
        ?int $branchId = null,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): Notification {
        $notification = new Notification();
        $notification->user_id = $userId;
        $notification->business_id = $businessId;
        $notification->branch_id = $branchId;
        $notification->type = $type;
        $notification->title = $title;
        $notification->message = $message;
        $notification->is_read = false;
        $notification->data = $data;
        $notification->save();

        // if ($branchId) {
        //     event(new BookingNotification($notification));
        // }

        return $notification;
    }

    /**
     * Create a booking notification.
     *
     * @param int $branchId The branch ID
     * @param string $referenceNumber The booking reference number
     * @param string $customerName The customer name
     * @param string $customerPhone The customer phone
     * @param array $bookingDetails The booking details
     * @return Notification The created notification
     */
    public static function createBookingNotification(
        int $branchId,
        string $referenceNumber,
        string $customerName,
        string $customerPhone,
        array $bookingDetails
    ): Notification {
        $title = "Đặt sân mới";
        $message = "Khách hàng {$customerName} đã đặt sân thành công";

        return self::create(
            null,
            null,
            $branchId,
            'booking',
            $title,
            $message,
            [
                'reference_number' => $referenceNumber,
                'customer_name' => $customerName,
                'customer_phone' => $customerPhone,
                'booking_details' => $bookingDetails
            ]
        );
    }

    public static function createPaymentNotification(
        int $branchId,
        string $referenceNumber,
        string $customerName,
        string $customerPhone,
        array $bookingDetails,
        string $paymentMethod,
        float $amount,
        string $status
    ): Notification {
        $title = "Thanh toán sân: #{$referenceNumber}";
        $message = "Khách hàng {$customerName} đã thanh toán sân thành công";
        return self::create(
            Auth::id(),
            null,
            $branchId,
            'payment',
            $title,
            $message,
            [
                'reference_number' => $referenceNumber,
                'customer_name' => $customerName,
                'customer_phone' => $customerPhone,
                'booking_details' => $bookingDetails,
                'payment_method' => $paymentMethod,
                'amount' => $amount,
                'status' => $status
            ]
        );
    }

    public static function createNewCourtForBranch(
        int $businessId,
        int $branchId,
        string $courtName,
        string $courtType,
        string $courtImage,
        string $courtDescription
    ): Notification {
        $title = "Cập nhật sân";
        $message = "Sân mới #{$courtName} đã được thêm vào chi nhánh của bạn.";

        return self::create(
            Auth::id(),
            $businessId,
            $branchId,
            'court',
            $title,
            $message,
            [
                'court_name' => $courtName,
                'court_type' => $courtType,
                'court_image' => $courtImage,
                'court_description' => $courtDescription
            ]
        );
    }

    /**
     * Create a notification for a new branch added to a business.
     *
     * @param int $businessId The business ID
     * @param int $branchId The branch ID
     * @param string $branchName The branch name
     * @param string $branchAddress The branch address
     * @param string $branchPhone The branch phone number
     * @param string|null $branchImage The branch image URL or null
     * @return Notification The created notification
     */
    public static function createNewBranchForBusiness(
        int $businessId,
        int $branchId,
        string $branchName,
        string $branchAddress,
        string $branchPhone,
        ?string $branchImage = null
    ): Notification {
        $title = "Chi nhánh mới";
        $message = "Chi nhánh mới #{$branchName} đã được thêm vào doanh nghiệp của bạn.";

        return self::create(
            Auth::id(),
            $businessId,
            null,
            'branch',
            $title,
            $message,
            [
                'branch_id' => $branchId,
                'branch_name' => $branchName,
                'branch_address' => $branchAddress,
                'branch_phone' => $branchPhone,
                'branch_image' => $branchImage
            ]
        );
    }

    /**
     * Create a notification for an updated court in a branch.
     *
     * @param int $branchId The branch ID
     * @param int $courtId The court ID
     * @param string $courtName The court name
     * @param string $courtType The court type
     * @param string $courtImage The court image URL
     * @param string $courtDescription The court description
     * @param array $changedFields Array of fields that were changed
     * @return Notification The created notification
     */
    public static function updateCourtForBranch(
        int $businessId,
        int $branchId,
        int $courtId,
        string $courtName,
        string $courtType,
        string $courtImage,
        string $courtDescription,
        array $changedFields = []
    ): Notification {
        $title = "Cập nhật sân";
        $message = "Sân #{$courtName} đã được cập nhật thông tin.";

        return self::create(
            Auth::id(),
            $businessId,
            $branchId,
            'court',
            $title,
            $message,
            [
                'court_id' => $courtId,
                'court_name' => $courtName,
                'court_type' => $courtType,
                'court_image' => $courtImage,
                'court_description' => $courtDescription,
                'changed_fields' => $changedFields
            ]
        );
    }

    /**
     * Create a notification for an updated branch in a business.
     *
     * @param int $businessId The business ID
     * @param int $branchId The branch ID
     * @param string $branchName The branch name
     * @param string $branchAddress The branch address
     * @param string $branchPhone The branch phone number
     * @param string|null $branchImage The branch image URL or null
     * @param array $changedFields Array of fields that were changed
     * @return Notification The created notification
     */
    public static function updateBranchForBusiness(
        int $businessId,
        int $branchId,
        string $branchName,
        string $branchAddress,
        string $branchPhone,
        ?string $branchImage = null,
        array $changedFields = []
    ): Notification {
        $title = "Cập nhật chi nhánh";
        $message = "Chi nhánh #{$branchName} đã được cập nhật thông tin.";

        return self::create(
            Auth::id(),
            $businessId,
            $branchId,
            'branch',
            $title,
            $message,
            [
                'branch_id' => $branchId,
                'branch_name' => $branchName,
                'branch_address' => $branchAddress,
                'branch_phone' => $branchPhone,
                'branch_image' => $branchImage,
                'changed_fields' => $changedFields
            ]
        );
    }

    /**
     * Create a notification for a deleted court in a branch.
     *
     * @param int $branchId The branch ID
     * @param int $courtId The court ID
     * @param string $courtName The court name
     * @return Notification The created notification
     */
    public static function deleteCourtForBranch(
        int $businessId,
        int $branchId,
        int $courtId,
        string $courtName
    ): Notification {
        $title = "Xóa sân";
        $message = "Sân #{$courtName} đã bị xóa khỏi chi nhánh.";

        return self::create(
            Auth::id(),
            $businessId,
            $branchId,
            'court',
            $title,
            $message,
            [
                'court_id' => $courtId,
                'court_name' => $courtName,
                'action' => 'delete'
            ]
        );
    }

    /**
     * Create a notification for a deleted branch in a business.
     *
     * @param int $businessId The business ID
     * @param int $branchId The branch ID
     * @param string $branchName The branch name
     * @return Notification The created notification
     */
    public static function deleteBranchForBusiness(
        int $businessId,
        int $branchId,
        string $branchName,
        string $deleterInfo
    ): Notification {
        $title = "Xóa chi nhánh";
        $message = "Chi nhánh #{$branchName} đã bị xóa khỏi doanh nghiệp.";

        return self::create(
            Auth::id(),
            $businessId,
            null,
            'branch',
            $title,
            $message,
            [
                'branch_id' => $branchId,
                'branch_name' => $branchName,
                'action' => 'delete',
                'deleter_info' => $deleterInfo,
            ]
        );
    }
}
