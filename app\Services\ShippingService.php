<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Cache;

class ShippingService
{
    /**
     * Get GHN settings from the database
     *
     * @return array
     */
    private static function getGhnSettings()
    {
        $settings = SystemSettingService::get('market_ghn_setting', [
            'active' => false,
            'shop_id' => '',
            'use_dynamic_pricing' => false,
            'environment' => 'dev'
        ]);

        // Decrypt API key if exists
        if (!empty($settings['api_key'])) {
            try {
                $settings['api_key'] = Crypt::decryptString($settings['api_key']);
            } catch (\Exception $e) {
                Log::error('Error decrypting GHN API key: ' . $e->getMessage());
                $settings['api_key'] = '';
            }
        }

        return $settings;
    }

    /**
     * Get available shipping services from GHN API
     *
     * @param int $fromDistrictId Source district ID
     * @param int $toDistrictId Destination district ID
     * @return array ['success' => bool, 'data' => array, 'message' => string]
     */
    public static function getAvailableServices($fromDistrictId, $toDistrictId)
    {
        $settings = self::getGhnSettings();

        if (!$settings['active'] || empty($settings['api_key']) || empty($settings['shop_id'])) {
            return [
                'success' => false,
                'data' => null,
                'message' => 'GHN shipping is not properly configured'
            ];
        }

        $baseUrl = $settings['environment'] === 'prod'
            ? 'https://online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/available-services'
            : 'https://dev-online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/available-services';

        try {
            $response = Http::withHeaders([
                'Token' => $settings['api_key'],
                'Content-Type' => 'application/json',
            ])->post($baseUrl, [
                'shop_id' => (int)$settings['shop_id'],
                'from_district' => (int)$fromDistrictId,
                'to_district' => (int)$toDistrictId
            ]);

            $result = $response->json();

            if ($response->successful() && isset($result['data']) && count($result['data']) > 0) {
                // Extract the first service by default
                $defaultService = $result['data'][0];

                return [
                    'success' => true,
                    'data' => $result['data'],
                    'default_service' => [
                        'service_id' => $defaultService['service_id'],
                        'service_type_id' => $defaultService['service_type_id'],
                        'short_name' => $defaultService['short_name'] ?? 'Standard Shipping'
                    ],
                    'message' => 'Successfully retrieved available services'
                ];
            } else {
                Log::error('GHN API Error: ' . json_encode($result));
                return [
                    'success' => false,
                    'data' => null,
                    'message' => $result['message'] ?? 'Error retrieving available services'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception in GHN API call: ' . $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error communicating with GHN API: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Calculate shipping fee using GHN API
     *
     * @param int $fromDistrictId Source district ID
     * @param int $toDistrictId Destination district ID
     * @param int $toWardCode Destination ward code
     * @param int $weight Package weight in grams
     * @param int $length Package length in cm
     * @param int $width Package width in cm
     * @param int $height Package height in cm
     * @param int|null $serviceId Service ID from available services
     * @param int|null $serviceTypeId Service type ID from available services
     * @param array|null $items Array of product items with name, quantity, weight, etc.
     * @return array ['success' => bool, 'fee' => int, 'message' => string]
     */
    public static function calculateGhnShippingFee(
        $fromDistrictId,
        $toDistrictId,
        $toWardCode,
        $weight = 500,
        $length = 15,
        $width = 15,
        $height = 10,
        $serviceId = null,
        $serviceTypeId = null,
        $items = null
    ) {
        $settings = self::getGhnSettings();

        if (!$settings['active'] || empty($settings['api_key']) || empty($settings['shop_id'])) {
            return [
                'success' => false,
                'fee' => null,
                'message' => 'GHN shipping is not properly configured'
            ];
        }

        // Get available services if service ID is not provided
        if ($serviceId === null || $serviceTypeId === null) {
            $services = self::getAvailableServices($fromDistrictId, $toDistrictId);
            if ($services['success'] && isset($services['default_service'])) {
                $serviceId = $services['default_service']['service_id'];
                $serviceTypeId = $services['default_service']['service_type_id'];
            } else {
                Log::warning('Could not get available services from GHN, using default parameters');
            }
        }

        $baseUrl = $settings['environment'] === 'prod'
            ? 'https://online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/fee'
            : 'https://dev-online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/fee';

        try {
            $payload = [
                'to_district_id' => (int)$toDistrictId,
                'to_ward_code' => $toWardCode,
                'from_district_id' => (int)$fromDistrictId,
                'weight' => $weight,
                'length' => $length,
                'width' => $width,
                'height' => $height,
                'insurance_value' => 0,
                'cod_failed_amount' => 0,
                'coupon' => null
            ];

            // Add service ID and service type ID if available
            if ($serviceId !== null) {
                $payload['service_id'] = (int)$serviceId;
            }

            if ($serviceTypeId !== null) {
                $payload['service_type_id'] = (int)$serviceTypeId;
            }

            // Add items if available
            if ($items !== null && is_array($items) && count($items) > 0) {
                $payload['items'] = $items;
            }

            $response = Http::withHeaders([
                'Token' => $settings['api_key'],
                'ShopId' => (int)$settings['shop_id'],
                'Content-Type' => 'application/json',
            ])->post($baseUrl, $payload);

            $result = $response->json();

            if ($response->successful() && isset($result['data']['total'])) {
                return [
                    'success' => true,
                    'fee' => $result['data']['total'],
                    'message' => 'Successfully calculated shipping fee',
                    'extra_data' => [
                        'expected_delivery_time' => $result['data']['expected_delivery_time'] ?? null,
                        'service_fee' => $result['data']['service_fee'] ?? null,
                        'insurance_fee' => $result['data']['insurance_fee'] ?? null
                    ]
                ];
            } else {
                Log::error('GHN API Error: ' . json_encode($result));
                return [
                    'success' => false,
                    'fee' => null,
                    'message' => $result['message'] ?? 'Error calculating shipping fee'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Exception in GHN API call: ' . $e->getMessage());
            return [
                'success' => false,
                'fee' => null,
                'message' => 'Error communicating with GHN API: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Calculate shipping fee based on settings and data
     *
     * @param array $shippingData Shipping data including destination info
     * @return array ['fee' => int, 'dynamic' => bool]
     */
    public static function calculateShippingFee($shippingData)
    {
        // Get settings
        $settings = SystemSettingService::get('market_ghn_setting', [
            'active' => false
        ]);

        // Generate a cache key for this shipping calculation
        $cacheKey = 'shipping_' . md5(json_encode($shippingData));

        // Check if we have a cached result (valid for 5 minutes)
        if ($cachedResult = Cache::get($cacheKey)) {
            Log::info("Using cached shipping calculation from ShippingService");
            return $cachedResult;
        }

        // If GHN is active, try to use the API
        if ($settings['active'] && !empty($shippingData['district_id']) && !empty($shippingData['ward_code'])) {
            // Default values for GHN's home district and ward
            $fromDistrictId = env('GHN_DEFAULT_DISTRICT_ID', 1454); // Default district (adjust as needed)

            // Get weight from cart items if available
            $weight = $shippingData['weight'] ?? 500; // Default 500g if not specified

            // Get product items if available for detailed shipping calculation
            $items = isset($shippingData['items']) ? self::formatItemsForGhn($shippingData['items']) : null;

            // Get available services first to get service_id and service_type_id
            $services = self::getAvailableServices($fromDistrictId, $shippingData['district_id']);
            $serviceId = null;
            $serviceTypeId = null;

            if ($services['success'] && isset($services['default_service'])) {
                $serviceId = $services['default_service']['service_id'];
                $serviceTypeId = $services['default_service']['service_type_id'];
            }

            $result = self::calculateGhnShippingFee(
                $fromDistrictId,
                $shippingData['district_id'],
                $shippingData['ward_code'],
                $weight,
                $shippingData['length'] ?? 15,
                $shippingData['width'] ?? 15,
                $shippingData['height'] ?? 10,
                $serviceId,
                $serviceTypeId,
                $items
            );

            if ($result['success']) {
                $response = [
                    'success' => true,
                    'fee' => $result['fee'],
                    'dynamic' => true,
                    'extra_data' => $result['extra_data'] ?? [],
                    'services' => $services['success'] ? $services['data'] : []
                ];

                // Cache the result for 5 minutes
                Cache::put($cacheKey, $response, now()->addMinutes(5));
                return $response;
            }

            // Return error response if API call fails
            $errorResponse = [
                'success' => false,
                'fee' => 0,
                'dynamic' => false,
                'message' => $result['message'] ?? 'Failed to calculate shipping fee'
            ];

            // Cache error response for a shorter time (1 minute)
            Cache::put($cacheKey, $errorResponse, now()->addMinutes(1));
            return $errorResponse;
        }

        // Return error if GHN is not active or required data is missing
        $notConfiguredResponse = [
            'success' => false,
            'fee' => 0,
            'dynamic' => false,
            'message' => 'GHN shipping is not properly configured or required address data is missing'
        ];

        // Cache not configured response
        Cache::put($cacheKey, $notConfiguredResponse, now()->addMinutes(5));
        return $notConfiguredResponse;
    }

    /**
     * Format cart items data for GHN API
     *
     * @param array $items Array of cart items
     * @return array Formatted items for GHN API
     */
    private static function formatItemsForGhn($items)
    {
        $formattedItems = [];

        foreach ($items as $item) {
            // Extract weight from the weight attribute
            // Assuming weight is stored as "200g" or just "200" to represent 200 grams
            $weight = 0;
            if (isset($item['weight']) && !empty($item['weight'])) {
                $weight = preg_replace('/[^0-9]/', '', $item['weight']);
                $weight = (int)$weight;
            } else {
                // Default weight if not specified
                $weight = 200;
            }

            $formattedItems[] = [
                'name' => $item['name'] ?? 'Product',
                'quantity' => $item['quantity'] ?? 1,
                'weight' => $weight,
                'length' => $item['length'] ?? 15,
                'width' => $item['width'] ?? 15,
                'height' => $item['height'] ?? 10
            ];
        }

        return $formattedItems;
    }
}
