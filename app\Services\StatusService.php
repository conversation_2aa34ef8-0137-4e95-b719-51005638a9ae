<?php

namespace App\Services;

class StatusService
{
    /**
     * <PERSON><PERSON><PERSON> danh sách trạng thái đặt sân
     *
     * @return array
     */
    public static function getBookingStatuses()
    {
        return [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'pending', 'label' => 'Chờ xác nhận'],
            ['value' => 'confirmed', 'label' => 'Đã xác nhận'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
            ['value' => 'cancelled', 'label' => 'Đã hủy'],
        ];
    }

    /**
     * L<PERSON>y danh sách trạng thái đặt sân cho dropdown
     * 
     * @param bool $includeAll Có bao gồm lựa chọn "Tất cả" hay không
     * @return array
     */
    public static function getBookingStatusesForDropdown($includeAll = true)
    {
        $statuses = self::getBookingStatuses();

        if (!$includeAll) {
            return array_filter($statuses, function ($status) {
                return $status['value'] !== 'all';
            });
        }

        return $statuses;
    }

    /**
     * Lấy class CSS cho từng trạng thái đặt sân
     *
     * @param string $status
     * @return string
     */
    public static function getBookingStatusClass($status)
    {
        switch ($status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Lấy tên hiển thị cho từng trạng thái đặt sân
     *
     * @param string $status
     * @return string
     */
    public static function getBookingStatusDisplay($status)
    {
        switch ($status) {
            case 'completed':
                return 'Hoàn thành';
            case 'confirmed':
                return 'Đã xác nhận';
            case 'pending':
                return 'Chờ xác nhận';
            case 'cancelled':
                return 'Đã hủy';
            default:
                return 'Không xác định';
        }
    }

    /**
     * Kiểm tra trạng thái có hợp lệ không
     *
     * @param string $status
     * @return bool
     */
    public static function isValidBookingStatus($status)
    {
        $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
        return in_array($status, $validStatuses);
    }

    /**
     * Lấy danh sách trạng thái thanh toán
     *
     * @return array
     */
    public static function getPaymentStatuses()
    {
        return [
            ['value' => 'all', 'label' => 'Tất cả trạng thái'],
            ['value' => 'unpaid', 'label' => 'Chưa thanh toán'],
            ['value' => 'paid', 'label' => 'Đã thanh toán'],
            ['value' => 'partial', 'label' => 'Thanh toán một phần'],
            ['value' => 'completed', 'label' => 'Hoàn thành'],
        ];
    }

    /**
     * Lấy danh sách trạng thái thanh toán cho dropdown
     * 
     * @param bool $includeAll Có bao gồm lựa chọn "Tất cả" hay không
     * @return array
     */
    public static function getPaymentStatusesForDropdown($includeAll = true)
    {
        $statuses = self::getPaymentStatuses();

        if (!$includeAll) {
            return array_filter($statuses, function ($status) {
                return $status['value'] !== 'all';
            });
        }

        return $statuses;
    }

    /**
     * Lấy tên hiển thị cho từng trạng thái thanh toán
     *
     * @param string|null $status
     * @return string
     */
    public static function getPaymentStatusDisplay($status)
    {
        switch ($status) {
            case 'unpaid':
                return 'Chưa thanh toán';
            case 'paid':
                return 'Đã thanh toán';
            case 'partial':
                return 'Thanh toán một phần';
            case 'completed':
                return 'Hoàn thành';
            default:
                return $status ? ucfirst($status) : 'Không xác định';
        }
    }

    /**
     * Lấy class CSS cho từng trạng thái thanh toán
     *
     * @param string $status
     * @return string
     */
    public static function getPaymentStatusClass($status)
    {
        switch ($status) {
            case 'paid':
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'partial':
                return 'bg-blue-100 text-blue-800';
            case 'unpaid':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Lấy danh sách phương thức thanh toán
     *
     * @return array
     */
    public static function getPaymentMethods()
    {
        return [
            ['value' => 'all', 'label' => __('common.all_methods')],
            ['value' => 'cash', 'label' => __('payment.method_cash')],
            ['value' => 'bank_transfer', 'label' => __('payment.method_bank_transfer')],
            ['value' => 'credit_card', 'label' => __('payment.method_credit_card')],
            ['value' => 'momo', 'label' => __('payment.method_momo')],
            ['value' => 'vnpay', 'label' => __('payment.method_vnpay')],
            ['value' => 'zalopay', 'label' => __('payment.method_zalopay')],
        ];
    }

    /**
     * Lấy tên hiển thị cho từng phương thức thanh toán
     *
     * @param string|null $method
     * @return string
     */
    public static function getPaymentMethodDisplay($method)
    {
        switch ($method) {
            case 'cash':
                return __('payment.method_cash');
            case 'bank_transfer':
                return __('payment.method_bank_transfer');
            case 'credit_card':
                return __('payment.method_credit_card');
            case 'momo':
                return __('payment.method_momo');
            case 'vnpay':
                return __('payment.method_vnpay');
            case 'zalopay':
                return __('payment.method_zalopay');
            default:
                return $method ? ucfirst($method) : __('payment.method_unknown');
        }
    }

    /**
     * Lấy danh sách trạng thái khách hàng
     *
     * @return array
     */
    public static function getCustomerStatuses()
    {
        return [
            ['value' => 'all', 'label' => __('common.all_status')],
            ['value' => 'active', 'label' => __('customer.status_active')],
            ['value' => 'inactive', 'label' => __('customer.status_inactive')],
            ['value' => 'blocked', 'label' => __('customer.status_blocked')],
        ];
    }

    /**
     * Lấy tên hiển thị cho từng trạng thái khách hàng
     *
     * @param string $status
     * @return string
     */
    public static function getCustomerStatusDisplay($status)
    {
        switch ($status) {
            case 'active':
                return __('customer.status_active');
            case 'inactive':
                return __('customer.status_inactive');
            case 'blocked':
                return __('customer.status_blocked');
            default:
                return __('common.unknown_status');
        }
    }

    /**
     * Lấy class CSS cho từng trạng thái khách hàng
     *
     * @param string $status
     * @return string
     */
    public static function getCustomerStatusClass($status)
    {
        switch ($status) {
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'inactive':
                return 'bg-yellow-100 text-yellow-800';
            case 'blocked':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }
}