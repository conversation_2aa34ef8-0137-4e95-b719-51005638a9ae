<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\SystemSetting;
use Illuminate\Support\Facades\Cache;

class SystemSettingService
{
    /**
     * Lấy giá trị setting theo key, có cache
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = 'system_setting_' . $key;
        return Cache::remember($cacheKey, 600, function () use ($key, $default) {
            $setting = SystemSetting::where('key_setting', $key)->first();

            if (!$setting) {
                return $default;
            }


            if ($setting->type === 'json') {
                $decoded = json_decode($setting->setting_value, true);
                return $decoded !== null ? $decoded : $default;
            }


            if ($setting->type === 'boolean') {
                return filter_var($setting->setting_value, FILTER_VALIDATE_BOOLEAN);
            }


            if ($setting->type === 'number') {
                return is_numeric($setting->setting_value) ? (float) $setting->setting_value : $default;
            }

            return $setting->setting_value ?? $default;
        });
    }

    /**
     * Đặt giá trị setting và xóa cache liên quan
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $group
     * @param string|null $displayName
     * @param string $type
     * @return \App\Models\SystemSetting
     */
    public static function set(string $key, $value, ?string $group = null, ?string $displayName = null, string $type = 'text')
    {

        if ($type === 'json' && (is_array($value) || is_object($value))) {
            $value = json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }


        if ($type === 'boolean') {
            $value = $value ? '1' : '0';
        }


        if ($type === 'number') {
            $value = (string) $value;
        }

        $setting = SystemSetting::updateOrCreate(
            ['key_setting' => $key],
            [
                'setting_value' => $value,
                'group' => $group,
                'display_name' => $displayName,
                'type' => $type,
            ]
        );

        Cache::forget('system_setting_' . $key);
        if ($group) {
            Cache::forget('system_setting_group_' . $group);
        }
        return $setting;
    }

    /**
     * Kiểm tra xem một setting có tồn tại hay không
     *
     * @param string $key
     * @return bool
     */
    public static function exists(string $key): bool
    {
        return SystemSetting::where('key_setting', $key)->exists();
    }

    /**
     * Xóa một setting
     *
     * @param string $key
     * @return int
     */
    public static function delete(string $key): int
    {
        return SystemSetting::where('key_setting', $key)->delete();
    }

    /**
     * Lấy tất cả setting theo group, có cache
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup(string $group)
    {
        $cacheKey = 'system_setting_group_' . $group;
        return Cache::remember($cacheKey, 600, function () use ($group) {
            $settings = SystemSetting::where('group', $group)->get();
            $result = [];

            foreach ($settings as $setting) {
                $key = $setting->key_setting;

                if ($setting->type === 'json') {
                    $result[$key] = json_decode($setting->setting_value, true);
                } elseif ($setting->type === 'boolean') {
                    $result[$key] = filter_var($setting->setting_value, FILTER_VALIDATE_BOOLEAN);
                } elseif ($setting->type === 'number') {
                    $result[$key] = is_numeric($setting->setting_value) ? (float) $setting->setting_value : null;
                } else {
                    $result[$key] = $setting->setting_value;
                }
            }

            return $result;
        });
    }

    /**
     * Lấy cài đặt thanh toán cho một nhà cung cấp cụ thể
     *
     * @param string $provider
     * @return mixed
     */
    public static function getPaymentSettings(string $provider)
    {
        $key = "market_{$provider}_payment";
        return self::get($key, []);
    }

    /**
     * Đặt cài đặt thanh toán cho một nhà cung cấp cụ thể
     *
     * @param string $provider
     * @param mixed $settings
     * @return \App\Models\SystemSetting
     */
    public static function setPaymentSettings(string $provider, $settings)
    {
        $key = "market_{$provider}_payment";
        $displayName = ucfirst($provider) . ' Payment Settings';

        return self::set($key, $settings, 'marketplace_payment', $displayName, 'json');
    }
}
