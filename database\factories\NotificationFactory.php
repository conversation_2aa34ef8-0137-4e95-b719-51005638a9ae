<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\Business;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Notification>
 */
class NotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $notificationTypes = ['system', 'booking', 'payment', 'user', 'court'];
        $type = $this->faker->randomElement($notificationTypes);

        $titles = [
            'system' => ['Thông báo hệ thống', 'Cậ<PERSON> nhật hệ thống', '<PERSON><PERSON>o trì hệ thống'],
            'booking' => ['Đặt sân mới', 'Hủy đặt sân', 'Thay đổi lịch đặt sân'],
            'payment' => ['Thanh toán thành công', '<PERSON><PERSON> toán thất bại', 'Hoàn tiền'],
            'user' => ['<PERSON><PERSON>ời dùng mới', 'Cậ<PERSON> nhật thông tin người dùng', '<PERSON>êu cầu trợ giúp'],
            'court' => ['Cập nhật sân', 'Tình trạng sân', 'Sân mới được thêm'],
        ];

        $messages = [
            'system' => [
                'Hệ thống sẽ bảo trì vào ngày 15/06/2025 từ 22:00 đến 23:00.',
                'Phiên bản mới đã được cập nhật với nhiều tính năng hấp dẫn.',
                'Vui lòng cập nhật thông tin doanh nghiệp của bạn.',
            ],
            'booking' => [
                'Có người dùng mới đặt sân #%d vào ngày %s.',
                'Đặt sân #%d vào ngày %s đã bị hủy.',
                'Lịch đặt sân #%d đã được thay đổi từ %s sang %s.',
            ],
            'payment' => [
                'Thanh toán %s đã được xác nhận cho đặt sân #%d.',
                'Thanh toán %s cho đặt sân #%d không thành công.',
                'Số tiền %s đã được hoàn lại cho đặt sân #%d.',
            ],
            'user' => [
                'Người dùng mới %s đã đăng ký.',
                'Người dùng %s đã cập nhật thông tin cá nhân.',
                'Người dùng %s cần trợ giúp về việc đặt sân.',
            ],
            'court' => [
                'Sân #%d đã được cập nhật thông tin.',
                'Sân #%d đang gặp vấn đề kỹ thuật.',
                'Sân mới #%d đã được thêm vào chi nhánh của bạn.',
            ],
        ];

        $title = $this->faker->randomElement($titles[$type]);

        $message = $this->faker->randomElement($messages[$type]);
        $courtId = $this->faker->numberBetween(1, 20);
        $date = $this->faker->date('d/m/Y');
        $time = $this->faker->time('H:i');
        $userName = $this->faker->name;
        $amount = number_format($this->faker->numberBetween(100000, 1000000), 0, ',', '.') . 'đ';

        // Format message with random data
        if ($type === 'booking') {
            $message = sprintf($message, $courtId, $date, $date);
        } elseif ($type === 'payment') {
            $message = sprintf($message, $amount, $courtId);
        } elseif ($type === 'user') {
            $message = sprintf($message, $userName);
        } elseif ($type === 'court') {
            $message = sprintf($message, $courtId);
        }

        // Prepare any additional data for the notification
        $data = null;
        if ($type === 'booking') {
            $data = [
                'booking_id' => $this->faker->numberBetween(1, 100),
                'court_id' => $courtId,
                'date' => $date,
                'time' => $time,
                'user_name' => $userName,
            ];
        } elseif ($type === 'payment') {
            $data = [
                'booking_id' => $this->faker->numberBetween(1, 100),
                'amount' => (int) str_replace(['đ', '.'], '', $amount),
                'payment_method' => $this->faker->randomElement(['Chuyển khoản', 'Tiền mặt', 'Momo']),
            ];
        }

        return [
            'user_id' => User::inRandomOrder()->first()->id ?? User::factory()->create()->id,
            'business_id' => Business::inRandomOrder()->first()->id ?? null,
            'branch_id' => Branch::inRandomOrder()->first()->id ?? null,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'is_read' => $this->faker->boolean(30), // 30% chance of being read
            'read_at' => function (array $attributes) {
                return $attributes['is_read'] ? $this->faker->dateTimeBetween('-1 month', 'now') : null;
            },
            'data' => $data,
            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Indicate that the notification is for a branch.
     */
    public function forBranch(Branch $branch): static
    {
        return $this->state(function (array $attributes) use ($branch) {
            return [
                'branch_id' => $branch->id,
                'business_id' => $branch->business_id,
            ];
        });
    }

    /**
     * Indicate that the notification is for a business.
     */
    public function forBusiness(Business $business): static
    {
        return $this->state(function (array $attributes) use ($business) {
            return [
                'business_id' => $business->id,
                'branch_id' => null,
            ];
        });
    }

    /**
     * Indicate that the notification is for a user.
     */
    public function forUser(User $user): static
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'user_id' => $user->id,
            ];
        });
    }

    /**
     * Indicate that the notification is read.
     */
    public function read(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_read' => true,
                'read_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            ];
        });
    }

    /**
     * Indicate that the notification is unread.
     */
    public function unread(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_read' => false,
                'read_at' => null,
            ];
        });
    }
}
