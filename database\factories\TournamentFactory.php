<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Tournament;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tournament>
 */
class TournamentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('now', '+6 months');
        $endDate = (clone $startDate)->modify('+' . $this->faker->numberBetween(1, 5) . ' days');
        $registrationDeadline = (clone $startDate)->modify('-' . $this->faker->numberBetween(7, 30) . ' days');

        $categories = [
            ["Men's Singles", "Women's Singles"],
            ["Men's Doubles", "Women's Doubles"],
            ["Mixed Doubles"],
            ["Men's Singles", "Women's Singles", "Mixed Doubles"],
            ["Men's Doubles", "Women's Doubles", "Mixed Doubles"],
            ["Men's Singles", "Women's Singles", "Men's Doubles", "Women's Doubles", "Mixed Doubles"],
            ["U18 Singles", "U18 Doubles"],
            ["U21 Singles", "U21 Doubles"],
            ["Men's 40+", "Women's 40+", "Mixed 40+"],
        ];

        $organizers = [
            'Liên đoàn Pickleball Việt Nam',
            'Hiệp hội Pickleball Việt Nam',
            'CLB Pickleball TP.HCM',
            'CLB Pickleball Hà Nội',
            'CLB Pickleball Đà Nẵng',
            'Trung tâm Thể thao Thanh thiếu niên',
            'Hội Thể thao Đại học Việt Nam',
            'CLB Pickleball Masters',
        ];

        $locations = [
            'Sân vận động Phú Thọ, Quận 11, TP.HCM',
            'Nhà thi đấu Trịnh Hoài Đức, Đống Đa, Hà Nội',
            'Cung thể thao Tiên Sơn, Đà Nẵng',
            'Sân tennis Lam Sơn, Quận 3, TP.HCM',
            'Sân vận động Hoa Lư, Quận 1, TP.HCM',
            'Nhà thi đấu Đại học Quốc gia TP.HCM',
            'Sân thể thao Rạch Miễu, Quận 2, TP.HCM',
            'Cung thể thao Nguyễn Du, Hà Nội',
        ];

        $tournamentTypes = [
            'Giải Pickleball',
            'Pickleball Championship',
            'Pickleball Challenge Cup',
            'Pickleball Pro Tour',
            'Pickleball Masters',
            'Pickleball Youth Championship',
            'Pickleball Open',
            'Pickleball Tournament',
        ];

        $cities = ['TP.HCM', 'Hà Nội', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng', 'Nha Trang', 'Vũng Tàu'];

        return [
            'business_id' => $this->faker->boolean(40) ? $this->faker->numberBetween(1, 3) : null, // 40% chance of being business tournament
            'title' => $this->faker->randomElement($tournamentTypes) . ' ' . $this->faker->randomElement($cities) . ' ' . $this->faker->year(),
            'description' => $this->faker->paragraph(3),
            'image_url' => 'about.jpg',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'location' => $this->faker->randomElement($locations),
            'participants_limit' => $this->faker->randomElement([32, 48, 64, 80, 96, 120]),
            'registration_deadline' => $registrationDeadline,
            'entry_fee' => $this->faker->randomElement([200000, 300000, 400000, 500000, 600000, 800000, 1000000]),
            'prize_money' => $this->faker->randomElement([5000000, 8000000, 10000000, 15000000, 20000000, 25000000, 30000000]),
            'status' => $this->faker->randomElement([
                Tournament::STATUS_UPCOMING,
                Tournament::STATUS_ONGOING,
                Tournament::STATUS_COMPLETED,
                Tournament::STATUS_CANCELLED
            ]),
            'categories' => $this->faker->randomElement($categories),
            'organizer' => $this->faker->randomElement($organizers),
            'rating' => $this->faker->randomFloat(2, 3.5, 5.0),
            'review_count' => $this->faker->numberBetween(5, 100),
            'featured' => $this->faker->boolean(20), // 20% chance of being featured
            'metadata' => [
                'contact_email' => $this->faker->email(),
                'contact_phone' => $this->faker->phoneNumber(),
                'rules' => $this->faker->randomElement([
                    'Theo luật thi đấu quốc tế',
                    'Theo luật thi đấu chuyên nghiệp quốc tế',
                    'Dành cho sinh viên dưới 25 tuổi',
                    'Dành cho vận động viên trên 40 tuổi',
                    'Dành cho vận động viên dưới 21 tuổi'
                ]),
                'equipment_provided' => $this->faker->boolean(),
                'live_stream' => $this->faker->boolean(30),
                'parking_available' => $this->faker->boolean(80),
                'food_service' => $this->faker->boolean(60),
            ]
        ];
    }

    /**
     * Indicate that the tournament is featured.
     */
    public function featured(): static
    {
        return $this->state(fn(array $attributes) => [
            'featured' => true,
        ]);
    }

    /**
     * Indicate that the tournament is upcoming.
     */
    public function upcoming(): static
    {
        $startDate = $this->faker->dateTimeBetween('+1 week', '+6 months');
        $endDate = (clone $startDate)->modify('+' . $this->faker->numberBetween(1, 5) . ' days');
        $registrationDeadline = (clone $startDate)->modify('-' . $this->faker->numberBetween(7, 30) . ' days');

        return $this->state(fn(array $attributes) => [
            'status' => Tournament::STATUS_UPCOMING,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'registration_deadline' => $registrationDeadline,
        ]);
    }

    /**
     * Indicate that the tournament is ongoing.
     */
    public function ongoing(): static
    {
        $startDate = $this->faker->dateTimeBetween('-1 week', 'now');
        $endDate = $this->faker->dateTimeBetween('now', '+1 week');
        $registrationDeadline = (clone $startDate)->modify('-' . $this->faker->numberBetween(7, 30) . ' days');

        return $this->state(fn(array $attributes) => [
            'status' => Tournament::STATUS_ONGOING,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'registration_deadline' => $registrationDeadline,
        ]);
    }

    /**
     * Indicate that the tournament is completed.
     */
    public function completed(): static
    {
        $endDate = $this->faker->dateTimeBetween('-6 months', '-1 week');
        $startDate = (clone $endDate)->modify('-' . $this->faker->numberBetween(1, 5) . ' days');
        $registrationDeadline = (clone $startDate)->modify('-' . $this->faker->numberBetween(7, 30) . ' days');

        return $this->state(fn(array $attributes) => [
            'status' => Tournament::STATUS_COMPLETED,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'registration_deadline' => $registrationDeadline,
        ]);
    }

    /**
     * Indicate that the tournament is system-wide (no business).
     */
    public function systemWide(): static
    {
        return $this->state(fn(array $attributes) => [
            'business_id' => null,
        ]);
    }

    /**
     * Indicate that the tournament is organized by a specific business.
     */
    public function forBusiness($businessId = null): static
    {
        return $this->state(fn(array $attributes) => [
            'business_id' => $businessId ?? $this->faker->numberBetween(1, 3),
        ]);
    }

    /**
     * Indicate that the tournament is organized by business ID 1.
     */
    public function forBusinessOne(): static
    {
        return $this->state(fn(array $attributes) => [
            'business_id' => 1,
        ]);
    }

    /**
     * Indicate that the tournament is organized by business ID 2.
     */
    public function forBusinessTwo(): static
    {
        return $this->state(fn(array $attributes) => [
            'business_id' => 2,
        ]);
    }

    /**
     * Indicate that the tournament is organized by business ID 3.
     */
    public function forBusinessThree(): static
    {
        return $this->state(fn(array $attributes) => [
            'business_id' => 3,
        ]);
    }
}
