<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('court_bookings', function (Blueprint $table) {
            $table->decimal('overtime_fee', 12, 2)->nullable()->after('total_price')->comment('<PERSON><PERSON> chênh lệch khi check out tr<PERSON> hơn end_time');
            $table->integer('overtime_minutes')->nullable()->after('overtime_fee')->comment('<PERSON><PERSON> phút check out trễ so với end_time');
            $table->boolean('overtime_calculated')->default(false)->after('overtime_minutes')->comment('Đã tính phí chênh lệch hay chưa');
            $table->boolean('overtime_fee_paid')->default(false)->after('overtime_calculated')->comment('<PERSON><PERSON> thanh toán phí chênh lệch hay chưa');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('court_bookings', function (Blueprint $table) {
            $table->dropColumn('overtime_fee');
            $table->dropColumn('overtime_minutes');
            $table->dropColumn('overtime_calculated');
            $table->dropColumn('overtime_fee_paid');
        });
    }
};
