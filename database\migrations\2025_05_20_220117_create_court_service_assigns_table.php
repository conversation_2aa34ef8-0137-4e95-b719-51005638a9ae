<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('court_service_assigns', function (Blueprint $table) {
            $table->id();
            $table->foreignId('court_service_id')->constrained()->cascadeOnDelete();
            $table->foreignId('business_id')->constrained()->cascadeOnDelete();
            $table->foreignId('branch_id')->constrained()->cascadeOnDelete();
            $table->string('status')->default('active');
            $table->decimal('price', 10, 2);
            $table->string('unit')->default('hour');
            $table->string('discount_type')->nullable();
            $table->string('discount_person')->nullable();
            $table->decimal('discount_amount', 10, 2)->nullable();
            $table->json('settings')->nullable();
            $table->timestamps();

            // Ensure unique service assignment per branch
            $table->unique(['court_service_id', 'branch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('court_service_assigns');
    }
};
