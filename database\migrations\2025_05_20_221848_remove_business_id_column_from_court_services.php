<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First check if the business_id column exists
        if (Schema::hasColumn('court_services', 'business_id')) {
            Schema::table('court_services', function (Blueprint $table) {
                // Drop foreign key constraint if it exists
                try {
                    $table->dropForeign(['business_id']);
                } catch (\Exception $e) {
                    // Foreign key might not exist, continue
                }

                // Drop the unique index if it exists
                try {
                    $table->dropIndex('court_services_business_id_name_unique');
                } catch (\Exception $e) {
                    // Index might not exist, continue
                }

                // Drop the column
                $table->dropColumn('business_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only add the column if it doesn't exist
        if (!Schema::hasColumn('court_services', 'business_id')) {
            Schema::table('court_services', function (Blueprint $table) {
                $table->foreignId('business_id')->nullable()->constrained('businesses')->nullOnDelete();
            });
        }
    }
};
