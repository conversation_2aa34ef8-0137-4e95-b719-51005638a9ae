<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('sku')->nullable()->unique();
            $table->string('barcode')->nullable();
            $table->string('unit')->nullable()->comment('e.g. piece, set, pack');
            $table->decimal('import_price', 12, 2)->default(0);
            $table->decimal('sale_price', 12, 2)->default(0);
            $table->integer('alert_quantity')->default(10);
            $table->integer('quantity')->default(0);
            $table->string('brand')->nullable();
            $table->string('image_url')->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->string('weight')->nullable();
            $table->string('dimensions')->nullable();
            $table->string('slug')->nullable()->unique();
            $table->timestamps();
            $table->softDeletes();

            
            $table->index(['category_id', 'status']);
            $table->index(['brand', 'status']);
            $table->index(['sale_price', 'status']);
            $table->index(['is_featured', 'status']);
            $table->index(['weight', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
