<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // We don't need to modify the schema since payment_status already exists
        // We're just documenting that 'partial' is now a valid status

        // Add a comment to the column to document the allowed values
        DB::statement("ALTER TABLE bookings MODIFY COLUMN payment_status 
            VARCHAR(255) COMMENT 'Valid values: unpaid, paid, partial, cancelled, refunded'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to revert for documentation changes
    }
};
