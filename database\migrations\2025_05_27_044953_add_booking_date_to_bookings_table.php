<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->date('booking_date')->nullable()->after('customer_email')->comment('Date of the booking, synchronized with court_bookings');
        });

        // Synchronize booking_date from court_bookings
        $this->syncBookingDates();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn('booking_date');
        });
    }

    /**
     * Synchronize booking dates from court_bookings to bookings
     */
    private function syncBookingDates(): void
    {
        // Get all bookings that have associated court_bookings
        $bookings = DB::table('bookings')
            ->select('bookings.id', 'bookings.reference_number')
            ->whereNull('bookings.booking_date')
            ->get();

        foreach ($bookings as $booking) {
            // Get the earliest court booking date for this booking
            $courtBooking = DB::table('court_bookings')
                ->where('reference_number', $booking->reference_number)
                ->orderBy('booking_date', 'asc')
                ->first();

            if ($courtBooking) {
                DB::table('bookings')
                    ->where('id', $booking->id)
                    ->update(['booking_date' => $courtBooking->booking_date]);
            }
        }
    }
};
