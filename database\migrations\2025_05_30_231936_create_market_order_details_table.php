<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('market_order_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('market_order_id')->constrained('market_orders')->cascadeOnDelete();
            $table->string('reference_code')->unique()->nullable();
            $table->foreignId('product_id')->nullable()->constrained('products')->nullOnDelete();
            $table->string('product_name');
            $table->string('product_sku')->nullable();
            $table->string('product_image_url')->nullable();
            $table->decimal('unit_price', 10, 2);
            $table->integer('quantity');
            $table->decimal('total_price', 10, 2);
            $table->json('product_options')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index('market_order_id');
            $table->index('product_id');
            $table->index('reference_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('market_order_details');
    }
};
