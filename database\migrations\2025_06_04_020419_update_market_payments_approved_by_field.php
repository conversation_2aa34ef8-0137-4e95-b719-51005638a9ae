<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('market_payments', function (Blueprint $table) {
            // Drop the foreign key constraint and change the column to string
            $table->dropForeign(['approved_by']);
            $table->string('approved_by')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('market_payments', function (Blueprint $table) {
            // Revert back to foreign key (this might need data cleanup)
            $table->unsignedBigInteger('approved_by')->nullable()->change();
            $table->foreign('approved_by')->references('id')->on('users')->nullOnDelete();
        });
    }
};
