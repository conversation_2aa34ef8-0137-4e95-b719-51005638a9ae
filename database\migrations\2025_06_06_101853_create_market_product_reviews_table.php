<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('market_product_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('market_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('market_order_detail_id')->constrained()->onDelete('cascade');
            $table->integer('rating')->unsigned()->comment('1-5 stars');
            $table->text('comment')->nullable();
            $table->json('images')->nullable()->comment('Array of review image URLs');
            $table->boolean('is_verified_purchase')->default(true);
            $table->boolean('is_published')->default(true);
            $table->integer('helpful_count')->default(0);
            $table->integer('unhelpful_count')->default(0);
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();

            $table->index(['product_id', 'is_published']);
            $table->index(['user_id', 'product_id']);
            $table->index(['rating']);

            $table->unique(['user_id', 'market_order_detail_id']);
        });

        Schema::create('market_product_review_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('market_product_review_id')->constrained()->onDelete('cascade');
            $table->enum('vote_type', ['helpful', 'unhelpful']);
            $table->timestamps();

            $table->index(['market_product_review_id', 'vote_type'], 'mprv_review_vote_type_idx');

            $table->unique(['user_id', 'market_product_review_id'], 'mprv_user_review_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('market_product_review_votes');
        Schema::dropIfExists('market_product_reviews');
    }
};
