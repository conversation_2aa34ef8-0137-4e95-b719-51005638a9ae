<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_bundles', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên combo
            $table->text('description')->nullable(); // Mô tả combo
            $table->string('slug')->unique(); // URL slug
            $table->string('image_url')->nullable(); // Hình ảnh combo
            $table->decimal('original_price', 12, 2); // Tổng giá gốc các sản phẩm
            $table->decimal('bundle_price', 12, 2); // Giá combo (có giảm)
            $table->decimal('discount_amount', 12, 2)->default(0); // Số tiền giảm
            $table->decimal('discount_percentage', 5, 2)->default(0); // % giảm giá
            $table->boolean('is_active')->default(true); // Trạng thái hoạt động
            $table->boolean('is_featured')->default(false); // Combo nổi bật
            $table->integer('sort_order')->default(0); // Thứ tự hiển thị
            $table->timestamp('starts_at')->nullable(); // Thời gian bắt đầu
            $table->timestamp('expires_at')->nullable(); // Thời gian kết thúc
            $table->integer('stock_quantity')->default(0); // Số lượng combo có sẵn
            $table->json('meta_data')->nullable(); // Dữ liệu mở rộng
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_active', 'is_featured', 'sort_order']);
            $table->index(['starts_at', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_bundles');
    }
};
