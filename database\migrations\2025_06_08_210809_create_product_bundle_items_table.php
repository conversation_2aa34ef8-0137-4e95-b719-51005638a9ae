<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_bundle_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bundle_id')->constrained('product_bundles')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('quantity')->default(1); // Số lượng sản phẩm trong combo
            $table->decimal('item_price', 12, 2); // Giá sản phẩm tại thời điểm tạo combo
            $table->integer('sort_order')->default(0); // Thứ tự hiển thị trong combo
            $table->json('product_options')->nullable(); // T<PERSON><PERSON> chọn sản phẩm (size, màu, etc.)
            $table->timestamps();

            $table->unique(['bundle_id', 'product_id'], 'bundle_product_unique');
            $table->index(['bundle_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_bundle_items');
    }
};
