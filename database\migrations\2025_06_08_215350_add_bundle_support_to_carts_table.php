<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->foreignId('bundle_id')->nullable()->constrained('product_bundles')->onDelete('cascade');
            $table->enum('item_type', ['product', 'bundle'])->default('product');

            // Make product_id nullable since we can have bundle items
            $table->foreignId('product_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->dropForeign(['bundle_id']);
            $table->dropColumn(['bundle_id', 'item_type']);

            // Restore product_id as not nullable
            $table->foreignId('product_id')->nullable(false)->change();
        });
    }
};
