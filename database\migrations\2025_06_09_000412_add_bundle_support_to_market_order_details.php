<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('market_order_details', function (Blueprint $table) {
            $table->foreignId('bundle_id')->nullable()->after('product_id')->constrained('product_bundles')->nullOnDelete();
            $table->enum('item_type', ['product', 'bundle'])->default('product')->after('bundle_id');

            $table->index('bundle_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('market_order_details', function (Blueprint $table) {
            $table->dropForeign(['bundle_id']);
            $table->dropColumn(['bundle_id', 'item_type']);
        });
    }
};
