<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_revenues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('court_id')->nullable()->constrained('courts')->onDelete('cascade');
            $table->date('revenue_date');

            // Booking counts
            $table->integer('total_bookings')->default(0);
            $table->integer('confirmed_bookings')->default(0);
            $table->integer('cancelled_bookings')->default(0);
            $table->integer('completed_bookings')->default(0);

            // Revenue amounts
            $table->decimal('gross_revenue', 12, 2)->default(0);
            $table->decimal('commission_amount', 12, 2)->default(0);
            $table->decimal('net_revenue', 12, 2)->default(0);
            $table->decimal('peak_hour_revenue', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('refund_amount', 12, 2)->default(0);

            $table->timestamps();

            // Add unique constraint to prevent duplicate entries for the same date and court/branch
            $table->unique(['branch_id', 'court_id', 'revenue_date'], 'daily_revenue_unique');

            // Add indexes for faster queries
            $table->index('revenue_date');
            $table->index(['business_id', 'revenue_date']);
            $table->index(['branch_id', 'revenue_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_revenues');
    }
};
