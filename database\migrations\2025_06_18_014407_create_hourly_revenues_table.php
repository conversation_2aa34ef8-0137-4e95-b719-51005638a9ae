<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hourly_revenues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->date('revenue_date');
            $table->integer('hour_slot')->comment('Hour of the day (0-23)');
            $table->integer('total_bookings')->default(0);
            $table->decimal('total_revenue', 12, 2)->default(0);
            $table->timestamps();

            // Add unique constraint to prevent duplicate entries
            $table->unique(['branch_id', 'revenue_date', 'hour_slot'], 'hourly_revenue_unique');

            // Add indexes for faster queries
            $table->index('revenue_date');
            $table->index(['business_id', 'revenue_date']);
            $table->index(['branch_id', 'revenue_date']);
            $table->index('hour_slot');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hourly_revenues');
    }
};
