<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cron_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('command');
            $table->string('cron_expression');
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive', 'failed', 'running'])->default('active');
            $table->timestamp('last_run_at')->nullable();
            $table->timestamp('next_run_at')->nullable();
            $table->decimal('execution_time', 8, 3)->nullable();
            $table->integer('memory_usage')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            $table->integer('timeout')->default(300); // 5 minutes
            $table->integer('max_retries')->default(3);
            $table->integer('retry_count')->default(0);
            $table->enum('environment', ['production', 'staging', 'development'])->default('production');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->index(['status']);
            $table->index(['next_run_at']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cron_jobs');
    }
};
