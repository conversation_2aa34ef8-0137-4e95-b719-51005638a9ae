<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cron_job_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cron_job_id')->constrained()->onDelete('cascade');
            $table->enum('level', ['info', 'success', 'warning', 'error']);
            $table->text('message');
            $table->json('context')->nullable();
            $table->decimal('execution_time', 8, 3)->nullable();
            $table->integer('memory_usage')->nullable();
            $table->timestamp('created_at');
            
            $table->index(['cron_job_id', 'created_at']);
            $table->index(['level']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cron_job_logs');
    }
};
