<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monthly_statistics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->string('year_month', 7)->comment('Format: YYYY-MM');
            $table->integer('total_bookings')->default(0);
            $table->decimal('total_revenue', 12, 2)->default(0);
            $table->decimal('average_booking_value', 12, 2)->default(0);
            $table->integer('new_customers')->default(0);
            $table->integer('returning_customers')->default(0);
            $table->decimal('occupancy_rate', 5, 2)->default(0)->comment('Percentage of available slots booked');
            $table->json('peak_days')->nullable()->comment('JSON array of peak days with booking counts');
            $table->json('peak_hours')->nullable()->comment('JSON array of peak hours with booking counts');
            $table->timestamps();

            // Add unique constraint to prevent duplicate entries
            $table->unique(['branch_id', 'year_month'], 'monthly_stats_branch_month_unique');
            $table->unique(['business_id', 'year_month'], 'monthly_stats_business_month_unique')->where('branch_id', null);

            // Add indexes for faster queries
            $table->index('year_month');
            $table->index(['business_id', 'year_month']);
            $table->index(['branch_id', 'year_month']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monthly_statistics');
    }
};