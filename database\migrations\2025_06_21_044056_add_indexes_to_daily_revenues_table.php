<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_revenues', function (Blueprint $table) {
            // Compound indexes for common query patterns in RevenueController
            $table->index(['business_id', 'branch_id', 'revenue_date'], 'idx_business_branch_date');
            $table->index(['revenue_date', 'business_id'], 'idx_date_business');
            $table->index(['revenue_date', 'branch_id'], 'idx_date_branch');

            // Indexes for aggregation queries
            $table->index(['business_id', 'gross_revenue'], 'idx_business_revenue');
            $table->index(['branch_id', 'gross_revenue'], 'idx_branch_revenue');

            // Index for peak day queries
            $table->index(['revenue_date', 'gross_revenue'], 'idx_date_revenue');

            // Index for court-specific queries
            $table->index(['court_id', 'revenue_date'], 'idx_court_date');

            // Index for status-based filtering
            $table->index(['confirmed_bookings'], 'idx_confirmed_bookings');
            $table->index(['cancelled_bookings'], 'idx_cancelled_bookings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_revenues', function (Blueprint $table) {
            $table->dropIndex('idx_business_branch_date');
            $table->dropIndex('idx_date_business');
            $table->dropIndex('idx_date_branch');
            $table->dropIndex('idx_business_revenue');
            $table->dropIndex('idx_branch_revenue');
            $table->dropIndex('idx_date_revenue');
            $table->dropIndex('idx_court_date');
            $table->dropIndex('idx_confirmed_bookings');
            $table->dropIndex('idx_cancelled_bookings');
        });
    }
};
