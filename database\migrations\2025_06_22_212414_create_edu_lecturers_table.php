<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('edu_lecturers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('title')->nullable();
            $table->text('short_description')->nullable();
            $table->text('description')->nullable();
            $table->text('achievements')->nullable();
            $table->text('certifications')->nullable();
            $table->json('social_links')->nullable();
            $table->string('profile_image')->nullable();
            $table->decimal('rating', 3, 2)->default(0.00);
            $table->integer('total_students')->default(0);
            $table->integer('total_courses')->default(0);
            $table->integer('total_reviews')->default(0);
            $table->integer('experience_years')->default(0);
            $table->string('status')->default('pending_approval');
            $table->timestamps();

            $table->index('user_id');
            $table->index('status');
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('edu_lecturers');
    }
};
