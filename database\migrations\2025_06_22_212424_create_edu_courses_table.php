<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('edu_courses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lecturer_id')->constrained('edu_lecturers')->onDelete('cascade');
            $table->string('title');
            $table->text('short_description')->nullable();
            $table->text('description')->nullable();
            $table->string('thumbnail')->nullable();
            $table->string('category')->nullable();
            $table->string('slug')->unique();
            $table->string('level')->default('beginner');
            $table->integer('duration_hours')->default(0);
            $table->integer('total_lessons')->default(0);
            $table->decimal('price', 10, 2)->default(0.00);
            $table->decimal('original_price', 10, 2)->nullable();
            $table->integer('max_students')->nullable();
            $table->integer('enrolled_students')->default(0);
            $table->decimal('rating', 3, 2)->default(0.00);
            $table->integer('total_reviews')->default(0);
            $table->json('curriculum')->nullable();
            $table->json('requirements')->nullable();
            $table->json('outcomes')->nullable();
            $table->json('tags')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_free')->default(false);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('status')->default('pending_approval');
            $table->timestamp('published_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->integer('views_count')->default(0);
            $table->timestamps();

            $table->index('lecturer_id');
            $table->index('status');
            $table->index('category');
            $table->index('level');
            $table->index('is_featured');
            $table->index('is_free');
            $table->index('rating');
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('edu_courses');
    }
};
