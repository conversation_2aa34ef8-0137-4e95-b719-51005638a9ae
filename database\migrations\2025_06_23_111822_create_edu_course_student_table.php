<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('edu_course_student', function (Blueprint $table) {
            $table->id();
            $table->foreignId('edu_course_id')->constrained('edu_courses')->onDelete('cascade');
            $table->foreignId('edu_student_id')->constrained('edu_students')->onDelete('cascade');
            $table->string('status')->default('enrolled');
            $table->integer('progress')->default(0);
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->unique(['edu_course_id', 'edu_student_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('edu_course_student');
    }
};
