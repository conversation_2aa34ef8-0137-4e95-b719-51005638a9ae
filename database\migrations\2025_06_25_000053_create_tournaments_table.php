<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tournaments', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('image_url')->nullable();
            $table->date('start_date');
            $table->date('end_date');
            $table->string('location');
            $table->integer('participants_limit')->default(0);
            $table->date('registration_deadline');
            $table->decimal('entry_fee', 10, 2)->default(0);
            $table->decimal('prize_money', 12, 2)->default(0);
            $table->enum('status', ['upcoming', 'ongoing', 'completed', 'cancelled','pending_approval'])->default('upcoming');
            $table->json('categories')->nullable(); // Array of tournament categories
            $table->string('organizer');
            $table->decimal('rating', 3, 2)->default(0)->nullable();
            $table->integer('review_count')->default(0);
            $table->boolean('featured')->default(false);
            $table->json('metadata')->nullable(); // Additional tournament data
            $table->timestamps();

            // Indexes for better performance
            $table->index('status');
            $table->index('start_date');
            $table->index('end_date');
            $table->index('featured');
            $table->index('registration_deadline');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tournaments');
    }
};
