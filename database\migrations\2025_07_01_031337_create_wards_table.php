<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wards', function (Blueprint $table) {
            $table->id();
            $table->string('ward_code', 6)->unique();
            $table->string('name');
            $table->string('province_code', 2);
            $table->timestamps();

            // Indexes
            $table->index('ward_code');
            $table->index('province_code');

            // Foreign key constraint
            $table->foreign('province_code')
                ->references('province_code')
                ->on('provinces')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wards');
    }
};
