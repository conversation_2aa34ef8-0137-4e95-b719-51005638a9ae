<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aff_withdrawals', function (Blueprint $table) {
            $table->timestamp('rejected_at')->nullable()->after('rejection_reason');
            $table->unsignedBigInteger('rejected_by')->nullable()->after('rejected_at');

            $table->foreign('rejected_by')->references('id')->on('users')->onDelete('set null');
            $table->index('rejected_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aff_withdrawals', function (Blueprint $table) {
            $table->dropForeign(['rejected_by']);
            $table->dropIndex(['rejected_at']);
            $table->dropColumn(['rejected_at', 'rejected_by']);
        });
    }
};
