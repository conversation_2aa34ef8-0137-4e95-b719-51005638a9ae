<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AffSetting;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffLink;
use App\Models\AffKol;
use App\Models\AffKolContract;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffWithdrawal;
use App\Models\AffAffiliateCampaign;
use App\Models\User;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AffiliateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed affiliate settings
        $this->seedSettings();

        // Seed affiliates from existing users
        $this->seedAffiliatesFromUsers();

        // Seed sample campaigns
        $this->seedCampaigns();

        // Seed affiliate-campaign relationships
        $this->seedAffiliateCampaigns();

        // Seed affiliate links
        $this->seedAffiliateLinks();

        // Seed clicks data
        $this->seedClicks();

        // Seed conversions data
        $this->seedConversions();

        // Seed commissions data
        $this->seedCommissions();

        // Seed withdrawal requests
        $this->seedWithdrawals();

        // Seed sample KOLs
        $this->seedKols();

        // Seed KOL contracts
        $this->seedKolContracts();
    }

    /**
     * Seed affiliate system settings.
     */
    private function seedSettings(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'aff_system_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable affiliate system',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'aff_auto_approve_affiliates',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Auto approve new affiliate registrations',
                'group' => 'general',
                'is_public' => false,
            ],
            [
                'key' => 'aff_cookie_duration',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Cookie duration in days',
                'group' => 'tracking',
                'is_public' => true,
            ],

            // Commission Settings
            [
                'key' => 'aff_default_commission_rate',
                'value' => '5.00',
                'type' => 'number',
                'description' => 'Default commission rate percentage',
                'group' => 'commission',
                'is_public' => true,
            ],
            [
                'key' => 'aff_tier_rates',
                'value' => json_encode([
                    'bronze' => 3.0,
                    'silver' => 5.0,
                    'gold' => 7.0,
                    'platinum' => 10.0,
                ]),
                'type' => 'json',
                'description' => 'Commission rates by tier',
                'group' => 'commission',
                'is_public' => true,
            ],

            // Payment Settings
            [
                'key' => 'aff_minimum_payout',
                'value' => '100000',
                'type' => 'number',
                'description' => 'Minimum payout amount in VND',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_schedule',
                'value' => 'monthly',
                'type' => 'string',
                'description' => 'Payment schedule (weekly, monthly, quarterly)',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_methods',
                'value' => json_encode(['bank_transfer', 'momo', 'zalopay']),
                'type' => 'json',
                'description' => 'Available payment methods',
                'group' => 'payment',
                'is_public' => true,
            ],

            // Email Settings
            [
                'key' => 'aff_welcome_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send welcome email to new affiliates',
                'group' => 'email',
                'is_public' => false,
            ],
            [
                'key' => 'aff_commission_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send email notifications for new commissions',
                'group' => 'email',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            AffSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Seed affiliates from existing users.
     */
    private function seedAffiliatesFromUsers(): void
    {
        // Get existing users (excluding admin users)
        $users = User::whereNotIn('email', ['<EMAIL>', '<EMAIL>'])
            ->take(10)
            ->get();

        if ($users->isEmpty()) {
            // Create sample users if none exist
            $sampleUsers = [
                [
                    'name' => 'Nguyễn Văn A',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234567',
                ],
                [
                    'name' => 'Trần Thị B',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234568',
                ],
                [
                    'name' => 'Lê Văn C',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234569',
                ],
                [
                    'name' => 'Phạm Thị D',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234570',
                ],
                [
                    'name' => 'Hoàng Văn E',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234571',
                ],
            ];

            foreach ($sampleUsers as $userData) {
                $users->push(User::create($userData));
            }
        }

        $tiers = ['bronze', 'silver', 'gold', 'platinum'];
        $statuses = ['active', 'active', 'active', 'pending', 'inactive']; // More active affiliates

        foreach ($users as $index => $user) {
            // Skip if user already has affiliate record
            if (AffAffiliate::where('user_id', $user->id)->exists()) {
                continue;
            }

            // Generate unique referral code
            do {
                $referralCode = Str::upper(Str::random(6) . $user->id);
            } while (AffAffiliate::where('referral_code', $referralCode)->exists());

            $tier = $tiers[array_rand($tiers)];
            $status = $statuses[array_rand($statuses)];

            // Commission rates based on tier
            $commissionRates = [
                'bronze' => rand(300, 500) / 100,   // 3-5%
                'silver' => rand(500, 700) / 100,   // 5-7%
                'gold' => rand(700, 1000) / 100,    // 7-10%
                'platinum' => rand(1000, 1500) / 100, // 10-15%
            ];

            AffAffiliate::create([
                'user_id' => $user->id,
                'referral_code' => $referralCode,
                'status' => $status,
                'tier' => $tier,
                'commission_rate' => $commissionRates[$tier],
                'bio' => $this->generateAffiliateBio(),
                'website_url' => rand(0, 1) ? 'https://example-' . $user->id . '.com' : null,
                'social_profiles' => [
                    'facebook' => 'https://facebook.com/' . Str::slug($user->name),
                    'instagram' => 'https://instagram.com/' . Str::slug($user->name),
                    'youtube' => rand(0, 1) ? 'https://youtube.com/@' . Str::slug($user->name) : null,
                ],
                'approved_at' => $status === 'active' ? now()->subDays(rand(1, 30)) : null,
                'approved_by' => $status === 'active' ? 1 : null,
                'last_activity_at' => $status === 'active' ? now()->subHours(rand(1, 72)) : null,
            ]);
        }
    }

    /**
     * Generate random affiliate bio.
     */
    private function generateAffiliateBio(): string
    {
        $bios = [
            'Chuyên gia pickleball với 5 năm kinh nghiệm. Đam mê chia sẻ kiến thức về thể thao.',
            'Huấn luyện viên pickleball chuyên nghiệp. Giúp mọi người yêu thích môn thể thao này.',
            'Blogger thể thao, chuyên viết về pickleball và các môn thể thao vợt.',
            'Influencer thể thao với hơn 50k followers. Quảng bá lối sống khỏe mạnh.',
            'Vận động viên pickleball chuyên nghiệp. Tham gia nhiều giải đấu trong nước.',
            'Chủ câu lạc bộ pickleball địa phương. Kết nối cộng đồng người chơi.',
            'Nhà phân phối thiết bị thể thao. Chuyên về vợt và phụ kiện pickleball.',
            'Content creator về thể thao. Tạo video hướng dẫn kỹ thuật pickleball.',
        ];

        return $bios[array_rand($bios)];
    }

    /**
     * Seed sample campaigns.
     */
    private function seedCampaigns(): void
    {
        $campaigns = [
            [
                'name' => 'Khuyến mãi mùa hè 2025',
                'campaign_code' => 'SUMMER2025',
                'description' => 'Chiến dịch khuyến mãi đặt sân pickleball mùa hè',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 5.00,
                'budget' => 10000000, // 10M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(3)->toDateString(),
                'target_audience' => 'Người chơi pickleball, thể thao',
                'terms_conditions' => 'Áp dụng cho đặt sân online',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Sản phẩm vợt pickleball',
                'campaign_code' => 'RACKET2025',
                'description' => 'Quảng bá sản phẩm vợt pickleball chất lượng cao',
                'type' => 'product',
                'status' => 'active',
                'commission_rate' => 8.00,
                'budget' => 5000000, // 5M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(2)->toDateString(),
                'target_audience' => 'Người chơi pickleball chuyên nghiệp',
                'terms_conditions' => 'Áp dụng cho mua sản phẩm trên 500k',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Giải đấu pickleball 2025',
                'campaign_code' => 'TOURNAMENT2025',
                'description' => 'Quảng bá giải đấu pickleball lớn nhất năm',
                'type' => 'event',
                'status' => 'draft',
                'commission_rate' => 10.00,
                'budget' => 20000000, // 20M VND
                'start_date' => now()->addMonth()->toDateString(),
                'end_date' => now()->addMonths(4)->toDateString(),
                'target_audience' => 'Cộng đồng pickleball Việt Nam',
                'terms_conditions' => 'Hoa hồng cho mỗi đăng ký tham gia',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Khóa học pickleball cơ bản',
                'campaign_code' => 'BASIC2025',
                'description' => 'Khóa học pickleball dành cho người mới bắt đầu',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 12.00,
                'budget' => 8000000, // 8M VND
                'start_date' => now()->subDays(10)->toDateString(),
                'end_date' => now()->addMonths(2)->toDateString(),
                'target_audience' => 'Người mới chơi pickleball',
                'terms_conditions' => 'Áp dụng cho khóa học từ 1 tháng trở lên',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Phụ kiện pickleball cao cấp',
                'campaign_code' => 'PREMIUM2025',
                'description' => 'Bán phụ kiện pickleball chất lượng cao',
                'type' => 'product',
                'status' => 'active',
                'commission_rate' => 15.00,
                'budget' => 12000000, // 12M VND
                'start_date' => now()->subDays(5)->toDateString(),
                'end_date' => now()->addMonths(3)->toDateString(),
                'target_audience' => 'Người chơi pickleball chuyên nghiệp',
                'terms_conditions' => 'Hoa hồng cao cho sản phẩm premium',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Đặt sân cuối tuần',
                'campaign_code' => 'WEEKEND2025',
                'description' => 'Ưu đãi đặt sân pickleball cuối tuần',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 6.00,
                'budget' => 6000000, // 6M VND
                'start_date' => now()->subDays(15)->toDateString(),
                'end_date' => now()->addMonths(1)->toDateString(),
                'target_audience' => 'Người chơi pickleball nghiệp dư',
                'terms_conditions' => 'Chỉ áp dụng thứ 7, chủ nhật',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Pickleball cho doanh nghiệp',
                'campaign_code' => 'CORPORATE2025',
                'description' => 'Gói dịch vụ pickleball cho team building',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 20.00,
                'budget' => 15000000, // 15M VND
                'start_date' => now()->subDays(20)->toDateString(),
                'end_date' => now()->addMonths(6)->toDateString(),
                'target_audience' => 'Doanh nghiệp, công ty',
                'terms_conditions' => 'Tối thiểu 10 người tham gia',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Pickleball trẻ em',
                'campaign_code' => 'KIDS2025',
                'description' => 'Chương trình pickleball dành cho trẻ em',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 8.50,
                'budget' => 7000000, // 7M VND
                'start_date' => now()->subDays(8)->toDateString(),
                'end_date' => now()->addMonths(4)->toDateString(),
                'target_audience' => 'Trẻ em từ 8-16 tuổi',
                'terms_conditions' => 'Cần có phụ huynh đồng ý',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Pickleball người cao tuổi',
                'campaign_code' => 'SENIOR2025',
                'description' => 'Chương trình pickleball cho người cao tuổi',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 9.00,
                'budget' => 5000000, // 5M VND
                'start_date' => now()->subDays(12)->toDateString(),
                'end_date' => now()->addMonths(3)->toDateString(),
                'target_audience' => 'Người cao tuổi từ 55+',
                'terms_conditions' => 'Có chế độ ưu đãi đặc biệt',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Pickleball nữ',
                'campaign_code' => 'WOMEN2025',
                'description' => 'Chương trình pickleball dành riêng cho nữ',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 11.00,
                'budget' => 9000000, // 9M VND
                'start_date' => now()->subDays(18)->toDateString(),
                'end_date' => now()->addMonths(2)->toDateString(),
                'target_audience' => 'Phụ nữ yêu thích thể thao',
                'terms_conditions' => 'Ưu đãi đặc biệt cho hội viên nữ',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Pickleball chuyên nghiệp',
                'campaign_code' => 'PRO2025',
                'description' => 'Chương trình đào tạo pickleball chuyên nghiệp',
                'type' => 'service',
                'status' => 'draft',
                'commission_rate' => 25.00,
                'budget' => 30000000, // 30M VND
                'start_date' => now()->addDays(10)->toDateString(),
                'end_date' => now()->addMonths(12)->toDateString(),
                'target_audience' => 'Vận động viên chuyên nghiệp',
                'terms_conditions' => 'Cần có chứng chỉ huấn luyện viên',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            // Check if campaign code already exists
            if (!AffCampaign::where('campaign_code', $campaignData['campaign_code'])->exists()) {
                AffCampaign::create($campaignData);
            }
        }
    }

    /**
     * Seed affiliate-campaign relationships.
     */
    private function seedAffiliateCampaigns(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        foreach ($affiliates as $affiliate) {
            // Each affiliate joins 1-3 campaigns
            $campaignCount = rand(1, min(3, $campaigns->count()));
            $selectedCampaigns = $campaigns->random($campaignCount);

            foreach ($selectedCampaigns as $campaign) {
                // Check if relationship already exists
                if (
                    AffAffiliateCampaign::where('affiliate_id', $affiliate->id)
                        ->where('campaign_id', $campaign->id)
                        ->exists()
                ) {
                    continue;
                }

                $status = $campaign->auto_approve_affiliates ? 'approved' : ['pending', 'approved'][array_rand(['pending', 'approved'])];

                AffAffiliateCampaign::create([
                    'affiliate_id' => $affiliate->id,
                    'campaign_id' => $campaign->id,
                    'status' => $status,
                    'applied_at' => now()->subDays(rand(1, 30)),
                    'approved_at' => $status === 'approved' ? now()->subDays(rand(0, 15)) : null,
                    'approved_by' => $status === 'approved' ? 1 : null,
                ]);
            }
        }
    }

    /**
     * Seed affiliate links.
     */
    private function seedAffiliateLinks(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        foreach ($affiliates as $affiliate) {
            // Each affiliate creates 2-5 links
            $linkCount = rand(2, 5);

            for ($i = 0; $i < $linkCount; $i++) {
                $campaign = $campaigns->random();
                $shortCode = Str::lower(Str::random(8));

                AffLink::create([
                    'affiliate_id' => $affiliate->id,
                    'campaign_id' => rand(0, 1) ? $campaign->id : null,
                    'title' => $this->generateLinkTitle(),
                    'description' => $this->generateLinkDescription(),
                    'original_url' => $this->generateOriginalUrl(),
                    'short_code' => $shortCode,
                    'affiliate_url' => url('/aff/' . $shortCode),
                    'type' => ['product', 'category', 'landing_page', 'custom'][array_rand(['product', 'category', 'landing_page', 'custom'])],
                    'utm_source' => 'affiliate',
                    'utm_medium' => 'referral',
                    'utm_campaign' => $campaign->campaign_code,
                    'utm_content' => $affiliate->referral_code,
                    'is_active' => rand(0, 10) > 1, // 90% active
                    'clicks' => 0, // Will be updated by clicks seeder
                    'unique_clicks' => 0,
                    'conversions' => 0,
                    'last_clicked_at' => null,
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }

    /**
     * Seed clicks data.
     */
    private function seedClicks(): void
    {
        $links = AffLink::where('is_active', true)->get();

        // Debug: Check if we have links
        if ($links->isEmpty()) {
            echo "No active links found, skipping clicks seeding\n";
            return;
        }
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::all();

        // Ensure we have at least 10 clicks even if no links exist
        $minClicks = 50;
        $clicksCreated = 0;

        if ($links->isNotEmpty()) {
            foreach ($links as $link) {
                // Generate 5-50 clicks per link over the last 30 days
                $clickCount = rand(5, 50);

                for ($i = 0; $i < $clickCount; $i++) {
                    $clickedAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

                    AffClick::create([
                        'affiliate_id' => $link->affiliate_id,
                        'link_id' => $link->id,
                        'campaign_id' => $link->campaign_id,
                        'ip_address' => $this->generateRandomIP(),
                        'user_agent' => $this->generateRandomUserAgent(),
                        'referrer_url' => $this->generateReferrerUrl(),
                        'device_type' => ['desktop', 'mobile', 'tablet'][array_rand(['desktop', 'mobile', 'tablet'])],
                        'browser' => ['Chrome', 'Firefox', 'Safari', 'Edge'][array_rand(['Chrome', 'Firefox', 'Safari', 'Edge'])],
                        'country' => 'VN',
                        'city' => ['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'][array_rand(['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'])],
                        'is_unique' => rand(0, 1),
                        'clicked_at' => $clickedAt,
                    ]);
                    $clicksCreated++;
                }

                // Update link statistics
                $totalClicks = AffClick::where('link_id', $link->id)->count();
                $uniqueClicks = AffClick::where('link_id', $link->id)->where('is_unique', true)->count();
                $lastClick = AffClick::where('link_id', $link->id)->latest('clicked_at')->first();

                $link->update([
                    'clicks' => $totalClicks,
                    'unique_clicks' => $uniqueClicks,
                    'last_clicked_at' => $lastClick?->clicked_at,
                ]);
            }
        }

        // Create additional clicks if we haven't reached minimum
        while ($clicksCreated < $minClicks && $affiliates->isNotEmpty() && $campaigns->isNotEmpty()) {
            $affiliate = $affiliates->random();
            $campaign = $campaigns->random();
            $clickedAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

            AffClick::create([
                'affiliate_id' => $affiliate->id,
                'link_id' => null, // Direct campaign click
                'campaign_id' => $campaign->id,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => $this->generateRandomUserAgent(),
                'referrer_url' => $this->generateReferrerUrl(),
                'device_type' => ['desktop', 'mobile', 'tablet'][array_rand(['desktop', 'mobile', 'tablet'])],
                'browser' => ['Chrome', 'Firefox', 'Safari', 'Edge'][array_rand(['Chrome', 'Firefox', 'Safari', 'Edge'])],
                'country' => 'VN',
                'city' => ['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'][array_rand(['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'])],
                'is_unique' => rand(0, 1),
                'clicked_at' => $clickedAt,
            ]);
            $clicksCreated++;
        }
    }

    /**
     * Seed conversions data.
     */
    private function seedConversions(): void
    {
        $clicks = AffClick::all();
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::all();

        $minConversions = 15;
        $conversionsCreated = 0;

        if ($clicks->isNotEmpty()) {
            // Convert 3-8% of clicks to conversions
            $conversionRate = 0.05; // 5% average conversion rate
            $conversionsToCreate = max($minConversions, (int) ($clicks->count() * $conversionRate));

            $selectedClicks = $clicks->random(min($conversionsToCreate, $clicks->count()));

            foreach ($selectedClicks as $click) {
                $orderValue = rand(100000, 2000000); // 100k - 2M VND
                $affiliate = AffAffiliate::find($click->affiliate_id);
                $commissionRate = $affiliate ? $affiliate->commission_rate : 5.0;
                $commissionAmount = $orderValue * ($commissionRate / 100);

                AffConversion::create([
                    'affiliate_id' => $click->affiliate_id,
                    'campaign_id' => $click->campaign_id,
                    'link_id' => $click->link_id,
                    'click_id' => $click->id,
                    'conversion_type' => ['sale', 'booking', 'signup'][array_rand(['sale', 'booking', 'signup'])],
                    'order_id' => 'ORD' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                    'customer_id' => 'CUST' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    'order_value' => $orderValue,
                    'commission_rate' => $commissionRate,
                    'commission_amount' => $commissionAmount,
                    'status' => ['pending', 'approved', 'approved', 'approved'][array_rand(['pending', 'approved', 'approved', 'approved'])], // 75% approved
                    'converted_at' => $click->clicked_at->addMinutes(rand(1, 120)), // Convert within 2 hours of click
                    'created_at' => $click->clicked_at->addMinutes(rand(1, 120)),
                    'approved_at' => rand(0, 1) ? now()->subDays(rand(0, 7)) : null,
                    'approved_by' => rand(0, 1) ? 1 : null,
                    'currency' => 'VND',
                    'conversion_data' => [
                        'product_category' => ['court_booking', 'equipment', 'training'][array_rand(['court_booking', 'equipment', 'training'])],
                        'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                    ],
                ]);
                $conversionsCreated++;
            }
        }

        // Create additional conversions if we haven't reached minimum
        while ($conversionsCreated < $minConversions && $affiliates->isNotEmpty() && $campaigns->isNotEmpty()) {
            $affiliate = $affiliates->random();
            $campaign = $campaigns->random();
            $orderValue = rand(100000, 2000000); // 100k - 2M VND
            $commissionRate = $affiliate->commission_rate;
            $commissionAmount = $orderValue * ($commissionRate / 100);
            $convertedAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23));

            AffConversion::create([
                'affiliate_id' => $affiliate->id,
                'campaign_id' => $campaign->id,
                'link_id' => null,
                'click_id' => null,
                'conversion_type' => ['sale', 'booking', 'signup'][array_rand(['sale', 'booking', 'signup'])],
                'order_id' => 'ORD' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                'customer_id' => 'CUST' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'order_value' => $orderValue,
                'commission_rate' => $commissionRate,
                'commission_amount' => $commissionAmount,
                'status' => ['pending', 'approved', 'approved', 'approved'][array_rand(['pending', 'approved', 'approved', 'approved'])],
                'converted_at' => $convertedAt,
                'created_at' => $convertedAt,
                'approved_at' => rand(0, 1) ? $convertedAt->addDays(rand(1, 3)) : null,
                'approved_by' => rand(0, 1) ? 1 : null,
                'currency' => 'VND',
                'conversion_data' => [
                    'product_category' => ['court_booking', 'equipment', 'training'][array_rand(['court_booking', 'equipment', 'training'])],
                    'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                ],
            ]);
            $conversionsCreated++;
        }

        // Update link conversion counts
        $links = AffLink::all();
        foreach ($links as $link) {
            $conversionCount = AffConversion::where('link_id', $link->id)->where('status', 'approved')->count();
            $link->update(['conversions' => $conversionCount]);
        }
    }

    /**
     * Seed commissions data.
     */
    private function seedCommissions(): void
    {
        $conversions = AffConversion::where('status', 'approved')->get();
        $affiliates = AffAffiliate::where('status', 'active')->get();

        $minCommissions = 12;
        $commissionsCreated = 0;

        // Create commissions from approved conversions
        foreach ($conversions as $conversion) {
            AffCommission::create([
                'affiliate_id' => $conversion->affiliate_id,
                'conversion_id' => $conversion->id,
                'amount' => $conversion->commission_amount,
                'rate' => $conversion->commission_rate,
                'status' => ['pending', 'approved', 'approved', 'paid'][array_rand(['pending', 'approved', 'approved', 'paid'])],
                'notes' => 'Hoa hồng từ conversion #' . $conversion->id,
                'approved_at' => $conversion->approved_at,
                'approved_by' => $conversion->approved_by,
                'paid_at' => rand(0, 1) ? now()->subDays(rand(0, 15)) : null,
                'paid_by' => rand(0, 1) ? 1 : null,
                'created_at' => $conversion->converted_at,
            ]);
            $commissionsCreated++;
        }

        // Create additional commissions if needed
        while ($commissionsCreated < $minCommissions && $affiliates->isNotEmpty()) {
            $affiliate = $affiliates->random();
            $amount = rand(50000, 500000); // 50k - 500k VND
            $createdAt = now()->subDays(rand(0, 30));

            AffCommission::create([
                'affiliate_id' => $affiliate->id,
                'conversion_id' => null, // Manual commission
                'amount' => $amount,
                'rate' => $affiliate->commission_rate,
                'status' => ['pending', 'approved', 'approved', 'paid'][array_rand(['pending', 'approved', 'approved', 'paid'])],
                'notes' => 'Hoa hồng thưởng thành tích tháng ' . $createdAt->format('m/Y'),
                'approved_at' => rand(0, 1) ? $createdAt->addDays(rand(1, 5)) : null,
                'approved_by' => rand(0, 1) ? 1 : null,
                'paid_at' => rand(0, 1) ? $createdAt->addDays(rand(5, 15)) : null,
                'paid_by' => rand(0, 1) ? 1 : null,
                'created_at' => $createdAt,
            ]);
            $commissionsCreated++;
        }
    }

    /**
     * Seed withdrawal requests.
     */
    private function seedWithdrawals(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $minWithdrawals = 10;
        $withdrawalsCreated = 0;

        foreach ($affiliates as $affiliate) {
            // Create 1-3 withdrawal requests per affiliate
            $withdrawalCount = rand(1, 3);

            for ($i = 0; $i < $withdrawalCount && $withdrawalsCreated < $minWithdrawals * 2; $i++) {
                $totalCommissions = AffCommission::where('affiliate_id', $affiliate->id)
                    ->whereIn('status', ['approved', 'paid'])
                    ->sum('amount');

                // Create withdrawal even if no commissions (for demo purposes)
                $withdrawalAmount = $totalCommissions > 0
                    ? min($totalCommissions * 0.8, rand(200000, 1000000))
                    : rand(100000, 500000);

                $requestedAt = now()->subDays(rand(1, 30));
                $statuses = ['pending', 'approved', 'processing', 'completed', 'rejected'];
                $status = $statuses[array_rand($statuses)];

                $fee = $withdrawalAmount * 0.02; // 2% fee
                $netAmount = $withdrawalAmount - $fee;

                AffWithdrawal::create([
                    'affiliate_id' => $affiliate->id,
                    'amount' => $withdrawalAmount,
                    'fee' => $fee,
                    'net_amount' => $netAmount,
                    'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                    'payment_details' => [
                        'account_name' => $affiliate->user->name,
                        'account_number' => str_pad(rand(**********, **********), 10, '0', STR_PAD_LEFT),
                        'bank_name' => ['Vietcombank', 'Techcombank', 'BIDV', 'VietinBank', 'ACB'][array_rand(['Vietcombank', 'Techcombank', 'BIDV', 'VietinBank', 'ACB'])],
                    ],
                    'status' => $status,
                    'requested_at' => $requestedAt,
                    'processed_at' => in_array($status, ['approved', 'processing', 'completed']) ? $requestedAt->addDays(rand(1, 7)) : null,
                    'processed_by' => in_array($status, ['approved', 'processing', 'completed']) ? 1 : null,
                    'processing_notes' => $this->generateWithdrawalNote($requestedAt),
                ]);
                $withdrawalsCreated++;
            }
        }

        // Create additional withdrawals if needed
        while ($withdrawalsCreated < $minWithdrawals && $affiliates->isNotEmpty()) {
            $affiliate = $affiliates->random();
            $requestedAt = now()->subDays(rand(1, 30));
            $status = ['pending', 'approved', 'processing', 'completed'][array_rand(['pending', 'approved', 'processing', 'completed'])];

            $amount = rand(100000, 800000);
            $fee = $amount * 0.02; // 2% fee
            $netAmount = $amount - $fee;

            AffWithdrawal::create([
                'affiliate_id' => $affiliate->id,
                'amount' => $amount,
                'fee' => $fee,
                'net_amount' => $netAmount,
                'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                'payment_details' => [
                    'account_name' => $affiliate->user->name,
                    'account_number' => str_pad(rand(**********, **********), 10, '0', STR_PAD_LEFT),
                    'bank_name' => ['Vietcombank', 'Techcombank', 'BIDV'][array_rand(['Vietcombank', 'Techcombank', 'BIDV'])],
                ],
                'status' => $status,
                'requested_at' => $requestedAt,
                'processed_at' => in_array($status, ['approved', 'processing', 'completed']) ? $requestedAt->addDays(rand(1, 7)) : null,
                'processed_by' => in_array($status, ['approved', 'processing', 'completed']) ? 1 : null,
                'processing_notes' => $this->generateWithdrawalNote($requestedAt),
            ]);
            $withdrawalsCreated++;
        }
    }

    /**
     * Generate withdrawal note.
     */
    private function generateWithdrawalNote($date): string
    {
        $notes = [
            'Yêu cầu rút tiền hoa hồng tháng ' . $date->format('m/Y'),
            'Rút tiền thưởng hiệu suất tháng ' . $date->format('m/Y'),
            'Thanh toán hoa hồng đạt target',
            'Rút tiền hoa hồng tích lũy',
            'Yêu cầu thanh toán hoa hồng quý ' . $date->quarter . '/' . $date->year,
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Generate helper methods for realistic data.
     */
    private function generateLinkTitle(): string
    {
        $titles = [
            'Đặt sân pickleball giá rẻ',
            'Vợt pickleball chất lượng cao',
            'Khóa học pickleball cho người mới',
            'Giải đấu pickleball cuối tuần',
            'Phụ kiện pickleball chính hãng',
            'Sân pickleball gần nhà',
            'Combo đặt sân + thuê vợt',
            'Ưu đãi thành viên VIP',
        ];
        return $titles[array_rand($titles)];
    }

    private function generateLinkDescription(): string
    {
        $descriptions = [
            'Đặt sân pickleball với giá ưu đãi nhất. Hệ thống sân chất lượng cao.',
            'Mua vợt pickleball chính hãng với giá tốt nhất thị trường.',
            'Tham gia khóa học pickleball từ cơ bản đến nâng cao.',
            'Đăng ký tham gia giải đấu pickleball hàng tuần.',
            'Mua phụ kiện pickleball chất lượng với nhiều ưu đãi.',
        ];
        return $descriptions[array_rand($descriptions)];
    }

    private function generateOriginalUrl(): string
    {
        $urls = [
            'https://example.com/dat-san-pickleball',
            'https://example.com/vot-pickleball',
            'https://example.com/khoa-hoc-pickleball',
            'https://example.com/giai-dau-pickleball',
            'https://example.com/phu-kien-pickleball',
        ];
        return $urls[array_rand($urls)];
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 11; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0',
        ];
        return $userAgents[array_rand($userAgents)];
    }

    private function generateReferrerUrl(): ?string
    {
        $referrers = [
            'https://google.com',
            'https://facebook.com',
            'https://instagram.com',
            'https://youtube.com',
            'https://zalo.me',
            null, // Direct traffic
        ];
        return $referrers[array_rand($referrers)];
    }

    /**
     * Seed sample KOLs.
     */
    private function seedKols(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $minKols = 12;
        $kolsCreated = 0;

        // Create KOLs from existing affiliates
        foreach ($affiliates as $affiliate) {
            if ($kolsCreated >= $minKols)
                break;

            $tier = ['micro', 'macro', 'mega'][array_rand(['micro', 'macro', 'mega'])];
            $category = ['influencer', 'blogger', 'youtuber', 'tiktoker', 'celebrity', 'expert'][array_rand(['influencer', 'blogger', 'youtuber', 'tiktoker', 'celebrity', 'expert'])];

            AffKol::create([
                'affiliate_id' => $affiliate->id,
                'stage_name' => $this->generateStageName($category, $affiliate->id),
                'category' => $category,
                'niches' => ['pickleball', 'thể thao', 'sức khỏe', 'lifestyle'],
                'followers_count' => $this->getFollowersByTier($tier),
                'engagement_rate' => rand(200, 800) / 100, // 2-8%
                'social_stats' => [
                    'facebook' => ['followers' => rand(5000, 100000), 'engagement' => rand(200, 600) / 100],
                    'instagram' => ['followers' => rand(10000, 200000), 'engagement' => rand(300, 800) / 100],
                    'youtube' => ['subscribers' => rand(1000, 50000), 'avg_views' => rand(5000, 100000)],
                    'tiktok' => ['followers' => rand(15000, 300000), 'engagement' => rand(400, 1000) / 100],
                ],
                'base_rate' => $this->getRateByTier($tier),
                'preferred_content_type' => ['post', 'story', 'video', 'reel'][array_rand(['post', 'story', 'video', 'reel'])],
                'content_samples' => [
                    'https://example.com/kol-sample-' . $affiliate->id . '-1.jpg',
                    'https://example.com/kol-sample-' . $affiliate->id . '-2.jpg',
                    'https://example.com/kol-sample-' . $affiliate->id . '-3.mp4',
                ],
                'is_verified' => rand(0, 1),
                'tier' => $tier,
                'audience_demographics' => [
                    'age_groups' => ['18-24' => rand(15, 25), '25-34' => rand(35, 45), '35-44' => rand(25, 35), '45+' => rand(5, 15)],
                    'gender' => ['male' => rand(45, 65), 'female' => rand(35, 55)],
                    'locations' => ['HCM' => rand(35, 45), 'HN' => rand(25, 35), 'DN' => rand(10, 20), 'other' => rand(10, 20)],
                ],
                'average_views' => rand(10000, 500000),
                'average_likes' => rand(500, 25000),
                'average_comments' => rand(50, 2500),
                'average_shares' => rand(10, 500),
                'verified_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                'verified_by' => rand(0, 1) ? 1 : null,
                'created_at' => now()->subDays(rand(1, 60)),
            ]);
            $kolsCreated++;
        }

        // Create additional KOLs if needed
        while ($kolsCreated < $minKols) {
            // Get random affiliate or create new one
            $affiliate = $affiliates->random();
            $tier = ['micro', 'macro', 'mega'][array_rand(['micro', 'macro', 'mega'])];
            $category = ['influencer', 'blogger', 'youtuber', 'tiktoker', 'celebrity', 'expert'][array_rand(['influencer', 'blogger', 'youtuber', 'tiktoker', 'celebrity', 'expert'])];

            AffKol::create([
                'affiliate_id' => $affiliate->id,
                'stage_name' => $this->generateStageName($category, $kolsCreated + 100),
                'category' => $category,
                'niches' => ['pickleball', 'thể thao', 'sức khỏe', 'lifestyle', 'fitness'],
                'followers_count' => $this->getFollowersByTier($tier),
                'engagement_rate' => rand(200, 800) / 100,
                'social_stats' => [
                    'facebook' => ['followers' => rand(5000, 100000), 'engagement' => rand(200, 600) / 100],
                    'instagram' => ['followers' => rand(10000, 200000), 'engagement' => rand(300, 800) / 100],
                    'youtube' => ['subscribers' => rand(1000, 50000), 'avg_views' => rand(5000, 100000)],
                    'tiktok' => ['followers' => rand(15000, 300000), 'engagement' => rand(400, 1000) / 100],
                ],
                'base_rate' => $this->getRateByTier($tier),
                'preferred_content_type' => ['post', 'story', 'video', 'reel'][array_rand(['post', 'story', 'video', 'reel'])],
                'content_samples' => [
                    'https://example.com/kol-sample-' . ($kolsCreated + 100) . '-1.jpg',
                    'https://example.com/kol-sample-' . ($kolsCreated + 100) . '-2.jpg',
                ],
                'is_verified' => rand(0, 1),
                'tier' => $tier,
                'audience_demographics' => [
                    'age_groups' => ['18-24' => rand(15, 25), '25-34' => rand(35, 45), '35-44' => rand(25, 35), '45+' => rand(5, 15)],
                    'gender' => ['male' => rand(45, 65), 'female' => rand(35, 55)],
                    'locations' => ['HCM' => rand(35, 45), 'HN' => rand(25, 35), 'DN' => rand(10, 20), 'other' => rand(10, 20)],
                ],
                'average_views' => rand(10000, 500000),
                'average_likes' => rand(500, 25000),
                'average_comments' => rand(50, 2500),
                'average_shares' => rand(10, 500),
                'verified_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                'verified_by' => rand(0, 1) ? 1 : null,
                'created_at' => now()->subDays(rand(1, 60)),
            ]);
            $kolsCreated++;
        }
    }

    /**
     * Generate stage name based on category.
     */
    private function generateStageName($category, $id): string
    {
        $names = [
            'influencer' => ['Pickleball Influencer', 'Sports Guru', 'Fitness Inspiration', 'Healthy Living'],
            'blogger' => ['Pickleball Blogger', 'Sports Writer', 'Fitness Blog', 'Health Tips'],
            'youtuber' => ['Pickleball Channel', 'Sports Tube', 'Fitness Vlog', 'Health TV'],
            'tiktoker' => ['Pickleball TikTok', 'Sports TikTok', 'Fitness TikTok', 'Health TikTok'],
            'celebrity' => ['Sports Celebrity', 'Fitness Celebrity', 'Health Celebrity', 'Pickleball Star'],
            'expert' => ['Pickleball Expert', 'Sports Expert', 'Fitness Expert', 'Health Expert'],
        ];

        $categoryNames = $names[$category] ?? $names['influencer'];
        return $categoryNames[array_rand($categoryNames)] . ' ' . $id;
    }

    /**
     * Get follower count by tier.
     */
    private function getFollowersByTier($tier): int
    {
        return match ($tier) {
            'micro' => rand(1000, 100000),
            'macro' => rand(100000, 1000000),
            'mega' => rand(1000000, 5000000),
            default => rand(10000, 100000)
        };
    }

    /**
     * Get rate by tier.
     */
    private function getRateByTier($tier): int
    {
        return match ($tier) {
            'micro' => rand(500000, 2000000),    // 500k - 2M VND
            'macro' => rand(2000000, 10000000),  // 2M - 10M VND
            'mega' => rand(10000000, 50000000),  // 10M - 50M VND
            default => rand(1000000, 5000000)
        };
    }

    /**
     * Seed KOL contracts.
     */
    private function seedKolContracts(): void
    {
        $kols = AffKol::where('is_verified', true)->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        $minContracts = 10;
        $contractsCreated = 0;

        foreach ($kols as $kol) {
            if ($contractsCreated >= $minContracts * 2)
                break;

            // Each KOL can have 1-3 contracts
            $contractCount = rand(1, 3);

            for ($i = 0; $i < $contractCount && $contractsCreated < $minContracts * 2; $i++) {
                $campaign = $campaigns->random();
                $startDate = now()->subDays(rand(1, 30));
                $endDate = $startDate->copy()->addDays(rand(30, 90));
                $status = ['draft', 'sent', 'signed', 'active', 'completed', 'cancelled', 'expired'][array_rand(['draft', 'sent', 'signed', 'active', 'completed', 'cancelled', 'expired'])];

                $totalValue = rand(5000000, 50000000); // 5M - 50M VND

                AffKolContract::create([
                    'kol_id' => $kol->id,
                    'campaign_id' => $campaign->id,
                    'contract_number' => 'KOL-' . str_pad($contractsCreated + 1, 4, '0', STR_PAD_LEFT),
                    'title' => 'Hợp đồng KOL - ' . $campaign->name,
                    'description' => 'Hợp đồng hợp tác quảng bá chiến dịch ' . $campaign->name,
                    'type' => ['one_time', 'recurring', 'exclusive', 'non_exclusive'][array_rand(['one_time', 'recurring', 'exclusive', 'non_exclusive'])],
                    'status' => $status,
                    'total_value' => $totalValue,
                    'commission_rate' => rand(500, 2000) / 100, // 5-20%
                    'fixed_fee' => $totalValue * 0.8, // 80% fixed fee
                    'required_posts' => rand(5, 20),
                    'completed_posts' => $status === 'completed' ? rand(5, 20) : rand(0, 5),
                    'deliverables' => [
                        'posts' => rand(5, 20),
                        'stories' => rand(10, 30),
                        'videos' => rand(2, 8),
                        'live_sessions' => rand(1, 3),
                    ],
                    'content_requirements' => [
                        'brand_mention' => true,
                        'hashtags' => ['#pickleball', '#' . Str::slug($campaign->name)],
                        'content_approval' => true,
                        'posting_schedule' => 'flexible',
                    ],
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'deadline' => $endDate->subDays(rand(1, 5))->format('Y-m-d'),
                    'payment_schedule' => [
                        'advance' => ['percentage' => 30, 'amount' => $totalValue * 0.3, 'due_date' => $startDate->format('Y-m-d')],
                        'milestone_1' => ['percentage' => 40, 'amount' => $totalValue * 0.4, 'due_date' => $startDate->copy()->addDays(15)->format('Y-m-d')],
                        'final' => ['percentage' => 30, 'amount' => $totalValue * 0.3, 'due_date' => $endDate->format('Y-m-d')],
                    ],
                    'terms_conditions' => 'Điều khoản và điều kiện hợp đồng KOL chuẩn',
                    'contract_file_url' => 'https://contracts.example.com/kol-' . ($contractsCreated + 1) . '.pdf',
                    'sent_at' => in_array($status, ['sent', 'signed', 'active', 'completed']) ? $startDate->subDays(rand(1, 5)) : null,
                    'signed_at' => in_array($status, ['signed', 'active', 'completed']) ? $startDate->subDays(rand(1, 3)) : null,
                    'completed_at' => $status === 'completed' ? $endDate->subDays(rand(1, 5)) : null,
                    'created_by' => 1,
                    'created_at' => $startDate->subDays(rand(5, 15)),
                ]);
                $contractsCreated++;
            }
        }

        // Create additional contracts if needed
        while ($contractsCreated < $minContracts && $kols->isNotEmpty() && $campaigns->isNotEmpty()) {
            $kol = $kols->random();
            $campaign = $campaigns->random();
            $startDate = now()->subDays(rand(1, 30));
            $endDate = $startDate->copy()->addDays(rand(30, 90));
            $status = ['draft', 'sent', 'signed', 'active', 'completed'][array_rand(['draft', 'sent', 'signed', 'active', 'completed'])];

            $totalValue = rand(5000000, 30000000);

            AffKolContract::create([
                'kol_id' => $kol->id,
                'campaign_id' => $campaign->id,
                'contract_number' => 'KOL-' . str_pad($contractsCreated + 1, 4, '0', STR_PAD_LEFT),
                'title' => 'Hợp đồng KOL - ' . $campaign->name,
                'description' => 'Hợp đồng hợp tác quảng bá chiến dịch ' . $campaign->name,
                'type' => ['one_time', 'recurring'][array_rand(['one_time', 'recurring'])],
                'status' => $status,
                'total_value' => $totalValue,
                'commission_rate' => rand(500, 1500) / 100, // 5-15%
                'fixed_fee' => $totalValue * 0.7, // 70% fixed fee
                'required_posts' => rand(5, 15),
                'completed_posts' => $status === 'completed' ? rand(5, 15) : rand(0, 3),
                'deliverables' => [
                    'posts' => rand(5, 20),
                    'stories' => rand(10, 30),
                    'videos' => rand(2, 8),
                ],
                'content_requirements' => [
                    'brand_mention' => true,
                    'hashtags' => ['#pickleball'],
                    'content_approval' => true,
                ],
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'deadline' => $endDate->subDays(rand(1, 3))->format('Y-m-d'),
                'payment_schedule' => [
                    'advance' => ['percentage' => 50, 'amount' => $totalValue * 0.5],
                    'final' => ['percentage' => 50, 'amount' => $totalValue * 0.5],
                ],
                'terms_conditions' => 'Điều khoản hợp đồng KOL',
                'contract_file_url' => 'https://contracts.example.com/kol-' . ($contractsCreated + 1) . '.pdf',
                'sent_at' => in_array($status, ['sent', 'signed', 'active', 'completed']) ? $startDate->subDays(rand(1, 5)) : null,
                'signed_at' => in_array($status, ['signed', 'active', 'completed']) ? $startDate->subDays(rand(1, 3)) : null,
                'completed_at' => $status === 'completed' ? $endDate->subDays(rand(1, 3)) : null,
                'created_by' => 1,
                'created_at' => $startDate->subDays(rand(5, 15)),
            ]);
            $contractsCreated++;
        }
    }
}
