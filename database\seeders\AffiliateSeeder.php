<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AffSetting;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffLink;
use App\Models\AffKol;
use App\Models\AffClick;
use App\Models\AffConversion;
use App\Models\AffCommission;
use App\Models\AffWithdrawal;
use App\Models\AffAffiliateCampaign;
use App\Models\User;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AffiliateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed affiliate settings
        $this->seedSettings();

        // Seed affiliates from existing users
        $this->seedAffiliatesFromUsers();

        // Seed sample campaigns
        $this->seedCampaigns();

        // Seed affiliate-campaign relationships
        $this->seedAffiliateCampaigns();

        // Seed affiliate links
        $this->seedAffiliateLinks();

        // Seed clicks data
        $this->seedClicks();

        // Seed conversions data
        $this->seedConversions();

        // Seed commissions data
        $this->seedCommissions();

        // Seed withdrawal requests
        $this->seedWithdrawals();

        // Seed sample KOLs
        $this->seedKols();
    }

    /**
     * Seed affiliate system settings.
     */
    private function seedSettings(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'aff_system_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable affiliate system',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'aff_auto_approve_affiliates',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Auto approve new affiliate registrations',
                'group' => 'general',
                'is_public' => false,
            ],
            [
                'key' => 'aff_cookie_duration',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Cookie duration in days',
                'group' => 'tracking',
                'is_public' => true,
            ],

            // Commission Settings
            [
                'key' => 'aff_default_commission_rate',
                'value' => '5.00',
                'type' => 'number',
                'description' => 'Default commission rate percentage',
                'group' => 'commission',
                'is_public' => true,
            ],
            [
                'key' => 'aff_tier_rates',
                'value' => json_encode([
                    'bronze' => 3.0,
                    'silver' => 5.0,
                    'gold' => 7.0,
                    'platinum' => 10.0,
                ]),
                'type' => 'json',
                'description' => 'Commission rates by tier',
                'group' => 'commission',
                'is_public' => true,
            ],

            // Payment Settings
            [
                'key' => 'aff_minimum_payout',
                'value' => '100000',
                'type' => 'number',
                'description' => 'Minimum payout amount in VND',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_schedule',
                'value' => 'monthly',
                'type' => 'string',
                'description' => 'Payment schedule (weekly, monthly, quarterly)',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_methods',
                'value' => json_encode(['bank_transfer', 'momo', 'zalopay']),
                'type' => 'json',
                'description' => 'Available payment methods',
                'group' => 'payment',
                'is_public' => true,
            ],

            // Email Settings
            [
                'key' => 'aff_welcome_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send welcome email to new affiliates',
                'group' => 'email',
                'is_public' => false,
            ],
            [
                'key' => 'aff_commission_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send email notifications for new commissions',
                'group' => 'email',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            AffSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Seed affiliates from existing users.
     */
    private function seedAffiliatesFromUsers(): void
    {
        // Get existing users (excluding admin users)
        $users = User::whereNotIn('email', ['<EMAIL>', '<EMAIL>'])
            ->take(10)
            ->get();

        if ($users->isEmpty()) {
            // Create sample users if none exist
            $sampleUsers = [
                [
                    'name' => 'Nguyễn Văn A',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234567',
                ],
                [
                    'name' => 'Trần Thị B',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234568',
                ],
                [
                    'name' => 'Lê Văn C',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234569',
                ],
                [
                    'name' => 'Phạm Thị D',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234570',
                ],
                [
                    'name' => 'Hoàng Văn E',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '0901234571',
                ],
            ];

            foreach ($sampleUsers as $userData) {
                $users->push(User::create($userData));
            }
        }

        $tiers = ['bronze', 'silver', 'gold', 'platinum'];
        $statuses = ['active', 'active', 'active', 'pending', 'inactive']; // More active affiliates

        foreach ($users as $index => $user) {
            // Skip if user already has affiliate record
            if (AffAffiliate::where('user_id', $user->id)->exists()) {
                continue;
            }

            $tier = $tiers[array_rand($tiers)];
            $status = $statuses[array_rand($statuses)];

            // Commission rates based on tier
            $commissionRates = [
                'bronze' => rand(300, 500) / 100,   // 3-5%
                'silver' => rand(500, 700) / 100,   // 5-7%
                'gold' => rand(700, 1000) / 100,    // 7-10%
                'platinum' => rand(1000, 1500) / 100, // 10-15%
            ];

            AffAffiliate::create([
                'user_id' => $user->id,
                'referral_code' => Str::upper(Str::random(6) . $user->id),
                'status' => $status,
                'tier' => $tier,
                'commission_rate' => $commissionRates[$tier],
                'bio' => $this->generateAffiliateBio(),
                'website_url' => rand(0, 1) ? 'https://example-' . $user->id . '.com' : null,
                'social_profiles' => [
                    'facebook' => 'https://facebook.com/' . Str::slug($user->name),
                    'instagram' => 'https://instagram.com/' . Str::slug($user->name),
                    'youtube' => rand(0, 1) ? 'https://youtube.com/@' . Str::slug($user->name) : null,
                ],
                'approved_at' => $status === 'active' ? now()->subDays(rand(1, 30)) : null,
                'approved_by' => $status === 'active' ? 1 : null,
                'last_activity_at' => $status === 'active' ? now()->subHours(rand(1, 72)) : null,
                'application_notes' => $status === 'pending' ? 'Đang chờ xét duyệt hồ sơ' : null,
            ]);
        }
    }

    /**
     * Generate random affiliate bio.
     */
    private function generateAffiliateBio(): string
    {
        $bios = [
            'Chuyên gia pickleball với 5 năm kinh nghiệm. Đam mê chia sẻ kiến thức về thể thao.',
            'Huấn luyện viên pickleball chuyên nghiệp. Giúp mọi người yêu thích môn thể thao này.',
            'Blogger thể thao, chuyên viết về pickleball và các môn thể thao vợt.',
            'Influencer thể thao với hơn 50k followers. Quảng bá lối sống khỏe mạnh.',
            'Vận động viên pickleball chuyên nghiệp. Tham gia nhiều giải đấu trong nước.',
            'Chủ câu lạc bộ pickleball địa phương. Kết nối cộng đồng người chơi.',
            'Nhà phân phối thiết bị thể thao. Chuyên về vợt và phụ kiện pickleball.',
            'Content creator về thể thao. Tạo video hướng dẫn kỹ thuật pickleball.',
        ];

        return $bios[array_rand($bios)];
    }

    /**
     * Seed sample campaigns.
     */
    private function seedCampaigns(): void
    {
        $campaigns = [
            [
                'name' => 'Khuyến mãi mùa hè 2025',
                'campaign_code' => 'SUMMER2025',
                'description' => 'Chiến dịch khuyến mãi đặt sân pickleball mùa hè',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 5.00,
                'budget' => 10000000, // 10M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(3)->toDateString(),
                'target_audience' => 'Người chơi pickleball, thể thao',
                'terms_conditions' => 'Áp dụng cho đặt sân online',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Sản phẩm vợt pickleball',
                'campaign_code' => 'RACKET2025',
                'description' => 'Quảng bá sản phẩm vợt pickleball chất lượng cao',
                'type' => 'product',
                'status' => 'active',
                'commission_rate' => 8.00,
                'budget' => 5000000, // 5M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(2)->toDateString(),
                'target_audience' => 'Người chơi pickleball chuyên nghiệp',
                'terms_conditions' => 'Áp dụng cho mua sản phẩm trên 500k',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Giải đấu pickleball 2025',
                'campaign_code' => 'TOURNAMENT2025',
                'description' => 'Quảng bá giải đấu pickleball lớn nhất năm',
                'type' => 'event',
                'status' => 'draft',
                'commission_rate' => 10.00,
                'budget' => 20000000, // 20M VND
                'start_date' => now()->addMonth()->toDateString(),
                'end_date' => now()->addMonths(4)->toDateString(),
                'target_audience' => 'Cộng đồng pickleball Việt Nam',
                'terms_conditions' => 'Hoa hồng cho mỗi đăng ký tham gia',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            AffCampaign::create($campaignData);
        }
    }

    /**
     * Seed affiliate-campaign relationships.
     */
    private function seedAffiliateCampaigns(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        foreach ($affiliates as $affiliate) {
            // Each affiliate joins 1-3 campaigns
            $campaignCount = rand(1, min(3, $campaigns->count()));
            $selectedCampaigns = $campaigns->random($campaignCount);

            foreach ($selectedCampaigns as $campaign) {
                AffAffiliateCampaign::create([
                    'affiliate_id' => $affiliate->id,
                    'campaign_id' => $campaign->id,
                    'status' => $campaign->auto_approve_affiliates ? 'approved' : ['pending', 'approved'][array_rand(['pending', 'approved'])],
                    'joined_at' => now()->subDays(rand(1, 30)),
                    'approved_at' => now()->subDays(rand(0, 15)),
                    'approved_by' => 1,
                ]);
            }
        }
    }

    /**
     * Seed affiliate links.
     */
    private function seedAffiliateLinks(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();
        $campaigns = AffCampaign::where('status', 'active')->get();

        foreach ($affiliates as $affiliate) {
            // Each affiliate creates 2-5 links
            $linkCount = rand(2, 5);

            for ($i = 0; $i < $linkCount; $i++) {
                $campaign = $campaigns->random();
                $shortCode = Str::lower(Str::random(8));

                AffLink::create([
                    'affiliate_id' => $affiliate->id,
                    'campaign_id' => rand(0, 1) ? $campaign->id : null,
                    'title' => $this->generateLinkTitle(),
                    'description' => $this->generateLinkDescription(),
                    'original_url' => $this->generateOriginalUrl(),
                    'short_code' => $shortCode,
                    'affiliate_url' => url('/aff/' . $shortCode),
                    'type' => ['product', 'category', 'landing_page', 'custom'][array_rand(['product', 'category', 'landing_page', 'custom'])],
                    'utm_source' => 'affiliate',
                    'utm_medium' => 'referral',
                    'utm_campaign' => $campaign->campaign_code,
                    'utm_content' => $affiliate->referral_code,
                    'is_active' => rand(0, 10) > 1, // 90% active
                    'clicks' => 0, // Will be updated by clicks seeder
                    'unique_clicks' => 0,
                    'conversions' => 0,
                    'last_clicked_at' => null,
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }

    /**
     * Seed clicks data.
     */
    private function seedClicks(): void
    {
        $links = AffLink::where('is_active', true)->get();
        $campaigns = AffCampaign::all();

        foreach ($links as $link) {
            // Generate 10-200 clicks per link over the last 30 days
            $clickCount = rand(10, 200);

            for ($i = 0; $i < $clickCount; $i++) {
                $clickedAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

                AffClick::create([
                    'affiliate_id' => $link->affiliate_id,
                    'link_id' => $link->id,
                    'campaign_id' => $link->campaign_id,
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateRandomUserAgent(),
                    'referrer_url' => $this->generateReferrerUrl(),
                    'device_type' => ['desktop', 'mobile', 'tablet'][array_rand(['desktop', 'mobile', 'tablet'])],
                    'browser' => ['Chrome', 'Firefox', 'Safari', 'Edge'][array_rand(['Chrome', 'Firefox', 'Safari', 'Edge'])],
                    'country' => 'Vietnam',
                    'city' => ['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'][array_rand(['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Can Tho'])],
                    'is_unique' => rand(0, 1),
                    'clicked_at' => $clickedAt,
                    'created_at' => $clickedAt,
                ]);
            }

            // Update link statistics
            $totalClicks = AffClick::where('link_id', $link->id)->count();
            $uniqueClicks = AffClick::where('link_id', $link->id)->where('is_unique', true)->count();
            $lastClick = AffClick::where('link_id', $link->id)->latest('clicked_at')->first();

            $link->update([
                'clicks' => $totalClicks,
                'unique_clicks' => $uniqueClicks,
                'last_clicked_at' => $lastClick?->clicked_at,
            ]);
        }
    }

    /**
     * Seed conversions data.
     */
    private function seedConversions(): void
    {
        $clicks = AffClick::all();

        // Convert 2-8% of clicks to conversions
        $conversionRate = 0.05; // 5% average conversion rate
        $conversionsToCreate = (int) ($clicks->count() * $conversionRate);

        $selectedClicks = $clicks->random(min($conversionsToCreate, $clicks->count()));

        foreach ($selectedClicks as $click) {
            $orderValue = rand(100000, 2000000); // 100k - 2M VND
            $commissionRate = AffAffiliate::find($click->affiliate_id)->commission_rate ?? 5.0;
            $commissionAmount = $orderValue * ($commissionRate / 100);

            AffConversion::create([
                'affiliate_id' => $click->affiliate_id,
                'campaign_id' => $click->campaign_id,
                'link_id' => $click->link_id,
                'click_id' => $click->id,
                'conversion_type' => ['sale', 'booking', 'signup'][array_rand(['sale', 'booking', 'signup'])],
                'order_id' => 'ORD' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),
                'customer_id' => 'CUST' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'order_value' => $orderValue,
                'commission_rate' => $commissionRate,
                'commission_amount' => $commissionAmount,
                'status' => ['pending', 'approved', 'approved', 'approved'][array_rand(['pending', 'approved', 'approved', 'approved'])], // 75% approved
                'converted_at' => $click->clicked_at->addMinutes(rand(1, 120)), // Convert within 2 hours of click
                'approved_at' => rand(0, 1) ? now()->subDays(rand(0, 7)) : null,
                'approved_by' => rand(0, 1) ? 1 : null,
                'currency' => 'VND',
                'conversion_data' => [
                    'product_category' => ['court_booking', 'equipment', 'training'][array_rand(['court_booking', 'equipment', 'training'])],
                    'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                ],
            ]);
        }

        // Update link conversion counts
        $links = AffLink::all();
        foreach ($links as $link) {
            $conversionCount = AffConversion::where('link_id', $link->id)->where('status', 'approved')->count();
            $link->update(['conversions' => $conversionCount]);
        }
    }

    /**
     * Seed commissions data.
     */
    private function seedCommissions(): void
    {
        $conversions = AffConversion::where('status', 'approved')->get();

        foreach ($conversions as $conversion) {
            AffCommission::create([
                'affiliate_id' => $conversion->affiliate_id,
                'conversion_id' => $conversion->id,
                'amount' => $conversion->commission_amount,
                'rate' => $conversion->commission_rate,
                'status' => ['pending', 'approved', 'approved', 'paid'][array_rand(['pending', 'approved', 'approved', 'paid'])],
                'currency' => 'VND',
                'notes' => 'Hoa hồng từ conversion #' . $conversion->id,
                'approved_at' => $conversion->approved_at,
                'approved_by' => $conversion->approved_by,
                'paid_at' => rand(0, 1) ? now()->subDays(rand(0, 15)) : null,
                'paid_by' => rand(0, 1) ? 1 : null,
                'created_at' => $conversion->converted_at,
            ]);
        }
    }

    /**
     * Seed withdrawal requests.
     */
    private function seedWithdrawals(): void
    {
        $affiliates = AffAffiliate::where('status', 'active')->get();

        foreach ($affiliates as $affiliate) {
            // Some affiliates have withdrawal requests
            if (rand(0, 1)) {
                $totalCommissions = AffCommission::where('affiliate_id', $affiliate->id)
                    ->whereIn('status', ['approved', 'paid'])
                    ->sum('amount');

                if ($totalCommissions > 100000) { // Minimum 100k VND
                    $withdrawalAmount = min($totalCommissions * 0.8, rand(200000, 1000000)); // Withdraw up to 80% of earnings

                    AffWithdrawal::create([
                        'affiliate_id' => $affiliate->id,
                        'amount' => $withdrawalAmount,
                        'currency' => 'VND',
                        'payment_method' => ['bank_transfer', 'momo', 'zalopay'][array_rand(['bank_transfer', 'momo', 'zalopay'])],
                        'payment_details' => [
                            'account_name' => $affiliate->user->name,
                            'account_number' => '**********',
                            'bank_name' => 'Vietcombank',
                        ],
                        'status' => ['pending', 'approved', 'processing', 'completed'][array_rand(['pending', 'approved', 'processing', 'completed'])],
                        'requested_at' => now()->subDays(rand(1, 15)),
                        'processed_at' => rand(0, 1) ? now()->subDays(rand(0, 10)) : null,
                        'processed_by' => rand(0, 1) ? 1 : null,
                        'notes' => 'Yêu cầu rút tiền hoa hồng tháng ' . now()->format('m/Y'),
                    ]);
                }
            }
        }
    }

    /**
     * Generate helper methods for realistic data.
     */
    private function generateLinkTitle(): string
    {
        $titles = [
            'Đặt sân pickleball giá rẻ',
            'Vợt pickleball chất lượng cao',
            'Khóa học pickleball cho người mới',
            'Giải đấu pickleball cuối tuần',
            'Phụ kiện pickleball chính hãng',
            'Sân pickleball gần nhà',
            'Combo đặt sân + thuê vợt',
            'Ưu đãi thành viên VIP',
        ];
        return $titles[array_rand($titles)];
    }

    private function generateLinkDescription(): string
    {
        $descriptions = [
            'Đặt sân pickleball với giá ưu đãi nhất. Hệ thống sân chất lượng cao.',
            'Mua vợt pickleball chính hãng với giá tốt nhất thị trường.',
            'Tham gia khóa học pickleball từ cơ bản đến nâng cao.',
            'Đăng ký tham gia giải đấu pickleball hàng tuần.',
            'Mua phụ kiện pickleball chất lượng với nhiều ưu đãi.',
        ];
        return $descriptions[array_rand($descriptions)];
    }

    private function generateOriginalUrl(): string
    {
        $urls = [
            'https://example.com/dat-san-pickleball',
            'https://example.com/vot-pickleball',
            'https://example.com/khoa-hoc-pickleball',
            'https://example.com/giai-dau-pickleball',
            'https://example.com/phu-kien-pickleball',
        ];
        return $urls[array_rand($urls)];
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 11; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0',
        ];
        return $userAgents[array_rand($userAgents)];
    }

    private function generateReferrerUrl(): string
    {
        $referrers = [
            'https://google.com',
            'https://facebook.com',
            'https://instagram.com',
            'https://youtube.com',
            'https://zalo.me',
            null, // Direct traffic
        ];
        return $referrers[array_rand($referrers)];
    }

    /**
     * Seed sample KOLs.
     */
    private function seedKols(): void
    {
        $affiliates = AffAffiliate::take(2)->get();

        foreach ($affiliates as $affiliate) {
            AffKol::create([
                'affiliate_id' => $affiliate->id,
                'stage_name' => 'Pickleball Pro ' . $affiliate->id,
                'category' => ['influencer', 'blogger', 'youtuber'][array_rand(['influencer', 'blogger', 'youtuber'])],
                'niches' => ['pickleball', 'thể thao', 'sức khỏe'],
                'followers_count' => rand(10000, 500000),
                'engagement_rate' => rand(200, 800) / 100, // 2-8%
                'social_stats' => [
                    'facebook' => ['followers' => rand(5000, 100000), 'engagement' => rand(200, 600) / 100],
                    'instagram' => ['followers' => rand(10000, 200000), 'engagement' => rand(300, 800) / 100],
                    'youtube' => ['subscribers' => rand(1000, 50000), 'avg_views' => rand(5000, 100000)],
                ],
                'base_rate' => rand(1000000, 10000000), // 1-10M VND per post
                'preferred_content_type' => ['post', 'story', 'video'][array_rand(['post', 'story', 'video'])],
                'content_samples' => [
                    'https://example.com/sample1.jpg',
                    'https://example.com/sample2.jpg',
                ],
                'is_verified' => rand(0, 1),
                'tier' => ['micro', 'macro'][array_rand(['micro', 'macro'])],
                'audience_demographics' => [
                    'age_groups' => ['18-24' => 20, '25-34' => 40, '35-44' => 30, '45+' => 10],
                    'gender' => ['male' => 60, 'female' => 40],
                    'locations' => ['HCM' => 40, 'HN' => 30, 'DN' => 15, 'other' => 15],
                ],
                'average_views' => rand(10000, 500000),
                'average_likes' => rand(500, 25000),
                'average_comments' => rand(50, 2500),
                'average_shares' => rand(10, 500),
                'verified_at' => rand(0, 1) ? now() : null,
                'verified_by' => rand(0, 1) ? 1 : null,
            ]);
        }
    }
}
