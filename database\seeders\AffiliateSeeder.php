<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AffSetting;
use App\Models\AffAffiliate;
use App\Models\AffCampaign;
use App\Models\AffLink;
use App\Models\AffKol;
use App\Models\User;
use Illuminate\Support\Str;

class AffiliateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed affiliate settings
        $this->seedSettings();
        
        // Seed sample affiliates
        $this->seedAffiliates();
        
        // Seed sample campaigns
        $this->seedCampaigns();
        
        // Seed sample KOLs
        $this->seedKols();
    }

    /**
     * Seed affiliate system settings.
     */
    private function seedSettings(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'aff_system_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable affiliate system',
                'group' => 'general',
                'is_public' => true,
            ],
            [
                'key' => 'aff_auto_approve_affiliates',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Auto approve new affiliate registrations',
                'group' => 'general',
                'is_public' => false,
            ],
            [
                'key' => 'aff_cookie_duration',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Cookie duration in days',
                'group' => 'tracking',
                'is_public' => true,
            ],
            
            // Commission Settings
            [
                'key' => 'aff_default_commission_rate',
                'value' => '5.00',
                'type' => 'number',
                'description' => 'Default commission rate percentage',
                'group' => 'commission',
                'is_public' => true,
            ],
            [
                'key' => 'aff_tier_rates',
                'value' => json_encode([
                    'bronze' => 3.0,
                    'silver' => 5.0,
                    'gold' => 7.0,
                    'platinum' => 10.0,
                ]),
                'type' => 'json',
                'description' => 'Commission rates by tier',
                'group' => 'commission',
                'is_public' => true,
            ],
            
            // Payment Settings
            [
                'key' => 'aff_minimum_payout',
                'value' => '100000',
                'type' => 'number',
                'description' => 'Minimum payout amount in VND',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_schedule',
                'value' => 'monthly',
                'type' => 'string',
                'description' => 'Payment schedule (weekly, monthly, quarterly)',
                'group' => 'payment',
                'is_public' => true,
            ],
            [
                'key' => 'aff_payment_methods',
                'value' => json_encode(['bank_transfer', 'momo', 'zalopay']),
                'type' => 'json',
                'description' => 'Available payment methods',
                'group' => 'payment',
                'is_public' => true,
            ],
            
            // Email Settings
            [
                'key' => 'aff_welcome_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send welcome email to new affiliates',
                'group' => 'email',
                'is_public' => false,
            ],
            [
                'key' => 'aff_commission_email_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Send email notifications for new commissions',
                'group' => 'email',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            AffSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Seed sample affiliates.
     */
    private function seedAffiliates(): void
    {
        // Create sample users for affiliates
        $users = [
            [
                'name' => 'Nguyễn Văn A',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '0901234567',
            ],
            [
                'name' => 'Trần Thị B',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '0901234568',
            ],
            [
                'name' => 'Lê Văn C',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '0901234569',
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create($userData);
            
            AffAffiliate::create([
                'user_id' => $user->id,
                'referral_code' => Str::upper(Str::random(8)),
                'status' => 'active',
                'tier' => ['bronze', 'silver', 'gold'][array_rand(['bronze', 'silver', 'gold'])],
                'commission_rate' => rand(300, 1000) / 100, // 3-10%
                'bio' => 'Affiliate marketer chuyên về thể thao và pickleball',
                'website_url' => 'https://example.com',
                'social_profiles' => [
                    'facebook' => 'https://facebook.com/example',
                    'instagram' => 'https://instagram.com/example',
                ],
                'approved_at' => now(),
                'approved_by' => 1, // Assuming admin user ID is 1
                'last_activity_at' => now(),
            ]);
        }
    }

    /**
     * Seed sample campaigns.
     */
    private function seedCampaigns(): void
    {
        $campaigns = [
            [
                'name' => 'Khuyến mãi mùa hè 2025',
                'campaign_code' => 'SUMMER2025',
                'description' => 'Chiến dịch khuyến mãi đặt sân pickleball mùa hè',
                'type' => 'service',
                'status' => 'active',
                'commission_rate' => 5.00,
                'budget' => 10000000, // 10M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(3)->toDateString(),
                'target_audience' => 'Người chơi pickleball, thể thao',
                'terms_conditions' => 'Áp dụng cho đặt sân online',
                'auto_approve_affiliates' => true,
                'created_by' => 1,
            ],
            [
                'name' => 'Sản phẩm vợt pickleball',
                'campaign_code' => 'RACKET2025',
                'description' => 'Quảng bá sản phẩm vợt pickleball chất lượng cao',
                'type' => 'product',
                'status' => 'active',
                'commission_rate' => 8.00,
                'budget' => 5000000, // 5M VND
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonths(2)->toDateString(),
                'target_audience' => 'Người chơi pickleball chuyên nghiệp',
                'terms_conditions' => 'Áp dụng cho mua sản phẩm trên 500k',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
            [
                'name' => 'Giải đấu pickleball 2025',
                'campaign_code' => 'TOURNAMENT2025',
                'description' => 'Quảng bá giải đấu pickleball lớn nhất năm',
                'type' => 'event',
                'status' => 'draft',
                'commission_rate' => 10.00,
                'budget' => 20000000, // 20M VND
                'start_date' => now()->addMonth()->toDateString(),
                'end_date' => now()->addMonths(4)->toDateString(),
                'target_audience' => 'Cộng đồng pickleball Việt Nam',
                'terms_conditions' => 'Hoa hồng cho mỗi đăng ký tham gia',
                'auto_approve_affiliates' => false,
                'created_by' => 1,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            AffCampaign::create($campaignData);
        }
    }

    /**
     * Seed sample KOLs.
     */
    private function seedKols(): void
    {
        $affiliates = AffAffiliate::take(2)->get();
        
        foreach ($affiliates as $affiliate) {
            AffKol::create([
                'affiliate_id' => $affiliate->id,
                'stage_name' => 'Pickleball Pro ' . $affiliate->id,
                'category' => ['influencer', 'blogger', 'youtuber'][array_rand(['influencer', 'blogger', 'youtuber'])],
                'niches' => ['pickleball', 'thể thao', 'sức khỏe'],
                'followers_count' => rand(10000, 500000),
                'engagement_rate' => rand(200, 800) / 100, // 2-8%
                'social_stats' => [
                    'facebook' => ['followers' => rand(5000, 100000), 'engagement' => rand(200, 600) / 100],
                    'instagram' => ['followers' => rand(10000, 200000), 'engagement' => rand(300, 800) / 100],
                    'youtube' => ['subscribers' => rand(1000, 50000), 'avg_views' => rand(5000, 100000)],
                ],
                'base_rate' => rand(1000000, 10000000), // 1-10M VND per post
                'preferred_content_type' => ['post', 'story', 'video'][array_rand(['post', 'story', 'video'])],
                'content_samples' => [
                    'https://example.com/sample1.jpg',
                    'https://example.com/sample2.jpg',
                ],
                'is_verified' => rand(0, 1),
                'tier' => ['micro', 'macro'][array_rand(['micro', 'macro'])],
                'audience_demographics' => [
                    'age_groups' => ['18-24' => 20, '25-34' => 40, '35-44' => 30, '45+' => 10],
                    'gender' => ['male' => 60, 'female' => 40],
                    'locations' => ['HCM' => 40, 'HN' => 30, 'DN' => 15, 'other' => 15],
                ],
                'average_views' => rand(10000, 500000),
                'average_likes' => rand(500, 25000),
                'average_comments' => rand(50, 2500),
                'average_shares' => rand(10, 500),
                'verified_at' => rand(0, 1) ? now() : null,
                'verified_by' => rand(0, 1) ? 1 : null,
            ]);
        }
    }
}
