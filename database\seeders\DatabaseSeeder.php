<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Database\Seeders\BookingEventSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run(): void
    {
        $this->call([
            ProvinceSeeder::class,
            DistrictSeeder::class,
            WardSeeder::class,
            RolePermissionSeeder::class,
            BusinessSeeder::class,
            BusinessSettingSeeder::class,
            BranchSeeder::class,
            BranchImageSeeder::class,
            CustomUserSeeder::class,
            ModuleSeeder::class,
            ModuleProfileSeeder::class,
            CourtSeeder::class,
            CourtPriceSeeder::class,
            CourtServiceSeeder::class,
            CustomerSeeder::class,
            CourtBookingSeeder::class,
            BookingSeeder::class,
            BookingEventSeeder::class,
            ReviewSeeder::class,
            NotificationSeeder::class,
            AmenitySeeder::class,
            BranchAmenitySeeder::class,
            PaymentMethodSeeder::class,
            PaymentSeeder::class,
            CategorySeeder::class,
            ProductSeeder::class,
            FirstTimeDiscountCouponSeeder::class,
            EduLecturerSeeder::class,
            EduCourseSeeder::class,
            EduStudentSeeder::class,
            EduReviewSeeder::class,
            TournamentSeeder::class,
        ]);

        BranchSeeder::updateBranchPrices();
    }
}
