<?php

namespace Database\Seeders;

use App\Models\EduReview;
use App\Models\EduCourse;
use App\Models\EduStudent;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EduReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $enrollments = DB::table('edu_course_student')
            ->where('status', '!=', 'dropped')
            ->get();

        if ($enrollments->isEmpty()) {
            $this->command->info('Không có học viên nào đăng ký khóa học. Vui lòng chạy EduStudentSeeder trước.');
            return;
        }

        $this->command->info('Đang tạo đánh giá cho khóa học...');

        $reviewComments = [
            [
                'rating' => 5,
                'comments' => [
                    'Khóa học rất tuyệt vời! Giảng viên hướng dẫn rất chi tiết và dễ hiểu.',
                    'Tôi đã học được rất nhiều kỹ thuật mới. Highly recommended!',
                    'Phương pháp giảng dạy rất hay, từ cơ bản đến nâng cao một cách logic.',
                    'Giảng viên rất tận tình và chuyên nghiệp. Khóa học đáng giá tiền.',
                    'Nội dung khóa học phong phú, thực hành nhiều. Tôi rất hài lòng.'
                ]
            ],
            [
                'rating' => 4,
                'comments' => [
                    'Khóa học tốt, nội dung hay nhưng thời gian hơi ngắn.',
                    'Giảng viên giỏi, chỉ có điều lớp học hơi đông.',
                    'Chất lượng tốt, nhưng mong muốn có thêm bài tập thực hành.',
                    'Khóa học bổ ích, tuy nhiên một số phần hơi khó theo.',
                    'Giảng viên kiến thức tốt, cách truyền đạt cần cải thiện thêm.'
                ]
            ],
            [
                'rating' => 3,
                'comments' => [
                    'Khóa học ổn, phù hợp với người mới bắt đầu.',
                    'Nội dung cơ bản, mong muốn có thêm kiến thức nâng cao.',
                    'Giảng viên tốt nhưng tài liệu còn thiếu.',
                    'Khóa học đáp ứng được mong đợi cơ bản của tôi.',
                    'Cần cải thiện thêm về phần thực hành.'
                ]
            ],
            [
                'rating' => 2,
                'comments' => [
                    'Khóa học chưa đáp ứng được kỳ vọng của tôi.',
                    'Nội dung hơi sơ sài, cần bổ sung thêm.',
                    'Giảng viên thiếu kinh nghiệm thực tế.',
                    'Thời gian học không phù hợp với nội dung.',
                    'Cần cải thiện chất lượng bài giảng.'
                ]
            ],
            [
                'rating' => 1,
                'comments' => [
                    'Khóa học không như mong đợi, cần cải thiện nhiều.',
                    'Nội dung không phù hợp với level được quảng cáo.',
                    'Giảng viên không chuẩn bị bài kỹ.',
                    'Không đáng giá tiền bỏ ra.',
                    'Rất thất vọng với chất lượng khóa học.'
                ]
            ]
        ];

        DB::beginTransaction();

        try {
            $reviewCount = 0;

            foreach ($enrollments as $enrollment) {

                if (rand(1, 100) <= 60) {

                    $rating = $this->getWeightedRandomRating();

                    $ratingGroup = $reviewComments[$rating - 1];
                    $comment = $ratingGroup['comments'][array_rand($ratingGroup['comments'])];

                    EduReview::create([
                        'edu_course_id' => $enrollment->edu_course_id,
                        'edu_student_id' => $enrollment->edu_student_id,
                        'rating' => $rating,
                        'comment' => $comment,
                        'is_published' => rand(1, 100) <= 90,
                        'created_at' => now()->subDays(rand(1, 60)),
                        'updated_at' => now()->subDays(rand(1, 60)),
                    ]);

                    $reviewCount++;
                }
            }

            $courses = EduCourse::all();
            foreach ($courses as $course) {
                $reviews = EduReview::where('edu_course_id', $course->id)
                    ->where('is_published', true)
                    ->get();

                if ($reviews->count() > 0) {
                    $averageRating = $reviews->avg('rating');
                    $course->update([
                        'rating' => round($averageRating, 1),
                        'total_reviews' => $reviews->count()
                    ]);
                }
            }

            DB::commit();
            $this->command->info("Đã tạo thành công {$reviewCount} đánh giá!");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Lỗi khi tạo đánh giá: ' . $e->getMessage());
        }
    }

    /**
     * Get weighted random rating (higher ratings more likely)
     */
    private function getWeightedRandomRating(): int
    {
        $ratings = [5, 4, 3, 2, 1];
        $weights = [40, 30, 20, 7, 3];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($ratings as $index => $rating) {
            $cumulative += $weights[$index];
            if ($random <= $cumulative) {
                return $rating;
            }
        }

        return 5;
    }
}
