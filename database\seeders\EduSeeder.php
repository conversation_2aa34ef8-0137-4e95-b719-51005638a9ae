<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class EduSeeder extends Seeder
{
    /**
     * Run the education module database seeds.
     */
    public function run(): void
    {
        $this->command->info('🎓 Bắt đầu tạo dữ liệu cho module Giáo dục Pickleball...');

        $this->call([
            EduLecturerSeeder::class,
            EduCourseSeeder::class,
            EduStudentSeeder::class,
            EduReviewSeeder::class,
        ]);

        $this->command->info('✅ Hoàn thành tạo dữ liệu module Giáo dục!');
        $this->command->info('📊 Đã tạo:');
        $this->command->info('   - Giảng viên và thông tin chi tiết');
        $this->command->info('   - Khóa học đa dạng về Pickleball');
        $this->command->info('   - Học viên và đăng ký khóa học');
        $this->command->info('   - <PERSON><PERSON><PERSON> gi<PERSON> và feedback');
    }
}
