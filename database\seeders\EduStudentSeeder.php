<?php

namespace Database\Seeders;

use App\Models\EduStudent;
use App\Models\EduCourse;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class EduStudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $lecturerUserIds = DB::table('edu_lecturers')->pluck('user_id')->toArray();

        $users = User::whereDoesntHave('eduStudent')
            ->where('id', '>', 1)
            ->whereNotIn('id', $lecturerUserIds)
            ->take(20)
            ->get();

        if ($users->isEmpty()) {
            $this->command->info('Không có user nào để tạo học viên. Vui lòng chạy UserSeeder trước.');
            return;
        }

        $studentRole = Role::firstOrCreate(['name' => 'student']);

        $courses = EduCourse::where('status', 'active')->get();

        if ($courses->isEmpty()) {
            $this->command->info('Không có khóa học nào để đăng ký. Vui lòng chạy EduCourseSeeder trước.');
            return;
        }

        $this->command->info('Đang tạo ' . count($users) . ' học viên...');

        DB::beginTransaction();

        try {
            foreach ($users as $user) {

                $student = EduStudent::create([
                    'user_id' => $user->id,
                    'enrolled_courses' => [],
                    'status' => 'active',
                ]);

                if (!$user->hasRole('student')) {
                    $user->assignRole($studentRole);
                }

                $coursesToEnroll = $courses->random(rand(1, 3));

                foreach ($coursesToEnroll as $course) {

                    if ($course->enrolled_students < $course->max_students) {

                        $student->courses()->attach($course->id, [
                            'status' => $this->getRandomEnrollmentStatus(),
                            'progress' => rand(0, 100),
                            'completed_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        $course->increment('enrolled_students');
                    }
                }

                $this->command->info("Đã tạo học viên: {$user->name}");
            }

            DB::commit();
            $this->command->info('Đã tạo thành công ' . count($users) . ' học viên!');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Lỗi khi tạo học viên: ' . $e->getMessage());
        }
    }

    /**
     * Get random interests for students
     */
    private function getRandomInterests(): array
    {
        $allInterests = [
            'Kỹ thuật cơ bản',
            'Chiến thuật nâng cao',
            'Thể lực và sức bền',
            'Thi đấu chuyên nghiệp',
            'Pickleball trẻ em',
            'Phục hồi chấn thương',
            'Dinh dưỡng thể thao',
            'Tâm lý thi đấu',
            'Huấn luyện viên',
            'Cộng đồng Pickleball'
        ];

        return collect($allInterests)->random(rand(2, 5))->values()->toArray();
    }

    /**
     * Get random enrollment status
     */
    private function getRandomEnrollmentStatus(): string
    {
        $statuses = ['enrolled', 'completed', 'in_progress', 'dropped'];
        $weights = [50, 20, 25, 5];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($statuses as $index => $status) {
            $cumulative += $weights[$index];
            if ($random <= $cumulative) {
                return $status;
            }
        }

        return 'enrolled';
    }
}
