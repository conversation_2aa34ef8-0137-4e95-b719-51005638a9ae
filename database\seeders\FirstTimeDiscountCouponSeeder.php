<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MarketCoupon;
use Carbon\Carbon;

class FirstTimeDiscountCouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $existingCoupon = MarketCoupon::where('first_order_only', true)->first();
        if (!$existingCoupon) {
            MarketCoupon::create([
                'code' => 'FREE',
                'name' => 'Khuyến mãi khách hàng mới',
                'description' => 'Giảm 10% cho đơn hàng đầu tiên của bạn',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 100000.00,
                'maximum_discount' => 100000.00,
                'usage_limit' => null,
                'usage_limit_per_user' => 1,
                'used_count' => 0,
                'is_active' => true,
                'starts_at' => Carbon::now(),
                'expires_at' => null,
                'applicable_products' => null,
                'applicable_categories' => null,
                'excluded_products' => null,
                'first_order_only' => true,
            ]);
            $this->command->info('First-time buyer discount coupon created successfully.');
        } else {
            $this->command->info('First-time buyer discount coupon already exists with code: ' . $existingCoupon->code);
        }
    }
}
