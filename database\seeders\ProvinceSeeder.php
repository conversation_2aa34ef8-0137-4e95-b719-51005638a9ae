<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProvinceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Truncate the table first
        DB::table('provinces')->truncate();

        // Read and execute SQL file
        $sqlFile = database_path('../data/provinces.sql');

        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);

            // Split SQL statements and execute each one
            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !str_starts_with($statement, '--') && !str_starts_with($statement, '/*')) {
                    try {
                        DB::unprepared($statement);
                    } catch (\Exception $e) {
                        // Skip invalid statements (like comments or empty lines)
                        continue;
                    }
                }
            }

            $this->command->info('Provinces imported successfully from SQL file.');
        } else {
            // Fallback: Insert data manually if SQL file is not found
            $provinces = [
                ['id' => 1, 'province_code' => '01', 'name' => 'Thành phố Hà Nội', 'short_name' => 'Thành phố Hà Nội', 'code' => 'HNI', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
                ['id' => 2, 'province_code' => '04', 'name' => 'Cao Bằng', 'short_name' => 'Cao Bằng', 'code' => 'CBG', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 3, 'province_code' => '08', 'name' => 'Tuyên Quang', 'short_name' => 'Tuyên Quang', 'code' => 'TGQ', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 4, 'province_code' => '11', 'name' => 'Điện Biên', 'short_name' => 'Điện Biên', 'code' => 'DBN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 5, 'province_code' => '12', 'name' => 'Lai Châu', 'short_name' => 'Lai Châu', 'code' => 'LCU', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 6, 'province_code' => '14', 'name' => 'Sơn La', 'short_name' => 'Sơn La', 'code' => 'SLA', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 7, 'province_code' => '15', 'name' => 'Lào Cai', 'short_name' => 'Lào Cai', 'code' => 'LCI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 8, 'province_code' => '19', 'name' => 'Thái Nguyên', 'short_name' => 'Thái Nguyên', 'code' => 'TNN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 9, 'province_code' => '20', 'name' => 'Lạng Sơn', 'short_name' => 'Lạng Sơn', 'code' => 'LSN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                ['id' => 10, 'province_code' => '22', 'name' => 'Quảng Ninh', 'short_name' => 'Quảng Ninh', 'code' => 'QNH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
                // Add more provinces here or continue reading from SQL file
            ];

            foreach ($provinces as $province) {
                DB::table('provinces')->insert($province + ['created_at' => now(), 'updated_at' => now()]);
            }

            $this->command->info('Provinces seeded with fallback data.');
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
}
