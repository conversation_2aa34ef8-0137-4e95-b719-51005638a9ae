<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProvinceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        DB::table('provinces')->truncate();

        $sqlFile = database_path('../data/provinces.sql');

        if (file_exists($sqlFile)) {
            $this->command->info('SQL file found at: ' . $sqlFile);

            $sql = file_get_contents($sqlFile);
            preg_match_all('/INSERT INTO `provinces`.*?;/s', $sql, $matches);

            if (!empty($matches[0])) {
                foreach ($matches[0] as $insertStatement) {
                    try {
                        DB::unprepared($insertStatement);
                        $this->command->info('Executed: ' . substr($insertStatement, 0, 50) . '...');
                    } catch (\Exception $e) {
                        $this->command->error('Error executing statement: ' . $e->getMessage());
                    }
                }
                $this->command->info('Provinces imported successfully from SQL file.');
            } else {
                $this->command->error('No INSERT statements found in SQL file.');
                $this->fallbackInsert();
            }
        } else {
            $this->command->error('SQL file not found at: ' . $sqlFile);
            $this->fallbackInsert();
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        $this->command->info('Total provinces after seeding: ' . DB::table('provinces')->count());
    }

    private function fallbackInsert()
    {
        $provinces = [
            ['id' => 1, 'province_code' => '01', 'name' => 'Thành phố Hà Nội', 'short_name' => 'Thành phố Hà Nội', 'code' => 'HNI', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 2, 'province_code' => '04', 'name' => 'Cao Bằng', 'short_name' => 'Cao Bằng', 'code' => 'CBG', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 3, 'province_code' => '08', 'name' => 'Tuyên Quang', 'short_name' => 'Tuyên Quang', 'code' => 'TGQ', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 4, 'province_code' => '11', 'name' => 'Điện Biên', 'short_name' => 'Điện Biên', 'code' => 'DBN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 5, 'province_code' => '12', 'name' => 'Lai Châu', 'short_name' => 'Lai Châu', 'code' => 'LCU', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 6, 'province_code' => '14', 'name' => 'Sơn La', 'short_name' => 'Sơn La', 'code' => 'SLA', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 7, 'province_code' => '15', 'name' => 'Lào Cai', 'short_name' => 'Lào Cai', 'code' => 'LCI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 8, 'province_code' => '19', 'name' => 'Thái Nguyên', 'short_name' => 'Thái Nguyên', 'code' => 'TNN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 9, 'province_code' => '20', 'name' => 'Lạng Sơn', 'short_name' => 'Lạng Sơn', 'code' => 'LSN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 10, 'province_code' => '22', 'name' => 'Quảng Ninh', 'short_name' => 'Quảng Ninh', 'code' => 'QNH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 11, 'province_code' => '24', 'name' => 'Bắc Ninh', 'short_name' => 'Bắc Ninh', 'code' => 'BNH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 12, 'province_code' => '25', 'name' => 'Phú Thọ', 'short_name' => 'Phú Thọ', 'code' => 'PTO', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 13, 'province_code' => '31', 'name' => 'Thành phố Hải Phòng', 'short_name' => 'Thành phố Hải Phòng', 'code' => 'HPG', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 14, 'province_code' => '33', 'name' => 'Hưng Yên', 'short_name' => 'Hưng Yên', 'code' => 'HYN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 15, 'province_code' => '37', 'name' => 'Ninh Bình', 'short_name' => 'Ninh Bình', 'code' => 'NBH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 16, 'province_code' => '38', 'name' => 'Thanh Hóa', 'short_name' => 'Thanh Hóa', 'code' => 'THA', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 17, 'province_code' => '40', 'name' => 'Nghệ An', 'short_name' => 'Nghệ An', 'code' => 'NAN', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 18, 'province_code' => '42', 'name' => 'Hà Tĩnh', 'short_name' => 'Hà Tĩnh', 'code' => 'HTH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 19, 'province_code' => '44', 'name' => 'Quảng Trị', 'short_name' => 'Quảng Trị', 'code' => 'QTI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 20, 'province_code' => '46', 'name' => 'Thành phố Huế', 'short_name' => 'Thành phố Huế', 'code' => 'TTH', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 21, 'province_code' => '48', 'name' => 'Thành phố Đà Nẵng', 'short_name' => 'Thành phố Đà Nẵng', 'code' => 'DNG', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 22, 'province_code' => '51', 'name' => 'Quảng Ngãi', 'short_name' => 'Quảng Ngãi', 'code' => 'QNI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 23, 'province_code' => '52', 'name' => 'Gia Lai', 'short_name' => 'Gia Lai', 'code' => 'GLI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 24, 'province_code' => '56', 'name' => 'Khánh Hòa', 'short_name' => 'Khánh Hòa', 'code' => 'KHA', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 25, 'province_code' => '66', 'name' => 'Đắk Lắk', 'short_name' => 'Đắk Lắk', 'code' => 'DLK', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 26, 'province_code' => '68', 'name' => 'Lâm Đồng', 'short_name' => 'Lâm Đồng', 'code' => 'LDG', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 27, 'province_code' => '75', 'name' => 'Đồng Nai', 'short_name' => 'Đồng Nai', 'code' => 'DNI', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 28, 'province_code' => '79', 'name' => 'Thành phố Hồ Chí Minh', 'short_name' => 'Thành phố Hồ Chí Minh', 'code' => 'HCM', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 29, 'province_code' => '80', 'name' => 'Tây Ninh', 'short_name' => 'Tây Ninh', 'code' => 'TNH', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 30, 'province_code' => '82', 'name' => 'Đồng Tháp', 'short_name' => 'Đồng Tháp', 'code' => 'DTP', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 31, 'province_code' => '86', 'name' => 'Vĩnh Long', 'short_name' => 'Vĩnh Long', 'code' => 'VLG', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 32, 'province_code' => '91', 'name' => 'An Giang', 'short_name' => 'An Giang', 'code' => 'AGG', 'place_type' => 'Tỉnh', 'country' => 'VN'],
            ['id' => 33, 'province_code' => '92', 'name' => 'Thành phố Cần Thơ', 'short_name' => 'Thành phố Cần Thơ', 'code' => 'CTO', 'place_type' => 'Thành phố Trung Ương', 'country' => 'VN'],
            ['id' => 34, 'province_code' => '96', 'name' => 'Cà Mau', 'short_name' => 'Cà Mau', 'code' => 'CMU', 'place_type' => 'Tỉnh', 'country' => 'VN'],
        ];

        foreach ($provinces as $province) {
            DB::table('provinces')->insert($province + ['created_at' => now(), 'updated_at' => now()]);
        }

        $this->command->info('Provinces seeded with fallback data.');
    }
}
