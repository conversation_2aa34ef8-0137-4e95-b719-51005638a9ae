<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SystemSetting;

class SystemSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // About Settings
        $this->createAboutSettings();

        // Terms Settings  
        $this->createTermsSettings();

        // General Settings
        $this->createGeneralSettings();

        // Email Settings
        $this->createEmailSettings();

        // SEO Settings
        $this->createSeoSettings();

        // Social Settings
        $this->createSocialSettings();

        // Payment Settings
        $this->createPaymentSettings();

        // Landing Page Settings
        $this->createLandingPageSettings();

        // UI Settings
        $this->createUiSettings();
    }

    private function createAboutSettings()
    {
        $settings = [
            [
                'key' => 'about_title',
                'value' => 'Giới thiệu về Pickleball',
                'display_name' => 'Tiêu đề trang giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_image',
                'value' => '/storage/about.jpg',
                'display_name' => 'Hình ảnh trang giới thiệu',
                'type' => 'image'
            ],
            [
                'key' => 'about_description_1',
                'value' => 'Pickleball là một môn thể thao kết hợp giữa tennis, bóng bàn và cầu lông.',
                'display_name' => 'Mô tả 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_description_2',
                'value' => 'Pickleball được phát triển vào năm 1965 và ngày càng phổ biến trên toàn thế giới.',
                'display_name' => 'Mô tả 2',
                'type' => 'textarea'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'about',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createTermsSettings()
    {
        $settings = [
            [
                'key' => 'terms_title',
                'value' => 'Điều khoản sử dụng',
                'display_name' => 'Tiêu đề trang điều khoản',
                'type' => 'text'
            ],
            [
                'key' => 'terms_content',
                'value' => 'Mục đích và phạm vi thu thập thông tin
Việc thu thập thông tin của khách hàng mục đích quản lý Người Tiêu Dùng liên quan đến các hoạt động mua sản phẩm trên website và kịp thời xử lý các tình huống phát sinh (nếu có). Thông tin cá nhân thu thập được sẽ chỉ được sử dụng trong việc kinh doanh bán sản phẩm chúng tôi và nhằm hỗ trợ khách hàng khi gặp những vẫn đề liên quan đến việc mua sản phẩm.

Thông tin cá nhân mà PIBA thu thập bao gồm:

Họ và tên
Địa chỉ
Điện thoại
Email
Ngoài thông tin cá nhân là các thông tin về dịch vụ:

Tên sản phẩm
Số lượng
Đặc tính các sản phẩm
Công ty thu thập các thông tin trên tại các trang: đăng ký / đăng nhập và trang liên hệ

Phạm vi sử dụng thông tin
Thông tin cá nhân thu thập được sẽ chỉ được PIBA sử dụng trong nội bộ công ty và cho một hoặc tất cả các mục đích sau đây:

Hỗ trợ khách hàng
Cung cấp thông tin liên quan đến dịch vụ
Xử lý đơn đặt hàng và cung cấp dịch vụ và thông tin qua trang web của chúng tôi theo yêu cầu của bạn
Chúng tôi có thể sẽ gửi thông tin sản phẩm, dịch vụ mới, thông tin về các sự kiện sắp tới hoặc thông tin tuyển dụng nếu quý khách đăng kí nhận email thông báo.

Ngoài ra, chúng tôi sẽ sử dụng thông tin bạn cung cấp để hỗ trợ quản lý tài khoản khách hàng; xác nhận và thực hiện các giao dịch tài chính liên quan đến các khoản thanh toán trực tuyến của bạn.

Thời gian lưu trữ thông tin
Đối với thông tin cá nhân, PIBA chỉ xóa đi dữ liệu này nếu khách hàng có yêu cầu.

Những người hoặc tổ chức có thể được tiếp cận với thông tin cá nhân
Đối tượng được tiếp cận với thông tin cá nhân của khách hàng thuộc một trong những trường hợp sau:

Công ty PIBA là đơn vị chủ thể của sản phẩm PIBA / PIBA.vn
Cung cấp cho cơ quan quản lý nhà nước có thẩm quyền khi có yêu cầu.
Địa chỉ của đơn vị thu thập và quản lý thông tin cá nhân
Công ty TNHH PIBA

Địa chỉ:
Abc Street, Xyz City, 123456, Country

Điện thoại:
+841234567890

Website:
piba.vn

Email:
<EMAIL>

Phương tiện và công cụ để người dùng tiếp cận và chỉnh sửa dữ liệu cá nhân của mình
PIBA không thu thập thông tin khách hàng qua trang web, thông tin cá nhân khách hàng được thực hiện thu thập qua email liên hệ đặt mua sản phẩm, dịch vụ gửi về hộp mail của chúng tôi: <EMAIL>. Bạn có thể liên hệ địa chỉ email cùng số điện thoại trên để yêu cầu PIBA chỉnh sửa dữ liệu cá nhân của mình.

Cơ chế tiếp nhận và giải quyết khiếu nại của người tiêu dùng
Tại HiSport, việc bảo vệ thông tin cá nhân của bạn là rất quan trọng, bạn được đảm bảo rằng thông tin cung cấp cho chúng tôi sẽ được mật HiSport cam kết không chia sẻ, bán hoặc cho thuê thông tin cá nhân của bạn cho bất kỳ người nào khác. HiSport cam kết chỉ sử dụng các thông tin của bạn vào các trường hợp sau:

Nâng cao chất lượng dịch vụ dành cho khách hàng
Giải quyết các tranh chấp, khiếu nại
Khi cơ quan pháp luật có yêu cầu
PIBA hiểu rằng quyền lợi của bạn trong việc bảo vệ thông tin cá nhân cũng chính là trách nhiệm của chúng tôi nên trong bất kỳ trường hợp có thắc mắc, góp ý nào liên quan đến chính sách bảo mật của PIBA, và liên quan đến việc thông tin cá nhân bị sử dụng sai mục đích hoặc phạm vi đã thông báo vui lòng liên hệ qua số hotline +841234567890 hoặc email: <EMAIL>',
                'display_name' => 'Nội dung điều khoản',
                'type' => 'richtext'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'terms',
                $setting['display_name'],
                $setting['type']
            );
        }
    }
}