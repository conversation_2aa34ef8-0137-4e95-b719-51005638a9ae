<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SystemSetting;

class SystemSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // About Settings
        $this->createAboutSettings();

        // Terms Settings  
        $this->createTermsSettings();

        // General Settings
        $this->createGeneralSettings();

        // Email Settings
        $this->createEmailSettings();

        // SEO Settings
        $this->createSeoSettings();

        // Social Settings
        $this->createSocialSettings();

        // Payment Settings
        $this->createPaymentSettings();

        // Landing Page Settings
        $this->createLandingPageSettings();

        // UI Settings
        $this->createUiSettings();
    }

    private function createAboutSettings()
    {
        $settings = [
            [
                'key' => 'about_title',
                'value' => 'Giới thiệu về Pickleball',
                'display_name' => 'Tiêu đề trang giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_image',
                'value' => '/storage/about.jpg',
                'display_name' => 'Hình ảnh trang giới thiệu',
                'type' => 'image'
            ],
            [
                'key' => 'about_description_1',
                'value' => 'Pickleball là một môn thể thao kết hợp giữa tennis, bóng bàn và cầu lông.',
                'display_name' => 'Mô tả 1',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_description_2',
                'value' => 'Pickleball được phát triển vào năm 1965 và ngày càng phổ biến trên toàn thế giới.',
                'display_name' => 'Mô tả 2',
                'type' => 'textarea'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'about',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createTermsSettings()
    {
        $settings = [
            [
                'key' => 'terms_title',
                'value' => 'Điều khoản sử dụng',
                'display_name' => 'Tiêu đề trang điều khoản',
                'type' => 'text'
            ],
            [
                'key' => 'terms_content',
                'value' => 'Mục đích và phạm vi thu thập thông tin
Việc thu thập thông tin của khách hàng mục đích quản lý Người Tiêu Dùng liên quan đến các hoạt động mua sản phẩm trên website và kịp thời xử lý các tình huống phát sinh (nếu có). Thông tin cá nhân thu thập được sẽ chỉ được sử dụng trong việc kinh doanh bán sản phẩm chúng tôi và nhằm hỗ trợ khách hàng khi gặp những vẫn đề liên quan đến việc mua sản phẩm.

Thông tin cá nhân mà PIBA thu thập bao gồm:

Họ và tên
Địa chỉ
Điện thoại
Email
Ngoài thông tin cá nhân là các thông tin về dịch vụ:

Tên sản phẩm
Số lượng
Đặc tính các sản phẩm
Công ty thu thập các thông tin trên tại các trang: đăng ký / đăng nhập và trang liên hệ

Phạm vi sử dụng thông tin
Thông tin cá nhân thu thập được sẽ chỉ được PIBA sử dụng trong nội bộ công ty và cho một hoặc tất cả các mục đích sau đây:

Hỗ trợ khách hàng
Cung cấp thông tin liên quan đến dịch vụ
Xử lý đơn đặt hàng và cung cấp dịch vụ và thông tin qua trang web của chúng tôi theo yêu cầu của bạn
Chúng tôi có thể sẽ gửi thông tin sản phẩm, dịch vụ mới, thông tin về các sự kiện sắp tới hoặc thông tin tuyển dụng nếu quý khách đăng kí nhận email thông báo.

Ngoài ra, chúng tôi sẽ sử dụng thông tin bạn cung cấp để hỗ trợ quản lý tài khoản khách hàng; xác nhận và thực hiện các giao dịch tài chính liên quan đến các khoản thanh toán trực tuyến của bạn.

Thời gian lưu trữ thông tin
Đối với thông tin cá nhân, PIBA chỉ xóa đi dữ liệu này nếu khách hàng có yêu cầu.

Những người hoặc tổ chức có thể được tiếp cận với thông tin cá nhân
Đối tượng được tiếp cận với thông tin cá nhân của khách hàng thuộc một trong những trường hợp sau:

Công ty PIBA là đơn vị chủ thể của sản phẩm PIBA / PIBA.vn
Cung cấp cho cơ quan quản lý nhà nước có thẩm quyền khi có yêu cầu.
Địa chỉ của đơn vị thu thập và quản lý thông tin cá nhân
Công ty TNHH PIBA

Địa chỉ:
Abc Street, Xyz City, 123456, Country

Điện thoại:
+841234567890

Website:
piba.vn

Email:
<EMAIL>

Phương tiện và công cụ để người dùng tiếp cận và chỉnh sửa dữ liệu cá nhân của mình
PIBA không thu thập thông tin khách hàng qua trang web, thông tin cá nhân khách hàng được thực hiện thu thập qua email liên hệ đặt mua sản phẩm, dịch vụ gửi về hộp mail của chúng tôi: <EMAIL>. Bạn có thể liên hệ địa chỉ email cùng số điện thoại trên để yêu cầu PIBA chỉnh sửa dữ liệu cá nhân của mình.

Cơ chế tiếp nhận và giải quyết khiếu nại của người tiêu dùng
Tại HiSport, việc bảo vệ thông tin cá nhân của bạn là rất quan trọng, bạn được đảm bảo rằng thông tin cung cấp cho chúng tôi sẽ được mật HiSport cam kết không chia sẻ, bán hoặc cho thuê thông tin cá nhân của bạn cho bất kỳ người nào khác. HiSport cam kết chỉ sử dụng các thông tin của bạn vào các trường hợp sau:

Nâng cao chất lượng dịch vụ dành cho khách hàng
Giải quyết các tranh chấp, khiếu nại
Khi cơ quan pháp luật có yêu cầu
PIBA hiểu rằng quyền lợi của bạn trong việc bảo vệ thông tin cá nhân cũng chính là trách nhiệm của chúng tôi nên trong bất kỳ trường hợp có thắc mắc, góp ý nào liên quan đến chính sách bảo mật của PIBA, và liên quan đến việc thông tin cá nhân bị sử dụng sai mục đích hoặc phạm vi đã thông báo vui lòng liên hệ qua số hotline +841234567890 hoặc email: <EMAIL>',
                'display_name' => 'Nội dung điều khoản',
                'type' => 'richtext'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'terms',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createGeneralSettings()
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'Pickleball',
                'display_name' => 'Tên trang web',
                'type' => 'text'
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Đặt sân Pickleball dễ dàng',
                'display_name' => 'Slogan/Tagline',
                'type' => 'text'
            ],
            [
                'key' => 'site_logo',
                'value' => '/storage/logo.png',
                'display_name' => 'Logo chính',
                'type' => 'image'
            ],
            [
                'key' => 'show_logo_text',
                'value' => 'off',
                'display_name' => 'Bật logo chữ bên cạnh logo chính',
                'type' => 'select'
            ],
            [
                'key' => 'logo_text',
                'value' => 'Pickleball',
                'display_name' => 'Tên logo',
                'type' => 'text'
            ],
            [
                'key' => 'logo_text_color',
                'value' => '#333333',
                'display_name' => 'Màu chữ logo',
                'type' => 'color'
            ],
            [
                'key' => 'site_favicon',
                'value' => '/storage/favicon.ico',
                'display_name' => 'Logo thu nhỏ/Favicon',
                'type' => 'image'
            ],
            [
                'key' => 'primary_color',
                'value' => '#ee0033',
                'display_name' => 'Màu chủ đạo',
                'type' => 'color'
            ],
            [
                'key' => 'secondary_color',
                'value' => '#333333',
                'display_name' => 'Màu phụ',
                'type' => 'color'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'display_name' => 'Email liên hệ chính',
                'type' => 'email'
            ],
            [
                'key' => 'contact_phone',
                'value' => '+84 123 456 789',
                'display_name' => 'Số điện thoại hỗ trợ',
                'type' => 'text'
            ],
            [
                'key' => 'contact_address',
                'value' => 'Số 123, Đường ABC, Quận XYZ, TP.HCM',
                'display_name' => 'Địa chỉ văn phòng',
                'type' => 'textarea'
            ],
            [
                'key' => 'working_hours',
                'value' => 'Thứ 2 - Thứ 6: 8:00 - 17:30, Thứ 7: 8:00 - 12:00, Chủ nhật: Nghỉ',
                'display_name' => 'Giờ làm việc',
                'type' => 'textarea'
            ],
            [
                'key' => 'timezone',
                'value' => 'Asia/Ho_Chi_Minh',
                'display_name' => 'Múi giờ mặc định',
                'type' => 'select',
            ],
            [
                'key' => 'default_language',
                'value' => 'vi',
                'display_name' => 'Ngôn ngữ mặc định',
                'type' => 'select',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => 'off',
                'display_name' => 'Chế độ bảo trì',
                'type' => 'select',
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'Hệ thống đang được bảo trì. Vui lòng quay lại sau.',
                'display_name' => 'Thông báo bảo trì',
                'type' => 'textarea'
            ],
            [
                'key' => 'email_verification',
                'value' => 'on',
                'display_name' => 'Xác thực email khi đăng ký',
                'type' => 'select',
            ],
            [
                'key' => 'clear_cache',
                'value' => '',
                'display_name' => 'Xóa cache',
                'type' => 'button'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'general',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createEmailSettings()
    {
        $settings = [
            [
                'key' => 'mail_driver',
                'value' => 'smtp',
                'display_name' => 'Driver Email',
                'type' => 'select'
            ],
            [
                'key' => 'mail_host',
                'value' => 'smtp.gmail.com',
                'display_name' => 'SMTP Host',
                'type' => 'text'
            ],
            [
                'key' => 'mail_port',
                'value' => '587',
                'display_name' => 'SMTP Port',
                'type' => 'text'
            ],
            [
                'key' => 'mail_username',
                'value' => '<EMAIL>',
                'display_name' => 'SMTP Username',
                'type' => 'text'
            ],
            [
                'key' => 'mail_password',
                'value' => '',
                'display_name' => 'SMTP Password',
                'type' => 'password'
            ],
            [
                'key' => 'mail_encryption',
                'value' => 'tls',
                'display_name' => 'SMTP Encryption',
                'type' => 'select'
            ],
            [
                'key' => 'mail_from_address',
                'value' => '<EMAIL>',
                'display_name' => 'Địa chỉ email gửi',
                'type' => 'email'
            ],
            [
                'key' => 'mail_from_name',
                'value' => 'Pickleball',
                'display_name' => 'Tên người gửi',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'email',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createSeoSettings()
    {
        $settings = [
            [
                'key' => 'meta_title',
                'value' => 'Pickleball - Đặt sân chơi Pickleball trực tuyến',
                'display_name' => 'Meta Title',
                'type' => 'text'
            ],
            [
                'key' => 'meta_description',
                'value' => 'Đặt sân chơi Pickleball trực tuyến, tìm kiếm và đặt sân một cách dễ dàng.',
                'display_name' => 'Meta Description',
                'type' => 'textarea'
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'pickleball, đặt sân, sân chơi, thể thao',
                'display_name' => 'Meta Keywords',
                'type' => 'text'
            ],
            [
                'key' => 'google_analytics',
                'value' => '',
                'display_name' => 'Google Analytics ID',
                'type' => 'text'
            ],
            [
                'key' => 'google_tag_manager',
                'value' => '',
                'display_name' => 'Google Tag Manager ID',
                'type' => 'text'
            ],
            [
                'key' => 'google_site_verification',
                'value' => '',
                'display_name' => 'Google Site Verification',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'seo',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createSocialSettings()
    {
        $settings = [
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/',
                'display_name' => 'Facebook URL',
                'type' => 'url'
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'display_name' => 'Twitter URL',
                'type' => 'url'
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/',
                'display_name' => 'Instagram URL',
                'type' => 'url'
            ],
            [
                'key' => 'youtube_url',
                'value' => 'https://youtube.com/',
                'display_name' => 'YouTube URL',
                'type' => 'url'
            ],
            [
                'key' => 'linkedin_url',
                'value' => '',
                'display_name' => 'LinkedIn URL',
                'type' => 'url'
            ],
            [
                'key' => 'tiktok_url',
                'value' => '',
                'display_name' => 'TikTok URL',
                'type' => 'url'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'social',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createPaymentSettings()
    {
        $settings = [
            [
                'key' => 'payment_currency',
                'value' => 'VND',
                'display_name' => 'Đơn vị tiền tệ',
                'type' => 'select'
            ],
            [
                'key' => 'payment_deadline',
                'value' => '60',
                'display_name' => 'Thời gian thanh toán',
                'type' => 'number'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'payment',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createLandingPageSettings()
    {
        $settings = [
            [
                'key' => 'landing_page_type',
                'value' => 'default',
                'display_name' => 'Kiểu trang chủ',
                'type' => 'select'
            ],
            [
                'key' => 'landing_custom_html',
                'value' => '',
                'display_name' => 'HTML tùy chỉnh',
                'type' => 'textarea'
            ],
            [
                'key' => 'hero_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Hero',
                'type' => 'select'
            ],
            [
                'key' => 'hero_title',
                'value' => 'Đặt sân Pickleball dễ dàng',
                'display_name' => 'Tiêu đề Hero',
                'type' => 'text'
            ],
            [
                'key' => 'hero_subtitle',
                'value' => 'Tìm và đặt sân Pickleball gần bạn chỉ với vài thao tác đơn giản',
                'display_name' => 'Mô tả Hero',
                'type' => 'textarea'
            ],
            [
                'key' => 'hero_image',
                'value' => '/storage/landing/hero.jpg',
                'display_name' => 'Hình ảnh Hero',
                'type' => 'image'
            ],
            [
                'key' => 'hero_button_text',
                'value' => 'Đặt sân ngay',
                'display_name' => 'Nút Hero',
                'type' => 'text'
            ],
            [
                'key' => 'hero_button_url',
                'value' => '/search',
                'display_name' => 'Liên kết nút Hero',
                'type' => 'text'
            ],
            [
                'key' => 'features_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Tính năng',
                'type' => 'select'
            ],
            [
                'key' => 'features_title',
                'value' => 'Tính năng nổi bật',
                'display_name' => 'Tiêu đề Tính năng',
                'type' => 'text'
            ],
            [
                'key' => 'features_subtitle',
                'value' => 'Khám phá những tính năng tuyệt vời của chúng tôi',
                'display_name' => 'Mô tả Tính năng',
                'type' => 'textarea'
            ],
            [
                'key' => 'modules_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Modules',
                'type' => 'select'
            ],
            [
                'key' => 'modules_title',
                'value' => 'Các phần của hệ thống',
                'display_name' => 'Tiêu đề Modules',
                'type' => 'text'
            ],
            [
                'key' => 'modules_subtitle',
                'value' => 'Hệ thống của chúng tôi bao gồm nhiều phần khác nhau',
                'display_name' => 'Mô tả Modules',
                'type' => 'textarea'
            ],
            [
                'key' => 'testimonials_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Đánh giá',
                'type' => 'select'
            ],
            [
                'key' => 'testimonials_title',
                'value' => 'Khách hàng nói gì về chúng tôi',
                'display_name' => 'Tiêu đề Đánh giá',
                'type' => 'text'
            ],
            [
                'key' => 'testimonials_subtitle',
                'value' => 'Những đánh giá từ khách hàng của chúng tôi',
                'display_name' => 'Mô tả Đánh giá',
                'type' => 'textarea'
            ],
            [
                'key' => 'contact_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần Liên hệ',
                'type' => 'select'
            ],
            [
                'key' => 'contact_title',
                'value' => 'Liên hệ với chúng tôi',
                'display_name' => 'Tiêu đề Liên hệ',
                'type' => 'text'
            ],
            [
                'key' => 'contact_subtitle',
                'value' => 'Gửi thông tin liên hệ cho chúng tôi',
                'display_name' => 'Mô tả Liên hệ',
                'type' => 'textarea'
            ],
            [
                'key' => 'cta_enabled',
                'value' => 'true',
                'display_name' => 'Hiển thị phần CTA',
                'type' => 'select'
            ],
            [
                'key' => 'cta_title',
                'value' => 'Bắt đầu ngay hôm nay',
                'display_name' => 'Tiêu đề CTA',
                'type' => 'text'
            ],
            [
                'key' => 'cta_subtitle',
                'value' => 'Đăng ký tài khoản để trải nghiệm dịch vụ của chúng tôi',
                'display_name' => 'Mô tả CTA',
                'type' => 'textarea'
            ],
            [
                'key' => 'cta_button_text',
                'value' => 'Đăng ký ngay',
                'display_name' => 'Nút CTA',
                'type' => 'text'
            ],
            [
                'key' => 'cta_button_url',
                'value' => '/register',
                'display_name' => 'Liên kết nút CTA',
                'type' => 'text'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'landing_page',
                $setting['display_name'],
                $setting['type']
            );
        }
    }

    private function createUiSettings()
    {
        $settings = [
            // Login Page Settings
            [
                'key' => 'login_background_image',
                'value' => '/storage/ui/login-bg.jpg',
                'display_name' => 'Ảnh nền trang đăng nhập',
                'type' => 'image'
            ],
            [
                'key' => 'login_main_title',
                'value' => 'Chào mừng bạn trở lại',
                'display_name' => 'Tiêu đề chính trang đăng nhập',
                'type' => 'text'
            ],
            [
                'key' => 'login_subtitle',
                'value' => 'Đăng nhập để trải nghiệm dịch vụ của chúng tôi',
                'display_name' => 'Tiêu đề phụ trang đăng nhập',
                'type' => 'text'
            ],
            // About Page Settings
            [
                'key' => 'about_hero_title',
                'value' => 'Tận hưởng trải nghiệm Pickleball tuyệt vời',
                'display_name' => 'Tiêu đề chính trang Giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_hero_subtitle',
                'value' => 'Nền tảng đặt sân thông minh giúp bạn tìm kiếm, so sánh và đặt sân Pickleball một cách nhanh chóng, dễ dàng',
                'display_name' => 'Tiêu đề phụ trang Giới thiệu',
                'type' => 'textarea'
            ],
            [
                'key' => 'about_hero_button_text',
                'value' => 'Tìm sân ngay',
                'display_name' => 'Nút trang Giới thiệu',
                'type' => 'text'
            ],
            [
                'key' => 'about_hero_image',
                'value' => '/storage/about.jpg',
                'display_name' => 'Ảnh chính trang Giới thiệu',
                'type' => 'image'
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::setValue(
                $setting['key'],
                $setting['value'],
                'ui',
                $setting['display_name'],
                $setting['type']
            );
        }
    }
}