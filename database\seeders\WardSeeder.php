<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Truncate the table first
        DB::table('wards')->truncate();

        // Read and execute SQL file
        $sqlFile = database_path('../data/wards.sql');

        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);

            // Split SQL statements and execute each one
            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !str_starts_with($statement, '--') && !str_starts_with($statement, '/*')) {
                    try {
                        DB::unprepared($statement);
                    } catch (\Exception $e) {
                        // Skip invalid statements (like comments or empty lines)
                        continue;
                    }
                }
            }

            $this->command->info('Wards imported successfully from SQL file.');
        } else {
            // Fallback: Insert some sample data if SQL file is not found
            $wards = [
                ['id' => 1, 'ward_code' => '00070', 'name' => 'Phường Hoàn Kiếm', 'province_code' => '01'],
                ['id' => 2, 'ward_code' => '00073', 'name' => 'Phường Cửa Nam', 'province_code' => '01'],
                ['id' => 3, 'ward_code' => '00004', 'name' => 'Phường Ba Đình', 'province_code' => '01'],
                ['id' => 4, 'ward_code' => '00008', 'name' => 'Phường Ngọc Hà', 'province_code' => '01'],
                ['id' => 5, 'ward_code' => '00025', 'name' => 'Phường Giảng Võ', 'province_code' => '01'],
                // Add more sample wards here
            ];

            foreach ($wards as $ward) {
                DB::table('wards')->insert($ward + ['created_at' => now(), 'updated_at' => now()]);
            }

            $this->command->info('Wards seeded with fallback data.');
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
}
