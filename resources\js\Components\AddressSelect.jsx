import React, { useState, useEffect, useRef } from 'react';
import AddressService from '@/Services/AddressService';
import { Search, ChevronDown, X } from 'lucide-react';

/**
 * Remove Vietnamese diacritics for fuzzy search
 * @param {string} str 
 * @returns {string}
 */
const removeVietnameseDiacritics = (str) => {
    return str
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove combining diacritical marks
        .replace(/đ/g, 'd')
        .replace(/Đ/g, 'D')
        .toLowerCase();
};

/**
 * Fuzzy search function for Vietnamese text
 * @param {string} searchTerm 
 * @param {string} targetText 
 * @returns {boolean}
 */
const fuzzySearch = (searchTerm, targetText) => {
    if (!searchTerm || !targetText) return false;

    const normalizedSearch = removeVietnameseDiacritics(searchTerm.trim());
    const normalizedTarget = removeVietnameseDiacritics(targetText);

    // Direct include check (without diacritics)
    if (normalizedTarget.includes(normalizedSearch)) {
        return true;
    }

    // Word boundary check for partial matches
    const searchWords = normalizedSearch.split(/\s+/);
    return searchWords.every(word =>
        normalizedTarget.includes(word) ||
        normalizedTarget.split(/\s+/).some(targetWord =>
            targetWord.startsWith(word) || targetWord.includes(word)
        )
    );
};

/**
 * AddressSelect Component - 2 levels: Province and Ward with Fuzzy Search
 * 
 * @param {Object} props
 * @param {Function} props.onChange - Callback when address selection changes
 * @param {Object} props.defaultValues - Default selected values { provinceId, wardId }
 * @param {Boolean} props.required - Whether fields are required
 * @param {String} props.className - Additional class for select elements
 * @param {Object} props.labels - Custom labels { province, ward }
 */
export default function AddressSelect({
    onChange,
    defaultValues = {},
    required = false,
    className = '',
    labels = {
        province: 'Tỉnh/Thành phố',
        ward: 'Phường/Xã'
    }
}) {

    const [provinces, setProvinces] = useState([]);
    const [wards, setWards] = useState([]);
    const [filteredProvinces, setFilteredProvinces] = useState([]);
    const [filteredWards, setFilteredWards] = useState([]);

    const [selectedProvince, setSelectedProvince] = useState(null);
    const [selectedWard, setSelectedWard] = useState(null);

    const [provinceSearch, setProvinceSearch] = useState('');
    const [wardSearch, setWardSearch] = useState('');
    const [showProvinceDropdown, setShowProvinceDropdown] = useState(false);
    const [showWardDropdown, setShowWardDropdown] = useState(false);

    const [loadingProvinces, setLoadingProvinces] = useState(false);
    const [loadingWards, setLoadingWards] = useState(false);
    const [error, setError] = useState('');

    const provinceDropdownRef = useRef(null);
    const wardDropdownRef = useRef(null);
    const provinceInputRef = useRef(null);
    const wardInputRef = useRef(null);

    useEffect(() => {
        fetchProvinces();
    }, []);

    useEffect(() => {
        if (selectedProvince) {
            fetchWards(selectedProvince.province_code);
            setSelectedWard(null);
            setWardSearch('');
        } else {
            setWards([]);
            setFilteredWards([]);
        }
    }, [selectedProvince]);

    useEffect(() => {
        if (onChange) {
            onChange({
                provinceId: selectedProvince?.id || '',
                wardId: selectedWard?.id || '',
                provinceCode: selectedProvince?.province_code || '',
                wardCode: selectedWard?.ward_code || '',
                province: selectedProvince?.name || '',
                ward: selectedWard?.name || '',
                fullAddress: selectedWard && selectedProvince
                    ? `${selectedWard.name}, ${selectedProvince.name}`
                    : ''
            });
        }
    }, [selectedProvince, selectedWard]);

    useEffect(() => {
        if (defaultValues.provinceId && provinces.length > 0) {
            const province = provinces.find(p => p.id === Number(defaultValues.provinceId));
            if (province) {
                setSelectedProvince(province);
                setProvinceSearch(province.name);
            }
        }
    }, [defaultValues.provinceId, provinces]);

    useEffect(() => {
        if (defaultValues.wardId && wards.length > 0) {
            const ward = wards.find(w => w.id === Number(defaultValues.wardId));
            if (ward) {
                setSelectedWard(ward);
                setWardSearch(ward.name);
            }
        }
    }, [defaultValues.wardId, wards]);

    // Filter provinces with fuzzy search
    useEffect(() => {
        if (!provinceSearch.trim()) {
            setFilteredProvinces(provinces);
        } else {
            const filtered = provinces.filter(province =>
                fuzzySearch(provinceSearch, province.name) ||
                fuzzySearch(provinceSearch, province.short_name) ||
                fuzzySearch(provinceSearch, province.code)
            );
            setFilteredProvinces(filtered);
        }
    }, [provinceSearch, provinces]);

    // Filter wards with fuzzy search
    useEffect(() => {
        if (!wardSearch.trim()) {
            setFilteredWards(wards);
        } else {
            const filtered = wards.filter(ward =>
                fuzzySearch(wardSearch, ward.name)
            );
            setFilteredWards(filtered);
        }
    }, [wardSearch, wards]);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (provinceDropdownRef.current && !provinceDropdownRef.current.contains(event.target)) {
                setShowProvinceDropdown(false);
            }
            if (wardDropdownRef.current && !wardDropdownRef.current.contains(event.target)) {
                setShowWardDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const fetchProvinces = async () => {
        setLoadingProvinces(true);
        setError('');

        try {
            const response = await AddressService.getProvinces();
            if (response.success) {
                setProvinces(response.data || []);
                setFilteredProvinces(response.data || []);
            } else {
                setError(response.message || 'Failed to fetch provinces');
            }
        } catch (err) {
            setError('Error loading provinces. Please try again.');
            console.error(err);
        } finally {
            setLoadingProvinces(false);
        }
    };

    const fetchWards = async (provinceCode) => {
        setLoadingWards(true);
        setError('');

        try {
            const response = await AddressService.getWardsByProvince(provinceCode);
            if (response.success) {
                setWards(response.data || []);
                setFilteredWards(response.data || []);
            } else {
                setError(response.message || 'Failed to fetch wards');
            }
        } catch (err) {
            setError('Error loading wards. Please try again.');
            console.error(err);
        } finally {
            setLoadingWards(false);
        }
    };

    const handleProvinceSelect = (province) => {
        setSelectedProvince(province);
        setProvinceSearch(province.name);
        setShowProvinceDropdown(false);
    };

    const handleWardSelect = (ward) => {
        setSelectedWard(ward);
        setWardSearch(ward.name);
        setShowWardDropdown(false);
    };

    const clearProvince = () => {
        setSelectedProvince(null);
        setProvinceSearch('');
        setShowProvinceDropdown(false);
        provinceInputRef.current?.focus();
    };

    const clearWard = () => {
        setSelectedWard(null);
        setWardSearch('');
        setShowWardDropdown(false);
        wardInputRef.current?.focus();
    };

    const inputClasses = `w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-400 focus:border-primary-500 transition-colors duration-200 ${className}`;
    const dropdownClasses = "absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto";

    const highlightMatch = (text, searchTerm) => {
        if (!searchTerm.trim()) return text;

        const normalizedSearch = removeVietnameseDiacritics(searchTerm);
        const normalizedText = removeVietnameseDiacritics(text);

        // Find the matching part
        const index = normalizedText.indexOf(normalizedSearch);
        if (index === -1) return text;

        // Get the original text part that matches
        const beforeMatch = text.substring(0, index);
        const match = text.substring(index, index + searchTerm.length);
        const afterMatch = text.substring(index + searchTerm.length);

        return (
            <span>
                {beforeMatch}
                <span className="bg-yellow-200 font-medium">{match}</span>
                {afterMatch}
            </span>
        );
    };

    return (
        <div className="space-y-4">
            {error && (
                <div className="text-red-500 text-sm">{error}</div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Province Select with Search */}
                <div className="relative" ref={provinceDropdownRef}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {labels.province} {required && <span className="text-red-500">*</span>}
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Search className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                            ref={provinceInputRef}
                            type="text"
                            value={provinceSearch}
                            onChange={(e) => setProvinceSearch(e.target.value)}
                            onFocus={() => setShowProvinceDropdown(true)}
                            placeholder={loadingProvinces ? 'Đang tải...' : `Tìm kiếm ${labels.province.toLowerCase()}... (VD: "ha noi", "hcm")`}
                            className={`${inputClasses} pl-10 pr-10`}
                            disabled={loadingProvinces}
                            required={required}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center">
                            {selectedProvince && (
                                <button
                                    type="button"
                                    onClick={clearProvince}
                                    className="px-2 py-1 text-gray-400 hover:text-gray-600"
                                >
                                    <X className="h-4 w-4" />
                                </button>
                            )}
                            <div className="px-2 py-1">
                                <ChevronDown className="h-4 w-4 text-gray-400" />
                            </div>
                        </div>
                    </div>

                    {showProvinceDropdown && (
                        <div className={dropdownClasses}>
                            {filteredProvinces.length === 0 ? (
                                <div className="px-3 py-2 text-gray-500">
                                    {loadingProvinces ? 'Đang tải...' : 'Không tìm thấy kết quả'}
                                </div>
                            ) : (
                                filteredProvinces.map(province => (
                                    <button
                                        key={province.id}
                                        type="button"
                                        onClick={() => handleProvinceSelect(province)}
                                        className={`w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none ${selectedProvince?.id === province.id ? 'bg-primary-50 text-primary-700' : ''
                                            }`}
                                    >
                                        <div className="font-medium">
                                            {highlightMatch(province.name, provinceSearch)}
                                        </div>
                                        {province.short_name !== province.name && (
                                            <div className="text-sm text-gray-500">
                                                {highlightMatch(province.short_name, provinceSearch)}
                                            </div>
                                        )}
                                    </button>
                                ))
                            )}
                        </div>
                    )}
                </div>

                {/* Ward Select with Search */}
                <div className="relative" ref={wardDropdownRef}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {labels.ward} {required && <span className="text-red-500">*</span>}
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Search className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                            ref={wardInputRef}
                            type="text"
                            value={wardSearch}
                            onChange={(e) => setWardSearch(e.target.value)}
                            onFocus={() => setShowWardDropdown(true)}
                            placeholder={
                                !selectedProvince
                                    ? `Chọn ${labels.province.toLowerCase()} trước`
                                    : loadingWards
                                        ? 'Đang tải...'
                                        : `Tìm kiếm ${labels.ward.toLowerCase()}... (VD: "can giuoc", "phuong 1")`
                            }
                            className={`${inputClasses} pl-10 pr-10`}
                            disabled={!selectedProvince || loadingWards}
                            required={required}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center">
                            {selectedWard && (
                                <button
                                    type="button"
                                    onClick={clearWard}
                                    className="px-2 py-1 text-gray-400 hover:text-gray-600"
                                >
                                    <X className="h-4 w-4" />
                                </button>
                            )}
                            <div className="px-2 py-1">
                                <ChevronDown className="h-4 w-4 text-gray-400" />
                            </div>
                        </div>
                    </div>

                    {showWardDropdown && selectedProvince && (
                        <div className={dropdownClasses}>
                            {filteredWards.length === 0 ? (
                                <div className="px-3 py-2 text-gray-500">
                                    {loadingWards ? 'Đang tải...' : 'Không tìm thấy kết quả'}
                                </div>
                            ) : (
                                filteredWards.map(ward => (
                                    <button
                                        key={ward.id}
                                        type="button"
                                        onClick={() => handleWardSelect(ward)}
                                        className={`w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none ${selectedWard?.id === ward.id ? 'bg-primary-50 text-primary-700' : ''
                                            }`}
                                    >
                                        {highlightMatch(ward.name, wardSearch)}
                                    </button>
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Selected Address Display */}
            {selectedProvince && selectedWard && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                    <div className="text-sm font-medium text-gray-700">Địa chỉ đã chọn:</div>
                    <div className="text-gray-900">{selectedWard.name}, {selectedProvince.name}</div>
                </div>
            )}
        </div>
    );
} 