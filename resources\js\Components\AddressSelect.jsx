import React, { useState, useEffect } from 'react';
import AddressService from '@/Services/AddressService';

/**
 * AddressSelect Component - 2 levels: Province and Ward
 * 
 * @param {Object} props
 * @param {Function} props.onChange - Callback when address selection changes
 * @param {Object} props.defaultValues - Default selected values { provinceId, wardId }
 * @param {Boolean} props.required - Whether fields are required
 * @param {String} props.className - Additional class for select elements
 * @param {Object} props.labels - Custom labels { province, ward }
 */
export default function AddressSelect({
    onChange,
    defaultValues = {},
    required = false,
    className = '',
    labels = {
        province: 'Tỉnh/Thành phố',
        ward: 'Phường/Xã'
    }
}) {

    const [provinces, setProvinces] = useState([]);
    const [wards, setWards] = useState([]);

    const [selectedProvince, setSelectedProvince] = useState(defaultValues.provinceId || '');
    const [selectedWard, setSelectedWard] = useState(defaultValues.wardId || '');

    const [loadingProvinces, setLoadingProvinces] = useState(false);
    const [loadingWards, setLoadingWards] = useState(false);

    const [error, setError] = useState('');

    useEffect(() => {
        fetchProvinces();
    }, []);

    useEffect(() => {
        if (selectedProvince) {
            const selectedProvinceData = provinces.find(p => p.id === Number(selectedProvince));
            if (selectedProvinceData) {
                fetchWards(selectedProvinceData.province_code);
            }
            setSelectedWard('');
        } else {
            setWards([]);
        }
    }, [selectedProvince, provinces]);

    useEffect(() => {
        if (onChange) {
            const selectedProvinceData = provinces.find(p => p.id === Number(selectedProvince));
            const selectedWardData = wards.find(w => w.id === Number(selectedWard));

            onChange({
                provinceId: selectedProvince,
                wardId: selectedWard,
                provinceCode: selectedProvinceData?.province_code || '',
                wardCode: selectedWardData?.ward_code || '',
                province: selectedProvinceData?.name || '',
                ward: selectedWardData?.name || '',
                fullAddress: selectedWardData && selectedProvinceData
                    ? `${selectedWardData.name}, ${selectedProvinceData.name}`
                    : ''
            });
        }
    }, [selectedProvince, selectedWard, provinces, wards]);

    useEffect(() => {
        if (defaultValues.provinceId) {
            setSelectedProvince(defaultValues.provinceId);
        }
    }, [defaultValues.provinceId]);

    useEffect(() => {
        if (defaultValues.wardId && wards.length > 0) {
            setSelectedWard(defaultValues.wardId);
        }
    }, [defaultValues.wardId, wards]);

    const fetchProvinces = async () => {
        setLoadingProvinces(true);
        setError('');

        try {
            const response = await AddressService.getProvinces();
            if (response.success) {
                setProvinces(response.data || []);
            } else {
                setError(response.message || 'Failed to fetch provinces');
            }
        } catch (err) {
            setError('Error loading provinces. Please try again.');
            console.error(err);
        } finally {
            setLoadingProvinces(false);
        }
    };

    const fetchWards = async (provinceCode) => {
        setLoadingWards(true);
        setError('');

        try {
            const response = await AddressService.getWardsByProvince(provinceCode);
            if (response.success) {
                setWards(response.data || []);
            } else {
                setError(response.message || 'Failed to fetch wards');
            }
        } catch (err) {
            setError('Error loading wards. Please try again.');
            console.error(err);
        } finally {
            setLoadingWards(false);
        }
    };

    const handleProvinceChange = (e) => {
        const value = e.target.value;
        setSelectedProvince(value);
    };

    const handleWardChange = (e) => {
        const value = e.target.value;
        setSelectedWard(value);
    };

    const selectClasses = `w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${className}`;

    return (
        <div className="space-y-4">
            {error && (
                <div className="text-primary-500 text-sm">{error}</div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {labels.province} {required && <span className="text-primary-500">*</span>}
                    </label>
                    <select
                        value={selectedProvince}
                        onChange={handleProvinceChange}
                        className={selectClasses}
                        disabled={loadingProvinces}
                        required={required}
                    >
                        <option value="">{loadingProvinces ? 'Đang tải...' : `-- Chọn ${labels.province} --`}</option>
                        {provinces.map(province => (
                            <option key={province.id} value={province.id}>
                                {province.name}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {labels.ward} {required && <span className="text-primary-500">*</span>}
                    </label>
                    <select
                        value={selectedWard}
                        onChange={handleWardChange}
                        className={selectClasses}
                        disabled={!selectedProvince || loadingWards}
                        required={required}
                    >
                        <option value="">{loadingWards ? 'Đang tải...' : `-- Chọn ${labels.ward} --`}</option>
                        {wards.map(ward => (
                            <option key={ward.id} value={ward.id}>
                                {ward.name}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
        </div>
    );
} 