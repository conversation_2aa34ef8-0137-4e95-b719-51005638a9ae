import React from 'react';
import { __ } from '@/utils/lang';
import { ArrowUp, ArrowDown, ChevronLeft, ChevronRight, FileText, FileSpreadsheet, Eye, Edit, Trash2, Loader } from 'lucide-react';
import { router } from '@inertiajs/react';

export default function DataTable({
    data = [],
    columns = [],
    actions = [],
    onSort = () => { },
    sortField = null,
    sortDirection = 'asc',
    emptyStateMessage = null,
    primaryKey = 'id',
    viewRoute = null,
    editRoute = null,
    deleteCallback = null,
    confirmingDeletionId = null,
    cancelDeletion = () => { },
    highlightColor = 'primary',
    enableDefaultActions = true,
    title = null,
    icon: Icon = null,
    onExportPdf = null,
    onExportExcel = null,
    actionIcon = false
}) {
    const handleSortClick = (field) => {
        const direction = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
        onSort(field, direction);
    };

    const defaultEmptyStateMessage = __('common.no_results_found') || 'No results found';

    const defaultActions = [];

    if (enableDefaultActions) {
        if (viewRoute) {
            defaultActions.push({
                label: __('common.view') || 'View',
                icon: <Eye className="h-4 w-4" />,
                onClick: (item) => window.location.href = route(viewRoute, item[primaryKey])
            });
        }
        if (editRoute) {
            defaultActions.push({
                label: __('common.edit') || 'Edit',
                icon: <Edit className="h-4 w-4" />,
                onClick: (item) => window.location.href = route(editRoute, item[primaryKey])
            });
        }
        if (deleteCallback) {
            defaultActions.push({
                label: __('common.delete') || 'Delete',
                icon: <Trash2 className="h-4 w-4" />,
                onClick: (item) => deleteCallback(item[primaryKey])
            });
        }
    }

    const getActionsForItem = (item) => {
        if (typeof actions === 'function') {
            return [...defaultActions, ...actions(item)];
        }
        return [...defaultActions, ...actions];
    };

    const handlePaginationClick = (url) => {
        if (url) {
            router.get(url);
        }
    };

    const pagination = data.links || null;

    const getButtonStyle = (actionLabel) => {
        const label = actionLabel.toLowerCase();
        if (label === 'view' || label === 'xem') {
            return 'bg-blue-50 text-blue-700 hover:bg-blue-100';
        } else if (label === 'edit' || label === 'sửa') {
            return 'bg-yellow-50 text-yellow-700 hover:bg-yellow-100';
        } else if (label === 'delete' || label === 'xóa') {
            return 'bg-red-50 text-red-700 hover:bg-red-100';
        } else {
            return 'bg-gray-100 text-gray-700 hover:bg-gray-200';
        }
    };


    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" style={{ position: 'relative', zIndex: 1 }}>

            {(title || onExportPdf || onExportExcel) && (
                <div className="flex justify-between items-center px-5 py-4 border-b border-gray-200">
                    {title && (
                        <div className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                            {Icon && <Icon className="h-5 w-5 text-primary-600" />}
                            {title}
                        </div>
                    )}
                    <div className="flex gap-2">
                        {onExportPdf && (
                            <button
                                onClick={onExportPdf}
                                className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 rounded-md hover:bg-gray-200 text-gray-700"
                            >
                                <FileText className="h-4 w-4" />
                                <span>PDF</span>
                            </button>
                        )}
                        {onExportExcel && (
                            <button
                                onClick={onExportExcel}
                                className="flex items-center gap-1 px-3 py-2 text-sm bg-gray-100 rounded-md hover:bg-gray-200 text-gray-700"
                            >
                                <FileSpreadsheet className="h-4 w-4" />
                                <span>Excel</span>
                            </button>
                        )}
                    </div>
                </div>
            )}

            {/* Table */}
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead>
                        <tr>
                            {columns.map((column, index) => (
                                <th
                                    key={index}
                                    className={`px-4 py-3.5 text-left text-sm font-semibold text-gray-600 ${column.className || ''}`}
                                    onClick={() => column.sortable && handleSortClick(column.field)}
                                    style={{ cursor: column.sortable ? 'pointer' : 'default' }}
                                >
                                    {column.sortable ? (
                                        <div className="flex items-center">
                                            {column.label}
                                            {sortField === column.field && (
                                                sortDirection === 'asc' ?
                                                    <ArrowUp size={14} className="ml-1" /> :
                                                    <ArrowDown size={14} className="ml-1" />
                                            )}
                                        </div>
                                    ) : (
                                        column.label
                                    )}
                                </th>
                            ))}
                            {(defaultActions.length > 0 || (typeof actions === 'function' || actions.length > 0)) && (
                                <th className="px-4 py-3.5 text-right text-sm font-semibold text-gray-600 w-48">
                                    {__('common.actions') || 'Thao tác'}
                                </th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {data.length > 0 || (data.data && data.data.length > 0) ? (
                            (data.data || data).map((item) => (
                                <tr key={item[primaryKey]} className="hover:bg-gray-50 border-b border-gray-200 last:border-0">
                                    {columns.map((column, colIndex) => (
                                        <td key={colIndex} className={`px-4 py-4 ${column.tdClassName || ''}`}>
                                            {column.render ? column.render(item) : item[column.field]}
                                        </td>
                                    ))}
                                    {(defaultActions.length > 0 || (typeof actions === 'function' || actions.length > 0)) && (
                                        <td className="px-4 py-4">
                                            {confirmingDeletionId === item[primaryKey] ? (
                                                <div className="flex justify-end gap-2">
                                                    <button
                                                        className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md bg-red-50 text-red-700 hover:bg-red-100"
                                                        onClick={() => deleteCallback(item[primaryKey])}
                                                    >
                                                        {__('common.confirm') || 'Xác nhận'}
                                                    </button>
                                                    <button
                                                        className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
                                                        onClick={cancelDeletion}
                                                    >
                                                        {__('common.cancel') || 'Hủy'}
                                                    </button>
                                                </div>
                                            ) : (
                                                <div className="flex justify-end gap-2">
                                                    {getActionsForItem(item).map((action, idx) => {
                                                        // Check if this action should be visible
                                                        const isVisible = action.isVisible
                                                            ? (typeof action.isVisible === 'function' ? action.isVisible(item) : action.isVisible)
                                                            : true; // Default to visible if not specified

                                                        if (!isVisible) {
                                                            return null;
                                                        }

                                                        // Check if action is in loading state
                                                        const isActionLoading = action.isLoading && typeof action.isLoading === 'function' && action.isLoading(item);

                                                        return <button
                                                            key={idx}
                                                            className={`flex items-center gap-1 ${actionIcon ? 'h-8 w-8 justify-center' : 'px-3 py-1.5'} text-xs font-medium rounded-md ${getButtonStyle(action.label)} ${action.className || ''}`}
                                                            onClick={() => action.onClick(item)}
                                                            title={action.label}
                                                            disabled={isActionLoading}
                                                        >
                                                            {isActionLoading ? (
                                                                <Loader className="h-4 w-4 animate-spin" />
                                                            ) : actionIcon ? (
                                                                action.icon || (
                                                                    action.label.toLowerCase() === 'view' || action.label.toLowerCase() === 'xem' ?
                                                                        <Eye className="h-4 w-4" /> :
                                                                        action.label.toLowerCase() === 'edit' || action.label.toLowerCase() === 'sửa' ?
                                                                            <Edit className="h-4 w-4" /> :
                                                                            action.label.toLowerCase() === 'delete' || action.label.toLowerCase() === 'xóa' ?
                                                                                <Trash2 className="h-4 w-4" /> :
                                                                                null
                                                                )
                                                            ) : (
                                                                <>{action.label}</>
                                                            )}
                                                        </button>
                                                    })}
                                                </div>
                                            )}
                                        </td>
                                    )}
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td
                                    colSpan={columns.length + ((defaultActions.length > 0 || (typeof actions === 'function' || actions.length > 0)) ? 1 : 0)}
                                    className="px-4 py-8 text-center text-gray-500"
                                >
                                    {emptyStateMessage || defaultEmptyStateMessage}
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {pagination && pagination.length > 3 && (
                <div className="px-5 py-4 border-t border-gray-200 flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                        Hiển thị {data.from || 0} đến {data.to || 0} trong tổng số {data.total || 0} kết quả
                    </div>

                    <div className="flex gap-1">
                        {pagination.map((link, index) => {
                            if (index === 0 && link.url === null) return null;
                            if (index === pagination.length - 1 && link.url === null) return null;

                            const isFirst = index === 0;
                            const isLast = index === pagination.length - 1;

                            return (
                                <button
                                    key={index}
                                    className={`w-8 h-8 flex items-center justify-center rounded-md text-sm
                                        ${link.active
                                            ? `bg-${highlightColor}-600 text-white`
                                            : 'hover:bg-gray-100 text-gray-700 border border-transparent hover:border-gray-300'}
                                        ${link.url === null ? 'opacity-50 cursor-not-allowed' : ''}
                                        ${(isFirst || isLast) ? 'px-2' : ''}
                                    `}
                                    onClick={() => handlePaginationClick(link.url)}
                                    disabled={link.url === null}
                                >
                                    {isFirst ? (
                                        <ChevronLeft className="h-4 w-4" />
                                    ) : isLast ? (
                                        <ChevronRight className="h-4 w-4" />
                                    ) : (
                                        link.label
                                    )}
                                </button>
                            );
                        })}
                    </div>
                </div>
            )}
        </div>
    );
}
