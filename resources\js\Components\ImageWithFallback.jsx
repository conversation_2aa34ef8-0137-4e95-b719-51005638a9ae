import React, { useState } from 'react';

export default function ImageWithFallback({
  src,
  alt,
  fallbackText,
  className = '',
  imageClassName = '',
  fallbackClassName = '',
  width = 'w-10',
  height = 'h-10',
  rounded = 'rounded-md',
  bgColor = 'bg-gray-200',
  textColor = 'text-gray-500',
  textSize = 'text-sm',
}) {
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    setHasError(true);
  };

  const containerClasses = `${width} ${height} ${rounded} ${className}`;

  if (hasError || !src) {
    return (
      <div className={`${containerClasses} ${bgColor} flex items-center justify-center ${fallbackClassName}`}>
        <span className={`${textSize} font-medium ${textColor}`}>
            {!src && (
              <img
                src={'/storage/marketlogo.png'}
                alt={alt}
                className={`${width} ${height} ${rounded} object-cover ${imageClassName}`}
              />
            )}
        </span>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      <img
        src={src}
        alt={alt}
        className={`${width} ${height} ${rounded} object-cover ${imageClassName}`}
        onError={handleError}
      />
    </div>
  );
}
