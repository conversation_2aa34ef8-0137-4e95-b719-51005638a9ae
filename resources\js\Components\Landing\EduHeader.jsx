import React, { useState, useEffect } from 'react';
import { Link, usePage, router } from '@inertiajs/react';
import {
    Search,
    User,
    Menu,
    X
} from 'lucide-react';
import { Input } from '@/Components/ui/input';
import { Button } from '@/Components/ui/button';
import { __ } from '@/utils/lang';

export default function EduHeader() {
    const { auth } = usePage().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [isProfileOpen, setIsProfileOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        const handleClickOutside = (event) => {
            const profileMenu = document.getElementById('profile-menu');
            const profileButton = document.getElementById('profile-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuButton = document.getElementById('mobile-menu-button');

            if (profileMenu && profileButton &&
                !profileMenu.contains(event.target) &&
                !profileButton.contains(event.target)) {
                setIsProfileOpen(false);
            }

            if (mobileMenu && mobileMenuButton &&
                !mobileMenu.contains(event.target) &&
                !mobileMenuButton.contains(event.target)) {
                setMobileMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const getUserInitials = () => {
        if (!auth.user || !auth.user.name) return '?';

        const nameParts = auth.user.name.split(' ');
        if (nameParts.length >= 2) {
            return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
        }
        return nameParts[0][0].toUpperCase();
    };

    const toggleProfileMenu = () => {
        setIsProfileOpen(!isProfileOpen);
    };

    const toggleMobileMenu = () => {
        setMobileMenuOpen(!mobileMenuOpen);
    };

    const handleLinkClick = (e, href, method = 'get', data = {}) => {
        e.preventDefault();
        setIsProfileOpen(false);
        router.visit(href, { method, data });
    };

    const handleLogout = (e) => {
        e.preventDefault();
        router.post(route('logout'));
    };

    const handleSearchInputChange = (e) => {
        setSearchTerm(e.target.value);
    };

    const handleSearch = () => {
        if (searchTerm.trim()) {
            router.get(`/edu/search?q=${encodeURIComponent(searchTerm.trim())}`);
        }
    };

    const handleSearchKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const renderAccountSection = () => {
        if (auth.user) {
            return (
                <div className="relative">
                    <button
                        id="profile-button"
                        onClick={toggleProfileMenu}
                        className="flex items-center gap-2"
                    >
                        <div className="w-8 h-8 rounded-full bg-[#ee0033] text-white flex items-center justify-center font-medium shadow-md hover:bg-[#cc0028] transition-all">
                            {getUserInitials()}
                        </div>
                        <span className="hidden md:inline text-sm font-medium">{auth.user.name}</span>
                    </button>

                    {isProfileOpen && (
                        <div
                            id="profile-menu"
                            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
                        >
                            <div className="px-4 py-2 border-b border-gray-200">
                                <p className="text-sm font-medium text-gray-900">{auth.user.name}</p>
                                <p className="text-xs text-gray-500 truncate">{auth.user.email}</p>
                            </div>

                            <a
                                href="/user/profile?tab=profile"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                onClick={(e) => handleLinkClick(e, '/user/profile?tab=profile')}
                            >
                                {__('cart.profile')}
                            </a>
                            <a
                                href="/edu/my-courses"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                onClick={(e) => handleLinkClick(e, '/edu/my-courses')}
                            >
                                Khóa học của tôi
                            </a>
                            <a
                                href="#"
                                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 border-t border-gray-200 cursor-pointer"
                                onClick={handleLogout}
                            >
                                {__('cart.logout')}
                            </a>
                        </div>
                    )}
                </div>
            );
        } else {
            return (
                <div className="flex items-center space-x-4">
                    <Button variant="outline" className="border-[#ee0033] text-[#ee0033] hover:bg-[#ee0033] hover:text-white">
                        Đăng nhập
                    </Button>
                    <Button className="bg-[#ee0033] hover:bg-[#cc0028]">
                        Đăng ký
                    </Button>
                </div>
            );
        }
    };

    return (
        <header className="bg-white shadow-[0_2px_10px_rgba(0,0,0,0.1)] sticky top-0 z-50">
            <div className="container mx-auto flex justify-between items-center px-4 lg:px-12 py-4">
                <Link href="/edu" className="flex items-center">
                    <img
                        src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgPGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDUiIHN0cm9rZT0iIzAzNzg2QyIgc3Ryb2tlLXdpZHRoPSI2IiBmaWxsPSJ0cmFuc3BhcmVudCIgLz4KICA8cmVjdCB4PSIyNSIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0U1QjYzRCIgcng9IjUiIHJ5PSI1IiAvPgogIDxjaXJjbGUgY3g9IjM1IiBjeT0iNTAiIHI9IjgiIGZpbGw9IiMwNUE0OTMiIC8+CiAgPGNpcmNsZSBjeD0iNjUiIGN5PSI1MCIgcj0iOCIgZmlsbD0iIzA1QTQ5MyIgLz4KPC9zdmc+"
                        alt="PIBA Academy Logo"
                        className="h-10 mr-2"
                    />
                    <div className="text-2xl font-bold text-[#ee0033]">
                        PIBA<span className="text-secondary"> Academy</span>
                    </div>
                </Link>

                {/* Desktop Navigation */}
                <nav className="hidden lg:flex space-x-8">
                    <Link href="/edu" className="text-dark-gray hover:text-[#ee0033] font-medium">Trang chủ</Link>
                    <Link href="/edu/courses" className="text-dark-gray hover:text-[#ee0033] font-medium">Khóa học</Link>
                    {/* <Link href="/edu/lecturers" className="text-dark-gray hover:text-[#ee0033] font-medium">Giảng Viên</Link> */}
                    <Link href="/edu/materials" className="text-dark-gray hover:text-[#ee0033] font-medium">Học liệu</Link>
                    <Link href="/edu/contact" className="text-dark-gray hover:text-[#ee0033] font-medium">Liên hệ</Link>
                </nav>

                {/* Desktop Account */}
                <div className="hidden lg:block">
                    {renderAccountSection()}
                </div>

                {/* Mobile menu button */}
                <button
                    id="mobile-menu-button"
                    className="lg:hidden p-2"
                    onClick={toggleMobileMenu}
                >
                    {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </button>
            </div>

            {/* Mobile menu */}
            {mobileMenuOpen && (
                <div id="mobile-menu" className="lg:hidden bg-white border-t border-border-color">
                    <nav className="flex flex-col p-4 space-y-4">
                        <Link href="/edu" className="text-dark-gray hover:text-[#ee0033] font-medium">Trang chủ</Link>
                        <Link href="/edu/courses" className="text-dark-gray hover:text-[#ee0033] font-medium">Khóa học</Link>
                        {/* <Link href="/edu/lecturers" className="text-dark-gray hover:text-[#ee0033] font-medium">Giảng Viên</Link> */}
                        <Link href="/edu/materials" className="text-dark-gray hover:text-[#ee0033] font-medium">Học liệu</Link>
                        <Link href="/edu/contact" className="text-dark-gray hover:text-[#ee0033] font-medium">Liên hệ</Link>

                        <div className="pt-4 border-t border-border-color">
                            {auth.user ? (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-[#ee0033] text-white flex items-center justify-center font-medium">
                                            {getUserInitials()}
                                        </div>
                                        <span className="font-medium">{auth.user.name}</span>
                                    </div>
                                    <Link href="/edu/my-courses" className="block text-dark-gray hover:text-[#ee0033]">
                                        Khóa học của tôi
                                    </Link>
                                    <Link href="/user/profile?tab=profile" className="block text-dark-gray hover:text-[#ee0033]">
                                        Tài khoản của tôi
                                    </Link>
                                    <button
                                        onClick={handleLogout}
                                        className="text-[#ee0033] hover:text-[#cc0028] font-medium"
                                    >
                                        Đăng xuất
                                    </button>
                                </div>
                            ) : (
                                <div className="flex flex-col space-y-2">
                                    <Button variant="outline" className="border-[#ee0033] text-[#ee0033] hover:bg-[#ee0033] hover:text-white">
                                        Đăng nhập
                                    </Button>
                                    <Button className="bg-[#ee0033] hover:bg-[#cc0028]">
                                        Đăng ký
                                    </Button>
                                </div>
                            )}
                        </div>
                    </nav>
                </div>
            )}
        </header>
    );
}
