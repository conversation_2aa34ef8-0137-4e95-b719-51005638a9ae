import React, { useState, useEffect } from 'react';
import { Link, usePage, router } from '@inertiajs/react';
import { Search } from 'lucide-react';

export default function Header() {
    const { auth, siteSettings } = usePage().props;
    const siteName = siteSettings?.site_name || 'PIBA';
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const [isProfileOpen, setIsProfileOpen] = useState(false);
    const [isPageLoading, setIsPageLoading] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 50) {
                setIsScrolled(true);
            } else {
                setIsScrolled(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            const profileMenu = document.getElementById('profile-menu');
            const profileButton = document.getElementById('profile-button');

            if (profileMenu && profileButton &&
                !profileMenu.contains(event.target) &&
                !profileButton.contains(event.target)) {
                setIsProfileOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleLinkClick = (e, href, method = 'get', data = {}) => {
        e.preventDefault();
        setIsPageLoading(true);
        setIsProfileOpen(false);
        router.visit(href, { method, data });
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const toggleProfileMenu = () => {
        setIsProfileOpen(!isProfileOpen);
    };

    const handleNavLinkClick = (e) => {
        if (e.target.getAttribute('href').startsWith('#')) {
            e.preventDefault();

            const targetId = e.target.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                const header = document.getElementById('header');
                const headerHeight = header ? header.offsetHeight : 0;

                const targetPosition = targetElement.getBoundingClientRect().top;
                const offsetPosition = targetPosition + window.pageYOffset - headerHeight;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });

                if (isMenuOpen) {
                    setIsMenuOpen(false);
                }
            }
        }
    };

    const getUserInitials = () => {
        if (!auth.user || !auth.user.name) return '?';

        const nameParts = auth.user.name.split(' ');
        if (nameParts.length >= 2) {
            return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
        }
        return nameParts[0][0].toUpperCase();
    };

    const renderAvatar = () => {
        return (
            <div className="relative">
                <button
                    id="profile-button"
                    onClick={toggleProfileMenu}
                    className="relative flex items-center"
                >
                    <div className="w-10 h-10 rounded-full bg-[#ee0033] text-white flex items-center justify-center font-medium shadow-md hover:bg-[#cc0028] transition-all">
                        {getUserInitials()}
                    </div>
                </button>

                {isProfileOpen && (
                    <div
                        id="profile-menu"
                        className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
                    >
                        <div className="px-4 py-2 border-b border-gray-200">
                            <p className="text-sm font-medium text-gray-900">{auth.user.name}</p>
                            <p className="text-xs text-gray-500 truncate">{auth.user.email}</p>
                        </div>

                        <a
                            href="/user/profile?tab=profile"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                            onClick={(e) => handleLinkClick(e, '/user/profile?tab=profile')}
                        >
                            Hồ sơ
                        </a>
                        <a
                            href="/user/profile?tab=booking"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                            onClick={(e) => handleLinkClick(e, '/user/profile?tab=booking')}
                        >
                            Đặt sân của tôi
                        </a>
                        <a
                            href="/customer/booking/verify"
                            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                            onClick={(e) => handleLinkClick(e, '/customer/booking/verify')}
                        >
                            Tra cứu đặt sân
                        </a>
                        {auth.user && auth.user.roles &&
                            (auth.user.roles.includes('super-admin') ||
                                auth.user.roles.includes('admin') ||
                                auth.user.roles.includes('manager') ||
                                auth.user.roles.includes('staff')) && (
                                <a
                                    href="/dashboard"
                                    className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                    onClick={(e) => handleLinkClick(e, '/dashboard')}
                                >
                                    Dashboard
                                </a>
                            )}
                        <a
                            href="#"
                            className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 border-t border-gray-200 cursor-pointer"
                            onClick={handleLogout}
                        >
                            Đăng xuất
                        </a>
                    </div>
                )}
            </div>
        );
    };

    const handleLogout = (e) => {
        e.preventDefault();
        router.post(route('logout'));
    };

    // Theo dõi sự kiện chuyển trang
    useEffect(() => {
        const handleStart = () => {
            setIsPageLoading(true);
        };

        const handleFinish = () => {
            setTimeout(() => {
                setIsPageLoading(false);
            }, 150);
        };

        const unsubscribeStart = router.on('start', handleStart);
        const unsubscribeFinish = router.on('finish', handleFinish);

        return () => {
            unsubscribeStart();
            unsubscribeFinish();
        };
    }, []);

    return (
        <header
            id="header"
            className={`fixed w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md py-3' : 'bg-transparent py-5'
                }`}
        >
            <div className="container mx-auto ">
                {/* Desktop layout with 3 sections */}
                <div className="hidden w-full lg:flex lg:justify-between lg:items-center">
                    {/* Logo section - Left */}
                    <div className="w-1/3 ">
                        <a href="#" className={`logo flex items-center font-bold text-xl ${isScrolled ? 'text-[#ee0033]' : 'text-white'}`}>
                            <img
                                src={siteSettings?.site_logo || '/images/logo.png'}
                                alt="Logo"
                                className="mr-2 w-13 h-12"
                            />

                            {siteSettings?.show_logo_text === 'on' && (
                                <div
                                    className="font-bold text-lg md:text-xl font-montserrat"
                                    style={{ color: isScrolled ? (siteSettings?.logo_text_color || '#333333') : 'white' }}
                                >
                                    {siteSettings?.logo_text || siteName}
                                </div>
                            )}
                        </a>
                    </div>

                    {/* Navigation links - Center */}
                    <div className="w-full flex items-center justify-center space-x-8">
                        <a href="#booking" onClick={handleNavLinkClick} className={`hover:text-[#ee0033] transition-colors ${isScrolled ? 'text-gray-800' : 'text-white'}`}>
                            Booking
                        </a>
                        <a href="#marketplace" onClick={handleNavLinkClick} className={`hover:text-[#ee0033] transition-colors ${isScrolled ? 'text-gray-800' : 'text-white'}`}>
                            Marketplace
                        </a>
                        <a href="#education" onClick={handleNavLinkClick} className={`hover:text-[#ee0033] transition-colors ${isScrolled ? 'text-gray-800' : 'text-white'}`}>
                            Education
                        </a>
                        <a href="#social" onClick={handleNavLinkClick} className={`hover:text-[#ee0033] transition-colors ${isScrolled ? 'text-gray-800' : 'text-white'}`}>
                            Social
                        </a>
                        <a href="#affiliate" onClick={handleNavLinkClick} className={`hover:text-[#ee0033] transition-colors ${isScrolled ? 'text-gray-800' : 'text-white'}`}>
                            Affiliate
                        </a>
                    </div>

                    {/* Action buttons - Right */}
                    <div className="w-1/3 flex items-end justify-end">
                        <div className="relative mr-2">
                            <a
                                href="/search"
                                className="flex justify-center items-center px-5 py-2 bg-white text-[#ee0033] font-bold rounded-md shadow-lg border-2 border-[#ee0033] hover:bg-[#ee0033] hover:text-white transition-all duration-300"
                                onClick={(e) => handleLinkClick(e, '/search')}
                            >
                                <span className="relative">Đặt sân</span>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1 -mr-1 transition-transform duration-300 transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </a>
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#ee0033] rounded-full animate-ping"></div>
                        </div>

                        {auth.user && (
                            renderAvatar()
                        )}
                    </div>
                </div>

                {/* Mobile layout */}
                <div className="flex justify-between items-center lg:hidden">
                    {/* Logo */}
                    <a href="#" className={`logo flex items-center font-bold text-xl ${isScrolled ? 'text-[#ee0033]' : 'text-white'}`}>
                        <img
                            src={siteSettings?.site_logo || '/images/logo.png'}
                            alt="Logo"
                            className="mr-2 w-10 h-10"
                        />
                        {siteSettings?.show_logo_text === 'on' && (
                            <div
                                className="font-bold text-lg font-montserrat"
                                style={{ color: isScrolled ? (siteSettings?.logo_text_color || '#333333') : 'white' }}
                            >
                                {siteSettings?.logo_text || siteName}
                            </div>
                        )}
                    </a>

                    {/* Mobile action buttons and hamburger */}
                    <div className="flex items-center gap-4">
                        {/* Mobile action buttons */}
                        <div className="flex items-center gap-2">
                            {/* Book court button for mobile */}
                            <a
                                href="/search"
                                className="flex items-center px-4 py-2 bg-white text-[#ee0033] font-bold rounded-md shadow-md border border-[#ee0033] hover:bg-[#ee0033] hover:text-white transition-all duration-300"
                                onClick={(e) => handleLinkClick(e, '/search')}
                            >
                                <span>Đặt sân</span>
                            </a>


                        </div>

                        <div className={`hamburger cursor-pointer ${isScrolled ? 'text-[#ee0033]' : 'text-white'}`}
                            onClick={toggleMenu}
                        >
                            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
                        </div>
                    </div>
                </div>

                {/* Mobile menu dropdown */}
                {isMenuOpen && (
                    <div className="block lg:hidden absolute top-full left-0 w-full bg-white shadow-md py-4 px-6 space-y-4">
                        {auth.user && (
                            <>
                                <div className="px-4 py-2 border-b border-gray-200 flex items-center gap-2">
                                    <div className="w-10 h-10 rounded-full bg-[#ee0033] text-white flex items-center justify-center font-medium shadow-md hover:bg-[#cc0028] transition-all">
                                        {getUserInitials()}
                                    </div>
                                    <div className='flex flex-col'>
                                        <p className="text-sm font-medium text-gray-900">{auth.user.name}</p>
                                        <p className="text-xs text-gray-500 truncate">{auth.user.email}</p>
                                    </div>
                                </div>
                                <a href="/search" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Đặt sân
                                </a>
                                <a href="/user/profile?tab=profile" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Hồ sơ
                                </a>
                                <a href="/user/profile?tab=booking" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Đặt sân của tôi
                                </a>
                                <a href="/customer/booking/verify" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Tra cứu đặt sân
                                </a>
                                {auth.user && auth.user.roles &&
                                    (auth.user.roles.includes('super-admin') ||
                                        auth.user.roles.includes('admin') ||
                                        auth.user.roles.includes('manager') ||
                                        auth.user.roles.includes('staff')) && (
                                        <a href="/dashboard" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                            Dashboard
                                        </a>
                                    )}
                                <a href="/user/profile?tab=favorites" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Sân yêu thích
                                </a></>
                        )}
                        {!auth.user && (
                            <div className='flex items-center gap-2 border-b border-gray-200 pb-4'>
                                <a href="/login" className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Đăng nhập
                                </a>
                                <a href="/register" className="block hover:text-[#ee0033] transition-colors text-gray-800">
                                    Đăng ký
                                </a>
                            </div>
                        )}
                        <a href="#about" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                            Giới thiệu
                        </a>
                        <a href="#modules" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                            Modules
                        </a>
                        <a href="#testimonials" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                            Đánh giá
                        </a>
                        <a href="#contact" onClick={handleNavLinkClick} className="block hover:text-[#ee0033] transition-colors text-gray-800">
                            Liên hệ
                        </a>
                        {auth.user && (
                            <a
                                href="#"
                                className="block hover:text-[#ee0033] transition-colors text-primary"
                                onClick={handleLogout}
                            >
                                Đăng xuất
                            </a>
                        )}

                    </div>
                )}
            </div>
        </header>
    );
} 