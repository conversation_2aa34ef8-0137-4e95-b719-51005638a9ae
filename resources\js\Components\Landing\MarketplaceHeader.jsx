import React, { useState, useEffect } from 'react';
import { Link, usePage, router } from '@inertiajs/react';
import {
    Search,
    Heart,
    ShoppingCart,
    User,
    ChevronDown,
    Package,
    Menu,
    X
} from 'lucide-react';
import { Input } from '@/Components/ui/input';
import { Badge } from '@/Components/ui/badge';
import { __ } from '@/utils/lang';

export default function MarketplaceHeader({ topCategories = [], moreCategories = [] }) {
    const { auth } = usePage().props;
    const [openDropdown, setOpenDropdown] = useState(null);
    const [cartCount, setCartCount] = useState(0);
    const [wishlistCount, setWishlistCount] = useState(0);
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [isProfileOpen, setIsProfileOpen] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        const updateCartCount = async () => {
            try {
                const response = await fetch('/marketplace/cart/count', {
                    credentials: 'same-origin'
                });
                const data = await response.json();
                if (data.count !== undefined) {
                    setCartCount(data.count);
                    setIsLoggedIn(true);
                } else {
                    const localCart = JSON.parse(localStorage.getItem('cart') || '[]');
                    setCartCount(localCart.length);
                    setIsLoggedIn(false);
                }
            } catch (error) {
                const localCart = JSON.parse(localStorage.getItem('cart') || '[]');
                setCartCount(localCart.length);
                setIsLoggedIn(false);
            }
        };

        const updateWishlistCount = () => {
            try {
                const localWishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
                setWishlistCount(localWishlist.length);
            } catch (error) {
                setWishlistCount(0);
            }
        };

        updateCartCount();
        updateWishlistCount();

        const handleCartUpdate = () => {
            updateCartCount();
        };

        const handleWishlistUpdate = () => {
            updateWishlistCount();
        };

        window.addEventListener('cartUpdated', handleCartUpdate);
        window.addEventListener('wishlistUpdated', handleWishlistUpdate);

        return () => {
            window.removeEventListener('cartUpdated', handleCartUpdate);
            window.removeEventListener('wishlistUpdated', handleWishlistUpdate);
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            const profileMenu = document.getElementById('profile-menu');
            const profileButton = document.getElementById('profile-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuButton = document.getElementById('mobile-menu-button');

            if (profileMenu && profileButton &&
                !profileMenu.contains(event.target) &&
                !profileButton.contains(event.target)) {
                setIsProfileOpen(false);
            }

            if (mobileMenu && mobileMenuButton &&
                !mobileMenu.contains(event.target) &&
                !mobileMenuButton.contains(event.target)) {
                setIsMobileMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const getUserInitials = () => {
        if (!auth.user || !auth.user.name) return '?';

        const nameParts = auth.user.name.split(' ');
        if (nameParts.length >= 2) {
            return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
        }
        return nameParts[0][0].toUpperCase();
    };

    const toggleProfileMenu = () => {
        setIsProfileOpen(!isProfileOpen);
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const handleLinkClick = (e, href, method = 'get', data = {}) => {
        e.preventDefault();
        setIsProfileOpen(false);
        router.visit(href, { method, data });
    };

    const handleLogout = (e) => {
        e.preventDefault();
        router.post(route('logout'));
    };

    const handleSearchInputChange = (e) => {
        setSearchTerm(e.target.value);
    };

    const handleSearch = () => {
        if (searchTerm.trim()) {
            router.get(`/marketplace/search?q=${encodeURIComponent(searchTerm.trim())}`);
        }
    };

    const handleSearchKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const renderAccountSection = () => {
        if (auth.user) {
            return (
                <div className="relative">
                    <button
                        id="profile-button"
                        onClick={toggleProfileMenu}
                        className="flex flex-col items-center text-gray-700 hover:text-primary"
                    >
                        <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-[#ee0033] text-white flex items-center justify-center font-medium shadow-md hover:bg-[#cc0028] transition-all text-xs sm:text-sm">
                            {getUserInitials()}
                        </div>
                        <span className="text-[10px] sm:text-xs mt-1 hidden sm:block">{__('cart.account')}</span>
                    </button>

                    {isProfileOpen && (
                        <div
                            id="profile-menu"
                            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
                        >
                            <div className="px-4 py-2 border-b border-gray-200">
                                <p className="text-sm font-medium text-gray-900">{auth.user.name}</p>
                                <p className="text-xs text-gray-500 truncate">{auth.user.email}</p>
                            </div>

                            <a
                                href="/user/profile?tab=profile"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                onClick={(e) => handleLinkClick(e, '/user/profile?tab=profile')}
                            >
                                {__('cart.profile')}
                            </a>
                            <a
                                href="/user/profile?tab=booking"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                onClick={(e) => handleLinkClick(e, '/user/profile?tab=booking')}
                            >
                                {__('cart.my_bookings')}
                            </a>
                            <a
                                href="/customer/booking/verify"
                                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                onClick={(e) => handleLinkClick(e, '/customer/booking/verify')}
                            >
                                {__('cart.booking_lookup')}
                            </a>
                            <a
                                href="#"
                                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 border-t border-gray-200 cursor-pointer"
                                onClick={handleLogout}
                            >
                                {__('cart.logout')}
                            </a>
                        </div>
                    )}
                </div>
            );
        } else {
            return (
                <Link href="/login" className="flex flex-col items-center text-gray-700 hover:text-primary">
                    <User className="h-5 w-5 sm:h-6 sm:w-6" />
                    <span className="text-[10px] sm:text-xs hidden sm:block">{__('cart.account')}</span>
                </Link>
            );
        }
    };

    const sortedTopCategories = Array.isArray(topCategories)
        ? [...topCategories].sort((a, b) => (a.order || 0) - (b.order || 0))
        : [];

    const sortedMoreCategories = Array.isArray(moreCategories)
        ? [...moreCategories].sort((a, b) => (a.order || 0) - (b.order || 0))
        : [];

    return (
        <header className="bg-white shadow-sm sticky top-0 z-50">
            <div className="mx-auto px-4 sm:px-6 lg:px-8">
                {/* Top Header */}
                <div className="flex justify-between items-center py-2 sm:py-4">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Link href="/marketplace" className="flex items-center">
                            <img
                                src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgPGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDUiIHN0cm9rZT0iIzAzNzg2QyIgc3Ryb2tlLXdpZHRoPSI2IiBmaWxsPSIjZmZmZmZmIiAvPgogIDxyZWN0IHg9IjI1IiB5PSIzMCIgd2lkdGg9IjUwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRTVCNjNEIiByeD0iNSIgcnk9IjUiIC8+CiAgPGNpcmNsZSBjeD0iMzUiIGN5PSI1MCIgcj0iOCIgZmlsbD0iIzA1QTQ5MyIgLz4KICA8Y2lyY2xlIGN4PSI2NSIgY3k9IjUwIiByPSI4IiBmaWxsPSIjMDVBNDkzIiAvPgo8L3N2Zz4="
                                alt="Logo"
                                className="h-8 w-8 sm:h-12 sm:w-12 mr-2 sm:mr-3"
                            />
                            <span className="text-lg sm:text-2xl font-bold text-primary">
                                PIBA<span className="text-secondary"></span>
                                <span className="hidden sm:inline"> Marketplace</span>
                            </span>
                        </Link>
                    </div>

                    {/* Search Bar - Desktop */}
                    <div className="hidden md:flex flex-1 max-w-md mx-8">
                        <div className="relative w-full">
                            <Input
                                type="text"
                                placeholder={__('marketplace.search_products')}
                                value={searchTerm}
                                onChange={handleSearchInputChange}
                                onKeyPress={handleSearchKeyPress}
                                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                            />
                            <button
                                onClick={handleSearch}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary"
                            >
                                <Search className="h-5 w-5" />
                            </button>
                        </div>
                    </div>

                    {/* Desktop Actions */}
                    <div className="hidden md:flex items-center space-x-4">
                        {renderAccountSection()}
                        {auth.user && (
                            <Link href="/marketplace/orders" className="flex flex-col items-center text-gray-700 hover:text-primary relative">
                                <Package className="h-6 w-6" />
                                <span className="text-xs">{__('cart.my_orders')}</span>
                            </Link>
                        )}
                        <Link href="/marketplace/wishlist" className="flex flex-col items-center text-gray-700 hover:text-primary relative">
                            <Heart className="h-6 w-6" />
                            <span className="text-xs">{__('cart.wishlist')}</span>
                            {wishlistCount > 0 && (
                                <Badge className="absolute -top-2 -right-2 bg-pink-500 text-white">
                                    {wishlistCount}
                                </Badge>
                            )}
                        </Link>
                        <Link href="/marketplace/cart" className="flex flex-col items-center text-gray-700 hover:text-primary relative">
                            <ShoppingCart className="h-6 w-6" />
                            <span className="text-xs">{__('cart.cart')}</span>
                            {cartCount > 0 && (
                                <Badge className="absolute -top-2 -right-2 bg-secondary text-white">
                                    {cartCount}
                                </Badge>
                            )}
                        </Link>
                    </div>

                    {/* Mobile Actions */}
                    <div className="flex md:hidden items-center space-x-2">
                        {renderAccountSection()}
                        <Link href="/marketplace/wishlist" className="relative">
                            <Heart className="h-5 w-5 text-gray-700" />
                            {wishlistCount > 0 && (
                                <Badge className="absolute -top-1 -right-1 bg-pink-500 text-white text-xs w-4 h-4 flex items-center justify-center">
                                    {wishlistCount}
                                </Badge>
                            )}
                        </Link>
                        <Link href="/marketplace/cart" className="relative">
                            <ShoppingCart className="h-5 w-5 text-gray-700" />
                            {cartCount > 0 && (
                                <Badge className="absolute -top-1 -right-1 bg-secondary text-white text-xs w-4 h-4 flex items-center justify-center">
                                    {cartCount}
                                </Badge>
                            )}
                        </Link>
                        <button
                            id="mobile-menu-button"
                            onClick={toggleMobileMenu}
                            className="text-gray-700 hover:text-primary"
                        >
                            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                        </button>
                    </div>
                </div>

                {/* Mobile Search Bar */}
                <div className="md:hidden pb-3">
                    <div className="relative">
                        <Input
                            type="text"
                            placeholder={__('marketplace.search_products')}
                            value={searchTerm}
                            onChange={handleSearchInputChange}
                            onKeyPress={handleSearchKeyPress}
                            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                        <button
                            onClick={handleSearch}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary"
                        >
                            <Search className="h-5 w-5" />
                        </button>
                    </div>
                </div>

                {/* Desktop Navigation */}
                <nav className="hidden md:flex justify-between items-center py-4 border-t">
                    <div className="flex space-x-8 relative">
                        <Link href="/marketplace" className="text-gray-700 hover:text-primary">{__('cart.homepage')}</Link>
                        <span className="text-gray-700 hover:text-primary">|</span>
                        <Link href="/marketplace/bundles" className="text-gray-700 hover:text-primary">{__('marketplace.bundles')}</Link>
                        <span className="text-gray-700 hover:text-primary">|</span>
                        {sortedTopCategories.map(cat => (
                            <div
                                key={cat.id}
                                className="relative"
                                onMouseEnter={() => setOpenDropdown(cat.id)}
                                onMouseLeave={() => setOpenDropdown(null)}
                            >
                                <Link
                                    href={`/marketplace/category/${cat.slug}`}
                                    className="text-gray-700 hover:text-primary flex items-center"
                                >
                                    {cat.name}
                                    {cat.children && cat.children.length > 0 && (
                                        <ChevronDown className="ml-1 h-4 w-4" />
                                    )}
                                </Link>
                                {cat.children && cat.children.length > 0 && openDropdown === cat.id && (
                                    <div className="absolute left-0 top-full min-w-[180px] bg-white shadow-lg rounded z-20" style={{ paddingTop: 2 }}>
                                        <div style={{ height: 8, marginTop: -8, pointerEvents: 'none' }} />
                                        <ul className="py-2">
                                            {cat.children.map(child => (
                                                <li key={child.id}>
                                                    <Link
                                                        href={`/marketplace/category/${child.slug}`}
                                                        className="block px-4 py-2 text-gray-700 hover:bg-primary/10 hover:text-primary whitespace-nowrap"
                                                    >
                                                        {child.name}
                                                    </Link>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        ))}
                        {sortedMoreCategories.length > 0 && (
                            <div
                                className="relative"
                                onMouseEnter={() => setOpenDropdown('more')}
                                onMouseLeave={() => setOpenDropdown(null)}
                            >
                                <div className="text-gray-700 hover:text-primary cursor-pointer flex items-center">
                                    {__('cart.other')}
                                    <ChevronDown className="ml-1 h-4 w-4" />
                                </div>
                                {openDropdown === 'more' && (
                                    <div className="absolute left-0 top-full min-w-[200px] bg-white shadow-lg rounded z-20" style={{ paddingTop: 2 }}>
                                        <div style={{ height: 8, marginTop: -8, pointerEvents: 'none' }} />
                                        <ul className="py-2">
                                            {sortedMoreCategories.map(cat => (
                                                <li key={cat.id} className="relative group">
                                                    <Link
                                                        href={`/marketplace/category/${cat.slug}`}
                                                        className="block px-4 py-2 text-gray-700 hover:bg-primary/10 hover:text-primary whitespace-nowrap flex items-center justify-between"
                                                    >
                                                        {cat.name}
                                                        {cat.children && cat.children.length > 0 && (
                                                            <ChevronDown className="h-4 w-4 -rotate-90" />
                                                        )}
                                                    </Link>
                                                    {cat.children && cat.children.length > 0 && (
                                                        <div className="absolute left-full top-0 min-w-[180px] bg-white shadow-lg rounded z-30 hidden group-hover:block">
                                                            <ul className="py-2">
                                                                {cat.children.map(child => (
                                                                    <li key={child.id}>
                                                                        <Link
                                                                            href={`/marketplace/category/${child.slug}`}
                                                                            className="block px-4 py-2 text-gray-700 hover:bg-primary/10 hover:text-primary whitespace-nowrap"
                                                                        >
                                                                            {child.name}
                                                                        </Link>
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    )}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                    <Link href="/marketplace/special-offers" className="text-secondary font-semibold">{__('cart.special_offers')}</Link>
                </nav>

                {/* Mobile Navigation */}
                {isMobileMenuOpen && (
                    <div id="mobile-menu" className="md:hidden border-t bg-white">
                        <div className="py-4 space-y-4">
                            <Link
                                href="/marketplace"
                                className="block text-gray-700 hover:text-primary"
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                {__('cart.homepage')}
                            </Link>

                            <Link
                                href="/marketplace/bundles"
                                className="block text-gray-700 hover:text-primary font-medium"
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                {__('marketplace.bundles')}
                            </Link>

                            {auth.user && (
                                <Link
                                    href="/marketplace/orders"
                                    className="flex items-center text-gray-700 hover:text-primary"
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    <Package className="h-5 w-5 mr-2" />
                                    {__('cart.my_orders')}
                                </Link>
                            )}

                            {sortedTopCategories.map(cat => (
                                <div key={cat.id} className="space-y-2">
                                    <Link
                                        href={`/marketplace/category/${cat.slug}`}
                                        className="block text-gray-700 hover:text-primary font-medium"
                                        onClick={() => setIsMobileMenuOpen(false)}
                                    >
                                        {cat.name}
                                    </Link>
                                    {cat.children && cat.children.length > 0 && (
                                        <div className="ml-4 space-y-1">
                                            {cat.children.map(child => (
                                                <Link
                                                    key={child.id}
                                                    href={`/marketplace/category/${child.slug}`}
                                                    className="block text-sm text-gray-600 hover:text-primary"
                                                    onClick={() => setIsMobileMenuOpen(false)}
                                                >
                                                    {child.name}
                                                </Link>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}

                            {sortedMoreCategories.map(cat => (
                                <div key={cat.id} className="space-y-2">
                                    <Link
                                        href={`/marketplace/category/${cat.slug}`}
                                        className="block text-gray-700 hover:text-primary font-medium"
                                        onClick={() => setIsMobileMenuOpen(false)}
                                    >
                                        {cat.name}
                                    </Link>
                                    {cat.children && cat.children.length > 0 && (
                                        <div className="ml-4 space-y-1">
                                            {cat.children.map(child => (
                                                <Link
                                                    key={child.id}
                                                    href={`/marketplace/category/${child.slug}`}
                                                    className="block text-sm text-gray-600 hover:text-primary"
                                                    onClick={() => setIsMobileMenuOpen(false)}
                                                >
                                                    {child.name}
                                                </Link>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}

                            <Link
                                href="/marketplace/special-offers"
                                className="block text-secondary font-semibold"
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                {__('cart.special_offers')}
                            </Link>
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}
