import React, { useState, useRef } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { ShoppingCart, Package } from 'lucide-react';
import ImageWithFallback from '@/Components/ImageWithFallback';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';

export default function BundleCard({ bundle }) {
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const processingRef = useRef(false);

    const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const addToCart = async (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Prevent double clicks like ProductDetail
        if (processingRef.current) return;

        processingRef.current = true;

        try {
            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('marketplace.csrf_token_missing'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        bundle_id: bundle.id,
                        quantity: 1,
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                // Guest user - use localStorage like ProductDetail
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.bundle_id && item.bundle_id === bundle.id) ||
                    (item.id === bundle.id && !item.bundle_id)
                );

                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += 1;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        bundle_id: bundle.id,
                        name: bundle.name,
                        slug: bundle.slug,
                        image: bundle.image || bundle.image_url_formatted || bundle.image_url,
                        price: bundle.bundle_price || bundle.total_price,
                        quantity: 1,
                        category: 'Bundle',
                        stock_quantity: 999, // Bundles typically don't have stock limits
                        subtotal: bundle.bundle_price || bundle.total_price
                    });
                }

                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', __('marketplace.bundle_added_to_cart'));
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            addAlert('error', __('marketplace.bundle_add_cart_error'));
        } finally {
            processingRef.current = false;
        }
    };

    const calculateSavings = () => {
        const originalPrice = bundle.original_total_price || bundle.original_price || 0;
        const currentPrice = bundle.total_price || bundle.bundle_price || 0;

        if (!originalPrice || originalPrice <= currentPrice) {
            return 0;
        }
        return originalPrice - currentPrice;
    };

    const calculateDiscountPercentage = () => {
        const originalPrice = bundle.original_total_price || bundle.original_price || 0;
        const currentPrice = bundle.total_price || bundle.bundle_price || 0;

        if (!originalPrice || originalPrice <= currentPrice) {
            return 0;
        }
        return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
    };

    const savings = calculateSavings();
    const discountPercentage = calculateDiscountPercentage();

    return (
        <div className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
            <Link href={`/marketplace/bundles/${bundle.slug || bundle.id}`}>
                <div className="relative">
                    {/* Bundle Image */}
                    <div className="aspect-w-16 aspect-h-12 bg-accent">
                        <ImageWithFallback
                            src={bundle.image_url}
                            alt={bundle.name}
                            fallbackText={bundle.name ? bundle.name.charAt(0).toUpperCase() : 'B'}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                            width="w-full"
                            height="h-48"
                            rounded="rounded-none"
                        />
                    </div>

                    {/* Badges */}
                    <div className="absolute top-2 left-2 flex flex-col space-y-1">
                        {bundle.is_featured && (
                            <span className="bg-secondary text-secondary-foreground px-2 py-1 rounded text-xs font-semibold">
                                {__('marketplace.featured_badge')}
                            </span>
                        )}
                        {discountPercentage > 0 && (
                            <span className="bg-destructive text-destructive-foreground px-2 py-1 rounded text-xs font-semibold">
                                {__('product.discount_percent', { percent: discountPercentage })}
                            </span>
                        )}
                    </div>

                    {/* Bundle count badge */}
                    <div className="absolute top-2 right-2">
                        <span className="bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-semibold flex items-center gap-1">
                            <Package className="w-3 h-3" />
                            {(bundle.items?.length || bundle.items_count || 0)} {__('product.products')}
                        </span>
                    </div>
                </div>

                <div className="p-4">
                    {/* Bundle Name */}
                    <h3 className="font-semibold text-lg text-foreground mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                        {bundle.name}
                    </h3>

                    {/* Bundle Description */}
                    {bundle.description && (
                        <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                            {bundle.description}
                        </p>
                    )}

                    {/* Product Items Preview */}
                    <div className="mb-3">
                        <div className="text-xs text-muted-foreground mb-1">{__('product.bundle_items')}</div>
                        <div className="space-y-1">
                            {bundle.items?.slice(0, 2).map((item, index) => (
                                <div key={index} className="flex justify-between text-xs text-muted-foreground">
                                    <span className="line-clamp-1">{item.product?.name || __('product.product')}</span>
                                    <span>x{item.quantity || 1}</span>
                                </div>
                            ))}
                            {bundle.items?.length > 2 && (
                                <div className="text-xs text-primary">
                                    +{bundle.items.length - 2} {__('orders.and_more_items', { count: bundle.items.length - 2 })}
                                </div>
                            )}
                            {(!bundle.items || bundle.items.length === 0) && bundle.items_count > 0 && (
                                <div className="text-xs text-muted-foreground">
                                    {bundle.items_count} {__('product.products')}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <div className="text-xl font-bold text-primary">
                                    {formatCurrency(bundle.total_price || bundle.bundle_price || 0)}
                                </div>
                                {savings > 0 && (
                                    <div className="text-sm text-muted-foreground line-through">
                                        {formatCurrency(bundle.original_total_price || bundle.original_price || 0)}
                                    </div>
                                )}
                            </div>
                            {savings > 0 && (
                                <div className="text-right">
                                    <div className="text-sm text-secondary font-semibold">
                                        {__('product.bundle_savings')}
                                    </div>
                                    <div className="text-sm font-bold text-secondary">
                                        {formatCurrency(savings)}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </Link>

            {/* Action Buttons */}
            <div className="p-4 pt-0">
                <div className="flex space-x-2">
                    <Button
                        onClick={addToCart}
                        disabled={processingRef.current || !bundle.is_active}
                        variant={bundle.is_active ? "primary" : "ghost"}
                        className={`flex-1 ${processingRef.current ? 'opacity-50' : ''}`}
                    >
                        {processingRef.current ? (
                            <div className="flex items-center justify-center">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                {__('common.adding')}
                            </div>
                        ) : (
                            <>
                                <ShoppingCart className="mr-2 h-4 w-4" />
                                {__('marketplace.add_to_cart')}
                            </>
                        )}
                    </Button>

                    <Button
                        asChild
                        variant="outline"
                    >
                        <Link href={`/marketplace/bundles/${bundle.slug || bundle.id}`}>
                            {__('common.details')}
                        </Link>
                    </Button>
                </div>
            </div>
        </div>
    );
}

export { BundleCard };
