import { router } from "@inertiajs/react";

export default function SecondaryButton({
    type = 'button',
    href = null,
    className = '',
    disabled,
    children,
    onClick,
    ...props
}) {
    const handleNavigate = () => {
        if (href) {
            router.visit(href);
        }
    }

    return (
        <button
            {...props}
            type={type}
            className={
                `inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold tracking-widest text-gray-700 shadow-sm transition duration-150 ease-in-out hover:bg-gray-50 disabled:opacity-25 ${disabled && 'opacity-25'
                } ` + className
            }
            onClick={href ? handleNavigate : onClick}
            disabled={disabled}
        >
            {children}
        </button>
    );
}