import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';

export default forwardRef(function SelectWithLabel(
    { className = '', isFocused = false, label, id, errors, children, required = false, ...props },
    ref,
) {
    const localRef = useRef(null);

    useImperativeHandle(ref, () => ({
        focus: () => localRef.current?.focus(),
    }));

    useEffect(() => {
        if (isFocused) {
            localRef.current?.focus();
        }
    }, [isFocused]);

    return (
        <div className="mb-4">
            <label htmlFor={id} className="text-sm block mb-1 font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
                {...props}
                id={id}
                ref={localRef}
                className={`w-full border rounded-md px-4 py-2 h-10 text-sm focus:outline-none focus:border-2 ${errors ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-primary-600'} ${className}`}
            >
                {children}
            </select>
            {errors && (
                <p className="mt-1 text-sm text-red-600">{errors}</p>
            )}
        </div>
    );
});
