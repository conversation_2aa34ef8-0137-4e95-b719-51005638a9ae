import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';

export default forwardRef(function TextInputWithLabel(
    {
        type = 'text',
        className = '',
        isFocused = false,
        label,
        placeholder,
        id,
        errors,
        required = false,
        icon = null,
        iconPosition = 'left',
        ...props
    },
    ref,
) {
    const localRef = useRef(null);

    useImperativeHandle(ref, () => ({
        focus: () => localRef.current?.focus(),
    }));

    useEffect(() => {
        if (isFocused) {
            localRef.current?.focus();
        }
    }, [isFocused]);

    const iconPaddingClass = icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : '';

    return (
        <div className="">
            <label htmlFor={id} className="text-sm block mb-1 font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="relative">
                {icon && iconPosition === 'left' && (
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        {icon}
                    </div>
                )}
                <input
                    {...props}
                    type={type}
                    id={id}
                    ref={localRef}
                    className={`w-full focus:ring-1 focus:ring-ring border rounded-md px-4 py-2 h-10 text-sm focus:outline-none ${iconPaddingClass} ${errors ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-600'} ${className}`}
                    placeholder={placeholder}
                />
                {icon && iconPosition === 'right' && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        {icon}
                    </div>
                )}
            </div>
            {errors && (
                <p className="mt-1 text-sm text-red-600">{errors}</p>
            )}
        </div>
    );
});
