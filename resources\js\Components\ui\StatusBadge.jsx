import React from 'react';
import { __ } from '@/utils/lang';

/**
 * StatusBadge Component - <PERSON><PERSON><PERSON> thị trạng thái với màu sắc tương ứng
 *
 * @param {string} status - Trạng thái cần hiển thị (pending, cancelled, pending_approval, confirm, complete, active, inactive, ...)
 * @param {boolean} isActive - Trạng thái active/inactive (optional)
 * @param {string} className - Custom className (optional)
 * @param {string} text - Custom text to display (optional)
 * @param {string} color - Custom color style (optional, e.g. "bg-purple-100 text-purple-800")
 * @param {object} customStyles - Custom styles object to override default styles (optional)
 * @param {object} props - Các props khác
 * @returns {JSX.Element}
 */
const StatusBadge = ({ status, isActive, className = '', text, color, customStyles = {}, ...props }) => {

    if (isActive !== undefined) {
        status = isActive ? 'active' : 'inactive';
    }

    const normalizedStatus = status?.toLowerCase().trim() || 'default';

    const defaultStyles = {
        pending: 'bg-yellow-100 text-yellow-800',
        cancelled: 'bg-red-100 text-red-800',
        pending_approval: 'bg-orange-100 text-orange-800',
        confirm: 'bg-blue-100 text-blue-800',
        confirmed: 'bg-blue-100 text-blue-800',
        complete: 'bg-green-100 text-green-800',
        completed: 'bg-green-100 text-green-800',
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-gray-100 text-gray-800',
        processing: 'bg-blue-100 text-blue-800',
        waiting: 'bg-purple-100 text-purple-800',
        rejected: 'bg-red-100 text-red-800',
        approved: 'bg-green-100 text-green-800',

        paid: 'bg-emerald-100 text-emerald-800',
        unpaid: 'bg-red-100 text-red-800',
        partial: 'bg-amber-100 text-amber-800',
        refunded: 'bg-purple-100 text-purple-800',
        failed: 'bg-red-100 text-red-800',

        cash: 'bg-green-100 text-green-800',
        momo: 'bg-pink-100 text-pink-800',
        vnpay: 'bg-blue-100 text-blue-800',
        zalopay: 'bg-blue-100 text-blue-800',
        bank_transfer: 'bg-indigo-100 text-indigo-800',
        credit_card: 'bg-gray-100 text-gray-800',

        indoor: 'bg-green-100 text-green-800',
        outdoor: 'bg-blue-100 text-blue-800',

        expired: 'bg-red-100 text-red-800',

        upcoming: 'bg-blue-100 text-blue-800',
        ongoing: 'bg-green-100 text-green-800',
        default: 'bg-gray-100 text-gray-800',
    };

    const mergedStyles = { ...defaultStyles, ...customStyles };

    const styleClass = color || mergedStyles[normalizedStatus] || mergedStyles.default;

    const getTranslatedStatus = (status) => {

        if (text) {
            return text;
        }

        const commonStatus = __(`common.status_${normalizedStatus}`);

        if (commonStatus !== `common.status_${normalizedStatus}`) {
            return commonStatus;
        }

        const directStatus = __(`common.${normalizedStatus}`);
        if (directStatus !== `common.${normalizedStatus}`) {
            return directStatus;
        }

        return status;
    };

    return (
        <span
            className={`inline-flex items-center text-center px-2 py-0.5 rounded-lg text-xs font-medium ${styleClass} ${className}`}
            {...props}
        >
            {getTranslatedStatus(status)}
        </span>
    );
};

export default StatusBadge;
