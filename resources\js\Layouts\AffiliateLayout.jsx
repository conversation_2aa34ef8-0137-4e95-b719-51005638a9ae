import React, { useState, useEffect } from 'react';
import { Link, usePage, router } from '@inertiajs/react';
import {
    User,
    Bell,
    Calendar,
    Clock,
    Heart,
    UserPlus,
    Gift,
    CreditCard,
    FileText,
    HelpCircle,
    ChevronDown,
    LogOut,
    ChevronLeft,
    ChevronRight,
    Home,
    BarChart3,
    Link as LinkIcon,
    DollarSign,
    TrendingUp,
    Settings,
    Download
} from 'lucide-react';
import Loading from '@/Components/Loading';
import UserAvatar from '@/Components/UserAvatar';

const AffiliateLayout = ({ children, title = 'Affiliate Dashboard' }) => {
    const [notifications, setNotifications] = useState(3);
    const { url: currentPath, props: { siteSettings } = { siteSettings: {} } } = usePage();
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [userMenuOpen, setUserMenuOpen] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
    const { auth } = usePage().props;
    const siteName = siteSettings?.site_name || 'PicklePlay';
    const logo = siteSettings?.site_logo || '/images/logo.png';

    const user = {
        avatar: auth.user.avatar_url,
        email: auth.user.email,
        name: auth.user.name
    }

    const handleLinkClick = (e, href, method = 'get', data = {}, replace = false) => {
        e.preventDefault();
        setIsPageLoading(true);
        setUserMenuOpen(false);
        router.visit(href, { method, data, replace });
    };

    const toggleSidebar = () => {
        setSidebarCollapsed(!sidebarCollapsed);
    };

    useEffect(() => {
        const handleStart = () => {
            setIsPageLoading(true);
        };

        const handleFinish = () => {
            setTimeout(() => {
                setIsPageLoading(false);
            }, 150);
        };

        const unsubscribeStart = router.on('start', handleStart);
        const unsubscribeFinish = router.on('finish', handleFinish);

        // Load sidebar state from localStorage if available
        const savedSidebarState = localStorage.getItem('affiliateSidebarCollapsed');
        if (savedSidebarState !== null) {
            setSidebarCollapsed(JSON.parse(savedSidebarState));
        }

        return () => {
            unsubscribeStart();
            unsubscribeFinish();
        };
    }, []);

    useEffect(() => {
        localStorage.setItem('affiliateSidebarCollapsed', JSON.stringify(sidebarCollapsed));
    }, [sidebarCollapsed]);

    return (
        <div className="flex flex-col w-full max-w-[1440px] h-screen mx-auto bg-white shadow-md overflow-hidden">
            {isPageLoading && (
                <Loading
                    fullScreen
                    text="Đang tải..."
                />
            )}

            <header className="sticky top-0 bg-white px-2 py-2 items-center shadow-md z-1000">
                <div className='flex justify-between px-[50px]'>
                    <div className="w-1/3 flex items-center gap-3">
                        <a
                            href="/"
                            className={`flex items-center gap-1 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors ${currentPath === '/' ? 'bg-gray-100 text-primary' : 'text-gray-700'}`}
                            onClick={(e) => handleLinkClick(e, '/')}
                        >
                            <Home size={18} />
                            <span className="text-sm font-medium">Trang chủ</span>
                        </a>
                        <a
                            href="/"
                            className="flex items-center gap-2 no-underline"
                            onClick={(e) => handleLinkClick(e, '/')}
                        >
                            <img src={logo} alt={siteName} className="h-13 w-12 object-contain" onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.parentElement.innerHTML += `
                                    <div class="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                        <span class="text-3xl font-medium text-gray-500">
                                           <i class="fas fa-image"></i>
                                        </span>
                                    </div>
                                `;
                            }} />
                        </a>
                    </div>

                    <nav className="w-full items-center justify-center hidden md:flex gap-8 text-base">
                        <span className="text-sm text-[#333] font-bold text-primary">
                            Affiliate Dashboard
                        </span>
                    </nav>

                    <div className="w-1/3 flex items-center justify-end gap-2">
                        <div className="relative">
                            <button
                                className="p-2 rounded-full hover:bg-gray-100 transition-colors relative"
                                onClick={() => setUserMenuOpen(!userMenuOpen)}
                            >
                                <Bell size={20} className="text-gray-600" />
                                {notifications > 0 && (
                                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                        {notifications}
                                    </span>
                                )}
                            </button>
                        </div>

                        <div className="relative">
                            <button
                                className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                                onClick={() => setUserMenuOpen(!userMenuOpen)}
                            >
                                <UserAvatar user={user} size="sm" />
                                <ChevronDown size={16} className="text-gray-600" />
                            </button>

                            {userMenuOpen && (
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                    <div className="px-4 py-2 border-b border-gray-200">
                                        <p className="text-sm font-medium text-gray-900">{user.name}</p>
                                        <p className="text-xs text-gray-500">{user.email}</p>
                                    </div>
                                    
                                    <Link
                                        href="/affiliate/profile"
                                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        onClick={(e) => handleLinkClick(e, '/affiliate/profile')}
                                    >
                                        <User size={16} />
                                        Hồ sơ affiliate
                                    </Link>
                                    
                                    <Link
                                        href="/user/profile"
                                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        onClick={(e) => handleLinkClick(e, '/user/profile')}
                                    >
                                        <Settings size={16} />
                                        Tài khoản cá nhân
                                    </Link>
                                    
                                    <button
                                        className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        onClick={(e) => handleLinkClick(e, '/logout', 'post')}
                                    >
                                        <LogOut size={16} />
                                        Đăng xuất
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </header>

            <div className="flex flex-1 overflow-hidden relative">
                {/* Sidebar */}
                <div className={`bg-white border-r border-gray-200 h-full flex flex-col flex-shrink-0 z-10 transition-all duration-300 ${sidebarCollapsed ? 'w-16' : 'w-64'}`}>
                    <div className="py-5 flex-grow overflow-y-auto custom-scrollbar">
                        {!sidebarCollapsed && (
                            <div className="px-5 py-2 text-xs uppercase tracking-wider text-gray-500 font-semibold">
                                Dashboard
                            </div>
                        )}

                        <Link
                            href="/affiliate/dashboard"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/dashboard') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/dashboard')}
                        >
                            <BarChart3 size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Tổng quan</span>}
                        </Link>

                        {!sidebarCollapsed && (
                            <div className="px-5 py-2 mt-2 text-xs uppercase tracking-wider text-gray-500 font-semibold">
                                Quản lý Links
                            </div>
                        )}

                        {sidebarCollapsed && <div className="border-t border-gray-200 my-2"></div>}

                        <Link
                            href="/affiliate/links"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/links') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/links')}
                        >
                            <LinkIcon size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Affiliate Links</span>}
                        </Link>

                        {!sidebarCollapsed && (
                            <div className="px-5 py-2 mt-2 text-xs uppercase tracking-wider text-gray-500 font-semibold">
                                Thu nhập
                            </div>
                        )}

                        {sidebarCollapsed && <div className="border-t border-gray-200 my-2"></div>}

                        <Link
                            href="/affiliate/commissions"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/commissions') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/commissions')}
                        >
                            <DollarSign size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Hoa hồng</span>}
                        </Link>

                        <Link
                            href="/affiliate/withdrawals"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/withdrawals') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/withdrawals')}
                        >
                            <CreditCard size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Rút tiền</span>}
                        </Link>

                        {!sidebarCollapsed && (
                            <div className="px-5 py-2 mt-2 text-xs uppercase tracking-wider text-gray-500 font-semibold">
                                Báo cáo
                            </div>
                        )}

                        {sidebarCollapsed && <div className="border-t border-gray-200 my-2"></div>}

                        <Link
                            href="/affiliate/reports"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/reports') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/reports')}
                        >
                            <TrendingUp size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Báo cáo hiệu suất</span>}
                        </Link>

                        {!sidebarCollapsed && (
                            <div className="px-5 py-2 mt-2 text-xs uppercase tracking-wider text-gray-500 font-semibold">
                                Tài khoản
                            </div>
                        )}

                        {sidebarCollapsed && <div className="border-t border-gray-200 my-2"></div>}

                        <Link
                            href="/affiliate/profile"
                            className={`px-5 py-2 flex items-center gap-3 hover:bg-gray-50 transition-colors ${currentPath.startsWith('/affiliate/profile') ? 'text-red-600 bg-red-50' : 'text-gray-600'}`}
                            onClick={(e) => handleLinkClick(e, '/affiliate/profile')}
                        >
                            <User size={16} />
                            {!sidebarCollapsed && <span className="text-sm font-medium">Hồ sơ affiliate</span>}
                        </Link>
                    </div>

                    {/* Sidebar Toggle Button */}
                    <div className="border-t border-gray-200 p-2">
                        <button
                            onClick={toggleSidebar}
                            className="w-full flex items-center justify-center p-2 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                            {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
                        </button>
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 flex flex-col overflow-hidden">
                    <main className="flex-1 overflow-y-auto bg-gray-50 p-6">
                        {children}
                    </main>
                </div>
            </div>
        </div>
    );
};

export default AffiliateLayout;
