import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AffiliateLayout from '@/Layouts/AffiliateLayout';
import { 
    DollarSign, 
    TrendingUp, 
    Clock, 
    CheckCircle,
    AlertCircle,
    Calendar,
    Filter,
    Download
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils/formatting';

export default function Index({ commissions, stats, filters }) {
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });

    const handleFilter = () => {
        router.get(route('affiliate.commissions.index'), {
            status: selectedStatus,
            start_date: dateRange.start_date,
            end_date: dateRange.end_date
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const getStatusColor = (status) => {
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            approved: 'bg-blue-100 text-blue-800',
            paid: 'bg-green-100 text-green-800',
            rejected: 'bg-red-100 text-red-800'
        };
        return colors[status] || colors.pending;
    };

    const getStatusLabel = (status) => {
        const labels = {
            pending: 'Chờ duyệt',
            approved: 'Đã duyệt',
            paid: 'Đã trả',
            rejected: 'Từ chối'
        };
        return labels[status] || status;
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending':
                return <Clock className="w-4 h-4" />;
            case 'approved':
                return <CheckCircle className="w-4 h-4" />;
            case 'paid':
                return <CheckCircle className="w-4 h-4" />;
            case 'rejected':
                return <AlertCircle className="w-4 h-4" />;
            default:
                return <Clock className="w-4 h-4" />;
        }
    };

    return (
        <AffiliateLayout title="Hoa hồng">
            <Head title="Hoa hồng" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <DollarSign className="w-6 h-6 mr-3" />
                            Quản lý Hoa hồng
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Theo dõi thu nhập và hoa hồng từ affiliate
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Tổng thu nhập</p>
                                <p className="text-2xl font-bold text-gray-900 mt-2">
                                    {formatCurrency(stats.total_earned)}
                                </p>
                            </div>
                            <div className="p-3 rounded-lg bg-green-50 text-green-600">
                                <TrendingUp className="w-6 h-6" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Chờ duyệt</p>
                                <p className="text-2xl font-bold text-gray-900 mt-2">
                                    {formatCurrency(stats.pending_amount)}
                                </p>
                            </div>
                            <div className="p-3 rounded-lg bg-yellow-50 text-yellow-600">
                                <Clock className="w-6 h-6" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Có thể rút</p>
                                <p className="text-2xl font-bold text-gray-900 mt-2">
                                    {formatCurrency(stats.available_balance)}
                                </p>
                            </div>
                            <div className="p-3 rounded-lg bg-blue-50 text-blue-600">
                                <DollarSign className="w-6 h-6" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Đã thanh toán</p>
                                <p className="text-2xl font-bold text-gray-900 mt-2">
                                    {formatCurrency(stats.paid_amount)}
                                </p>
                            </div>
                            <div className="p-3 rounded-lg bg-green-50 text-green-600">
                                <CheckCircle className="w-6 h-6" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Trạng thái
                            </label>
                            <select
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                                <option value="">Tất cả trạng thái</option>
                                <option value="pending">Chờ duyệt</option>
                                <option value="approved">Đã duyệt</option>
                                <option value="paid">Đã trả</option>
                                <option value="rejected">Từ chối</option>
                            </select>
                        </div>
                        
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Từ ngày
                            </label>
                            <input
                                type="date"
                                value={dateRange.start_date}
                                onChange={(e) => setDateRange({...dateRange, start_date: e.target.value})}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Đến ngày
                            </label>
                            <input
                                type="date"
                                value={dateRange.end_date}
                                onChange={(e) => setDateRange({...dateRange, end_date: e.target.value})}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                        </div>
                        
                        <div className="flex items-end">
                            <button
                                onClick={handleFilter}
                                className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                <Filter className="w-4 h-4 mr-2" />
                                Lọc
                            </button>
                        </div>
                    </div>
                </div>

                {/* Commissions Table */}
                <div className="bg-white rounded-lg shadow overflow-hidden">
                    {commissions.data && commissions.data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Conversion
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Hoa hồng
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Trạng thái
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Ngày tạo
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Ngày thanh toán
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {commissions.data.map((commission) => (
                                        <tr key={commission.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4">
                                                <div className="space-y-1">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        Conversion #{commission.conversion_id}
                                                    </div>
                                                    {commission.conversion?.campaign && (
                                                        <div className="text-xs text-gray-500">
                                                            Chiến dịch: {commission.conversion.campaign.name}
                                                        </div>
                                                    )}
                                                    <div className="text-xs text-gray-500">
                                                        Giá trị đơn hàng: {formatCurrency(commission.conversion?.order_value || 0)}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="space-y-1">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {formatCurrency(commission.amount)}
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        Tỷ lệ: {commission.rate}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(commission.status)}`}>
                                                    {getStatusIcon(commission.status)}
                                                    <span className="ml-1">{getStatusLabel(commission.status)}</span>
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 text-sm text-gray-500">
                                                <div className="flex items-center">
                                                    <Calendar className="w-3 h-3 mr-1" />
                                                    {formatDateTime(commission.created_at)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 text-sm text-gray-500">
                                                {commission.paid_at ? (
                                                    <div className="flex items-center">
                                                        <Calendar className="w-3 h-3 mr-1" />
                                                        {formatDateTime(commission.paid_at)}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">-</span>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Chưa có hoa hồng
                            </h3>
                            <p className="text-gray-500">
                                Hoa hồng sẽ xuất hiện khi có conversion thành công
                            </p>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {commissions.data && commissions.data.length > 0 && commissions.last_page > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
                        <div className="flex-1 flex justify-between sm:hidden">
                            {commissions.prev_page_url && (
                                <a
                                    href={commissions.prev_page_url}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Trước
                                </a>
                            )}
                            {commissions.next_page_url && (
                                <a
                                    href={commissions.next_page_url}
                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Sau
                                </a>
                            )}
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Hiển thị <span className="font-medium">{commissions.from}</span> đến{' '}
                                    <span className="font-medium">{commissions.to}</span> trong{' '}
                                    <span className="font-medium">{commissions.total}</span> kết quả
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AffiliateLayout>
    );
}
