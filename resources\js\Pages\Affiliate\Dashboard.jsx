import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AffiliateLayout from '@/Layouts/AffiliateLayout';
import {
    DollarSign,
    MousePointer,
    TrendingUp,
    Users,
    Eye,
    Calendar,
    ArrowUpRight,
    ArrowDownRight,
    ExternalLink
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils/formatting';

export default function Dashboard({
    affiliate = null,
    stats = {},
    period_stats = {},
    chart_data = [],
    recent_clicks = [],
    recent_conversions = [],
    recent_commissions = [],
    top_links = [],
    filters = {}
}) {
    const [dateRange, setDateRange] = useState({
        start_date: filters?.start_date || '',
        end_date: filters?.end_date || ''
    });

    const statCards = [
        {
            title: 'Số dư khả dụng',
            value: formatCurrency(stats?.available_balance || 0),
            icon: DollarSign,
            color: 'green',
            description: '<PERSON><PERSON> thể rút tiền'
        },
        {
            title: 'Tổng thu nhập',
            value: formatCurrency(stats?.total_earnings || 0),
            icon: TrendingUp,
            color: 'blue',
            description: 'Tất cả thời gian'
        },
        {
            title: 'Tổng clicks',
            value: (stats?.total_clicks || 0).toLocaleString(),
            icon: MousePointer,
            color: 'purple',
            description: 'Tất cả thời gian'
        },
        {
            title: 'Tỷ lệ chuyển đổi',
            value: `${stats?.conversion_rate || 0}%`,
            icon: Users,
            color: 'orange',
            description: 'Tất cả thời gian'
        }
    ];

    const getColorClasses = (color) => {
        const colors = {
            green: 'bg-green-50 text-green-600 border-green-200',
            blue: 'bg-blue-50 text-blue-600 border-blue-200',
            purple: 'bg-purple-50 text-purple-600 border-purple-200',
            orange: 'bg-orange-50 text-orange-600 border-orange-200'
        };
        return colors[color] || colors.blue;
    };

    return (
        <AffiliateLayout title="Affiliate Dashboard">
            <Head title="Affiliate Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Chào mừng trở lại, {affiliate?.user?.name || 'Partner'}!
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Tier: <span className="font-medium capitalize">{affiliate?.tier || 'Bronze'}</span> •
                            Mã giới thiệu: <span className="font-medium">{affiliate?.referral_code || 'N/A'}</span>
                        </p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${(affiliate?.status || 'pending') === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                            }`}>
                            {(affiliate?.status || 'pending') === 'active' ? 'Hoạt động' : 'Chờ duyệt'}
                        </span>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {statCards.map((stat, index) => (
                        <div key={index} className="bg-white rounded-lg shadow p-6 border">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                    <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                                </div>
                                <div className={`p-3 rounded-lg ${getColorClasses(stat.color)}`}>
                                    <stat.icon className="w-6 h-6" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Performance Chart */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Hiệu suất 30 ngày qua
                        </h3>
                        <div className="flex items-center space-x-4 text-sm">
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                <span className="text-gray-600">Clicks</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span className="text-gray-600">Conversions</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                <span className="text-gray-600">Thu nhập</span>
                            </div>
                        </div>
                    </div>

                    <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div className="text-center">
                            <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                            <p className="text-gray-500">Biểu đồ hiệu suất</p>
                            <p className="text-sm text-gray-400">Sẽ được implement với Chart.js</p>
                        </div>
                    </div>
                </div>

                {/* Recent Activity & Top Links */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Commissions */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                Hoa hồng gần đây
                            </h3>
                            <a
                                href="/affiliate/commissions"
                                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                            >
                                Xem tất cả
                                <ExternalLink className="w-3 h-3 ml-1" />
                            </a>
                        </div>

                        <div className="space-y-3">
                            {recent_commissions && recent_commissions.length > 0 ? (
                                recent_commissions.map((commission, index) => (
                                    <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                                        <div className="flex-1">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(commission.amount)}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {formatDateTime(commission.created_at)}
                                            </p>
                                        </div>
                                        <span className={`px-2 py-1 text-xs rounded-full ${commission.status === 'paid'
                                            ? 'bg-green-100 text-green-800'
                                            : commission.status === 'approved'
                                                ? 'bg-blue-100 text-blue-800'
                                                : 'bg-yellow-100 text-yellow-800'
                                            }`}>
                                            {commission.status === 'paid' ? 'Đã trả' :
                                                commission.status === 'approved' ? 'Đã duyệt' : 'Chờ duyệt'}
                                        </span>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                    <p className="text-gray-500">Chưa có hoa hồng nào</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Top Performing Links */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                Links hiệu suất cao
                            </h3>
                            <a
                                href="/affiliate/links"
                                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                            >
                                Quản lý links
                                <ExternalLink className="w-3 h-3 ml-1" />
                            </a>
                        </div>

                        <div className="space-y-3">
                            {top_links && top_links.length > 0 ? (
                                top_links.map((link, index) => (
                                    <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {link.title || 'Untitled Link'}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {link.clicks} clicks • {link.conversions} conversions
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(link.commissions)}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {link.conversion_rate}%
                                            </p>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <MousePointer className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                    <p className="text-gray-500">Chưa có links nào</p>
                                    <a
                                        href="/affiliate/links/create"
                                        className="text-sm text-blue-600 hover:text-blue-800 mt-2 inline-block"
                                    >
                                        Tạo link đầu tiên
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Thao tác nhanh
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <a
                            href="/affiliate/links/create"
                            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                        >
                            <div className="text-center">
                                <MousePointer className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm font-medium text-gray-900">Tạo link mới</p>
                                <p className="text-xs text-gray-500">Tạo affiliate link</p>
                            </div>
                        </a>

                        <a
                            href="/affiliate/withdrawals/create"
                            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
                        >
                            <div className="text-center">
                                <DollarSign className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm font-medium text-gray-900">Rút tiền</p>
                                <p className="text-xs text-gray-500">Yêu cầu rút tiền</p>
                            </div>
                        </a>

                        <a
                            href="/affiliate/reports"
                            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors"
                        >
                            <div className="text-center">
                                <TrendingUp className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm font-medium text-gray-900">Xem báo cáo</p>
                                <p className="text-xs text-gray-500">Phân tích hiệu suất</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </AffiliateLayout>
    );
}
