import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AffiliateLayout from '@/Layouts/AffiliateLayout';
import { 
    Link as LinkIcon, 
    Plus, 
    Copy, 
    Eye, 
    Edit, 
    Trash2,
    ExternalLink,
    MousePointer,
    TrendingUp,
    Search,
    Filter
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils/formatting';
import PrimaryButton from '@/Components/PrimaryButton';

export default function Index({ links, campaigns, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');
    const [selectedType, setSelectedType] = useState(filters.type || '');
    const [copiedLink, setCopiedLink] = useState(null);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(route('affiliate.links.index'), { 
            search: searchQuery,
            campaign_id: selectedCampaign,
            type: selectedType
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const copyToClipboard = async (url, linkId) => {
        try {
            await navigator.clipboard.writeText(url);
            setCopiedLink(linkId);
            setTimeout(() => setCopiedLink(null), 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
        }
    };

    const getTypeColor = (type) => {
        const colors = {
            product: 'bg-blue-100 text-blue-800',
            category: 'bg-green-100 text-green-800',
            landing_page: 'bg-purple-100 text-purple-800',
            custom: 'bg-gray-100 text-gray-800'
        };
        return colors[type] || colors.custom;
    };

    const getTypeLabel = (type) => {
        const labels = {
            product: 'Sản phẩm',
            category: 'Danh mục',
            landing_page: 'Landing Page',
            custom: 'Tùy chỉnh'
        };
        return labels[type] || type;
    };

    return (
        <AffiliateLayout title="Quản lý Links">
            <Head title="Quản lý Links" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <LinkIcon className="w-6 h-6 mr-3" />
                            Quản lý Affiliate Links
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Tạo và quản lý các affiliate links của bạn
                        </p>
                    </div>
                    <Link href={route('affiliate.links.create')}>
                        <PrimaryButton className="flex items-center">
                            <Plus className="w-4 h-4 mr-2" />
                            Tạo link mới
                        </PrimaryButton>
                    </Link>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <form onSubmit={handleSearch} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Tìm kiếm
                                </label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <input
                                        type="text"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        placeholder="Tìm theo tiêu đề, URL..."
                                        className="pl-10 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    />
                                </div>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Chiến dịch
                                </label>
                                <select
                                    value={selectedCampaign}
                                    onChange={(e) => setSelectedCampaign(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                >
                                    <option value="">Tất cả chiến dịch</option>
                                    {campaigns.map((campaign) => (
                                        <option key={campaign.id} value={campaign.id}>
                                            {campaign.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Loại link
                                </label>
                                <select
                                    value={selectedType}
                                    onChange={(e) => setSelectedType(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                >
                                    <option value="">Tất cả loại</option>
                                    <option value="product">Sản phẩm</option>
                                    <option value="category">Danh mục</option>
                                    <option value="landing_page">Landing Page</option>
                                    <option value="custom">Tùy chỉnh</option>
                                </select>
                            </div>
                            
                            <div className="flex items-end">
                                <PrimaryButton type="submit" className="w-full">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Lọc
                                </PrimaryButton>
                            </div>
                        </div>
                    </form>
                </div>

                {/* Links List */}
                <div className="bg-white rounded-lg shadow overflow-hidden">
                    {links.data && links.data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Link
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Hiệu suất
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Thu nhập
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Ngày tạo
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Thao tác
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {links.data.map((link) => (
                                        <tr key={link.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4">
                                                <div className="space-y-2">
                                                    <div className="flex items-center space-x-2">
                                                        <h3 className="text-sm font-medium text-gray-900">
                                                            {link.title || 'Untitled Link'}
                                                        </h3>
                                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(link.type)}`}>
                                                            {getTypeLabel(link.type)}
                                                        </span>
                                                        {link.campaign && (
                                                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                                {link.campaign.name}
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                                                            {link.affiliate_url}
                                                        </code>
                                                        <button
                                                            onClick={() => copyToClipboard(link.affiliate_url, link.id)}
                                                            className="text-gray-400 hover:text-gray-600"
                                                            title="Copy link"
                                                        >
                                                            {copiedLink === link.id ? (
                                                                <span className="text-green-600 text-xs">Đã copy!</span>
                                                            ) : (
                                                                <Copy className="w-4 h-4" />
                                                            )}
                                                        </button>
                                                    </div>
                                                    {link.description && (
                                                        <p className="text-xs text-gray-500 truncate max-w-md">
                                                            {link.description}
                                                        </p>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="space-y-1">
                                                    <div className="flex items-center text-sm">
                                                        <MousePointer className="w-3 h-3 mr-1 text-gray-400" />
                                                        <span className="text-gray-900">{link.clicks}</span>
                                                        <span className="text-gray-500 ml-1">clicks</span>
                                                    </div>
                                                    <div className="flex items-center text-sm">
                                                        <TrendingUp className="w-3 h-3 mr-1 text-gray-400" />
                                                        <span className="text-gray-900">{link.conversions}</span>
                                                        <span className="text-gray-500 ml-1">conversions</span>
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        CTR: {link.conversion_rate}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="space-y-1">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {formatCurrency(link.commissions)}
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        Doanh thu: {formatCurrency(link.revenue)}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 text-sm text-gray-500">
                                                {formatDateTime(link.created_at)}
                                            </td>
                                            <td className="px-6 py-4 text-right">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <Link
                                                        href={route('affiliate.links.show', link.id)}
                                                        className="text-gray-400 hover:text-gray-600"
                                                        title="Xem chi tiết"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </Link>
                                                    <Link
                                                        href={route('affiliate.links.edit', link.id)}
                                                        className="text-blue-400 hover:text-blue-600"
                                                        title="Chỉnh sửa"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </Link>
                                                    <a
                                                        href={link.original_url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-green-400 hover:text-green-600"
                                                        title="Mở link gốc"
                                                    >
                                                        <ExternalLink className="w-4 h-4" />
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <LinkIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Chưa có affiliate links
                            </h3>
                            <p className="text-gray-500 mb-6">
                                Tạo link đầu tiên để bắt đầu kiếm hoa hồng
                            </p>
                            <Link href={route('affiliate.links.create')}>
                                <PrimaryButton>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Tạo link đầu tiên
                                </PrimaryButton>
                            </Link>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {links.data && links.data.length > 0 && links.last_page > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
                        <div className="flex-1 flex justify-between sm:hidden">
                            {links.prev_page_url && (
                                <Link
                                    href={links.prev_page_url}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Trước
                                </Link>
                            )}
                            {links.next_page_url && (
                                <Link
                                    href={links.next_page_url}
                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Sau
                                </Link>
                            )}
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Hiển thị <span className="font-medium">{links.from}</span> đến{' '}
                                    <span className="font-medium">{links.to}</span> trong{' '}
                                    <span className="font-medium">{links.total}</span> kết quả
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    {/* Pagination links would go here */}
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AffiliateLayout>
    );
}
