import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import CustomerLayout from '@/Layouts/CustomerLayout';
import {
    Calendar,
    MapPin,
    Users,
    Trophy,
    Search,
    Star,
    CalendarClock
} from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

export default function Tournament({ tournaments: tournamentsProp, filters, statuses }) {
    const [selectedFilter, setSelectedFilter] = useState(filters?.status || 'all');
    const [searchQuery, setSearchQuery] = useState(filters?.search || '');

    const tournaments = tournamentsProp?.data || [];

    const filteredTournaments = tournaments.filter(tournament => {
        if (selectedFilter !== 'all' && tournament.status !== selectedFilter) {
            return false;
        }
        if (searchQuery && !tournament.title.toLowerCase().includes(searchQuery.toLowerCase())) {
            return false;
        }

        return true;
    });

    const formatDateRange = (startDate, endDate) => {
        const start = new Date(startDate);
        const end = new Date(endDate);

        const startDay = start.getDate();
        const startMonth = start.getMonth() + 1;
        const startYear = start.getFullYear();

        const endDay = end.getDate();
        const endMonth = end.getMonth() + 1;
        const endYear = end.getFullYear();

        if (startYear === endYear && startMonth === endMonth) {
            return `${startDay} - ${endDay}/${startMonth}/${startYear}`;
        } else if (startYear === endYear) {
            return `${startDay}/${startMonth} - ${endDay}/${endMonth}/${startYear}`;
        } else {
            return `${startDay}/${startMonth}/${startYear} - ${endDay}/${endMonth}/${endYear}`;
        }
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'upcoming':
                return <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">Sắp diễn ra</span>;
            case 'ongoing':
                return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Đang diễn ra</span>;
            case 'completed':
                return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">Đã kết thúc</span>;
            case 'cancelled':
                return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">Đã hủy</span>;
            case 'pending_approval':
                return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">Chờ duyệt</span>;
            default:
                return null;
        }
    };

    return (
        <CustomerLayout>
            <Head title="Giải đấu Pickleball" />

            {/* Hero Section */}
            <div className=" bg-gradient-to-br from-[#ee0033] to-[#cc0029] text-white text-center py-24">
                <div className="flex flex-col items-center mx-auto px-4">
                    <h1 className="text-3xl md:text-4xl font-bold mb-4">Giải đấu Pickleball</h1>
                    <p className="text-center text-lg md:text-xl opacity-90 max-w-2xl">
                        Khám phá và tham gia các giải đấu Pickleball hấp dẫn trên toàn quốc.
                        Nâng cao kỹ năng và kết nối với cộng đồng người chơi.
                    </p>
                </div>
            </div>

            {/* Filter and Search Section */}
            <div className="bg-white border-b">
                <div className=" mx-auto px-4 py-4">
                    <div className="flex flex-col md:flex-row gap-4 justify-between">
                        <div className="flex flex-wrap gap-2">
                            <button
                                onClick={() => setSelectedFilter('all')}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors 
                                ${selectedFilter === 'all' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                            >
                                Tất cả
                            </button>
                            <button
                                onClick={() => setSelectedFilter('upcoming')}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors 
                                ${selectedFilter === 'upcoming' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                            >
                                Sắp diễn ra
                            </button>
                            <button
                                onClick={() => setSelectedFilter('ongoing')}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors 
                                ${selectedFilter === 'ongoing' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                            >
                                Đang diễn ra
                            </button>
                            <button
                                onClick={() => setSelectedFilter('completed')}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors
                                ${selectedFilter === 'completed' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                            >
                                Đã kết thúc
                            </button>
                          
                        </div>
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Tìm kiếm giải đấu..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full md:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                            />
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Featured Tournaments */}
            {filteredTournaments.some(tournament => tournament.featured) && (
                <div className="bg-gray-50 py-8">
                    <div className=" mx-auto px-4">
                        <h2 className="text-2xl font-bold mb-6 flex items-center">
                            <Star className="text-yellow-500 mr-2" size={24} />
                            Giải đấu nổi bật
                        </h2>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {filteredTournaments
                                .filter(tournament => tournament.featured)
                                .map(tournament => (
                                    <div key={tournament.id} className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                                        <div className="flex flex-col md:flex-row h-full">
                                            <div className="md:w-2/5 h-48 md:h-auto relative">
                                                <div className="absolute top-2 left-2 z-10">
                                                    {getStatusBadge(tournament.status)}
                                                </div>
                                                <img
                                                    src={tournament.formatted_image_url || "https://via.placeholder.com/300x200?text=Pickleball+Tournament"}
                                                    alt={tournament.title}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                            <div className="p-5 md:w-3/5 flex flex-col justify-between">
                                                <div>
                                                    <h3 className="text-xl font-bold mb-2">{tournament.title}</h3>
                                                    <div className="flex items-start gap-2 mb-2">
                                                        <Calendar size={16} className="text-primary mt-1 flex-shrink-0" />
                                                        <span className="text-sm text-gray-700">
                                                            {formatDateRange(tournament.start_date, tournament.end_date)}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-start gap-2 mb-2">
                                                        <MapPin size={16} className="text-primary mt-1 flex-shrink-0" />
                                                        <span className="text-sm text-gray-700">{tournament.location}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <Users size={16} className="text-primary flex-shrink-0" />
                                                        <span className="text-sm text-gray-700">{tournament.participants_limit} người tham gia</span>
                                                    </div>
                                                </div>
                                                <div className="flex justify-between items-center mt-4">
                                                    <div className="flex items-center">
                                                        <Trophy size={16} className="text-yellow-500 mr-1" />
                                                        <span className="text-sm font-medium">{formatCurrency(tournament.prize_money)}</span>
                                                    </div>
                                                    <button className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium hover:bg-primary-dark transition-colors">
                                                        Chi tiết
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </div>
                </div>
            )}

            {/* All Tournaments */}
            <div className="py-8">
                <div className=" mx-auto px-4">
                    <h2 className="text-2xl font-bold mb-6">Tất cả giải đấu</h2>

                    {filteredTournaments.length === 0 ? (
                        <div className="bg-white rounded-lg p-8 text-center">
                            <Calendar className="mx-auto text-gray-400 mb-4" size={48} />
                            <h3 className="text-xl font-medium text-gray-700 mb-2">Không tìm thấy giải đấu</h3>
                            <p className="text-gray-500">
                                Không có giải đấu nào phù hợp với tiêu chí tìm kiếm của bạn.
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            {filteredTournaments.map(tournament => (
                                <div key={tournament.id} className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow border border-gray-100">
                                    <div className="relative">
                                        <img
                                            src={tournament.formatted_image_url || "https://via.placeholder.com/400x200?text=Pickleball+Tournament"}
                                            alt={tournament.title}
                                            className="w-full h-48 object-cover"
                                        />
                                        <div className="absolute top-2 left-2">
                                            {getStatusBadge(tournament.status)}
                                        </div>
                                    </div>
                                    <div className="p-5">
                                        <h3 className="text-lg font-bold mb-3 line-clamp-2">{tournament.title}</h3>

                                        <div className="space-y-2 mb-4">
                                            <div className="flex items-start gap-2">
                                                <Calendar size={16} className="text-primary mt-1 flex-shrink-0" />
                                                <span className="text-sm text-gray-700">
                                                    {formatDateRange(tournament.start_date, tournament.end_date)}
                                                </span>
                                            </div>
                                            <div className="flex items-start gap-2">
                                                <MapPin size={16} className="text-primary mt-1 flex-shrink-0" />
                                                <span className="text-sm text-gray-700 line-clamp-1">{tournament.location}</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <CalendarClock size={16} className="text-primary flex-shrink-0" />
                                                <span className="text-sm text-gray-700">
                                                    Hạn đăng ký: {new Date(tournament.registration_deadline).toLocaleDateString('vi-VN')}
                                                </span>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>


        </CustomerLayout>
    );
} 