import React, { useState, useRef, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload, Plus, Trash2, Calendar, Clock, DollarSign, Users, BookOpen } from 'lucide-react';
import Checkbox from '@/Components/Checkbox';

export default function Create({ lecturers = [] }) {
    const { flash } = usePage().props;
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(null);
    const [curriculum, setCurriculum] = useState(['']);
    const [requirements, setRequirements] = useState(['']);
    const [outcomes, setOutcomes] = useState(['']);
    const [tags, setTags] = useState(['']);
    const { addAlert } = useToast();

    const { data, setData, post, processing, errors, reset } = useForm({
        lecturer_id: '',
        title: '',
        short_description: '',
        description: '',
        category: '',
        level: 'beginner',
        duration_hours: '',
        total_lessons: '',
        price: '',
        original_price: '',
        max_students: '',
        curriculum: [],
        requirements: [],
        outcomes: [],
        tags: [],
        thumbnail: null,
        is_featured: false,
        is_free: false,
        start_date: '',
        end_date: '',
        status: 'pending_approval',
    });

    useEffect(() => {
        if (flash.error) {
            addAlert('error', flash.error);
        }
        if (flash.success) {
            addAlert('success', flash.success);
        }
    }, [flash]);

    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            const firstError = Object.values(errors)[0];
            addAlert('error', firstError);
        }
    }, [errors]);

    useEffect(() => {
        setData('curriculum', curriculum.filter(item => item.trim() !== ''));
    }, [curriculum]);

    useEffect(() => {
        setData('requirements', requirements.filter(item => item.trim() !== ''));
    }, [requirements]);

    useEffect(() => {
        setData('outcomes', outcomes.filter(item => item.trim() !== ''));
    }, [outcomes]);

    useEffect(() => {
        setData('tags', tags.filter(item => item.trim() !== ''));
    }, [tags]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.edu.courses.store'), {
            onSuccess: () => {
                reset();
                setImagePreview(null);
                setCurriculum(['']);
                setRequirements(['']);
                setOutcomes(['']);
                setTags(['']);
                addAlert('success', __('edu.course_created_successfully'));
            },
            onError: (errors) => {
                console.log('Validation errors:', errors);
                if (Object.keys(errors).length > 0) {
                    addAlert('error', Object.values(errors)[0]);
                }
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('thumbnail', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('thumbnail', null);
        setImagePreview(null);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    const addCurriculumItem = () => {
        setCurriculum([...curriculum, '']);
    };

    const removeCurriculumItem = (index) => {
        const newCurriculum = curriculum.filter((_, i) => i !== index);
        setCurriculum(newCurriculum);
    };

    const updateCurriculumItem = (index, value) => {
        const newCurriculum = [...curriculum];
        newCurriculum[index] = value;
        setCurriculum(newCurriculum);
    };

    const addRequirement = () => {
        setRequirements([...requirements, '']);
    };

    const removeRequirement = (index) => {
        const newRequirements = requirements.filter((_, i) => i !== index);
        setRequirements(newRequirements);
    };

    const updateRequirement = (index, value) => {
        const newRequirements = [...requirements];
        newRequirements[index] = value;
        setRequirements(newRequirements);
    };

    const addOutcome = () => {
        setOutcomes([...outcomes, '']);
    };

    const removeOutcome = (index) => {
        const newOutcomes = outcomes.filter((_, i) => i !== index);
        setOutcomes(newOutcomes);
    };

    const updateOutcome = (index, value) => {
        const newOutcomes = [...outcomes];
        newOutcomes[index] = value;
        setOutcomes(newOutcomes);
    };

    const addTag = () => {
        setTags([...tags, '']);
    };

    const removeTag = (index) => {
        const newTags = tags.filter((_, i) => i !== index);
        setTags(newTags);
    };

    const updateTag = (index, value) => {
        const newTags = [...tags];
        newTags[index] = value;
        setTags(newTags);
    };

    return (
        <SuperAdminLayout>
            <Head title={__('edu.create_new_course')} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.create_new_course')}</h1>
                        <Link
                            href={route('superadmin.edu.courses.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            <i className="fas fa-arrow-left mr-2"></i>
                            {__('edu.back_to_courses')}
                        </Link>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="md:col-span-2">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('edu.basic_information')}</h2>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="lecturer_id"
                                label={__('edu.lecturer')}
                                value={data.lecturer_id}
                                onChange={(e) => setData('lecturer_id', e.target.value)}
                                errors={errors.lecturer_id}
                                required
                            >
                                <option value="">{__('edu.select_lecturer')}</option>
                                {lecturers.map((lecturer) => (
                                    <option key={lecturer.id} value={lecturer.id}>
                                        {lecturer.user?.name}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="title"
                                type="text"
                                label={__('edu.course_title')}
                                value={data.title}
                                onChange={(e) => setData('title', e.target.value)}
                                errors={errors.title}
                                placeholder={__('edu.course_title_placeholder')}
                                required
                            />
                        </div>

                        <div className="md:col-span-2">
                            <TextareaWithLabel
                                id="short_description"
                                label={__('edu.short_description')}
                                rows="2"
                                value={data.short_description}
                                onChange={(e) => setData('short_description', e.target.value)}
                                errors={errors.short_description}
                                placeholder={__('edu.short_description_placeholder')}
                                required
                            />
                        </div>

                        <div className="md:col-span-2">
                            <TextareaWithLabel
                                id="description"
                                label={__('edu.detailed_description')}
                                rows="4"
                                value={data.description}
                                onChange={(e) => setData('description', e.target.value)}
                                errors={errors.description}
                                placeholder={__('edu.detailed_description_placeholder')}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="category"
                                type="text"
                                label={__('edu.category')}
                                value={data.category}
                                onChange={(e) => setData('category', e.target.value)}
                                errors={errors.category}
                                placeholder={__('edu.category_placeholder')}
                            />
                        </div>

                        <div>
                            <SelectWithLabel
                                id="level"
                                label={__('edu.level')}
                                value={data.level}
                                onChange={(e) => setData('level', e.target.value)}
                                errors={errors.level}
                                required
                            >
                                <option value="beginner">{__('edu.beginner')}</option>
                                <option value="intermediate">{__('edu.intermediate')}</option>
                                <option value="advanced">{__('edu.advanced')}</option>
                            </SelectWithLabel>
                        </div>

                        <div className="md:col-span-2">
                            <label className="text-sm block mb-1 font-medium text-gray-700">{__('edu.course_thumbnail')}</label>
                            <div className="mt-1 flex items-center">
                                <input
                                    type="file"
                                    id="thumbnail"
                                    ref={imageInputRef}
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleImageChange}
                                />

                                <div className="flex items-center space-x-4">
                                    {imagePreview ? (
                                        <div className="relative">
                                            <img
                                                src={imagePreview}
                                                alt="Course Preview"
                                                className="h-32 w-32 object-cover rounded-md border border-gray-200"
                                            />
                                            <button
                                                type="button"
                                                onClick={clearImage}
                                                className="absolute -top-2 -right-2 text-red-500 bg-white rounded-full"
                                            >
                                                <XCircle className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div
                                            className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                                            onClick={() => imageInputRef.current?.click()}
                                        >
                                            <Upload className="h-8 w-8 text-gray-400" />
                                            <span className="mt-2 text-sm text-gray-500">{__('edu.upload_image')}</span>
                                        </div>
                                    )}

                                    <div className="flex flex-col">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => imageInputRef.current?.click()}
                                            className="mb-2"
                                        >
                                            {imagePreview ? __('edu.change_image') : __('edu.browse_image')}
                                        </Button>
                                        <p className="text-xs text-gray-500">
                                            {__('edu.supported_formats')}
                                        </p>
                                        {errors.thumbnail && (
                                            <p className="text-sm text-red-600 mt-1">{errors.thumbnail}</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.course_details')}
                            </h2>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="duration_hours"
                                type="number"
                                min="0"
                                label={__('edu.duration_hours')}
                                value={data.duration_hours}
                                onChange={(e) => setData('duration_hours', e.target.value)}
                                errors={errors.duration_hours}
                                icon={<Clock className="h-4 w-4 text-gray-400" />}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="total_lessons"
                                type="number"
                                min="0"
                                label={__('edu.total_lessons')}
                                value={data.total_lessons}
                                onChange={(e) => setData('total_lessons', e.target.value)}
                                errors={errors.total_lessons}
                                icon={<BookOpen className="h-4 w-4 text-gray-400" />}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="price"
                                type="number"
                                step="0.01"
                                min="0"
                                label={__('edu.price')}
                                value={data.price}
                                onChange={(e) => setData('price', e.target.value)}
                                errors={errors.price}
                                icon={<DollarSign className="h-4 w-4 text-gray-400" />}
                                required
                                disabled={data.is_free}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="original_price"
                                type="number"
                                step="0.01"
                                min="0"
                                label={__('edu.original_price')}
                                value={data.original_price}
                                onChange={(e) => setData('original_price', e.target.value)}
                                errors={errors.original_price}
                                disabled={data.is_free}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="max_students"
                                type="number"
                                min="1"
                                label={__('edu.max_students')}
                                value={data.max_students}
                                onChange={(e) => setData('max_students', e.target.value)}
                                errors={errors.max_students}
                                icon={<Users className="h-4 w-4 text-gray-400" />}
                            />
                        </div>

                        <div>
                            <SelectWithLabel
                                id="status"
                                label={__('edu.status')}
                                value={data.status}
                                onChange={(e) => setData('status', e.target.value)}
                                errors={errors.status}
                                required
                            >
                                <option value="pending_approval">{__('edu.pending_approval')}</option>
                                <option value="active">{__('edu.active')}</option>
                                <option value="inactive">{__('edu.inactive')}</option>
                                <option value="suspended">{__('edu.suspended')}</option>
                            </SelectWithLabel>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="start_date"
                                type="date"
                                label={__('edu.start_date')}
                                value={data.start_date}
                                onChange={(e) => setData('start_date', e.target.value)}
                                errors={errors.start_date}
                                icon={<Calendar className="h-4 w-4 text-gray-400" />}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="end_date"
                                type="date"
                                label={__('edu.end_date')}
                                value={data.end_date}
                                onChange={(e) => setData('end_date', e.target.value)}
                                errors={errors.end_date}
                                icon={<Calendar className="h-4 w-4 text-gray-400" />}
                            />
                        </div>

                        <div className="md:col-span-2 mt-4 border-t border-gray-200 pt-4">
                            <div className="flex flex-col md:flex-row gap-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_featured"
                                        checked={data.is_featured}
                                        onChange={(e) => setData('is_featured', e.target.checked)}
                                    />
                                    <label htmlFor="is_featured" className="text-sm font-medium text-gray-700">
                                        {__('edu.featured_course')}
                                    </label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_free"
                                        checked={data.is_free}
                                        onChange={(e) => setData('is_free', e.target.checked)}
                                    />
                                    <label htmlFor="is_free" className="text-sm font-medium text-gray-700">
                                        {__('edu.free_course')}
                                    </label>
                                </div>
                            </div>
                            {(errors.is_featured || errors.is_free) && (
                                <p className="text-sm text-red-600 mt-1">
                                    {errors.is_featured || errors.is_free}
                                </p>
                            )}
                        </div>

                        {/* Curriculum Section */}
                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.curriculum')}
                            </h2>
                            <div className="space-y-3">
                                {curriculum.map((item, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`curriculum_${index}`}
                                            type="text"
                                            label={`${__('edu.curriculum_item')} ${index + 1}`}
                                            value={item}
                                            onChange={(e) => updateCurriculumItem(index, e.target.value)}
                                            placeholder={__('edu.curriculum_item_placeholder')}
                                            className="flex-1"
                                        />
                                        {curriculum.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeCurriculumItem(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addCurriculumItem}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_curriculum_item')}
                                </Button>
                            </div>
                        </div>

                        {/* Requirements Section */}
                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.requirements')}
                            </h2>
                            <div className="space-y-3">
                                {requirements.map((requirement, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`requirement_${index}`}
                                            type="text"
                                            label={`${__('edu.requirement')} ${index + 1}`}
                                            value={requirement}
                                            onChange={(e) => updateRequirement(index, e.target.value)}
                                            placeholder={__('edu.requirement_placeholder')}
                                            className="flex-1"
                                        />
                                        {requirements.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeRequirement(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addRequirement}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_requirement')}
                                </Button>
                            </div>
                        </div>

                        {/* Learning Outcomes Section */}
                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.learning_outcomes')}
                            </h2>
                            <div className="space-y-3">
                                {outcomes.map((outcome, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`outcome_${index}`}
                                            type="text"
                                            label={`${__('edu.outcome')} ${index + 1}`}
                                            value={outcome}
                                            onChange={(e) => updateOutcome(index, e.target.value)}
                                            placeholder={__('edu.outcome_placeholder')}
                                            className="flex-1"
                                        />
                                        {outcomes.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeOutcome(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addOutcome}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_outcome')}
                                </Button>
                            </div>
                        </div>

                        {/* Tags Section */}
                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.tags')}
                            </h2>
                            <div className="space-y-3">
                                {tags.map((tag, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`tag_${index}`}
                                            type="text"
                                            label={`${__('edu.tag')} ${index + 1}`}
                                            value={tag}
                                            onChange={(e) => updateTag(index, e.target.value)}
                                            placeholder={__('edu.tag_placeholder')}
                                            className="flex-1"
                                        />
                                        {tags.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeTag(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addTag}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_tag')}
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                        <Link
                            href={route('superadmin.edu.courses.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('edu.cancel')}
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {processing ? __('edu.creating') : __('edu.create_course')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
