import React, { useState, useRef, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload, Plus, Trash2, Clock, Users, BookOpen, Star, Calendar, DollarSign } from 'lucide-react';

export default function Edit({ course = {}, lecturers = [] }) {
    const { flash } = usePage().props;
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(course.thumbnail_url || null);
    const [curriculum, setCurriculum] = useState(course.curriculum || ['']);
    const [requirements, setRequirements] = useState(course.requirements || ['']);
    const [outcomes, setOutcomes] = useState(course.outcomes || ['']);
    const [tags, setTags] = useState(course.tags || ['']);
    const { addAlert } = useToast();

    const { data, setData, post, processing, errors } = useForm({
        title: course.title || '',
        slug: course.slug || '',
        description: course.description || '',
        short_description: course.short_description || '',
        lecturer_id: course.lecturer_id || '',
        category: course.category || '',
        level: course.level || 'beginner',
        duration_hours: course.duration_hours || '',
        total_lessons: course.total_lessons || '',
        price: course.price || '',
        original_price: course.original_price || '',
        max_students: course.max_students || '',
        curriculum: course.curriculum || [],
        requirements: course.requirements || [],
        outcomes: course.outcomes || [],
        tags: course.tags || [],
        is_featured: course.is_featured || false,
        is_free: course.is_free || false,
        start_date: course.start_date ? course.start_date.split('T')[0] : '',
        end_date: course.end_date ? course.end_date.split('T')[0] : '',
        status: course.status || 'pending',
        thumbnail: null,
        _method: 'PUT',
    });

    useEffect(() => {
        if (flash.error) {
            addAlert('error', flash.error);
        }
        if (flash.success) {
            addAlert('success', flash.success);
        }
    }, [flash]);

    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            const firstError = Object.values(errors)[0];
            addAlert('error', firstError);
        }
    }, [errors]);

    useEffect(() => {
        setData('curriculum', curriculum.filter(item => item.trim() !== ''));
    }, [curriculum]);

    useEffect(() => {
        setData('requirements', requirements.filter(item => item.trim() !== ''));
    }, [requirements]);

    useEffect(() => {
        setData('outcomes', outcomes.filter(item => item.trim() !== ''));
    }, [outcomes]);

    useEffect(() => {
        setData('tags', tags.filter(item => item.trim() !== ''));
    }, [tags]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.edu.courses.update', course.id), {
            onSuccess: (response) => {
                setImagePreview(course.thumbnail_url);
                addAlert('success', __('edu.messages.course_updated'));
            },
            onError: (errors) => {
                console.log('Validation errors:', errors);
                if (Object.keys(errors).length > 0) {
                    addAlert('error', Object.values(errors)[0]);
                }
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('thumbnail', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('thumbnail', null);
        setImagePreview(null);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    const addCurriculumItem = () => {
        setCurriculum([...curriculum, '']);
    };

    const removeCurriculumItem = (index) => {
        const newCurriculum = curriculum.filter((_, i) => i !== index);
        setCurriculum(newCurriculum);
    };

    const updateCurriculumItem = (index, value) => {
        const newCurriculum = [...curriculum];
        newCurriculum[index] = value;
        setCurriculum(newCurriculum);
    };

    const addRequirement = () => {
        setRequirements([...requirements, '']);
    };

    const removeRequirement = (index) => {
        const newRequirements = requirements.filter((_, i) => i !== index);
        setRequirements(newRequirements);
    };

    const updateRequirement = (index, value) => {
        const newRequirements = [...requirements];
        newRequirements[index] = value;
        setRequirements(newRequirements);
    };

    const addOutcome = () => {
        setOutcomes([...outcomes, '']);
    };

    const removeOutcome = (index) => {
        const newOutcomes = outcomes.filter((_, i) => i !== index);
        setOutcomes(newOutcomes);
    };

    const updateOutcome = (index, value) => {
        const newOutcomes = [...outcomes];
        newOutcomes[index] = value;
        setOutcomes(newOutcomes);
    };

    const addTag = () => {
        setTags([...tags, '']);
    };

    const removeTag = (index) => {
        const newTags = tags.filter((_, i) => i !== index);
        setTags(newTags);
    };

    const updateTag = (index, value) => {
        const newTags = [...tags];
        newTags[index] = value;
        setTags(newTags);
    };

    return (
        <SuperAdminLayout>
            <Head title={__('edu.edit_course') + ': ' + course.title} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">
                            {__('edu.edit_course')}: {course.title}
                        </h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.edu.courses.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('edu.back_to_courses')}
                            </Link>
                            <Link
                                href={route('superadmin.edu.courses.show', course.id)}
                                className="px-4 py-2 border border-blue-300 bg-blue-50 rounded-md text-sm text-blue-700 hover:bg-blue-100"
                            >
                                <i className="fas fa-eye mr-2"></i>
                                {__('edu.view_course')}
                            </Link>
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="p-6 space-y-8">
                        {/* Basic Information */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <BookOpen className="w-5 h-5 mr-2" />
                                {__('edu.basic_information')}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <TextInputWithLabel
                                        id="title"
                                        type="text"
                                        label={__('edu.course_title')}
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder={__('edu.course_title_placeholder')}
                                        errors={errors.title}
                                        required
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="slug"
                                        type="text"
                                        label={__('edu.course_slug')}
                                        value={data.slug}
                                        onChange={(e) => setData('slug', e.target.value)}
                                        errors={errors.slug}
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <TextareaWithLabel
                                        id="short_description"
                                        label={__('edu.short_description')}
                                        value={data.short_description}
                                        onChange={(e) => setData('short_description', e.target.value)}
                                        placeholder={__('edu.short_description_placeholder')}
                                        errors={errors.short_description}
                                        rows={3}
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <TextareaWithLabel
                                        id="description"
                                        label={__('edu.course_description')}
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder={__('edu.course_description_placeholder')}
                                        errors={errors.description}
                                        rows={6}
                                        required
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Course Details */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <Users className="w-5 h-5 mr-2" />
                                Chi tiết khóa học
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <SelectWithLabel
                                        id="lecturer_id"
                                        label={__('edu.course_lecturer')}
                                        value={data.lecturer_id}
                                        onChange={(e) => setData('lecturer_id', e.target.value)}
                                        errors={errors.lecturer_id}
                                        required
                                    >
                                        <option value="">{__('edu.select_lecturer')}</option>
                                        {lecturers.map((lecturer) => (
                                            <option key={lecturer.id} value={lecturer.id}>
                                                {lecturer.user?.name} - {lecturer.title}
                                            </option>
                                        ))}
                                    </SelectWithLabel>
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="category"
                                        type="text"
                                        label={__('edu.category')}
                                        value={data.category}
                                        onChange={(e) => setData('category', e.target.value)}
                                        placeholder={__('edu.category_placeholder')}
                                        errors={errors.category}
                                        required
                                    />
                                </div>

                                <div>
                                    <SelectWithLabel
                                        id="level"
                                        label={__('edu.level')}
                                        value={data.level}
                                        onChange={(e) => setData('level', e.target.value)}
                                        errors={errors.level}
                                        required
                                    >
                                        <option value="beginner">{__('edu.beginner')}</option>
                                        <option value="intermediate">{__('edu.intermediate')}</option>
                                        <option value="advanced">{__('edu.advanced')}</option>
                                        <option value="expert">{__('edu.expert')}</option>
                                    </SelectWithLabel>
                                </div>

                                <div>
                                    <SelectWithLabel
                                        id="status"
                                        label={__('edu.status')}
                                        value={data.status}
                                        onChange={(e) => setData('status', e.target.value)}
                                        errors={errors.status}
                                        required
                                    >
                                        <option value="pending">Pending</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="suspended">Suspended</option>
                                    </SelectWithLabel>
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="duration_hours"
                                        type="number"
                                        label={__('edu.duration_hours')}
                                        value={data.duration_hours}
                                        onChange={(e) => setData('duration_hours', e.target.value)}
                                        errors={errors.duration_hours}
                                        min="0"
                                        step="0.5"
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="total_lessons"
                                        type="number"
                                        label={__('edu.total_lessons')}
                                        value={data.total_lessons}
                                        onChange={(e) => setData('total_lessons', e.target.value)}
                                        errors={errors.total_lessons}
                                        min="0"
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="max_students"
                                        type="number"
                                        label={__('edu.max_students')}
                                        value={data.max_students}
                                        onChange={(e) => setData('max_students', e.target.value)}
                                        errors={errors.max_students}
                                        min="1"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Pricing & Dates */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <DollarSign className="w-5 h-5 mr-2" />
                                Giá & Lịch trình
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <TextInputWithLabel
                                        id="price"
                                        type="number"
                                        label={__('edu.price')}
                                        value={data.price}
                                        onChange={(e) => setData('price', e.target.value)}
                                        errors={errors.price}
                                        min="0"
                                        step="0.01"
                                        required
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="original_price"
                                        type="number"
                                        label={__('edu.original_price')}
                                        value={data.original_price}
                                        onChange={(e) => setData('original_price', e.target.value)}
                                        errors={errors.original_price}
                                        min="0"
                                        step="0.01"
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="start_date"
                                        type="date"
                                        label={__('edu.start_date')}
                                        value={data.start_date}
                                        onChange={(e) => setData('start_date', e.target.value)}
                                        errors={errors.start_date}
                                    />
                                </div>

                                <div>
                                    <TextInputWithLabel
                                        id="end_date"
                                        type="date"
                                        label={__('edu.end_date')}
                                        value={data.end_date}
                                        onChange={(e) => setData('end_date', e.target.value)}
                                        errors={errors.end_date}
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <div className="flex items-center space-x-6">
                                        <label className="flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={data.is_featured}
                                                onChange={(e) => setData('is_featured', e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-600">{__('edu.is_featured')}</span>
                                        </label>

                                        <label className="flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={data.is_free}
                                                onChange={(e) => setData('is_free', e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-600">{__('edu.is_free')}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Thumbnail Upload */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <Upload className="w-5 h-5 mr-2" />
                                {__('edu.course_thumbnail')}
                            </h2>
                            <div className="space-y-4">
                                {imagePreview && (
                                    <div className="relative inline-block">
                                        <img
                                            src={imagePreview}
                                            alt="Course thumbnail preview"
                                            className="w-32 h-24 object-cover rounded-lg border border-gray-300"
                                        />
                                        <button
                                            type="button"
                                            onClick={clearImage}
                                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                        >
                                            <XCircle className="w-4 h-4" />
                                        </button>
                                    </div>
                                )}

                                <div>
                                    <input
                                        ref={imageInputRef}
                                        type="file"
                                        accept="image/*"
                                        onChange={handleImageChange}
                                        className="hidden"
                                        id="thumbnail"
                                    />
                                    <label
                                        htmlFor="thumbnail"
                                        className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        <Upload className="w-4 h-4 mr-2" />
                                        {imagePreview ? __('edu.change_image') : __('edu.upload_image')}
                                    </label>
                                    <p className="mt-1 text-xs text-gray-500">{__('edu.supported_formats')}</p>
                                </div>
                                {errors.thumbnail && (
                                    <p className="text-red-600 text-sm">{errors.thumbnail}</p>
                                )}
                            </div>
                        </div>

                        {/* Curriculum */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <BookOpen className="w-5 h-5 mr-2" />
                                {__('edu.curriculum')}
                            </h2>
                            <div className="space-y-3">
                                {curriculum.map((item, index) => (
                                    <div key={index} className="flex items-center space-x-3">
                                        <div className="flex-1">
                                            <TextInputWithLabel
                                                id={`curriculum_${index}`}
                                                type="text"
                                                value={item}
                                                onChange={(e) => updateCurriculumItem(index, e.target.value)}
                                                placeholder={__('edu.curriculum_placeholder')}
                                                hideLabel
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => removeCurriculumItem(index)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addCurriculumItem}
                                    className="text-blue-600 hover:text-blue-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Thêm nội dung khóa học
                                </Button>
                            </div>
                        </div>

                        {/* Requirements */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('edu.requirements')}</h2>
                            <div className="space-y-3">
                                {requirements.map((requirement, index) => (
                                    <div key={index} className="flex items-center space-x-3">
                                        <div className="flex-1">
                                            <TextInputWithLabel
                                                id={`requirement_${index}`}
                                                type="text"
                                                value={requirement}
                                                onChange={(e) => updateRequirement(index, e.target.value)}
                                                placeholder={__('edu.requirements_placeholder')}
                                                hideLabel
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => removeRequirement(index)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addRequirement}
                                    className="text-blue-600 hover:text-blue-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Thêm yêu cầu
                                </Button>
                            </div>
                        </div>

                        {/* Learning Outcomes */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('edu.outcomes')}</h2>
                            <div className="space-y-3">
                                {outcomes.map((outcome, index) => (
                                    <div key={index} className="flex items-center space-x-3">
                                        <div className="flex-1">
                                            <TextInputWithLabel
                                                id={`outcome_${index}`}
                                                type="text"
                                                value={outcome}
                                                onChange={(e) => updateOutcome(index, e.target.value)}
                                                placeholder={__('edu.outcomes_placeholder')}
                                                hideLabel
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => removeOutcome(index)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addOutcome}
                                    className="text-blue-600 hover:text-blue-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Thêm kết quả học tập
                                </Button>
                            </div>
                        </div>

                        {/* Tags */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('edu.tags')}</h2>
                            <div className="space-y-3">
                                {tags.map((tag, index) => (
                                    <div key={index} className="flex items-center space-x-3">
                                        <div className="flex-1">
                                            <TextInputWithLabel
                                                id={`tag_${index}`}
                                                type="text"
                                                value={tag}
                                                onChange={(e) => updateTag(index, e.target.value)}
                                                placeholder={__('edu.tag_placeholder')}
                                                hideLabel
                                            />
                                        </div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => removeTag(index)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addTag}
                                    className="text-blue-600 hover:text-blue-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    {__('edu.add_tag')}
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Submit Section */}
                    <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                        <Link
                            href={route('superadmin.edu.courses.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('edu.cancel')}
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing ? __('edu.updating') : __('edu.save_changes')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
