import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Search, Filter, BookOpen, GraduationCap, Clock, Users, Star } from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import ImageWithFallback from '@/Components/ImageWithFallback';
import PrimaryButton from '@/Components/PrimaryButton';
import Pagination from '@/Components/Pagination';
import StatusBadge from '@/Components/ui/StatusBadge';
import axios from 'axios';
import Loading from '@/Components/Loading';

export default function Index({
    courses: initialCourses = { data: [], links: [], from: 0, to: 0, total: 0 },
    lecturers = [],
    categories = [],
    levels = [],
    filters = {}
}) {
    const { processing, flash, csrf_token } = usePage().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const { addAlert } = useToast();
    const [courses, setCourses] = useState(initialCourses);
    const [selectedCategory, setSelectedCategory] = useState(filters.category || '');
    const [selectedLevel, setSelectedLevel] = useState(filters.level || '');
    const [selectedLecturer, setSelectedLecturer] = useState(filters.lecturer_id || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedFeatured, setSelectedFeatured] = useState(filters.is_featured ?? '');
    const [selectedFree, setSelectedFree] = useState(filters.is_free ?? '');

    useEffect(() => {
        if (flash.error) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', flash.error);
        }
        if (flash.success) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', flash.success);
        }
    }, [flash]);

    const handleSearch = (e) => {
        e.preventDefault();
        fetchCourses({
            ...filters,
            search: searchQuery,
            category: selectedCategory,
            level: selectedLevel,
            lecturer_id: selectedLecturer,
            status: selectedStatus,
            is_featured: selectedFeatured,
            is_free: selectedFree,
        }, true);
    };

    const handleCategoryFilter = (e) => {
        const category = e.target.value;
        setSelectedCategory(category);
        fetchCourses({
            ...filters,
            category,
            level: selectedLevel,
            lecturer_id: selectedLecturer,
            status: selectedStatus,
            is_featured: selectedFeatured,
            is_free: selectedFree,
        });
    };

    const handleLevelFilter = (e) => {
        const level = e.target.value;
        setSelectedLevel(level);
        fetchCourses({
            ...filters,
            category: selectedCategory,
            level,
            lecturer_id: selectedLecturer,
            status: selectedStatus,
            is_featured: selectedFeatured,
            is_free: selectedFree,
        });
    };

    const handleLecturerFilter = (e) => {
        const lecturer_id = e.target.value;
        setSelectedLecturer(lecturer_id);
        fetchCourses({
            ...filters,
            category: selectedCategory,
            level: selectedLevel,
            lecturer_id,
            status: selectedStatus,
            is_featured: selectedFeatured,
            is_free: selectedFree,
        });
    };

    const handleStatusFilter = (status) => {
        setSelectedStatus(status);
        fetchCourses({
            ...filters,
            category: selectedCategory,
            level: selectedLevel,
            lecturer_id: selectedLecturer,
            status,
            is_featured: selectedFeatured,
            is_free: selectedFree,
        });
    };

    const handleFeaturedFilter = (isFeatured) => {
        setSelectedFeatured(isFeatured);
        fetchCourses({
            ...filters,
            category: selectedCategory,
            level: selectedLevel,
            lecturer_id: selectedLecturer,
            status: selectedStatus,
            is_featured: isFeatured,
            is_free: selectedFree,
        });
    };

    const handleFreeFilter = (isFree) => {
        setSelectedFree(isFree);
        fetchCourses({
            ...filters,
            category: selectedCategory,
            level: selectedLevel,
            lecturer_id: selectedLecturer,
            status: selectedStatus,
            is_featured: selectedFeatured,
            is_free: isFree,
        });
    };

    const handleSort = (field, direction) => {
        applyFilters({ sort: field, direction });
    };

    const applyFilters = (newFilters) => {
        router.get(route('superadmin.edu.courses.index'),
            { ...filters, ...newFilters },
            { preserveState: true, preserveScroll: true }
        );
    };

    const confirmDelete = (courseId) => {
        setIsDeleting(courseId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteCourse = () => {
        if (!isDeleting) return;

        setIsProcessing(true);

        axios.delete(route('superadmin.edu.courses.destroy', isDeleting), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {
            setCourses(prevCourses => {
                const updatedData = prevCourses.data.filter(course => course.id !== isDeleting);
                const updatedCourses = {
                    ...prevCourses,
                    data: updatedData,
                    total: prevCourses.total - 1
                };

                if (updatedCourses.to) {
                    updatedCourses.to = Math.max(updatedCourses.to - 1, updatedCourses.from);
                }

                return updatedCourses;
            });

            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', response.data.message || __('edu.course_deleted_successfully'));
        })
        .catch(error => {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', error.response?.data?.message || __('edu.delete_failed'));
        });
    };

    const fetchCourses = (params, updateUrl = false) => {
        setIsProcessing(true);

        if (updateUrl) {
            router.get(route('superadmin.edu.courses.index'), params, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => setIsProcessing(false),
                onError: () => {
                    setIsProcessing(false);
                    addAlert('error', __('edu.fetch_failed'));
                }
            });
        } else {
            axios.get(route('superadmin.edu.courses.api'), {
                params,
            })
            .then(res => {
                if (res.data && res.data.data) {
                    setCourses(res.data.data);
                }
                setIsProcessing(false);
            })
            .catch(() => {
                setIsProcessing(false);
                addAlert('error', __('edu.fetch_failed'));
            });
        }
    };

    const getLevelColor = (level) => {
        const colors = {
            'beginner': 'bg-green-100 text-green-800',
            'intermediate': 'bg-yellow-100 text-yellow-800',
            'advanced': 'bg-red-100 text-red-800'
        };
        return colors[level] || 'bg-gray-100 text-gray-800';
    };

    const getLevelLabel = (level) => {
        const labels = {
            'beginner': __('edu.beginner'),
            'intermediate': __('edu.intermediate'),
            'advanced': __('edu.advanced')
        };
        return labels[level] || level;
    };

    const getStatusColor = (status) => {
        const colors = {
            'pending_approval': 'bg-yellow-100 text-yellow-800',
            'active': 'bg-green-100 text-green-800',
            'inactive': 'bg-gray-100 text-gray-800',
            'suspended': 'bg-red-100 text-red-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getStatusLabel = (status) => {
        const labels = {
            'pending_approval': __('edu.pending_approval'),
            'active': __('edu.active'),
            'inactive': __('edu.inactive'),
            'suspended': __('edu.suspended')
        };
        return labels[status] || status;
    };

    const columns = [
        {
            field: 'title',
            label: __('edu.course_title'),
            sortable: true,
            render: (course) => (
                <div className="flex items-center space-x-3">
                    <ImageWithFallback
                        src={course.thumbnail_url}
                        alt={course.title}
                        fallbackText={course.title.charAt(0).toUpperCase()}
                        width="w-12"
                        height="h-12"
                        className="object-cover"
                    />
                    <div>
                        <div className="font-medium text-gray-900">{course.title}</div>
                        <div className="text-xs text-gray-500">
                            {course.category || __('edu.no_category')}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'lecturer_name',
            label: __('edu.lecturer'),
            render: (course) => (
                <div className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900">
                        {course.lecturer?.user?.name || __('edu.no_lecturer')}
                    </span>
                </div>
            )
        },
        {
            field: 'level',
            label: __('edu.level'),
            render: (course) => (
                <StatusBadge
                    text={getLevelLabel(course.level)}
                    color={getLevelColor(course.level)}
                />
            )
        },
        {
            field: 'price',
            label: __('edu.price'),
            sortable: true,
            render: (course) => (
                <div>
                    {course.is_free ? (
                        <span className="font-medium text-green-600">{__('edu.free')}</span>
                    ) : (
                        <div>
                            <div className="font-medium text-gray-900">{formatCurrency(course.price)}</div>
                            {course.original_price && course.original_price > course.price && (
                                <div className="text-xs text-gray-500 line-through">
                                    {formatCurrency(course.original_price)}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'enrolled_students',
            label: __('edu.students'),
            sortable: true,
            render: (course) => (
                <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-900">
                        {course.enrolled_students || 0}
                        {course.max_students && `/${course.max_students}`}
                    </span>
                </div>
            )
        },
        {
            field: 'status',
            label: __('edu.status'),
            render: (course) => (
                <div className="flex items-center">
                    <StatusBadge
                        text={getStatusLabel(course.status)}
                        color={getStatusColor(course.status)}
                    />
                    {course.is_featured && (
                        <StatusBadge
                            text={__('edu.featured')}
                            color="bg-purple-100 text-purple-800"
                            className="ml-2"
                        />
                    )}
                </div>
            )
        },
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('edu.courses')} />

            <div className="bg-white rounded-lg shadow-md relative">
                {isProcessing && <Loading overlay text={__('edu.loading')} />}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.course_management')}</h1>
                        <PrimaryButton
                            href={route('superadmin.edu.courses.create')}
                            className="flex items-center gap-2"
                        >
                            <Plus className="w-4 h-4" />
                            {__('edu.add_course')}
                        </PrimaryButton>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-4">
                            <form onSubmit={handleSearch} className="w-full">
                                <div className="relative">
                                    <TextInputWithLabel
                                        id="search"
                                        type="text"
                                        label={__('edu.search_courses')}
                                        placeholder={__('edu.search_courses_placeholder')}
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                    <div className="absolute top-9 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <button type="submit" className="hidden">{__('edu.search')}</button>
                                </div>
                            </form>

                            <SelectWithLabel
                                id="category_filter"
                                label={__('edu.category')}
                                value={selectedCategory}
                                onChange={handleCategoryFilter}
                                className="w-full"
                            >
                                <option value="">{__('edu.all_categories')}</option>
                                {categories.map((category) => (
                                    <option key={category} value={category}>
                                        {category}
                                    </option>
                                ))}
                            </SelectWithLabel>

                            <SelectWithLabel
                                id="level_filter"
                                label={__('edu.level')}
                                value={selectedLevel}
                                onChange={handleLevelFilter}
                                className="w-full"
                            >
                                <option value="">{__('edu.all_levels')}</option>
                                {levels.map((level) => (
                                    <option key={level} value={level}>
                                        {getLevelLabel(level)}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div className="space-y-4">
                            <SelectWithLabel
                                id="lecturer_filter"
                                label={__('edu.lecturer')}
                                value={selectedLecturer}
                                onChange={handleLecturerFilter}
                                className="w-full"
                            >
                                <option value="">{__('edu.all_lecturers')}</option>
                                {lecturers.map((lecturer) => (
                                    <option key={lecturer.id} value={lecturer.id}>
                                        {lecturer.user?.name}
                                    </option>
                                ))}
                            </SelectWithLabel>

                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('edu.status')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleStatusFilter('')}
                                        variant={selectedStatus === '' ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('edu.all_status')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('active')}
                                        variant={selectedStatus === 'active' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === 'active' ? "bg-green-600" : ""}
                                    >
                                        {__('edu.active')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('pending_approval')}
                                        variant={selectedStatus === 'pending_approval' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === 'pending_approval' ? "bg-yellow-600" : ""}
                                    >
                                        {__('edu.pending')}
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('edu.featured')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleFeaturedFilter('')}
                                        variant={selectedFeatured === '' ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('edu.all_courses')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFeaturedFilter('1')}
                                        variant={selectedFeatured === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedFeatured === '1' ? "bg-purple-600" : ""}
                                    >
                                        {__('edu.featured')}
                                    </Button>
                                </div>
                            </div>

                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('edu.price_type')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleFreeFilter('')}
                                        variant={selectedFree === '' ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('edu.all_courses')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFreeFilter('1')}
                                        variant={selectedFree === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedFree === '1' ? "bg-green-600" : ""}
                                    >
                                        {__('edu.free')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFreeFilter('0')}
                                        variant={selectedFree === '0' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedFree === '0' ? "bg-blue-600" : ""}
                                    >
                                        {__('edu.paid')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <DataTable
                    data={courses.data}
                    columns={columns}
                    onSort={handleSort}
                    sortField={filters.sort || 'created_at'}
                    sortDirection={filters.direction || 'desc'}
                    emptyStateMessage={__('edu.no_courses_found')}
                    primaryKey="id"
                    viewRoute="superadmin.edu.courses.show"
                    editRoute="superadmin.edu.courses.edit"
                    deleteCallback={confirmDelete}
                    cancelDeletion={cancelDelete}
                    loading={isProcessing}
                />

                <ConfirmDeleteModal
                    isOpen={isDeleting !== null}
                    onClose={cancelDelete}
                    onConfirm={deleteCourse}
                    title={__('edu.delete_course')}
                    message={__('edu.delete_course_confirmation')}
                    isProcessing={isProcessing}
                />

                {courses.links && courses.links.length > 3 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-700">
                                {__('edu.showing')} {courses.from} {__('edu.to')} {courses.to} {__('edu.of')} {courses.total} {__('edu.courses').toLowerCase()}
                            </p>
                            <Pagination
                                links={courses.links}
                                preserveState={true}
                                preserveScroll={true}
                            />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
