import React, { useState } from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';
import { formatDateTime, formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import {
    Pencil,
    Trash2,
    Star,
    Users,
    BookOpen,
    Calendar,
    Clock,
    DollarSign,
    Eye,
    Tag,
    Award,
    CheckCircle,
    Target,
    List,
    User
} from 'lucide-react';
import ImageWithFallback from '@/Components/ImageWithFallback';

export default function Show({ course = {} }) {
    const [isDeleting, setIsDeleting] = useState(false);
    const { addAlert } = useToast();

    const handleDelete = () => {
        router.delete(route('superadmin.edu.courses.destroy', course.id), {
            onSuccess: () => {
                addAlert('success', __('edu.messages.course_deleted'));
            },
            onError: (errors) => {
                addAlert('error', errors.message || __('edu.delete_failed'));
                setIsDeleting(false);
            }
        });
    };

    const cancelDelete = () => {
        setIsDeleting(false);
    };

    const getStatusColor = (status) => {
        const colors = {
            'active': 'bg-green-100 text-green-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'suspended': 'bg-red-100 text-red-800',
            'inactive': 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getStatusLabel = (status) => {
        const labels = {
            'active': __('edu.active'),
            'pending': 'Pending',
            'suspended': __('edu.suspended'),
            'inactive': __('edu.inactive')
        };
        return labels[status] || status;
    };

    const getLevelColor = (level) => {
        const colors = {
            'beginner': 'bg-green-100 text-green-800',
            'intermediate': 'bg-blue-100 text-blue-800',
            'advanced': 'bg-purple-100 text-purple-800',
            'expert': 'bg-red-100 text-red-800'
        };
        return colors[level] || 'bg-gray-100 text-gray-800';
    };

    const getLevelLabel = (level) => {
        const labels = {
            'beginner': __('edu.beginner'),
            'intermediate': __('edu.intermediate'),
            'advanced': __('edu.advanced'),
            'expert': __('edu.expert')
        };
        return labels[level] || level;
    };

    const formatPrice = (price) => {
        if (course.is_free) return 'Miễn phí';
        if (!price) return 'N/A';
        return formatCurrency(price);
    };

    return (
        <SuperAdminLayout>
            <Head title={__('edu.course_details') + ': ' + course.title} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.course_details')}</h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.edu.courses.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('edu.back_to_courses')}
                            </Link>
                            <Link
                                href={route('superadmin.edu.courses.edit', course.id)}
                                className="px-4 py-2 border border-blue-300 bg-blue-50 rounded-md text-sm text-blue-700 hover:bg-blue-100"
                            >
                                <Pencil className="w-4 h-4 inline-block mr-1" />
                                {__('edu.edit_course')}
                            </Link>
                            {!isDeleting ? (
                                <Button
                                    variant="destructive"
                                    onClick={() => setIsDeleting(true)}
                                >
                                    <Trash2 className="w-4 h-4 mr-1" />
                                    {__('common.delete')}
                                </Button>
                            ) : (
                                <div className="flex space-x-2">
                                    <Button
                                        variant="destructive"
                                        onClick={handleDelete}
                                    >
                                        {__('edu.confirm')}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={cancelDelete}
                                    >
                                        {__('edu.cancel')}
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Course Image and Basic Info */}
                        <div className="lg:col-span-1">
                            <div className="flex flex-col space-y-6">
                                {/* Course Thumbnail */}
                                <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                                    <ImageWithFallback
                                        src={course.thumbnail_url}
                                        alt={course.title}
                                        fallbackText={course.title?.charAt(0).toUpperCase() || 'C'}
                                        width="w-full"
                                        height="h-48"
                                        rounded="rounded-lg"
                                        className="mb-4"
                                        textSize="text-4xl"
                                    />

                                    <h2 className="text-xl font-bold text-gray-900 mb-2 break-words">
                                        {course.title}
                                    </h2>

                                    {course.short_description && (
                                        <p className="text-gray-600 mb-4 break-words">{course.short_description}</p>
                                    )}

                                    <div className="space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-500 text-sm">{__('edu.status')}:</span>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(course.status)}`}>
                                                {getStatusLabel(course.status)}
                                            </span>
                                        </div>

                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-500 text-sm">{__('edu.level')}:</span>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
                                                {getLevelLabel(course.level)}
                                            </span>
                                        </div>

                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-500 text-sm">{__('edu.price')}:</span>
                                            <div className="flex items-center space-x-2">
                                                {course.is_free ? (
                                                    <span className="font-bold text-green-600">Miễn phí</span>
                                                ) : (
                                                    <>
                                                        <span className="font-bold text-gray-900">{formatPrice(course.price)}</span>
                                                        {course.original_price && parseFloat(course.original_price) > parseFloat(course.price) && (
                                                            <span className="text-sm text-gray-500 line-through">{formatPrice(course.original_price)}</span>
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-500 text-sm">{__('edu.category')}:</span>
                                            <span className="font-medium text-gray-900 break-words text-right ml-2">{course.category}</span>
                                        </div>

                                        {course.lecturer && (
                                            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                                                <span className="text-gray-500 text-sm">{__('edu.lecturer')}:</span>
                                                <div className="flex items-center min-w-0">
                                                    <User className="w-4 h-4 text-gray-400 mr-1 flex-shrink-0" />
                                                    <span className="font-medium text-gray-900 break-words">
                                                        {course.lecturer.user?.name || course.lecturer.title}
                                                    </span>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {(course.is_featured || course.is_free) && (
                                        <div className="mt-4 pt-4 border-t border-gray-200">
                                            <div className="flex flex-wrap gap-2">
                                                {course.is_featured && (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <Star className="w-3 h-3 mr-1" />
                                                        Nổi bật
                                                    </span>
                                                )}
                                                {course.is_free && (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <CheckCircle className="w-3 h-3 mr-1" />
                                                        Khóa học miễn phí
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Course Details */}
                        <div className="lg:col-span-2">
                            <div className="space-y-6">
                                {/* Statistics */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <div className="flex items-center">
                                            <Users className="w-8 h-8 text-blue-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-blue-600">{course.enrolled_students || 0}</p>
                                                <p className="text-sm text-blue-600">{__('edu.enrolled_students')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                        <div className="flex items-center">
                                            <Eye className="w-8 h-8 text-green-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-green-600">{course.views_count || 0}</p>
                                                <p className="text-sm text-green-600">{__('edu.views_count')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                        <div className="flex items-center">
                                            <Clock className="w-8 h-8 text-purple-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-purple-600">{course.duration_hours || 0}</p>
                                                <p className="text-sm text-purple-600">Giờ</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                                        <div className="flex items-center">
                                            <BookOpen className="w-8 h-8 text-orange-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-orange-600">{course.total_lessons || 0}</p>
                                                <p className="text-sm text-orange-600">Bài học</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Course Description */}
                                {course.description && (
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <BookOpen className="w-5 h-5 mr-2" />
                                            {__('edu.course_description')}
                                        </h3>
                                        <div className="text-gray-700 whitespace-pre-line break-words overflow-hidden">
                                            {course.description}
                                        </div>
                                    </div>
                                )}

                                {/* Curriculum */}
                                {course.curriculum && course.curriculum.length > 0 && (
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <List className="w-5 h-5 mr-2" />
                                            {__('edu.curriculum')}
                                        </h3>
                                        <ul className="space-y-2">
                                            {course.curriculum.map((item, index) => (
                                                <li key={index} className="flex items-start">
                                                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                                    <span className="text-gray-700 break-words flex-1">{item}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                {/* Requirements */}
                                {course.requirements && course.requirements.length > 0 && (
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <Award className="w-5 h-5 mr-2" />
                                            {__('edu.requirements')}
                                        </h3>
                                        <ul className="space-y-2">
                                            {course.requirements.map((requirement, index) => (
                                                <li key={index} className="flex items-start">
                                                    <i className="fas fa-dot-circle text-blue-500 mr-2 mt-1 text-xs"></i>
                                                    <span className="text-gray-700 break-words flex-1">{requirement}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                {/* Learning Outcomes */}
                                {course.outcomes && course.outcomes.length > 0 && (
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <Target className="w-5 h-5 mr-2" />
                                            {__('edu.outcomes')}
                                        </h3>
                                        <ul className="space-y-2">
                                            {course.outcomes.map((outcome, index) => (
                                                <li key={index} className="flex items-start">
                                                    <Target className="w-4 h-4 text-purple-500 mr-2 mt-0.5 flex-shrink-0" />
                                                    <span className="text-gray-700 break-words flex-1">{outcome}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                {/* Tags */}
                                {course.tags && course.tags.length > 0 && (
                                    <div className="border-t border-gray-200 pt-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                            <Tag className="w-5 h-5 mr-2" />
                                            {__('edu.tags')}
                                        </h3>
                                        <div className="flex flex-wrap gap-2">
                                            {course.tags.map((tag, index) => (
                                                <span
                                                    key={index}
                                                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 break-words max-w-full"
                                                >
                                                    <Tag className="w-3 h-3 mr-1 flex-shrink-0" />
                                                    <span className="truncate">{tag}</span>
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Course Schedule */}
                                {(course.start_date || course.end_date) && (
                                    <div className="border-t border-gray-200 pt-6">
                                                                                 <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                             <Calendar className="w-5 h-5 mr-2" />
                                             Lịch khóa học
                                         </h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {course.start_date && (
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">{__('edu.start_date')}:</span>
                                                    <span className="text-gray-900">{formatDateTime(course.start_date)}</span>
                                                </div>
                                            )}
                                            {course.end_date && (
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">{__('edu.end_date')}:</span>
                                                    <span className="text-gray-900">{formatDateTime(course.end_date)}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}

                                {/* System Information */}
                                <div className="border-t border-gray-200 pt-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-3">{__('edu.system_information')}</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">{__('common.created_at')}:</span>
                                                <span className="text-gray-900">{formatDateTime(course.created_at)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">{__('common.updated_at')}:</span>
                                                <span className="text-gray-900">{formatDateTime(course.updated_at)}</span>
                                            </div>
                                            {course.published_at && (
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">{__('edu.published_at')}:</span>
                                                    <span className="text-gray-900">{formatDateTime(course.published_at)}</span>
                                                </div>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">Course ID:</span>
                                                <span className="text-gray-900">{course.id}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">Slug:</span>
                                                <span className="text-gray-900">{course.slug || 'N/A'}</span>
                                            </div>
                                            {course.max_students && (
                                                <div className="flex justify-between">
                                                    <span className="text-gray-500">{__('edu.max_students')}:</span>
                                                    <span className="text-gray-900">{course.max_students}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
