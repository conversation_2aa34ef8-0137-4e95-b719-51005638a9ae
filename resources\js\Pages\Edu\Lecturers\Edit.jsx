import React, { useState, useRef, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload, Plus, Trash2 } from 'lucide-react';

export default function Edit({ lecturer = {}, users = [] }) {
    const { flash } = usePage().props;
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(lecturer.profile_image_url || null);
    const [achievements, setAchievements] = useState(lecturer.achievements || ['']);
    const [certifications, setCertifications] = useState(lecturer.certifications || ['']);
    const [socialLinks, setSocialLinks] = useState(lecturer.social_links?.length ? lecturer.social_links : [{ platform: '', url: '' }]);
    const { addAlert } = useToast();

    const { data, setData, post, processing, errors } = useForm({
        user_id: lecturer.user_id || '',
        title: lecturer.title || '',
        short_description: lecturer.short_description || '',
        description: lecturer.description || '',
        achievements: lecturer.achievements || [],
        certifications: lecturer.certifications || [],
        social_links: lecturer.social_links || [],
        profile_image: null,
        experience_years: lecturer.experience_years || '',
        status: lecturer.status || 'pending_approval',
        _method: 'PUT',
    });

    useEffect(() => {
        if (flash.error) {
            addAlert('error', flash.error);
        }
        if (flash.success) {
            addAlert('success', flash.success);
        }
    }, [flash]);

    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            const firstError = Object.values(errors)[0];
            addAlert('error', firstError);
        }
    }, [errors]);

    useEffect(() => {
        setData('achievements', achievements.filter(item => item.trim() !== ''));
    }, [achievements]);

    useEffect(() => {
        setData('certifications', certifications.filter(item => item.trim() !== ''));
    }, [certifications]);

    useEffect(() => {
        setData('social_links', socialLinks.filter(item => item.platform.trim() !== '' && item.url.trim() !== ''));
    }, [socialLinks]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.edu.lecturers.update', lecturer.id), {
            onSuccess: (response) => {
                setImagePreview(lecturer.profile_image_url);
                addAlert('success', __('edu.lecturer_updated_successfully'));
            },
            onError: (errors) => {
                console.log('Validation errors:', errors);
                if (Object.keys(errors).length > 0) {
                    addAlert('error', Object.values(errors)[0]);
                }
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('profile_image', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('profile_image', null);
        setImagePreview(null);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    const addAchievement = () => {
        setAchievements([...achievements, '']);
    };

    const removeAchievement = (index) => {
        const newAchievements = achievements.filter((_, i) => i !== index);
        setAchievements(newAchievements);
    };

    const updateAchievement = (index, value) => {
        const newAchievements = [...achievements];
        newAchievements[index] = value;
        setAchievements(newAchievements);
    };

    const addCertification = () => {
        setCertifications([...certifications, '']);
    };

    const removeCertification = (index) => {
        const newCertifications = certifications.filter((_, i) => i !== index);
        setCertifications(newCertifications);
    };

    const updateCertification = (index, value) => {
        const newCertifications = [...certifications];
        newCertifications[index] = value;
        setCertifications(newCertifications);
    };

    const addSocialLink = () => {
        setSocialLinks([...socialLinks, { platform: '', url: '' }]);
    };

    const removeSocialLink = (index) => {
        const newSocialLinks = socialLinks.filter((_, i) => i !== index);
        setSocialLinks(newSocialLinks);
    };

    const updateSocialLink = (index, field, value) => {
        const newSocialLinks = [...socialLinks];
        newSocialLinks[index][field] = value;
        setSocialLinks(newSocialLinks);
    };

    return (
        <SuperAdminLayout>
            <Head title={__('edu.edit_lecturer') + ': ' + (lecturer.user?.name || lecturer.title)} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">
                            {__('edu.edit_lecturer')}: {lecturer.user?.name || lecturer.title}
                        </h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.edu.lecturers.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('edu.back_to_lecturers')}
                            </Link>
                            <Link
                                href={route('superadmin.edu.lecturers.show', lecturer.id)}
                                className="px-4 py-2 border border-blue-300 bg-blue-50 rounded-md text-sm text-blue-700 hover:bg-blue-100"
                            >
                                <i className="fas fa-eye mr-2"></i>
                                {__('edu.view_lecturer')}
                            </Link>
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="md:col-span-2">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('edu.basic_information')}</h2>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="user_id"
                                label={__('edu.user_account')}
                                value={data.user_id}
                                onChange={(e) => setData('user_id', e.target.value)}
                                errors={errors.user_id}
                                required
                            >
                                <option value="">{__('edu.select_user')}</option>
                                {users.map((user) => (
                                    <option key={user.id} value={user.id}>
                                        {user.name} ({user.email})
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="title"
                                type="text"
                                label={__('edu.lecturer_title')}
                                value={data.title}
                                onChange={(e) => setData('title', e.target.value)}
                                errors={errors.title}
                                placeholder={__('edu.lecturer_title_placeholder')}
                            />
                        </div>

                        <div className="md:col-span-2">
                            <TextareaWithLabel
                                id="short_description"
                                label={__('edu.short_description')}
                                rows="2"
                                value={data.short_description}
                                onChange={(e) => setData('short_description', e.target.value)}
                                errors={errors.short_description}
                                placeholder={__('edu.short_description_placeholder')}
                            />
                        </div>

                        <div className="md:col-span-2">
                            <TextareaWithLabel
                                id="description"
                                label={__('edu.detailed_description')}
                                rows="4"
                                value={data.description}
                                onChange={(e) => setData('description', e.target.value)}
                                errors={errors.description}
                                placeholder={__('edu.detailed_description_placeholder')}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="experience_years"
                                type="number"
                                min="0"
                                label={__('edu.experience_years')}
                                value={data.experience_years}
                                onChange={(e) => setData('experience_years', e.target.value)}
                                errors={errors.experience_years}
                            />
                        </div>

                        <div>
                            <SelectWithLabel
                                id="status"
                                label={__('edu.status')}
                                value={data.status}
                                onChange={(e) => setData('status', e.target.value)}
                                errors={errors.status}
                                required
                            >
                                <option value="pending_approval">{__('edu.pending_approval')}</option>
                                <option value="active">{__('edu.active')}</option>
                                <option value="inactive">{__('edu.inactive')}</option>
                                <option value="suspended">{__('edu.suspended')}</option>
                            </SelectWithLabel>
                        </div>

                        <div className="md:col-span-2">
                            <label className="text-sm block mb-1 font-medium text-gray-700">{__('edu.profile_image')}</label>
                            <div className="mt-1 flex items-center">
                                <input
                                    type="file"
                                    id="profile_image"
                                    ref={imageInputRef}
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleImageChange}
                                />

                                <div className="flex items-center space-x-4">
                                    {imagePreview ? (
                                        <div className="relative">
                                            <img
                                                src={imagePreview}
                                                alt="Profile Preview"
                                                className="h-32 w-32 object-cover rounded-full border border-gray-200"
                                            />
                                            <button
                                                type="button"
                                                onClick={clearImage}
                                                className="absolute -top-2 -right-2 text-red-500 bg-white rounded-full"
                                            >
                                                <XCircle className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div
                                            className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-full flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                                            onClick={() => imageInputRef.current?.click()}
                                        >
                                            <Upload className="h-8 w-8 text-gray-400" />
                                            <span className="mt-2 text-sm text-gray-500">{__('edu.upload_image')}</span>
                                        </div>
                                    )}

                                    <div className="flex flex-col">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => imageInputRef.current?.click()}
                                            className="mb-2"
                                        >
                                            {imagePreview ? __('edu.change_image') : __('edu.browse_image')}
                                        </Button>
                                        <p className="text-xs text-gray-500">
                                            {__('edu.supported_formats')}
                                        </p>
                                        {errors.profile_image && (
                                            <p className="text-sm text-red-600 mt-1">{errors.profile_image}</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.achievements')}
                            </h2>
                            <div className="space-y-3">
                                {achievements.map((achievement, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`achievement_${index}`}
                                            type="text"
                                            label={`${__('edu.achievement')} ${index + 1}`}
                                            value={achievement}
                                            onChange={(e) => updateAchievement(index, e.target.value)}
                                            placeholder={__('edu.achievement_placeholder')}
                                            className="flex-1"
                                        />
                                        {achievements.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeAchievement(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addAchievement}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_achievement')}
                                </Button>
                            </div>
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.certifications')}
                            </h2>
                            <div className="space-y-3">
                                {certifications.map((certification, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <TextInputWithLabel
                                            id={`certification_${index}`}
                                            type="text"
                                            label={`${__('edu.certification')} ${index + 1}`}
                                            value={certification}
                                            onChange={(e) => updateCertification(index, e.target.value)}
                                            placeholder={__('edu.certification_placeholder')}
                                            className="flex-1"
                                        />
                                        {certifications.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeCertification(index)}
                                                className="mt-6"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addCertification}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_certification')}
                                </Button>
                            </div>
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('edu.social_links')}
                            </h2>
                            <div className="space-y-3">
                                {socialLinks.map((link, index) => (
                                    <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                        <TextInputWithLabel
                                            id={`platform_${index}`}
                                            type="text"
                                            label={`${__('edu.platform')} ${index + 1}`}
                                            value={link.platform}
                                            onChange={(e) => updateSocialLink(index, 'platform', e.target.value)}
                                            placeholder={__('edu.platform_placeholder')}
                                        />
                                        <div className="flex items-center space-x-2">
                                            <TextInputWithLabel
                                                id={`url_${index}`}
                                                type="url"
                                                label={`${__('edu.url')} ${index + 1}`}
                                                value={link.url}
                                                onChange={(e) => updateSocialLink(index, 'url', e.target.value)}
                                                placeholder={__('edu.url_placeholder')}
                                                className="flex-1"
                                            />
                                            {socialLinks.length > 1 && (
                                                <Button
                                                    type="button"
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeSocialLink(index)}
                                                    className="mt-6"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={addSocialLink}
                                    className="flex items-center gap-2"
                                >
                                    <Plus className="h-4 w-4" />
                                    {__('edu.add_social_link')}
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                        <Link
                            href={route('superadmin.edu.lecturers.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('edu.cancel')}
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {processing ? __('edu.updating') : __('edu.save_changes')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
