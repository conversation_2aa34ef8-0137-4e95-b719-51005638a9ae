import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Search, Filter } from 'lucide-react';
import { useToast } from '@/Hooks/useToastContext';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import ImageWithFallback from '@/Components/ImageWithFallback';
import PrimaryButton from '@/Components/PrimaryButton';
import Pagination from '@/Components/Pagination';
import StatusBadge from '@/Components/ui/StatusBadge';
import axios from 'axios';
import Loading from '@/Components/Loading';

export default function Index({ lecturers: initialLecturers = { data: [], links: [], from: 0, to: 0, total: 0 }, filters = {} }) {
    const { processing, flash, csrf_token } = usePage().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const { addAlert } = useToast();
    const [lecturers, setLecturers] = useState(initialLecturers);
    const [selectedStatus, setSelectedStatus] = useState(filters.status ?? '');

    useEffect(() => {
        if (flash.error) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', flash.error);
        }
        if (flash.success) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', flash.success);
        }
    }, [flash]);

    const handleSearch = (e) => {
        e.preventDefault();
        fetchLecturers({
            ...filters,
            search: searchQuery,
            status: selectedStatus,
        });
    };

    const handleStatusFilter = (status) => {
        setSelectedStatus(status);
        fetchLecturers({
            ...filters,
            status,
        });
    };

    const handleSort = (field, direction) => {
        fetchLecturers({
            ...filters,
            sort: field,
            direction
        });
    };

    const confirmDelete = (lecturerId) => {
        setIsDeleting(lecturerId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteLecturer = () => {
        if (!isDeleting) return;

        axios.delete(route('superadmin.edu.lecturers.destroy', isDeleting), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {
            setLecturers(prevLecturers => {
                const updatedData = prevLecturers.data.filter(lecturer => lecturer.id !== isDeleting);
                const updatedLecturers = {
                    ...prevLecturers,
                    data: updatedData,
                    total: prevLecturers.total - 1
                };

                if (updatedLecturers.to) {
                    updatedLecturers.to = Math.max(updatedLecturers.to - 1, updatedLecturers.from);
                }

                return updatedLecturers;
            });

            setIsDeleting(null);
            addAlert('success', response.data.message || __('edu.lecturer_deleted_successfully'));
        })
        .catch(error => {
            setIsDeleting(null);
            addAlert('error', error.response?.data?.message || __('edu.delete_failed'));
        });
    };

    const fetchLecturers = (params) => {
        setIsProcessing(true);

        axios.get(route('superadmin.edu.lecturers.api'), {
            params
        })
        .then(response => {
            setLecturers(response.data.data);

            const url = new URL(window.location);
            Object.entries(params).forEach(([key, value]) => {
                if (value) {
                    url.searchParams.set(key, value);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.pushState({}, '', url);

            setIsProcessing(false);
        })
        .catch(error => {
            setIsProcessing(false);
            addAlert('error', error.response?.data?.message || __('edu.fetch_failed'));
        });
    };

    const columns = [
        {
            field: 'user.name',
            label: __('edu.lecturer_name'),
            sortable: true,
            render: (lecturer) => (
                <div className="flex items-center space-x-3">
                    <ImageWithFallback
                        src={lecturer.profile_image_url}
                        alt={lecturer.user?.name || lecturer.title}
                        fallbackText={(lecturer.user?.name || lecturer.title || 'L').charAt(0).toUpperCase()}
                        width="w-10"
                        height="h-10"
                        rounded="rounded-full"
                    />
                    <div>
                        <div className="font-medium text-gray-900">{lecturer.user?.name || __('edu.no_user')}</div>
                        <div className="text-xs text-gray-500">
                            {lecturer.title || __('edu.no_title')}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'experience_years',
            label: __('edu.experience'),
            sortable: true,
            render: (lecturer) => (
                <div className="text-center">
                    <span className="font-medium text-gray-900">
                        {lecturer.experience_years || 0} {__('edu.years')}
                    </span>
                </div>
            )
        },
        {
            field: 'total_courses',
            label: __('edu.courses'),
            sortable: true,
            render: (lecturer) => (
                <div className="text-center">
                    <span className="text-blue-600 font-medium">
                        {lecturer.total_courses || 0}
                    </span>
                </div>
            )
        },
        {
            field: 'total_students',
            label: __('edu.students'),
            sortable: true,
            render: (lecturer) => (
                <div className="text-center">
                    <span className="text-green-600 font-medium">
                        {lecturer.total_students || 0}
                    </span>
                </div>
            )
        },
        {
            field: 'rating',
            label: __('edu.rating'),
            sortable: true,
            render: (lecturer) => (
                <div className="flex items-center">
                    <span className="text-yellow-500 mr-1">★</span>
                    <span className="font-medium">
                        {lecturer.rating ? lecturer.rating.toFixed(1) : '0.0'}
                    </span>
                    <span className="text-xs text-gray-500 ml-1">
                        ({lecturer.total_reviews || 0})
                    </span>
                </div>
            )
        },
        {
            field: 'status',
            label: __('edu.status'),
            render: (lecturer) => {
                const statusColors = {
                    'active': 'bg-green-100 text-green-800',
                    'pending_approval': 'bg-yellow-100 text-yellow-800',
                    'suspended': 'bg-red-100 text-red-800',
                    'inactive': 'bg-gray-100 text-gray-800'
                };
                const statusLabels = {
                    'active': __('edu.active'),
                    'pending_approval': __('edu.pending_approval'),
                    'suspended': __('edu.suspended'),
                    'inactive': __('edu.inactive')
                };

                return (
                    <StatusBadge
                        text={statusLabels[lecturer.status] || lecturer.status}
                        color={statusColors[lecturer.status] || 'bg-gray-100 text-gray-800'}
                    />
                );
            }
        },
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('edu.lecturers')} />

            <div className="bg-white rounded-lg shadow-md relative">
                {isProcessing && <Loading overlay text={__('edu.loading')} />}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.lecturer_management')}</h1>
                        <PrimaryButton
                            href={route('superadmin.edu.lecturers.create')}
                            className="flex items-center gap-2"
                        >
                            <Plus className="w-4 h-4" />
                            {__('edu.add_lecturer')}
                        </PrimaryButton>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                            <form onSubmit={handleSearch} className="w-full">
                                <div className="relative">
                                    <TextInputWithLabel
                                        id="search"
                                        type="text"
                                        label={__('edu.search_lecturers')}
                                        placeholder={__('edu.search_lecturers_placeholder')}
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                    <div className="absolute top-9 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <button type="submit" className="hidden">{__('edu.search')}</button>
                                </div>
                            </form>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('edu.status')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleStatusFilter('')}
                                        variant={selectedStatus === '' || selectedStatus === null || selectedStatus === undefined ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('edu.all_status')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('active')}
                                        variant={selectedStatus === 'active' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === 'active' ? "bg-green-600" : ""}
                                    >
                                        {__('edu.active')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('pending_approval')}
                                        variant={selectedStatus === 'pending_approval' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === 'pending_approval' ? "bg-yellow-600" : ""}
                                    >
                                        {__('edu.pending_approval')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('suspended')}
                                        variant={selectedStatus === 'suspended' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === 'suspended' ? "bg-red-600" : ""}
                                    >
                                        {__('edu.suspended')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <DataTable
                    data={lecturers?.data || []}
                    columns={columns}
                    onSort={handleSort}
                    sortField={filters?.sort || 'created_at'}
                    sortDirection={filters?.direction || 'desc'}
                    emptyStateMessage={__('edu.no_lecturers_found')}
                    primaryKey="id"
                    viewRoute="superadmin.edu.lecturers.show"
                    editRoute="superadmin.edu.lecturers.edit"
                    deleteCallback={confirmDelete}
                    cancelDeletion={cancelDelete}
                    loading={isProcessing}
                />

                <ConfirmDeleteModal
                    isOpen={isDeleting !== null}
                    onClose={cancelDelete}
                    onConfirm={deleteLecturer}
                    title={__('edu.delete_lecturer')}
                    message={__('edu.delete_lecturer_confirmation')}
                    isProcessing={isProcessing}
                />

                {lecturers?.links && lecturers.links.length > 3 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-700">
                                {__('edu.showing')} {lecturers?.from || 0} {__('edu.to')} {lecturers?.to || 0} {__('edu.of')} {lecturers?.total || 0} {__('edu.lecturers')?.toLowerCase() || 'lecturers'}
                            </p>
                            <Pagination
                                links={lecturers?.links || []}
                                preserveState={true}
                                preserveScroll={true}
                            />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
