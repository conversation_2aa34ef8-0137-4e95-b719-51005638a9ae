import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';
import { formatDateTime } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import { Pencil, Trash2, Star, Award, Users, BookOpen, Calendar, Mail, Phone, Globe } from 'lucide-react';
import ImageWithFallback from '@/Components/ImageWithFallback';

export default function Show({ lecturer = {} }) {
    const [isDeleting, setIsDeleting] = useState(false);
    const { addAlert } = useToast();

    const handleDelete = () => {
        router.delete(route('superadmin.edu.lecturers.destroy', lecturer.id), {
            onSuccess: () => {
                addAlert('success', __('edu.lecturer_deleted_successfully'));
            },
            onError: (errors) => {
                addAlert('error', errors.message || __('edu.delete_failed'));
                setIsDeleting(false);
            }
        });
    };

    const cancelDelete = () => {
        setIsDeleting(false);
    };

    const getStatusColor = (status) => {
        const colors = {
            'active': 'bg-green-100 text-green-800',
            'pending_approval': 'bg-yellow-100 text-yellow-800',
            'suspended': 'bg-red-100 text-red-800',
            'inactive': 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getStatusLabel = (status) => {
        const labels = {
            'active': __('edu.active'),
            'pending_approval': __('edu.pending_approval'),
            'suspended': __('edu.suspended'),
            'inactive': __('edu.inactive')
        };
        return labels[status] || status;
    };

    const getSocialIcon = (platform) => {
        const icons = {
            'facebook': 'fab fa-facebook-f',
            'twitter': 'fab fa-twitter',
            'linkedin': 'fab fa-linkedin-in',
            'instagram': 'fab fa-instagram',
            'youtube': 'fab fa-youtube',
            'github': 'fab fa-github',
            'website': 'fas fa-globe'
        };
        return icons[platform.toLowerCase()] || 'fas fa-link';
    };

    return (
        <SuperAdminLayout>
            <Head title={__('edu.lecturer') + ': ' + (lecturer.user?.name || lecturer.title)} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.lecturer_details')}</h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.edu.lecturers.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('edu.back_to_lecturers')}
                            </Link>
                            <Link
                                href={route('superadmin.edu.lecturers.edit', lecturer.id)}
                                className="px-4 py-2 border border-blue-300 bg-blue-50 rounded-md text-sm text-blue-700 hover:bg-blue-100"
                            >
                                <Pencil className="w-4 h-4 inline-block mr-1" />
                                {__('edu.edit_lecturer')}
                            </Link>
                            {!isDeleting ? (
                                <Button
                                    variant="destructive"
                                    onClick={() => setIsDeleting(true)}
                                >
                                    <Trash2 className="w-4 h-4 mr-1" />
                                    {__('common.delete')}
                                </Button>
                            ) : (
                                <div className="flex space-x-2">
                                    <Button
                                        variant="destructive"
                                        onClick={handleDelete}
                                    >
                                        {__('edu.confirm')}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={cancelDelete}
                                    >
                                        {__('edu.cancel')}
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="md:col-span-1">
                            <div className="flex flex-col items-center p-6 bg-gray-50 rounded-lg border border-gray-200">
                                <ImageWithFallback
                                    src={lecturer.profile_image_url}
                                    alt={lecturer.user?.name || lecturer.title}
                                    fallbackText={(lecturer.user?.name || lecturer.title || 'L').charAt(0).toUpperCase()}
                                    width="w-32"
                                    height="h-32"
                                    rounded="rounded-full"
                                    className="mb-4"
                                    textSize="text-3xl"
                                />

                                <h2 className="text-xl font-bold text-gray-900 mb-2 text-center">
                                    {lecturer.user?.name || __('edu.no_user')}
                                </h2>

                                {lecturer.title && (
                                    <p className="text-lg text-gray-600 mb-4 text-center">{lecturer.title}</p>
                                )}

                                <div className="w-full space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-500 text-sm">{__('edu.status')}:</span>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(lecturer.status)}`}>
                                            {getStatusLabel(lecturer.status)}
                                        </span>
                                    </div>

                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-500 text-sm">{__('edu.experience')}:</span>
                                        <span className="font-medium text-gray-900">
                                            {lecturer.experience_years || 0} {__('edu.years')}
                                        </span>
                                    </div>

                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-500 text-sm">{__('edu.rating')}:</span>
                                        <div className="flex items-center">
                                            <Star className="w-4 h-4 text-yellow-500 mr-1" />
                                            <span className="font-medium">
                                                {lecturer.rating ? lecturer.rating.toFixed(1) : '0.0'}
                                            </span>
                                            <span className="text-xs text-gray-500 ml-1">
                                                ({lecturer.total_reviews || 0} {__('edu.reviews')})
                                            </span>
                                        </div>
                                    </div>

                                    {lecturer.user?.email && (
                                        <div className="flex items-center pt-2 border-t border-gray-200">
                                            <Mail className="w-4 h-4 text-gray-400 mr-2" />
                                            <span className="text-sm text-gray-600">{lecturer.user.email}</span>
                                        </div>
                                    )}

                                    {lecturer.user?.phone && (
                                        <div className="flex items-center">
                                            <Phone className="w-4 h-4 text-gray-400 mr-2" />
                                            <span className="text-sm text-gray-600">{lecturer.user.phone}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {lecturer.social_links && lecturer.social_links.length > 0 && (
                                <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-3">{__('edu.social_links')}</h3>
                                    <div className="space-y-2">
                                        {lecturer.social_links.map((link, index) => (
                                            <div key={index} className="flex items-center">
                                                <i className={`${getSocialIcon(link.platform)} text-gray-400 mr-3 w-4`}></i>
                                                <a
                                                    href={link.url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:text-blue-800 text-sm"
                                                >
                                                    {link.platform}
                                                </a>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="md:col-span-2">
                            <div className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <div className="flex items-center">
                                            <BookOpen className="w-8 h-8 text-blue-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-blue-600">{lecturer.total_courses || 0}</p>
                                                <p className="text-sm text-blue-600">{__('edu.total_courses')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                        <div className="flex items-center">
                                            <Users className="w-8 h-8 text-green-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-green-600">{lecturer.total_students || 0}</p>
                                                <p className="text-sm text-green-600">{__('edu.total_students')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                        <div className="flex items-center">
                                            <Award className="w-8 h-8 text-purple-600 mr-3" />
                                            <div>
                                                <p className="text-2xl font-bold text-purple-600">{lecturer.total_reviews || 0}</p>
                                                <p className="text-sm text-purple-600">{__('edu.total_reviews')}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {lecturer.short_description && (
                                    <div className="border-t border-gray-200 pt-4">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{__('edu.short_description')}</h3>
                                        <p className="text-gray-700">{lecturer.short_description}</p>
                                    </div>
                                )}

                                {lecturer.description && (
                                    <div className="border-t border-gray-200 pt-4">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{__('edu.detailed_description')}</h3>
                                        <p className="text-gray-700 whitespace-pre-line">{lecturer.description}</p>
                                    </div>
                                )}

                                {lecturer.achievements && lecturer.achievements.length > 0 && (
                                    <div className="border-t border-gray-200 pt-4">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3">{__('edu.achievements')}</h3>
                                        <ul className="space-y-2">
                                            {lecturer.achievements.map((achievement, index) => (
                                                <li key={index} className="flex items-center">
                                                    <Award className="w-4 h-4 text-yellow-500 mr-2 flex-shrink-0" />
                                                    <span className="text-gray-700">{achievement}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                {lecturer.certifications && lecturer.certifications.length > 0 && (
                                    <div className="border-t border-gray-200 pt-4">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-3">{__('edu.certifications')}</h3>
                                        <ul className="space-y-2">
                                            {lecturer.certifications.map((certification, index) => (
                                                <li key={index} className="flex items-center">
                                                    <i className="fas fa-certificate text-blue-500 mr-2 flex-shrink-0"></i>
                                                    <span className="text-gray-700">{certification}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-3">{__('edu.system_information')}</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">{__('common.created_at')}:</span>
                                                <span className="text-gray-900">{formatDateTime(lecturer.created_at)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">{__('common.updated_at')}:</span>
                                                <span className="text-gray-900">{formatDateTime(lecturer.updated_at)}</span>
                                            </div>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">ID:</span>
                                                <span className="text-gray-900">{lecturer.id}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">{__('edu.user_id')}:</span>
                                                <span className="text-gray-900">{lecturer.user_id}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
