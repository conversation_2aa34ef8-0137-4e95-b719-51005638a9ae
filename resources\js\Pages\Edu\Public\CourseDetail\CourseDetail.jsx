import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
    Play,
    Clock,
    Users,
    Star,
    Calendar,
    CheckCircle,
    Circle,
    FileText,
    Infinity,
    Smartphone,
    Award,
    MessageCircle,
    Shield,
    Mail,
    Phone,
    Facebook,
    Twitter,
    Instagram,
    Youtube
} from 'lucide-react';

const courseDescriptionStyles = `
.description-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.description-content img {
    max-width: 100%;
    height: auto;
}

.description-content p,
.description-content div {
    max-width: 100%;
    overflow-x: auto;
}

.description-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 100%;
    padding: 1rem;
    background-color: #f8f8f8;
    border-radius: 4px;
}

.description-content table {
    width: 100%;
    border-collapse: collapse;
    overflow-x: auto;
    display: block;
    max-width: 100%;
}

.description-content table td,
.description-content table th {
    border: 1px solid #ddd;
    padding: 8px;
}

.description-content a {
    color: #05A493;
    text-decoration: underline;
}
`;

import { But<PERSON> } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import EduHeader from '@/Components/Landing/EduHeader';
import { Footer } from '@/Components/Landing';

export default function CourseDetail({ course, reviews, relatedCourses, ratingStats }) {
    const [activeTab, setActiveTab] = useState('overview');
    const [countdown, setCountdown] = useState({
        days: 0,
        hours: 0,
        minutes: 0
    });

    const formatPrice = (price) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    const renderStars = (rating) => {
        return [...Array(5)].map((_, i) => (
            <Star
                key={i}
                className={`h-4 w-4 ${i < rating ? 'fill-current text-secondary' : 'text-gray-300'}`}
            />
        ));
    };

    useEffect(() => {

        if (!course.discountEndDate) return;

        const endDate = new Date(course.discountEndDate);

        const calculateTimeLeft = () => {
            const now = new Date();
            const difference = endDate - now;

            if (difference <= 0) {
                return { days: 0, hours: 0, minutes: 0 };
            }

            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

            return { days, hours, minutes };
        };

        setCountdown(calculateTimeLeft());

        const timer = setInterval(() => {
            const timeLeft = calculateTimeLeft();
            setCountdown(timeLeft);

            if (timeLeft.days === 0 && timeLeft.hours === 0 && timeLeft.minutes === 0) {
                clearInterval(timer);
            }
        }, 60000);

        return () => clearInterval(timer);
    }, [course.discountEndDate]);

    return (
        <div className="flex flex-col min-h-screen bg-light-gray">
            <Head title={`${course.title} - PickleAcademy`} />

            {/* Add style tag for description content */}
            <style dangerouslySetInnerHTML={{ __html: courseDescriptionStyles }} />

            <EduHeader />

            <main className="pt-1">
                <section className="container mx-auto px-4 lg:px-12 py-8">
                    {/* Course Hero Section */}
                    <div className="bg-white rounded-lg overflow-hidden shadow-[0_4px_15px_rgba(0,0,0,0.05)] mb-8">
                        <div className="flex flex-col lg:flex-row">
                            <div className="lg:w-2/5 h-64 lg:h-auto">
                                <ImageWithFallback
                                    src={course.image}
                                    alt={course.title}
                                    fallbackText={course.title.charAt(0)}
                                    width="w-full"
                                    height="h-full"
                                    imageClassName="object-cover"
                                    rounded="rounded-none"
                                    bgColor="bg-gray-100"
                                    textColor="text-[#ee0033]"
                                    textSize="text-4xl"
                                />
                            </div>
                            <div className="lg:w-3/5 p-6 lg:p-8">
                                <Badge className="bg-[#05A493]/10 text-[#05A493] mb-4">
                                    {course.level}
                                </Badge>
                                <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                                    {course.title}
                                </h1>
                                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                                    {course.subtitle}
                                </p>

                                <div className="flex flex-wrap gap-6 mb-6">
                                    <div className="flex items-center text-gray-600">
                                        <Star className="h-4 w-4 mr-2 text-[#05A493]" />
                                        {course.rating} ({course.reviewCount} đánh giá)
                                    </div>
                                    <div className="flex items-center text-gray-600">
                                        <Users className="h-4 w-4 mr-2 text-[#05A493]" />
                                        {course.studentCount} học viên
                                    </div>
                                    <div className="flex items-center text-gray-600">
                                        <Play className="h-4 w-4 mr-2 text-[#05A493]" />
                                        {course.lessons} bài học ({course.duration})
                                    </div>
                                    <div className="flex items-center text-gray-600">
                                        <Calendar className="h-4 w-4 mr-2 text-[#05A493]" />
                                        Cập nhật {course.lastUpdated || 'Chưa cập nhật'}
                                    </div>
                                </div>

                                <div className="flex items-center pt-4 border-t border-gray-200">
                                    <Link
                                        href={route('edu.lecturers.profile', course.instructor.id)}
                                        className="flex items-center group"
                                    >
                                        <div className="w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-secondary group-hover:border-[#ee0033] transition-colors">
                                            <ImageWithFallback
                                                src={course.instructor.avatar}
                                                alt={course.instructor.name}
                                                fallbackText={course.instructor.name.charAt(0)}
                                                width="w-full"
                                                height="h-full"
                                                rounded="rounded-none"
                                                bgColor="bg-gray-100"
                                                textColor="text-[#ee0033]"
                                                textSize="text-lg"
                                            />
                                        </div>
                                        <div>
                                            <div className="font-semibold text-gray-900 group-hover:text-[#ee0033] transition-colors flex items-center">
                                                {course.instructor.name}
                                                <svg className="w-3.5 h-3.5 ml-1 text-gray-400 group-hover:text-[#ee0033] transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                </svg>
                                            </div>
                                            <div className="text-gray-600 text-sm">
                                                {course.instructor.title}
                                            </div>
                                        </div>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Course Content */}
                    <div className="flex flex-col lg:flex-row gap-8">
                        {/* Main Content */}
                        <div className="lg:w-2/3">
                            {/* Tabs */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] mb-8">
                                <div className="flex border-b border-gray-200">
                                    {[
                                        { id: 'overview', label: 'Tổng quan' },
                                        { id: 'curriculum', label: 'Nội dung' },
                                        { id: 'instructor', label: 'Giảng viên' },
                                        { id: 'reviews', label: 'Đánh giá' }
                                    ].map(tab => (
                                        <button
                                            key={tab.id}
                                            onClick={() => setActiveTab(tab.id)}
                                            className={`flex-1 py-4 px-6 font-medium transition-colors ${
                                                activeTab === tab.id
                                                    ? 'text-[#ee0033] border-b-2 border-[#ee0033]'
                                                    : 'text-gray-600 hover:text-[#ee0033]'
                                            }`}
                                        >
                                            {tab.label}
                                        </button>
                                    ))}
                                </div>

                                <div className="p-6 lg:p-8">
                                    {/* Overview Tab */}
                                    {activeTab === 'overview' && (
                                        <div className="space-y-8">
                                            <div>
                                                <h3 className="text-xl font-semibold mb-4">Mô tả khóa học</h3>
                                                <div className="space-y-4 text-gray-600 leading-relaxed">
                                                    <div
                                                        className="description-content overflow-hidden max-w-full"
                                                        dangerouslySetInnerHTML={{ __html: course.description }}
                                                    />
                                                </div>
                                            </div>

                                            {course.outcomes && course.outcomes.length > 0 && (
                                                <div>
                                                    <h3 className="text-xl font-semibold mb-4">Bạn sẽ học được gì</h3>
                                                    <ul className="space-y-3 overflow-hidden">
                                                        {course.outcomes.map((item, index) => (
                                                            <li key={index} className="flex items-start">
                                                                <CheckCircle className="h-5 w-5 text-[#05A493] mr-3 mt-0.5 flex-shrink-0" />
                                                                <span className="text-gray-600 break-words overflow-hidden">{item}</span>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}

                                            {course.requirements && course.requirements.length > 0 && (
                                                <div>
                                                    <h3 className="text-xl font-semibold mb-4">Yêu cầu</h3>
                                                    <ul className="space-y-3 overflow-hidden">
                                                        {course.requirements.map((item, index) => (
                                                            <li key={index} className="flex items-start">
                                                                <Circle className="h-5 w-5 text-secondary mr-3 mt-0.5 flex-shrink-0" />
                                                                <span className="text-gray-600 break-words overflow-hidden">{item}</span>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Curriculum Tab */}
                                    {activeTab === 'curriculum' && (
                                        <div className="space-y-6">
                                            <div>
                                                <h3 className="text-xl font-semibold mb-4">Nội dung khóa học</h3>
                                                <p className="text-gray-600 mb-6">
                                                    Khóa học gồm {course.lessons} bài học với tổng thời lượng {course.duration} học.
                                                </p>
                                            </div>

                                            {course.curriculum && course.curriculum.length > 0 ? (
                                                course.curriculum.map((section, sectionIndex) => (
                                                    <div key={sectionIndex} className="border border-gray-200 rounded-lg">
                                                        <div className="flex justify-between items-center p-4 bg-gray-50">
                                                            <h4 className="font-semibold text-gray-900">{section.title}</h4>
                                                            <span className="text-sm text-gray-600">
                                                                {section.lessons} bài học ({section.duration})
                                                            </span>
                                                        </div>
                                                        {section.items && (
                                                            <div className="divide-y divide-gray-200">
                                                                {section.items.map((lesson, lessonIndex) => (
                                                                    <div key={lessonIndex} className="flex items-center p-4 hover:bg-gray-50 transition-colors">
                                                                        <div className="mr-4">
                                                                            {lesson.type === 'video' ? (
                                                                                <Play className="h-5 w-5 text-[#05A493]" />
                                                                            ) : (
                                                                                <FileText className="h-5 w-5 text-[#05A493]" />
                                                                            )}
                                                                        </div>
                                                                        <div className="flex-1">
                                                                            <div className="font-medium text-gray-900">{lesson.title}</div>
                                                                        </div>
                                                                        {lesson.preview && (
                                                                            <button className="text-[#05A493] text-sm font-medium mr-4">
                                                                                Xem thử
                                                                            </button>
                                                                        )}
                                                                        <div className="text-sm text-gray-600">{lesson.duration}</div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="text-center py-8 text-gray-500">
                                                    <p>Nội dung khóa học sẽ được cập nhật sớm.</p>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Instructor Tab */}
                                    {activeTab === 'instructor' && (
                                        <div className="space-y-6">
                                            <div className="flex flex-col md:flex-row gap-6">
                                                <div className="w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden border-3 border-secondary mx-auto md:mx-0 flex-shrink-0">
                                                    <ImageWithFallback
                                                        src={course.instructor.avatar}
                                                        alt={course.instructor.name}
                                                        fallbackText={course.instructor.name.charAt(0)}
                                                        width="w-full"
                                                        height="h-full"
                                                        rounded="rounded-none"
                                                        bgColor="bg-gray-100"
                                                        textColor="text-[#ee0033]"
                                                        textSize="text-3xl"
                                                    />
                                                </div>
                                                <div className="flex-1 text-center md:text-left">
                                                    <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                                                        {course.instructor.name}
                                                    </h3>
                                                    <p className="text-[#05A493] text-lg mb-4">
                                                        {course.instructor.title}
                                                    </p>
                                                    <div className="flex flex-wrap justify-center md:justify-start gap-6 mb-4">
                                                        <div className="flex items-center">
                                                            <Play className="h-4 w-4 text-[#05A493] mr-2" />
                                                            <span className="font-semibold mr-1">{course.instructor.courses}</span>
                                                            <span className="text-gray-600">khóa học</span>
                                                        </div>
                                                        <div className="flex items-center">
                                                            <Users className="h-4 w-4 text-[#05A493] mr-2" />
                                                            <span className="font-semibold mr-1">{course.instructor.students}</span>
                                                            <span className="text-gray-600">học viên</span>
                                                        </div>
                                                        <div className="flex items-center">
                                                            <Star className="h-4 w-4 text-[#05A493] mr-2" />
                                                            <span className="font-semibold mr-1">{course.instructor.rating}</span>
                                                            <span className="text-gray-600">đánh giá trung bình</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {course.instructor.description && (
                                                <div className="space-y-4 text-gray-600 leading-relaxed">
                                                    <div dangerouslySetInnerHTML={{ __html: course.instructor.description }} />
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Reviews Tab */}
                                    {activeTab === 'reviews' && (
                                        <div className="space-y-6">
                                            <div className="flex flex-col md:flex-row items-center gap-6 pb-6 border-b border-gray-200">
                                                <div className="text-center">
                                                    <div className="text-5xl font-bold text-gray-900 mb-2">{ratingStats.average}</div>
                                                    <div className="flex gap-1 mb-2 justify-center">
                                                        {renderStars(Math.round(ratingStats.average))}
                                                    </div>
                                                    <div className="text-sm text-gray-600">{ratingStats.total} đánh giá</div>
                                                </div>

                                                {/* Rating breakdown */}
                                                {ratingStats.total > 0 && (
                                                    <div className="flex-1 max-w-md">
                                                        {[5, 4, 3, 2, 1].map((star) => {
                                                            const count = ratingStats.breakdown[star] || 0;
                                                            const percentage = ratingStats.total > 0 ? (count / ratingStats.total) * 100 : 0;
                                                            return (
                                                                <div key={star} className="flex items-center gap-2 mb-1">
                                                                    <span className="text-sm w-8">{star}</span>
                                                                    <Star className="h-4 w-4 fill-current text-yellow-400" />
                                                                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                                                                        <div
                                                                            className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                                                            style={{ width: `${percentage}%` }}
                                                                        />
                                                                    </div>
                                                                    <span className="text-sm text-gray-600 w-8">{count}</span>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="space-y-6">
                                                {reviews && reviews.length > 0 ? reviews.map((review) => (
                                                    <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                                                        <div className="flex items-start justify-between mb-4">
                                                            <div className="flex gap-3">
                                                                <div className="w-12 h-12 rounded-full overflow-hidden">
                                                                    <ImageWithFallback
                                                                        src={review.avatar}
                                                                        alt={review.name}
                                                                        fallbackText={review.name.charAt(0)}
                                                                        width="w-full"
                                                                        height="h-full"
                                                                        rounded="rounded-none"
                                                                        bgColor="bg-[#ee0033]"
                                                                        textColor="text-white"
                                                                        textSize="text-lg"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <div className="font-semibold text-gray-900">{review.name}</div>
                                                                    <div className="text-sm text-gray-600">{review.date}</div>
                                                                </div>
                                                            </div>
                                                            <div className="flex gap-1">
                                                                {renderStars(review.rating)}
                                                            </div>
                                                        </div>
                                                        <p className="text-gray-600 leading-relaxed">{review.content}</p>
                                                    </div>
                                                )) : (
                                                    <div className="text-center py-8 text-gray-500">
                                                        <p>Chưa có đánh giá nào cho khóa học này.</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="lg:w-1/3">
                            {/* Pricing Card */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] mb-6">
                                <div className="p-6">
                                    <div className="flex items-center mb-3">
                                        <span className="text-3xl font-bold text-[#ee0033]">
                                            {formatPrice(course.price)}
                                        </span>
                                        {course.originalPrice && course.originalPrice > course.price && (
                                            <>
                                                <span className="text-lg text-gray-500 line-through ml-3">
                                                    {formatPrice(course.originalPrice)}
                                                </span>
                                                <Badge className="bg-secondary text-gray-900 ml-3">
                                                    -{course.discount}%
                                                </Badge>
                                            </>
                                        )}
                                    </div>
                                    {/* Countdown Timer */}
                                    {course.discount && course.discountEndDate && (
                                        <div className="bg-gray-50 rounded-lg p-4 mb-6 text-center">
                                            <div className="text-sm font-medium mb-3">Giá ưu đãi kết thúc trong:</div>
                                            <div className="flex justify-center gap-4">
                                                <div className="text-center">
                                                    <div className="w-10 h-10 bg-[#ee0033] text-white rounded flex items-center justify-center font-semibold text-lg mb-1">
                                                        {countdown.days}
                                                    </div>
                                                    <div className="text-xs text-gray-600">Ngày</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="w-10 h-10 bg-[#ee0033] text-white rounded flex items-center justify-center font-semibold text-lg mb-1">
                                                        {countdown.hours}
                                                    </div>
                                                    <div className="text-xs text-gray-600">Giờ</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="w-10 h-10 bg-[#ee0033] text-white rounded flex items-center justify-center font-semibold text-lg mb-1">
                                                        {countdown.minutes}
                                                    </div>
                                                    <div className="text-xs text-gray-600">Phút</div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <div className="space-y-3">
                                        <Button className="w-full bg-[#ee0033] hover:bg-[#ee0033]/90 h-12 text-lg font-semibold">
                                            Đăng ký ngay
                                        </Button>
                                        <Button variant="outline" className="w-full h-12 border-[#ee0033] text-[#ee0033] hover:bg-[#ee0033] hover:text-white">
                                            Thêm vào giỏ hàng
                                        </Button>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 p-6">
                                    <h3 className="font-semibold text-gray-900 mb-4">Khóa học bao gồm</h3>
                                    <ul className="space-y-3">
                                        {[
                                            { icon: Play, text: `${course.duration} video học tập` },
                                            { icon: FileText, text: `${course.lessons} bài học` },
                                            { icon: Infinity, text: "Truy cập trọn đời" },
                                            { icon: Smartphone, text: "Truy cập trên điện thoại và máy tính" },
                                            { icon: Award, text: "Chứng chỉ hoàn thành" },
                                            { icon: MessageCircle, text: "Hỗ trợ từ giảng viên" },
                                        ].map((feature, index) => (
                                            <li key={index} className="flex items-center text-gray-600">
                                                <feature.icon className="h-4 w-4 text-[#05A493] mr-3 flex-shrink-0" />
                                                {feature.text}
                                            </li>
                                        ))}
                                    </ul>
                                </div>

                                {course.tags && course.tags.length > 0 && (
                                    <div className="border-t border-gray-200 p-6">
                                        <h3 className="font-semibold text-gray-900 mb-4">Thẻ</h3>
                                        <div className="flex flex-wrap gap-2">
                                            {course.tags.map((tag, index) => (
                                                <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-600">
                                                    {tag}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Related Courses */}
                    {relatedCourses && relatedCourses.length > 0 && (
                        <div className="mt-12">
                            <h2 className="text-2xl font-semibold text-center text-gray-900 mb-8">
                                Khóa học liên quan
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {relatedCourses.map((relatedCourse) => (
                                    <Link
                                        key={relatedCourse.id}
                                        href={route('edu.courses.show', relatedCourse.slug)}
                                        className="bg-white rounded-lg overflow-hidden shadow-[0_4px_15px_rgba(0,0,0,0.05)] transition-transform hover:transform hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,0,0,0.1)]"
                                    >
                                        <div className="h-48 overflow-hidden">
                                            <ImageWithFallback
                                                src={relatedCourse.image}
                                                alt={relatedCourse.title}
                                                fallbackText={relatedCourse.title.charAt(0)}
                                                width="w-full"
                                                height="h-full"
                                                imageClassName="object-cover transition-transform duration-500 hover:scale-110"
                                                rounded="rounded-none"
                                                bgColor="bg-gray-100"
                                                textColor="text-[#ee0033]"
                                                textSize="text-2xl"
                                            />
                                        </div>
                                        <div className="p-5">
                                            <h3 className="font-semibold text-lg text-gray-900 mb-3 line-clamp-2">
                                                {relatedCourse.title}
                                            </h3>
                                            <div className="flex items-center mb-3">
                                                <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                                                    <ImageWithFallback
                                                        src={relatedCourse.instructorAvatar}
                                                        alt={relatedCourse.instructor}
                                                        fallbackText={relatedCourse.instructor.charAt(0)}
                                                        width="w-full"
                                                        height="h-full"
                                                        rounded="rounded-none"
                                                        bgColor="bg-gray-100"
                                                        textColor="text-[#ee0033]"
                                                        textSize="text-sm"
                                                    />
                                                </div>
                                                <span className="text-sm text-gray-600">{relatedCourse.instructor}</span>
                                            </div>
                                            <div className="font-semibold text-[#ee0033]">
                                                {formatPrice(relatedCourse.price)}
                                            </div>
                                        </div>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    )}
                </section>
            </main>

            <Footer />
        </div>
    );
}
