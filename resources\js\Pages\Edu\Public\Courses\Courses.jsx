import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import {
    Search,
    ChevronDown,
    RotateCcw,
    Video,
    Clock,
    User,
    ChevronLeft,
    ChevronRight,
    Facebook,
    Twitter,
    Instagram,
    Youtube,
    Mail
} from 'lucide-react';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import EduHeader from '@/Components/Landing/EduHeader';
import Pagination from '@/Components/Pagination';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import { Footer } from '@/Components/Landing';

export default function Courses({ courses, categories, levelOptions, instructorOptions, filters }) {
    const [activeCategory, setActiveCategory] = useState(filters.category || 'all');
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [levelFilter, setLevelFilter] = useState(filters.level || '');
    const [instructorFilter, setInstructorFilter] = useState(filters.instructor || '');
    const [minPrice, setMinPrice] = useState(filters.min_price || '');
    const [maxPrice, setMaxPrice] = useState(filters.max_price || '');
    const [sortBy, setSortBy] = useState(filters.sort_by || 'popular');
    const sortOptions = [
        { value: 'popular', label: 'Phổ biến nhất' },
        { value: 'newest', label: 'Mới nhất' },
        { value: 'price-asc', label: 'Giá tăng dần' },
        { value: 'price-desc', label: 'Giá giảm dần' }
    ];

    const formatPrice = (price) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    const getLevelLabel = (level) => {
        const option = levelOptions.find(opt => opt.value === level);
        return option ? option.label : level;
    };

    const handleSearch = (e) => {
        e.preventDefault();
        applyFilters();
    };

    const applyFilters = (resetPage = true) => {
        const params = {
            category: activeCategory,
            search: searchTerm,
            level: levelFilter,
            instructor: instructorFilter,
            min_price: minPrice,
            max_price: maxPrice,
            sort_by: sortBy,
        };

        if (!resetPage) {
            const currentPage = new URLSearchParams(window.location.search).get('page');
            if (currentPage) {
                params.page = currentPage;
            }
        }

        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === 'all') {
                delete params[key];
            }
        });

        router.get(route('edu.courses.index'), params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleResetFilters = () => {
        setActiveCategory('all');
        setSearchTerm('');
        setLevelFilter('');
        setInstructorFilter('');
        setMinPrice('');
        setMaxPrice('');
        setSortBy('popular');

        router.get(route('edu.courses.index'), {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

        const handleCategoryChange = (categoryId) => {
        setActiveCategory(categoryId);

        const params = {
            category: categoryId,
            search: searchTerm,
            level: levelFilter,
            instructor: instructorFilter,
            min_price: minPrice,
            max_price: maxPrice,
            sort_by: sortBy,
        };

        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === 'all') {
                delete params[key];
            }
        });

        router.get(route('edu.courses.index'), params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSortChange = (newSortBy) => {
        setSortBy(newSortBy);

        const params = {
            category: activeCategory,
            search: searchTerm,
            level: levelFilter,
            instructor: instructorFilter,
            min_price: minPrice,
            max_price: maxPrice,
            sort_by: newSortBy,
        };

        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === 'all') {
                delete params[key];
            }
        });

        router.get(route('edu.courses.index'), params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handlePageChange = (url) => {
        if (url) {

            const urlObj = new URL(url, window.location.origin);
            const page = urlObj.searchParams.get('page');

            const params = {
                category: activeCategory,
                search: searchTerm,
                level: levelFilter,
                instructor: instructorFilter,
                min_price: minPrice,
                max_price: maxPrice,
                sort_by: sortBy,
                page: page
            };

            Object.keys(params).forEach(key => {
                if (key !== 'page' && (params[key] === '' || params[key] === 'all')) {
                    delete params[key];
                }
            });

            router.get(route('edu.courses.index'), params, {
                preserveState: true,
                preserveScroll: true,
            });
        }
    };

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== (filters.search || '')) {
                applyFilters(true);
            }
        }, 500);

        return () => clearTimeout(timeoutId);
    }, [searchTerm]);

    useEffect(() => {
        if (levelFilter !== (filters.level || '') ||
            instructorFilter !== (filters.instructor || '') ||
            minPrice !== (filters.min_price || '') ||
            maxPrice !== (filters.max_price || '')) {
            applyFilters(true);
        }
    }, [levelFilter, instructorFilter, minPrice, maxPrice]);

    return (
        <div className="flex flex-col min-h-screen bg-light-gray">
            <Head title="Danh Mục Khóa Học - PickleAcademy" />

            <EduHeader />

            {/* Main Content */}
            <main className="pt-1">
                <section className="container mx-auto px-4 lg:px-12 py-6">
                {/* Search and Filter Section */}
                <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-6 mb-6">
                    <form onSubmit={handleSearch} className="mb-4">
                        <div className="flex gap-3 items-end">
                            <div className="flex-1">
                                <TextInputWithLabel
                                    id="search-courses"
                                    type="text"
                                    label="Tìm kiếm"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="Tìm kiếm khóa học..."
                                    icon={<Search className="h-4 w-4 text-gray-400" />}
                                    iconPosition="left"
                                />
                            </div>
                            <button
                                type="submit"
                                className="bg-[#ee0033] text-white hover:bg-[#ee0033]/90 px-6 h-10 rounded-md flex items-center justify-center mb-1"
                            >
                                <Search className="h-4 w-4 mr-2" />
                                Tìm kiếm
                            </button>
                        </div>
                    </form>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                        <div>
                            <SelectWithLabel
                                id="level-filter"
                                label="Cấp độ"
                                value={levelFilter}
                                onChange={(e) => setLevelFilter(e.target.value)}
                            >
                                {levelOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="instructor-filter"
                                label="Huấn luyện viên"
                                value={instructorFilter}
                                onChange={(e) => setInstructorFilter(e.target.value)}
                            >
                                {instructorOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <label className="text-sm block mb-1 font-medium text-gray-700">Giá (VNĐ)</label>
                            <div className="flex items-center gap-1">
                                <TextInputWithLabel
                                    id="min-price"
                                    type="number"
                                    value={minPrice}
                                    onChange={(e) => setMinPrice(e.target.value)}
                                    placeholder="Tối thiểu"
                                    className="w-20 text-xs"
                                />
                                <span className="text-gray-500 text-sm mt-6">-</span>
                                <TextInputWithLabel
                                    id="max-price"
                                    type="number"
                                    value={maxPrice}
                                    onChange={(e) => setMaxPrice(e.target.value)}
                                    placeholder="Tối đa"
                                    className="w-20 text-xs"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Reset Filter Button */}
                    <div className="flex justify-end mb-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleResetFilters}
                            className="border-[#ee0033] text-[#ee0033] hover:bg-[#ee0033] hover:text-white"
                        >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Đặt lại bộ lọc
                        </Button>
                    </div>

                    {/* Category Tabs */}
                    <div className="flex flex-wrap gap-3 mt-2">
                        {categories.map(category => (
                            <button
                                key={category.id}
                                onClick={() => handleCategoryChange(category.id)}
                                className={`px-5 py-2 rounded-full font-medium text-sm transition-all duration-300 ${
                                    activeCategory === category.id
                                        ? 'bg-[#ee0033] text-white border border-[#ee0033]'
                                        : 'bg-white border border-gray-300 text-gray-700 hover:border-[#ee0033] hover:text-[#ee0033]'
                                }`}
                            >
                                {category.label} {category.count !== undefined && (
                                    <span className="ml-1 text-xs">({category.count})</span>
                                )}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Results Info */}
                <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-4">
                    <div className="text-gray-600 order-2 md:order-1">
                        Hiển thị <span className="font-semibold text-[#ee0033]">{courses.from || 0}-{courses.to || 0}</span> trong số <span className="font-semibold text-[#ee0033]">{courses.total}</span> khóa học
                    </div>
                    <div className="flex items-center gap-2 w-full md:w-auto order-1 md:order-2">
                        <div className="w-full md:w-auto">
                            <SelectWithLabel
                                id="sort-select"
                                label="Sắp xếp theo"
                                value={sortBy}
                                onChange={(e) => handleSortChange(e.target.value)}
                                className="min-w-[200px]"
                            >
                                {sortOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>
                    </div>
                </div>

                {/* Course Cards */}
                {courses.data && courses.data.length === 0 ? (
                    <div className="text-center py-10">
                        <div className="text-gray-300 mb-4">
                            <Search className="h-14 w-14 mx-auto" />
                        </div>
                        <div className="text-xl text-gray-600 mb-4">Không tìm thấy khóa học phù hợp với tiêu chí tìm kiếm</div>
                        <Button
                            onClick={handleResetFilters}
                            className="bg-[#ee0033] hover:bg-[#ee0033]/90"
                        >
                            Xóa bộ lọc
                        </Button>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 courses-container">
                        {courses.data && courses.data.map(course => (
                            <Link
                                key={course.id}
                                href={route('edu.courses.show', course.slug)}
                                className="bg-white rounded-lg overflow-hidden shadow-[0_4px_15px_rgba(0,0,0,0.05)] transition-all duration-300 hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,0,0,0.15)] block"
                            >
                                <div className="h-[200px] overflow-hidden relative">
                                    <ImageWithFallback
                                        src={course.image}
                                        alt={course.title}
                                        fallbackText={course.title.charAt(0)}
                                        width="w-full"
                                        height="h-full"
                                        imageClassName="object-cover transition-transform duration-500 hover:scale-110"
                                        rounded="rounded-none"
                                        bgColor="bg-gray-100"
                                        textColor="text-[#ee0033]"
                                        textSize="text-2xl"
                                    />
                                    {course.badge && (
                                        <Badge className="absolute top-4 right-4 bg-[#E5B63D] text-[#333333] font-semibold">
                                            {course.badge}
                                        </Badge>
                                    )}
                                    {course.discount_percentage && (
                                        <Badge className="absolute top-4 left-4 bg-red-500 text-white font-semibold">
                                            -{course.discount_percentage}%
                                        </Badge>
                                    )}
                                </div>
                                <div className="p-4">
                                    <span className="text-[#ee0033] text-sm font-medium block mb-2">
                                        {getLevelLabel(course.level)}
                                    </span>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-3 leading-snug line-clamp-2">
                                        {course.title}
                                    </h3>
                                    <div className="flex gap-4 mb-3">
                                        <div className="flex items-center text-sm text-gray-600">
                                            <Video className="h-4 w-4 mr-2 text-[#ee0033]" />
                                            {course.lessons} bài học
                                        </div>
                                        <div className="flex items-center text-sm text-gray-600">
                                            <Clock className="h-4 w-4 mr-2 text-[#ee0033]" />
                                            {course.duration} giờ
                                        </div>
                                    </div>
                                    <div className="flex items-center pt-3 border-t border-gray-200">
                                        <Link
                                            href={route('edu.lecturers.profile', course.instructor.id)}
                                            onClick={(e) => e.stopPropagation()}
                                            className="flex items-center flex-1 group hover:opacity-80 transition-opacity"
                                        >
                                            <div className="w-12 h-12 rounded-full overflow-hidden mr-3">
                                                <ImageWithFallback
                                                    src={course.instructor.avatar}
                                                    alt={course.instructor.name}
                                                    fallbackText={course.instructor.name.charAt(0)}
                                                    width="w-full"
                                                    height="h-full"
                                                    imageClassName="object-cover"
                                                    rounded="rounded-none"
                                                    bgColor="bg-gray-100"
                                                    textColor="text-[#ee0033]"
                                                    textSize="text-sm"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <div className="font-medium text-gray-900 text-sm group-hover:text-[#ee0033] transition-colors flex items-center">
                                                    {course.instructor.name}
                                                </div>
                                            </div>
                                        </Link>
                                        <div className="text-right">
                                            {course.original_price && course.original_price > course.price && (
                                                <div className="text-sm text-gray-500 line-through">
                                                    {formatPrice(course.original_price)}
                                                </div>
                                            )}
                                            <div className="text-xl font-bold text-[#ee0033]">
                                                {formatPrice(course.price)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {courses.links && courses.links.length > 3 && (
                    <div className="mt-8">
                        <Pagination
                            links={courses.links}
                            onPageChange={(url) => handlePageChange(url)}
                            className="justify-center"
                        />
                    </div>
                )}
                </section>
            </main>

            <Footer />
        </div>
    );
}

