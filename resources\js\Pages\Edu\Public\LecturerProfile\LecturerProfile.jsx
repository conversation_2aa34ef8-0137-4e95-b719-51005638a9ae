import React, { useState, useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import {
    Medal,
    Trophy,
    Book,
    Award,
    Mail,
    Phone,
    Facebook,
    Twitter,
    Instagram,
    Youtube,
    Clock,
    Users,
    Star
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { router } from '@inertiajs/react';
import Footer from '@/Components/Landing/Footer';
import EduHeader from '@/Components/Landing/EduHeader';
import ImageWithFallback from '@/Components/ImageWithFallback';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Components/Pagination';

export default function LecturerProfile({ lecturer, courses, reviews, ratingStats }) {

    const urlParams = new URLSearchParams(window.location.search);
    const tabFromUrl = urlParams.get('tab') || 'popular';

    const [activeTab, setActiveTab] = useState(tabFromUrl);
    const [coursesData, setCoursesData] = useState(courses);
    const [reviewsData, setReviewsData] = useState(reviews);
    const [currentRatingStats, setCurrentRatingStats] = useState(ratingStats);

    console.log('Data structure:', {
        courses,
        reviews,
        coursesData,
        reviewsData,
        coursesLinks: coursesData?.links,
        reviewsLinks: reviewsData?.links
    });

    const getCoursesArray = () => {
        if (coursesData?.data) return coursesData.data;
        if (Array.isArray(coursesData)) return coursesData;
        return [];
    };

    const getReviewsArray = () => {
        if (reviewsData?.data) return reviewsData.data;
        if (Array.isArray(reviewsData)) return reviewsData;
        return [];
    };

    useEffect(() => {
        setCoursesData(courses);
    }, [courses]);

    useEffect(() => {
        setReviewsData(reviews);
    }, [reviews]);

    useEffect(() => {
        setCurrentRatingStats(ratingStats);
    }, [ratingStats]);

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const tabFromUrl = urlParams.get('tab');
        if (tabFromUrl && tabFromUrl !== activeTab) {
            setActiveTab(tabFromUrl);
        }
    }, []);

    const achievements = lecturer.achievements?.map((achievement, index) => ({
        icon: [Medal, Trophy, Book, Award][index] || Award,
        title: achievement.title || achievement,
        detail: achievement.detail || achievement.description || ''
    })) || [];

    const formatPrice = (price) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    const certifications = lecturer.certifications || [];

    const getSocialMediaIcon = (type) => {
        if (!type) return null;

        const typeStr = String(type).toLowerCase();

        const icons = {
            facebook: Facebook,
            fb: Facebook,
            twitter: Twitter,
            x: Twitter,
            instagram: Instagram,
            ig: Instagram,
            insta: Instagram,
            youtube: Youtube,
            yt: Youtube,

        };

        if (icons[typeStr]) {
            return icons[typeStr];
        }

        for (const [key, icon] of Object.entries(icons)) {
            if (typeStr.includes(key)) {
                return icon;
            }
        }

        return null;
    };

    const socialLinks = lecturer.social_links ? (
        Array.isArray(lecturer.social_links)
            ? lecturer.social_links
            : Object.entries(lecturer.social_links || {}).map(([type, url]) => ({ type, url }))
    ) : [];

    const renderStars = (rating) => {
        return [...Array(5)].map((_, i) => (
            <Star
                key={i}
                className={`h-4 w-4 ${i < rating ? 'fill-current text-secondary' : 'text-gray-300'}`}
            />
        ));
    };

    const handleCoursesPageChange = (page) => {

        router.get(`/edu/lecturers/${lecturer.id}`,
            { coursePage: page, tab: activeTab },
            {
                preserveState: true,
                preserveScroll: true,
                only: ['courses'],
                onSuccess: (page) => {
                    if (page.props.courses) {
                        setCoursesData(page.props.courses);
                    }
                }
            }
        );
    };

    const handleTabChange = (tab) => {
        setActiveTab(tab);

        router.get(`/edu/lecturers/${lecturer.id}`,
            { tab: tab, coursePage: 1 },
            {
                preserveState: true,
                preserveScroll: true,
                only: ['courses'],
                onSuccess: (page) => {
                    if (page.props.courses) {
                        setCoursesData(page.props.courses);
                    }
                }
            }
        );
    };

    const handleReviewsPageChange = (page) => {

        router.get(`/edu/lecturers/${lecturer.id}`,
            { reviewPage: page },
            {
                preserveState: true,
                preserveScroll: true,
                only: ['reviews', 'ratingStats'],
                onSuccess: (page) => {
                    if (page.props.reviews) {
                        setReviewsData(page.props.reviews);
                    }
                    if (page.props.ratingStats) {
                        setCurrentRatingStats(page.props.ratingStats);
                    }
                }
            }
        );
    };

    return (
        <div className="flex flex-col min-h-screen bg-light-gray">
            <Head title={`${lecturer.name} - Huấn luyện viên Pickleball | PIBA Academy`} />

            <EduHeader />

            <main className="pt-6">
                {/* Hero Section */}
                <section className="bg-[#ee0033] text-white relative overflow-hidden">
                    <div className="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Cpath%20d%3D%22M36%2034v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zM36%206V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6%2034v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6%204V0H4v4H0v2h4v4h2V6h4V4H6z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E')]"></div>

                    <div className="container mx-auto relative z-10 px-4 lg:px-12 py-16">
                        <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-10">
                                                         <div className="w-32 h-32 lg:w-48 lg:h-48 rounded-full overflow-hidden border-4 border-white shadow-[0_5px_15px_rgba(0,0,0,0.3)] flex-shrink-0 mx-auto lg:mx-0">
                                 <ImageWithFallback
                                     src={lecturer.avatar}
                                     alt={lecturer.name}
                                     fallbackText={lecturer.name.charAt(0)}
                                     width="w-full"
                                     height="h-full"
                                     rounded="rounded-none"
                                     bgColor="bg-gray-200"
                                     textColor="text-[#ee0033]"
                                     textSize="text-4xl"
                                 />
                             </div>

                            <div className="flex-1 text-center lg:text-left">
                                                                 <h1 className="text-3xl lg:text-5xl font-bold mb-2 drop-shadow-md">{lecturer.name}</h1>
                                 <p className="text-lg lg:text-xl text-white mb-5 font-medium">{lecturer.title}</p>
                                 <p className="text-sm lg:text-base mb-5 max-w-3xl leading-relaxed text-white/90">{lecturer.brief}</p>

                                                                 <div className="flex flex-wrap justify-center lg:justify-start gap-4 lg:gap-8">
                                     <div className="text-center bg-white/10 px-4 py-2 rounded-lg">
                                         <div className="text-2xl font-bold">{lecturer.stats.courses}+</div>
                                         <div className="text-sm text-white">Khóa học</div>
                                     </div>
                                     <div className="text-center bg-white/10 px-4 py-2 rounded-lg">
                                         <div className="text-2xl font-bold">{lecturer.stats.students}+</div>
                                         <div className="text-sm text-white">Học viên</div>
                                     </div>
                                     <div className="text-center bg-white/10 px-4 py-2 rounded-lg">
                                         <div className="text-2xl font-bold">
                                             {lecturer.stats.rating ? Number(lecturer.stats.rating).toFixed(1) : '0.0'}/5
                                         </div>
                                         <div className="text-sm text-white">Đánh giá</div>
                                     </div>
                                     <div className="text-center bg-white/10 px-4 py-2 rounded-lg">
                                         <div className="text-2xl font-bold">{lecturer.stats.experience}+</div>
                                         <div className="text-sm text-white">Năm kinh nghiệm</div>
                                     </div>
                                 </div>
                            </div>

                                                         {/* Social Media Links - Mobile */}
                             {socialLinks.length > 0 && (
                                <div className="flex lg:hidden justify-center gap-4 mt-6">
                                    {socialLinks.map((social, index) => {
                                        const IconComponent = getSocialMediaIcon(social.type);
                                        if (!IconComponent || !social.url) return null;

                                        return (
                                            <a
                                                key={index}
                                                href={social.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-10 h-10 bg-white/20 hover:bg-white hover:text-[#ee0033] rounded-full flex items-center justify-center transition-colors"
                                            >
                                                <IconComponent className="h-5 w-5" />
                                            </a>
                                        );
                                    })}
                                </div>
                             )}

                             {/* Social Media Links - Desktop */}
                             {socialLinks.length > 0 && (
                                <div className="hidden lg:flex flex-col gap-4">
                                    {socialLinks.map((social, index) => {
                                        const IconComponent = getSocialMediaIcon(social.type);
                                        if (!IconComponent || !social.url) return null;

                                        return (
                                            <a
                                                key={index}
                                                href={social.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-10 h-10 bg-white/20 hover:bg-white hover:text-[#ee0033] rounded-full flex items-center justify-center transition-colors"
                                            >
                                                <IconComponent className="h-5 w-5" />
                                            </a>
                                        );
                                    })}
                                </div>
                             )}
                        </div>
                    </div>
                </section>

                {/* Main Content */}
                <section className="container mx-auto px-4 lg:px-12 py-12">
                    <div className="flex flex-col lg:flex-row gap-8">
                        <div className="flex-1">
                            {/* About Section */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-8 mb-8">
                                <h2 className="text-2xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Giới thiệu
                                </h2>
                                <div className="space-y-5 text-gray-600 leading-relaxed">
                                    <div dangerouslySetInnerHTML={{ __html: lecturer.description }} />
                                </div>
                            </div>

                            {/* Achievements Section */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-8 mb-8">
                                <h2 className="text-2xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Thành tích nổi bật
                                </h2>
                                <div className="space-y-5">
                                    {achievements.map((achievement, index) => {
                                        const IconComponent = achievement.icon;
                                        return (
                                            <div key={index} className="flex gap-4">
                                                <div className="w-10 h-10 rounded-full bg-[#ee0033]/10 text-[#ee0033] flex items-center justify-center flex-shrink-0">
                                                    <IconComponent className="h-5 w-5" />
                                                </div>
                                                <div className="flex-1">
                                                    <h3 className="font-semibold text-dark-gray mb-1">
                                                        {achievement.title}
                                                    </h3>
                                                    <p className="text-text-gray text-sm">
                                                        {achievement.detail}
                                                    </p>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Courses Section */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-8 mb-8">
                                <h2 className="text-2xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Các khóa học
                                </h2>

                                <div className="flex flex-wrap border-b border-border-color mb-5">
                                    <button
                                        onClick={() => handleTabChange('popular')}
                                        className={`px-3 md:px-5 py-2 font-medium relative text-sm md:text-base ${
                                            activeTab === 'popular'
                                                ? 'text-[#ee0033] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-0.5 after:bg-[#ee0033]'
                                                : 'text-text-gray hover:text-[#ee0033]'
                                        }`}
                                    >
                                        Phổ biến nhất
                                    </button>
                                    <button
                                        onClick={() => handleTabChange('newest')}
                                        className={`px-3 md:px-5 py-2 font-medium relative text-sm md:text-base ${
                                            activeTab === 'newest'
                                                ? 'text-[#ee0033] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-0.5 after:bg-[#ee0033]'
                                                : 'text-text-gray hover:text-[#ee0033]'
                                        }`}
                                    >
                                        Mới nhất
                                    </button>
                                    <button
                                        onClick={() => handleTabChange('all')}
                                        className={`px-3 md:px-5 py-2 font-medium relative text-sm md:text-base ${
                                            activeTab === 'all'
                                                ? 'text-[#ee0033] after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-0.5 after:bg-[#ee0033]'
                                                : 'text-text-gray hover:text-[#ee0033]'
                                        }`}
                                    >
                                        Tất cả khóa học
                                    </button>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    {getCoursesArray().map((course) => (
                                        <Link
                                            key={course.id}
                                            href={`/edu/courses/${course.slug}`}
                                            className="bg-white rounded-lg shadow-[0_2px_10px_rgba(0,0,0,0.05)] overflow-hidden transition-transform hover:transform hover:translate-y-[-3px] hover:shadow-[0_5px_15px_rgba(0,0,0,0.1)] flex flex-col h-full"
                                        >
                                            <div className="w-full h-[160px] flex-shrink-0">
                                                <ImageWithFallback
                                                    src={course.image}
                                                    alt={course.title}
                                                    fallbackText={course.title.charAt(0)}
                                                    width="w-full"
                                                    height="h-full"
                                                    rounded="rounded-none"
                                                    bgColor="bg-gray-100"
                                                    textColor="text-[#ee0033]"
                                                    textSize="text-lg"
                                                />
                                            </div>
                                            <div className="flex-1 p-4 flex flex-col justify-between">
                                                <div>
                                                    <Badge variant="outline" className="text-xs mb-2">
                                                        {course.level}
                                                    </Badge>
                                                    <h3 className="font-semibold text-sm mb-2 line-clamp-2 h-10">
                                                        {course.title}
                                                    </h3>
                                                    <div className="flex gap-4 text-xs text-gray-600 mb-2">
                                                        <div className="flex items-center gap-1">
                                                            <Clock className="h-3 w-3" />
                                                            {course.duration}
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Users className="h-3 w-3" />
                                                            {course.students} học viên
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="font-bold text-[#ee0033] text-sm mt-2">
                                                    {formatPrice(course.price)}
                                                </div>
                                            </div>
                                        </Link>
                                    ))}
                                </div>

                                {/* Courses Pagination */}
                                <div className="flex justify-center gap-1 md:gap-2 mt-8">
                                    {coursesData?.links && coursesData.links.length > 0 ? (
                                        <Pagination
                                            links={coursesData.links}
                                            onPageChange={handleCoursesPageChange}
                                            className=""
                                        />
                                    ) : (
                                        <>
                                            <Button variant="outline" size="icon" className="w-8 h-8 md:w-10 md:h-10">
                                                ‹
                                            </Button>
                                            <Button size="icon" className="w-8 h-8 md:w-10 md:h-10 bg-[#ee0033] text-xs md:text-sm">1</Button>
                                            <Button variant="outline" size="icon" className="w-8 h-8 md:w-10 md:h-10">
                                                ›
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>

                            {/* Reviews Section */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-8">
                                <h2 className="text-2xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Đánh giá từ học viên
                                </h2>

                                                                <div className="flex flex-col md:flex-row items-center gap-6 mb-8">
                                    <div className="text-center">
                                        <div className="text-4xl font-bold text-[#ee0033] mb-2">
                                            {currentRatingStats?.average ? Number(currentRatingStats.average).toFixed(1) : '0.0'}
                                        </div>
                                        <div className="flex gap-1 mb-2 justify-center">
                                            {renderStars(Math.round(currentRatingStats?.average || 0))}
                                        </div>
                                        <div className="text-sm text-text-gray">
                                            ({currentRatingStats?.total || 0} đánh giá)
                                        </div>
                                    </div>

                                    {/* Rating breakdown */}
                                    {currentRatingStats?.total > 0 && (
                                        <div className="flex-1 max-w-md">
                                            {[5, 4, 3, 2, 1].map((star) => {
                                                const count = currentRatingStats.breakdown[star] || 0;
                                                const percentage = currentRatingStats.total > 0 ? (count / currentRatingStats.total) * 100 : 0;
                                                return (
                                                    <div key={star} className="flex items-center gap-2 mb-1">
                                                        <span className="text-sm w-8">{star}</span>
                                                        <Star className="h-4 w-4 fill-current text-yellow-400" />
                                                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                                                            <div
                                                                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                                                style={{ width: `${percentage}%` }}
                                                            />
                                                        </div>
                                                        <span className="text-sm text-gray-600 w-8">{count}</span>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-6">
                                    {getReviewsArray().length > 0 ? getReviewsArray().map((review) => (
                                        <div key={review.id} className="border-b border-border-color pb-6 last:border-b-0">
                                            <div className="flex items-start justify-between mb-4">
                                                <div className="flex gap-3">
                                                    <div className="w-10 h-10 rounded-full overflow-hidden">
                                                        <ImageWithFallback
                                                            src={review.avatar}
                                                            alt={review.name}
                                                            fallbackText={review.name.charAt(0)}
                                                            width="w-full"
                                                            height="h-full"
                                                            rounded="rounded-none"
                                                            bgColor="bg-primary"
                                                            textColor="text-white"
                                                            textSize="text-sm"
                                                        />
                                                    </div>
                                                    <div>
                                                        <div className="font-semibold">{review.name}</div>
                                                        <div className="text-sm text-tertiary">{review.course}</div>
                                                        <div className="text-xs text-text-gray">{review.date}</div>
                                                    </div>
                                                </div>
                                                <div className="flex gap-1">
                                                    {renderStars(review.rating)}
                                                </div>
                                            </div>
                                            <p className="text-text-gray leading-relaxed">{review.content}</p>
                                        </div>
                                    )) : (
                                        <div className="text-center py-8 text-gray-500">
                                            <p>Chưa có đánh giá nào.</p>
                                        </div>
                                    )}
                                </div>

                                {/* Reviews Pagination */}
                                <div className="flex justify-center gap-1 md:gap-2 mt-8">
                                    {reviewsData?.links && reviewsData.links.length > 0 ? (
                                        <Pagination
                                            links={reviewsData.links}
                                            onPageChange={handleReviewsPageChange}
                                            className=""
                                        />
                                    ) : (
                                        <>
                                            <Button variant="outline" size="icon" className="w-8 h-8 md:w-10 md:h-10">
                                                ‹
                                            </Button>
                                            <Button size="icon" className="w-8 h-8 md:w-10 md:h-10 bg-[#ee0033] text-xs md:text-sm">1</Button>
                                            <Button variant="outline" size="icon" className="w-8 h-8 md:w-10 md:h-10">
                                                ›
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="w-full lg:w-80">
                            {/* Contact Info */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-6 mb-6">
                                <h3 className="text-xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Thông tin liên hệ
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 rounded-full bg-[#ee0033]/10 text-[#ee0033] flex items-center justify-center">
                                            <Mail className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <div className="text-sm text-text-gray">Email</div>
                                            <div className="font-medium">{lecturer.email}</div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 rounded-full bg-[#ee0033]/10 text-[#ee0033] flex items-center justify-center">
                                            <Phone className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <div className="text-sm text-text-gray">Điện thoại</div>
                                            <div className="font-medium">{lecturer.phone}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Certifications */}
                            <div className="bg-white rounded-lg shadow-[0_4px_15px_rgba(0,0,0,0.05)] p-6">
                                <h3 className="text-xl font-semibold text-[#ee0033] mb-5 pb-4 border-b border-border-color">
                                    Chứng chỉ & Chứng nhận
                                </h3>
                                <div className="space-y-4">
                                    {certifications.map((cert, index) => (
                                        <div key={index} className="flex items-center gap-3 pb-3 border-b border-gray-200 last:border-b-0">
                                            <div className="w-10 h-10 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center flex-shrink-0">
                                                <Award className="h-5 w-5" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="font-medium text-gray-800 text-sm">
                                                    {cert.name || cert.title || cert}
                                                </div>
                                                <div className="text-xs text-gray-600">
                                                    {cert.issuer || cert.organization || ''}
                                                </div>
                                            </div>
                                            <div className="text-sm text-gray-500 font-medium">
                                                {cert.year || cert.date || ''}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

            <Footer />
        </div>
    );
}
