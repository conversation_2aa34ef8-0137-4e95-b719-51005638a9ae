import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import {
    ArrowLeft,
    Save,
    Star,
    User,
    BookOpen,
    Calendar,
    Eye,
    EyeOff
} from 'lucide-react';
import ImageWithFallback from '@/Components/ImageWithFallback';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import Loading from '@/Components/Loading';

export default function Edit({ review = {} }) {
    const { data, setData, put, processing, errors } = useForm({
        is_published: review.is_published || false,
        comment: review.comment || '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('superadmin.edu.reviews.update', review.id));
    };

    const renderStars = (rating) => {
        return [...Array(5)].map((_, i) => (
            <Star
                key={i}
                className={`h-5 w-5 ${i < rating ? 'fill-current text-yellow-500' : 'text-gray-300'}`}
            />
        ));
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <SuperAdminLayout>
            <Head title={`${__('edu.edit_review')} - ${review.student?.user?.name || __('edu.unknown_student')}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={route('superadmin.edu.reviews.index')}
                            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            {__('edu.back_to_reviews')}
                        </Link>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {__('edu.edit_review')}
                        </h1>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Review Information Card */}
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <div className="flex items-start justify-between mb-4">
                                    <div className="flex items-center space-x-4">
                                        <ImageWithFallback
                                            src={review.student?.user?.profile_photo_url}
                                            alt={review.student?.user?.name || 'Student'}
                                            fallbackText={(review.student?.user?.name || 'S').charAt(0).toUpperCase()}
                                            width="w-12"
                                            height="h-12"
                                            rounded="rounded-full"
                                        />
                                        <div>
                                            <h3 className="text-lg font-semibold text-gray-900">
                                                {review.student?.user?.name || __('edu.unknown_student')}
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                {review.student?.user?.email}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <div className="flex items-center space-x-1">
                                            {renderStars(review.rating)}
                                            <span className="ml-2 text-lg font-semibold text-gray-900">
                                                {review.rating}/5
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Review Date */}
                                <div className="mb-4 text-sm text-gray-600">
                                    <Calendar className="h-4 w-4 inline mr-1" />
                                    {formatDate(review.created_at)}
                                </div>

                                {/* Review Comment - Editable */}
                                <div className="mb-6">
                                    <TextInputWithLabel
                                        id="comment"
                                        label={__('edu.review_comment')}
                                        value={data.comment}
                                        onChange={(e) => setData('comment', e.target.value)}
                                        placeholder={__('edu.no_comment_provided')}
                                        rows={6}
                                        multiline={true}
                                        error={errors.comment}
                                    />
                                </div>

                                {/* Publication Status */}
                                <div className="mb-6">
                                    <SelectWithLabel
                                        id="is_published"
                                        label={__('edu.review_status')}
                                        value={data.is_published ? '1' : '0'}
                                        onChange={(e) => setData('is_published', e.target.value === '1')}
                                        error={errors.is_published}
                                        options={[
                                            { value: '1', label: __('edu.published') },
                                            { value: '0', label: __('edu.unpublished') }
                                        ]}
                                    />
                                </div>

                                {/* Form Actions */}
                                <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                                    <Link
                                        href={route('superadmin.edu.reviews.index')}
                                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                    >
                                        {__('edu.cancel')}
                                    </Link>
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        {processing ? (
                                            <Loading className="w-4 h-4 mr-2" />
                                        ) : (
                                            <Save className="w-4 h-4 mr-2" />
                                        )}
                                        {processing ? __('edu.updating') : __('edu.save_changes')}
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Course Information */}
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
                                    {__('edu.course_information')}
                                </h3>

                                {review.course ? (
                                    <div className="space-y-3">
                                        <div>
                                            <Link
                                                href={route('superadmin.edu.courses.show', review.course.id)}
                                                className="text-blue-600 hover:text-blue-800 font-medium"
                                            >
                                                {review.course.title}
                                            </Link>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.level')}:</strong>
                                            <Badge variant="outline" className="ml-2">
                                                {review.course.level}
                                            </Badge>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.price')}:</strong>
                                            <span className="ml-2 font-semibold text-green-600">
                                                {new Intl.NumberFormat('vi-VN', {
                                                    style: 'currency',
                                                    currency: 'VND'
                                                }).format(review.course.price)}
                                            </span>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.enrolled_students')}:</strong>
                                            <span className="ml-2">{review.course.enrolled_students || 0}</span>
                                        </div>
                                    </div>
                                ) : (
                                    <p className="text-gray-500 italic">
                                        {__('edu.course_not_found')}
                                    </p>
                                )}
                            </div>

                            {/* Lecturer Information */}
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <User className="h-5 w-5 mr-2 text-purple-600" />
                                    {__('edu.lecturer_information')}
                                </h3>

                                {review.course?.lecturer ? (
                                    <div className="space-y-3">
                                        <div className="flex items-center space-x-3">
                                            <ImageWithFallback
                                                src={review.course.lecturer.profile_image_url}
                                                alt={review.course.lecturer.user?.name || 'Lecturer'}
                                                fallbackText={(review.course.lecturer.user?.name || 'L').charAt(0).toUpperCase()}
                                                width="w-10"
                                                height="h-10"
                                                rounded="rounded-full"
                                            />
                                            <div>
                                                <Link
                                                    href={route('superadmin.edu.lecturers.show', review.course.lecturer.id)}
                                                    className="text-blue-600 hover:text-blue-800 font-medium"
                                                >
                                                    {review.course.lecturer.user?.name || __('edu.unknown_lecturer')}
                                                </Link>
                                                <p className="text-xs text-gray-600">
                                                    {review.course.lecturer.title}
                                                </p>
                                            </div>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.experience')}:</strong>
                                            <span className="ml-2">{review.course.lecturer.experience_years} {__('edu.years')}</span>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.rating')}:</strong>
                                            <span className="ml-2 flex items-center">
                                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                                <span className="ml-1">{review.course.lecturer.rating?.toFixed(1) || '0.0'}</span>
                                            </span>
                                        </div>
                                    </div>
                                ) : (
                                    <p className="text-gray-500 italic">
                                        {__('edu.unknown_lecturer')}
                                    </p>
                                )}
                            </div>

                            {/* Student Information */}
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <User className="h-5 w-5 mr-2 text-green-600" />
                                    {__('edu.student_information')}
                                </h3>

                                {review.student?.user ? (
                                    <div className="space-y-3">
                                        <div className="flex items-center space-x-3">
                                            <ImageWithFallback
                                                src={review.student.user.profile_photo_url}
                                                alt={review.student.user.name}
                                                fallbackText={review.student.user.name.charAt(0).toUpperCase()}
                                                width="w-10"
                                                height="h-10"
                                                rounded="rounded-full"
                                            />
                                            <div>
                                                <p className="font-medium text-gray-900">
                                                    {review.student.user.name}
                                                </p>
                                                <p className="text-xs text-gray-600">
                                                    {review.student.user.email}
                                                </p>
                                            </div>
                                        </div>

                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.status')}:</strong>
                                            <span className="ml-2">{review.student.status || __('edu.active')}</span>
                                        </div>

                                        {review.student.bio && (
                                            <div className="text-sm text-gray-600">
                                                <strong>Bio:</strong>
                                                <p className="mt-1 text-gray-700">{review.student.bio}</p>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <p className="text-gray-500 italic">
                                        {__('edu.unknown_student')}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
