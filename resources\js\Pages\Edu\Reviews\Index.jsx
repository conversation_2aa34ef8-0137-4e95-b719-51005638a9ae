import React, { useState, useEffect, useCallback } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Checkbox } from '@/Components/ui/checkbox';
import { Search, Filter, Star, Eye, EyeOff, MoreHorizontal } from 'lucide-react';
import { useToast } from '@/Hooks/useToastContext';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Pagination from '@/Components/Pagination';
import StatusBadge from '@/Components/ui/StatusBadge';
import axios from 'axios';
import Loading from '@/Components/Loading';


function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

export default function Index({ reviews: initialReviews = { data: [], links: [], from: 0, to: 0, total: 0 }, courses = [], lecturers = [], filters = {} }) {
    const { processing, flash, csrf_token } = usePage().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [selectedRating, setSelectedRating] = useState(filters.rating || '');
    const [selectedCourse, setSelectedCourse] = useState(filters.course_id || '');
    const [selectedLecturer, setSelectedLecturer] = useState(filters.lecturer_id || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.is_published ?? '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [bulkAction, setBulkAction] = useState('');
    const { addAlert } = useToast();
    const [reviews, setReviews] = useState(initialReviews);

    useEffect(() => {
        if (flash.error) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', flash.error);
        }
        if (flash.success) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', flash.success);
        }
    }, [flash]);


    const debouncedFilter = useCallback(
        debounce((params) => {
            fetchReviews(params);
        }, 300),
        []
    );

    const handleSearch = (e) => {
        e.preventDefault();
        const params = {
            ...filters,
            search: searchQuery,
            rating: selectedRating,
            course_id: selectedCourse,
            lecturer_id: selectedLecturer,
            is_published: selectedStatus,
        };
        fetchReviews(params);
    };

    const handleFilterChange = useCallback(() => {
        const params = {
            ...filters,
            search: searchQuery,
            rating: selectedRating,
            course_id: selectedCourse,
            lecturer_id: selectedLecturer,
            is_published: selectedStatus,
        };
        debouncedFilter(params);
    }, [searchQuery, selectedRating, selectedCourse, selectedLecturer, selectedStatus, filters, debouncedFilter]);


    useEffect(() => {
        handleFilterChange();
    }, [selectedRating, selectedCourse, selectedLecturer, selectedStatus]);


    useEffect(() => {
        if (searchQuery !== (filters.search || '')) {
            handleFilterChange();
        }
    }, [searchQuery]);

    const handleSort = (field, direction) => {
        fetchReviews({
            ...filters,
            sort: field,
            direction
        });
    };

    const confirmDelete = (reviewId) => {
        setIsDeleting(reviewId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteReview = () => {
        if (!isDeleting) return;

        axios.delete(route('superadmin.edu.reviews.destroy', isDeleting), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {
            setReviews(prevReviews => {
                const updatedData = prevReviews.data.filter(review => review.id !== isDeleting);
                const updatedReviews = {
                    ...prevReviews,
                    data: updatedData,
                    total: prevReviews.total - 1
                };

                if (updatedReviews.to) {
                    updatedReviews.to = Math.max(updatedReviews.to - 1, updatedReviews.from);
                }

                return updatedReviews;
            });

            setIsDeleting(null);
            addAlert('success', response.data.message || __('edu.review_deleted_successfully'));
        })
        .catch(error => {
            setIsDeleting(null);
            addAlert('error', error.response?.data?.message || __('edu.delete_failed'));
        });
    };

    const togglePublished = (reviewId) => {
        setIsProcessing(true);

        axios.post(route('superadmin.edu.reviews.toggle-published', reviewId), {}, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {
            setReviews(prevReviews => {
                const updatedData = prevReviews.data.map(review =>
                    review.id === reviewId
                        ? { ...review, is_published: response.data.is_published }
                        : review
                );
                return { ...prevReviews, data: updatedData };
            });

            setIsProcessing(false);
            addAlert('success', response.data.message || __('edu.review_status_updated'));
        })
        .catch(error => {
            setIsProcessing(false);
            addAlert('error', error.response?.data?.message || __('edu.update_failed'));
        });
    };

    const fetchReviews = (params) => {
        setIsProcessing(true);

        axios.get(route('superadmin.edu.reviews.api'), {
            params
        })
        .then(response => {
            setReviews(response.data.data);


            const url = new URL(window.location);
            Object.entries(params).forEach(([key, value]) => {
                if (value) {
                    url.searchParams.set(key, value);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.pushState({}, '', url);

            setIsProcessing(false);
        })
        .catch(error => {
            setIsProcessing(false);
            addAlert('error', error.response?.data?.message || __('edu.fetch_failed'));
        });
    };

    const handleSelectItem = (itemId) => {
        setSelectedItems(prev =>
            prev.includes(itemId)
                ? prev.filter(id => id !== itemId)
                : [...prev, itemId]
        );
    };

    const handleSelectAll = () => {
        if (selectedItems.length === reviews.data.length) {
            setSelectedItems([]);
        } else {
            setSelectedItems(reviews.data.map(review => review.id));
        }
    };

    const handleBulkAction = () => {
        if (!bulkAction || selectedItems.length === 0) return;

        setIsProcessing(true);

        axios.post(route('superadmin.edu.reviews.bulk-update'), {
            action: bulkAction,
            review_ids: selectedItems
        }, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {
            fetchReviews(filters);
            setSelectedItems([]);
            setBulkAction('');
            setIsProcessing(false);
            addAlert('success', response.data.message);
        })
        .catch(error => {
            setIsProcessing(false);
            addAlert('error', error.response?.data?.message || __('edu.bulk_update_failed'));
        });
    };

    const renderStars = (rating) => {
        return [...Array(5)].map((_, i) => (
            <Star
                key={i}
                className={`h-4 w-4 ${i < rating ? 'fill-current text-yellow-500' : 'text-gray-300'}`}
            />
        ));
    };

    const columns = [
        {
            field: 'select',
            label: (
                <Checkbox
                    checked={selectedItems.length === reviews.data.length && reviews.data.length > 0}
                    onCheckedChange={handleSelectAll}
                />
            ),
            render: (review) => (
                <Checkbox
                    checked={selectedItems.includes(review.id)}
                    onCheckedChange={() => handleSelectItem(review.id)}
                />
            )
        },
        {
            field: 'student.user.name',
            label: __('edu.student'),
            sortable: true,
            render: (review) => (
                <div className="flex items-center space-x-3">
                    <ImageWithFallback
                        src={review.student?.user?.profile_photo_url}
                        alt={review.student?.user?.name || 'Student'}
                        fallbackText={(review.student?.user?.name || 'S').charAt(0).toUpperCase()}
                        width="w-8"
                        height="h-8"
                        rounded="rounded-full"
                    />
                    <div>
                        <div className="font-medium text-gray-900">
                            {review.student?.user?.name || __('edu.unknown_student')}
                        </div>
                        <div className="text-xs text-gray-500">
                            {review.student?.user?.email}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'course.title',
            label: __('edu.course'),
            sortable: true,
            render: (review) => (
                <div>
                    <div className="font-medium text-gray-900 max-w-48 truncate">
                        {review.course?.title || __('edu.unknown_course')}
                    </div>
                    <div className="text-xs text-gray-500">
                        {review.course?.lecturer?.user?.name}
                    </div>
                </div>
            )
        },
        {
            field: 'rating',
            label: __('edu.rating'),
            sortable: true,
            render: (review) => (
                <div className="flex items-center space-x-1">
                    {renderStars(review.rating)}
                    <span className="ml-2 text-sm font-medium">{review.rating}/5</span>
                </div>
            )
        },
        {
            field: 'comment',
            label: __('edu.comment'),
            render: (review) => (
                <div className="max-w-xs">
                    <p className="text-sm text-gray-600 line-clamp-2">
                        {review.comment || __('edu.no_comment')}
                    </p>
                </div>
            )
        },
        {
            field: 'is_published',
            label: __('edu.status'),
            render: (review) => (
                <div className="flex items-center space-x-2">
                    <StatusBadge
                        text={review.is_published ? __('edu.published') : __('edu.unpublished')}
                        color={review.is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                    />
                    <button
                        onClick={() => togglePublished(review.id)}
                        className="p-1 hover:bg-gray-100 rounded"
                        title={review.is_published ? __('edu.unpublish') : __('edu.publish')}
                    >
                        {review.is_published ? (
                            <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                            <Eye className="h-4 w-4 text-green-500" />
                        )}
                    </button>
                </div>
            )
        },
        {
            field: 'created_at',
            label: __('edu.date'),
            sortable: true,
            render: (review) => (
                <div className="text-sm text-gray-600">
                    {new Date(review.created_at).toLocaleDateString('vi-VN')}
                </div>
            )
        },
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('edu.review_management')} />

            <div className="bg-white rounded-lg shadow-md relative">
                {isProcessing && <Loading overlay text={__('edu.loading')} />}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center mb-4">
                        <h1 className="text-xl font-semibold text-gray-900">{__('edu.review_management')}</h1>

                        {/* Bulk Actions */}
                        {selectedItems.length > 0 && (
                            <div className="flex items-center space-x-2">
                                <SelectWithLabel
                                    id="bulk-action"
                                    value={bulkAction}
                                    onChange={(e) => setBulkAction(e.target.value)}
                                    className="min-w-[150px]"
                                >
                                    <option value="">{__('edu.select_action')}</option>
                                    <option value="publish">{__('edu.publish')}</option>
                                    <option value="unpublish">{__('edu.unpublish')}</option>
                                    <option value="delete">{__('edu.delete')}</option>
                                </SelectWithLabel>
                                <Button
                                    onClick={handleBulkAction}
                                    disabled={!bulkAction}
                                    className="bg-blue-600 hover:bg-blue-700"
                                >
                                    {__('edu.apply')} ({selectedItems.length})
                                </Button>
                            </div>
                        )}
                    </div>

                    {/* Search and Filters */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <form onSubmit={handleSearch} className="w-full">
                                <div className="relative">
                                    <TextInputWithLabel
                                        id="search"
                                        type="text"
                                        label={__('edu.search_reviews')}
                                        placeholder={__('edu.search_reviews_placeholder')}
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                    <div className="absolute top-9 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <button type="submit" className="hidden">{__('edu.search')}</button>
                                </div>
                            </form>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="rating-filter"
                                label={__('edu.rating')}
                                value={selectedRating}
                                onChange={(e) => setSelectedRating(e.target.value)}
                            >
                                <option value="">{__('edu.all_ratings')}</option>
                                <option value="5">5 {__('edu.stars')}</option>
                                <option value="4">4 {__('edu.stars')}</option>
                                <option value="3">3 {__('edu.stars')}</option>
                                <option value="2">2 {__('edu.stars')}</option>
                                <option value="1">1 {__('edu.star')}</option>
                            </SelectWithLabel>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="status-filter"
                                label={__('edu.status')}
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                            >
                                <option value="">{__('edu.all_status')}</option>
                                <option value="1">{__('edu.published')}</option>
                                <option value="0">{__('edu.unpublished')}</option>
                            </SelectWithLabel>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="course-filter"
                                label={__('edu.course')}
                                value={selectedCourse}
                                onChange={(e) => setSelectedCourse(e.target.value)}
                            >
                                <option value="">{__('edu.all_courses')}</option>
                                {courses.map(course => (
                                    <option key={course.id} value={course.id}>
                                        {course.title}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <SelectWithLabel
                                id="lecturer-filter"
                                label={__('edu.lecturer')}
                                value={selectedLecturer}
                                onChange={(e) => setSelectedLecturer(e.target.value)}
                            >
                                <option value="">{__('edu.all_lecturers')}</option>
                                {lecturers.map(lecturer => (
                                    <option key={lecturer.id} value={lecturer.id}>
                                        {lecturer.user?.name}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>
                    </div>
                </div>

                <DataTable
                    data={reviews?.data || []}
                    columns={columns}
                    onSort={handleSort}
                    sortField={filters?.sort || 'created_at'}
                    sortDirection={filters?.direction || 'desc'}
                    emptyStateMessage={__('edu.no_reviews_found')}
                    primaryKey="id"
                    viewRoute="superadmin.edu.reviews.show"
                    editRoute="superadmin.edu.reviews.edit"
                    deleteCallback={confirmDelete}
                    cancelDeletion={cancelDelete}
                    loading={isProcessing}
                />

                <ConfirmDeleteModal
                    isOpen={isDeleting !== null}
                    onClose={cancelDelete}
                    onConfirm={deleteReview}
                    title={__('edu.delete_review')}
                    message={__('edu.delete_review_confirmation')}
                    isProcessing={isProcessing}
                />

                {reviews?.links && reviews.links.length > 3 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-700">
                                {__('edu.showing')} {reviews?.from || 0} {__('edu.to')} {reviews?.to || 0} {__('edu.of')} {reviews?.total || 0} {__('edu.reviews')?.toLowerCase() || 'reviews'}
                            </p>
                            <Pagination
                                links={reviews?.links || []}
                                preserveState={true}
                                preserveScroll={true}
                            />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
