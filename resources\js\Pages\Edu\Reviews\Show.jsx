import React, { useState } from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import {
    ArrowLeft,
    Edit,
    Trash2,
    Star,
    User,
    BookOpen,
    Calendar,
    Eye,
    EyeOff
} from 'lucide-react';
import ImageWithFallback from '@/Components/ImageWithFallback';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import StatusBadge from '@/Components/ui/StatusBadge';

export default function Show({ review = {} }) {
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = () => {
        setIsDeleting(true);
        router.delete(route('superadmin.edu.reviews.destroy', review.id), {
            onSuccess: () => {
                // 
            },
            onError: () => {
                setIsDeleting(false);
                setShowDeleteModal(false);
            },
            onFinish: () => {
                setIsDeleting(false);
                setShowDeleteModal(false);
            }
        });
    };

    const cancelDelete = () => {
        setShowDeleteModal(false);
    };

    const renderStars = (rating) => {
        return [...Array(5)].map((_, i) => (
            <Star
                key={i}
                className={`h-5 w-5 ${i < rating ? 'fill-current text-yellow-500' : 'text-gray-300'}`}
            />
        ));
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <SuperAdminLayout>
            <Head title={`${__('edu.review_details')} - ${review.student?.user?.name || __('edu.unknown_student')}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={route('superadmin.edu.reviews.index')}
                            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            {__('edu.back_to_reviews')}
                        </Link>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {__('edu.review_details')}
                        </h1>
                    </div>

                    <div className="flex items-center space-x-3">
                        <Link
                            href={route('superadmin.edu.reviews.edit', review.id)}
                            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                        >
                            <Edit className="w-4 h-4 mr-2" />
                            {__('edu.edit_review')}
                        </Link>
                        <Button
                            onClick={() => setShowDeleteModal(true)}
                            variant="destructive"
                            className="flex items-center"
                        >
                            <Trash2 className="w-4 h-4 mr-2" />
                            {__('edu.delete_review')}
                        </Button>
                    </div>
                </div>

                {/* Review Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Review Card */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex items-start justify-between mb-4">
                                <div className="flex items-center space-x-4">
                                    <ImageWithFallback
                                        src={review.student?.user?.profile_photo_url}
                                        alt={review.student?.user?.name || 'Student'}
                                        fallbackText={(review.student?.user?.name || 'S').charAt(0).toUpperCase()}
                                        width="w-12"
                                        height="h-12"
                                        rounded="rounded-full"
                                    />
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900">
                                            {review.student?.user?.name || __('edu.unknown_student')}
                                        </h3>
                                        <p className="text-sm text-gray-600">
                                            {review.student?.user?.email}
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-center space-x-3">
                                    <div className="flex items-center space-x-1">
                                        {renderStars(review.rating)}
                                        <span className="ml-2 text-lg font-semibold text-gray-900">
                                            {review.rating}/5
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Review Comment */}
                            <div className="mb-4">
                                <h4 className="text-sm font-medium text-gray-700 mb-2">
                                    {__('edu.review_comment')}:
                                </h4>
                                {review.comment ? (
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                                            {review.comment}
                                        </p>
                                    </div>
                                ) : (
                                    <div className="bg-gray-50 rounded-lg p-4">
                                        <p className="text-gray-500 italic">
                                            {__('edu.no_comment_provided')}
                                        </p>
                                    </div>
                                )}
                            </div>

                            {/* Review Status */}
                            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-700">
                                        {__('edu.status')}:
                                    </span>
                                    <StatusBadge
                                        text={review.is_published ? __('edu.published') : __('edu.unpublished')}
                                        color={review.is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                                    />
                                    {review.is_published ? (
                                        <Eye className="h-4 w-4 text-green-500" />
                                    ) : (
                                        <EyeOff className="h-4 w-4 text-gray-500" />
                                    )}
                                </div>

                                <div className="text-sm text-gray-600">
                                    <Calendar className="h-4 w-4 inline mr-1" />
                                    {formatDate(review.created_at)}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Course Information */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
                                {__('edu.course_information')}
                            </h3>

                            {review.course ? (
                                <div className="space-y-3">
                                    <div>
                                        <Link
                                            href={route('superadmin.edu.courses.show', review.course.id)}
                                            className="text-blue-600 hover:text-blue-800 font-medium"
                                        >
                                            {review.course.title}
                                        </Link>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.level')}:</strong>
                                        <Badge variant="outline" className="ml-2">
                                            {review.course.level}
                                        </Badge>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.price')}:</strong>
                                        <span className="ml-2 font-semibold text-green-600">
                                            {new Intl.NumberFormat('vi-VN', {
                                                style: 'currency',
                                                currency: 'VND'
                                            }).format(review.course.price)}
                                        </span>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.students_enrolled')}:</strong>
                                        <span className="ml-2">{review.course.enrolled_students || 0}</span>
                                    </div>
                                </div>
                            ) : (
                                <p className="text-gray-500 italic">
                                    {__('edu.course_not_found')}
                                </p>
                            )}
                        </div>

                        {/* Lecturer Information */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <User className="h-5 w-5 mr-2 text-purple-600" />
                                {__('edu.lecturer_information')}
                            </h3>

                            {review.course?.lecturer ? (
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3">
                                        <ImageWithFallback
                                            src={review.course.lecturer.profile_image_url}
                                            alt={review.course.lecturer.user?.name || 'Lecturer'}
                                            fallbackText={(review.course.lecturer.user?.name || 'L').charAt(0).toUpperCase()}
                                            width="w-10"
                                            height="h-10"
                                            rounded="rounded-full"
                                        />
                                        <div>
                                            <Link
                                                href={route('superadmin.edu.lecturers.show', review.course.lecturer.id)}
                                                className="text-blue-600 hover:text-blue-800 font-medium"
                                            >
                                                {review.course.lecturer.user?.name}
                                            </Link>
                                            <p className="text-sm text-gray-600">
                                                {review.course.lecturer.title}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.experience')}:</strong>
                                        <span className="ml-2">
                                            {review.course.lecturer.experience_years || 0} {__('edu.years')}
                                        </span>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.total_courses')}:</strong>
                                        <span className="ml-2">{review.course.lecturer.total_courses || 0}</span>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.rating')}:</strong>
                                        <span className="ml-2 flex items-center">
                                            <Star className="h-4 w-4 fill-current text-yellow-500 mr-1" />
                                            {review.course.lecturer.rating ?
                                                review.course.lecturer.rating.toFixed(1) : '0.0'
                                            } / 5
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <p className="text-gray-500 italic">
                                    {__('edu.lecturer_not_found')}
                                </p>
                            )}
                        </div>

                        {/* Student Information */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <User className="h-5 w-5 mr-2 text-green-600" />
                                {__('edu.student_information')}
                            </h3>

                            {review.student ? (
                                <div className="space-y-3">
                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.email')}:</strong>
                                        <span className="ml-2">{review.student.user?.email}</span>
                                    </div>

                                    <div className="text-sm text-gray-600">
                                        <strong>{__('edu.joined_date')}:</strong>
                                        <span className="ml-2">
                                            {formatDate(review.student.created_at)}
                                        </span>
                                    </div>

                                    {review.student.bio && (
                                        <div className="text-sm text-gray-600">
                                            <strong>{__('edu.bio')}:</strong>
                                            <p className="ml-2 mt-1 text-gray-700">
                                                {review.student.bio}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-gray-500 italic">
                                    {__('edu.student_not_found')}
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <ConfirmDeleteModal
                isOpen={showDeleteModal}
                onClose={cancelDelete}
                onConfirm={handleDelete}
                title={__('edu.delete_review')}
                message={__('edu.delete_review_confirmation')}
                isProcessing={isDeleting}
            />
        </SuperAdminLayout>
    );
}
