import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Head, Link, useForm, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload, Plus, Trash2, Package, Search } from 'lucide-react';

export default function Edit({ bundle, products }) {
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(bundle.image_url_formatted);
    const { addAlert } = useToast();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [selectedProducts, setSelectedProducts] = useState(
        bundle.bundle_items?.map(item => ({
            id: item.product.id,
            name: item.product.name,
            sale_price: item.product.sale_price,
            quantity: item.quantity,
            image_url: item.product.image_url,
            category: item.product.category,
            options: item.product_options || {}
        })) || []
    );

    const categories = useMemo(() => {
        const uniqueCategories = [];
        const seen = new Set();
        products.forEach(product => {
            if (product.category && !seen.has(product.category.id)) {
                seen.add(product.category.id);
                uniqueCategories.push(product.category);
            }
        });
        return uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));
    }, [products]);

    const filteredProducts = useMemo(() => {
        return products.filter(product => {
            if (selectedProducts.some(selected => selected.id === product.id)) {
                return false;
            }

            if (searchQuery) {
                const query = searchQuery.toLowerCase();
                const matchesName = product.name.toLowerCase().includes(query);
                const matchesCategory = product.category?.name.toLowerCase().includes(query);
                if (!matchesName && !matchesCategory) {
                    return false;
                }
            }

            if (selectedCategory && product.category?.id.toString() !== selectedCategory) {
                return false;
            }

            return true;
        });
    }, [products, selectedProducts, searchQuery, selectedCategory]);

    const { data, setData, put, processing, errors, reset } = useForm({
        name: bundle.name || '',
        description: bundle.description || '',
        bundle_price: bundle.bundle_price || '',
        is_active: bundle.is_active || false,
        is_featured: bundle.is_featured || false,
        starts_at: bundle.starts_at ? new Date(bundle.starts_at).toISOString().slice(0, 16) : '',
        expires_at: bundle.expires_at ? new Date(bundle.expires_at).toISOString().slice(0, 16) : '',
        stock_quantity: bundle.stock_quantity || '',
        image_url: '',
        products: [],
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        if (selectedProducts.length < 2) {
            addAlert('error', __('product.minimum_products_required'));
            return;
        }

        const productData = selectedProducts.map(product => ({
            product_id: product.id,
            quantity: product.quantity,
            product_options: product.options || null
        }));

        const formData = new FormData();

        formData.append('name', data.name);
        formData.append('description', data.description || '');
        formData.append('bundle_price', data.bundle_price);
        formData.append('is_active', data.is_active ? '1' : '0');
        formData.append('is_featured', data.is_featured ? '1' : '0');
        formData.append('starts_at', data.starts_at || '');
        formData.append('expires_at', data.expires_at || '');
        formData.append('stock_quantity', data.stock_quantity);

        if (data.image_url && data.image_url instanceof File) {
            formData.append('image_url', data.image_url);
        }

        formData.append('_method', 'PUT');

        productData.forEach((product, index) => {
            formData.append(`products[${index}][product_id]`, product.product_id);
            formData.append(`products[${index}][quantity]`, product.quantity);
            if (product.product_options && typeof product.product_options === 'object' && Object.keys(product.product_options).length > 0) {
                Object.keys(product.product_options).forEach(key => {
                    if (product.product_options[key] !== null && product.product_options[key] !== undefined) {
                        formData.append(`products[${index}][product_options][${key}]`, product.product_options[key]);
                    }
                });
            }
        });

        router.post(route('superadmin.marketplace.bundles.update', bundle.id), formData, {
            onSuccess: () => {
                addAlert('success', __('product.bundle_updated'));
            },
            onError: (errors) => {
                if (errors.name) {
                    addAlert('error', errors.name);
                } else if (errors.products) {
                    addAlert('error', errors.products);
                } else if (errors.error) {
                    addAlert('error', errors.error);
                } else {
                    addAlert('error', 'Có lỗi xảy ra khi cập nhật bundle. Vui lòng thử lại.');
                }
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image_url', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('image_url', '');
        setImagePreview(bundle.image_url);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    const addProduct = (product) => {
        if (selectedProducts.find(p => p.id === product.id)) {
            addAlert('warning', __('product.product_already_added', 'Product already added to bundle'));
            return;
        }

        setSelectedProducts(prev => [...prev, {
            ...product,
            quantity: 1,
            options: {}
        }]);
    };

    const removeProduct = (productId) => {
        setSelectedProducts(prev => prev.filter(p => p.id !== productId));
    };

    const updateProductQuantity = (productId, quantity) => {
        if (quantity < 1) return;

        setSelectedProducts(prev =>
            prev.map(p =>
                p.id === productId
                    ? { ...p, quantity: parseInt(quantity) || 1 }
                    : p
            )
        );
    };

    const calculateOriginalPrice = () => {
        return selectedProducts.reduce((total, product) => {
            return total + (product.sale_price * product.quantity);
        }, 0);
    };

    const calculateSavings = () => {
        const originalPrice = calculateOriginalPrice();
        const bundlePrice = parseFloat(data.bundle_price) || 0;
        return Math.max(0, originalPrice - bundlePrice);
    };

    const calculateDiscountPercentage = () => {
        const originalPrice = calculateOriginalPrice();
        const savings = calculateSavings();
        return originalPrice > 0 ? ((savings / originalPrice) * 100).toFixed(2) : 0;
    };

    return (
        <SuperAdminLayout>
            <Head title={__('product.edit_bundle')} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('product.edit_bundle')}: {bundle.name}</h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.marketplace.bundles.show', bundle.id)}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                {__('common.view')}
                            </Link>
                            <Link
                                href={route('superadmin.marketplace.bundles.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('common.back')}
                            </Link>
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Left Column - Basic Information */}
                        <div className="space-y-6">
                            <div>
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('common.basic_information')}</h2>

                                <div className="space-y-4">
                                    <TextInputWithLabel
                                        label={__('product.bundle_name')}
                                        name="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        error={errors.name}
                                        required
                                    />

                                    <TextareaWithLabel
                                        label={__('product.bundle_description')}
                                        name="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        error={errors.description}
                                        rows={4}
                                    />

                                    <TextInputWithLabel
                                        label={__('product.bundle_price')}
                                        name="bundle_price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.bundle_price}
                                        onChange={(e) => setData('bundle_price', e.target.value)}
                                        error={errors.bundle_price}
                                        required
                                    />

                                    <TextInputWithLabel
                                        label={__('product.bundle_stock')}
                                        name="stock_quantity"
                                        type="number"
                                        min="0"
                                        value={data.stock_quantity}
                                        onChange={(e) => setData('stock_quantity', e.target.value)}
                                        error={errors.stock_quantity}
                                        required
                                    />
                                </div>
                            </div>

                            {/* Image Upload */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {__('common.image')}
                                </label>
                                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div className="space-y-1 text-center">
                                        {imagePreview ? (
                                            <div className="relative">
                                                <img
                                                    src={imagePreview}
                                                    alt="Preview"
                                                    className="mx-auto h-32 w-32 object-cover rounded-lg"
                                                />
                                                <button
                                                    type="button"
                                                    onClick={clearImage}
                                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                                                >
                                                    <XCircle className="w-4 h-4" />
                                                </button>
                                            </div>
                                        ) : (
                                            <>
                                                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                                <div className="flex text-sm text-gray-600">
                                                    <label
                                                        htmlFor="image-upload"
                                                        className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                                                    >
                                                        <span>{__('common.upload_file')}</span>
                                                        <input
                                                            id="image-upload"
                                                            ref={imageInputRef}
                                                            name="image_url"
                                                            type="file"
                                                            className="sr-only"
                                                            accept="image/*"
                                                            onChange={handleImageChange}
                                                        />
                                                    </label>
                                                    <p className="pl-1">{__('common.or_drag_drop')}</p>
                                                </div>
                                                <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                                            </>
                                        )}
                                    </div>
                                </div>
                                {errors.image_url && <p className="mt-1 text-sm text-red-600">{errors.image_url}</p>}
                            </div>

                            {/* Settings */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-4">{__('common.settings')}</h3>
                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <TextInputWithLabel
                                            label={__('product.bundle_start_date')}
                                            name="starts_at"
                                            type="datetime-local"
                                            value={data.starts_at}
                                            onChange={(e) => setData('starts_at', e.target.value)}
                                            error={errors.starts_at}
                                        />

                                        <TextInputWithLabel
                                            label={__('product.bundle_end_date')}
                                            name="expires_at"
                                            type="datetime-local"
                                            value={data.expires_at}
                                            onChange={(e) => setData('expires_at', e.target.value)}
                                            error={errors.expires_at}
                                        />
                                    </div>

                                    <div className="flex items-center space-x-6">
                                        <label className="flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={data.is_active}
                                                onChange={(e) => setData('is_active', e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-900">{__('product.bundle_active')}</span>
                                        </label>

                                        <label className="flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={data.is_featured}
                                                onChange={(e) => setData('is_featured', e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-900">{__('product.featured_bundle')}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Bundle Stats */}
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h3 className="text-sm font-medium text-gray-900 mb-2">{__('common.statistics')}</h3>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-gray-500">{__('common.created_at')}:</span>
                                        <div className="font-medium">{new Date(bundle.created_at).toLocaleDateString()}</div>
                                    </div>
                                    <div>
                                        <span className="text-gray-500">{__('common.updated_at')}:</span>
                                        <div className="font-medium">{new Date(bundle.updated_at).toLocaleDateString()}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Product Selection */}
                        <div className="space-y-6">
                            <div>
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('product.select_products')}</h2>

                                {/* Selected Products */}
                                {selectedProducts.length > 0 && (
                                    <div className="mb-6">
                                        <h3 className="text-sm font-medium text-gray-900 mb-3">{__('product.bundle_items')}</h3>
                                        <div className="space-y-3">
                                            {selectedProducts.map((product) => (
                                                <div key={product.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                                                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 mr-3">
                                                        <img
                                                            src={product.image_url}
                                                            alt={product.name}
                                                            className="w-full h-full object-cover"
                                                            onError={(e) => {
                                                                e.target.style.display = 'none';
                                                                e.target.nextSibling.style.display = 'flex';
                                                            }}
                                                        />
                                                        <div className="w-full h-full flex items-center justify-center" style={{display: 'none'}}>
                                                            <Package className="w-6 h-6 text-gray-400" />
                                                        </div>
                                                    </div>

                                                    <div className="flex-1">
                                                        <div className="font-medium text-sm">{product.name}</div>
                                                        <div className="text-xs text-gray-500">
                                                            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(product.sale_price)}
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="number"
                                                            min="1"
                                                            value={product.quantity}
                                                            onChange={(e) => updateProductQuantity(product.id, e.target.value)}
                                                            className="w-16 px-2 py-1 text-sm border border-gray-300 rounded"
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => removeProduct(product.id)}
                                                            className="text-red-600 hover:text-red-800"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        {/* Pricing Summary */}
                                        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                                            <div className="text-sm space-y-1">
                                                <div className="flex justify-between">
                                                    <span>{__('product.original_price')}:</span>
                                                    <span>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(calculateOriginalPrice())}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span>{__('product.bundle_price')}:</span>
                                                    <span>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(parseFloat(data.bundle_price) || 0)}</span>
                                                </div>
                                                <div className="flex justify-between text-green-600 font-medium">
                                                    <span>{__('product.bundle_savings')}:</span>
                                                    <span>
                                                        {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(calculateSavings())}
                                                        ({calculateDiscountPercentage()}%)
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Available Products */}
                                <div>
                                    <h3 className="text-sm font-medium text-gray-900 mb-3">{__('product.available_products')}</h3>

                                    <div className="mb-4 space-y-3">
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <Search className="h-4 w-4 text-gray-400" />
                                            </div>
                                            <input
                                                type="text"
                                                placeholder={__('product.search_products')}
                                                value={searchQuery}
                                                onChange={(e) => setSearchQuery(e.target.value)}
                                                className="block w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                            />
                                        </div>

                                        <select
                                            value={selectedCategory}
                                            onChange={(e) => setSelectedCategory(e.target.value)}
                                            className="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="">{__('product.all_categories')}</option>
                                            {categories.map((category) => (
                                                <option key={category.id} value={category.id.toString()}>
                                                    {category.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                                        {filteredProducts.length > 0 ? (
                                            filteredProducts.map((product) => (
                                                <div key={product.id} className="flex items-center p-3 border-b border-gray-100 last:border-b-0">
                                                    <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-200 mr-3">
                                                        <img
                                                            src={product.image_url}
                                                            alt={product.name}
                                                            className="w-full h-full object-cover"
                                                            onError={(e) => {
                                                                e.target.style.display = 'none';
                                                                e.target.nextSibling.style.display = 'flex';
                                                            }}
                                                        />
                                                        <div className="w-full h-full flex items-center justify-center" style={{display: 'none'}}>
                                                            <Package className="w-5 h-5 text-gray-400" />
                                                        </div>
                                                    </div>

                                                    <div className="flex-1">
                                                        <div className="font-medium text-sm">{product.name}</div>
                                                        <div className="text-xs text-gray-500">
                                                            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(product.sale_price)}
                                                            {product.category && ` • ${product.category.name}`}
                                                        </div>
                                                        <div className="text-xs text-gray-500">
                                                            {__('common.stock')}: {product.quantity}
                                                        </div>
                                                    </div>

                                                    <button
                                                        type="button"
                                                        onClick={() => addProduct(product)}
                                                        disabled={selectedProducts.find(p => p.id === product.id) || product.quantity === 0}
                                                        className="text-blue-600 hover:text-blue-800 disabled:text-gray-400"
                                                    >
                                                        <Plus className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="p-6 text-center text-gray-500">
                                                <Package className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                                                <p className="text-sm">
                                                    {searchQuery || selectedCategory
                                                        ? __('product.no_products_found')
                                                        : __('product.no_available_products')
                                                    }
                                                </p>
                                                {(searchQuery || selectedCategory) && (
                                                    <button
                                                        type="button"
                                                        onClick={() => {
                                                            setSearchQuery('');
                                                            setSelectedCategory('');
                                                        }}
                                                        className="mt-2 text-xs text-blue-600 hover:text-blue-800"
                                                    >
                                                        {__('common.clear_filters')}
                                                    </button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Submit Buttons */}
                    <div className="mt-8 flex justify-end space-x-3 pt-6 border-t border-gray-200">
                        <Link
                            href={route('superadmin.marketplace.bundles.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('common.cancel')}
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing || selectedProducts.length < 2}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
                        >
                            {processing ? __('common.updating') : __('product.update_bundle')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
