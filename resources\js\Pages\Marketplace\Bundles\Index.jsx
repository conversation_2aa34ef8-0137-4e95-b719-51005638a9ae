import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Search, Filter } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import ImageWithFallback from '@/Components/ImageWithFallback';
import PrimaryButton from '@/Components/PrimaryButton';
import Pagination from '@/Components/Pagination';
import StatusBadge from '@/Components/ui/StatusBadge';
import Loading from '@/Components/Loading';

export default function Index({ bundles, filters }) {
    const { processing, flash, csrf_token } = usePage().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const { addAlert } = useToast();
    const [selectedStatus, setSelectedStatus] = useState(filters.status ?? '');
    const [selectedFeatured, setSelectedFeatured] = useState(filters.is_featured ?? '');

    useEffect(() => {
        if (flash.error) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', flash.error);
        }
        if (flash.success) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', flash.success);
        }
    }, [flash]);

    const handleSearch = (e) => {
        e.preventDefault();
        applyFilters({
            search: searchQuery,
            status: selectedStatus,
            is_featured: selectedFeatured
        });
    };

    const handleStatusFilter = (status) => {
        setSelectedStatus(status);
        applyFilters({
            status,
            is_featured: selectedFeatured,
        });
    };

    const handleFeaturedFilter = (isFeatured) => {
        setSelectedFeatured(isFeatured);
        applyFilters({
            status: selectedStatus,
            is_featured: isFeatured,
        });
    };

    const handleSort = (field, direction) => {
        applyFilters({ sort: field, direction });
    };

    const applyFilters = (newFilters) => {
        const filteredParams = Object.fromEntries(
            Object.entries({ ...filters, ...newFilters }).filter(([key, value]) =>
                value !== null && value !== undefined && value !== ''
            )
        );

        router.get(route('superadmin.marketplace.bundles.index'), filteredParams, {
            preserveState: true,
            preserveScroll: true,
            only: ['bundles']
        });
    };

    const confirmDelete = (bundleId) => {
        setIsDeleting(bundleId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteBundle = () => {
        if (!isDeleting) return;

        setIsProcessing(true);

        router.delete(route('superadmin.marketplace.bundles.destroy', isDeleting), {
            onSuccess: () => {
                setIsProcessing(false);
                setIsDeleting(null);
                addAlert('success', __('product.bundle_deleted'));
            },
            onError: (errors) => {
                setIsProcessing(false);
                setIsDeleting(null);
                addAlert('error', errors.message || __('marketplace.delete_failed'));
            }
        });
    };

    const columns = [
        {
            field: 'name',
            label: __('product.bundle_name'),
            sortable: true,
            render: (bundle) => (
                <div className="flex items-center space-x-3">
                    <ImageWithFallback
                        src={bundle.image_url_formatted}
                        alt={bundle.name}
                        fallbackText={bundle.name.charAt(0).toUpperCase()}
                        width="w-10"
                        height="h-10"
                    />
                    <div>
                        <div className="font-medium text-gray-900">{bundle.name}</div>
                        <div className="text-xs text-gray-500">
                            {__('product.bundle_items')}: {bundle.bundle_items_count || 0}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'bundle_price',
            label: __('product.bundle_price'),
            sortable: true,
            render: (bundle) => (
                <div>
                    <div className="font-medium text-gray-900">{formatCurrency(bundle.bundle_price)}</div>
                    <div className="text-xs text-gray-500">
                        {__('product.original_price')}: {formatCurrency(bundle.original_price)}
                    </div>
                </div>
            )
        },
        {
            field: 'discount_percentage',
            label: __('product.bundle_savings'),
            render: (bundle) => (
                <div className="text-center">
                    <span className="text-green-600 font-medium">
                        {bundle.discount_percentage}%
                    </span>
                    <div className="text-xs text-gray-500">
                        -{formatCurrency(bundle.discount_amount)}
                    </div>
                </div>
            )
        },
        {
            field: 'stock_quantity',
            label: __('product.bundle_stock'),
            sortable: true,
            render: (bundle) => (
                <div className="text-center">
                    <span className={`${bundle.stock_quantity > 0 ? 'text-green-600' : 'text-red-600'} font-medium`}>
                        {bundle.stock_quantity}
                    </span>
                </div>
            )
        },
        {
            field: 'is_active',
            label: __('product.bundle_status'),
            render: (bundle) => (
                <div className="flex items-center">
                    <StatusBadge
                        text={bundle.is_active ? __('product.bundle_active') : __('product.bundle_inactive')}
                        color={bundle.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                    />
                    {bundle.is_featured && (
                        <StatusBadge
                            text={__('product.featured_bundle')}
                            color="bg-purple-100 text-purple-800"
                            className="ml-2"
                        />
                    )}
                </div>
            )
        },
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('product.bundles')} />

            <div className="bg-white rounded-lg shadow-md relative">
                {isProcessing && <Loading overlay text={__('marketplace.loading')} />}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('product.bundles')}</h1>
                        <PrimaryButton
                            href={route('superadmin.marketplace.bundles.create')}
                            className="flex items-center gap-2"
                        >
                            <Plus className="w-4 h-4" />
                            {__('product.create_bundle')}
                        </PrimaryButton>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                            <form onSubmit={handleSearch} className="w-full">
                                <div className="relative">
                                    <TextInputWithLabel
                                        id="search"
                                        type="text"
                                        label={__('marketplace.search_products')}
                                        placeholder={__('marketplace.search_products_placeholder')}
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                    <div className="absolute top-9 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <button type="submit" className="hidden">{__('marketplace.search')}</button>
                                </div>
                            </form>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('marketplace.status')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleStatusFilter('')}
                                        variant={selectedStatus === '' || selectedStatus === null || selectedStatus === undefined ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.all_status')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('1')}
                                        variant={selectedStatus === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === '1' ? "bg-green-600" : ""}
                                    >
                                        {__('product.bundle_active')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('0')}
                                        variant={selectedStatus === '0' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === '0' ? "bg-gray-600" : ""}
                                    >
                                        {__('product.bundle_inactive')}
                                    </Button>
                                </div>
                            </div>

                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('marketplace.featured')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleFeaturedFilter('')}
                                        variant={selectedFeatured === '' || selectedFeatured === null || selectedFeatured === undefined ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.all_products')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFeaturedFilter('1')}
                                        variant={selectedFeatured === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedFeatured === '1' ? "bg-purple-600" : ""}
                                    >
                                        {__('product.featured_bundle')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFeaturedFilter('0')}
                                        variant={selectedFeatured === '0' ? "default" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.non_featured')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <DataTable
                    data={bundles.data}
                    columns={columns}
                    onSort={handleSort}
                    sortField={filters.sort || 'created_at'}
                    sortDirection={filters.direction || 'desc'}
                    emptyStateMessage={__('marketplace.no_products_found')}
                    primaryKey="id"
                    viewRoute="superadmin.marketplace.bundles.show"
                    editRoute="superadmin.marketplace.bundles.edit"
                    deleteCallback={confirmDelete}
                    cancelDeletion={cancelDelete}
                    loading={isProcessing}
                />

                <ConfirmDeleteModal
                    isOpen={isDeleting !== null}
                    onClose={cancelDelete}
                    onConfirm={deleteBundle}
                    title={__('product.delete_bundle')}
                    message={__('marketplace.delete_product_confirmation')}
                    isProcessing={isProcessing}
                />

                {bundles.links && bundles.links.length > 3 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-700">
                                {__('marketplace.showing')} {bundles.from} {__('marketplace.to')} {bundles.to} {__('marketplace.of')} {bundles.total} {__('product.bundles').toLowerCase()}
                            </p>
                            <Pagination
                                links={bundles.links}
                                preserveState={true}
                                preserveScroll={true}
                            />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
