import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { Button } from '@/Components/ui/button';
import { Edit, Package, Calendar, Clock, Star, ToggleLeft, ToggleRight, RefreshCw, Trash2 } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import { toast } from 'react-toastify';
import { useToast } from '@/Hooks/useToastContext';

export default function Show({ bundle }) {
    const [isProcessing, setIsProcessing] = useState(false);
    const [processingAction, setProcessingAction] = useState('');
    const { addAlert } = useToast();

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
    };

    const formatDateTime = (dateTime) => {
        return dateTime ? new Date(dateTime).toLocaleString() : __('common.not_provided');
    };

    const isActive = bundle.is_active && (!bundle.expires_at || new Date(bundle.expires_at) > new Date());
    const isExpired = bundle.expires_at && new Date(bundle.expires_at) < new Date();
    const isUpcoming = bundle.starts_at && new Date(bundle.starts_at) > new Date();

    const handleToggleActive = () => {
        if (isProcessing) return;

        setIsProcessing(true);
        setProcessingAction('toggle-active');

        router.post(route('superadmin.marketplace.bundles.toggle-active', bundle.id), {}, {
            onSuccess: () => {
                const message = bundle.is_active
                    ? __('product.bundle_deactivated_successfully')
                    : __('product.bundle_activated_successfully');
                addAlert('success', message);
            },
            onError: (errors) => {
                console.error('Toggle active error:', errors);
                addAlert('error', errors.message || __('common.error_occurred'));
            },
            onFinish: () => {
                setIsProcessing(false);
                setProcessingAction('');
            }
        });
    };

    const handleToggleFeatured = () => {
        if (isProcessing) return;

        setIsProcessing(true);
        setProcessingAction('toggle-featured');

        router.post(route('superadmin.marketplace.bundles.toggle-featured', bundle.id), {}, {
            onSuccess: () => {
                const message = bundle.is_featured
                    ? __('product.bundle_unfeatured_successfully')
                    : __('product.bundle_featured_successfully');
                addAlert('success', message);
            },
            onError: (errors) => {
                console.error('Toggle featured error:', errors);
                addAlert('error', errors.message || __('common.error_occurred'));
            },
            onFinish: () => {
                setIsProcessing(false);
                setProcessingAction('');
            }
        });
    };

    const handleRefreshPricing = () => {
        if (isProcessing) return;

        setIsProcessing(true);
        setProcessingAction('refresh-pricing');

        router.post(route('superadmin.marketplace.bundles.refresh-pricing', bundle.id), {}, {
            onSuccess: () => {
                addAlert('success', __('product.bundle_pricing_refreshed_successfully'));
            },
            onError: (errors) => {
                console.error('Refresh pricing error:', errors);
                addAlert('error', errors.message || __('common.error_occurred'));
            },
            onFinish: () => {
                setIsProcessing(false);
                setProcessingAction('');
            }
        });
    };

    const handleDelete = () => {
        if (isProcessing) return;

        if (!confirm(__('product.delete_bundle_confirmation', { name: bundle.name }))) {
            return;
        }

        setIsProcessing(true);
        setProcessingAction('delete');

        router.delete(route('superadmin.marketplace.bundles.destroy', bundle.id), {
            onSuccess: () => {
                addAlert('success', __('product.bundle_deleted_successfully'));
                router.visit(route('superadmin.marketplace.bundles.index'));
            },
            onError: (errors) => {
                console.error('Delete error:', errors);
                addAlert('error', errors.message || __('common.error_occurred'));
            },
            onFinish: () => {
                setIsProcessing(false);
                setProcessingAction('');
            }
        });
    };

    return (
        <SuperAdminLayout>
            <Head title={`${__('product.bundle')}: ${bundle.name}`} />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">{bundle.name}</h1>
                                <p className="mt-1 text-sm text-gray-600">
                                    {__('common.created_at')}: {formatDateTime(bundle.created_at)}
                                </p>
                            </div>
                            <div className="flex space-x-3">
                                <Button asChild variant="primary">
                                    <Link href={route('superadmin.marketplace.bundles.edit', bundle.id)}>
                                        <Edit className="w-4 h-4 mr-2" />
                                        {__('common.edit')}
                                    </Link>
                                </Button>
                                <Button asChild variant="secondary">
                                    <Link href={route('superadmin.marketplace.bundles.index')}>
                                        {__('common.back')}
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Left Column - Bundle Details */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Basic Information */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('common.basic_information')}</h2>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <div className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">{__('product.bundle_name')}</label>
                                                <p className="mt-1 text-sm text-gray-900">{bundle.name}</p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">{__('product.bundle_description')}</label>
                                                <p className="mt-1 text-sm text-gray-900">
                                                    {bundle.description || __('common.not_provided')}
                                                </p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">{__('common.slug')}</label>
                                                <p className="mt-1 text-sm text-gray-900 font-mono">{bundle.slug}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        {/* Bundle Image */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500 mb-2">{__('common.image')}</label>
                                            <div className="w-48 h-48 rounded-lg overflow-hidden bg-gray-100">
                                                <ImageWithFallback
                                                    src={bundle.image_url_formatted}
                                                    alt={bundle.name}
                                                    className="w-full h-full object-cover"
                                                    fallbackIcon={Package}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Pricing Information */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('product.pricing_information')}</h2>

                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                                        <div className="text-sm text-gray-500">{__('product.original_price')}</div>
                                        <div className="text-lg font-semibold text-gray-900 line-through">
                                            {formatCurrency(bundle.original_price)}
                                        </div>
                                    </div>

                                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                                        <div className="text-sm text-blue-600">{__('product.bundle_price')}</div>
                                        <div className="text-xl font-bold text-blue-600">
                                            {formatCurrency(bundle.bundle_price)}
                                        </div>
                                    </div>

                                    <div className="text-center p-4 bg-green-50 rounded-lg">
                                        <div className="text-sm text-green-600">{__('product.bundle_savings')}</div>
                                        <div className="text-lg font-semibold text-green-600">
                                            {formatCurrency(bundle.discount_amount)}
                                        </div>
                                    </div>

                                    <div className="text-center p-4 bg-green-50 rounded-lg">
                                        <div className="text-sm text-green-600">{__('product.discount_percentage')}</div>
                                        <div className="text-lg font-semibold text-green-600">
                                            {bundle.discount_percentage}%
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Bundle Items */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('product.bundle_items')}</h2>

                                <div className="space-y-4">
                                    {bundle.bundle_items.map((item, index) => (
                                        <div key={item.id} className="flex items-center p-4 border border-gray-200 rounded-lg">
                                            <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 mr-4">
                                                <ImageWithFallback
                                                    src={item.product.image_url}
                                                    alt={item.product.name}
                                                    className="w-full h-full object-cover"
                                                    fallbackIcon={Package}
                                                />
                                            </div>

                                            <div className="flex-1">
                                                <h3 className="font-medium text-gray-900">{item.product.name}</h3>
                                                <p className="text-sm text-gray-500">
                                                    {item.product.category?.name}
                                                </p>
                                                <div className="flex items-center space-x-4 mt-2 text-sm">
                                                    <span className="text-gray-600">
                                                        {__('product.product_quantity')}: {item.quantity}
                                                    </span>
                                                    <span className="text-gray-600">
                                                        {__('product.product_price')}: {formatCurrency(item.item_price)}
                                                    </span>
                                                    <span className="font-medium text-gray-900">
                                                        {__('common.total')}: {formatCurrency(item.item_price * item.quantity)}
                                                    </span>
                                                </div>
                                                {item.product_options && Object.keys(item.product_options).length > 0 && (
                                                    <div className="mt-1 text-xs text-gray-500">
                                                        {__('common.options')}: {JSON.stringify(item.product_options)}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="text-right">
                                                <div className="text-sm text-gray-500">#{index + 1}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="mt-4 pt-4 border-t border-gray-200">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">{__('product.total_products')}:</span>
                                        <span className="font-medium">{bundle.bundle_items.length} {__('product.products')}</span>
                                    </div>
                                    <div className="flex justify-between text-sm mt-1">
                                        <span className="text-gray-600">{__('common.total_quantity')}:</span>
                                        <span className="font-medium">
                                            {bundle.bundle_items.reduce((sum, item) => sum + item.quantity, 0)} {__('cart.items')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Status & Actions */}
                        <div className="space-y-6">
                            {/* Status Card */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('common.status')}</h2>

                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">{__('product.bundle_status')}</span>
                                        <StatusBadge
                                            status={bundle.is_active ? 'active' : 'inactive'}
                                            text={bundle.is_active ? __('product.bundle_active') : __('product.bundle_inactive')}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">{__('product.featured_bundle')}</span>
                                        <div className="flex items-center">
                                            {bundle.is_featured ? (
                                                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                            ) : (
                                                <span className="text-sm text-gray-400">{__('common.no')}</span>
                                            )}
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">{__('product.bundle_stock')}</span>
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            bundle.stock_quantity > 10
                                                ? 'bg-green-100 text-green-800'
                                                : bundle.stock_quantity > 0
                                                ? 'bg-yellow-100 text-yellow-800'
                                                : 'bg-red-100 text-red-800'
                                        }`}>
                                            {bundle.stock_quantity}
                                        </span>
                                    </div>

                                    <div className="pt-4 border-t border-gray-200">
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">{__('product.bundle_start_date')}</span>
                                                <span className="text-gray-900">{formatDateTime(bundle.starts_at)}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">{__('product.bundle_end_date')}</span>
                                                <span className="text-gray-900">{formatDateTime(bundle.expires_at)}</span>
                                            </div>
                                        </div>

                                        {isExpired && (
                                            <div className="mt-3 p-2 bg-red-50 rounded-md">
                                                <p className="text-sm text-red-600">{__('product.bundle_expired')}</p>
                                            </div>
                                        )}

                                        {isUpcoming && (
                                            <div className="mt-3 p-2 bg-yellow-50 rounded-md">
                                                <p className="text-sm text-yellow-600">{__('product.bundle_expires_soon')}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('common.quick_actions')}</h2>

                                <div className="space-y-3">
                                    <Button asChild variant="outline" className="w-full">
                                        <Link href={route('superadmin.marketplace.bundles.edit', bundle.id)}>
                                            <Edit className="w-4 h-4 mr-2" />
                                            {__('product.edit_bundle')}
                                        </Link>
                                    </Button>

                                    <Button
                                        onClick={handleToggleActive}
                                        disabled={isProcessing && processingAction === 'toggle-active'}
                                        variant={bundle.is_active ? "destructive" : "default"}
                                        className="w-full"
                                    >
                                        {isProcessing && processingAction === 'toggle-active' ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                {__('common.processing')}
                                            </>
                                        ) : bundle.is_active ? (
                                            <>
                                                <ToggleLeft className="w-4 h-4 mr-2" />
                                                {__('product.deactivate_bundle')}
                                            </>
                                        ) : (
                                            <>
                                                <ToggleRight className="w-4 h-4 mr-2" />
                                                {__('product.activate_bundle')}
                                            </>
                                        )}
                                    </Button>

                                    <Button
                                        onClick={handleToggleFeatured}
                                        disabled={isProcessing && processingAction === 'toggle-featured'}
                                        variant={bundle.is_featured ? "secondary" : "outline"}
                                        className={`w-full ${bundle.is_featured ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : ''}`}
                                    >
                                        {isProcessing && processingAction === 'toggle-featured' ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                {__('common.processing')}
                                            </>
                                        ) : (
                                            <>
                                                <Star className={`w-4 h-4 mr-2 ${bundle.is_featured ? 'fill-current' : ''}`} />
                                                {bundle.is_featured ? __('product.remove_featured') : __('product.set_featured')}
                                            </>
                                        )}
                                    </Button>

                                    <Button
                                        onClick={handleRefreshPricing}
                                        disabled={isProcessing && processingAction === 'refresh-pricing'}
                                        variant="outline"
                                        className="w-full"
                                    >
                                        {isProcessing && processingAction === 'refresh-pricing' ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                {__('common.processing')}
                                            </>
                                        ) : (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2" />
                                                {__('product.refresh_pricing')}
                                            </>
                                        )}
                                    </Button>

                                    <Button
                                        onClick={handleDelete}
                                        disabled={isProcessing && processingAction === 'delete'}
                                        variant="destructive"
                                        className="w-full"
                                    >
                                        {isProcessing && processingAction === 'delete' ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                {__('common.processing')}
                                            </>
                                        ) : (
                                            <>
                                                <Trash2 className="w-4 h-4 mr-2" />
                                                {__('product.delete_bundle')}
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </div>

                            {/* Bundle Meta */}
                            <div className="bg-white shadow rounded-lg p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">{__('common.metadata')}</h2>

                                <div className="space-y-3 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">{__('common.id')}</span>
                                        <span className="text-gray-900 font-mono">#{bundle.id}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">{__('product.bundle_sort_order')}</span>
                                        <span className="text-gray-900">{bundle.sort_order}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">{__('common.created_at')}</span>
                                        <span className="text-gray-900">{formatDateTime(bundle.created_at)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">{__('common.updated_at')}</span>
                                        <span className="text-gray-900">{formatDateTime(bundle.updated_at)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
