import React, { useState, useRef, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload } from 'lucide-react';

export default function Create({ parentCategories }) {
    const { flash } = usePage().props;
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(null);
    const { addAlert } = useToast();

    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        description: '',
        parent_id: '',
        is_sub: false,
        status: true,
        image: null,
    });

    useEffect(() => {
        if (flash.error) {
            addAlert('error', flash.error);
        }
        if (flash.success) {
            addAlert('success', flash.success);
        }
    }, [flash]);

    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            const firstError = Object.values(errors)[0];
            addAlert('error', firstError);
        }
    }, [errors]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.marketplace.categories.store'), {
            onSuccess: () => {
                reset();
                setImagePreview(null);
            },
            onError: (errors) => {
                console.log('Validation errors:', errors);
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('image', null);
        setImagePreview(null);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    const handleIsSubChange = (e) => {
        const isSubcategory = e.target.checked;
        setData({
            ...data,
            is_sub: isSubcategory,
            parent_id: isSubcategory ? data.parent_id : '', 
        });
    };

    return (
        <SuperAdminLayout>
            <Head title={__('marketplace.create_category')} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('marketplace.create_new_category')}</h1>
                        <Link
                            href={route('superadmin.marketplace.categories.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            <i className="fas fa-arrow-left mr-2"></i>
                            {__('marketplace.back_to_categories')}
                        </Link>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="p-6 grid grid-cols-1 gap-6">
                        <div className="w-full">
                            <TextInputWithLabel
                                id="name"
                                type="text"
                                label={__('marketplace.category_name')}
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                errors={errors.name}
                                required
                            />
                        </div>

                        <div className="w-full">
                            <div className="flex items-center mb-4">
                                <input
                                    id="is_sub"
                                    type="checkbox"
                                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                                    checked={data.is_sub}
                                    onChange={handleIsSubChange}
                                />
                                <label htmlFor="is_sub" className="ml-2 text-sm font-medium text-gray-700">
                                    {__('marketplace.this_is_subcategory')}
                                </label>
                            </div>

                            {data.is_sub && (
                                <div className="mt-2">
                                    <SelectWithLabel
                                        id="parent_id"
                                        label={__('marketplace.parent_category')}
                                        value={data.parent_id}
                                        onChange={(e) => setData('parent_id', e.target.value)}
                                        errors={errors.parent_id}
                                        required={data.is_sub}
                                    >
                                        <option value="">{__('marketplace.select_parent_category')}</option>
                                        {parentCategories.map((category) => (
                                            <option key={category.id} value={category.id}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </SelectWithLabel>
                                </div>
                            )}
                        </div>

                        <div className="w-full">
                            <TextareaWithLabel
                                id="description"
                                label={__('marketplace.description')}
                                rows="3"
                                value={data.description}
                                onChange={(e) => setData('description', e.target.value)}
                                errors={errors.description}
                            />
                        </div>

                        <div className="w-full">
                            <label className="text-sm block mb-1 font-medium text-gray-700">{__('marketplace.category_image')}</label>
                            <div className="mt-1 flex items-center">
                                <input
                                    type="file"
                                    id="image"
                                    ref={imageInputRef}
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleImageChange}
                                />

                                <div className="flex items-center space-x-4">
                                    {imagePreview ? (
                                        <div className="relative">
                                            <img
                                                src={imagePreview}
                                                alt={__('marketplace.category_preview')}
                                                className="h-32 w-32 object-cover rounded-md border border-gray-200"
                                            />
                                            <button
                                                type="button"
                                                onClick={clearImage}
                                                className="absolute -top-2 -right-2 text-red-500 bg-white rounded-full"
                                            >
                                                <XCircle className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div
                                            className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                                            onClick={() => imageInputRef.current?.click()}
                                        >
                                            <Upload className="h-8 w-8 text-gray-400" />
                                            <span className="mt-2 text-sm text-gray-500">{__('marketplace.upload_image')}</span>
                                        </div>
                                    )}

                                    <div className="flex flex-col">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => imageInputRef.current?.click()}
                                            className="mb-2"
                                        >
                                            {imagePreview ? __('marketplace.change_image') : __('marketplace.browse_image')}
                                        </Button>
                                        <p className="text-xs text-gray-500">
                                            {__('marketplace.supported_formats')}
                                        </p>
                                        {errors.image && (
                                            <p className="text-sm text-red-600 mt-1">{errors.image}</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="w-full mt-4 border-t border-gray-200 pt-4">
                            <div className="flex items-center space-x-2">
                                <input
                                    id="status"
                                    type="checkbox"
                                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                                    checked={data.status}
                                    onChange={(e) => setData('status', e.target.checked)}
                                />
                                <label htmlFor="status" className="text-sm font-medium text-gray-700">
                                    {__('marketplace.active_category')}
                                </label>
                            </div>
                            {errors.status && (
                                <p className="text-sm text-red-600 mt-1">{errors.status}</p>
                            )}
                        </div>
                    </div>

                    <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                        <Link
                            href={route('superadmin.marketplace.categories.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('marketplace.cancel')}
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {processing ? __('marketplace.creating') : __('marketplace.create_category')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
