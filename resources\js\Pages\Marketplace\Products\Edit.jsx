import React, { useState, useRef, useEffect } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import { Button } from '@/Components/ui/button';
import { useToast } from '@/Hooks/useToastContext';
import { XCircle, Upload } from 'lucide-react';

export default function Edit({ product, categories }) {
    const { flash } = usePage().props;
    const imageInputRef = useRef(null);
    const [imagePreview, setImagePreview] = useState(product.image_url_formatted || null);
    const { addAlert } = useToast();

    const { data, setData, post, processing, errors } = useForm({
        name: product.name || '',
        description: product.description || '',
        category_id: product.category_id || '',
        sku: product.sku || '',
        barcode: product.barcode || '',
        unit: product.unit || '',
        import_price: product.import_price || '',
        sale_price: product.sale_price || '',
        alert_quantity: product.alert_quantity || '',
        quantity: product.quantity || '',
        brand: product.brand || '',
        image: null,
        status: product.status || true,
        is_featured: product.is_featured || false,
        weight: product.weight || '',
        dimensions: product.dimensions || '',
        _method: 'PUT',
    });

    useEffect(() => {
        if (flash.error) {
            addAlert('error', flash.error);
        }
        if (flash.success) {
            addAlert('success', flash.success);
        }
    }, [flash]);

    useEffect(() => {
        if (Object.keys(errors).length > 0) {
            const firstError = Object.values(errors)[0];
            addAlert('error', firstError);
        }
    }, [errors]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.marketplace.products.update', product.id), {
            onSuccess: () => {
                setImagePreview(product.image_url_formatted);
            },
            onError: (errors) => {
                console.log('Validation errors:', errors);
            }
        });
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const clearImage = () => {
        setData('image', null);
        setImagePreview(null);
        if (imageInputRef.current) {
            imageInputRef.current.value = '';
        }
    };

    return (
        <SuperAdminLayout>
            <Head title={__('marketplace.edit_product') + ': ' + product.name} />

            <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('marketplace.edit_product')}: {product.name}</h1>
                        <div className="flex space-x-2">
                            <Link
                                href={route('superadmin.marketplace.products.index')}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <i className="fas fa-arrow-left mr-2"></i>
                                {__('marketplace.back_to_products')}
                            </Link>
                            <Link
                                href={route('superadmin.marketplace.products.show', product.id)}
                                className="px-4 py-2 border border-blue-300 bg-blue-50 rounded-md text-sm text-blue-700 hover:bg-blue-100"
                            >
                                <i className="fas fa-eye mr-2"></i>
                                {__('marketplace.view_product')}
                            </Link>
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="md:col-span-2">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">{__('marketplace.basic_information')}</h2>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="name"
                                type="text"
                                label={__('marketplace.product_name')}
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                errors={errors.name}
                                required
                            />
                        </div>

                        <div>
                            <SelectWithLabel
                                id="category_id"
                                label={__('marketplace.product_category')}
                                value={data.category_id}
                                onChange={(e) => setData('category_id', e.target.value)}
                                errors={errors.category_id}
                                required
                            >
                                <option value="">{__('marketplace.select_category')}</option>
                                {categories.map((category) => (
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div className="md:col-span-2">
                            <TextareaWithLabel
                                id="description"
                                label={__('marketplace.product_description')}
                                rows="3"
                                value={data.description}
                                onChange={(e) => setData('description', e.target.value)}
                                errors={errors.description}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="sku"
                                type="text"
                                label={__('marketplace.product_sku')}
                                value={data.sku}
                                onChange={(e) => setData('sku', e.target.value)}
                                errors={errors.sku}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="barcode"
                                type="text"
                                label={__('marketplace.product_barcode')}
                                value={data.barcode}
                                onChange={(e) => setData('barcode', e.target.value)}
                                errors={errors.barcode}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="brand"
                                type="text"
                                label={__('marketplace.product_brand')}
                                value={data.brand}
                                onChange={(e) => setData('brand', e.target.value)}
                                errors={errors.brand}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="unit"
                                type="text"
                                label={__('marketplace.product_unit')}
                                value={data.unit}
                                onChange={(e) => setData('unit', e.target.value)}
                                errors={errors.unit}
                            />
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('marketplace.price_inventory')}
                            </h2>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="import_price"
                                type="number"
                                step="0.01"
                                min="0"
                                label={__('marketplace.cost_price')}
                                value={data.import_price}
                                onChange={(e) => setData('import_price', e.target.value)}
                                errors={errors.import_price}
                                required
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="sale_price"
                                type="number"
                                step="0.01"
                                min="0"
                                label={__('marketplace.sale_price')}
                                value={data.sale_price}
                                onChange={(e) => setData('sale_price', e.target.value)}
                                errors={errors.sale_price}
                                required
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="quantity"
                                type="number"
                                min="0"
                                label={__('marketplace.quantity_in_stock')}
                                value={data.quantity}
                                onChange={(e) => setData('quantity', e.target.value)}
                                errors={errors.quantity}
                                required
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="alert_quantity"
                                type="number"
                                min="0"
                                label={__('marketplace.alert_quantity')}
                                value={data.alert_quantity}
                                onChange={(e) => setData('alert_quantity', e.target.value)}
                                errors={errors.alert_quantity}
                            />
                        </div>

                        <div className="md:col-span-2 mt-4">
                            <h2 className="text-lg font-medium text-gray-900 mb-4 pt-4 border-t border-gray-200">
                                {__('marketplace.additional_information')}
                            </h2>
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="weight"
                                type="text"
                                label={__('marketplace.product_weight')}
                                value={data.weight}
                                onChange={(e) => setData('weight', e.target.value)}
                                errors={errors.weight}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                id="dimensions"
                                type="text"
                                label={__('marketplace.product_dimensions')}
                                value={data.dimensions}
                                onChange={(e) => setData('dimensions', e.target.value)}
                                errors={errors.dimensions}
                            />
                        </div>

                        <div className="md:col-span-2">
                            <label className="text-sm block mb-1 font-medium text-gray-700">{__('marketplace.product_image')}</label>
                            <div className="mt-1 flex items-center">
                                <input
                                    type="file"
                                    id="image"
                                    ref={imageInputRef}
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleImageChange}
                                />

                                <div className="flex items-center space-x-4">
                                    {imagePreview ? (
                                        <div className="relative">
                                            <img
                                                src={imagePreview}
                                                alt="Product Preview"
                                                className="h-32 w-32 object-cover rounded-md border border-gray-200"
                                            />
                                            <button
                                                type="button"
                                                onClick={clearImage}
                                                className="absolute -top-2 -right-2 text-red-500 bg-white rounded-full"
                                            >
                                                <XCircle className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ) : (
                                        <div
                                            className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                                            onClick={() => imageInputRef.current?.click()}
                                        >
                                            <Upload className="h-8 w-8 text-gray-400" />
                                            <span className="mt-2 text-sm text-gray-500">{__('marketplace.upload_image')}</span>
                                        </div>
                                    )}

                                    <div className="flex flex-col">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => imageInputRef.current?.click()}
                                            className="mb-2"
                                        >
                                            {imagePreview ? __('marketplace.change_image') : __('marketplace.browse_image')}
                                        </Button>
                                        <p className="text-xs text-gray-500">
                                            {__('marketplace.supported_formats')}
                                        </p>
                                        {errors.image && (
                                            <p className="text-sm text-red-600 mt-1">{errors.image}</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="md:col-span-2 mt-4 border-t border-gray-200 pt-4">
                            <div className="flex flex-col md:flex-row gap-4">
                                <div className="flex items-center space-x-2">
                                    <input
                                        id="status"
                                        type="checkbox"
                                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                                        checked={data.status}
                                        onChange={(e) => setData('status', e.target.checked)}
                                    />
                                    <label htmlFor="status" className="text-sm font-medium text-gray-700">
                                        {__('marketplace.active_product')}
                                    </label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <input
                                        id="is_featured"
                                        type="checkbox"
                                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                                        checked={data.is_featured}
                                        onChange={(e) => setData('is_featured', e.target.checked)}
                                    />
                                    <label htmlFor="is_featured" className="text-sm font-medium text-gray-700">
                                        {__('marketplace.featured_product')}
                                    </label>
                                </div>
                            </div>
                            {(errors.status || errors.is_featured) && (
                                <p className="text-sm text-red-600 mt-1">
                                    {errors.status || errors.is_featured}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                        <Link
                            href={route('superadmin.marketplace.products.index')}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
                        >
                            {__('marketplace.cancel')}
                        </Link>
                        <Button
                            type="submit"
                            disabled={processing}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {processing ? __('marketplace.updating') : __('marketplace.save_changes')}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
