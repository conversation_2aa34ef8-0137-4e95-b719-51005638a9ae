import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Search, Filter } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';
import ImageWithFallback from '@/Components/ImageWithFallback';
import PrimaryButton from '@/Components/PrimaryButton';
import Pagination from '@/Components/Pagination';
import StatusBadge from '@/Components/ui/StatusBadge';
import axios from 'axios';
import Loading from '@/Components/Loading';

export default function Index({ products: initialProducts, categories, filters }) {
    const { processing, flash, csrf_token } = usePage().props;
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const { addAlert } = useToast();
    const [products, setProducts] = useState(initialProducts);
    const [selectedCategory, setSelectedCategory] = useState(filters.category_id || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status ?? '');
    const [selectedFeatured, setSelectedFeatured] = useState(filters.is_featured ?? '');

    useEffect(() => {
        if (flash.error) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', flash.error);
        }
        if (flash.success) {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', flash.success);
        }
    }, [flash]);

    const handleSearch = (e) => {
        e.preventDefault();
        fetchProducts({
            ...filters,
            search: searchQuery,
            category_id: selectedCategory,
            status: selectedStatus,
            is_featured: selectedFeatured,
        }, true);
    };

    const handleCategoryFilter = (e) => {
        const category_id = e.target.value;
        setSelectedCategory(category_id);
        fetchProducts({
            ...filters,
            category_id,
            status: selectedStatus,
            is_featured: selectedFeatured,
        });
    };

    const handleStatusFilter = (status) => {
        setSelectedStatus(status);
        fetchProducts({
            ...filters,
            category_id: selectedCategory,
            status,
            is_featured: selectedFeatured,
        });
    };

    const handleFeaturedFilter = (isFeatured) => {
        setSelectedFeatured(isFeatured);
        fetchProducts({
            ...filters,
            category_id: selectedCategory,
            status: selectedStatus,
            is_featured: isFeatured,
        });
    };

    const handleSort = (field, direction) => {
        applyFilters({ sort: field, direction });
    };

    const applyFilters = (newFilters) => {
        router.get(route('superadmin.marketplace.products.index'),
            { ...filters, ...newFilters },
            { preserveState: true, preserveScroll: true }
        );
    };

    const confirmDelete = (productId) => {
        setIsDeleting(productId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteProduct = () => {
        if (!isDeleting) return;

        setIsProcessing(true);

        axios.delete(route('superadmin.marketplace.products.destroy', isDeleting), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrf_token
            }
        })
        .then(response => {

            setProducts(prevProducts => {
                const updatedData = prevProducts.data.filter(product => product.id !== isDeleting);


                const updatedProducts = {
                    ...prevProducts,
                    data: updatedData,
                    total: prevProducts.total - 1
                };


                if (updatedProducts.to) {
                    updatedProducts.to = Math.max(updatedProducts.to - 1, updatedProducts.from);
                }

                return updatedProducts;
            });

            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('success', response.data.message || __('marketplace.product_deleted_successfully'));
        })
        .catch(error => {
            setIsProcessing(false);
            setIsDeleting(null);
            addAlert('error', error.response?.data?.message || __('marketplace.delete_failed'));
        });
    };

    const fetchProducts = (params, updateUrl = false) => {
        setIsProcessing(true);

        if (updateUrl) {
            router.get(route('superadmin.marketplace.products.index'), params, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => setIsProcessing(false),
                onError: () => {
                    setIsProcessing(false);
                    addAlert('error', __('marketplace.fetch_failed'));
                }
            });
        } else {
            axios.get(route('superadmin.marketplace.products.api'), {
                params,
            })
            .then(res => {
                if (res.data && res.data.data) {
                    setProducts(res.data.data);
                }
                setIsProcessing(false);
            })
            .catch(() => {
                setIsProcessing(false);
                addAlert('error', __('marketplace.fetch_failed'));
            });
        }
    };

    const columns = [
        {
            field: 'name',
            label: __('marketplace.product_name'),
            sortable: true,
            render: (product) => (
                <div className="flex items-center space-x-3">
                    <ImageWithFallback
                        src={product.image_url_formatted}
                        alt={product.name}
                        fallbackText={product.name.charAt(0).toUpperCase()}
                        width="w-10"
                        height="h-10"
                    />
                    <div>
                        <div className="font-medium text-gray-900">{product.name}</div>
                        <div className="text-xs text-gray-500">
                            {__('marketplace.product_sku')}: {product.sku || __('marketplace.n_a')}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'category_id',
            label: __('marketplace.product_category'),
            render: (product) => (
                <StatusBadge
                    text={product.category ? product.category.name : __('common.none')}
                    color="bg-blue-100 text-blue-800"
                />
            )
        },
        {
            field: 'sale_price',
            label: __('marketplace.product_price'),
            sortable: true,
            render: (product) => (
                <div>
                    <div className="font-medium text-gray-900">{formatCurrency(product.sale_price)}</div>
                    <div className="text-xs text-gray-500">
                        {__('marketplace.cost_price')}: {formatCurrency(product.import_price)}
                    </div>
                </div>
            )
        },
        {
            field: 'quantity',
            label: __('marketplace.stock'),
            sortable: true,
            render: (product) => (
                <div className="text-center">
                    <span className={`${product.quantity > 0 ? 'text-green-600' : 'text-red-600'} font-medium`}>
                        {product.quantity}
                    </span>
                </div>
            )
        },
        {
            field: 'status',
            label: __('marketplace.status'),
            render: (product) => (
                <div className="flex items-center">
                    <StatusBadge
                        text={product.status ? __('marketplace.active') : __('marketplace.inactive')}
                        color={product.status ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                    />
                    {product.is_featured && (
                        <StatusBadge
                            text={__('marketplace.featured')}
                            color="bg-purple-100 text-purple-800"
                            className="ml-2"
                        />
                    )}
                </div>
            )
        },
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('marketplace.products')} />

            <div className="bg-white rounded-lg shadow-md relative">
                {isProcessing && <Loading overlay text={__('marketplace.loading')} />}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">{__('marketplace.product_management')}</h1>
                        <PrimaryButton
                            href={route('superadmin.marketplace.products.create')}
                            className="flex items-center gap-2"
                        >
                            <Plus className="w-4 h-4" />
                            {__('marketplace.add_product')}
                        </PrimaryButton>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                            <form onSubmit={handleSearch} className="w-full">
                                <div className="relative">
                                    <TextInputWithLabel
                                        id="search"
                                        type="text"
                                        label={__('marketplace.search_products')}
                                        placeholder={__('marketplace.search_products_placeholder')}
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                    <div className="absolute top-9 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400" />
                                    </div>
                                    <button type="submit" className="hidden">{__('marketplace.search')}</button>
                                </div>
                            </form>

                            <SelectWithLabel
                                id="category_filter"
                                label={__('marketplace.product_category')}
                                value={selectedCategory}
                                onChange={handleCategoryFilter}
                                className="w-full"
                            >
                                <option value="">{__('marketplace.all_categories')}</option>
                                {categories.map((category) => (
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </SelectWithLabel>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('marketplace.status')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleStatusFilter('')}
                                        variant={selectedStatus === '' || selectedStatus === null || selectedStatus === undefined ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.all_status')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('1')}
                                        variant={selectedStatus === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === '1' ? "bg-green-600" : ""}
                                    >
                                        {__('marketplace.active')}
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusFilter('0')}
                                        variant={selectedStatus === '0' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedStatus === '0' ? "bg-gray-600" : ""}
                                    >
                                        {__('marketplace.inactive')}
                                    </Button>
                                </div>
                            </div>

                            <div>
                                <div className="text-sm font-medium text-gray-700 mb-2">{__('marketplace.featured')}:</div>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        onClick={() => handleFeaturedFilter('')}
                                        variant={selectedFeatured === '' || selectedFeatured === null || selectedFeatured === undefined ? "secondary" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.all_products')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFeaturedFilter('1')}
                                        variant={selectedFeatured === '1' ? "default" : "outline"}
                                        size="sm"
                                        className={selectedFeatured === '1' ? "bg-purple-600" : ""}
                                    >
                                        {__('marketplace.featured')}
                                    </Button>
                                    <Button
                                        onClick={() => handleFeaturedFilter('0')}
                                        variant={selectedFeatured === '0' ? "default" : "outline"}
                                        size="sm"
                                    >
                                        {__('marketplace.non_featured')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <DataTable
                    data={products.data}
                    columns={columns}
                    onSort={handleSort}
                    sortField={filters.sort || 'created_at'}
                    sortDirection={filters.direction || 'desc'}
                    emptyStateMessage={__('marketplace.no_products_found')}
                    primaryKey="id"
                    viewRoute="superadmin.marketplace.products.show"
                    editRoute="superadmin.marketplace.products.edit"
                    deleteCallback={confirmDelete}
                    cancelDeletion={cancelDelete}
                    loading={isProcessing}
                />

                <ConfirmDeleteModal
                    isOpen={isDeleting !== null}
                    onClose={cancelDelete}
                    onConfirm={deleteProduct}
                    title={__('marketplace.delete_product')}
                    message={__('marketplace.delete_product_confirmation')}
                    isProcessing={isProcessing}
                />

                {products.links && products.links.length > 3 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-700">
                                {__('marketplace.showing')} {products.from} {__('marketplace.to')} {products.to} {__('marketplace.of')} {products.total} {__('marketplace.products').toLowerCase()}
                            </p>
                            <Pagination
                                links={products.links}
                                preserveState={true}
                                preserveScroll={true}
                            />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
