import React, { useState, useEffect } from 'react';
import { Head, Link, usePage, router } from '@inertiajs/react';
import {
    ChevronRight,
    ChevronLeft,
    ShoppingCart,
    Star,
    Package,
    Heart,
    X,
    Plus,
    Minus,
    Shield,
    Truck,
    RefreshCw,
    Share2,
    Facebook,
    Twitter,
    Check,
    CheckCircle
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { FALLBACK_IMAGE_URL } from '@/constants/config';
import { useToast } from '@/Hooks/useToastContext';
import BundleCard from '@/Components/Marketplace/BundleCard';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import Footer from '@/Components/Landing/Footer';
import Loading from '@/Components/Loading';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/Components/ui/tabs';
import ImageWithFallback from '@/Components/ImageWithFallback';

export default function BundleDetail({ bundle, relatedBundles = [], topCategories = [], moreCategories = [] }) {
    const { flash, errors } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [quantity, setQuantity] = useState(1);
    const [isAddingToCart, setIsAddingToCart] = useState(false);
    const [isWishlist, setIsWishlist] = useState(false);
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const processingRef = React.useRef(false);

    useEffect(() => {
        const handleStart = (event) => {
            if (event.detail.visit.url.includes('/marketplace/bundles/')) {
                setIsPageLoading(true);
            }
        };
        const handleFinish = () => setIsPageLoading(false);

        document.addEventListener('inertia:start', handleStart);
        document.addEventListener('inertia:finish', handleFinish);

        return () => {
            document.removeEventListener('inertia:start', handleStart);
            document.removeEventListener('inertia:finish', handleFinish);
        };
    }, []);

    useEffect(() => {
        console.log('Bundle detail data:', bundle);
        console.log('Bundle image paths:', {
            image: bundle.image,
            image_url: bundle.image_url,
            image_url_formatted: bundle.image_url_formatted
        });
    }, [bundle]);
      const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const handleAddToCart = async (bundleId = null, message = null, type = 'success') => {
        // Prevent double clicks - exactly like ProductDetail
        if (processingRef.current) {
            return;
        }

        processingRef.current = true;

        try {
            const targetBundleId = bundleId || bundle.id;
            const targetQuantity = quantity;
            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('marketplace.csrf_token_missing'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        bundle_id: targetBundleId,
                        quantity: targetQuantity
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                // Guest user - use localStorage like ProductDetail
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.bundle_id && item.bundle_id === targetBundleId) ||
                    (item.id === targetBundleId && !item.bundle_id)
                );

                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += targetQuantity;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        bundle_id: targetBundleId,
                        name: bundle.name,
                        slug: bundle.slug,
                        image: bundle.image || bundle.image_url_formatted || bundle.image_url,
                        price: bundle.bundle_price || bundle.total_price,
                        original_price: bundle.original_total_price || bundle.original_price,
                        quantity: targetQuantity,
                        category: 'Bundle',
                        stock_quantity: 999, // Bundles typically don't have stock limits
                        subtotal: (bundle.bundle_price || bundle.total_price) * targetQuantity
                    });
                }

                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', message || __('marketplace.bundle_added_to_cart'));
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            addAlert('error', __('marketplace.bundle_add_cart_error'));
        } finally {
            processingRef.current = false;
        }
    };

    // Keep the existing legacy function for compatibility
    const addToCart = async () => {
        await handleAddToCart();
    };    const calculateSavings = () => {
        const originalPrice = bundle.original_total_price || bundle.original_price || 0;
        const bundlePrice = bundle.bundle_price || bundle.total_price || 0;

        if (!originalPrice || originalPrice <= bundlePrice) {
            return 0;
        }
        return originalPrice - bundlePrice;
    };

    const calculateDiscountPercentage = () => {
        const originalPrice = bundle.original_total_price || bundle.original_price || 0;
        const bundlePrice = bundle.bundle_price || bundle.total_price || 0;

        if (!originalPrice || originalPrice <= bundlePrice) {
            return 0;
        }
        return Math.round(((originalPrice - bundlePrice) / originalPrice) * 100);
    };

    // Quantity handling like ProductDetail
    const handleQuantityChange = (value) => {
        const newValue = quantity + value;
        if (newValue >= 1) {
            setQuantity(newValue);
        }
    };

    // Handle wishlist toggle
    const handleWishlist = () => {
        try {
            const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
            const isInWishlist = wishlist.some(item =>
                (item.bundle_id && item.bundle_id === bundle.id) ||
                (item.id === bundle.id && item.type === 'bundle')
            );

            if (isInWishlist) {
                const updatedWishlist = wishlist.filter(item =>
                    !(
                        (item.bundle_id && item.bundle_id === bundle.id) ||
                        (item.id === bundle.id && item.type === 'bundle')
                    )
                );
                localStorage.setItem('wishlist', JSON.stringify(updatedWishlist));
                addAlert('info', __('marketplace.removed_from_wishlist'));
                setIsWishlist(false);
            } else {
                const wishlistId = Date.now() + Math.random();
                wishlist.push({
                    id: wishlistId,
                    bundle_id: bundle.id,
                    name: bundle.name,
                    slug: bundle.slug,
                    type: 'bundle',
                    image: bundle.image || bundle.image_url_formatted || bundle.image_url,
                    price: bundle.bundle_price || bundle.total_price,
                    original_price: bundle.original_total_price || bundle.original_price,
                    discount_percentage: discountPercentage,
                    category: 'Bundle',
                    added_at: new Date().toISOString()
                });
                localStorage.setItem('wishlist', JSON.stringify(wishlist));
                addAlert('success', __('marketplace.added_to_wishlist'));
                setIsWishlist(true);
            }

            window.dispatchEvent(new CustomEvent('wishlistUpdated'));
        } catch (error) {
            console.error('Wishlist error:', error);
            addAlert('error', __('marketplace.wishlist_error'));
        }
    };

    // Check if bundle is in wishlist when component mounts
    useEffect(() => {
        try {
            const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
            const isInWishlist = wishlist.some(item =>
                (item.bundle_id && item.bundle_id === bundle.id) ||
                (item.id === bundle.id && item.type === 'bundle')
            );
            setIsWishlist(isInWishlist);
        } catch (error) {
            console.error('Error checking wishlist status:', error);
        }
    }, [bundle.id]);

    const savings = calculateSavings();
    const discountPercentage = calculateDiscountPercentage();

    if (isPageLoading) {
        return <Loading />;
    }

    return (
        <>
            <Head title={bundle.name} />
            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            <div className="bg-muted min-h-screen">
                <div className="container mx-auto px-4 py-8">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6" aria-label="Breadcrumb">
                        <ol className="inline-flex items-center space-x-1 md:space-x-3">
                            <li className="inline-flex items-center">
                                <Link href="/marketplace" className="text-muted-foreground hover:text-primary">
                                    {__('marketplace.home')}
                                </Link>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <ChevronRight className="w-4 h-4 text-muted-foreground mx-2" />
                                    <Link href="/marketplace/bundles" className="text-muted-foreground hover:text-primary">
                                        {__('product.bundles')}
                                    </Link>
                                </div>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <ChevronRight className="w-4 h-4 text-muted-foreground mx-2" />
                                    <span className="text-muted-foreground">{bundle.name}</span>
                                </div>
                            </li>
                        </ol>
                    </nav>

                    {/* Bundle Details */}
                    <div className="bg-card rounded-lg shadow-lg overflow-hidden">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
                            {/* Bundle Image */}
                            <div className="space-y-4">
                                <div className="aspect-w-1 aspect-h-1 bg-accent rounded-lg overflow-hidden">
                                    <ImageWithFallback
                                        src={bundle.image_url}
                                        alt={bundle.name}
                                        fallbackText={bundle.name ? bundle.name.charAt(0).toUpperCase() : 'B'}
                                        className="w-full h-96 object-cover"
                                        width="w-full"
                                        height="h-96"
                                        rounded="rounded-none"
                                    />
                                </div>

                                {/* Badges */}
                                <div className="flex space-x-2">
                                    {bundle.is_featured && (
                                        <Badge variant="secondary">
                                            {__('marketplace.featured_badge')}
                                        </Badge>
                                    )}
                                    {discountPercentage > 0 && (
                                        <Badge variant="destructive">
                                            {__('product.discount_percent', { percent: discountPercentage })}
                                        </Badge>
                                    )}
                                </div>
                            </div>

                            {/* Bundle Info */}
                            <div className="space-y-6">
                                <div>
                                    <h1 className="text-3xl font-bold text-foreground mb-2">{bundle.name}</h1>
                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                        <span>{bundle.items?.length} {__('product.products')}</span>
                                        {bundle.is_active && (
                                            <div className="flex items-center gap-2 text-primary">
                                                <CheckCircle className="h-4 w-4" />
                                                <span>{__('marketplace.in_stock')}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                {/* Description */}
                                {bundle.description && (
                                    <div>
                                        <h3 className="text-lg font-semibold mb-2">{__('marketplace.product_description')}</h3>
                                        <p className="text-card-foreground leading-relaxed">{bundle.description}</p>
                                    </div>
                                )}

                                {/* Pricing */}
                                <div className="bg-muted p-4 rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-3xl font-bold text-primary">
                                            {formatCurrency(bundle.bundle_price || bundle.total_price)}
                                        </span>
                                        {savings > 0 && (
                                            <div className="text-right">
                                                <div className="text-lg text-muted-foreground line-through">
                                                    {formatCurrency(bundle.original_total_price || bundle.original_price)}
                                                </div>
                                                <div className="text-lg font-bold text-primary">
                                                    {__('product.bundle_savings') + ' ' + formatCurrency(savings)}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                {/* Add to Cart */}
                                <div className="space-y-4">

                                    <div className="flex items-center mb-6">
                                        <div className="inline-flex border rounded-md">
                                            <button
                                                type="button"
                                                onClick={() => handleQuantityChange(-1)}
                                                className="w-10 h-10 flex items-center justify-center hover:bg-accent"
                                                disabled={quantity <= 1}
                                            >
                                                <Minus className="h-4 w-4" />
                                            </button>
                                            <input
                                                type="number"
                                                id="quantity"
                                                min="1"
                                                value={quantity}
                                                onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                                                className="w-16 text-center border-x border-x-input focus:ring-0 focus:outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                            />
                                            <button
                                                type="button"
                                                onClick={() => handleQuantityChange(1)}
                                                className="w-10 h-10 flex items-center justify-center hover:bg-accent"
                                            >
                                                <Plus className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>

                                    <div className="flex space-x-4">
                                        <Button
                                            onClick={() => handleAddToCart()}
                                            disabled={processingRef.current || !bundle.is_active}
                                            className={`flex-1 ${processingRef.current ? 'opacity-50' : ''}`}
                                            variant={bundle.is_active ? "default" : "ghost"}
                                            size="lg"
                                        >
                                            {processingRef.current ? (
                                                <div className="flex items-center justify-center">
                                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                                    {__('common.adding')}
                                                </div>
                                            ) : (
                                                <>
                                                    <ShoppingCart className="mr-2 h-4 w-4" />
                                                    {__('marketplace.add_to_cart')}
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            asChild
                                            variant="outline"
                                            size="lg"
                                        >
                                            <Link href="/marketplace/cart">
                                                {__('marketplace.cart')}
                                            </Link>
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="icon"
                                            className="w-12 h-12"
                                            onClick={handleWishlist}
                                            title={isWishlist ? __('marketplace.remove_from_wishlist') : __('marketplace.add_to_wishlist')}
                                        >
                                            <Heart
                                                className={`h-5 w-5 ${isWishlist ? 'fill-current text-secondary' : ''}`}
                                            />
                                        </Button>
                                    </div>
                                </div>

                                <div className="border-t pt-6">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div className="flex items-center gap-2">
                                            <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                                                <Shield className="h-4 w-4 text-primary" />
                                            </div>
                                            <span className="text-sm text-muted-foreground">{__('marketplace.warranty_12_months')}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                                                <Truck className="h-4 w-4 text-primary" />
                                            </div>
                                            <span className="text-sm text-muted-foreground">{__('marketplace.nationwide_shipping')}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                                                <RefreshCw className="h-4 w-4 text-primary" />
                                            </div>
                                            <span className="text-sm text-muted-foreground">{__('marketplace.return_30_days')}</span>
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between pt-4 border-t">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-semibold">{__('marketplace.share')}:</span>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="icon" className="w-8 h-8">
                                                    <Facebook className="h-4 w-4" />
                                                </Button>
                                                <Button variant="outline" size="icon" className="w-8 h-8">
                                                    <Twitter className="h-4 w-4" />
                                                </Button>
                                                <Button variant="outline" size="icon" className="w-8 h-8">
                                                    <Share2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/* Bundle Items */}
                        <div className="border-t px-8 py-6">
                            <Tabs defaultValue="products" className="w-full">
                                <TabsList className="mb-4">
                                    <TabsTrigger value="products" className="flex items-center gap-2">
                                        <Package className="h-4 w-4" /> {__('product.products_in_bundle')} ({bundle.items?.length || 0})
                                    </TabsTrigger>
                                    <TabsTrigger value="description">
                                        {__('marketplace.product_description')}
                                    </TabsTrigger>
                                </TabsList>
                                <TabsContent value="products">
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {bundle.items?.map((item, index) => (
                                            <div key={index} className="bg-muted rounded-lg p-4">
                                                <Link
                                                    href={`/marketplace/product/${item.product?.slug || item.product?.id || 'unknown'}`}
                                                    className="block group"
                                                >
                                                    <div className="aspect-w-1 aspect-h-1 bg-card rounded-lg overflow-hidden mb-3">
                                                        <ImageWithFallback
                                                            src={item.product?.image_url || item.product?.image}
                                                            alt={item.product?.name || 'Product'}
                                                            fallbackText={item.product?.name ? item.product.name.charAt(0).toUpperCase() : 'P'}
                                                            className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                                                            width="w-full"
                                                            height="h-32"
                                                            rounded="rounded-none"
                                                        />
                                                    </div>
                                                    <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors mb-1">
                                                        {item.product?.name || __('product.product')}
                                                    </h3>
                                                    <p className="text-sm text-muted-foreground mb-2">
                                                        {item.product?.category?.name || __('marketplace.category')}
                                                    </p>
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-lg font-bold text-primary">
                                                            {formatCurrency(item.product?.sale_price || 0)}
                                                        </span>
                                                        <span className="text-sm text-muted-foreground">
                                                            {__('marketplace.quantity')}: {item.quantity || 1}
                                                        </span>
                                                    </div>
                                                </Link>
                                            </div>
                                        ))}
                                    </div>
                                </TabsContent>
                                <TabsContent value="description">
                                    <div className="prose max-w-none">
                                        {bundle.description ? (
                                            <p className="text-card-foreground leading-relaxed whitespace-pre-wrap">{bundle.description}</p>
                                        ) : (
                                            <p className="text-muted-foreground italic">{__('marketplace.no_bundle_description')}</p>
                                        )}
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </div>
                    </div>
                    {/* Related Bundles */}
                    {relatedBundles && relatedBundles.length > 0 && (
                        <div className="mt-12">
                            <h2 className="text-2xl font-bold text-foreground mb-6">{__('marketplace.related_bundles')}</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {relatedBundles.map((relatedBundle) => (
                                    <BundleCard key={relatedBundle.id} bundle={relatedBundle} />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <Footer />
        </>
    );
}
