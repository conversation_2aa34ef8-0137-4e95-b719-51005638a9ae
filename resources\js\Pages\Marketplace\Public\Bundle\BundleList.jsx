import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { ChevronRight, Filter, Grid } from 'lucide-react';
import { __ } from '@/utils/lang';
import BundleCard from '@/Components/Marketplace/BundleCard';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import Footer from '@/Components/Landing/Footer';
import Loading from '@/Components/Loading';
import { Button } from '@/Components/ui/button';

export default function BundleList({ bundles = { data: [], total: 0, from: 0, to: 0, links: [] }, topCategories = [], moreCategories = [], categories = [], filters = {} }) {
    const [currentFilters, setCurrentFilters] = useState(filters);
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [showFilters, setShowFilters] = useState(false);    const handleFilterChange = (newFilters) => {
        setCurrentFilters(newFilters);
        router.get('/marketplace/bundles', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSortChange = (sortValue) => {
        const newFilters = { ...currentFilters, sort: sortValue };
        handleFilterChange(newFilters);
    };

    const clearFilters = () => {
        router.get('/marketplace/bundles');
    };

    const handleFilterToggle = () => {
        setShowFilters(!showFilters);
    };    return (
        <div className="flex flex-col min-h-screen max-w-[1480px] mx-auto">
            <Head title={`${__('product.bundles')} - ${__('common.app_name')} ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('marketplace.home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('product.bundles')}</span>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="mx-auto px-2 sm:px-3 lg:px-4 py-4">
                <div className="flex gap-4">
                    {/* Filter Sidebar */}
                    <aside className={`w-72 bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] p-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-semibold text-primary">{__('product.filter_bundles')}</h3>
                            <button
                                onClick={clearFilters}
                                className="text-sm text-tertiary hover:underline"
                            >
                                {__('product.clear_all_filters')}
                            </button>
                        </div>

                        {/* Category Filter */}
                        {categories && categories.length > 0 && (
                            <div className="mb-6">
                                <h4 className="font-medium mb-3">{__('marketplace.categories')}</h4>
                                <div className="space-y-2">
                                    {categories.map(category => (
                                        <div key={category.id} className="flex items-center">
                                            <input
                                                type="checkbox"
                                                id={`category-${category.id}`}
                                                checked={currentFilters.categories?.includes(category.id)}
                                                onChange={(e) => {
                                                    const categories = currentFilters.categories || [];
                                                    const newCategories = e.target.checked
                                                        ? [...categories, category.id]
                                                        : categories.filter(id => id !== category.id);
                                                    handleFilterChange({ ...currentFilters, categories: newCategories });
                                                }}
                                                className="mr-2"
                                            />
                                            <label htmlFor={`category-${category.id}`} className="text-sm cursor-pointer">
                                                {category.name}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        <Button className="w-full bg-primary hover:bg-tertiary" onClick={() => handleFilterChange(currentFilters)}>
                            {__('product.apply_filters')}
                        </Button>
                    </aside>

                    {/* Bundle Listing */}
                    <main className="flex-1 min-w-0">
                        <div className="listing-header mb-4">
                            <div>
                                <h1 className="text-2xl font-bold text-primary">{__('product.bundles')}</h1>
                                <p className="text-sm text-gray-500">
                                    {__('product.showing_results', {
                                        from: bundles.from || 0,
                                        to: bundles.to || 0,
                                        total: bundles.total || 0
                                    })}
                                </p>
                            </div>
                            <Button
                                onClick={handleFilterToggle}
                                className="lg:hidden flex items-center gap-2"
                            >
                                <Filter className="h-4 w-4" />
                                <span>{__('product.filter_bundles')}</span>
                            </Button>
                        </div>

                        {/* Listing Controls */}
                        <div className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] px-3 py-4 mb-4">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <div className="flex flex-wrap gap-2">
                                    <span className="text-gray-400 text-sm">{__('product.filters')}: {Object.keys(currentFilters).length || 0}</span>
                                </div>
                                <div className="flex items-center gap-4">
                                    <select
                                        value={currentFilters.sort || 'featured'}
                                        onChange={(e) => handleSortChange(e.target.value)}
                                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                                    >
                                        <option value="featured">{__('product.sort_featured')}</option>
                                        <option value="price_low">{__('product.sort_price_asc')}</option>
                                        <option value="price_high">{__('product.sort_price_desc')}</option>
                                        <option value="name">{__('product.sort_name_asc')}</option>
                                        <option value="newest">{__('product.sort_newest')}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {bundles.data && bundles.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center bg-white rounded-lg shadow-sm p-8 min-h-[400px]">
                                <div className="text-gray-400 mb-4">
                                    <Grid className="h-16 w-16" />
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    {__('product.no_bundles_found')}
                                </h3>
                                <p className="text-gray-500 text-center mb-4">
                                    {__('product.no_bundles_desc')}
                                </p>
                                <Button onClick={clearFilters}>{__('product.clear_filters')}</Button>
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[400px]">
                                {bundles.data && bundles.data.map(bundle => (
                                    <BundleCard key={bundle.id} bundle={bundle} />
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        <div className="mt-8">
                            {bundles.total > 0 && bundles.links && (
                                <div className="flex justify-center">
                                    <div className="flex space-x-2">
                                        {bundles.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`px-3 py-2 rounded text-sm ${
                                                    link.active
                                                        ? 'bg-primary text-white'
                                                        : 'bg-white text-gray-700 hover:bg-gray-100 border'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </main>
                </div>
            </div>

            <Footer />
        </div>
    );
}
