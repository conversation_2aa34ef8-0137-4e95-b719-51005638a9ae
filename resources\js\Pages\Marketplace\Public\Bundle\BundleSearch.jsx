import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { ChevronRight, Search, Grid } from 'lucide-react';
import { __ } from '@/utils/lang';
import BundleCard from '@/Components/Marketplace/BundleCard';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import Footer from '@/Components/Landing/Footer';
import Loading from '@/Components/Loading';
import { Button } from '@/Components/ui/button';

export default function BundleSearch({ bundles = { data: [], total: 0, from: 0, to: 0, links: [] }, query = '', topCategories = [], moreCategories = [] }) {
    const [searchQuery, setSearchQuery] = useState(query || '');
    const [isPageLoading, setIsPageLoading] = useState(false);    const handleSearch = (e) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            router.get('/marketplace/bundles/search', { q: searchQuery.trim() });
        }
    };    return (
        <div className="flex flex-col min-h-screen max-w-[1480px] mx-auto">
            <Head title={`${__('product.search_bundles')} - "${query}" - ${__('common.app_name')} ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('product.home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <Link href="/marketplace/bundles" className="text-gray-500 hover:text-primary">{__('product.bundles')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('product.search_results')}</span>
                        {query && (
                            <>
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                                <span className="text-primary font-medium">"{query}"</span>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="mx-auto px-2 sm:px-3 lg:px-4 py-4">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex justify-between items-center mb-4">
                            <div>
                                <h1 className="text-3xl font-bold text-primary">
                                    {__('product.search_bundle_results')}
                                </h1>
                                {query && (
                                    <p className="text-gray-600 mt-2">
                                        {__('product.search_query')}: <span className="font-semibold">"{query}"</span>
                                    </p>
                                )}
                                <p className="text-sm text-gray-500 mt-1">
                                    {__('product.showing_results', {
                                        from: bundles.from || 0,
                                        to: bundles.to || 0,
                                        total: bundles.total || 0
                                    })}
                                </p>
                            </div>

                            <Link
                                href="/marketplace/bundles"
                                className="text-primary hover:text-tertiary font-medium"
                            >
                                ← {__('product.view_all_bundles')}
                            </Link>
                        </div>

                        {/* Search Form */}
                        <form onSubmit={handleSearch} className="max-w-md">
                            <div className="flex">
                                <input
                                    type="text"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    placeholder={__('product.search_bundles_placeholder')}
                                    className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                />
                                <Button
                                    type="submit"
                                    className="px-6 py-2 bg-primary text-white rounded-r-lg hover:bg-tertiary transition-colors"
                                >
                                    {__('common.search')}
                                </Button>
                            </div>
                        </form>
                    </div>

                    {/* Search Results */}
                    {bundles.data && bundles.data.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {bundles.data.map((bundle) => (
                                    <BundleCard key={bundle.id} bundle={bundle} />
                                ))}
                            </div>

                            {/* Pagination */}
                            {bundles.links && bundles.links.length > 3 && (
                                <div className="mt-8 flex justify-center">
                                    <div className="flex space-x-2">
                                        {bundles.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`px-3 py-2 rounded text-sm ${
                                                    link.active
                                                        ? 'bg-primary text-white'
                                                        : 'bg-white text-gray-700 hover:bg-gray-100 border'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-16">
                            <div className="text-gray-400 mb-6">
                                <Search className="h-16 w-16 mx-auto" />
                            </div>
                            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                                {query ? __('product.no_bundles_found') : __('product.enter_search_term')}
                            </h3>
                            <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                {query ? (
                                    __('product.no_bundles_search_desc', { query })
                                ) : (
                                    __('product.search_bundles_help')
                                )}
                            </p>
                            <div className="space-x-4">
                                <Link
                                    href="/marketplace/bundles"
                                    className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-tertiary transition-colors"
                                >
                                    {__('product.view_all_bundles')}
                                </Link>
                                <Link
                                    href="/marketplace/bundles/featured"
                                    className="bg-secondary text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors"
                                >
                                    {__('product.featured_bundles')}
                                </Link>
                            </div>
                        </div>
                    )}

                    {/* Search Suggestions */}
                    {bundles.data && bundles.data.length === 0 && query && (
                        <div className="mt-12 bg-white rounded-lg shadow-sm p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">{__('product.search_suggestions')}:</h3>
                            <ul className="space-y-2 text-gray-600">
                                <li>• {__('product.check_spelling')}</li>
                                <li>• {__('product.try_general_keywords')}</li>
                                <li>• {__('product.search_by_product_name')}</li>
                                <li>• {__('product.browse_by_category')}</li>
                            </ul>
                        </div>
                    )}
                </div>
            </div>

            <Footer />
        </div>
    );
}
