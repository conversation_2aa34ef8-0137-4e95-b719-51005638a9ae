import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ChevronRight, Star, Grid } from 'lucide-react';
import { __ } from '@/utils/lang';
import BundleCard from '@/Components/Marketplace/BundleCard';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import Footer from '@/Components/Landing/Footer';
import Loading from '@/Components/Loading';

export default function FeaturedBundles({ bundles = { data: [], total: 0, from: 0, to: 0, links: [] }, topCategories = [], moreCategories = [] }) {    return (
        <div className="flex flex-col min-h-screen max-w-[1480px] mx-auto">
            <Head title={`${__('product.featured_bundles')} - ${__('common.app_name')} ${__('marketplace.marketplace')}`} />

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('product.home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <Link href="/marketplace/bundles" className="text-gray-500 hover:text-primary">{__('product.bundles')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('product.featured_bundles')}</span>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="mx-auto px-2 sm:px-3 lg:px-4 py-4">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <div className="flex items-center justify-center mb-4">
                            <Star className="h-8 w-8 text-secondary mr-3" />
                            <h1 className="text-4xl font-bold text-primary">{__('product.featured_bundles')}</h1>
                        </div>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            {__('product.featured_bundles_desc')}
                        </p>
                        <div className="mt-6">
                            <Link
                                href="/marketplace/bundles"
                                className="text-primary hover:text-tertiary font-medium"
                            >
                                ← {__('product.view_all_bundles')}
                            </Link>
                        </div>
                        <p className="text-sm text-gray-500 mt-4">
                            {__('product.showing_results', {
                                from: bundles.from || 0,
                                to: bundles.to || 0,
                                total: bundles.total || 0
                            })}
                        </p>
                    </div>

                    {/* Featured Bundles Grid */}
                    {bundles.data && bundles.data.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {bundles.data.map((bundle) => (
                                    <BundleCard key={bundle.id} bundle={bundle} />
                                ))}
                            </div>

                            {/* Pagination */}
                            {bundles.links && bundles.links.length > 3 && (
                                <div className="mt-12 flex justify-center">
                                    <div className="flex space-x-2">
                                        {bundles.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`px-4 py-2 rounded-lg text-sm ${
                                                    link.active
                                                        ? 'bg-primary text-white'
                                                        : 'bg-white text-gray-700 hover:bg-gray-100 border'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-16">
                            <div className="text-gray-400 mb-6">
                                <Star className="h-16 w-16 mx-auto" />
                            </div>
                            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                                {__('product.no_featured_bundles')}
                            </h3>
                            <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                {__('product.no_featured_bundles_desc')}
                            </p>
                            <Link
                                href="/marketplace/bundles"
                                className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-tertiary transition-colors"
                            >
                                {__('product.view_all_bundles')}
                            </Link>
                        </div>
                    )}

                    {/* Call to Action */}
                    {bundles.data && bundles.data.length > 0 && (
                        <div className="mt-16 bg-primary rounded-lg p-8 text-center text-white">
                            <h2 className="text-2xl font-bold mb-4">{__('product.find_more_bundles')}</h2>
                            <p className="text-blue-100 mb-6">
                                {__('product.explore_bundle_collection')}
                            </p>
                            <Link
                                href="/marketplace/bundles"
                                className="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                            >
                                {__('product.view_all_bundles')}
                            </Link>
                        </div>
                    )}
                </div>
            </div>

            <Footer />
        </div>
    );
}
