import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import {
    Search,
    Heart,
    ShoppingCart,
    Eye,
    ChevronRight,
    Star,
    MapPin,
    Phone,
    Mail,
    Clock,
    Facebook,
    Instagram,
    Twitter,
    Youtube,
    CreditCard,
    User,
    ChevronDown,
    ArrowLeft,
    Lock,
    Trash2,
    Plus,
    Minus,
    X,
    Package,
    CheckCircle,
    RefreshCw,
    Gift,
    ArrowUpRight,
    TrendingUp
} from 'lucide-react';
import { __, formatCurrency } from '@/utils/lang';
import { FALLBACK_IMAGE_URL } from '@/constants/config';
import { useToast } from '@/Hooks/useToastContext';
import Loading from '@/Components/Loading';
import { Button } from '@/Components/ui/button';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import { Badge } from '@/Components/ui/badge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Footer from '@/Components/Landing/Footer';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import AddressSelect from '@/Components/AddressSelect';
import { Checkbox } from '@/Components/ui/checkbox';

export default function Cart({ cartItems: initialCartItems = [], cartCount: initialCartCount = 0, topCategories = [], moreCategories = [], paymentMethods = [], firstTimeDiscount = null, upsellProducts = [], crossSellProducts = [] }) {
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [cartItems, setCartItems] = useState([]);
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [showCheckout, setShowCheckout] = useState(false);
    const [isProcessingOrder, setIsProcessingOrder] = useState(false);
    const [isRedirectingToPayment, setIsRedirectingToPayment] = useState(false);
    const [isApplyingFirstTimeDiscount, setIsApplyingFirstTimeDiscount] = useState(false);

    const [selectedItems, setSelectedItems] = useState(new Set());
    const [shippingMethod, setShippingMethod] = useState('shipping');
    const [promoCode, setPromoCode] = useState('');
    const [activePromo, setActivePromo] = useState(null);
    const [discount, setDiscount] = useState(0);
    const [isValidatingCoupon, setIsValidatingCoupon] = useState(false);

    const [isCalculatingShipping, setIsCalculatingShipping] = useState(false);
    const [dynamicShippingFee, setDynamicShippingFee] = useState(null);
    const [dynamicShippingErrors, setDynamicShippingErrors] = useState(null);
    const [isDynamicShippingApplied, setIsDynamicShippingApplied] = useState(false);
    const [availableShippingServices, setAvailableShippingServices] = useState([]);
    const [selectedShippingService, setSelectedShippingService] = useState(null);

    useEffect(() => {
        if (initialCartItems && initialCartItems.length > 0) {
            setIsLoggedIn(true);
            setCartItems(initialCartItems);

            const localCart = JSON.parse(localStorage.getItem('cart') || '[]');
            if (localCart.length > 0) {
                syncLocalStorageCart(localCart);
                localStorage.removeItem('cart');
            }
        } else {
            setIsLoggedIn(false);
            const localCart = JSON.parse(localStorage.getItem('cart') || '[]');
            setCartItems(localCart);
        }
    }, [initialCartItems]);

    const getCsrfToken = () => {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) {
            console.warn('CSRF token not found. Please ensure the meta tag is present in the page head.');
            return '';
        }
        return token;
    };

    const syncLocalStorageCart = async (localCart) => {
        try {
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error('CSRF token not found, cannot sync cart');
                return;
            }

            await fetch('/marketplace/cart/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    items: localCart.map(item => ({
                        product_id: item.product_id || item.id,
                        quantity: item.quantity
                    }))
                })
            });

            router.reload();
        } catch (error) {
            console.error('Failed to sync cart:', error);
        }
    };

    const updateItemQuantity = async (itemId, newQuantity) => {
        if (newQuantity < 1) return;

        if (isLoggedIn) {
            try {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('cart.auth_error'));
                    return;
                }

                const response = await fetch(`/marketplace/cart/${itemId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ quantity: newQuantity })
                });

                const data = await response.json();
                if (data.success) {
                    setCartItems(items =>
                        items.map(item =>
                            item.id === itemId ? { ...item, quantity: newQuantity, subtotal: item.price * newQuantity } : item
                        )
                    );
                } else {
                    addAlert('error', data.error || __('cart.update_error'));
                }
            } catch (error) {
                addAlert('error', __('cart.cart_update_error'));
            }
        } else {
            const updatedItems = cartItems.map(item =>
                item.id === itemId ? { ...item, quantity: newQuantity, subtotal: item.price * newQuantity } : item
            );
            setCartItems(updatedItems);
            localStorage.setItem('cart', JSON.stringify(updatedItems));
        }
    };

    const removeItem = async (itemId) => {
        if (isLoggedIn) {
            try {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('cart.auth_error'));
                    return;
                }

                const response = await fetch(`/marketplace/cart/${itemId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    }
                });

                const data = await response.json();
                if (data.success) {
                    setCartItems(items => items.filter(item => item.id !== itemId));
                    addAlert('success', data.message);
                } else {
                    addAlert('error', data.error || __('cart.update_error'));
                }
            } catch (error) {
                addAlert('error', __('cart.cart_remove_error'));
            }
        } else {
            const updatedItems = cartItems.filter(item => item.id !== itemId);
            setCartItems(updatedItems);
            localStorage.setItem('cart', JSON.stringify(updatedItems));
            addAlert('success', __('cart.removed_from_cart'));

            window.dispatchEvent(new CustomEvent('cartUpdated'));
        }
    };

    const [checkoutData, setCheckoutData] = useState({
        customer_name: auth?.user?.name || '',
        customer_phone: auth?.user?.phone || '',
        customer_email: auth?.user?.email || '',
        shipping_address: '',
        province_name: '',
        district_name: '',
        ward_name: '',
        province_id: null,
        district_id: null,
        ward_id: null,
        payment_method_id: paymentMethods.length > 0 ? paymentMethods[0].id : null,
        notes: ''
    });

    const [shippingOptions, setShippingOptions] = useState({
        shipping: { name: __('cart.shipping'), price: 0, time: __('cart.shipping_standard_time') }
    });

    const applyFirstTimeCoupon = () => {
        if (!firstTimeDiscount || !isLoggedIn) return;

        setPromoCode(firstTimeDiscount.code);


        setTimeout(() => {
            applyPromoCode(firstTimeDiscount.code);
        }, 100);
    };

    const applyPromoCode = async (code = null) => {
        const couponCode = code || promoCode;
        if (!couponCode.trim()) return;

        if (!isLoggedIn) {
            addAlert('error', __('cart.login_required_for_coupon'));
            return;
        }

        setIsValidatingCoupon(true);

        try {
            const response = await fetch('/marketplace/orders/validate-coupon', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken(),
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    code: couponCode,
                    cart_total: subtotal,
                    cart_items: cartItems.map(item => ({
                        product_id: item.product_id || item.id,
                        quantity: item.quantity,
                        price: item.price
                    }))
                })
            });

            const data = await response.json();

            if (data.valid) {
                setDiscount(data.discount);
                setActivePromo({
                    code: couponCode,
                    discount: data.coupon.type === 'percentage' ? `${data.coupon.value}%` : formatCurrency(data.coupon.value)
                });
                addAlert('success', data.message);
            } else {
                addAlert('error', data.message);
            }
        } catch (error) {
            addAlert('error', __('cart.coupon_validation_error'));
        } finally {
            setIsValidatingCoupon(false);
        }
    };

    const removePromo = () => {
        setActivePromo(null);
        setDiscount(0);
        setPromoCode('');
        addAlert('info', __('cart.promo_removed'));
    };

    useEffect(() => {
        if (cartItems.length > 0) {
            const availableItemIds = cartItems
                .filter(item => item.status && item.stock_quantity > 0)
                .map(item => item.id);
            setSelectedItems(new Set(availableItemIds));
        } else {
            setSelectedItems(new Set());
        }
    }, [cartItems]);

    const handleItemSelect = (itemId, isSelected) => {
        const newSelectedItems = new Set(selectedItems);
        if (isSelected) {
            newSelectedItems.add(itemId);
        } else {
            newSelectedItems.delete(itemId);
        }
        setSelectedItems(newSelectedItems);
    };

    const handleSelectAll = (selectAll) => {
        if (selectAll) {
            const availableItemIds = cartItems
                .filter(item => item.status && item.stock_quantity > 0)
                .map(item => item.id);
            setSelectedItems(new Set(availableItemIds));
        } else {
            setSelectedItems(new Set());
        }
    };

    const selectedCartItems = cartItems.filter(item => selectedItems.has(item.id));
    const subtotal = selectedCartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shippingFee = shippingOptions[shippingMethod].price;
    const total = subtotal + shippingFee - discount;

    const availableItems = cartItems.filter(item => item.status && item.stock_quantity > 0);
    const isAllSelected = availableItems.length > 0 && availableItems.every(item => selectedItems.has(item.id));
    const isPartiallySelected = selectedItems.size > 0 && !isAllSelected;

    const handleCheckout = () => {
        if (!isLoggedIn) {
            addAlert('info', __('cart.login_required'));
            return;
        }

        if (selectedItems.size === 0) {
            addAlert('error', __('cart.no_items_selected'));
            return;
        }

        setShowCheckout(true);
    };

    const handleInputChange = (field, value) => {
        setCheckoutData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleAddressChange = (addressData) => {
        setCheckoutData(prev => {
            const specificAddress = prev.shipping_address.replace(
                /, (.*?)$/, ''
            ).trim();

            const addressParts = [];
            if (specificAddress) addressParts.push(specificAddress);
            if (addressData.ward) addressParts.push(addressData.ward);
            if (addressData.district) addressParts.push(addressData.district);
            if (addressData.province) addressParts.push(addressData.province);

            const fullAddress = addressParts.join(', ');

            return {
                ...prev,
                province_name: addressData.province,
                district_name: addressData.district,
                ward_name: addressData.ward,
                province_id: addressData.provinceId,
                district_id: addressData.districtId,
                ward_id: addressData.wardId,
                full_shipping_address: fullAddress
            };
        });
    };

    const handleSpecificAddressChange = (value) => {
        setCheckoutData(prev => {
            const addressParts = [];
            if (value.trim()) addressParts.push(value.trim());
            if (prev.ward_name) addressParts.push(prev.ward_name);
            if (prev.district_name) addressParts.push(prev.district_name);
            if (prev.province_name) addressParts.push(prev.province_name);

            const fullAddress = addressParts.join(', ');

            return {
                ...prev,
                shipping_address: value,
                full_shipping_address: fullAddress
            };
        });
    };

    useEffect(() => {
        if (checkoutData.province_id && checkoutData.district_id && checkoutData.ward_id) {
            const timer = setTimeout(() => {
                calculateShippingFee();
            }, 500);

            return () => clearTimeout(timer);
        }
    }, [checkoutData.province_id, checkoutData.district_id, checkoutData.ward_id]);

    const calculateShippingFee = async () => {
        if (!checkoutData.province_id || !checkoutData.district_id || !checkoutData.ward_id) {
            return;
        }

        if (isCalculatingShipping) {
            return;
        }

        setIsCalculatingShipping(true);
        setDynamicShippingErrors(null);

        const currentServiceId = selectedShippingService;

        try {
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error('CSRF token not found, cannot calculate shipping');
                setIsCalculatingShipping(false);
                return;
            }

            const cacheKey = `shipping_${checkoutData.province_id}_${checkoutData.district_id}_${checkoutData.ward_id}_${subtotal}`;

            const cachedData = sessionStorage.getItem(cacheKey);
            if (cachedData) {
                const data = JSON.parse(cachedData);
                processShippingData(data, currentServiceId);
                setIsCalculatingShipping(false);
                return;
            }

            const response = await fetch('/marketplace/cart/calculate-shipping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    province_id: checkoutData.province_id,
                    district_id: checkoutData.district_id,
                    ward_code: checkoutData.ward_id,
                    subtotal: subtotal
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionStorage.setItem(cacheKey, JSON.stringify(data));
                processShippingData(data, currentServiceId);
            } else {
                setDynamicShippingErrors(data.message || __('cart.shipping_calculation_error'));
            }
        } catch (error) {
            console.error('Failed to calculate shipping fees:', error);
            setDynamicShippingErrors(__('cart.shipping_calculation_error'));
        } finally {
            setIsCalculatingShipping(false);
        }
    };

    const processShippingData = (data, currentServiceId) => {
        if (data.services && data.services.length > 0) {
            setAvailableShippingServices(data.services);

            let serviceIdToSelect;
            const serviceExists = data.services.some(service => service.service_id === currentServiceId);

            if (serviceExists && currentServiceId) {
                serviceIdToSelect = currentServiceId;
            } else {
                serviceIdToSelect = data.services[0].service_id;
            }

            setSelectedShippingService(serviceIdToSelect);

            const serviceOptions = {};
            data.services.forEach(service => {
                serviceOptions[`service_${service.service_id}`] = {
                    name: service.short_name || __('cart.shipping'),
                    price: data.fee,
                    time: data.extra_data?.expected_delivery_time || __('cart.shipping_time'),
                    is_free: data.is_free || false,
                    dynamic: data.dynamic || false,
                    service_id: service.service_id,
                    service_type_id: service.service_type_id
                };
            });

            setShippingOptions(serviceOptions);

            const methodKey = `service_${serviceIdToSelect}`;
            if (Object.keys(serviceOptions).includes(methodKey)) {
                handleShippingMethodChange(methodKey);
            } else if (Object.keys(serviceOptions).length > 0) {
                handleShippingMethodChange(Object.keys(serviceOptions)[0]);
            }
        } else {
            setShippingOptions({
                shipping: {
                    name: __('cart.shipping'),
                    price: data.fee,
                    time: data.extra_data?.expected_delivery_time || __('cart.shipping_time'),
                    is_free: data.is_free || false,
                    dynamic: data.dynamic || false
                }
            });
            handleShippingMethodChange('shipping');
        }

        setIsDynamicShippingApplied(data.dynamic || false);
        setDynamicShippingFee(data.fee);

        if (data.dynamic) {
            addAlert('info', __('cart.dynamic_shipping_applied'));
        }
    };

    const submitOrder = async (e) => {
        e.preventDefault();

        if (!checkoutData.customer_name || !checkoutData.customer_phone || !checkoutData.shipping_address || !checkoutData.payment_method_id || !checkoutData.province_id || !checkoutData.district_id || !checkoutData.ward_id) {
            addAlert('error', __('cart.required_fields'));
            return;
        }

        if (selectedItems.size === 0) {
            addAlert('error', __('cart.no_items_selected'));
            return;
        }

        setIsProcessingOrder(true);

        try {
            const response = await fetch('/marketplace/orders/checkout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken(),
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    ...checkoutData,
                    shipping_address: checkoutData.full_shipping_address || checkoutData.shipping_address,
                    shipping_fee: shippingFee,
                    coupon_code: activePromo?.code || null,
                    cart_items: selectedCartItems.map(item => {
                        const isBundle = item.item_type === 'bundle';
                        return {
                            item_type: item.item_type || 'product',
                            product_id: isBundle ? null : (item.product_id || item.id),
                            bundle_id: isBundle ? (item.bundle_id || item.id) : null,
                            quantity: item.quantity,
                            price: item.price
                        };
                    })
                })
            });

            const data = await response.json();

            if (data.success) {
                addAlert('success', data.message);

                if (isLoggedIn) {
                    setCartItems(prevItems => prevItems.filter(item => !selectedItems.has(item.id)));
                } else {
                    const updatedItems = cartItems.filter(item => !selectedItems.has(item.id));
                    setCartItems(updatedItems);
                    localStorage.setItem('cart', JSON.stringify(updatedItems));
                }

                setSelectedItems(new Set());
                window.dispatchEvent(new CustomEvent('cartUpdated'));

                if (data.redirect_to_payment && data.payment_url) {
                    setIsRedirectingToPayment(true);
                    addAlert('info', __('cart.redirecting_to_payment'));

                    const paymentResponse = await fetch(data.payment_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': getCsrfToken(),
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: data.order.id,
                            return_url: window.location.origin + (data.order.payment_method === 'vnpay'
                                ? '/marketplace/payment/vnpay/return'
                                : '/marketplace/payment/momo/return')
                        })
                    });

                    const paymentData = await paymentResponse.json();

                    if (paymentData.success && paymentData.payment_url) {
                        setTimeout(() => {
                            window.location.href = paymentData.payment_url;
                        }, 500);
                        return;
                    } else {
                        setIsRedirectingToPayment(false);
                        addAlert('error', paymentData.message || __('checkout.payment_link_error'));
                        return;
                    }
                }

                router.visit(`/marketplace/my-orders/${data.order.id}`);
            } else {
                addAlert('error', data.message);
            }
        } catch (error) {
            addAlert('error', __('cart.order_error'));
        } finally {
            setIsProcessingOrder(false);
        }
    };

    const renderPaymentMethods = () => {
        if (paymentMethods.length === 0) {
            return (
                <div className="text-center py-4 text-gray-500">
                    {__('cart.no_payment_methods')}
                </div>
            );
        }

        return paymentMethods.map((method) => (
            <div
                key={method.id}
                className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                    checkoutData.payment_method_id === method.id ? 'border-primary bg-primary/5' : 'border-gray-200'
                }`}
                onClick={() => handleInputChange('payment_method_id', method.id)}
            >
                <input
                    type="radio"
                    checked={checkoutData.payment_method_id === method.id}
                    onChange={() => handleInputChange('payment_method_id', method.id)}
                    className="text-primary"
                />
                <div className="flex-1">
                    <div className="font-medium flex items-center gap-2">
                        {method.logo_url && (
                            <img
                                src={method.logo_url}
                                alt={method.payment_name}
                                className="w-8 h-8 object-contain"
                            />
                        )}
                        {method.payment_name}
                    </div>
                    {method.description && (
                        <div className="text-sm text-gray-500">{method.description}</div>
                    )}
                </div>
            </div>
        ));
    };

    const renderUpsellProducts = () => {
        if (!upsellProducts || upsellProducts.length === 0) {
            return null;
        }

        return (
            <div className="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 className="text-lg font-semibold mb-6 flex items-center">
                    <TrendingUp className="h-5 w-5 text-primary mr-2" />
                    {__('cart.premium_alternatives')}
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    {upsellProducts.map((product) => (
                        <div key={product.id} className="border rounded-lg p-3 hover:border-primary transition-colors">
                            <Link href={`/marketplace/product/${product.slug}`} className="block">
                                <div className="relative pt-[100%]">
                                    <ImageWithFallback
                                        src={product.image}
                                        alt={product.name}
                                        fallbackText={product.name.charAt(0)}
                                        width="w-full"
                                        height="h-full"
                                        imageClassName="object-cover absolute top-0 left-0"
                                        rounded="rounded-lg"
                                        bgColor="bg-gray-100"
                                        textColor="text-primary"
                                        textSize="text-2xl"
                                    />
                                </div>
                                <h3 className="mt-3 font-medium text-gray-900 line-clamp-2 h-12">{product.name}</h3>
                                <p className="mt-1 text-sm text-gray-500">{product.category}</p>
                                <div className="mt-2 flex justify-between items-center">
                                    <span className="text-primary font-semibold">{formatCurrency(product.price)}</span>
                                    <button
                                        onClick={(e) => {
                                            e.preventDefault();
                                            addToCart(product.id);
                                        }}
                                        className="flex items-center bg-primary text-white rounded-full p-2 hover:bg-tertiary transition-colors"
                                    >
                                        <Plus className="h-4 w-4" />
                                    </button>
                                </div>
                            </Link>
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderCrossSellProducts = () => {
        if (!crossSellProducts || crossSellProducts.length === 0) {
            return null;
        }

        return (
            <div className="bg-white rounded-lg shadow-sm p-6 mt-8">
                <h2 className="text-lg font-semibold mb-6 flex items-center">
                    <Package className="h-5 w-5 text-primary mr-2" />
                    {__('cart.cross_sell_products')}
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    {crossSellProducts.map((product) => (
                        <div key={product.id} className="border rounded-lg p-3 hover:border-primary transition-colors">
                            <Link href={`/marketplace/product/${product.slug}`} className="block">
                                <div className="relative pt-[100%]">
                                    <ImageWithFallback
                                        src={product.image}
                                        alt={product.name}
                                        fallbackText={product.name.charAt(0)}
                                        width="w-full"
                                        height="h-full"
                                        imageClassName="object-cover absolute top-0 left-0"
                                        rounded="rounded-lg"
                                        bgColor="bg-gray-100"
                                        textColor="text-primary"
                                        textSize="text-2xl"
                                    />
                                </div>
                                <h3 className="mt-3 font-medium text-gray-900 line-clamp-2 h-12">{product.name}</h3>
                                <p className="mt-1 text-sm text-gray-500">{product.category}</p>
                                <div className="mt-2 flex justify-between items-center">
                                    <span className="text-primary font-semibold">{formatCurrency(product.price)}</span>
                                    <button
                                        onClick={(e) => {
                                            e.preventDefault();
                                            addToCart(product.id);
                                        }}
                                        className="flex items-center bg-primary text-white rounded-full p-2 hover:bg-tertiary transition-colors"
                                    >
                                        <Plus className="h-4 w-4" />
                                    </button>
                                </div>
                            </Link>
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const addToCart = async (productId) => {
        if (!productId) return;

        try {
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                addAlert('error', __('cart.auth_error'));
                return;
            }

            const response = await fetch('/marketplace/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                addAlert('success', data.message);
                router.reload();
            } else {
                addAlert('error', data.error || __('cart.add_error'));
            }
        } catch (error) {
            addAlert('error', __('cart.cart_add_error'));
        }
    };

    const renderOrderSummary = (isCheckoutPage = false) => {
        return (
            <div className="space-y-4">
                {selectedItems.size === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        <p>{isCheckoutPage ? __('cart.no_items_selected') : __('cart.select_items_to_checkout')}</p>
                    </div>
                ) : (
                    <>
                        {isCheckoutPage && (
                            <div className="max-h-40 overflow-y-auto space-y-2">
                                {selectedCartItems.map(item => (
                                    <div key={item.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                                        <div className="w-10 h-10 rounded overflow-hidden">
                                            <ImageWithFallback
                                                src={item.image}
                                                alt={item.name}
                                                fallbackText={item.name.charAt(0)}
                                                width="w-full"
                                                height="h-full"
                                                imageClassName="object-cover"
                                                rounded="rounded"
                                                bgColor="bg-gray-100"
                                                textColor="text-primary"
                                                textSize="text-xs"
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">{item.name}</p>
                                            <p className="text-xs text-gray-500">{item.quantity}x {formatCurrency(item.price)}</p>
                                        </div>
                                        <span className="text-sm font-medium">{formatCurrency(item.price * item.quantity)}</span>
                                    </div>
                                ))}
                            </div>
                        )}

                        <div className="flex justify-between text-sm">
                            <span className="text-gray-500">{__('cart.subtotal')}</span>
                            <span className="font-medium">{formatCurrency(subtotal)}</span>
                        </div>

                        {isCheckoutPage && (
                            <div className="space-y-3">
                                <h3 className="font-medium">{__('cart.shipping')}</h3>
                                {Object.entries(shippingOptions).map(([key, option]) => (
                                    <div
                                        key={key}
                                        className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                                            shippingMethod === key ? 'border-primary bg-primary/5' : 'border-gray-200'
                                        }`}
                                        onClick={() => handleShippingMethodChange(key)}
                                    >
                                        <input
                                            type="radio"
                                            checked={shippingMethod === key}
                                            onChange={() => handleShippingMethodChange(key)}
                                            className="text-primary"
                                        />
                                        <div className="flex-1">
                                            <div className="font-medium">{option.name}</div>
                                            <div className="text-sm text-gray-500">{option.time}</div>
                                            {option.dynamic && shippingMethod === key && (
                                                <div className="text-xs text-primary mt-1">
                                                    <RefreshCw className="h-3 w-3 mr-1 inline" /> {__('cart.dynamic_pricing')}
                                                </div>
                                            )}
                                            {option.is_free && (
                                                <div className="text-xs text-green-600 mt-1">
                                                    <Gift className="h-3 w-3 mr-1 inline" /> {__('cart.free_shipping_applied')}
                                                </div>
                                            )}
                                        </div>
                                        <div className="font-medium">
                                            {isCalculatingShipping && shippingMethod === key ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                option.is_free
                                                    ? <span className="text-green-600">{__('cart.free')}</span>
                                                    : formatCurrency(option.price)
                                            )}
                                        </div>
                                    </div>
                                ))}
                                {dynamicShippingErrors && (
                                    <div className="text-sm text-red-500 mt-1">
                                        <i className="fas fa-exclamation-triangle mr-1"></i> {dynamicShippingErrors}
                                    </div>
                                )}
                                {checkoutData.province_id && checkoutData.district_id && checkoutData.ward_id && !isCalculatingShipping && (
                                    <button
                                        type="button"
                                        onClick={calculateShippingFee}
                                        className="text-sm text-primary hover:text-tertiary transition-colors flex items-center"
                                    >
                                        <RefreshCw className="h-3 w-3 mr-1" /> {__('cart.recalculate_shipping')}
                                    </button>
                                )}
                            </div>
                        )}

                        <div className="space-y-3">
                            <h3 className="font-medium">{__('cart.promo_code')}</h3>
                            {activePromo ? (
                                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                    <div className="text-sm">
                                        <span className="font-medium text-primary">{activePromo.code}</span>
                                        <span className="text-gray-500 ml-2">-{activePromo.discount}</span>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={removePromo}
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            ) : (
                                <div>
                                    {isLoggedIn && firstTimeDiscount && (
                                        <div className="mb-3 p-2 bg-green-50 rounded-md border border-green-100">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <Gift className="h-4 w-4 text-green-600" />
                                                    <span className="text-sm font-medium text-green-700">
                                                        {__('cart.available_discount')}: {firstTimeDiscount.code}
                                                    </span>
                                                </div>
                                                <Button
                                                    variant="link"
                                                    size="sm"
                                                    onClick={() => setPromoCode(firstTimeDiscount.code)}
                                                    className="text-green-600"
                                                >
                                                    {__('cart.use_code')}
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    <div className="flex gap-2">
                                        <TextInputWithLabel
                                            type="text"
                                            placeholder={isLoggedIn ? __('cart.enter_promo') : __('cart.login_required_for_coupon')}
                                            value={promoCode}
                                            onChange={(e) => setPromoCode(e.target.value)}
                                            className="flex-1"
                                            disabled={!isLoggedIn}
                                        />
                                        <Button
                                            onClick={() => applyPromoCode()}
                                            disabled={isValidatingCoupon || !isLoggedIn}
                                            className="bg-primary hover:bg-tertiary"
                                        >
                                            {isValidatingCoupon ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                __('cart.apply')
                                            )}
                                        </Button>
                                    </div>
                                    {!isLoggedIn && (
                                        <p className="text-xs text-gray-500 mt-1">
                                            {__('cart.login_required_for_coupon')}
                                        </p>
                                    )}
                                </div>
                            )}
                        </div>

                        {discount > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-gray-500">{__('cart.discount')}</span>
                                <span className="font-medium text-red-500">-{formatCurrency(discount)}</span>
                            </div>
                        )}

                        <div className="flex justify-between text-lg font-semibold pt-4 border-t">
                            <span>{__('cart.total')}</span>
                            <span className="text-primary">
                                {formatCurrency(isCheckoutPage ? (subtotal + shippingFee - discount) : subtotal - discount)}
                            </span>
                        </div>
                    </>
                )}

                <Button
                    className="w-full bg-primary hover:bg-tertiary mt-6"
                    onClick={isCheckoutPage ? null : handleCheckout}
                    disabled={selectedItems.size === 0 || (isCheckoutPage && isProcessingOrder)}
                    type={isCheckoutPage ? "submit" : "button"}
                >
                    {isCheckoutPage ? (
                        <>
                            <Lock className="h-4 w-4 mr-2" />
                            {isProcessingOrder
                                ? __('cart.processing_order')
                                : `${__('cart.complete_order')} (${formatCurrency(subtotal + shippingFee - discount)})`
                            }
                        </>
                    ) : !isLoggedIn ? (

                        <>
                            <User className="h-4 w-4 mr-2" />
                            {selectedItems.size === 0
                                ? __('cart.select_items_first')
                                : __('cart.login_required')
                            }
                        </>
                    ) : (

                        <>
                            <Lock className="h-4 w-4 mr-2" />
                            {selectedItems.size === 0
                                ? __('cart.select_items_first')
                                : `${__('cart.checkout')} (${selectedItems.size})`
                            }
                        </>
                    )}
                </Button>

                {!isLoggedIn && !isCheckoutPage && selectedItems.size > 0 && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex items-center gap-2 text-blue-700">
                            <User className="h-4 w-4" />
                            <span className="text-sm font-medium">
                                {__('cart.login_required')}
                            </span>
                        </div>
                        <p className="text-xs text-blue-600 mt-1">
                            {__('cart.login_to_check_availability')}
                        </p>
                        <div className="flex gap-2 mt-2">
                            <Link
                                href="/login"
                                className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                            >
                                {__('cart.login_button')}
                            </Link>
                            <Link
                                href="/login?tab=register"
                                className="text-xs border border-blue-600 text-blue-600 px-3 py-1 rounded hover:bg-blue-50 transition-colors"
                            >
                                {__('cart.register_button')}
                            </Link>
                        </div>
                    </div>
                )}

                <div className="text-center mt-4">
                    <p className="text-sm text-gray-500 mb-2">{__('cart.payment_methods')}</p>
                    <div className="flex justify-center gap-4 text-gray-400">
                        <CreditCard className="h-6 w-6" />
                        <CreditCard className="h-6 w-6" />
                        <CreditCard className="h-6 w-6" />
                        <CreditCard className="h-6 w-6" />
                    </div>
                </div>
            </div>
        );
    };


    const handleShippingMethodChange = (methodKey) => {
        setShippingMethod(methodKey);


        if (methodKey.startsWith('service_')) {
            const serviceId = parseInt(methodKey.replace('service_', ''), 10);
            setSelectedShippingService(serviceId);



        }
    };

    if (showCheckout) {
        return (
            <div className="flex flex-col min-h-screen bg-gray-50">
                <Head title={`${__('cart.checkout')} - PickleSocial ${__('marketplace.marketplace')}`} />
                {isProcessingOrder && <Loading fullScreen text={__('cart.processing_order')} />}

                <MarketplaceHeader
                    topCategories={topCategories}
                    moreCategories={moreCategories}
                />

                <div className="bg-white border-b">
                    <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex items-center space-x-2 text-sm">
                            <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('cart.home')}</Link>
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                            <Link href="/marketplace/cart" className="text-gray-500 hover:text-primary">{__('cart.title')}</Link>
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                            <span className="text-primary font-medium">{__('cart.checkout')}</span>
                        </div>
                    </div>
                </div>

                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl font-bold text-primary">
                            {__('cart.checkout')} ({selectedItems.size} {__('cart.selected_items')})
                        </h1>
                        <button
                            onClick={() => setShowCheckout(false)}
                            className="flex items-center text-tertiary hover:text-primary transition-colors"
                        >
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            {__('cart.back_to_cart')}
                        </button>
                    </div>

                    <form onSubmit={submitOrder} className="flex flex-col lg:flex-row gap-8">
                        <div className="flex-1 bg-white rounded-lg shadow-sm p-6">
                            <h2 className="text-lg font-semibold mb-6">{__('cart.shipping_info')}</h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <TextInputWithLabel
                                    label={__('cart.customer_name')}
                                    type="text"
                                    value={checkoutData.customer_name}
                                    onChange={(e) => handleInputChange('customer_name', e.target.value)}
                                    required
                                />
                                <TextInputWithLabel
                                    label={__('cart.customer_phone')}
                                    type="tel"
                                    value={checkoutData.customer_phone}
                                    onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                                    required
                                />
                                <div className="md:col-span-2">
                                    <TextInputWithLabel
                                        label={__('cart.customer_email')}
                                        type="email"
                                        value={checkoutData.customer_email}
                                        onChange={(e) => handleInputChange('customer_email', e.target.value)}
                                    />
                                </div>
                            </div>

                            <div className="mb-6">
                                <h3 className="text-md font-semibold mb-4">{__('cart.address')}</h3>
                                <AddressSelect
                                    onChange={handleAddressChange}
                                    defaultValues={{
                                        provinceId: checkoutData.province_id,
                                        districtId: checkoutData.district_id,
                                        wardId: checkoutData.ward_id
                                    }}
                                    required={true}
                                    labels={{
                                        province: __('cart.province'),
                                        district: __('cart.district'),
                                        ward: __('cart.ward')
                                    }}
                                />
                            </div>

                            <div className="mb-6">
                                <TextInputWithLabel
                                    label={__('cart.specific_address')}
                                    type="text"
                                    value={checkoutData.shipping_address}
                                    onChange={(e) => handleSpecificAddressChange(e.target.value)}
                                    placeholder={__('cart.specific_address_placeholder')}
                                    required
                                />
                            </div>

                            {checkoutData.full_shipping_address && (
                                <div className="mb-6 p-3 bg-gray-50 rounded-lg">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {__('cart.full_shipping_address')}
                                    </label>
                                    <p className="text-sm text-gray-600">{checkoutData.full_shipping_address}</p>
                                </div>
                            )}

                            <h3 className="text-md font-semibold mb-4">{__('cart.payment_method')}</h3>
                            <div className="space-y-3 mb-6">
                                {renderPaymentMethods()}
                            </div>

                            <TextInputWithLabel
                                label={__('cart.order_notes')}
                                type="textarea"
                                value={checkoutData.notes}
                                onChange={(e) => handleInputChange('notes', e.target.value)}
                                placeholder={__('cart.order_notes_placeholder')}
                                rows={3}
                            />
                        </div>

                        <div className="lg:w-96 bg-white rounded-lg shadow-sm p-6 h-fit">
                            <h2 className="text-lg font-semibold mb-6 pb-4 border-b">
                                {__('cart.order_summary')} ({selectedItems.size} {__('cart.items')})
                            </h2>

                            {renderOrderSummary(true)}
                        </div>
                    </form>
                </div>

                <Footer />
            </div>
        );
    }

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`${__('cart.title')} - PickleSocial ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            <div className="bg-white border-b">
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('cart.home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('cart.title')}</span>
                    </div>
                </div>
            </div>

            <div className="mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-2xl font-bold text-primary">
                        {__('cart.title')} <span className="font-normal text-gray-500">({cartItems.length} {__('cart.items')})</span>
                    </h1>
                    <Link
                        href="/marketplace"
                        className="flex items-center text-tertiary hover:text-primary transition-colors"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        {__('cart.continue_shopping')}
                    </Link>
                </div>

                {isLoggedIn && firstTimeDiscount && !activePromo && cartItems.length > 0 && (
                    <div className="w-full bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4 mb-4">
                        <div className="flex items-center gap-3">
                            <div className="bg-primary rounded-full p-2 text-white">
                                <Gift className="h-5 w-5" />
                            </div>
                            <div className="flex-1">
                                <h3 className="font-medium text-primary">{__('cart.first_time_discount')}</h3>
                                <p className="text-sm text-gray-600">
                                    {firstTimeDiscount.description || __('cart.first_time_discount_description')}:
                                    <span className="font-bold text-primary"> {firstTimeDiscount.discountText}</span> {__('cart.off')}
                                </p>
                            </div>
                            <Button
                                onClick={applyFirstTimeCoupon}
                                className="bg-primary hover:bg-tertiary text-white"
                                disabled={isApplyingFirstTimeDiscount}
                            >
                                {isApplyingFirstTimeDiscount ? (
                                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                                ) : null}
                                {__('cart.apply_discount')}
                            </Button>
                        </div>
                    </div>
                )}

                {cartItems.length === 0 ? (
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="text-center py-12">
                            <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500 mb-4">{__('cart.empty')}</p>
                            <Link
                                href="/marketplace"
                                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-tertiary transition-colors"
                            >
                                {__('cart.shop_now')}
                            </Link>
                        </div>
                    </div>
                ) : (
                    <div className="flex flex-col lg:flex-row gap-8">
                        <div className="flex-1 bg-white rounded-lg shadow-sm p-6">
                            <div className="flex items-center justify-between mb-4 pb-4 border-b">
                                <div className="flex items-center gap-3">
                                    <Checkbox
                                        checked={isAllSelected}
                                        onCheckedChange={(checked) => handleSelectAll(checked)}
                                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                                    />
                                    <span className="text-sm font-medium text-gray-700">
                                        {isAllSelected ? __('cart.deselect_all') : __('cart.select_all')}
                                    </span>
                                </div>
                                {selectedItems.size > 0 && (
                                    <span className="text-sm text-gray-500">
                                        {selectedItems.size} {__('cart.items_selected')}
                                    </span>
                                )}
                            </div>

                            <div className="grid grid-cols-12 gap-4 mb-4 pb-4 border-b text-sm font-medium text-gray-500">
                                <div className="col-span-1"></div>
                                <div className="col-span-5">{__('cart.product')}</div>
                                <div className="col-span-2 text-center">{__('cart.price')}</div>
                                <div className="col-span-2 text-center">{__('cart.quantity')}</div>
                                <div className="col-span-2 text-right">{__('cart.subtotal')}</div>
                            </div>

                            {cartItems.map(item => {

                                const isAvailable = isLoggedIn
                                    ? (item.item_type === 'bundle' ? item.is_available : (item.status && item.stock_quantity > 0))
                                    : true;
                                const isSelected = selectedItems.has(item.id);
                                const isBundle = item.item_type === 'bundle';

                                return (
                                    <div key={item.id} className={`grid grid-cols-12 gap-4 py-4 border-b last:border-0 ${!isAvailable ? 'opacity-50 bg-gray-50' : ''}`}>
                                        <div className="col-span-1 flex items-center justify-center">
                                            <Checkbox
                                                checked={isSelected}
                                                disabled={!isAvailable}
                                                onCheckedChange={(checked) => handleItemSelect(item.id, checked)}
                                                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary disabled:opacity-50"
                                            />
                                        </div>

                                        <div className="col-span-5">
                                            <div className="flex gap-4">
                                                <div className="w-24 h-24 rounded-lg overflow-hidden">
                                                    <ImageWithFallback
                                                        src={item.image}
                                                        alt={item.name}
                                                        fallbackText={item.name.charAt(0)}
                                                        width="w-full"
                                                        height="h-full"
                                                        imageClassName="object-cover"
                                                        rounded="rounded-lg"
                                                        bgColor="bg-gray-100"
                                                        textColor="text-primary"
                                                        textSize="text-xl"
                                                    />
                                                </div>
                                                <div>
                                                    <Link
                                                        href={isBundle ? `/marketplace/bundles/${item.slug}` : `/marketplace/product/${item.slug}`}
                                                        className="font-medium text-gray-900 hover:text-primary transition-colors"
                                                    >
                                                        {item.name}
                                                    </Link>

                                                    <div className="mt-1">
                                                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                                            isBundle
                                                                ? 'bg-blue-100 text-blue-800'
                                                                : 'bg-green-100 text-green-800'
                                                        }`}>
                                                            {isBundle ? __('product.bundle') : __('product.product')}
                                                        </span>
                                                    </div>

                                                    <div className="mt-2 space-y-1">
                                                        {isBundle ? (
                                                            <>
                                                                {item.bundle_items && (
                                                                    <div className="text-sm text-gray-500">
                                                                        {__('cart.bundle_items')}: {item.bundle_items.map(bundleItem => bundleItem.product_name).join(', ')}
                                                                    </div>
                                                                )}
                                                            </>
                                                        ) : (
                                                            <>
                                                                {item.category && (
                                                                    <div className="text-sm text-gray-500">
                                                                        {__('cart.category')}: {item.category}
                                                                    </div>
                                                                )}
                                                                <div className="text-sm text-gray-500">
                                                                    {__('cart.stock')}: {item.stock_quantity}
                                                                </div>
                                                            </>
                                                        )}
                                                        {!isLoggedIn ? (

                                                            <div className="text-sm text-blue-600 font-medium">
                                                                <User className="h-3 w-3 mr-1 inline" />
                                                                {__('cart.login_required')}
                                                            </div>
                                                        ) : (

                                                            !isAvailable && (
                                                                <div className="text-sm text-red-500 font-medium">
                                                                    {isBundle ? __('cart.bundle_unavailable') : (!item.status ? __('cart.product_unavailable') : __('cart.out_of_stock'))}
                                                                </div>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-span-2 text-center">
                                            <span className="font-medium">{formatCurrency(item.price)}</span>
                                        </div>
                                        <div className="col-span-2">
                                            <div className="flex items-center justify-center">
                                                <div className="flex border border-gray-300 rounded-md overflow-hidden">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-10 px-3 rounded-none border-0 hover:bg-gray-100 hover:text-primary"
                                                        onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                                                        disabled={item.quantity <= 1 || !isAvailable}
                                                    >
                                                        <Minus className="h-3 w-3" />
                                                    </Button>
                                                    <div className="w-16 flex items-center justify-center border-x border-gray-300">
                                                        <TextInputWithLabel
                                                            type="number"
                                                            value={item.quantity}
                                                            onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 1)}
                                                            className="w-full h-7 p-0 text-center border-0 rounded-none focus:ring-0 align-middle leading-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                                            min="1"
                                                            max={item.stock_quantity}
                                                            disabled={!isAvailable}
                                                            style={{ marginTop: "-4px" }}
                                                        />
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-10 px-3 rounded-none border-0 hover:bg-gray-100 hover:text-primary"
                                                        onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                                                        disabled={item.quantity >= item.stock_quantity || !isAvailable}
                                                    >
                                                        <Plus className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-span-2 text-right">
                                            <div className={`font-medium ${isSelected ? 'text-primary' : 'text-gray-600'}`}>
                                                {formatCurrency(item.price * item.quantity)}
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-gray-500 hover:text-red-500 mt-2"
                                                onClick={() => removeItem(item.id)}
                                            >
                                                <Trash2 className="h-4 w-4 mr-1" />
                                                {__('cart.remove')}
                                            </Button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        <div className="lg:w-96 bg-white rounded-lg shadow-sm p-6 h-fit">
                            <h2 className="text-lg font-semibold mb-6 pb-4 border-b">
                                {__('cart.order_summary')}
                                {selectedItems.size > 0 && (
                                    <span className="text-sm font-normal text-gray-500 ml-2">
                                        ({selectedItems.size} {__('cart.selected_items')})
                                    </span>
                                )}
                            </h2>

                            {renderOrderSummary(false)}
                        </div>
                    </div>
                )}

                {cartItems.length > 0 && upsellProducts && upsellProducts.length > 0 && renderUpsellProducts()}
                {cartItems.length > 0 && crossSellProducts && crossSellProducts.length > 0 && renderCrossSellProducts()}
            </div>

            <Footer />
        </div>
    );
}
