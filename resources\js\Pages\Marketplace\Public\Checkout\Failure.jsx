import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
    XCircle,
    AlertTriangle,
    RefreshCw,
    ArrowLeft,
    Phone,
    Mail,
    ChevronRight,
    CreditCard
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { Button } from '@/Components/ui/button';
import StatusBadge from '@/Components/ui/StatusBadge';
import Footer from '@/Components/Landing/Footer';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';

export default function CheckoutFailure({ order, errorMessage, topCategories = [], moreCategories = [] }) {
    const [isRetrying, setIsRetrying] = useState(false);

    const retryPayment = () => {
        if (!order) return;

        setIsRetrying(true);

        const paymentMethod = order.payment_method;

        if (paymentMethod === 'vnpay') {
            fetch(`/marketplace/payment/vnpay/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    alert(__('checkout.payment_link_create_failed'));
                    setIsRetrying(false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(__('checkout.general_error'));
                setIsRetrying(false);
            });
        } else if (paymentMethod === 'momo') {
            fetch(`/marketplace/payment/momo/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    alert(__('checkout.payment_link_create_failed'));
                    setIsRetrying(false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(__('checkout.general_error'));
                setIsRetrying(false);
            });
        } else {
            alert(__('checkout.contact_support_payment'));
            setIsRetrying(false);
        }
    };

    const canRetryPayment = order && ['vnpay', 'momo'].includes(order.payment_method) &&
                           order.payment_status === 'unpaid' &&
                           order.status !== 'cancelled';

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`${__('checkout.failure_title')} - PickleSocial ${__('marketplace.marketplace')}`} />

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">
                            {__('common.home')}
                        </Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <Link href="/marketplace/cart" className="text-gray-500 hover:text-primary">
                            {__('cart.title')}
                        </Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-red-600 font-medium">{__('checkout.failure_title')}</span>
                    </div>
                </div>
            </div>

            <div className="mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Failure Header */}
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-4">
                        <XCircle className="h-10 w-10 text-red-600" />
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        {__('checkout.payment_failed')}
                    </h1>
                    <p className="text-lg text-gray-600 mb-4">
                        {errorMessage}
                    </p>
                </div>

                <div className="max-w-2xl mx-auto">
                    {/* Error Details Card */}
                    <div className="bg-white rounded-lg shadow-sm border mb-6">
                        <div className="p-6">
                            <div className="flex items-start space-x-3">
                                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                                <div className="flex-1">
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                        {__('checkout.what_happened')}
                                    </h3>
                                    <p className="text-gray-600 mb-4">
                                        {__('checkout.payment_failed_description')}
                                    </p>

                                    {order && (
                                        <div className="bg-gray-50 p-4 rounded-lg">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm text-gray-500">{__('orders.order_number')}</span>
                                                <span className="font-mono text-sm font-medium">{order.order_number}</span>
                                            </div>
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm text-gray-500">{__('orders.total_amount')}</span>
                                                <span className="text-sm font-medium">{formatCurrency(order.total_amount)}</span>
                                            </div>
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm text-gray-500">{__('orders.payment_status')}</span>
                                                <StatusBadge
                                                    status="failed"
                                                    text={__('orders.payment_failed')}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Next Steps Card */}
                    <div className="bg-white rounded-lg shadow-sm border mb-6">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                {__('checkout.what_can_you_do')}
                            </h3>
                            <div className="space-y-4">
                                <div className="flex items-start space-x-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                                        1
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-gray-900">
                                            {__('checkout.retry_payment')}
                                        </h4>
                                        <p className="text-sm text-gray-600">
                                            {__('checkout.retry_payment_description')}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                                        2
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-gray-900">
                                            {__('checkout.check_payment_method')}
                                        </h4>
                                        <p className="text-sm text-gray-600">
                                            {__('checkout.check_payment_method_description')}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start space-x-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                                        3
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-gray-900">
                                            {__('checkout.contact_support')}
                                        </h4>
                                        <p className="text-sm text-gray-600">
                                            {__('checkout.contact_support_description')}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Contact Support Card */}
                    <div className="bg-blue-50 rounded-lg border border-blue-200 p-6 mb-6">
                        <h3 className="text-lg font-medium text-blue-900 mb-4">
                            {__('checkout.need_help')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center space-x-3">
                                <Phone className="h-5 w-5 text-blue-600" />
                                <div>
                                    <p className="text-sm text-blue-800 font-medium">
                                        {__('common.phone_support')}
                                    </p>
                                    <p className="text-sm text-blue-700">1900 123 456</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Mail className="h-5 w-5 text-blue-600" />
                                <div>
                                    <p className="text-sm text-blue-800 font-medium">
                                        {__('common.email_support')}
                                    </p>
                                    <p className="text-sm text-blue-700"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        {canRetryPayment && (
                            <Button
                                onClick={retryPayment}
                                disabled={isRetrying}
                                className="w-full sm:w-auto bg-primary hover:bg-tertiary"
                            >
                                {isRetrying ? (
                                    <>
                                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                        {__('checkout.processing')}
                                    </>
                                ) : (
                                    <>
                                        <CreditCard className="mr-2 h-4 w-4" />
                                        {__('checkout.retry_payment')}
                                    </>
                                )}
                            </Button>
                        )}
                        <Link href="/marketplace/orders">
                            <Button variant="outline" className="w-full sm:w-auto">
                                {__('orders.view_orders')}
                            </Button>
                        </Link>
                        <Link href="/marketplace">
                            <Button variant="outline" className="w-full sm:w-auto">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                {__('common.back_to_shopping')}
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>

            <Footer />
        </div>
    );
}
