import React from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Search,
    Heart,
    ShoppingCart,
    Eye,
    ChevronRight,
    Star,
    MapPin,
    Phone,
    Mail,
    Clock,
    Facebook,
    Instagram,
    Twitter,
    Youtube,
    CreditCard,
    User,
    ChevronDown,
    ArrowRight,
    TrendingUp,
    Gift,
    Truck,
    Shield,
    RefreshCw,
    Zap
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { FALLBACK_IMAGE_URL } from '@/constants/config';
import { useToast } from '@/Hooks/useToastContext';
import Loading from '@/Components/Loading';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Badge } from '@/Components/ui/badge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Footer from '@/Components/Landing/Footer';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import ProductActionButtons from '@/Components/ProductActionButtons';
import BundleCard from '@/Components/Marketplace/BundleCard';

export default function Home({ topCategories = [], moreCategories = [], featuredProducts = [], newArrivals = [], featuredBundles = [], brands = [] }) {
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const [isPageLoading, setIsPageLoading] = React.useState(false);
    const processingProductsRef = React.useRef(new Set());

    const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const handleAddToCart = async (productId, message = __('marketplace.added_to_cart'), type = 'success') => {

        if (processingProductsRef.current.has(productId)) {
            return;
        }

        processingProductsRef.current.add(productId);

        try {
            const product = [...featuredProducts, ...newArrivals].find(p => p.id === productId);
            if (!product) {
                addAlert('error', __('marketplace.product_not_found'));
                return;
            }

            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('common.csrf_token_not_found'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        product_id: product.id,
                        quantity: 1
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.product_id && item.product_id === product.id) ||
                    (item.id === product.id && !item.product_id)
                );
                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += 1;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        product_id: product.id,
                        name: product.name,
                        slug: product.slug,
                        image: product.image || product.image_url_formatted || product.image_url,
                        price: product.sale_price,
                        quantity: 1,
                        category: product.category?.name || '',
                        stock_quantity: product.quantity,
                        subtotal: product.sale_price
                    });
                }
                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', 'Đã thêm vào giỏ hàng');
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Cart error:', error);
            addAlert('error', 'Có lỗi xảy ra khi thêm vào giỏ hàng');
        } finally {

            setTimeout(() => {
                processingProductsRef.current.delete(productId);
            }, 1000);
        }
    };

    const handleAddToWishlist = (productId) => {
        addAlert('success', 'Đã thêm vào danh sách yêu thích');
    };

    const features = [
        {
            icon: <Truck className="h-6 w-6" />,
            title: __('marketplace.free_shipping'),
            description: __('marketplace.free_shipping_desc')
        },
        {
            icon: <Shield className="h-6 w-6" />,
            title: __('marketplace.authentic_warranty'),
            description: __('marketplace.authentic_warranty_desc')
        },
        {
            icon: <RefreshCw className="h-6 w-6" />,
            title: __('marketplace.easy_return'),
            description: __('marketplace.easy_return_desc')
        },
        {
            icon: <Gift className="h-6 w-6" />,
            title: __('marketplace.attractive_offers'),
            description: __('marketplace.attractive_offers_desc')
        }
    ];

    const renderProductCard = (product, section) => (
        <div key={product.id} className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] overflow-hidden relative group h-full">
            {product.is_featured && (
                <div className="absolute top-3 left-3 z-10 bg-secondary text-white text-xs font-semibold px-2.5 py-1.5 rounded">
                    {__('product.featured')}
                </div>
            )}
            <div className="relative h-56 flex items-center justify-center overflow-hidden">
                <Link
                    href={`/marketplace/product/${product.slug}`}
                    className="block w-full h-full"
                    onClick={() => setIsPageLoading(true)}
                >
                    <ImageWithFallback
                        src={product.image || product.image_url_formatted || product.image_url}
                        alt={product.name}
                        fallbackText={product.name.charAt(0)}
                        width="w-full"
                        height="h-full"
                        imageClassName="transition-transform duration-300 group-hover:scale-105"
                        rounded="rounded-none"
                        bgColor="bg-gray-100"
                        textColor="text-primary"
                        textSize="text-2xl"
                    />
                </Link>

                {/* Quick Actions - Outside Link */}
                <ProductActionButtons
                    product={product}
                    onAddToWishlist={handleAddToWishlist}
                    onAddToCart={handleAddToCart}
                    __={__}
                />
            </div>

            <div className="p-4">
                <Link
                    href={`/marketplace/product/${product.slug}`}
                    className="block"
                    onClick={() => setIsPageLoading(true)}
                >
                    <p className="text-sm text-tertiary mb-1">{product.category?.name}</p>
                    <h3 className="font-semibold mb-2 line-clamp-2 hover:text-primary transition-colors">{product.name}</h3>
                </Link>

                <div className="flex items-center gap-2 mb-2">
                    <div className="flex text-secondary">
                        {[...Array(5)].map((_, i) => (
                            <Star
                                key={i}
                                className={`h-4 w-4 ${i < Math.floor(product.rating || 0) ? 'fill-current' : i < (product.rating || 0) ? 'fill-current opacity-50' : ''}`}
                            />
                        ))}
                    </div>
                    <span className="text-sm text-gray-500">({product.review_count || 0})</span>
                </div>

                <div className="flex items-center gap-2">
                    <span className="text-lg font-bold text-primary">
                        {formatCurrency(product.sale_price)}
                    </span>
                    {product.import_price && Number(product.import_price) > Number(product.sale_price) && (
                        <>
                            <span className="text-sm text-gray-500 line-through">
                                {formatCurrency(product.import_price)}
                            </span>
                            <div className="bg-secondary text-white text-xs font-semibold px-1.5 py-0.5 rounded">
                                -{Math.round(((product.import_price - product.sale_price) / product.import_price) * 100)}%
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`PickleSocial ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb - Adding this to match other pages */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <span className="text-primary font-medium">{__('marketplace.breadcrumb_home')}</span>
                    </div>
                </div>
            </div>

            {/* Hero Section - Modern Design */}
            <div className="relative bg-gradient-to-br from-primary to-red-800 overflow-hidden">
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div className="text-white">
                            <div className="inline-flex items-center gap-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                                <Zap className="h-4 w-4 text-yellow-300" />
                                <span className="text-sm font-medium">{__('marketplace.hero_promo_badge')}</span>
                            </div>
                            <h1 className="text-5xl font-bold mb-6 leading-tight">
                                {__('marketplace.hero_title_main')}
                                <span className="block text-yellow-300">{__('marketplace.hero_title_highlight')}</span>
                            </h1>
                            <p className="text-xl mb-8 text-white text-opacity-90 leading-relaxed">
                                {__('marketplace.hero_description')}
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4">
                                <Link
                                    href="/marketplace"
                                    className="inline-flex items-center justify-center px-8 py-4 bg-white text-primary font-bold rounded-xl hover:bg-gray-100 transition-all duration-300 hover:shadow-lg transform hover:scale-105"
                                >
                                    {__('marketplace.hero_shop_now')}
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                                <Link
                                    href="/marketplace/categories"
                                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-bold rounded-xl hover:bg-white hover:text-primary transition-all duration-300"
                                >
                                    {__('marketplace.hero_explore_categories')}
                                </Link>
                            </div>
                        </div>
                        <div className="hidden lg:block">
                            <div className="relative">
                                <div className="absolute inset-0 bg-gradient-to-r from-yellow-300 to-primary rounded-3xl transform rotate-6 opacity-20"></div>
                                <img
                                    src="https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?auto=format&fit=crop&w=800&q=80"
                                    alt="Pickleball Equipment"
                                    className="relative rounded-3xl shadow-2xl"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="bg-white py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {features.map((feature, index) => (
                            <div key={index} className="text-center group">
                                <div className="w-16 h-16 bg-red-50 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                                    {feature.icon}
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                                <p className="text-gray-600 text-sm">{feature.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="bg-gray-50 py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">{__('marketplace.product_categories')}</h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            {__('marketplace.categories_description')}
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {topCategories.map(category => (
                            <Link
                                key={category.id}
                                href={`/marketplace/category/${category.slug}`}
                                className="group block"
                                onClick={() => setIsPageLoading(true)}
                            >
                                <div className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] overflow-hidden group hover:shadow-xl transition-all duration-300 h-full">
                                    <div className="relative h-48 overflow-hidden">
                                        <ImageWithFallback
                                            src={category.image_url_formatted || FALLBACK_IMAGE_URL}
                                            alt={category.name}
                                            fallbackText={category.name.charAt(0)}
                                            width="w-full"
                                            height="h-full"
                                            imageClassName="transition-transform duration-300 group-hover:scale-105"
                                            rounded="rounded-none"
                                            bgColor="bg-gray-100"
                                            textColor="text-primary"
                                            textSize="text-3xl"
                                        />
                                    </div>
                                    <div className="p-4">
                                        <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-primary transition-colors">{category.name}</h3>
                                        <p className="text-sm text-gray-500">
                                            {__('marketplace.products_count', { count: category.products_count })}
                                        </p>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                </div>
            </div>

            <div className="bg-white py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between mb-12">
                        <div>
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">{__('marketplace.featured_products_title')}</h2>
                            <p className="text-lg text-gray-600">{__('marketplace.featured_products_desc')}</p>
                        </div>
                        <Link
                            href="/marketplace/featured"
                            className="hidden sm:inline-flex items-center text-primary hover:text-secondary font-medium"
                        >
                            {__('marketplace.view_all')}
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {featuredProducts.map(product => renderProductCard(product, 'featured'))}
                    </div>
                </div>
            </div>

            <div className="bg-gradient-to-br from-gray-900 to-gray-800 py-16 relative overflow-hidden">
                <div className="absolute inset-0 opacity-30" style={{
                    backgroundImage: "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"
                }}></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-white mb-4">{__('marketplace.special_offers')}</h2>
                        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                            {__('marketplace.special_offers_desc')}
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div className="lg:col-span-2">
                            <div className="relative h-80 rounded-2xl overflow-hidden group">
                                <ImageWithFallback
                                    src="https://images.unsplash.com/photo-1594737625785-a6cbdabd333c?auto=format&fit=crop&w=800&q=80"
                                    alt="Special Offer"
                                    fallbackText="SALE"
                                    width="w-full"
                                    height="h-full"
                                    imageClassName="transition-transform duration-700 group-hover:scale-105"
                                    rounded="rounded-2xl"
                                    bgColor="bg-gradient-to-br from-primary to-yellow-500"
                                    textColor="text-white"
                                    textSize="text-4xl"
                                />
                                <div className="absolute inset-0 bg-gradient-to-r from-black from-opacity-70 to-transparent"></div>
                                <div className="absolute inset-0 p-8 flex flex-col justify-center">
                                    <div className="max-w-md">
                                        <div className="inline-flex items-center gap-2 bg-yellow-500 text-white px-4 py-2 rounded-full text-sm font-bold mb-4">
                                            <TrendingUp className="h-4 w-4" />
                                            {__('marketplace.hot_deal')}
                                        </div>
                                        <h3 className="text-3xl font-bold text-white mb-4">
                                            {__('marketplace.discount_up_to')}
                                        </h3>
                                        <p className="text-gray-200 mb-6">
                                            {__('marketplace.discount_description')}
                                        </p>
                                        <Link
                                            href="/marketplace/special-offers"
                                            className="inline-flex items-center bg-white text-gray-900 px-6 py-3 rounded-xl font-bold hover:bg-gray-100 transition-colors"
                                        >
                                            {__('marketplace.buy_now')}
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-6">
                            <div className="relative h-36 rounded-2xl overflow-hidden group">
                                <ImageWithFallback
                                    src="https://images.unsplash.com/photo-1556761175-b413da4baf72?auto=format&fit=crop&w=400&q=80"
                                    alt="Combo Deal"
                                    fallbackText="COMBO"
                                    width="w-full"
                                    height="h-full"
                                    imageClassName="transition-transform duration-500 group-hover:scale-105"
                                    rounded="rounded-2xl"
                                    bgColor="bg-gradient-to-br from-yellow-500 to-primary"
                                    textColor="text-white"
                                    textSize="text-2xl"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-50"></div>
                                <div className="absolute inset-0 p-6 flex flex-col justify-center">
                                    <h4 className="text-xl font-bold text-white mb-2">{__('marketplace.combo_deal')}</h4>
                                    <p className="text-gray-200 text-sm mb-3">{__('marketplace.buy_2_get_1')}</p>
                                    <Link href="/marketplace/bundles" className="text-yellow-300 hover:text-white transition-colors text-sm font-medium">
                                        {__('marketplace.see_more')}
                                    </Link>
                                </div>
                            </div>

                            <div className="relative h-36 rounded-2xl overflow-hidden group">
                                <ImageWithFallback
                                    src="https://images.unsplash.com/photo-1553729459-efe14ef6055d?auto=format&fit=crop&w=400&q=80"
                                    alt="Flash Sale"
                                    fallbackText="FLASH"
                                    width="w-full"
                                    height="h-full"
                                    imageClassName="transition-transform duration-500 group-hover:scale-105"
                                    rounded="rounded-2xl"
                                    bgColor="bg-gradient-to-br from-red-500 to-orange-500"
                                    textColor="text-white"
                                    textSize="text-2xl"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-50"></div>
                                <div className="absolute inset-0 p-6 flex flex-col justify-center">
                                    <h4 className="text-xl font-bold text-white mb-2">{__('marketplace.flash_sale')}</h4>
                                    <p className="text-gray-200 text-sm mb-3">{__('marketplace.shock_discount')}</p>
                                    {/* <Link href="/flash-sale" className="text-yellow-300 hover:text-white transition-colors text-sm font-medium">
                                        {__('marketplace.buy_immediately')}
                                    </Link> */}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="bg-gray-50 py-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between mb-12">
                        <div>
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">{__('marketplace.new_arrivals_title')}</h2>
                            <p className="text-lg text-gray-600">{__('marketplace.new_arrivals_desc')}</p>
                        </div>
                        <Link
                            href="/marketplace/new-arrivals"
                            className="hidden sm:inline-flex items-center text-primary hover:text-secondary font-medium"
                        >
                            {__('marketplace.view_all')}
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {newArrivals.map(product => renderProductCard(product, 'newArrivals'))}
                    </div>
                </div>
            </div>

            {/* Featured Bundles Section */}
            {featuredBundles && featuredBundles.length > 0 && (
                <div className="bg-white py-16">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between mb-12">
                            <div>
                                <h2 className="text-3xl font-bold text-gray-900 mb-4">{__('product.featured_bundles')}</h2>
                                <p className="text-lg text-gray-600">{__('product.featured_bundles_desc')}</p>
                            </div>
                            <Link
                                href={route('marketplace.bundles.featured')}
                                className="hidden sm:inline-flex items-center text-primary hover:text-secondary font-medium"
                            >
                                {__('product.view_all_bundles')}
                                <ArrowRight className="ml-2 h-4 w-4" />
                            </Link>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {featuredBundles.slice(0, 6).map(bundle => (
                                <BundleCard key={bundle.id} bundle={bundle} />
                            ))}
                        </div>

                        {/* Mobile view all button */}
                        <div className="sm:hidden text-center mt-8">
                            <Link
                                href={route('marketplace.bundles.featured')}
                                className="inline-flex items-center text-primary hover:text-secondary font-medium"
                            >
                                {__('product.view_all_featured_bundles')}
                                <ArrowRight className="ml-2 h-4 w-4" />
                            </Link>
                        </div>
                    </div>
                </div>
            )}

            <div className="bg-gradient-to-r from-primary to-red-800 py-16 relative overflow-hidden">
                <div className="absolute inset-0 opacity-20" style={{
                    backgroundImage: "url('data:image/svg+xml,%3Csvg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.1\"%3E%3Cpath d=\"M20 20c0 11.046-8.954 20-20 20v20h40V20H20z\"/%3E%3C/g%3E%3C/svg%3E')"
                }}></div>
                <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-full inline-flex items-center gap-2 px-6 py-3 mb-6">
                        <Mail className="h-5 w-5 text-white" />
                        <span className="text-white font-medium">{__('marketplace.newsletter_badge')}</span>
                    </div>
                    <h2 className="text-3xl font-bold text-white mb-4">
                        {__('marketplace.newsletter_title')}
                    </h2>
                    <p className="text-lg text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
                        {__('marketplace.newsletter_description')}
                    </p>
                    <form className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                        <Input
                            type="email"
                            placeholder={__('marketplace.email_placeholder')}
                            className="flex-1 h-12 border-0 bg-white text-gray-800 placeholder-gray-500 rounded-xl focus:ring-gray-300"
                        />
                        <Button
                            type="submit"
                            className="h-12 bg-secondary hover:bg-secondary/90 text-white font-bold px-8 rounded-xl"
                        >
                            {__('marketplace.subscribe')}
                        </Button>
                    </form>
                </div>
            </div>


            <Footer />
        </div>
    );
}
