import React, { useState, useEffect, useMemo } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import {
    Search,
    Heart,
    ShoppingCart,
    Eye,
    ChevronRight,
    ChevronLeft,
    Filter,
    Grid,
    List,
    X,
    Star,
    MapPin,
    Phone,
    Mail,
    Clock,
    Facebook,
    Instagram,
    Twitter,
    Youtube,
    CreditCard,
    User,
    ChevronDown
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { FALLBACK_IMAGE_URL } from '@/constants/config';
import { useToast } from '@/Hooks/useToastContext';
import Loading from '@/Components/Loading';
import { Checkbox } from '@/Components/ui/checkbox';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Badge } from '@/Components/ui/badge';
import { Dialog, DialogContent, DialogTrigger } from '@/Components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';
import Pagination from '@/Components/Pagination';
import Footer from '@/Components/Landing/Footer';
import ImageWithFallback from '@/Components/ImageWithFallback';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import ProductActionButtons from '@/Components/ProductActionButtons';

export default function SearchResults(props) {
    const {
        searchQuery = '',
        products = { data: [], total: 0, from: 0, to: 0, links: [] },
        topCategories = [],
        moreCategories = [],
        brands = [],
        weightRanges = [],
        filters = {},
        activeFilters = [],
        priceRange: defaultPriceRange = { min: 0, max: 10000000 }
    } = props;const [isPageLoading, setIsPageLoading] = useState(false);
    const [viewMode, setViewMode] = useState('grid');
    const [showFilters, setShowFilters] = useState(false);
    const [sortBy, setSortBy] = useState(filters?.sort || 'relevance');
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const [openDropdown, setOpenDropdown] = useState(null);
    const processingProductsRef = React.useRef(new Set());


    useEffect(() => {
        console.log('Search Products data:', products);
        console.log('Search Query:', searchQuery);
        if (products.data && products.data.length > 0) {
            console.log('First search product:', products.data[0]);
            console.log('First search product image:', products.data[0].image);
            console.log('First search product image_url:', products.data[0].image_url);
            console.log('First search product image_url_formatted:', products.data[0].image_url_formatted);
        }
    }, [products, searchQuery]);

    const [collapsed, setCollapsed] = useState({
        brand: false,
        price: false,
        material: false,
        weight: false,
        color: false,
        playStyle: false,
    });
    const toggleCollapse = (key) => setCollapsed(prev => ({ ...prev, [key]: !prev[key] }));


    const [selectedBrand, setSelectedBrand] = useState(filters?.brand || '');
    const [selectedWeightRange, setSelectedWeightRange] = useState(filters?.weight_range || '');
    const [selectedPriceRange, setSelectedPriceRange] = useState({
        min: filters?.min_price || defaultPriceRange.min,
        max: filters?.max_price || defaultPriceRange.max
    });


    useEffect(() => {
        console.log('Search Filters changed:', filters);
        console.log('Search Default price range:', defaultPriceRange);

        setSelectedBrand(filters?.brand || '');
        setSelectedWeightRange(filters?.weight_range || '');


        const newPriceRange = {
            min: defaultPriceRange.min,
            max: defaultPriceRange.max
        };        if (filters?.min_price !== undefined && filters?.min_price !== null) {
            newPriceRange.min = Number(filters.min_price);
        }

        if (filters?.max_price !== undefined && filters?.max_price !== null) {
            newPriceRange.max = Number(filters.max_price);
        }

        console.log('Setting search price range to:', newPriceRange);
        setSelectedPriceRange(newPriceRange);
    }, [filters, defaultPriceRange]);    useEffect(() => {
        const handleStart = (event) => {
            if (event.detail.visit.url.includes('/marketplace/product/')) {
                setIsPageLoading(true);
            }
        };
        const handleFinish = () => setIsPageLoading(false);

        document.addEventListener('inertia:start', handleStart);
        document.addEventListener('inertia:finish', handleFinish);

        return () => {
            document.removeEventListener('inertia:start', handleStart);
            document.removeEventListener('inertia:finish', handleFinish);
        };
    }, []);

    const handleSort = (value) => {
        setSortBy(value);        router.get(
            window.location.pathname,
            {
                ...(filters || {}),
                q: searchQuery,
                sort: value
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleFilterToggle = () => {
        setShowFilters(!showFilters);
    };

    const handleViewModeChange = (mode) => {
        setViewMode(mode);
    };

    const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const handleAddToCart = async (productId, message = null, type = 'success') => {

        if (processingProductsRef.current.has(productId)) {
            return;
        }

        processingProductsRef.current.add(productId);

        try {
            const product = products.data.find(p => p.id === productId);
            if (!product) {
                addAlert('error', __('marketplace.product_not_found_error'));
                return;
            }

            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('marketplace.csrf_token_missing'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        product_id: product.id,
                        quantity: 1
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.product_id && item.product_id === product.id) ||
                    (item.id === product.id && !item.product_id)
                );
                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += 1;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        product_id: product.id,
                        name: product.name,
                        slug: product.slug,
                        image: product.image || product.image_url_formatted || product.image_url,
                        price: product.sale_price,
                        quantity: 1,
                        category: product.category?.name || '',
                        stock_quantity: product.quantity,
                        subtotal: product.sale_price
                    });
                }
                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', message || __('product.added_to_cart'));
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Cart error:', error);
            addAlert('error', __('marketplace.cart_add_error'));
        } finally {

            setTimeout(() => {
                processingProductsRef.current.delete(productId);
            }, 1000);
        }
    };

    const handleAddToWishlist = (productId) => {
        addAlert('success', __('product.added_to_wishlist'));
    };

    const handleQuickView = (productId) => {
        addAlert('info', __('product.opening_quick_view'));
    };


    const handlePriceChange = (type, value) => {
        console.log(`Search Price ${type} changed to:`, value);

        let numValue = value;


        if (value === '' || value === null || value === undefined) {
            numValue = type === 'min' ? defaultPriceRange.min : defaultPriceRange.max;
        } else {
            numValue = Number(value);

            if (isNaN(numValue) || numValue < 0) {
                numValue = type === 'min' ? defaultPriceRange.min : defaultPriceRange.max;
            }
        }

        console.log(`Setting search ${type} price to:`, numValue);

        setSelectedPriceRange(prev => {
            const newRange = {
                ...prev,
                [type]: numValue
            };


            if (type === 'min' && newRange.max < numValue) {
                newRange.max = numValue;
            }
            if (type === 'max' && newRange.min > numValue) {
                newRange.min = numValue;
            }

            console.log('New search price range:', newRange);
            return newRange;
        });
    };


    const handleFilter = () => {
        console.log('Applying search filters with current state:', {
            brand: selectedBrand,
            weight_range: selectedWeightRange,
            min_price: selectedPriceRange.min,
            max_price: selectedPriceRange.max,
            defaultPriceRange,
            searchQuery
        });

        const filterParams = {};


        if (searchQuery && searchQuery.trim() !== '') {
            filterParams.q = searchQuery.trim();
        }


        if (selectedBrand && selectedBrand.trim() !== '') {
            filterParams.brand = selectedBrand.trim();
        }


        if (selectedWeightRange && selectedWeightRange.trim() !== '') {
            filterParams.weight_range = selectedWeightRange.trim();
        }


        const minPrice = Number(selectedPriceRange.min);
        const maxPrice = Number(selectedPriceRange.max);
        const defaultMin = Number(defaultPriceRange.min);
        const defaultMax = Number(defaultPriceRange.max);

        console.log('Search Price comparison:', {
            minPrice, maxPrice, defaultMin, defaultMax,
            minChanged: minPrice !== defaultMin,
            maxChanged: maxPrice !== defaultMax
        });

        if (!isNaN(minPrice) && minPrice !== defaultMin && minPrice >= 0) {
            filterParams.min_price = minPrice;
        }

        if (!isNaN(maxPrice) && maxPrice !== defaultMax && maxPrice >= 0) {
            filterParams.max_price = maxPrice;
        }


        if (sortBy && sortBy !== 'relevance') {
            filterParams.sort = sortBy;
        }

        console.log('Final search filter params to send:', filterParams);

        router.get(
            '/marketplace/search',
            filterParams,
            {
                preserveState: true,
                preserveScroll: true,
                onStart: () => console.log('Search filter request started'),
                onSuccess: (page) => {
                    console.log('Search filter request successful');
                    console.log('New search page props:', page.props);
                },
                onError: (errors) => console.log('Search filter request error:', errors)
            }
        );
    };


    const handleResetFilters = () => {
        console.log('Resetting search filters to default:', defaultPriceRange);

        setSelectedBrand('');
        setSelectedWeightRange('');
        setSelectedPriceRange({
            min: defaultPriceRange.min,
            max: defaultPriceRange.max
        });

        router.get(
            '/marketplace/search',
            { q: searchQuery },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => addAlert('success', __('common.filters_cleared'))
            }
        );
    };


    const getSortLabel = (sort) => {
        switch (sort) {
            case 'price-asc':
                return __('common.sort_price_asc');
            case 'price-desc':
                return __('common.sort_price_desc');
            case 'newest':
                return __('common.sort_newest');
            case 'name-asc':
                return __('common.sort_name_asc');
            case 'name-desc':
                return __('common.sort_name_desc');
            case 'relevance':
                return __('common.sort_relevance');
            default:
                return __('common.sort_relevance');
        }
    };
    const handleRemoveFilter = (filter) => {
        const newFilters = { ...(filters || {}) };

        newFilters.q = searchQuery;

        switch (filter.type) {
            case 'brand':
                delete newFilters.brand;
                break;
            case 'weight_range':
                delete newFilters.weight_range;
                break;
            case 'price_range':
                delete newFilters.min_price;
                delete newFilters.max_price;
                break;
        }

        router.get(
            '/marketplace/search',
            newFilters,
            { preserveState: true, preserveScroll: true }
        );
        addAlert('success', __('common.filter_removed'));
    };    const handlePageChange = (page) => {
        router.get(
            '/marketplace/search',
            { ...(filters || {}), q: searchQuery, page },
            { preserveState: true, preserveScroll: true }
        );
    };    return (
        <div className="flex flex-col min-h-screen max-w-[1480px] mx-auto">
            <Head title={`${__('product.search_results')} - "${searchQuery}" - ${__('common.app_name')} ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('product.home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('product.search_results')}</span>
                        {searchQuery && (
                            <>
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                                <span className="text-primary font-medium">"{searchQuery}"</span>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className=" mx-auto px-2 sm:px-3 lg:px-4 py-4 " >
                <div className="flex gap-4">
                    {/* Filter Sidebar */}
                    <aside className={`w-72 bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] p-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-semibold text-primary">{__('product.filter_products')}</h3>
                            <button
                                onClick={handleResetFilters}
                                className="text-sm text-tertiary hover:underline"
                            >
                                {__('product.clear_all_filters')}
                            </button>
                        </div>

                        {/* Price Range Filter */}
                        <div className="mb-6">
                            <button
                                onClick={() => toggleCollapse('price')}
                                className="flex justify-between items-center w-full text-left font-medium mb-3"
                            >
                                <span>{__('product.price_range')}</span>
                                <ChevronDown className={`h-5 w-5 transform transition-transform ${collapsed.price ? 'rotate-180' : ''}`} />
                            </button>
                            <div className={`space-y-4 ${collapsed.price ? 'hidden' : 'block'}`}>
                                <div className="flex items-center gap-2">
                                    <Input
                                        type="number"
                                        placeholder={__('product.price_from')}
                                        value={selectedPriceRange.min}
                                        onChange={(e) => handlePriceChange('min', e.target.value)}
                                        className="w-full"
                                        min={0}
                                        max={selectedPriceRange.max}
                                    />
                                    <span>-</span>
                                    <Input
                                        type="number"
                                        placeholder={__('product.price_to')}
                                        value={selectedPriceRange.max}
                                        onChange={(e) => handlePriceChange('max', e.target.value)}
                                        className="w-full"
                                        min={selectedPriceRange.min}
                                    />
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setSelectedPriceRange({
                                            min: defaultPriceRange.min,
                                            max: 1000000
                                        })}
                                    >
                                        {__('product.under_1m')}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={() => setSelectedPriceRange({
                                            min: 1000000,
                                            max: 2000000
                                        })}
                                    >
                                        {__('product.from_1m_to_2m')}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={() => setSelectedPriceRange({
                                            min: 2000000,
                                            max: 5000000
                                        })}
                                    >
                                        {__('product.from_2m_to_5m')}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={() => setSelectedPriceRange({
                                            min: 5000000,
                                            max: defaultPriceRange.max
                                        })}
                                    >
                                        {__('product.over_5m')}
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Thương hiệu */}
                        <div className="mb-6">
                            <button
                                onClick={() => toggleCollapse('brand')}
                                className="flex justify-between items-center w-full text-left font-medium mb-3 select-none"
                            >
                                <span>{__('product.brand')}</span>
                                <ChevronDown className={`h-5 w-5 transform transition-transform ${collapsed.brand ? 'rotate-180' : ''}`} />
                            </button>
                            <div className={`space-y-3 ${collapsed.brand ? 'hidden' : 'block'}`}>
                                {brands.length === 0 ? (
                                    <div className="text-sm text-gray-500">{__('product.no_brand_data')}</div>
                                ) : (
                                    brands.map((brand, idx) => (
                                        <div key={idx} className="flex items-center">
                                            <Checkbox
                                                id={`brand-${idx}`}
                                                checked={selectedBrand === brand}
                                                onCheckedChange={() => setSelectedBrand(
                                                    selectedBrand === brand ? '' : brand
                                                )}
                                            />
                                            <label htmlFor={`brand-${idx}`} className="ml-2 text-sm flex-1 cursor-pointer">
                                                {brand}
                                            </label>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>

                        {/* Weight Range Filter */}
                        <div className="mb-6">
                            <button
                                onClick={() => toggleCollapse('weight')}
                                className="flex justify-between items-center w-full text-left font-medium mb-3"
                            >
                                <span>{__('product.weight')}</span>
                                <ChevronDown className={`h-5 w-5 transform transition-transform ${collapsed.weight ? 'rotate-180' : ''}`} />
                            </button>
                            <div className={`space-y-3 ${collapsed.weight ? 'hidden' : 'block'}`}>
                                {weightRanges.length === 0 ? (
                                    <div className="text-sm text-gray-500">{__('product.no_weight_data')}</div>
                                ) : (
                                    <div className="space-y-3">
                                        {/* Debug info - có thể bỏ comment khi cần debug */}
                                        {/* <div className="text-xs text-gray-400 p-2 bg-gray-50 rounded">
                                            Debug: {weightRanges.length} ranges found
                                        </div> */}

                                        {weightRanges.map((range) => (
                                            <div key={range.id} className="flex items-center">
                                                <Checkbox
                                                    id={`weight-${range.id}`}
                                                    checked={selectedWeightRange === range.id}
                                                    onCheckedChange={() => setSelectedWeightRange(
                                                        selectedWeightRange === range.id ? '' : range.id
                                                    )}
                                                />
                                                <label
                                                    htmlFor={`weight-${range.id}`}
                                                    className="ml-2 text-sm flex-1 cursor-pointer"
                                                >
                                                    {range.name}
                                                </label>
                                                <span className="text-sm text-gray-500">({range.count})</span>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                        <Button className="w-full bg-primary hover:bg-tertiary" onClick={handleFilter}>
                            {__('product.apply_filters')}
                        </Button>
                    </aside>

                    {/* Product Listing */}
                    <main className="flex-1 min-w-0">
                        <div className="listing-header mb-4">
                            <div>
                                <h1 className="text-2xl font-bold text-primary">
                                    {searchQuery ? `${__('product.search_results_for')} "${searchQuery}"` : __('product.search_results')}
                                </h1>
                                <p className="text-sm text-gray-500">
                                    {__('product.showing_results', {
                                        from: products.from || 0,
                                        to: products.to || 0,
                                        total: products.total
                                    })}
                                </p>
                            </div>
                            <Button
                                onClick={handleFilterToggle}
                                className="lg:hidden flex items-center gap-2"
                            >
                                <Filter className="h-4 w-4" />
                                <span>{__('product.filter_products')}</span>
                            </Button>
                        </div>

                        {/* Listing Controls */}
                        <div className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] px-3 py-4 mb-4">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                {/* Active Filters */}
                                <div className="flex flex-wrap gap-2">
                                    {activeFilters.length === 0 ? (
                                        <span className="text-gray-400 text-sm">{__('product.no_filters_selected')}</span>
                                    ) : (
                                        activeFilters.map((filter, index) => (
                                            <div key={index} className="flex items-center gap-2 px-3 py-1.5 bg-gray-100 rounded-full text-sm">
                                                <span className="text-primary font-medium">{filter.label}</span>
                                                <button
                                                    onClick={() => handleRemoveFilter(filter)}
                                                    className="text-gray-500 hover:text-gray-700"
                                                >
                                                    <X className="h-4 w-4" />
                                                </button>
                                            </div>
                                        ))
                                    )}
                                </div>
                                {/* Sort Dropdown & View Mode */}
                                <div className="flex items-center gap-4">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" className="flex items-center gap-2">
                                                <span>{__('product.sort_by', { sort: getSortLabel(sortBy) })}</span>
                                                <ChevronDown className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                            <DropdownMenuItem onClick={() => handleSort('relevance')}>{__('product.sort_relevance')}</DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleSort('price-asc')}>{__('product.sort_price_asc')}</DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleSort('price-desc')}>{__('product.sort_price_desc')}</DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleSort('newest')}>{__('product.sort_newest')}</DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleSort('name-asc')}>{__('product.sort_name_asc')}</DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleSort('name-desc')}>{__('product.sort_name_desc')}</DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant={viewMode === 'grid' ? 'default' : 'outline'}
                                            size="icon"
                                            onClick={() => handleViewModeChange('grid')}
                                            title={__('product.grid_view')}
                                        >
                                            <Grid className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant={viewMode === 'list' ? 'default' : 'outline'}
                                            size="icon"
                                            onClick={() => handleViewModeChange('list')}
                                            title={__('product.list_view')}
                                        >
                                            <List className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {products.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center bg-white rounded-lg shadow-sm p-8 min-h-[400px]">
                                <div className="text-gray-400 mb-4">
                                    <Search className="h-16 w-16" />
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    {searchQuery ? __('product.no_products_found') : __('product.enter_search_term')}
                                </h3>
                                <p className="text-gray-500 text-center mb-4">
                                    {searchQuery ? __('product.no_products_desc') : __('product.search_help_text')}
                                </p>
                                {searchQuery ? (
                                    <Button onClick={handleResetFilters}>{__('product.clear_filters')}</Button>
                                ) : (
                                    <Button onClick={() => router.get('/marketplace')}>{__('product.browse_all_products')}</Button>
                                )}
                            </div>
                        ) : viewMode === 'grid' ? (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[400px]">
                                {products.data.slice(0, 9).map(product => {
                                    return (
                                        <div key={product.id} className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] overflow-hidden relative group h-full">
                                            {product.is_featured && (
                                                <div className="absolute top-3 left-3 z-10 bg-secondary text-white text-xs font-semibold px-2.5 py-1.5 rounded">
                                                    {__('product.bestseller')}
                                                </div>
                                            )}
                                            <div className="relative h-56 flex items-center justify-center overflow-hidden">
                                                <Link
                                                    href={`/marketplace/product/${product.slug}`}
                                                    className="block w-full h-full flex items-center justify-center"
                                                    onClick={() => setIsPageLoading(true)}
                                                >
                                                    <ImageWithFallback
                                                        src={product.image || product.image_url_formatted || product.image_url}
                                                        alt={product.name}
                                                        fallbackText={product.name.charAt(0)}
                                                        width="w-full"
                                                        height="h-full"
                                                        imageClassName="transition-transform duration-300 group-hover:scale-105"
                                                        rounded="rounded-none"
                                                        bgColor="bg-gray-100"
                                                        textColor="text-primary"
                                                        textSize="text-2xl"
                                                    />
                                                </Link>
                                                {/* Quick Actions - Outside Link */}
                                                <ProductActionButtons
                                                    product={product}
                                                    onAddToWishlist={handleAddToWishlist}
                                                    onAddToCart={handleAddToCart}
                                                    __={__}
                                                />
                                            </div>
                                            <div className="p-4">
                                                <Link
                                                    href={`/marketplace/product/${product.slug}`}
                                                    className="block"
                                                    onClick={() => setIsPageLoading(true)}
                                                >
                                                    <p className="text-sm text-tertiary mb-1">{product.category?.name}</p>
                                                    <h3 className="font-semibold mb-2 line-clamp-2 hover:text-primary transition-colors">{product.name}</h3>
                                                </Link>
                                                <div className="flex items-center gap-2 mb-2">
                                                    <div className="flex text-secondary">
                                                        {[...Array(5)].map((_, i) => (
                                                            <Star
                                                                key={i}
                                                                className={`h-4 w-4 ${i < Math.floor(product.rating || 0) ? 'fill-current' : i < (product.rating || 0) ? 'fill-current opacity-50' : ''}`}
                                                            />
                                                        ))}
                                                    </div>
                                                    <span className="text-sm text-gray-500">({product.review_count || 0})</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <span className="text-lg font-bold text-primary">
                                                        {formatCurrency(product.sale_price)}
                                                    </span>
                                                    {product.import_price && Number(product.import_price) > Number(product.sale_price) && (
                                                        <>
                                                        <span className="text-sm text-gray-500 line-through">
                                                            {formatCurrency(product.import_price)}
                                                        </span>
                                                            <div className="bg-secondary text-white text-xs font-semibold px-1.5 py-0.5 rounded">
                                                                {__('product.discount_percent', {
                                                                    percent: Math.round(((product.import_price - product.sale_price) / product.import_price) * 100)
                                                                })}
                                                            </div>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                                {/* Add empty divs to maintain grid layout when items are fewer than columns */}
                                {products.data.slice(0, 9).length < 3 && [...Array(3 - products.data.slice(0, 9).length)].map((_, i) => (
                                    <div key={`empty-${i}`} className="hidden lg:block" />
                                ))}
                                {products.data.slice(0, 9).length < 2 && (
                                    <div className="hidden md:block lg:hidden" />
                                )}
                            </div>
                        ) : (
                            <div className="flex flex-col gap-6 min-h-[400px]">
                                {products.data.map(product => (
                                    <Link
                                        href={`/marketplace/product/${product.slug}`}
                                        className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] flex overflow-hidden group transition hover:shadow-lg"
                                        key={product.id}
                                    >
                                        <div className="relative w-56 min-w-[14rem] h-56 flex-shrink-0 flex items-center justify-center overflow-hidden">
                                            {product.is_featured && (
                                                <div className="absolute top-3 left-3 z-10 bg-secondary text-white text-xs font-semibold px-2.5 py-1.5 rounded">
                                                    {__('product.bestseller')}
                                                </div>
                                            )}
                                            <ImageWithFallback
                                                src={product.image || product.image_url_formatted || product.image_url}
                                                alt={product.name}
                                                fallbackText={product.name.charAt(0)}
                                                width="w-full"
                                                height="h-full"
                                                imageClassName="transition-transform duration-300 group-hover:scale-105"
                                                rounded="rounded-none"
                                                bgColor="bg-gray-100"
                                                textColor="text-primary"
                                                textSize="text-2xl"
                                            />
                                        </div>
                                        <div className="flex-1 p-6 flex flex-col justify-between">
                                            <div>
                                                <p className="text-sm text-tertiary mb-1">{product.category?.name}</p>
                                                <h3 className="font-semibold mb-2 text-lg">{product.name}</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    {product.description || __('product.default_description')}
                                                </p>
                                                <div className="flex flex-wrap gap-4 mb-4">
                                                    {product.specs?.weight && (
                                                        <div className="flex items-center gap-2">
                                                            <Clock className="h-4 w-4 text-primary" />
                                                            <span className="text-sm">{product.specs.weight}</span>
                                                        </div>
                                                    )}
                                                    {product.specs?.grip && (
                                                        <div className="flex items-center gap-2">
                                                            <MapPin className="h-4 w-4 text-primary" />
                                                            <span className="text-sm">{product.specs.grip}</span>
                                                        </div>
                                                    )}
                                                    {product.specs?.material && (
                                                        <div className="flex items-center gap-2">
                                                            <Star className="h-4 w-4 text-primary" />
                                                            <span className="text-sm">{product.specs.material}</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                                <div className="flex items-center gap-2 mb-2 md:mb-0">
                                                    <span className="text-lg font-bold text-primary">
                                                        {formatCurrency(product.sale_price)}
                                                    </span>
                                                    {product.import_price && Number(product.import_price) > Number(product.sale_price) && (
                                                        <>
                                                            <span className="text-sm text-gray-500 line-through">
                                                                {formatCurrency(product.import_price)}
                                                            </span>
                                                                <div className="bg-secondary text-white text-xs font-semibold px-1.5 py-0.5 rounded">
                                                                    {__('product.discount_percent', {
                                                                        percent: Math.round(((product.import_price - product.sale_price) / product.import_price) * 100)
                                                                    })}
                                                                </div>
                                                        </>
                                                    )}
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button
                                                        className="flex-1 bg-primary hover:bg-tertiary"
                                                        onClick={e => { e.stopPropagation(); e.preventDefault(); handleAddToCart(product.id); }}
                                                    >
                                                        <ShoppingCart className="h-4 w-4 mr-2" />
                                                        {__('product.add_to_cart')}
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        className="flex-1"
                                                        onClick={e => { e.stopPropagation(); e.preventDefault(); handleAddToWishlist(product.id); }}
                                                    >
                                                        <Heart className="h-4 w-4 mr-2" />
                                                        {__('product.add_to_wishlist')}
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </Link>
                                ))}
                            </div>
                        )}

                        {/* Pagination */}
                        <div className="mt-8">
                            {products.total > 0 && (
                                <Pagination
                                    links={products.links}
                                    onPageChange={handlePageChange}
                                    preserveState={true}
                                    preserveScroll={true}
                                    data={filters}
                                />
                            )}
                        </div>
                    </main>
                </div>
            </div>

            <Footer />
        </div>
    );
}
