import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import {
    Package,
    Calendar,
    CreditCard,
    MapPin,
    Eye,
    RefreshCw,
    ChevronRight,
    Clock,
    CheckCircle,
    Star
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import { Button } from '@/Components/ui/button';
import StatusBadge from '@/Components/ui/StatusBadge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Footer from '@/Components/Landing/Footer';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';

export default function OrdersIndex({ orders, topCategories = [], moreCategories = [] }) {
    const { addAlert } = useToast();
    const [retryingOrders, setRetryingOrders] = useState(new Set());
    const [cancellingOrders, setCancellingOrders] = useState(new Set());
    const [showCancelModal, setShowCancelModal] = useState(false);
    const [orderToCancel, setOrderToCancel] = useState(null);


    const [activeTab, setActiveTab] = useState(() => {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('tab') || 'pending';
    });


    useEffect(() => {
        const url = new URL(window.location);
        url.searchParams.set('tab', activeTab);
        window.history.replaceState({}, '', url);
    }, [activeTab]);

    const getStatusBadgeColor = (status) => {
        const colors = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'confirmed': 'bg-blue-100 text-blue-800',
            'processing': 'bg-purple-100 text-purple-800',
            'shipping': 'bg-indigo-100 text-indigo-800',
            'completed': 'bg-green-100 text-green-800',
            'cancelled': 'bg-red-100 text-red-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getPaymentStatusBadgeColor = (status) => {
        const colors = {
            'unpaid': 'bg-red-100 text-red-800',
            'paid': 'bg-green-100 text-green-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'failed': 'bg-red-100 text-red-800',
            'refunded': 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const hasValidPaymentUrl = (order) => {
        if (!order.payment_url_created_at) return false;

        const createdAt = new Date(order.payment_url_created_at);
        const now = new Date();
        const diffMinutes = (now - createdAt) / (1000 * 60);

        const paymentMethod = order.payment_method;
        if (diffMinutes <= 30) {
            if (paymentMethod === 'momo' && order.momo_payment_url) {
                return true;
            }
            if (paymentMethod === 'vnpay' && order.vnpay_payment_url) {
                return true;
            }
        }

        return false;
    };

    const getExistingPaymentUrl = (order) => {
        if (!hasValidPaymentUrl(order)) return null;

        const paymentMethod = order.payment_method;
        if (paymentMethod === 'vnpay' && order.vnpay_payment_url) {
            return order.vnpay_payment_url;
        } else if (paymentMethod === 'momo' && order.momo_payment_url) {
            return order.momo_payment_url;
        }

        return null;
    };

    const retryPayment = (order) => {
        if (retryingOrders.has(order.id)) return;

        const existingUrl = getExistingPaymentUrl(order);
        if (existingUrl) {
            window.location.href = existingUrl;
            return;
        }

        setRetryingOrders(prev => new Set([...prev, order.id]));

        const paymentMethod = order.payment_method;

        if (paymentMethod === 'vnpay') {
            fetch(`/marketplace/payment/vnpay/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    addAlert('error', __('marketplace.payment_link_create_failed'));
                    setRetryingOrders(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(order.id);
                        return newSet;
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addAlert('error', __('marketplace.general_error'));
                setRetryingOrders(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(order.id);
                    return newSet;
                });
            });
        } else if (paymentMethod === 'momo') {
            fetch(`/marketplace/payment/momo/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    addAlert('error', __('marketplace.payment_link_create_failed'));
                    setRetryingOrders(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(order.id);
                        return newSet;
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addAlert('error', __('marketplace.general_error'));
                setRetryingOrders(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(order.id);
                    return newSet;
                });
            });
        }
    };

    const canRetryPayment = (order) => {
        return ['vnpay', 'momo'].includes(order.payment_method) &&
               order.payment_status === 'unpaid' &&
               order.status !== 'cancelled';
    };

    const canCancelOrder = (order) => {
        return order.status === 'pending' && order.payment_status !== 'paid';
    };

    const canReviewOrder = (order) => {
        return order.status === 'completed' && order.payment_status === 'paid' &&
               order.has_unreviewable_items;
    };


    const getOrdersByTab = (tabKey) => {
        if (!orders.data) return [];

        return orders.data.filter(order => {
            switch(tabKey) {
                case 'pending':
                    return order.status === 'pending';
                case 'pickup':
                    return ['confirmed', 'processing'].includes(order.status);
                case 'shipping':
                    return order.status === 'shipping';
                case 'review':
                    return order.status === 'completed' && order.payment_status === 'paid';
                default:
                    return false;
            }
        });
    };


    const getTabCount = (tabKey) => {
        return getOrdersByTab(tabKey).length;
    };

    const tabs = [
        {
            key: 'pending',
            label: __('orders.status_pending'),
            icon: Clock,
            count: getTabCount('pending'),
            color: 'text-yellow-600'
        },
        {
            key: 'pickup',
            label: __('orders.status_processing'),
            icon: Package,
            count: getTabCount('pickup'),
            color: 'text-blue-600'
        },
        {
            key: 'shipping',
            label: __('orders.status_shipping'),
            icon: MapPin,
            count: getTabCount('shipping'),
            color: 'text-purple-600'
        },
        {
            key: 'review',
            label: __('marketplace.write_review'),
            icon: Star,
            count: getTabCount('review'),
            color: 'text-green-600'
        }
    ];

    const cancelOrder = async (order) => {
        if (cancellingOrders.has(order.id)) return;

        setOrderToCancel(order);
        setShowCancelModal(true);
    };

    const confirmCancelOrder = async () => {
        if (!orderToCancel || cancellingOrders.has(orderToCancel.id)) return;

        setCancellingOrders(prev => new Set([...prev, orderToCancel.id]));

        try {
            const response = await fetch(`/marketplace/orders/${orderToCancel.id}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    reason: __('orders.cancel_order')
                })
            });

            const data = await response.json();

            if (response.ok) {
                addAlert('success', data.message || __('orders.cancel_success'));
                setShowCancelModal(false);
                setOrderToCancel(null);
                window.location.reload();
            } else {
                addAlert('error', data.error || __('orders.cancel_error'));
            }
        } catch (error) {
            console.error('Error cancelling order:', error);
            addAlert('error', __('orders.cancel_error'));
        } finally {
            setCancellingOrders(prev => {
                const newSet = new Set(prev);
                newSet.delete(orderToCancel.id);
                return newSet;
            });
        }
    };

    const cancelCancelOrder = () => {
        setShowCancelModal(false);
        setOrderToCancel(null);
    };

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`${__('orders.my_orders')} - PickleSocial ${__('marketplace.marketplace')}`} />

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">
                            {__('marketplace.home')}
                        </Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{__('orders.my_orders')}</span>
                    </div>
                </div>
            </div>

            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-2xl font-bold text-primary">
                        {__('orders.my_orders')}
                    </h1>
                </div>

                {/* Tabs Navigation */}
                <div className="bg-white rounded-lg shadow-sm border mb-6 overflow-hidden">
                    <div className="grid grid-cols-2 sm:grid-cols-4 w-full divide-x divide-gray-200">
                        {tabs.map((tab) => {
                            const IconComponent = tab.icon;
                            const isActive = activeTab === tab.key;
                            return (
                                <button
                                    key={tab.key}
                                    onClick={() => setActiveTab(tab.key)}
                                    className={`relative p-3 sm:p-6 text-center transition-all duration-200 border-b sm:border-b-0 min-h-[120px] sm:min-h-[140px] min-w-[180px] sm:min-w-[220px] flex items-center justify-center ${
                                        isActive
                                            ? 'bg-primary/5 border-b-2 border-b-primary'
                                            : 'hover:bg-gray-50 border-b-2 border-b-transparent'
                                    }`}
                                >
                                    <div className="flex flex-col items-center space-y-2 w-full">
                                        <div className={`p-2 sm:p-3 rounded-full transition-all duration-200 ${
                                            isActive
                                                ? 'bg-primary text-white'
                                                : 'bg-gray-100 text-gray-500'
                                        }`}>
                                            <IconComponent className="h-4 w-4 sm:h-6 sm:w-6" />
                                        </div>
                                        <div className="flex flex-col items-center min-h-[45px] min-w-[100px] sm:min-w-[120px] justify-center">
                                            <p className={`font-medium text-xs sm:text-sm leading-tight text-center break-words max-w-full px-1 ${
                                                isActive ? 'text-primary' : 'text-gray-700'
                                            }`}>
                                                {tab.label}
                                            </p>
                                            <div className="min-h-[22px] flex items-center justify-center mt-1">
                                                {tab.count > 0 && (
                                                    <div className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full min-w-[20px] ${
                                                        isActive
                                                            ? 'bg-primary text-white'
                                                            : 'bg-red-500 text-white'
                                                    }`}>
                                                        {tab.count}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            );
                        })}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="min-h-[500px]">
                    {(() => {
                        const filteredOrders = getOrdersByTab(activeTab);

                        return filteredOrders.length > 0 ? (
                            <div className="space-y-6 w-full">
                                {filteredOrders.map((order) => (
                                    <div key={order.id} className="bg-white rounded-lg shadow-sm border">
                                        {/* Order Header */}
                                        <div className="p-4 sm:p-6 border-b">
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                                                <div className="grid grid-cols-2 sm:flex sm:items-center gap-4 sm:space-x-6">
                                                    <div>
                                                        <p className="text-sm text-gray-500">{__('orders.order_number')}</p>
                                                        <p className="font-mono font-medium text-sm">{order.order_number}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm text-gray-500">{__('orders.order_date')}</p>
                                                        <p className="font-medium text-sm">
                                                            {new Date(order.created_at).toLocaleDateString('vi-VN')}
                                                        </p>
                                                    </div>
                                                    <div className="col-span-2 sm:col-span-1">
                                                        <p className="text-sm text-gray-500">{__('orders.total_amount')}</p>
                                                        <p className="font-medium text-primary">
                                                            {formatCurrency(order.total_amount)}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-3">
                                                    <StatusBadge
                                                        status={order.status}
                                                        text={__(`orders.status_${order.status}`)}
                                                        color={getStatusBadgeColor(order.status)}
                                                    />
                                                    <StatusBadge
                                                        status={order.payment_status}
                                                        text={__(`orders.payment_${order.payment_status}`)}
                                                        color={getPaymentStatusBadgeColor(order.payment_status)}
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        {/* Order Items Preview */}
                                        <div className="p-6">
                                            <div className="space-y-4">
                                                {order.order_details.slice(0, 2).map((item) => (
                                                    <div key={item.id} className="flex items-center space-x-4">
                                                        <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden">
                                                            <Link href={`/marketplace/product/${item.product?.slug}`} className="block w-full h-full">
                                                                <ImageWithFallback
                                                                    src={item.product_image_url}
                                                                    alt={item.product_name}
                                                                    fallbackText={item.product_name.charAt(0)}
                                                                    width="w-full"
                                                                    height="h-full"
                                                                    imageClassName="object-cover hover:scale-105 transition-transform duration-200"
                                                                    rounded="rounded-lg"
                                                                    bgColor="bg-gray-100"
                                                                    textColor="text-primary"
                                                                    textSize="text-lg"
                                                                />
                                                            </Link>
                                                        </div>
                                                        <div className="flex-1 min-w-0">
                                                            <h4 className="text-sm font-medium text-gray-900 truncate">
                                                                <Link
                                                                    href={`/marketplace/product/${item.product?.slug}`}
                                                                    className="hover:text-primary transition-colors duration-200"
                                                                >
                                                                    {item.product_name}
                                                                </Link>
                                                            </h4>
                                                            <p className="text-sm text-gray-500">
                                                                {formatCurrency(item.unit_price)} x {item.quantity}
                                                            </p>
                                                        </div>
                                                        <div className="text-right">
                                                            <p className="text-sm font-medium text-gray-900">
                                                                {formatCurrency(item.total_price)}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))}
                                                {order.order_details.length > 2 && (
                                                    <p className="text-sm text-gray-500 text-center">
                                                        {__('orders.and_more_items', { count: order.order_details.length - 2 })}
                                                    </p>
                                                )}
                                            </div>
                                        </div>

                                        {/* Order Actions */}
                                        <div className="px-4 sm:px-6 py-4 border-t bg-gray-50">
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                                <div className="flex items-center space-x-4">
                                                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                                                        <MapPin className="h-4 w-4" />
                                                        <span className="truncate">{order.shipping_address}</span>
                                                    </div>
                                                </div>
                                                <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                                                    {/* Tab-specific actions */}
                                                    {activeTab === 'pending' && (
                                                        <>
                                                            {canRetryPayment(order) && (
                                                                <Button
                                                                    onClick={() => retryPayment(order)}
                                                                    disabled={retryingOrders.has(order.id)}
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-primary border-primary hover:bg-primary hover:text-white w-full sm:w-auto"
                                                                >
                                                                    {retryingOrders.has(order.id) ? (
                                                                        <>
                                                                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                                                            {__('orders.processing')}
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <CreditCard className="h-4 w-4 mr-2" />
                                                                            {hasValidPaymentUrl(order) ? __('orders.continue_payment') : __('orders.retry_payment')}
                                                                        </>
                                                                    )}
                                                                </Button>
                                                            )}
                                                            {canCancelOrder(order) && (
                                                                <Button
                                                                    onClick={() => cancelOrder(order)}
                                                                    disabled={cancellingOrders.has(order.id)}
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 border-red-600 hover:bg-red-600 hover:text-white w-full sm:w-auto"
                                                                >
                                                                    {cancellingOrders.has(order.id) ? (
                                                                        <>
                                                                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                                                            {__('orders.cancelling')}
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <Clock className="h-4 w-4 mr-2" />
                                                                            {__('orders.cancel_order')}
                                                                        </>
                                                                    )}
                                                                </Button>
                                                            )}
                                                        </>
                                                    )}

                                                    {activeTab === 'pickup' && (
                                                        <div className="text-sm text-blue-600 font-medium flex items-center justify-center sm:justify-start p-2 bg-blue-50 rounded">
                                                            <Package className="h-4 w-4 mr-2" />
                                                            {__('orders.status_processing')}
                                                        </div>
                                                    )}

                                                    {activeTab === 'shipping' && (
                                                        <div className="text-sm text-purple-600 font-medium flex items-center justify-center sm:justify-start p-2 bg-purple-50 rounded">
                                                            <MapPin className="h-4 w-4 mr-2" />
                                                            {__('orders.status_shipping')}
                                                        </div>
                                                    )}

                                                    {activeTab === 'review' && canReviewOrder(order) && (
                                                        <Link href={`/marketplace/orders/${order.id}`} className="w-full sm:w-auto">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="text-secondary border-secondary hover:bg-secondary hover:text-white w-full"
                                                            >
                                                                <Star className="h-4 w-4 mr-2" />
                                                                {__('marketplace.write_review')}
                                                            </Button>
                                                        </Link>
                                                    )}

                                                    <Link href={`/marketplace/orders/${order.id}`} className="w-full sm:w-auto">
                                                        <Button variant="outline" size="sm" className="w-full">
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            {__('orders.view_details')}
                                                        </Button>
                                                    </Link>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                {/* Pagination */}
                                {orders.links && (
                                    <div className="flex justify-center mt-8">
                                        {/* Add pagination component here */}
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    {activeTab === 'pending' && __('orders.no_orders')}
                                    {activeTab === 'pickup' && __('orders.no_orders')}
                                    {activeTab === 'shipping' && __('orders.no_orders')}
                                    {activeTab === 'review' && __('orders.no_orders')}
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    {__('orders.no_orders_description')}
                                </p>
                                <Link href="/marketplace">
                                    <Button className="bg-primary hover:bg-tertiary">
                                        {__('marketplace.continue_shopping')}
                                    </Button>
                                </Link>
                            </div>
                        );
                    })()}
                </div>
            </div>

            <Footer />

            {/* Confirm Cancel Order Modal */}
            <ConfirmDeleteModal
                isOpen={showCancelModal}
                onClose={cancelCancelOrder}
                onConfirm={confirmCancelOrder}
                title={__('orders.confirm_cancel_order')}
                message={__('orders.delete_order_confirmation', { order_number: orderToCancel?.order_number })}
                isProcessing={cancellingOrders.has(orderToCancel?.id)}
            />
        </div>
    );
}
