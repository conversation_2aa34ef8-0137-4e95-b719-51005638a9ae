import React, { useState } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import {
    Package,
    Calendar,
    CreditCard,
    MapPin,
    Phone,
    Mail,
    ArrowLeft,
    RefreshCw,
    ChevronRight,
    Download,
    Truck,
    Star,
    MessageSquare
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import { Button } from '@/Components/ui/button';
import StatusBadge from '@/Components/ui/StatusBadge';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Footer from '@/Components/Landing/Footer';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';

export default function OrderShow({ order, topCategories = [], moreCategories = [] }) {
    const { addAlert } = useToast();
    const [isRetrying, setIsRetrying] = useState(false);
    const [showReviewModal, setShowReviewModal] = useState(false);
    const [reviewingItems, setReviewingItems] = useState([]);
    const [reviewForms, setReviewForms] = useState({});
    const [isSubmittingReview, setIsSubmittingReview] = useState(false);

    const getStatusBadgeColor = (status) => {
        const colors = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'confirmed': 'bg-blue-100 text-blue-800',
            'processing': 'bg-purple-100 text-purple-800',
            'shipping': 'bg-indigo-100 text-indigo-800',
            'completed': 'bg-green-100 text-green-800',
            'cancelled': 'bg-red-100 text-red-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getPaymentStatusBadgeColor = (status) => {
        const colors = {
            'unpaid': 'bg-red-100 text-red-800',
            'paid': 'bg-green-100 text-green-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'failed': 'bg-red-100 text-red-800',
            'refunded': 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const retryPayment = () => {

        const existingUrl = getExistingPaymentUrl();
        if (existingUrl) {

            window.location.href = existingUrl;
            return;
        }

        setIsRetrying(true);

        const paymentMethod = order.payment_method;

        if (paymentMethod === 'vnpay') {
            fetch(`/marketplace/payment/vnpay/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    alert(__('marketplace.payment_link_create_failed'));
                    setIsRetrying(false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(__('marketplace.general_error'));
                setIsRetrying(false);
            });
        } else if (paymentMethod === 'momo') {
            fetch(`/marketplace/payment/momo/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    order_id: order.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.payment_url) {
                    window.location.href = data.payment_url;
                } else {
                    alert(__('marketplace.payment_link_create_failed'));
                    setIsRetrying(false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(__('marketplace.general_error'));
                setIsRetrying(false);
            });
        }
    };


    const hasValidPaymentUrl = () => {
        if (!order.payment_url_created_at) return false;

        const createdAt = new Date(order.payment_url_created_at);
        const now = new Date();
        const diffMinutes = (now - createdAt) / (1000 * 60);

        const paymentMethod = order.payment_method;
        if (diffMinutes <= 30) {
            if (paymentMethod === 'momo' && order.momo_payment_url) {
                return true;
            }
            if (paymentMethod === 'vnpay' && order.vnpay_payment_url) {
                return true;
            }
        }

        return false;
    };


    const getExistingPaymentUrl = () => {
        if (!hasValidPaymentUrl()) return null;

        const paymentMethod = order.payment_method;
        if (paymentMethod === 'vnpay' && order.vnpay_payment_url) {
            return order.vnpay_payment_url;
        } else if (paymentMethod === 'momo' && order.momo_payment_url) {
            return order.momo_payment_url;
        }

        return null;
    };

    const canRetryPayment = ['vnpay', 'momo'].includes(order.payment_method) &&
                           order.payment_status === 'unpaid' &&
                           order.status !== 'cancelled';

    const canReviewOrder = () => {
        return order.status === 'completed' && order.payment_status === 'paid';
    };


    const canReviewItem = (item) => {
        console.log('Checking review for item:', item.id, 'Review:', item.review);
        return !item.review;
    };

    const openReviewModal = () => {

        const unreviewedItems = order.order_details.filter(item => canReviewItem(item));

        if (unreviewedItems.length === 0) {
            return;
        }

        setReviewingItems(unreviewedItems);


        const initialForms = {};
        unreviewedItems.forEach(item => {
            initialForms[item.id] = {
                rating: 5,
                comment: '',
                images: []
            };
        });
        setReviewForms(initialForms);
        setShowReviewModal(true);
    };

    const closeReviewModal = () => {
        setShowReviewModal(false);
        setReviewingItems([]);
        setReviewForms({});
    };

    const updateReviewForm = (itemId, field, value) => {
        setReviewForms(prev => ({
            ...prev,
            [itemId]: {
                ...prev[itemId],
                [field]: value
            }
        }));
    };

    const submitReviews = async () => {
        if (reviewingItems.length === 0 || isSubmittingReview) return;

        setIsSubmittingReview(true);

        try {

            const reviewPromises = reviewingItems.map(async (item) => {
                const reviewData = reviewForms[item.id];

                const response = await fetch('/marketplace/orders/review', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        market_order_detail_id: item.id,
                        rating: reviewData.rating,
                        comment: reviewData.comment,
                        images: reviewData.images
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || `${__('marketplace.failed_to_submit_review')} ${item.product_name}`);
                }

                return data;
            });

            await Promise.all(reviewPromises);

            alert(__('marketplace.all_reviews_submitted_success'));
            closeReviewModal();
            window.location.reload();

        } catch (error) {
            console.error('Error submitting reviews:', error);
            alert(error.message || __('marketplace.submit_error'));
        } finally {
            setIsSubmittingReview(false);
        }
    };

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`${__('orders.order_details')} ${order.order_number} - PickleSocial ${__('marketplace.marketplace')}`} />

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">
                            {__('marketplace.home')}
                        </Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <Link href="/marketplace/orders" className="text-gray-500 hover:text-primary">
                            {__('orders.my_orders')}
                        </Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-primary font-medium">{order.order_number}</span>
                    </div>
                </div>
            </div>

            <div className="mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="max-w-4xl mx-auto">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-8">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                {__('orders.order_details')}
                            </h1>
                            <p className="text-gray-600 mt-1">{order.order_number}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                            {canRetryPayment && (
                                <Button
                                    onClick={retryPayment}
                                    disabled={isRetrying}
                                    className="bg-primary hover:bg-tertiary"
                                >
                                    {isRetrying ? (
                                        <>
                                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                            {__('orders.processing')}
                                        </>
                                    ) : (
                                        <>
                                            <CreditCard className="mr-2 h-4 w-4" />
                                            {hasValidPaymentUrl() ? __('orders.continue_payment') : __('orders.retry_payment')}
                                        </>
                                    )}
                                </Button>
                            )}
                            <Link href="/marketplace/orders">
                                <Button variant="outline">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    {__('orders.back_to_orders')}
                                </Button>
                            </Link>
                        </div>
                    </div>

                    {/* Order Status Alert */}
                    {order.payment_status === 'unpaid' && canRetryPayment && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div className="flex items-center space-x-3">
                                <CreditCard className="h-5 w-5 text-yellow-600" />
                                <div className="flex-1">
                                    <h3 className="text-sm font-medium text-yellow-800">
                                        {hasValidPaymentUrl() ? __('orders.payment_started') : __('orders.payment_pending')}
                                    </h3>
                                    <p className="text-sm text-yellow-700 mt-1">
                                        {hasValidPaymentUrl()
                                            ? __('orders.payment_continue_description')
                                            : __('orders.payment_pending_description')}
                                    </p>
                                </div>
                                <Button
                                    onClick={retryPayment}
                                    disabled={isRetrying}
                                    size="sm"
                                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                                >
                                    {isRetrying ? (
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                    ) : (
                                        hasValidPaymentUrl() ? __('orders.continue_payment') : __('orders.retry_payment')
                                    )}
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* Review Alert for Completed Orders */}
                    {canReviewOrder() && order.order_details.some(item => canReviewItem(item)) && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div className="flex items-center space-x-3">
                                <Star className="h-5 w-5 text-green-600" />
                                <div className="flex-1">
                                    <h3 className="text-sm font-medium text-green-800">
                                        {__('marketplace.order_completed_title')}
                                    </h3>
                                    <p className="text-sm text-green-700 mt-1">
                                        {__('marketplace.order_completed_description')}
                                    </p>
                                </div>
                                {/* Ẩn nút nếu tất cả sản phẩm đã được đánh giá */}
                                {order.order_details.some(item => canReviewItem(item)) && (
                                    <Button
                                        onClick={openReviewModal}
                                        size="sm"
                                        className="bg-green-600 hover:bg-green-700 text-white"
                                    >
                                        <Star className="h-4 w-4 mr-1" />
                                        {__('marketplace.write_review')}
                                    </Button>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Order Summary Card */}
                    <div className="bg-white rounded-lg shadow-sm border mb-6">
                        <div className="p-6 border-b">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Order Status */}
                                <div className="flex items-center space-x-3">
                                    <Package className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-500">{__('orders.status')}</p>
                                        <StatusBadge
                                            status={order.status}
                                            text={__(`orders.status_${order.status}`)}
                                            color={getStatusBadgeColor(order.status)}
                                        />
                                    </div>
                                </div>

                                {/* Payment Status */}
                                <div className="flex items-center space-x-3">
                                    <CreditCard className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-500">{__('orders.payment_status')}</p>
                                        <StatusBadge
                                            status={order.payment_status}
                                            text={__(`orders.payment_${order.payment_status}`)}
                                            color={getPaymentStatusBadgeColor(order.payment_status)}
                                        />
                                    </div>
                                </div>

                                {/* Order Date */}
                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-500">{__('orders.order_date')}</p>
                                        <p className="font-medium">
                                            {new Date(order.created_at).toLocaleDateString('vi-VN')}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Customer Info */}
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                {__('orders.customer_info')}
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                                <span className="text-sm font-medium text-gray-700">
                                                    {order.customer_name.charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-900">{order.customer_name}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Phone className="h-4 w-4 text-gray-400" />
                                        <span className="text-gray-700">{order.customer_phone}</span>
                                    </div>
                                    {order.customer_email && (
                                        <div className="flex items-center space-x-3">
                                            <Mail className="h-4 w-4 text-gray-400" />
                                            <span className="text-gray-700">{order.customer_email}</span>
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <div className="flex items-start space-x-3">
                                        <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                                        <div>
                                            <p className="text-sm text-gray-500 mb-1">{__('orders.shipping_address')}</p>
                                            <p className="text-gray-700">{order.shipping_address}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Order Items */}
                    <div className="bg-white rounded-lg shadow-sm border mb-6">
                        <div className="p-6 border-b">
                            <h3 className="text-lg font-medium text-gray-900">
                                {__('orders.order_items')} ({order.order_details.length})
                            </h3>
                        </div>
                        <div className="divide-y">
                            {order.order_details.map((item) => (
                                <div key={item.id} className="p-6 flex items-center space-x-4">
                                    <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden">
                                        <Link href={`/marketplace/product/${item.product?.slug}`} className="block w-full h-full">
                                            <ImageWithFallback
                                                src={item.product_image_url}
                                                alt={item.product_name}
                                                fallbackText={item.product_name.charAt(0)}
                                                width="w-full"
                                                height="h-full"
                                                imageClassName="object-cover hover:scale-105 transition-transform duration-200"
                                                rounded="rounded-lg"
                                                bgColor="bg-gray-100"
                                                textColor="text-primary"
                                                textSize="text-lg"
                                            />
                                        </Link>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="text-sm font-medium text-gray-900 truncate">
                                            <Link
                                                href={`/marketplace/product/${item.product?.slug}`}
                                                className="hover:text-primary transition-colors duration-200"
                                            >
                                                {item.product_name}
                                            </Link>
                                        </h4>
                                        {item.product_sku && (
                                            <p className="text-sm text-gray-500">SKU: {item.product_sku}</p>
                                        )}
                                        <p className="text-sm text-gray-500">
                                            {formatCurrency(item.unit_price)} x {item.quantity}
                                        </p>
                                        {item.review && (
                                            <div className="flex items-center space-x-2 mt-2">
                                                <div className="flex items-center">
                                                    {[...Array(5)].map((_, i) => (
                                                        <Star
                                                            key={i}
                                                            className={`h-4 w-4 ${i < item.review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                                                        />
                                                    ))}
                                                </div>
                                                <span className="text-sm text-gray-600">{__('marketplace.reviewed')}</span>
                                            </div>
                                        )}
                                    </div>
                                    <div className="text-right flex flex-col items-end space-y-2">
                                        <p className="text-sm font-medium text-gray-900">
                                            {formatCurrency(item.total_price)}
                                        </p>
                                        {/* Hiển thị trạng thái đánh giá */}
                                        {item.review ? (
                                            <div className="flex items-center space-x-1 text-green-600">
                                                <Star className="h-4 w-4 fill-current" />
                                                <span className="text-sm">{__('marketplace.reviewed')}</span>
                                            </div>
                                        ) : canReviewOrder() ? (
                                            <div className="flex items-center space-x-1 text-orange-600">
                                                <Star className="h-4 w-4" />
                                                <span className="text-sm">{__('marketplace.pending_review')}</span>
                                            </div>
                                        ) : null}
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="p-6 border-t bg-gray-50">
                            <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">{__('orders.subtotal')}</span>
                                    <span className="font-medium">{formatCurrency(order.subtotal)}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">{__('orders.shipping_fee')}</span>
                                    <span className="font-medium">{formatCurrency(order.shipping_fee)}</span>
                                </div>
                                {order.discount_amount > 0 && (
                                    <div className="flex justify-between text-sm">
                                        <span className="text-gray-600">{__('orders.discount')}</span>
                                        <span className="font-medium text-red-600">
                                            -{formatCurrency(order.discount_amount)}
                                        </span>
                                    </div>
                                )}
                                <div className="flex justify-between text-lg font-semibold pt-2 border-t">
                                    <span>{__('orders.total')}</span>
                                    <span className="text-primary">{formatCurrency(order.total_amount)}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Payment Information */}
                    <div className="bg-white rounded-lg shadow-sm border">
                        <div className="p-6 border-b">
                            <h3 className="text-lg font-medium text-gray-900">
                                {__('orders.payment_information')}
                            </h3>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <p className="text-sm text-gray-500 mb-1">{__('orders.payment_method')}</p>
                                    <p className="font-medium">{order.payment_method_name}</p>
                                </div>
                                {order.payments && order.payments.length > 0 && (
                                    <div>
                                        <p className="text-sm text-gray-500 mb-1">{__('orders.transaction_id')}</p>
                                        <p className="font-mono text-sm">
                                            {order.payments[0].transaction_id || 'N/A'}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Review Modal */}
            {showReviewModal && reviewingItems.length > 0 && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                        <div className="p-6 border-b bg-primary text-white rounded-t-lg">
                            <h2 className="text-xl font-semibold flex items-center">
                                <Star className="h-5 w-5 mr-2" />
                                {__('marketplace.write_reviews_for_order')} #{order.order_number}
                            </h2>
                            <p className="text-primary-100 mt-1">
                                {reviewingItems.length === 1
                                    ? __('marketplace.review_single_product')
                                    : __('marketplace.review_multiple_products', { count: reviewingItems.length })
                                }
                            </p>
                        </div>

                        <div className="p-6 space-y-6">
                            {reviewingItems.map((item, index) => (
                                <div key={item.id} className="border rounded-lg p-4 bg-gray-50">
                                    {/* Product Info */}
                                    <div className="flex items-center space-x-4 mb-4">
                                        <div className="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden">
                                            <Link href={`/marketplace/product/${item.product?.slug}`} className="block w-full h-full">
                                                <ImageWithFallback
                                                    src={item.product_image_url}
                                                    alt={item.product_name}
                                                    fallbackText={item.product_name.charAt(0)}
                                                    width="w-full"
                                                    height="h-full"
                                                    imageClassName="object-cover hover:scale-105 transition-transform duration-200"
                                                    rounded="rounded-lg"
                                                    bgColor="bg-gray-100"
                                                    textColor="text-primary"
                                                    textSize="text-lg"
                                                />
                                            </Link>
                                        </div>
                                        <div className="flex-1">
                                            <h4 className="font-medium text-gray-900">
                                                <Link
                                                    href={`/marketplace/product/${item.product?.slug}`}
                                                    className="hover:text-primary transition-colors duration-200"
                                                >
                                                    {item.product_name}
                                                </Link>
                                            </h4>
                                            {item.product_sku && (
                                                <p className="text-sm text-gray-500">SKU: {item.product_sku}</p>
                                            )}
                                            <p className="text-sm text-gray-600">
                                                {formatCurrency(item.unit_price)} x {item.quantity} = {formatCurrency(item.total_price)}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <span className="text-sm text-gray-500">
                                                {index + 1}/{reviewingItems.length}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Rating */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            {__('marketplace.rating')}
                                        </label>
                                        <div className="flex items-center space-x-1">
                                            {[...Array(5)].map((_, i) => (
                                                <button
                                                    key={i}
                                                    type="button"
                                                    onClick={() => updateReviewForm(item.id, 'rating', i + 1)}
                                                    className="focus:outline-none"
                                                >
                                                    <Star
                                                        className={`h-6 w-6 ${i < (reviewForms[item.id]?.rating || 5) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                                                    />
                                                </button>
                                            ))}
                                            <span className="ml-2 text-sm text-gray-600">
                                                ({reviewForms[item.id]?.rating || 5}/5)
                                            </span>
                                        </div>
                                    </div>

                                    {/* Comment */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            {__('marketplace.comment')}
                                        </label>
                                        <textarea
                                            value={reviewForms[item.id]?.comment || ''}
                                            onChange={(e) => updateReviewForm(item.id, 'comment', e.target.value)}
                                            rows={3}
                                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder={__('marketplace.share_experience_about', { product: item.product_name })}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="p-6 border-t bg-gray-50 flex justify-between items-center">
                            <div className="text-sm text-gray-600">
                                {__('marketplace.total_reviews')}: <strong>{reviewingItems.length}</strong>
                            </div>
                            <div className="flex space-x-3">
                                <Button
                                    onClick={closeReviewModal}
                                    variant="outline"
                                    disabled={isSubmittingReview}
                                >
                                    {__('marketplace.cancel')}
                                </Button>
                                <Button
                                    onClick={submitReviews}
                                    disabled={isSubmittingReview}
                                    className="bg-primary hover:bg-tertiary"
                                >
                                    {isSubmittingReview ? (
                                        <>
                                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                            {__('marketplace.submitting')}
                                        </>
                                    ) : (
                                        <>
                                            <MessageSquare className="h-4 w-4 mr-2" />
                                            {__('marketplace.submit_all_reviews')}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <Footer />
        </div>
    );
}
