import React, { useState, useEffect } from 'react';
import { Head, Link, usePage, router } from '@inertiajs/react';
import {
    Search,
    Heart,
    ShoppingCart,
    ChevronRight,
    ChevronLeft,
    X,
    Star,
    Mail,
    Clock,
    Facebook,
    Instagram,
    Twitter,
    Youtube,
    CreditCard,
    User,
    ChevronDown,
    ArrowLeft,
    Lock,
    Trash2,
    Plus,
    Minus,
    Shield,
    Truck,
    RefreshCw,
    Check,
    CheckCircle,
    Share2,
    ThumbsUp,
    ThumbsDown,
    MessageSquare
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { FALLBACK_IMAGE_URL } from '@/constants/config';
import { useToast } from '@/Hooks/useToastContext';
import Loading from '@/Components/Loading';
import { Checkbox } from '@/Components/ui/checkbox';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Badge } from '@/Components/ui/badge';
import { Dialog, DialogContent, DialogTrigger } from '@/Components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/Components/ui/tabs';
import Pagination from '@/Components/Pagination';
import Footer from '@/Components/Landing/Footer';
import ImageWithFallback from '@/Components/ImageWithFallback';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import ProductActionButtons from '@/Components/ProductActionButtons';

export default function ProductDetail({ product, relatedProducts, topCategories, moreCategories }) {
    const { flash, errors } = usePage().props;
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [quantity, setQuantity] = useState(1);
    const [selectedColor, setSelectedColor] = useState('red');
    const [selectedDimension, setSelectedDimension] = useState('');
    const [isWishlist, setIsWishlist] = useState(false);
    const [activeTab, setActiveTab] = useState('description');
    const [showReviewModal, setShowReviewModal] = useState(false);
    const [showLightbox, setShowLightbox] = useState(false);
    const [currentImage, setCurrentImage] = useState(0);
    const [reviewVotes, setReviewVotes] = useState({});
    const [reviewCounts, setReviewCounts] = useState({});
    const [reviewForm, setReviewForm] = useState({
        rating: 5,
        comment: '',
        images: []
    });
    const [isSubmittingReview, setIsSubmittingReview] = useState(false);
    const { addAlert } = useToast();
    const { auth } = usePage().props;
    const processingRef = React.useRef(false);


    useEffect(() => {
        const handleStart = (event) => {
            if (event.detail.visit.url.includes('/marketplace/product/')) {
                setIsPageLoading(true);
            }
        };
        const handleFinish = () => setIsPageLoading(false);

        document.addEventListener('inertia:start', handleStart);
        document.addEventListener('inertia:finish', handleFinish);

        return () => {
            document.removeEventListener('inertia:start', handleStart);
            document.removeEventListener('inertia:finish', handleFinish);
        };
    }, []);


    useEffect(() => {
        console.log('Product detail data:', product);
        console.log('Product image paths:', {
            image: product.image,
            image_url: product.image_url,
            image_url_formatted: product.image_url_formatted
        });
    }, [product]);


    const images = (() => {
        let productImages = [];


        if (product.images) {
            try {
                const parsedImages = JSON.parse(product.images);
                productImages = parsedImages.filter(img => img && img.trim() !== '');
            } catch (e) {
                console.error('Error parsing product images:', e);
            }
        }


        if (productImages.length === 0) {
            const mainImage = product.image || product.image_url_formatted || product.image_url;
            if (mainImage && mainImage !== FALLBACK_IMAGE_URL) {
                productImages = [mainImage];
            }
        }


        if (productImages.length === 0) {
            productImages = [FALLBACK_IMAGE_URL];
        }

        return productImages;
    })();


    const dimensions = product.dimensions ? product.dimensions.split(',').map(dim => dim.trim()) : [];

    const handleQuantityChange = (value) => {
        const newValue = quantity + value;
        if (newValue >= 1) {
            setQuantity(newValue);
        }
    };

    const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const handleAddToCart = async (productId = null, message = null, type = 'success') => {

        if (processingRef.current) {
            return;
        }

        processingRef.current = true;

        try {
            const targetProductId = productId || product.id;
            const targetQuantity = quantity;
            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('marketplace.csrf_token_missing'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        product_id: targetProductId,
                        quantity: targetQuantity
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.product_id && item.product_id === targetProductId) ||
                    (item.id === targetProductId && !item.product_id)
                );

                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += targetQuantity;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        product_id: targetProductId,
                        name: product.name,
                        slug: product.slug,
                        image: product.image || product.image_url_formatted || product.image_url,
                        price: product.sale_price,
                        quantity: targetQuantity,
                        category: product.category?.name || '',
                        stock_quantity: product.quantity,
                        subtotal: product.sale_price * targetQuantity
                    });
                }

                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', __('marketplace.added_to_cart'));
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Cart error:', error);
            addAlert('error', __('marketplace.cart_add_error'));
        } finally {

            setTimeout(() => {
                processingRef.current = false;
            }, 1000);
        }
    };

    const handleWishlist = () => {
        setIsWishlist(!isWishlist);
        addAlert('success', isWishlist ? __('marketplace.removed_from_wishlist_success') : __('marketplace.added_to_wishlist_success'));
    };

    const handleImageChange = (index) => {
        setCurrentImage(index);
    };

    const handlePrevImage = () => {
        setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
    };

    const handleNextImage = () => {
        setCurrentImage((prev) => (prev + 1) % images.length);
    };

    const handleVoteClick = async (reviewId, voteType) => {
        if (!auth.user) {
            addAlert('error', __('marketplace.login_to_vote'));
            return;
        }

        try {
            const csrfToken = getCsrfToken();
            const response = await fetch(`/marketplace/reviews/${reviewId}/vote`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    vote_type: voteType
                })
            });

            const data = await response.json();

            if (data.success) {
                setReviewCounts(prev => ({
                    ...prev,
                    [reviewId]: {
                        helpful_count: data.helpful_count,
                        unhelpful_count: data.unhelpful_count
                    }
                }));

                setReviewVotes(prev => ({
                    ...prev,
                    [reviewId]: data.user_vote
                }));

                addAlert('success', data.message);
            } else {
                addAlert('error', data.error || __('marketplace.vote_error'));
            }
        } catch (error) {
            console.error('Vote error:', error);
            addAlert('error', __('marketplace.vote_error'));
        }
    };

    const loadVoteStatuses = async () => {
        if (!auth.user || !product.reviews || product.reviews.length === 0) {
            return;
        }

        try {
            const reviewIds = product.reviews.map(review => review.id);
            const csrfToken = getCsrfToken();

            const promises = reviewIds.map(reviewId =>
                fetch(`/marketplace/reviews/${reviewId}/vote-status`, {
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                })
            );

            const responses = await Promise.all(promises);
            const data = await Promise.all(responses.map(response => response.json()));

            const votes = {};
            const counts = {};

            data.forEach((result, index) => {
                const reviewId = reviewIds[index];
                if (result.success) {
                    votes[reviewId] = result.user_vote;
                    counts[reviewId] = {
                        helpful_count: result.helpful_count,
                        unhelpful_count: result.unhelpful_count
                    };
                }
            });

            setReviewVotes(votes);
            setReviewCounts(counts);
        } catch (error) {
            console.error('Error loading vote statuses:', error);
        }
    };

    const submitReview = async () => {
        if (isSubmittingReview) return;

        if (!auth.user) {
            addAlert('error', __('reviews.login_required'));
            return;
        }

        if (!canWriteReview()) {
            addAlert('error', __('reviews.cannot_review'));
            return;
        }

        setIsSubmittingReview(true);

        try {
            const csrfToken = getCsrfToken();
            const response = await fetch('/marketplace/products/review', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: product.id,
                    rating: reviewForm.rating,
                    comment: reviewForm.comment,
                    images: reviewForm.images
                })
            });

            const data = await response.json();

            if (response.ok) {
                addAlert('success', data.message || __('reviews.submit_success'));
                setShowReviewModal(false);
                window.location.reload();
            } else {
                addAlert('error', data.error || __('reviews.submit_error'));
            }
        } catch (error) {
            console.error('Error submitting review:', error);
            addAlert('error', __('reviews.submit_error'));
        } finally {
            setIsSubmittingReview(false);
        }
    };

    const canWriteReview = () => {
        if (!auth.user) return false;

        const hasAlreadyReviewed = product.reviews_data &&
            product.reviews_data.some(review => review.user_id === auth.user.id);

        if (hasAlreadyReviewed) return false;
        return true;
    };

    useEffect(() => {
        loadVoteStatuses();
    }, [auth.user, product.reviews]);


    return (
        <div className="flex flex-col min-h-screen">
            <Head title={`${product.name} - PickleSocial ${__('marketplace.marketplace')}`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-primary">{__('marketplace.breadcrumb_home')}</Link>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        {product.category && (
                            <>
                                <Link href={`/marketplace/category/${product.category.slug}`} className="text-gray-500 hover:text-primary">
                                    {product.category.name}
                                </Link>
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                            </>
                        )}
                        <span className="text-primary font-medium">{product.name}</span>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] p-8 mb-8">
                    <div className="flex flex-col lg:flex-row gap-8">
                        <div className="lg:w-1/2 flex-shrink-0">
                            <div className="relative w-full h-[400px] rounded-lg overflow-hidden mb-4 bg-gray-100 flex items-center justify-center">
                                <ImageWithFallback
                                    src={images[currentImage]}
                                    alt={product.name}
                                    fallbackText={product.name.charAt(0)}
                                    width="w-full"
                                    height="h-full"
                                    imageClassName="object-contain"
                                    rounded="rounded-lg"
                                    bgColor="bg-gray-100"
                                    textColor="text-primary"
                                    textSize="text-4xl"
                                />
                                <button
                                    onClick={() => setShowLightbox(true)}
                                    className="absolute top-4 right-4 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors"
                                >
                                    <Search className="h-5 w-5" />
                                </button>
                            </div>

                            <div className="relative min-h-[88px] flex items-center">
                                {images.length > 1 ? (
                                    <div className="w-full">
                                        <div className="flex gap-2 overflow-x-auto pb-2">
                                            {images.map((image, index) => (
                                                <button
                                                    key={index}
                                                    onClick={() => handleImageChange(index)}
                                                    className={`w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-colors bg-gray-100 flex items-center justify-center ${
                                                        currentImage === index ? 'border-primary' : 'border-gray-200 hover:border-gray-300'
                                                    }`}
                                                >
                                                    <ImageWithFallback
                                                        src={image}
                                                        alt={`Thumbnail ${index + 1}`}
                                                        fallbackText={(index + 1).toString()}
                                                        width="w-full"
                                                        height="h-full"
                                                        imageClassName="object-cover"
                                                        rounded="rounded-none"
                                                        bgColor="bg-gray-100"
                                                        textColor="text-primary"
                                                        textSize="text-sm"
                                                    />
                                                </button>
                                            ))}
                                        </div>

                                        {images.length > 4 && (
                                            <>
                                                <button
                                                    onClick={handlePrevImage}
                                                    className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:shadow-lg transition-shadow"
                                                >
                                                    <ChevronLeft className="h-4 w-4" />
                                                </button>
                                                <button
                                                    onClick={handleNextImage}
                                                    className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:shadow-lg transition-shadow"
                                                >
                                                    <ChevronRight className="h-4 w-4" />
                                                </button>
                                            </>
                                        )}
                                    </div>
                                ) : (

                                    <div className="flex gap-2">
                                        <div className="w-20 h-20 rounded-lg border-2 border-primary overflow-hidden bg-gray-100 flex items-center justify-center">
                                            <ImageWithFallback
                                                src={images[0]}
                                                alt="Thumbnail"
                                                fallbackText="1"
                                                width="w-full"
                                                height="h-full"
                                                imageClassName="object-cover"
                                                rounded="rounded-none"
                                                bgColor="bg-gray-100"
                                                textColor="text-primary"
                                                textSize="text-sm"
                                            />
                                        </div>
                                        <div className="w-20 h-20 rounded-lg border border-gray-200 bg-gray-50 opacity-30"></div>
                                        <div className="w-20 h-20 rounded-lg border border-gray-200 bg-gray-50 opacity-20"></div>
                                        <div className="w-20 h-20 rounded-lg border border-gray-200 bg-gray-50 opacity-10"></div>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="lg:w-1/2 flex-1 min-w-0">
                            {product.is_featured && (
                                <Badge className="bg-secondary text-white mb-2">{__('product.bestseller')}</Badge>
                            )}
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">{product.name}</h1>
                            <div className="flex items-center gap-4 mb-2">
                                <Link href={`/marketplace/brand/${product.brand}`} className="text-primary font-semibold">
                                    {product.brand}
                                </Link>
                                <span className="text-gray-500 text-sm">SKU: {product.sku}</span>
                            </div>
                            <div className="flex items-center gap-2 mb-4">
                                <div className="flex text-secondary">
                                    {[...Array(5)].map((_, i) => (
                                        <Star
                                            key={i}
                                            className={`h-5 w-5 ${
                                                i < Math.floor(product.rating || 0)
                                                    ? 'fill-current'
                                                    : i < (product.rating || 0)
                                                    ? 'fill-current opacity-50'
                                                    : ''
                                            }`}
                                        />
                                    ))}
                                </div>
                                <Link href="#reviews" className="text-sm text-gray-500">
                                    {product.review_count || 0} {__('marketplace.reviews')}
                                </Link>
                            </div>
                            <div className="flex items-center gap-4 mb-4">
                                <span className="text-2xl font-bold text-primary">
                                    {formatCurrency(product.sale_price)}
                                </span>
                                {product.import_price && Number(product.import_price) > Number(product.sale_price) && (
                                    <>
                                        <span className="text-lg text-gray-500 line-through">
                                            {formatCurrency(product.import_price)}
                                        </span>
                                        <Badge className="bg-secondary text-white">
                                            {__('product.discount_percent', { percent: Math.round(((product.import_price - product.sale_price) / product.import_price) * 100) })}
                                        </Badge>
                                    </>
                                )}
                            </div>
                            <p className="text-gray-600 mb-4">{product.description}</p>
                            <div className="flex items-center gap-2 text-green-600 font-semibold mb-6">
                                <CheckCircle className="h-5 w-5" />
                                <span>{__('marketplace.delivery_1_3_days')}</span>
                            </div>

                            {/* Product Options */}
                            <div className="space-y-6 mb-6">
                                {product.colors && (
                                    <div>
                                        <h3 className="text-sm font-semibold mb-2">{__('marketplace.colors')}:</h3>
                                        <div className="flex gap-2">
                                            {product.colors.split(',').map((color) => (
                                                <button
                                                    key={color}
                                                    onClick={() => setSelectedColor(color)}
                                                    className={`w-8 h-8 rounded-full border-2 transition-transform ${
                                                        selectedColor === color
                                                            ? 'scale-110 border-primary'
                                                            : 'border-white'
                                                    }`}
                                                    style={{ backgroundColor: color }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                                {dimensions.length > 0 && (
                                    <div>
                                        <h3 className="text-sm font-semibold mb-2">{__('marketplace.sizes')}:</h3>
                                        <div className="flex flex-wrap gap-2">
                                            {dimensions.map((dimension) => (
                                                <button
                                                    key={dimension}
                                                    onClick={() => setSelectedDimension(dimension)}
                                                    className={`px-4 py-2 border rounded-md transition-colors ${
                                                        selectedDimension === dimension
                                                            ? 'bg-primary text-white border-primary'
                                                            : 'border-gray-200 hover:border-primary'
                                                    }`}
                                                >
                                                    {dimension}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="flex items-center gap-4 mb-6">
                                <div className="flex border rounded-md">
                                    <button
                                        onClick={() => handleQuantityChange(-1)}
                                        className="w-10 h-10 flex items-center justify-center hover:bg-gray-100"
                                    >
                                        <Minus className="h-4 w-4" />
                                    </button>
                                    <Input
                                        type="number"
                                        value={quantity}
                                        onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                                        className="w-16 text-center border-x-0 rounded-none"
                                        min="1"
                                        max={product.quantity}
                                    />
                                    <button
                                        onClick={() => handleQuantityChange(1)}
                                        className="w-10 h-10 flex items-center justify-center hover:bg-gray-100"
                                    >
                                        <Plus className="h-4 w-4" />
                                    </button>
                                </div>
                                <Button
                                    className="flex-1 bg-primary hover:bg-tertiary"
                                    onClick={() => handleAddToCart()}
                                    disabled={!product.quantity || product.quantity < quantity}
                                >
                                    <ShoppingCart className="h-5 w-5 mr-2" />
                                    {product.quantity && product.quantity >= quantity
                                        ? __('marketplace.add_to_cart')
                                        : __('marketplace.out_of_stock')
                                    }
                                </Button>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    className="w-12 h-12"
                                    onClick={handleWishlist}
                                >
                                    <Heart
                                        className={`h-5 w-5 ${
                                            isWishlist ? 'fill-current text-secondary' : ''
                                        }`}
                                    />
                                </Button>
                            </div>

                            <div className="mb-6">
                                {product.quantity > 0 ? (
                                    <div className="flex items-center gap-2 text-green-600">
                                        <CheckCircle className="h-5 w-5" />
                                        <span>
                                            {product.quantity > 10
                                                ? __('marketplace.in_stock')
                                                : __('marketplace.low_stock', { count: product.quantity })
                                            }
                                        </span>
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2 text-red-600">
                                        <X className="h-5 w-5" />
                                        <span>{__('marketplace.out_of_stock')}</span>
                                    </div>
                                )}
                            </div>

                            <div className="border-t pt-6">
                                <div className="grid grid-cols-2 gap-4 mb-4">
                                    <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                            <Shield className="h-4 w-4 text-primary" />
                                        </div>
                                        <span className="text-sm">{__('marketplace.warranty_12_months')}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                            <Truck className="h-4 w-4 text-primary" />
                                        </div>
                                        <span className="text-sm">{__('marketplace.nationwide_shipping')}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                            <RefreshCw className="h-4 w-4 text-primary" />
                                        </div>
                                        <span className="text-sm">{__('marketplace.return_30_days')}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                            <Check className="h-4 w-4 text-primary" />
                                        </div>
                                        <span className="text-sm">{__('marketplace.authentic_product')}</span>
                                    </div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-semibold">{__('marketplace.share')}:</span>
                                    <div className="flex gap-2">
                                        <Button variant="outline" size="icon" className="w-8 h-8">
                                            <Facebook className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="icon" className="w-8 h-8">
                                            <Twitter className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="icon" className="w-8 h-8">
                                            <Share2 className="h-4 w-4" />
                                        </Button>
                                        <Button variant="outline" size="icon" className="w-8 h-8">
                                            <Mail className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] mb-8">
                    <Tabs defaultValue="description" className="w-full">
                        <TabsList className="w-full justify-start bg-gray-100 p-0">
                            <TabsTrigger
                                value="description"
                                className="px-8 py-4 data-[state=active]:bg-white data-[state=active]:text-primary"
                            >
                                {__('marketplace.product_description')}
                            </TabsTrigger>
                            <TabsTrigger
                                value="specifications"
                                className="px-8 py-4 data-[state=active]:bg-white data-[state=active]:text-primary"
                            >
                                {__('marketplace.specifications')}
                            </TabsTrigger>
                            <TabsTrigger
                                value="reviews"
                                className="px-8 py-4 data-[state=active]:bg-white data-[state=active]:text-primary"
                            >
                                {__('marketplace.customer_reviews')} ({product.review_count || 0})
                            </TabsTrigger>
                            <TabsTrigger
                                value="shipping"
                                className="px-8 py-4 data-[state=active]:bg-white data-[state=active]:text-primary"
                            >
                                {__('marketplace.shipping_returns')}
                            </TabsTrigger>
                        </TabsList>
                        <TabsContent value="description" className="p-8">
                            <h3 className="text-xl font-semibold text-primary mb-4">
                                {__('marketplace.introduction_about', { product: product.name })}
                            </h3>
                            <div className="prose max-w-none">
                                {product.description}
                            </div>
                        </TabsContent>
                        <TabsContent value="specifications" className="p-8">
                            <h3 className="text-xl font-semibold text-primary mb-4">
                                {__('marketplace.technical_specifications')}
                            </h3>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <tbody>
                                        <tr>
                                            <th className="py-3 px-4 text-left font-semibold w-1/3">{__('marketplace.brand')}</th>
                                            <td className="py-3 px-4">{product.brand || '—'}</td>
                                        </tr>
                                        <tr>
                                            <th className="py-3 px-4 text-left font-semibold w-1/3">{__('marketplace.weight')}</th>
                                            <td className="py-3 px-4">{product.weight || '—'}</td>
                                        </tr>
                                        <tr>
                                            <th className="py-3 px-4 text-left font-semibold w-1/3">{__('marketplace.dimensions')}</th>
                                            <td className="py-3 px-4">{product.dimensions || '—'}</td>
                                        </tr>
                                        {product.specifications && JSON.parse(product.specifications).map((spec, index) => (
                                            <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                                                <th className="py-3 px-4 text-left font-semibold w-1/3">
                                                    {spec.name}
                                                </th>
                                                <td className="py-3 px-4">{spec.value}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </TabsContent>
                        <TabsContent value="reviews" className="p-8">
                            <h3 className="text-xl font-semibold text-primary mb-4">
                                {__('marketplace.customer_reviews')}
                            </h3>
                            <div className="flex flex-col lg:flex-row gap-8 mb-8">
                                <div className="lg:w-1/4">
                                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                                        <div className="text-4xl font-bold text-primary mb-2">
                                            {product.rating || 0}
                                        </div>
                                        <div className="flex justify-center text-secondary mb-2">
                                            {[...Array(5)].map((_, i) => (
                                                <Star
                                                    key={i}
                                                    className={`h-5 w-5 ${
                                                        i < Math.floor(product.rating || 0)
                                                            ? 'fill-current'
                                                            : i < (product.rating || 0)
                                                            ? 'fill-current opacity-50'
                                                            : ''
                                                    }`}
                                                />
                                            ))}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {__('marketplace.based_on_reviews', { count: product.review_count || 0 })}
                                        </div>
                                    </div>
                                </div>
                                <div className="lg:w-1/2">
                                    <div className="space-y-4">
                                        {[5, 4, 3, 2, 1].map((rating) => (
                                            <div key={rating} className="flex items-center gap-4">
                                                <div className="w-16 text-sm text-gray-500">
                                                    {rating} sao
                                                </div>
                                                <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
                                                    <div
                                                        className="h-full bg-secondary"
                                                        style={{
                                                            width: `${(product.rating_distribution?.[rating] || 0) / (product.review_count || 1) * 100}%`
                                                        }}
                                                    />
                                                </div>
                                                <div className="w-12 text-sm text-gray-500 text-right">
                                                    {product.rating_distribution?.[rating] || 0}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                <div className="lg:w-1/4">
                                    {canWriteReview() ? (
                                        <Button
                                            className="w-full bg-primary hover:bg-tertiary"
                                            onClick={() => setShowReviewModal(true)}
                                        >
                                            {__('marketplace.write_review')}
                                        </Button>
                                    ) : auth.user ? (
                                        <div className="text-center p-3 bg-gray-100 rounded-md text-sm text-gray-600">
                                            {__('marketplace.already_reviewed_or_no_purchase')}
                                        </div>
                                    ) : (
                                        <Link href="/login" className="block w-full">
                                            <Button className="w-full bg-primary hover:bg-tertiary">
                                                {__('marketplace.login_to_review')}
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            </div>

                            {/* Reviews List */}
                            <div className="space-y-6">
                                {product.reviews_data && product.reviews_data.length > 0 ? (
                                    product.reviews_data.map((review, index) => (
                                        <div key={review.id || index} className="border-b pb-6 last:border-0">
                                            <div className="flex justify-between items-start mb-4">
                                                <div className="flex items-center gap-3">
                                                    <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-primary font-semibold">
                                                        {review.reviewer_name ? review.reviewer_name.charAt(0).toUpperCase() : 'U'}
                                                    </div>
                                                    <div>
                                                        <div className="flex items-center gap-2">
                                                            <div className="font-semibold">{review.reviewer_name || 'Khách hàng'}</div>
                                                            {review.verified_purchase && (
                                                                <CheckCircle className="h-4 w-4 text-green-600" title="Đã mua hàng xác thực" />
                                                            )}
                                                        </div>
                                                        <div className="text-sm text-gray-500">{review.created_at}</div>
                                                    </div>
                                                </div>
                                                <div className="flex text-secondary">
                                                    {[...Array(5)].map((_, i) => (
                                                        <Star
                                                            key={i}
                                                            className={`h-4 w-4 ${
                                                                i < review.rating ? 'fill-current' : ''
                                                            }`}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                            <p className="text-gray-600 mb-4">{review.comment}</p>
                                            <div className="flex items-center gap-4">
                                                {review.verified_purchase && (
                                                    <div className="flex items-center gap-2 text-green-600">
                                                        <CheckCircle className="h-4 w-4" />
                                                        <span className="text-sm">{__('marketplace.verified_purchase')}</span>
                                                    </div>
                                                )}
                                                <div className="flex items-center gap-2">
                                                    <span className="text-sm text-gray-500">
                                                        {__('marketplace.review_helpful')}
                                                    </span>
                                                    <Button
                                                        variant={reviewVotes[review.id] === 'helpful' ? "default" : "outline"}
                                                        size="sm"
                                                        className={`h-8 ${reviewVotes[review.id] === 'helpful' ? 'bg-green-600 hover:bg-green-700 text-white' : ''}`}
                                                        onClick={() => handleVoteClick(review.id, 'helpful')}
                                                    >
                                                        <ThumbsUp className="h-4 w-4 mr-1" />
                                                        {__('marketplace.yes')} ({(reviewCounts[review.id]?.helpful_count ?? review.helpful_count) || 0})
                                                    </Button>
                                                    <Button
                                                        variant={reviewVotes[review.id] === 'unhelpful' ? "default" : "outline"}
                                                        size="sm"
                                                        className={`h-8 ${reviewVotes[review.id] === 'unhelpful' ? 'bg-red-600 hover:bg-red-700 text-white' : ''}`}
                                                        onClick={() => handleVoteClick(review.id, 'unhelpful')}
                                                    >
                                                        <ThumbsDown className="h-4 w-4 mr-1" />
                                                        {__('marketplace.no')} ({(reviewCounts[review.id]?.unhelpful_count ?? review.unhelpful_count) || 0})
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8">
                                        <div className="text-gray-400 mb-4">
                                            <Star className="h-16 w-16 mx-auto" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                            {__('marketplace.no_reviews_yet')}
                                        </h3>
                                        <p className="text-gray-500 mb-4">
                                            {__('marketplace.be_first_to_review')}
                                        </p>
                                        {canWriteReview() ? (
                                            <Button
                                                className="bg-primary hover:bg-tertiary"
                                                onClick={() => setShowReviewModal(true)}
                                            >
                                                {__('marketplace.write_first_review')}
                                            </Button>
                                        ) : auth.user ? (
                                            <div className="p-3 bg-gray-100 rounded-md text-sm text-gray-600">
                                                {__('marketplace.already_reviewed_or_no_purchase')}
                                            </div>
                                        ) : (
                                            <Link href="/login">
                                                <Button className="bg-primary hover:bg-tertiary">
                                                    {__('marketplace.login_to_review')}
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                )}
                            </div>
                        </TabsContent>
                        <TabsContent value="shipping" className="p-8">
                            <h3 className="text-xl font-semibold text-primary mb-4">
                                {__('marketplace.shipping_information')}
                            </h3>
                            <div className="prose max-w-none">
                                <p>
                                    <strong>{__('marketplace.shipping_fee')}:</strong> {__('marketplace.free_shipping_above', { amount: '500.000đ' })} {__('marketplace.for_orders_below')} 500.000đ, {__('marketplace.shipping_fee_calculated_based_on_distance')}
                                </p>
                                <p>
                                    <strong>{__('marketplace.delivery_time')}:</strong> {__('marketplace.1_3_days')} {__('marketplace.for_other_provinces')} 3-5 {__('marketplace.business_days')}
                                </p>
                                <p>
                                    <strong>{__('marketplace.shipping_methods')}:</strong> {__('marketplace.partners')} GHN, GHTK, Viettel Post {__('marketplace.to_ensure_safe_timely_delivery')}
                                </p>

                                <h3 className="text-xl font-semibold text-primary mt-8 mb-4">
                                    {__('marketplace.return_policy')}
                                </h3>
                                <p>
                                    <strong>{__('marketplace.return_period')}:</strong> {__('marketplace.within_30_days')}
                                </p>
                                <p>
                                    <strong>{__('marketplace.return_conditions')}:</strong>
                                </p>
                                <ul>
                                    <li>
                                        {__('marketplace.product_must_be_intact')}
                                    </li>
                                    <li>
                                        {__('marketplace.all_accessories_and_packaging_must_be_included')}
                                    </li>
                                    <li>{__('marketplace.invoice_or_receipt_must_be_included')}</li>
                                </ul>
                                <p>
                                    <strong>{__('marketplace.eligible_return_reasons')}:</strong>
                                </p>
                                <ul>
                                    <li>{__('marketplace.defective_product')}</li>
                                    <li>{__('marketplace.incorrect_product')}</li>
                                    <li>
                                        {__('marketplace.dissatisfaction_with_product')} ({__('marketplace.size_color_style_exchange')})
                                    </li>
                                </ul>
                                <p>
                                    <strong>{__('marketplace.return_process')}:</strong> {__('marketplace.please_contact_our_customer_service')} 1900.6789 {__('marketplace.or_email')} <EMAIL> {__('marketplace.for_detailed_return_instructions')}
                                </p>
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>

                <div className="mb-8">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold text-primary">{__('marketplace.similar_products')}</h2>
                        {product.category && (
                            <Link
                                href={`/marketplace/category/${product.category.slug}`}
                                className="text-tertiary hover:underline flex items-center gap-1"
                            >
                                {__('marketplace.view_all')}
                                <ChevronRight className="h-4 w-4" />
                            </Link>
                        )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {relatedProducts.map((relatedProduct) => (
                            <div
                                key={relatedProduct.id}
                                className="bg-white rounded-lg shadow-[0_3px_10px_rgba(0,0,0,0.1)] overflow-hidden group relative"
                            >
                                <div className="relative h-56 flex items-center justify-center overflow-hidden">
                                    <Link
                                        href={`/marketplace/product/${relatedProduct.slug}`}
                                        className="block w-full h-full flex items-center justify-center"
                                        onClick={() => setIsPageLoading(true)}
                                    >
                                        <ImageWithFallback
                                            src={relatedProduct.image || relatedProduct.image_url_formatted || relatedProduct.image_url}
                                            alt={relatedProduct.name}
                                            fallbackText={relatedProduct.name.charAt(0)}
                                            width="w-full"
                                            height="h-full"
                                            imageClassName="transition-transform duration-300 group-hover:scale-105"
                                            rounded="rounded-none"
                                            bgColor="bg-gray-100"
                                            textColor="text-primary"
                                            textSize="text-2xl"
                                        />
                                    </Link>
                                    {relatedProduct.is_featured && (
                                        <Badge className="absolute top-3 left-3 bg-secondary text-white z-10">
                                            {__('product.bestseller')}
                                        </Badge>
                                    )}
                                    <ProductActionButtons
                                        product={relatedProduct}
                                        onAddToWishlist={handleWishlist}
                                        onAddToCart={handleAddToCart}
                                        __={__}
                                    />
                                </div>
                                <div className="p-4">
                                    <Link
                                        href={`/marketplace/product/${relatedProduct.slug}`}
                                        className="block"
                                        onClick={() => setIsPageLoading(true)}
                                    >
                                        <p className="text-sm text-tertiary mb-1">
                                            {relatedProduct.category?.name}
                                        </p>
                                        <h3 className="font-semibold mb-2 line-clamp-2 hover:text-primary transition-colors">
                                            {relatedProduct.name}
                                        </h3>
                                    </Link>
                                    <div className="flex items-center gap-2 mb-2">
                                        <div className="flex text-secondary">
                                            {[...Array(5)].map((_, i) => (
                                                <Star
                                                    key={i}
                                                    className={`h-4 w-4 ${
                                                        i < Math.floor(relatedProduct.rating || 0)
                                                            ? 'fill-current'
                                                            : i < (relatedProduct.rating || 0)
                                                            ? 'fill-current opacity-50'
                                                            : ''
                                                    }`}
                                                />
                                            ))}
                                        </div>
                                        <span className="text-sm text-gray-500">
                                            ({relatedProduct.review_count || 0})
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-lg font-bold text-primary">
                                            {formatCurrency(relatedProduct.sale_price)}
                                        </span>
                                        {relatedProduct.import_price && Number(relatedProduct.import_price) > Number(relatedProduct.sale_price) && (
                                            <>
                                                <span className="text-sm text-gray-500 line-through">
                                                    {formatCurrency(relatedProduct.import_price)}
                                                </span>
                                                <Badge className="bg-secondary text-white">
                                                    -{Math.round(((relatedProduct.import_price - relatedProduct.sale_price) / relatedProduct.import_price) * 100)}%
                                                </Badge>
                                            </>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <Dialog open={showLightbox} onOpenChange={setShowLightbox}>
                <DialogContent className="max-w-4xl">
                    <div className="relative">
                        <button
                            onClick={() => setShowLightbox(false)}
                            className="absolute top-4 right-4 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors z-10"
                        >
                            <X className="h-5 w-5" />
                        </button>
                        <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                            <div className="min-h-[400px] flex items-center justify-center">
                                <img
                                    src={images[currentImage]}
                                    alt={product.name}
                                    className="w-full h-auto max-h-[80vh] object-contain"
                                    onError={(e) => {
                                        e.target.style.display = 'none';
                                        e.target.nextSibling.style.display = 'flex';
                                    }}
                                />
                                <div
                                    className="hidden w-full h-[400px] bg-gray-200 items-center justify-center text-4xl text-primary font-bold"
                                >
                                    {product.name.charAt(0)}
                                </div>
                            </div>

                            {images.length > 1 && (
                                <>
                                    <button
                                        onClick={handlePrevImage}
                                        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors"
                                    >
                                        <ChevronLeft className="h-5 w-5" />
                                    </button>
                                    <button
                                        onClick={handleNextImage}
                                        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors"
                                    >
                                        <ChevronRight className="h-5 w-5" />
                                    </button>

                                    <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                        {currentImage + 1} / {images.length}
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            <Dialog open={showReviewModal} onOpenChange={setShowReviewModal}>
                <DialogContent className="max-w-2xl">
                    <div className="p-6 border-b">
                        <h3 className="text-lg font-medium text-gray-900">
                            {__('reviews.write_review')}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                            {product.name}
                        </p>
                    </div>

                    <div className="p-6 space-y-4">
                        {/* Rating */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {__('reviews.rating')}
                            </label>
                            <div className="flex items-center space-x-1">
                                {[...Array(5)].map((_, i) => (
                                    <button
                                        key={i}
                                        type="button"
                                        onClick={() => setReviewForm(prev => ({ ...prev, rating: i + 1 }))}
                                        className="focus:outline-none"
                                    >
                                        <Star
                                            className={`h-6 w-6 ${i < reviewForm.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Comment */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {__('reviews.comment')}
                            </label>
                            <textarea
                                value={reviewForm.comment}
                                onChange={(e) => setReviewForm(prev => ({ ...prev, comment: e.target.value }))}
                                rows={4}
                                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                placeholder={__('reviews.comment_placeholder')}
                            />
                        </div>
                    </div>

                    <div className="p-6 border-t bg-gray-50 flex justify-end space-x-3">
                        <Button
                            onClick={() => setShowReviewModal(false)}
                            variant="outline"
                            disabled={isSubmittingReview}
                        >
                            {__('common.cancel')}
                        </Button>
                        <Button
                            onClick={submitReview}
                            disabled={isSubmittingReview}
                            className="bg-primary hover:bg-tertiary"
                        >
                            {isSubmittingReview ? (
                                <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    {__('reviews.submitting')}
                                </>
                            ) : (
                                <>
                                    <MessageSquare className="h-4 w-4 mr-2" />
                                    {__('reviews.submit_review')}
                                </>
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            <Footer />
        </div>
    );
}
