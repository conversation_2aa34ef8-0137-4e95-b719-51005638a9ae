import React, { useState, useEffect } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import MarketplaceHeader from '@/Components/Landing/MarketplaceHeader';
import Footer from '@/Components/Landing/Footer';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import { useToast } from '@/Hooks/useToastContext';
import ProductActionButtons from '@/Components/ProductActionButtons';
import ImageWithFallback from '@/Components/ImageWithFallback';
import Loading from '@/Components/Loading';
import {
    Zap,
    Gift,
    Percent,
    Star,
    ShoppingCart,
    Heart,
    Tag,
    Clock,
    TrendingDown,
    Package,
    Copy,
    Check,
    ArrowRight,
    Flame,
    Award
} from 'lucide-react';

export default function SpecialOffers({
    topCategories = [],
    moreCategories = [],
    discountedProducts = [],
    featuredBundles = [],
    activeCoupons = [],
    flashSaleProducts = [],
    comboDeals = []
}) {
    const { auth } = usePage().props;
    const { addAlert } = useToast();
    const [isPageLoading, setIsPageLoading] = useState(false);
    const [copiedCoupon, setCopiedCoupon] = useState(null);
    const processingProductsRef = React.useRef(new Set());

    const getCsrfToken = () => {
        let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) token = document.querySelector('meta[name="_token"]')?.getAttribute('content');
        if (!token) {
            const form = document.querySelector('form input[name="_token"]');
            if (form) token = form.value;
        }
        if (!token) token = window.csrfToken || window._token;
        return token || '';
    };

    const handleAddToCart = async (productId, message = __('marketplace.added_to_cart'), type = 'success') => {
        if (processingProductsRef.current.has(productId)) {
            return;
        }

        processingProductsRef.current.add(productId);

        try {
            const product = [...discountedProducts, ...flashSaleProducts].find(p => p.id === productId);
            if (!product) {
                addAlert('error', __('marketplace.product_not_found_error'));
                return;
            }

            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    addAlert('error', __('marketplace.csrf_token_missing'));
                    return;
                }

                const response = await fetch('/marketplace/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        product_id: product.id,
                        quantity: 1
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('cartUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                const existingItemIndex = cart.findIndex(item =>
                    (item.product_id && item.product_id === product.id) ||
                    (item.id === product.id && !item.product_id)
                );

                if (existingItemIndex !== -1) {
                    cart[existingItemIndex].quantity += 1;
                    cart[existingItemIndex].subtotal = cart[existingItemIndex].price * cart[existingItemIndex].quantity;
                } else {
                    const cartId = Date.now() + Math.random();
                    cart.push({
                        id: cartId,
                        product_id: product.id,
                        name: product.name,
                        slug: product.slug,
                        image: product.image || product.image_url_formatted || product.image_url,
                        price: product.sale_price,
                        quantity: 1,
                        category: product.category?.name || '',
                        stock_quantity: product.quantity,
                        subtotal: product.sale_price
                    });
                }

                localStorage.setItem('cart', JSON.stringify(cart));
                addAlert('success', __('marketplace.added_to_cart'));
                window.dispatchEvent(new CustomEvent('cartUpdated'));
            }
        } catch (error) {
            console.error('Cart error:', error);
            addAlert('error', __('marketplace.add_to_cart_error'));
        } finally {
            setTimeout(() => {
                processingProductsRef.current.delete(productId);
            }, 500);
        }
    };

    const handleAddToWishlist = async (productId) => {
        try {
            const product = [...discountedProducts, ...flashSaleProducts].find(p => p.id === productId);
            if (!product) {
                addAlert('error', __('marketplace.product_not_found_error'));
                return;
            }

            const isLoggedIn = !!(auth && auth.user);

            if (isLoggedIn) {
                const csrfToken = getCsrfToken();
                const response = await fetch('/marketplace/wishlist/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        product_id: productId
                    })
                });

                const data = await response.json();
                if (data.success) {
                    addAlert('success', data.message);
                    window.dispatchEvent(new CustomEvent('wishlistUpdated'));
                } else {
                    addAlert('error', data.error);
                }
            } else {
                const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
                const existingItem = wishlist.find(item => item.product_id === productId);

                if (!existingItem) {
                    wishlist.push({
                        product_id: productId,
                        name: product.name,
                        slug: product.slug,
                        image: product.image || product.image_url_formatted || product.image_url,
                        price: product.sale_price,
                        category: product.category?.name || ''
                    });

                    localStorage.setItem('wishlist', JSON.stringify(wishlist));
                    addAlert('success', __('marketplace.added_to_wishlist'));
                    window.dispatchEvent(new CustomEvent('wishlistUpdated'));
                } else {
                    addAlert('info', __('marketplace.already_in_wishlist'));
                }
            }
        } catch (error) {
            console.error('Wishlist error:', error);
            addAlert('error', __('marketplace.add_to_wishlist_error'));
        }
    };

    const copyToClipboard = async (text, couponId) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedCoupon(couponId);                            addAlert('success', __('special_offers.coupon_copied'));
            setTimeout(() => setCopiedCoupon(null), 2000);
        } catch (err) {                            addAlert('error', __('special_offers.copy_failed'));
        }
    };

    const renderProductCard = (product) => (
        <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden group hover:shadow-xl transition-all duration-300 relative">
            {product.discount_percentage > 0 && (
                <div className="absolute top-3 left-3 z-10 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                    -{product.discount_percentage}%
                </div>
            )}

            <div className="relative h-48 bg-gray-100 flex items-center justify-center overflow-hidden">
                <Link
                    href={`/marketplace/product/${product.slug}`}
                    className="block w-full h-full"
                    onClick={() => setIsPageLoading(true)}
                >
                    <ImageWithFallback
                        src={product.image || product.image_url_formatted || product.image_url}
                        alt={product.name}
                        fallbackText={product.name.charAt(0)}
                        width="w-full"
                        height="h-full"
                        imageClassName="transition-transform duration-300 group-hover:scale-105"
                        rounded="rounded-none"
                        bgColor="bg-gray-100"
                        textColor="text-red-600"
                        textSize="text-xl"
                    />
                </Link>

                <ProductActionButtons
                    product={product}
                    onAddToWishlist={handleAddToWishlist}
                    onAddToCart={handleAddToCart}
                    __={__}
                />
            </div>

            <div className="p-4">
                <Link
                    href={`/marketplace/product/${product.slug}`}
                    className="block"
                    onClick={() => setIsPageLoading(true)}
                >
                    <p className="text-sm text-tertiary mb-1">{product.category?.name}</p>
                    <h3 className="font-semibold mb-2 line-clamp-2 hover:text-red-600 transition-colors">{product.name}</h3>
                </Link>

                <div className="flex items-center gap-2 mb-2">
                    <div className="flex text-red-500">
                        {[...Array(5)].map((_, i) => (
                            <Star
                                key={i}
                                className={`h-4 w-4 ${i < Math.floor(product.rating || 0) ? 'fill-current' : i < (product.rating || 0) ? 'fill-current opacity-50' : ''}`}
                            />
                        ))}
                    </div>
                    <span className="text-sm text-gray-500">({product.review_count || 0})</span>
                </div>

                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-red-600">
                            {formatCurrency(product.sale_price)}
                        </span>
                        {product.import_price && Number(product.import_price) > Number(product.sale_price) && (
                            <span className="text-sm text-gray-500 line-through">
                                {formatCurrency(product.import_price)}
                            </span>
                        )}
                    </div>
                </div>

                <div className="flex gap-2">
                    <Button
                        size="sm"
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleAddToWishlist(product.id);
                        }}
                        variant="outline"
                        className="flex items-center gap-1 flex-1"
                    >
                        <Heart className="h-4 w-4" />
                    </Button>
                    <Button
                        size="sm"
                        onClick={(e) => handleAddToCart(product.id, e)}
                        className="bg-red-600 hover:bg-red-700 flex items-center gap-1 flex-1"
                    >
                        <ShoppingCart className="h-4 w-4" />
                        {__('product.add_to_cart')}
                    </Button>
                </div>
            </div>
        </div>
    );

    const renderCouponCard = (coupon) => (
        <div key={coupon.id} className="relative bg-white rounded-lg shadow-lg overflow-hidden border-2 border-dashed border-red-200 h-48 min-h-[12rem]">
            {/* Left section - Main coupon */}
            <div className="flex h-full">
                <div className="flex-1 bg-gradient-to-br from-red-600 to-red-700 text-white p-6 relative">
                    {/* Fixed layout structure to prevent height variations */}
                    <div className="h-full flex flex-col">
                        {/* Header - fixed height */}
                        <div className="flex items-center gap-2 h-6 mb-3">
                            <Tag className="h-5 w-5 flex-shrink-0" />
                            <span className="text-sm font-medium uppercase tracking-wide">{__('special_offers.coupon_code')}</span>
                        </div>

                        {/* Coupon code - fixed height */}
                        <div className="text-3xl font-bold font-mono tracking-wider h-10 flex items-center mb-3">
                            {coupon.code}
                        </div>

                        {/* Description - fixed height container with scroll/clamp */}
                        <div className="h-12 mb-4 overflow-hidden">
                            <div className="text-sm opacity-90 line-clamp-3 leading-4">
                                {coupon.description}
                            </div>
                        </div>

                        {/* Discount value - positioned at bottom */}
                        <div className="mt-auto">
                            <div className="text-xl font-bold">
                                {__('special_offers.discount')} {coupon.display_value}
                            </div>
                        </div>
                    </div>

                    {/* Decorative circles for ticket effect */}
                    <div className="absolute -right-3 top-8 w-6 h-6 bg-gray-50 rounded-full"></div>
                    <div className="absolute -right-3 bottom-8 w-6 h-6 bg-gray-50 rounded-full"></div>
                </div>

                {/* Right section - Action button */}
                <div className="w-20 bg-gray-50 flex flex-col items-center justify-center p-4 relative">
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(coupon.code, coupon.id)}
                        className="flex flex-col items-center gap-1 h-auto p-2 hover:bg-red-50 text-red-600"
                    >
                        {copiedCoupon === coupon.id ? (
                            <>
                                <Check className="h-5 w-5" />
                                <span className="text-xs font-medium">Đã copy</span>
                            </>
                        ) : (
                            <>
                                <Copy className="h-5 w-5" />
                                <span className="text-xs font-medium">Sao chép</span>
                            </>
                        )}
                    </Button>

                    {/* Decorative circles for ticket effect */}
                    <div className="absolute -left-3 top-8 w-6 h-6 bg-gray-50 rounded-full border-2 border-dashed border-red-200"></div>
                    <div className="absolute -left-3 bottom-8 w-6 h-6 bg-gray-50 rounded-full border-2 border-dashed border-red-200"></div>
                </div>
            </div>
        </div>
    );

    const renderBundleCard = (bundle) => (
        <div key={bundle.id} className="bg-white rounded-lg shadow-md overflow-hidden group hover:shadow-xl transition-all duration-300 relative">                {bundle.discount_percentage > 0 && (
                <div className="absolute top-3 left-3 z-10 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
                    -{bundle.discount_percentage}% {__('special_offers.combo_save')}
                </div>
            )}

            <div className="relative h-48 bg-gray-100 flex items-center justify-center overflow-hidden">
                <Link
                    href={`/marketplace/bundles/${bundle.slug}`}
                    className="block w-full h-full"
                    onClick={() => setIsPageLoading(true)}
                >
                    <ImageWithFallback
                        src={bundle.image || bundle.image_url}
                        alt={bundle.name}
                        fallbackText={bundle.name.charAt(0)}
                        width="w-full"
                        height="h-full"
                        imageClassName="transition-transform duration-300 group-hover:scale-105"
                        rounded="rounded-none"
                        bgColor="bg-gray-100"
                        textColor="text-red-600"
                        textSize="text-xl"
                    />
                </Link>
            </div>

            <div className="p-4">
                <Link
                    href={`/marketplace/bundles/${bundle.slug}`}
                    className="block"
                    onClick={() => setIsPageLoading(true)}
                >
                    <h3 className="font-semibold mb-2 line-clamp-2 hover:text-red-600 transition-colors">{bundle.name}</h3>
                </Link>

                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{bundle.description}</p>

                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-red-600">
                            {formatCurrency(bundle.total_price)}
                        </span>
                        {bundle.savings_amount > 0 && (
                            <span className="text-sm text-green-600 font-medium">
                                {__('special_offers.save')} {formatCurrency(bundle.savings_amount)}
                            </span>
                        )}
                    </div>
                </div>

                <div className="text-sm text-gray-500 mb-3">
                    {bundle.items.length} {__('special_offers.products_included')}
                </div>

                <Link href={`/marketplace/bundles/${bundle.slug}`}>
                    <Button
                        size="sm"
                        className="w-full bg-red-600 hover:bg-red-700"
                        onClick={() => setIsPageLoading(true)}
                    >
                        <Package className="h-4 w-4 mr-2" />
                        {__('special_offers.view_bundle')}
                    </Button>
                </Link>
            </div>
        </div>
    );

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Head title={`${__('special_offers.special_offers')} - PIBA Marketplace`} />
            {isPageLoading && <Loading fullScreen text={__('common.loading')} />}

            <MarketplaceHeader
                topCategories={topCategories}
                moreCategories={moreCategories}
            />

            {/* Breadcrumb */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center space-x-2 text-sm">
                        <Link href="/marketplace" className="text-gray-500 hover:text-red-600">{__('marketplace.home')}</Link>
                        <span className="text-gray-400">›</span>
                        <span className="text-red-600 font-medium">{__('special_offers.special_offers')}</span>
                    </div>
                </div>
            </div>

            {/* Page Header */}
            <div className="bg-gradient-to-r from-red-600 to-red-800 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center text-white">
                        <h1 className="text-3xl md:text-4xl font-bold mb-2">
                            {__('special_offers.special_offers')}
                        </h1>
                        <p className="text-lg opacity-90">
                            {__('special_offers.page_description')}
                        </p>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

                {/* Active Coupons Section */}
                {activeCoupons.length > 0 && (
                    <section className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <Gift className="h-6 w-6 text-red-600" />
                            <h2 className="text-2xl font-bold text-gray-900">{__('special_offers.coupon_codes')}</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {activeCoupons.map(renderCouponCard)}
                        </div>
                    </section>
                )}

                {/* Flash Sale Section */}
                {flashSaleProducts.length > 0 && (
                    <section className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <Flame className="h-6 w-6 text-red-500" />
                            <h2 className="text-2xl font-bold text-gray-900">{__('special_offers.flash_sale')}</h2>
                            <Badge variant="destructive" className="animate-pulse">
                                {__('special_offers.limited_time')}
                            </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {flashSaleProducts.map(renderProductCard)}
                        </div>
                    </section>
                )}

                {/* Combo Deals Section */}
                {comboDeals.length > 0 && (
                    <section className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <Package className="h-6 w-6 text-red-600" />
                            <h2 className="text-2xl font-bold text-gray-900">{__('special_offers.combo_deals')}</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {comboDeals.map(renderBundleCard)}
                        </div>
                    </section>
                )}

                {/* All Discounted Products Section */}
                {discountedProducts.length > 0 && (
                    <section className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <Percent className="h-6 w-6 text-red-600" />
                            <h2 className="text-2xl font-bold text-gray-900">{__('special_offers.all_discounted_products')}</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {discountedProducts.map(renderProductCard)}
                        </div>
                    </section>
                )}

                {/* Featured Bundle Section */}
                {featuredBundles.length > 0 && (
                    <section className="mb-12">
                        <div className="flex items-center gap-3 mb-6">
                            <Award className="h-6 w-6 text-red-600" />
                            <h2 className="text-2xl font-bold text-gray-900">{__('special_offers.featured_bundles')}</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {featuredBundles.map(renderBundleCard)}
                        </div>
                    </section>
                )}

                {/* Call to Action Section */}
                <section className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-center">
                    <h3 className="text-2xl font-bold text-white mb-4">
                        {__('special_offers.dont_miss_out')}
                    </h3>
                    <p className="text-gray-300 mb-6">
                        {__('special_offers.subscribe_newsletter')}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Link href="/marketplace">
                            <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-gray-900">
                                {__('special_offers.browse_all_products')}
                            </Button>
                        </Link>
                        <Link href="/marketplace/categories">
                            <Button className="bg-red-600 hover:bg-red-700">
                                {__('special_offers.shop_by_category')}
                            </Button>
                        </Link>
                    </div>
                </section>
            </div>

            <Footer />
        </div>
    );
}
