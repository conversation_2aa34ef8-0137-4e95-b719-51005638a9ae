import React from 'react';
import { useForm } from '@inertiajs/react';
import { toast } from 'react-hot-toast';
import { __ } from '@/utils/lang';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import InputError from '@/Components/InputError';

export default function ShippingSettings({ settings }) {
    const { data, setData, post, processing, errors } = useForm({
        ghn: {
            active: settings.ghn?.active || false,
            api_key: settings.ghn?.api_key || '',
            shop_id: settings.ghn?.shop_id || '',
            use_dynamic_pricing: settings.ghn?.use_dynamic_pricing || false,
            environment: settings.ghn?.environment || 'dev'
        }
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.marketplace.settings.update-shipping'), {
            onSuccess: () => {
                toast.success(__('marketplace.settings_updated_success'));
            },
            onError: () => {
                toast.error(__('marketplace.settings_update_error'));
            },
        });
    };

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900">{__('marketplace.shipping_settings')}</h2>
                <p className="mt-1 text-sm text-gray-600">
                    {__('marketplace.shipping_settings_description')}
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
                {/* GHN Shipping Integration */}
                <div className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <i className="fas fa-truck text-blue-500"></i>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">
                                    {__('marketplace.ghn_api_integration')}
                                </h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    {__('marketplace.ghn_api_description')}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h3 className="text-md font-medium text-gray-900">
                                <i className="fas fa-truck mr-2 text-blue-500"></i>
                                {__('marketplace.ghn_shipping_integration')}
                            </h3>
                            <p className="text-sm text-gray-600">{__('marketplace.enable_ghn_shipping_description')}</p>
                        </div>
                        <div className="flex items-center">
                            <label className="relative inline-flex items-center cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={data.ghn.active}
                                    onChange={(e) => setData('ghn', {...data.ghn, active: e.target.checked})}
                                    className="sr-only peer"
                                />
                                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>

                    {data.ghn.active && (
                        <div className="space-y-4">
                            <div>
                                <TextInputWithLabel
                                    label={__('marketplace.api_key') + ' *'}
                                    id="ghn_api_key"
                                    type="password"
                                    value={data.ghn.api_key}
                                    onChange={(e) => setData('ghn', {...data.ghn, api_key: e.target.value})}
                                    placeholder={__('marketplace.enter_ghn_api_key')}
                                    errors={errors['ghn.api_key']}
                                    required={data.ghn.active}
                                />
                                <p className="mt-1 text-xs text-gray-500">
                                    {__('marketplace.api_key_security_note')}
                                </p>
                            </div>

                            <div>
                                <TextInputWithLabel
                                    label={__('marketplace.shop_id') + ' *'}
                                    id="ghn_shop_id"
                                    type="text"
                                    value={data.ghn.shop_id}
                                    onChange={(e) => setData('ghn', {...data.ghn, shop_id: e.target.value})}
                                    placeholder={__('marketplace.enter_ghn_shop_id')}
                                    errors={errors['ghn.shop_id']}
                                    required={data.ghn.active}
                                />
                            </div>

                            <div className="flex items-center">
                                <input
                                    id="use_dynamic_pricing"
                                    type="checkbox"
                                    checked={data.ghn.use_dynamic_pricing}
                                    onChange={(e) => setData('ghn', {...data.ghn, use_dynamic_pricing: e.target.checked})}
                                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                />
                                <label htmlFor="use_dynamic_pricing" className="ml-2 block text-sm text-gray-900">
                                    {__('marketplace.use_dynamic_pricing')}
                                </label>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">{__('marketplace.api_environment')}</label>
                                <div className="flex space-x-4">
                                    <label className="inline-flex items-center">
                                        <input
                                            type="radio"
                                            value="dev"
                                            checked={data.ghn.environment === 'dev'}
                                            onChange={() => setData('ghn', {...data.ghn, environment: 'dev'})}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                        <span className="ml-2 text-sm text-gray-700">{__('marketplace.development')}</span>
                                    </label>
                                    <label className="inline-flex items-center">
                                        <input
                                            type="radio"
                                            value="prod"
                                            checked={data.ghn.environment === 'prod'}
                                            onChange={() => setData('ghn', {...data.ghn, environment: 'prod'})}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                        <span className="ml-2 text-sm text-gray-700">{__('marketplace.production')}</span>
                                    </label>
                                </div>
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                                    </div>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-yellow-800">{__('marketplace.important')}</h3>
                                        <div className="mt-2 text-sm text-yellow-700">
                                            <ul className="list-disc pl-5 space-y-1">
                                                <li>{__('marketplace.api_key_encryption_note')}</li>
                                                <li>{__('marketplace.shop_id_accuracy_note')}</li>
                                                <li>{__('marketplace.production_environment_warning')}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="flex justify-end">
                    <button
                        type="submit"
                        disabled={processing}
                        className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 transition-colors duration-200"
                    >
                        {processing ? __('marketplace.saving') : __('marketplace.save_shipping_settings')}
                    </button>
                </div>
            </form>
        </div>
    );
}
