import React, { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import TextInputWithoutLabel from '@/Components/TextInputWithoutLabel';
import { ArrowLeft, Save } from 'lucide-react';
import SelectWithLabel from '@/Components/SelectWithLabel';

export default function Create() {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        commission_rate: 5.0,
        tier: 'bronze',
        status: 'pending',
        bio: '',
        website_url: '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.affiliate.affiliates.store'), {
            onSuccess: () => {
                reset();
            }
        });
    };

    const handleCancel = () => {
        router.visit(route('superadmin.affiliate.affiliates.index'));
    };

    return (
        <SuperAdminLayout title={__('affiliate.create_affiliate')}>
            <Head title={__('affiliate.create_affiliate')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <SecondaryButton
                    onClick={handleCancel}
                    className="mb-6"
                >
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    {__('affiliate.back_to_list')}
                </SecondaryButton>
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">

                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                {__('affiliate.create_new_affiliate')}
                            </h1>
                            <p className="text-gray-600 mt-1">
                                {__('affiliate.create_affiliate_description')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <div className="bg-white shadow rounded-lg">
                    <form onSubmit={handleSubmit} className="space-y-6 p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Basic Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
                                    {__('affiliate.basic_information')}
                                </h3>

                                <TextInputWithLabel
                                    label={__('affiliate.full_name')}
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    error={errors.name}
                                    required
                                    placeholder={__('affiliate.enter_full_name')}
                                />

                                <TextInputWithLabel
                                    label={__('affiliate.email_address')}
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    error={errors.email}
                                    required
                                    placeholder={__('affiliate.enter_email')}
                                />

                                <TextInputWithLabel
                                    label={__('affiliate.phone_number')}
                                    id="phone"
                                    type="text"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    error={errors.phone}
                                    placeholder={__('affiliate.enter_phone')}
                                />

                                <div>
                                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                                        {__('affiliate.description')}
                                    </label>
                                    <textarea
                                        id="bio"
                                        rows={4}
                                        value={data.bio}
                                        onChange={(e) => setData('bio', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder={__('affiliate.description_placeholder')}
                                    />
                                    {errors.bio && (
                                        <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
                                    )}
                                </div>

                                <TextInputWithLabel
                                    label={__('affiliate.website')}
                                    id="website_url"
                                    type="url"
                                    value={data.website_url}
                                    onChange={(e) => setData('website_url', e.target.value)}
                                    error={errors.website_url}
                                    placeholder={__('affiliate.website_placeholder')}
                                />
                            </div>

                            {/* Affiliate Settings */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
                                    {__('affiliate.affiliate_settings')}
                                </h3>

                                <TextInputWithLabel
                                    label={__('affiliate.commission_rate_label')}
                                    type="number"
                                    id="commission_rate"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    value={data.commission_rate}
                                    onChange={(e) => setData('commission_rate', parseFloat(e.target.value))}
                                    errors={errors.commission_rate}
                                    required
                                />

                                <SelectWithLabel
                                    label={__('affiliate.tier')}
                                    id="tier"
                                    value={data.tier}
                                    onChange={(e) => setData('tier', e.target.value)}
                                    errors={errors.tier}
                                    required
                                >
                                    <option value="bronze">{__('affiliate.bronze')}</option>
                                    <option value="silver">{__('affiliate.silver')}</option>
                                    <option value="gold">{__('affiliate.gold')}</option>
                                    <option value="platinum">{__('affiliate.platinum')}</option>
                                </SelectWithLabel>

                                <SelectWithLabel
                                    label={__('affiliate.status_label')}
                                    id="status"
                                    value={data.status}
                                    onChange={(e) => setData('status', e.target.value)}
                                    errors={errors.status}
                                    required
                                >
                                    <option value="pending">{__('affiliate.pending')}</option>
                                    <option value="active">{__('affiliate.active')}</option>
                                    <option value="inactive">{__('affiliate.inactive')}</option>
                                </SelectWithLabel>

                                {/* Info Box */}
                                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-blue-800">
                                                {__('affiliate.note')}
                                            </h3>
                                            <div className="mt-2 text-sm text-blue-700">
                                                <ul className="list-disc list-inside space-y-1">
                                                    <li>{__('affiliate.default_password_note')}</li>
                                                    <li>{__('affiliate.referral_code_note')}</li>
                                                    <li>{__('affiliate.email_notification_note')}</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Form Actions */}
                        <div className="flex justify-end space-x-4 pt-6 border-t">
                            <SecondaryButton
                                type="button"
                                onClick={handleCancel}
                                disabled={processing}
                            >
                                {__('affiliate.cancel')}
                            </SecondaryButton>
                            <PrimaryButton
                                type="submit"
                                disabled={processing}
                                className="inline-flex items-center"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                {processing ? __('affiliate.creating') : __('affiliate.create_affiliate_button')}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
