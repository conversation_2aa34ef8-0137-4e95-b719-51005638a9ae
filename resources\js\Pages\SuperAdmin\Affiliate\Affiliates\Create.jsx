import React, { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import TextInputWithoutLabel from '@/Components/TextInputWithoutLabel';
import { ArrowLeft, Save } from 'lucide-react';

export default function Create() {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        commission_rate: 5.0,
        tier: 'bronze',
        status: 'pending',
        bio: '',
        website_url: '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.affiliate.affiliates.store'), {
            onSuccess: () => {
                reset();
            }
        });
    };

    const handleCancel = () => {
        router.visit(route('superadmin.affiliate.affiliates.index'));
    };

    return (
        <SuperAdminLayout title="Thêm Affiliate">
            <Head title="Thêm Affiliate" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={handleCancel}
                            className="inline-flex items-center text-gray-600 hover:text-gray-900"
                        >
                            <ArrowLeft className="w-5 h-5 mr-2" />
                            Quay lại
                        </button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                Thêm Affiliate Mới
                            </h1>
                            <p className="text-gray-600 mt-1">
                                Tạo tài khoản affiliate mới trong hệ thống
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <div className="bg-white shadow rounded-lg">
                    <form onSubmit={handleSubmit} className="space-y-6 p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Basic Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
                                    Thông tin cơ bản
                                </h3>
                                
                                <TextInputWithLabel
                                    label="Họ và tên"
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    error={errors.name}
                                    required
                                    placeholder="Nhập họ và tên"
                                />

                                <TextInputWithLabel
                                    label="Email"
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    error={errors.email}
                                    required
                                    placeholder="Nhập địa chỉ email"
                                />

                                <TextInputWithLabel
                                    label="Số điện thoại"
                                    id="phone"
                                    type="text"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    error={errors.phone}
                                    placeholder="Nhập số điện thoại"
                                />

                                <div>
                                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                                        Mô tả
                                    </label>
                                    <textarea
                                        id="bio"
                                        rows={4}
                                        value={data.bio}
                                        onChange={(e) => setData('bio', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder="Mô tả về affiliate..."
                                    />
                                    {errors.bio && (
                                        <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
                                    )}
                                </div>

                                <TextInputWithLabel
                                    label="Website"
                                    id="website_url"
                                    type="url"
                                    value={data.website_url}
                                    onChange={(e) => setData('website_url', e.target.value)}
                                    error={errors.website_url}
                                    placeholder="https://example.com"
                                />
                            </div>

                            {/* Affiliate Settings */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
                                    Cài đặt Affiliate
                                </h3>

                                <div>
                                    <label htmlFor="commission_rate" className="block text-sm font-medium text-gray-700 mb-1">
                                        Tỷ lệ hoa hồng (%)
                                    </label>
                                    <input
                                        type="number"
                                        id="commission_rate"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={data.commission_rate}
                                        onChange={(e) => setData('commission_rate', parseFloat(e.target.value))}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        required
                                    />
                                    {errors.commission_rate && (
                                        <p className="mt-1 text-sm text-red-600">{errors.commission_rate}</p>
                                    )}
                                </div>

                                <div>
                                    <label htmlFor="tier" className="block text-sm font-medium text-gray-700 mb-1">
                                        Tier
                                    </label>
                                    <select
                                        id="tier"
                                        value={data.tier}
                                        onChange={(e) => setData('tier', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        required
                                    >
                                        <option value="bronze">Bronze</option>
                                        <option value="silver">Silver</option>
                                        <option value="gold">Gold</option>
                                        <option value="platinum">Platinum</option>
                                    </select>
                                    {errors.tier && (
                                        <p className="mt-1 text-sm text-red-600">{errors.tier}</p>
                                    )}
                                </div>

                                <div>
                                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                                        Trạng thái
                                    </label>
                                    <select
                                        id="status"
                                        value={data.status}
                                        onChange={(e) => setData('status', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        required
                                    >
                                        <option value="pending">Chờ duyệt</option>
                                        <option value="active">Hoạt động</option>
                                        <option value="inactive">Không hoạt động</option>
                                    </select>
                                    {errors.status && (
                                        <p className="mt-1 text-sm text-red-600">{errors.status}</p>
                                    )}
                                </div>

                                {/* Info Box */}
                                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-blue-800">
                                                Lưu ý
                                            </h3>
                                            <div className="mt-2 text-sm text-blue-700">
                                                <ul className="list-disc list-inside space-y-1">
                                                    <li>Mật khẩu mặc định sẽ là "password"</li>
                                                    <li>Mã giới thiệu sẽ được tự động tạo</li>
                                                    <li>Affiliate sẽ nhận email thông báo sau khi tạo</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Form Actions */}
                        <div className="flex justify-end space-x-4 pt-6 border-t">
                            <SecondaryButton
                                type="button"
                                onClick={handleCancel}
                                disabled={processing}
                            >
                                Hủy
                            </SecondaryButton>
                            <PrimaryButton
                                type="submit"
                                disabled={processing}
                                className="inline-flex items-center"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                {processing ? 'Đang tạo...' : 'Tạo Affiliate'}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
