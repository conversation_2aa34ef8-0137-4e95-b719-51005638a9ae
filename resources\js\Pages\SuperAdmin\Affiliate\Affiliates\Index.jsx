import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatDateTime, formatRelativeTime, formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Eye, Edit, Trash2, ToggleLeft, ToggleRight, Search, Users } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';

export default function Index({ affiliates, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isToggling, setIsToggling] = useState(null);
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleTierFilter = (tier) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: filters.status,
            tier: tier
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: filters.status,
            tier: filters.tier,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const confirmDelete = (affiliateId) => {
        setIsDeleting(affiliateId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteAffiliate = (affiliateId) => {
        setIsLoading(true);
        router.delete(route('superadmin.affiliate.affiliates.destroy', affiliateId), {
            onFinish: () => {
                setIsDeleting(null);
                setIsLoading(false);
            }
        });
    };

    const toggleStatus = (affiliateId) => {
        setIsToggling(affiliateId);
        router.post(route('superadmin.affiliate.affiliates.toggle-status', affiliateId), {}, {
            onFinish: () => setIsToggling(null)
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return 'green';
            case 'inactive':
                return 'gray';
            case 'pending':
                return 'yellow';
            case 'suspended':
                return 'red';
            default:
                return 'gray';
        }
    };

    const getTierColor = (tier) => {
        switch (tier) {
            case 'bronze':
                return 'bg-amber-100 text-amber-800';
            case 'silver':
                return 'bg-gray-100 text-gray-800';
            case 'gold':
                return 'bg-yellow-100 text-yellow-800';
            case 'platinum':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const columns = [
        {
            field: 'name',
            label: __('affiliate.affiliate_name'),
            sortable: true,
            render: (affiliate) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-indigo-600">
                                {affiliate.user?.name?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {affiliate.user?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {affiliate.user?.email}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'referral_code',
            label: __('affiliate.referral_code'),
            sortable: true,
            render: (affiliate) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {affiliate.referral_code}
                </span>
            )
        },
        {
            field: 'status',
            label: __('affiliate.status'),
            sortable: true,
            render: (affiliate) => (
                <StatusBadge status={affiliate.status} color={getStatusColor(affiliate.status)} />
            )
        },
        {
            field: 'tier',
            label: __('affiliate.tier_label'),
            sortable: true,
            render: (affiliate) => (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(affiliate.tier)}`}>
                    {affiliate.tier}
                </span>
            )
        },
        {
            field: 'commission_rate',
            label: __('affiliate.commission_rate'),
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-900">
                    {affiliate.commission_rate}%
                </span>
            )
        },
        {
            field: 'total_earnings',
            label: __('affiliate.total_earnings'),
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(affiliate.total_earnings)}
                </span>
            )
        },
        {
            field: 'conversion_rate',
            label: __('affiliate.conversion_rate_column'),
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-900">
                    {affiliate.conversion_rate}%
                </span>
            )
        },
        {
            field: 'created_at',
            label: __('affiliate.created_date'),
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-500">
                    {formatDateTime(affiliate.created_at)}
                </span>
            )
        },
        {
            field: 'actions',
            label: __('affiliate.actions'),
            render: (affiliate) => (
                <div className="flex items-center space-x-2">
                    <Link
                        href={route('superadmin.affiliate.affiliates.show', affiliate.id)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title={__('affiliate.view_details')}
                    >
                        <Eye className="w-4 h-4" />
                    </Link>
                    <Link
                        href={route('superadmin.affiliate.affiliates.edit', affiliate.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title={__('affiliate.edit')}
                    >
                        <Edit className="w-4 h-4" />
                    </Link>
                    <button
                        onClick={() => toggleStatus(affiliate.id)}
                        disabled={isToggling === affiliate.id}
                        className={`${affiliate.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={affiliate.status === 'active' ? __('affiliate.deactivate') : __('affiliate.activate')}
                    >
                        {affiliate.status === 'active' ? (
                            <ToggleRight className="w-4 h-4" />
                        ) : (
                            <ToggleLeft className="w-4 h-4" />
                        )}
                    </button>
                    <button
                        onClick={() => confirmDelete(affiliate.id)}
                        className="text-red-600 hover:text-red-900"
                        title={__('affiliate.delete')}
                    >
                        <Trash2 className="w-4 h-4" />
                    </button>
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: __('affiliate.all_status') },
        { value: 'active', label: __('affiliate.active') },
        { value: 'inactive', label: __('affiliate.inactive') },
        { value: 'pending', label: __('affiliate.pending') },
        { value: 'suspended', label: __('affiliate.suspended') }
    ];

    const tierOptions = [
        { value: '', label: __('affiliate.all_tiers') },
        { value: 'bronze', label: __('affiliate.bronze') },
        { value: 'silver', label: __('affiliate.silver') },
        { value: 'gold', label: __('affiliate.gold') },
        { value: 'platinum', label: __('affiliate.platinum') }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.manage_affiliates')}>
            <Head title={__('affiliate.manage_affiliates')} />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <Users className="w-6 h-6 mr-3" />
                            {__('affiliate.manage_affiliates')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.manage_affiliates_description')}
                        </p>
                    </div>
                    <Link
                        href={route('superadmin.affiliate.affiliates.create')}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                    >
                        <Plus className="w-4 h-4 mr-2" />
                        {__('affiliate.add_affiliate')}
                    </Link>
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <TextInputWithLabel
                            label={__('affiliate.search_button')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
                            placeholder={__('affiliate.search_placeholder')}
                            icon={<Search className="w-4 h-4" />}
                        />
                    </div>

                    <SelectWithLabel
                        label={__('affiliate.status')}
                        value={filters.status || ''}
                        onChange={(e) => handleStatusFilter(e.target.value)}
                    >
                        {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <SelectWithLabel
                        label={__('affiliate.tier_label')}
                        value={filters.tier || ''}
                        onChange={(e) => handleTierFilter(e.target.value)}
                    >
                        {tierOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <div>
                        <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                            {__('affiliate.search_button')}
                        </label>
                        <PrimaryButton
                            onClick={handleSearch}
                            className="w-full h-10"
                        >
                            <Search className="w-4 h-4 mr-2" />
                            {__('affiliate.search_button')}
                        </PrimaryButton>
                    </div>
                </div>

                <DataTable
                    data={affiliates}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Delete Confirmation Modal */}
            <ConfirmDeleteModal
                isOpen={isDeleting !== null}
                onClose={cancelDelete}
                onConfirm={() => deleteAffiliate(isDeleting)}
                itemType="affiliate"
                isProcessing={isLoading}
            />
        </SuperAdminLayout>
    );
}
