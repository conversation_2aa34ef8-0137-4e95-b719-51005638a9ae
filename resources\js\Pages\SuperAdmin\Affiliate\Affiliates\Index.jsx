import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatDateTime, formatRelativeTime, formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Eye, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TableFilterHeader from '@/Components/TableFilterHeader';

export default function Index({ affiliates, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isToggling, setIsToggling] = useState(null);
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleTierFilter = (tier) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: filters.status,
            tier: tier
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.affiliates.index'), {
            search: filters.search,
            status: filters.status,
            tier: filters.tier,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const confirmDelete = (affiliateId) => {
        setIsDeleting(affiliateId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteAffiliate = (affiliateId) => {
        setIsLoading(true);
        router.delete(route('superadmin.affiliate.affiliates.destroy', affiliateId), {
            onFinish: () => {
                setIsDeleting(null);
                setIsLoading(false);
            }
        });
    };

    const toggleStatus = (affiliateId) => {
        setIsToggling(affiliateId);
        router.post(route('superadmin.affiliate.affiliates.toggle-status', affiliateId), {}, {
            onFinish: () => setIsToggling(null)
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return 'green';
            case 'inactive':
                return 'gray';
            case 'pending':
                return 'yellow';
            case 'suspended':
                return 'red';
            default:
                return 'gray';
        }
    };

    const getTierColor = (tier) => {
        switch (tier) {
            case 'bronze':
                return 'bg-amber-100 text-amber-800';
            case 'silver':
                return 'bg-gray-100 text-gray-800';
            case 'gold':
                return 'bg-yellow-100 text-yellow-800';
            case 'platinum':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const columns = [
        {
            field: 'name',
            label: 'Affiliate',
            sortable: true,
            render: (affiliate) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-indigo-600">
                                {affiliate.user?.name?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {affiliate.user?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {affiliate.user?.email}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'referral_code',
            label: 'Mã giới thiệu',
            sortable: true,
            render: (affiliate) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {affiliate.referral_code}
                </span>
            )
        },
        {
            field: 'status',
            label: 'Trạng thái',
            sortable: true,
            render: (affiliate) => (
                <StatusBadge status={affiliate.status} color={getStatusColor(affiliate.status)} />
            )
        },
        {
            field: 'tier',
            label: 'Tier',
            sortable: true,
            render: (affiliate) => (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(affiliate.tier)}`}>
                    {affiliate.tier}
                </span>
            )
        },
        {
            field: 'commission_rate',
            label: 'Hoa hồng (%)',
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-900">
                    {affiliate.commission_rate}%
                </span>
            )
        },
        {
            field: 'total_earnings',
            label: 'Tổng thu nhập',
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(affiliate.total_earnings)}
                </span>
            )
        },
        {
            field: 'conversion_rate',
            label: 'Tỷ lệ chuyển đổi',
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-900">
                    {affiliate.conversion_rate}%
                </span>
            )
        },
        {
            field: 'created_at',
            label: 'Ngày tạo',
            sortable: true,
            render: (affiliate) => (
                <span className="text-sm text-gray-500">
                    {formatDateTime(affiliate.created_at)}
                </span>
            )
        },
        {
            field: 'actions',
            label: 'Thao tác',
            render: (affiliate) => (
                <div className="flex items-center space-x-2">
                    <Link
                        href={route('superadmin.affiliate.affiliates.show', affiliate.id)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Xem chi tiết"
                    >
                        <Eye className="w-4 h-4" />
                    </Link>
                    <Link
                        href={route('superadmin.affiliate.affiliates.edit', affiliate.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title="Chỉnh sửa"
                    >
                        <Edit className="w-4 h-4" />
                    </Link>
                    <button
                        onClick={() => toggleStatus(affiliate.id)}
                        disabled={isToggling === affiliate.id}
                        className={`${affiliate.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={affiliate.status === 'active' ? 'Vô hiệu hóa' : 'Kích hoạt'}
                    >
                        {affiliate.status === 'active' ? (
                            <ToggleRight className="w-4 h-4" />
                        ) : (
                            <ToggleLeft className="w-4 h-4" />
                        )}
                    </button>
                    <button
                        onClick={() => confirmDelete(affiliate.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Xóa"
                    >
                        <Trash2 className="w-4 h-4" />
                    </button>
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: 'Tất cả trạng thái' },
        { value: 'active', label: 'Hoạt động' },
        { value: 'inactive', label: 'Không hoạt động' },
        { value: 'pending', label: 'Chờ duyệt' },
        { value: 'suspended', label: 'Tạm khóa' }
    ];

    const tierOptions = [
        { value: '', label: 'Tất cả tier' },
        { value: 'bronze', label: 'Bronze' },
        { value: 'silver', label: 'Silver' },
        { value: 'gold', label: 'Gold' },
        { value: 'platinum', label: 'Platinum' }
    ];

    return (
        <SuperAdminLayout title="Quản lý Affiliate">
            <Head title="Quản lý Affiliate" />
            
            {isLoading && <Loading />}
            
            <div className="space-y-6">
                <TableFilterHeader
                    title="Quản lý Affiliate"
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    onSearch={handleSearch}
                    createRoute="superadmin.affiliate.affiliates.create"
                    createLabel="Thêm Affiliate"
                    filters={[
                        {
                            type: 'select',
                            value: filters.status || '',
                            onChange: handleStatusFilter,
                            options: statusOptions,
                            placeholder: 'Lọc theo trạng thái'
                        },
                        {
                            type: 'select',
                            value: filters.tier || '',
                            onChange: handleTierFilter,
                            options: tierOptions,
                            placeholder: 'Lọc theo tier'
                        }
                    ]}
                />

                <DataTable
                    data={affiliates}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Delete Confirmation Modal */}
            {isDeleting && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3 text-center">
                            <h3 className="text-lg font-medium text-gray-900">Xác nhận xóa</h3>
                            <div className="mt-2 px-7 py-3">
                                <p className="text-sm text-gray-500">
                                    Bạn có chắc chắn muốn xóa affiliate này? Hành động này không thể hoàn tác.
                                </p>
                            </div>
                            <div className="flex justify-center space-x-4 px-4 py-3">
                                <button
                                    onClick={cancelDelete}
                                    className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                >
                                    Hủy
                                </button>
                                <button
                                    onClick={() => deleteAffiliate(isDeleting)}
                                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                >
                                    Xóa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </SuperAdminLayout>
    );
}
