import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { User, Mail, Phone, Globe, DollarSign, TrendingUp, MousePointer, Target, Calendar, ArrowLeft } from 'lucide-react';

export default function Show({ affiliate, stats, recent_activity }) {
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatNumber = (number) => {
        return new Intl.NumberFormat('vi-VN').format(number || 0);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    const getTierColor = (tier) => {
        const colors = {
            bronze: 'bg-amber-100 text-amber-800',
            silver: 'bg-gray-100 text-gray-800',
            gold: 'bg-yellow-100 text-yellow-800',
            platinum: 'bg-purple-100 text-purple-800'
        };
        return colors[tier] || 'bg-gray-100 text-gray-800';
    };

    const getStatusColor = (status) => {
        const colors = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-red-100 text-red-800',
            pending: 'bg-yellow-100 text-yellow-800',
            suspended: 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getActivityIcon = (type) => {
        return type === 'conversion' ? Target : DollarSign;
    };

    return (
        <SuperAdminLayout>
            <Head title={`Affiliate - ${affiliate.user?.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={route('superadmin.affiliate.affiliates.index')}
                            className="text-gray-500 hover:text-gray-700"
                        >
                            <ArrowLeft className="w-5 h-5" />
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                {affiliate.user?.name || 'N/A'}
                            </h1>
                            <p className="text-gray-600">Affiliate Details</p>
                        </div>
                    </div>
                    <div className="flex space-x-2">
                        <Link
                            href={route('superadmin.affiliate.affiliates.edit', affiliate.id)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                        >
                            Edit Affiliate
                        </Link>
                    </div>
                </div>

                {/* Basic Info */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <User className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-500">Full Name</p>
                                    <p className="font-medium">{affiliate.user?.name || 'N/A'}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Mail className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-500">Email</p>
                                    <p className="font-medium">{affiliate.user?.email || 'N/A'}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Phone className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-500">Phone</p>
                                    <p className="font-medium">{affiliate.user?.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                        <div className="space-y-4">
                            <div>
                                <p className="text-sm text-gray-500">Referral Code</p>
                                <p className="font-medium text-lg">{affiliate.referral_code}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Tier</p>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTierColor(affiliate.tier)}`}>
                                    {affiliate.tier}
                                </span>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Status</p>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(affiliate.status)}`}>
                                    {affiliate.status}
                                </span>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Commission Rate</p>
                                <p className="font-medium">{affiliate.commission_rate}%</p>
                            </div>
                        </div>
                    </div>

                    {affiliate.bio && (
                        <div className="mt-6">
                            <p className="text-sm text-gray-500">Bio</p>
                            <p className="mt-1">{affiliate.bio}</p>
                        </div>
                    )}

                    {affiliate.website_url && (
                        <div className="mt-4">
                            <div className="flex items-center space-x-3">
                                <Globe className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-500">Website</p>
                                    <a
                                        href={affiliate.website_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        {affiliate.website_url}
                                    </a>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Performance Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.total_clicks)}</p>
                            </div>
                            <MousePointer className="h-8 w-8 text-blue-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Conversions</p>
                                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.total_conversions)}</p>
                            </div>
                            <Target className="h-8 w-8 text-green-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_earnings)}</p>
                            </div>
                            <DollarSign className="h-8 w-8 text-yellow-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.conversion_rate}%</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-purple-500" />
                        </div>
                    </div>
                </div>

                {/* Balance Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Available Balance</h3>
                        <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.available_balance)}</p>
                        <p className="text-sm text-gray-500 mt-2">Ready for withdrawal</p>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Pending Balance</h3>
                        <p className="text-3xl font-bold text-yellow-600">{formatCurrency(stats.pending_balance)}</p>
                        <p className="text-sm text-gray-500 mt-2">Awaiting approval</p>
                    </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
                    </div>
                    <div className="p-6">
                        {recent_activity && recent_activity.length > 0 ? (
                            <div className="space-y-4">
                                {recent_activity.map((activity, index) => {
                                    const Icon = getActivityIcon(activity.type);
                                    return (
                                        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                                            <div className={`p-2 rounded-full ${activity.type === 'conversion' ? 'bg-green-100' : 'bg-blue-100'}`}>
                                                <Icon className={`w-4 h-4 ${activity.type === 'conversion' ? 'text-green-600' : 'text-blue-600'}`} />
                                            </div>
                                            <div className="flex-1">
                                                <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                                                <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium text-gray-900">{formatCurrency(activity.amount)}</p>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <p className="text-gray-500 text-center py-8">No recent activity found.</p>
                        )}
                    </div>
                </div>

                {/* Dates */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Important Dates</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="flex items-center space-x-3">
                            <Calendar className="w-5 h-5 text-gray-400" />
                            <div>
                                <p className="text-sm text-gray-500">Joined</p>
                                <p className="font-medium">{formatDate(affiliate.created_at)}</p>
                            </div>
                        </div>
                        {affiliate.approved_at && (
                            <div className="flex items-center space-x-3">
                                <Calendar className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-500">Approved</p>
                                    <p className="font-medium">{formatDate(affiliate.approved_at)}</p>
                                </div>
                            </div>
                        )}
                        <div className="flex items-center space-x-3">
                            <Calendar className="w-5 h-5 text-gray-400" />
                            <div>
                                <p className="text-sm text-gray-500">Last Updated</p>
                                <p className="font-medium">{formatDate(affiliate.updated_at)}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
