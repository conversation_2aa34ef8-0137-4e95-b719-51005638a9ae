import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    BarChart3,
    TrendingUp,
    MousePointer,
    ShoppingCart,
    DollarSign,
    Users,
    Filter
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import SelectWithLabel from '@/Components/SelectWithLabel';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import DataTable from '@/Components/DataTable';
import SimpleLineChart from '@/Components/SimpleLineChart';

export default function Analytics({ analytics, filters, affiliates, campaigns }) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [selectedAffiliate, setSelectedAffiliate] = useState(filters.affiliate_id || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');

    const handleFilterChange = () => {
        router.get(route('superadmin.affiliate.analytics'), {
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const statCardsData = [
        {
            title: __('affiliate.total_clicks'),
            value: analytics.overview.total_clicks.toLocaleString(),
            icon: MousePointer,
            color: 'blue',
            trend: null
        },
        {
            title: __('affiliate.total_conversions'),
            value: analytics.overview.total_conversions.toLocaleString(),
            icon: ShoppingCart,
            color: 'green',
            trend: null
        },
        {
            title: __('affiliate.conversion_rate'),
            value: `${analytics.overview.conversion_rate}%`,
            icon: TrendingUp,
            color: 'purple',
            trend: null
        },
        {
            title: __('affiliate.total_revenue'),
            value: formatCurrency(analytics.overview.total_revenue),
            icon: DollarSign,
            color: 'emerald',
            trend: null
        },
        {
            title: __('affiliate.total_commissions'),
            value: formatCurrency(analytics.overview.total_commissions),
            icon: DollarSign,
            color: 'orange',
            trend: null
        },
        {
            title: __('affiliate.aov'),
            value: formatCurrency(analytics.performance_metrics.avg_order_value),
            icon: BarChart3,
            color: 'indigo',
            trend: null
        }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.analytics')}>
            <Head title={__('affiliate.analytics')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <BarChart3 className="w-6 h-6 mr-3" />
                            {__('affiliate.analytics')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.analytics_description')}
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6 mt-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-gray-600" />
                        <h3 className="text-lg font-medium text-gray-900">{__('affiliate.filters')}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.from_date')}
                            value={dateRange.start_date}
                            onChange={(e) => setDateRange({ ...dateRange, start_date: e.target.value })}
                        />

                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.to_date')}
                            value={dateRange.end_date}
                            onChange={(e) => setDateRange({ ...dateRange, end_date: e.target.value })}
                        />

                        <SelectWithLabel
                            label={__('affiliate.affiliate')}
                            value={selectedAffiliate}
                            onChange={(e) => setSelectedAffiliate(e.target.value)}
                        >
                            <option value="">{__('affiliate.all_affiliates')}</option>
                            {affiliates && affiliates.map((affiliate) => (
                                <option key={affiliate.id} value={affiliate.id}>
                                    {affiliate.user.name} ({affiliate.referral_code})
                                </option>
                            ))}
                        </SelectWithLabel>

                        <SelectWithLabel
                            label={__('affiliate.campaign')}
                            value={selectedCampaign}
                            onChange={(e) => setSelectedCampaign(e.target.value)}
                        >
                            <option value="">{__('affiliate.all_campaigns')}</option>
                            {campaigns && campaigns.map((campaign) => (
                                <option key={campaign.id} value={campaign.id}>
                                    {campaign.name}
                                </option>
                            ))}
                        </SelectWithLabel>

                        <div>
                            <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                                {__('affiliate.apply_filters')}
                            </label>
                            <PrimaryButton
                                onClick={handleFilterChange}
                                className="w-full h-10"
                            >
                                {__('affiliate.apply_filters')}
                            </PrimaryButton>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6 mb-6">
                    {statCardsData.map((stat, index) => {
                        const IconComponent = stat.icon;
                        const colorClasses = {
                            blue: { bg: 'bg-blue-50', icon: 'text-blue-600', text: 'text-blue-900' },
                            green: { bg: 'bg-green-50', icon: 'text-green-600', text: 'text-green-900' },
                            purple: { bg: 'bg-purple-50', icon: 'text-purple-600', text: 'text-purple-900' },
                            emerald: { bg: 'bg-emerald-50', icon: 'text-emerald-600', text: 'text-emerald-900' },
                            orange: { bg: 'bg-orange-50', icon: 'text-orange-600', text: 'text-orange-900' },
                            indigo: { bg: 'bg-indigo-50', icon: 'text-indigo-600', text: 'text-indigo-900' },
                        };
                        const colors = colorClasses[stat.color] || colorClasses.blue;

                        return (
                            <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <p className="text-sm font-medium text-gray-600 truncate">
                                            {stat.title}
                                        </p>
                                        <p className={`text-2xl font-bold ${colors.text} mt-1`}>
                                            {stat.value}
                                        </p>
                                    </div>
                                    <div className={`${colors.bg} p-3 rounded-lg`}>
                                        <IconComponent className={`w-6 h-6 ${colors.icon}`} />
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Charts Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Clicks Over Time Chart */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.clicks_over_time')}
                        </h3>
                        <div className="h-64">
                            {analytics.charts.clicks_over_time && analytics.charts.clicks_over_time.length > 0 ? (
                                <SimpleLineChart
                                    data={analytics.charts.clicks_over_time}
                                    xKey="date"
                                    yKey="clicks"
                                    color="#3B82F6"
                                    label="Clicks"
                                />
                            ) : (
                                <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg">
                                    <div className="text-center">
                                        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                        <p className="text-gray-500">{__('affiliate.no_click_data')}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Conversions Over Time Chart */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.conversions_over_time')}
                        </h3>
                        <div className="h-64">
                            {analytics.charts.conversions_over_time && analytics.charts.conversions_over_time.length > 0 ? (
                                <SimpleLineChart
                                    data={analytics.charts.conversions_over_time}
                                    xKey="date"
                                    yKey="conversions"
                                    color="#10B981"
                                    label="Conversions"
                                />
                            ) : (
                                <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg">
                                    <div className="text-center">
                                        <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                        <p className="text-gray-500">{__('affiliate.no_conversion_data')}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Revenue Over Time Chart */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.revenue_over_time')}
                        </h3>
                        <div className="h-64">
                            {analytics.charts.revenue_over_time && analytics.charts.revenue_over_time.length > 0 ? (
                                <SimpleLineChart
                                    data={analytics.charts.revenue_over_time}
                                    xKey="date"
                                    yKey="revenue"
                                    color="#F59E0B"
                                    label="Revenue"
                                    formatValue={(value) => formatCurrency(value)}
                                />
                            ) : (
                                <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg">
                                    <div className="text-center">
                                        <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                        <p className="text-gray-500">{__('affiliate.no_revenue_data')}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Top Affiliates */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.top_affiliates')}
                        </h3>
                        <div className="space-y-4">
                            {analytics.charts.top_affiliates && analytics.charts.top_affiliates.length > 0 ? (
                                analytics.charts.top_affiliates.map((affiliate, index) => (
                                    <div key={affiliate.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="flex-shrink-0">
                                                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                                    <span className="text-sm font-medium text-indigo-600">
                                                        #{index + 1}
                                                    </span>
                                                </div>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {affiliate.name}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {affiliate.email}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {__('affiliate.tier_label')}: {affiliate.tier}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(affiliate.revenue)}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {affiliate.clicks} {__('affiliate.clicks_text')} • {affiliate.conversions} {__('affiliate.conversions_text')}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {__('affiliate.commission')}: {formatCurrency(affiliate.commissions)}
                                            </p>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                    <p className="text-gray-500">{__('affiliate.no_affiliate_data')}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Performance Metrics */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">
                        {__('affiliate.performance_metrics')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                            <div className="text-3xl font-bold text-indigo-600">
                                {formatCurrency(analytics.performance_metrics.avg_order_value)}
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                                {__('affiliate.avg_order_value')}
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="text-3xl font-bold text-green-600">
                                {formatCurrency(analytics.performance_metrics.customer_lifetime_value)}
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                                {__('affiliate.customer_lifetime_value')}
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="text-3xl font-bold text-purple-600">
                                {analytics.performance_metrics.return_on_ad_spend}x
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                                {__('affiliate.return_on_ad_spend')}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Top Campaigns */}
                <DataTable
                    title={__('affiliate.top_campaigns')}
                    icon={BarChart3}
                    columns={[
                        {
                            field: 'name',
                            label: __('affiliate.campaign_name'),
                            render: (campaign) => (
                                <div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {campaign.name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {campaign.code}
                                    </div>
                                </div>
                            )
                        },
                        {
                            field: 'clicks',
                            label: __('affiliate.clicks'),
                            render: (campaign) => (
                                <span className="text-sm text-gray-900">
                                    {campaign.clicks?.toLocaleString()}
                                </span>
                            )
                        },
                        {
                            field: 'conversions',
                            label: __('affiliate.conversions'),
                            render: (campaign) => (
                                <span className="text-sm text-gray-900">
                                    {campaign.conversions?.toLocaleString()}
                                </span>
                            )
                        },
                        {
                            field: 'conversion_rate',
                            label: __('affiliate.conversion_rate'),
                            render: (campaign) => (
                                <span className="text-sm text-gray-900">
                                    {campaign.conversion_rate}%
                                </span>
                            )
                        },
                        {
                            field: 'revenue',
                            label: __('affiliate.revenue'),
                            render: (campaign) => (
                                <span className="text-sm text-gray-900">
                                    {formatCurrency(campaign.revenue)}
                                </span>
                            )
                        }
                    ]}
                    data={analytics.charts.top_campaigns || []}
                    emptyStateMessage={__('affiliate.no_campaign_data')}
                    enableDefaultActions={false}
                />
            </div>
        </SuperAdminLayout>
    );
}
