import React, { useState } from 'react';
import { Head, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import Modal from '@/Components/Modal';
import { __ } from '@/utils/lang';
import { formatDateTime } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import {
    UserPlus,
    Check,
    X,
    Eye,
    Clock,
    User,
    Mail,
    Phone,
    Globe,
    Search,
    FileText
} from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import { useToast } from '@/Hooks/useToastContext';

export default function Index({ applications, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isLoading, setIsLoading] = useState(false);
    const [actionModal, setActionModal] = useState(null);
    const [viewModal, setViewModal] = useState(null);
    const [rejectionReason, setRejectionReason] = useState('');
    const { processing } = usePage().props;
    const { addAlert } = useToast();

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.applications.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.applications.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.applications.index'), {
            search: filters.search,
            status: filters.status,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const openActionModal = (action, applicationId) => {
        setActionModal({ action, applicationId });
        setRejectionReason('');
    };

    const closeActionModal = () => {
        setActionModal(null);
        setRejectionReason('');
    };

    const openViewModal = (application) => {
        setViewModal(application);
    };

    const closeViewModal = () => {
        setViewModal(null);
    };

    const handleApprove = (applicationId) => {
        setIsLoading(true);
        router.post(route('superadmin.affiliate.applications.approve', applicationId), {}, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
                addAlert('success', __('affiliate.application_approved'));
            }
        });
    };

    const handleReject = (applicationId) => {
        if (!rejectionReason.trim()) {
            alert(__('affiliate.enter_rejection_reason'));
            return;
        }

        setIsLoading(true);
        router.post(route('superadmin.affiliate.applications.reject', applicationId), {
            reason: rejectionReason
        }, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
                addAlert('success', __('affiliate.application_rejected'));
            }
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'approved':
                return 'green';
            case 'pending':
                return 'yellow';
            case 'rejected':
                return 'red';
            default:
                return 'gray';
        }
    };

    const columns = [
        {
            field: 'applicant',
            label: __('affiliate.applicant'),
            render: (application) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <User className="w-5 h-5 text-indigo-600" />
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {application.user?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="w-3 h-3 mr-1" />
                            {application.user?.email || 'N/A'}
                        </div>
                        {application.user?.phone && (
                            <div className="text-sm text-gray-500 flex items-center">
                                <Phone className="w-3 h-3 mr-1" />
                                {application.user.phone}
                            </div>
                        )}
                    </div>
                </div>
            )
        },
        {
            field: 'website_url',
            label: __('affiliate.website'),
            render: (application) => (
                <div className="text-sm">
                    {application.website_url ? (
                        <a
                            href={application.website_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                            <Globe className="w-3 h-3 mr-1" />
                            {new URL(application.website_url).hostname}
                        </a>
                    ) : (
                        <span className="text-gray-500">-</span>
                    )}
                </div>
            )
        },
        {
            field: 'bio',
            label: __('affiliate.description_column'),
            render: (application) => (
                <div className="text-sm text-gray-900 max-w-xs">
                    <p className="truncate" title={application.bio}>
                        {application.bio || '-'}
                    </p>
                </div>
            )
        },
        {
            field: 'social_profiles',
            label: __('affiliate.social_networks'),
            render: (application) => (
                <div className="text-sm">
                    {application.social_profiles && Object.keys(application.social_profiles).length > 0 ? (
                        <div className="space-y-1">
                            {Object.entries(application.social_profiles).map(([platform, url]) => (
                                <div key={platform} className="flex items-center">
                                    <span className="text-gray-500 capitalize text-xs">{platform}:</span>
                                    <a
                                        href={url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-900 ml-1 text-xs truncate max-w-20"
                                        title={url}
                                    >
                                        Link
                                    </a>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <span className="text-gray-500">-</span>
                    )}
                </div>
            )
        },
        {
            field: 'status',
            label: __('affiliate.status'),
            sortable: true,
            render: (application) => (
                <StatusBadge status={application.status} />
            )
        },
        {
            field: 'created_at',
            label: __('affiliate.applied_date'),
            sortable: true,
            render: (application) => (
                <div className="text-sm">
                    <div className="text-gray-900 flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDateTime(application.created_at)}
                    </div>
                </div>
            )
        },
        {
            field: 'rejection_reason',
            label: __('affiliate.rejection_reason'),
            render: (application) => (
                <div className="text-sm text-gray-900 max-w-xs">
                    {application.rejection_reason ? (
                        <p className="truncate" title={application.rejection_reason}>
                            {application.rejection_reason}
                        </p>
                    ) : (
                        <span className="text-gray-500">-</span>
                    )}
                </div>
            )
        },
        {
            field: 'actions',
            label: __('affiliate.actions'),
            render: (application) => (
                <div className="flex items-center space-x-2">
                    {application.status === 'pending' && (
                        <>
                            <button
                                onClick={() => openActionModal('approve', application.id)}
                                className="text-green-600 hover:text-green-900"
                                title={__('affiliate.approve_application')}
                            >
                                <Check className="w-4 h-4" />
                            </button>
                            <button
                                onClick={() => openActionModal('reject', application.id)}
                                className="text-red-600 hover:text-red-900"
                                title={__('affiliate.reject_application')}
                            >
                                <X className="w-4 h-4" />
                            </button>
                        </>
                    )}
                    <button
                        onClick={() => openViewModal(application)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title={__('affiliate.view_details')}
                    >
                        <Eye className="w-4 h-4" />
                    </button>
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: 'all', label: __('affiliate.all_status') },
        { value: 'pending', label: __('affiliate.pending') },
        { value: 'approved', label: __('affiliate.approved') },
        { value: 'rejected', label: __('affiliate.rejected') }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.applications_management')}>
            <Head title={__('affiliate.applications_management')} />


            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <FileText className="w-6 h-6 mr-3" />
                            {__('affiliate.applications_management')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.applications_description')}
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <TextInputWithLabel
                            label={__('affiliate.search_button')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
                            placeholder={__('affiliate.search_placeholder')}
                            icon={<Search className="w-4 h-4" />}
                        />
                    </div>

                    <SelectWithLabel
                        label={__('affiliate.status')}
                        value={filters.status || ''}
                        onChange={(e) => handleStatusFilter(e.target.value)}
                    >
                        {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <div>
                        <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                            {__('affiliate.search_button')}
                        </label>
                        <PrimaryButton
                            onClick={handleSearch}
                            className="w-full h-10"
                        >
                            <Search className="w-4 h-4 mr-2" />
                            {__('affiliate.search_button')}
                        </PrimaryButton>
                    </div>
                </div>

                <DataTable
                    data={applications}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Action Modal */}
            <Modal
                show={actionModal !== null}
                onClose={closeActionModal}
                maxWidth="md"
                closeable={true}
            >
                {actionModal && (
                    <div className="p-6">
                        {/* Modal Header */}
                        <div className="flex items-center mb-4">
                            {actionModal.action === 'approve' && <Check className="w-6 h-6 text-green-600 mr-3" />}
                            {actionModal.action === 'reject' && <X className="w-6 h-6 text-red-600 mr-3" />}
                            <h3 className="text-lg font-semibold text-gray-900">
                                {actionModal.action === 'approve' && __('affiliate.approve_application_title')}
                                {actionModal.action === 'reject' && __('affiliate.reject_application_title')}
                            </h3>
                        </div>

                        {/* Modal Content */}
                        <div className="mb-6">
                            <p className="text-sm text-gray-600">
                                {actionModal.action === 'approve' && __('affiliate.approve_confirmation')}
                                {actionModal.action === 'reject' && __('affiliate.reject_reason_prompt')}
                            </p>
                        </div>

                        {/* Rejection Reason Textarea */}
                        {actionModal.action === 'reject' && (
                            <TextareaWithLabel
                                id="rejection_reason"
                                label={__('affiliate.rejection_reason')}
                                value={rejectionReason}
                                onChange={(e) => setRejectionReason(e.target.value)}
                                rows={4}
                                placeholder={__('affiliate.reject_reason_placeholder')}
                                required={true}
                                className="mb-0"
                            />
                        )}

                        {/* Modal Actions */}
                        <div className="flex justify-end space-x-3">
                            <SecondaryButton onClick={closeActionModal}>
                                {__('affiliate.cancel')}
                            </SecondaryButton>

                            <PrimaryButton
                                onClick={() => {
                                    if (actionModal.action === 'approve') {
                                        handleApprove(actionModal.applicationId);
                                    } else if (actionModal.action === 'reject') {
                                        handleReject(actionModal.applicationId);
                                    }
                                }}
                                className={actionModal.action === 'approve'
                                    ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                                    : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                                }
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <div className="flex items-center">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        {__('common.processing')}...
                                    </div>
                                ) : (
                                    <>
                                        {actionModal.action === 'approve' && __('affiliate.approve_button')}
                                        {actionModal.action === 'reject' && __('affiliate.reject_button')}
                                    </>
                                )}
                            </PrimaryButton>
                        </div>
                    </div>
                )}
            </Modal>

            {/* View Details Modal */}
            <Modal
                show={viewModal !== null}
                onClose={closeViewModal}
                maxWidth="2xl"
                closeable={true}
            >
                {viewModal && (
                    <div className="p-6">
                        {/* Modal Header */}
                        <div className="flex items-center mb-6">
                            <Eye className="w-6 h-6 text-indigo-600 mr-3" />
                            <h3 className="text-lg font-semibold text-gray-900">
                                {__('affiliate.view_details')}
                            </h3>
                        </div>

                        {/* Application Details */}
                        <div className="space-y-6">
                            {/* Basic Information */}
                            <div>
                                <h4 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">
                                    {__('affiliate.basic_information')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.full_name')}
                                        </label>
                                        <p className="text-sm text-gray-900">{viewModal.name}</p>
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.email_address')}
                                        </label>
                                        <p className="text-sm text-gray-900">{viewModal.email}</p>
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.phone_number')}
                                        </label>
                                        <p className="text-sm text-gray-900">{viewModal.phone || '-'}</p>
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.website')}
                                        </label>
                                        {viewModal.website_url ? (
                                            <a
                                                href={viewModal.website_url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-blue-600 hover:text-blue-900"
                                            >
                                                {viewModal.website_url}
                                            </a>
                                        ) : (
                                            <p className="text-sm text-gray-500">-</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Description */}
                            {viewModal.bio && (
                                <div>
                                    <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                                        {__('affiliate.description')}
                                    </label>
                                    <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">
                                        {viewModal.bio}
                                    </p>
                                </div>
                            )}

                            {/* Social Networks */}
                            {viewModal.social_profiles && Object.keys(viewModal.social_profiles).length > 0 && (
                                <div>
                                    <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
                                        {__('affiliate.social_networks')}
                                    </label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {Object.entries(viewModal.social_profiles).map(([platform, url]) => (
                                            <div key={platform} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                                <span className="text-sm font-medium text-gray-700 capitalize">{platform}</span>
                                                <a
                                                    href={url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:text-blue-900 truncate ml-2"
                                                >
                                                    {url}
                                                </a>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Application Status */}
                            <div>
                                <h4 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">
                                    {__('affiliate.status')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.status')}
                                        </label>
                                        <StatusBadge status={viewModal.status} />
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            {__('affiliate.created_at')}
                                        </label>
                                        <p className="text-sm text-gray-900">{formatDateTime(viewModal.created_at)}</p>
                                    </div>
                                </div>
                            </div>

                            {/* Rejection Reason */}
                            {viewModal.rejection_reason && (
                                <div>
                                    <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                                        {__('affiliate.rejection_reason')}
                                    </label>
                                    <p className="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                                        {viewModal.rejection_reason}
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* Modal Actions */}
                        <div className="flex justify-end mt-6 pt-4 border-t">
                            <SecondaryButton onClick={closeViewModal}>
                                {__('common.close')}
                            </SecondaryButton>
                        </div>
                    </div>
                )}
            </Modal>
        </SuperAdminLayout>
    );
}
