import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatDateTime, formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus, Eye, Edit, Trash2, ToggleLeft, ToggleRight, Calendar, Target } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TableFilterHeader from '@/Components/TableFilterHeader';

export default function Index({ campaigns, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isToggling, setIsToggling] = useState(null);
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleTypeFilter = (type) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: filters.status,
            type: type
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: filters.status,
            type: filters.type,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const confirmDelete = (campaignId) => {
        setIsDeleting(campaignId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteCampaign = (campaignId) => {
        setIsLoading(true);
        router.delete(route('superadmin.affiliate.campaigns.destroy', campaignId), {
            onFinish: () => {
                setIsDeleting(null);
                setIsLoading(false);
            }
        });
    };

    const toggleStatus = (campaignId) => {
        setIsToggling(campaignId);
        router.post(route('superadmin.affiliate.campaigns.toggle-status', campaignId), {}, {
            onFinish: () => setIsToggling(null)
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active':
                return 'green';
            case 'paused':
                return 'yellow';
            case 'completed':
                return 'blue';
            case 'cancelled':
                return 'red';
            case 'draft':
                return 'gray';
            default:
                return 'gray';
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'product':
                return 'bg-blue-100 text-blue-800';
            case 'service':
                return 'bg-green-100 text-green-800';
            case 'event':
                return 'bg-purple-100 text-purple-800';
            case 'general':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const columns = [
        {
            field: 'name',
            label: 'Chiến dịch',
            sortable: true,
            render: (campaign) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-indigo-100 flex items-center justify-center">
                            <Target className="w-5 h-5 text-indigo-600" />
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {campaign.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {campaign.campaign_code}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'type',
            label: 'Loại',
            sortable: true,
            render: (campaign) => (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(campaign.type)}`}>
                    {campaign.type}
                </span>
            )
        },
        {
            field: 'status',
            label: 'Trạng thái',
            sortable: true,
            render: (campaign) => (
                <StatusBadge status={campaign.status} color={getStatusColor(campaign.status)} />
            )
        },
        {
            field: 'commission_rate',
            label: 'Hoa hồng (%)',
            sortable: true,
            render: (campaign) => (
                <span className="text-sm text-gray-900">
                    {campaign.commission_rate}%
                </span>
            )
        },
        {
            field: 'budget',
            label: 'Ngân sách',
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {campaign.budget ? formatCurrency(campaign.budget) : 'Không giới hạn'}
                    </div>
                    {campaign.budget && (
                        <div className="text-gray-500">
                            Đã dùng: {formatCurrency(campaign.spent_budget || 0)}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'current_affiliates',
            label: 'Affiliates',
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {campaign.current_affiliates || 0}
                    </div>
                    {campaign.max_affiliates && (
                        <div className="text-gray-500">
                            / {campaign.max_affiliates}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'performance',
            label: 'Hiệu suất',
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900">
                        {campaign.total_clicks || 0} clicks
                    </div>
                    <div className="text-gray-500">
                        {campaign.total_conversions || 0} conversions
                    </div>
                </div>
            )
        },
        {
            field: 'start_date',
            label: 'Thời gian',
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(campaign.start_date).toLocaleDateString('vi-VN')}
                    </div>
                    {campaign.end_date && (
                        <div className="text-gray-500">
                            đến {new Date(campaign.end_date).toLocaleDateString('vi-VN')}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'actions',
            label: 'Thao tác',
            render: (campaign) => (
                <div className="flex items-center space-x-2">
                    <Link
                        href={route('superadmin.affiliate.campaigns.show', campaign.id)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Xem chi tiết"
                    >
                        <Eye className="w-4 h-4" />
                    </Link>
                    <Link
                        href={route('superadmin.affiliate.campaigns.edit', campaign.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title="Chỉnh sửa"
                    >
                        <Edit className="w-4 h-4" />
                    </Link>
                    <button
                        onClick={() => toggleStatus(campaign.id)}
                        disabled={isToggling === campaign.id}
                        className={`${campaign.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={campaign.status === 'active' ? 'Tạm dừng' : 'Kích hoạt'}
                    >
                        {campaign.status === 'active' ? (
                            <ToggleRight className="w-4 h-4" />
                        ) : (
                            <ToggleLeft className="w-4 h-4" />
                        )}
                    </button>
                    <button
                        onClick={() => confirmDelete(campaign.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Xóa"
                    >
                        <Trash2 className="w-4 h-4" />
                    </button>
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: 'Tất cả trạng thái' },
        { value: 'active', label: 'Hoạt động' },
        { value: 'paused', label: 'Tạm dừng' },
        { value: 'completed', label: 'Hoàn thành' },
        { value: 'cancelled', label: 'Đã hủy' },
        { value: 'draft', label: 'Nháp' }
    ];

    const typeOptions = [
        { value: '', label: 'Tất cả loại' },
        { value: 'product', label: 'Sản phẩm' },
        { value: 'service', label: 'Dịch vụ' },
        { value: 'event', label: 'Sự kiện' },
        { value: 'general', label: 'Tổng quát' }
    ];

    return (
        <SuperAdminLayout title="Quản lý Chiến dịch">
            <Head title="Quản lý Chiến dịch" />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                <TableFilterHeader
                    title="Quản lý Chiến dịch"
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    onSearch={handleSearch}
                    createRoute="superadmin.affiliate.campaigns.create"
                    createLabel="Tạo Chiến dịch"
                    filters={[
                        {
                            type: 'select',
                            value: filters.status || '',
                            onChange: handleStatusFilter,
                            options: statusOptions,
                            placeholder: 'Lọc theo trạng thái'
                        },
                        {
                            type: 'select',
                            value: filters.type || '',
                            onChange: handleTypeFilter,
                            options: typeOptions,
                            placeholder: 'Lọc theo loại'
                        }
                    ]}
                />

                <DataTable
                    data={campaigns}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Delete Confirmation Modal */}
            {isDeleting && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3 text-center">
                            <h3 className="text-lg font-medium text-gray-900">Xác nhận xóa</h3>
                            <div className="mt-2 px-7 py-3">
                                <p className="text-sm text-gray-500">
                                    Bạn có chắc chắn muốn xóa chiến dịch này? Hành động này không thể hoàn tác.
                                </p>
                            </div>
                            <div className="flex justify-center space-x-4 px-4 py-3">
                                <button
                                    onClick={cancelDelete}
                                    className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                >
                                    Hủy
                                </button>
                                <button
                                    onClick={() => deleteCampaign(isDeleting)}
                                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                >
                                    Xóa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </SuperAdminLayout>
    );
}
