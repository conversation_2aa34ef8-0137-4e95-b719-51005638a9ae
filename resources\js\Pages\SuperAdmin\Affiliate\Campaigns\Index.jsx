import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { Plus, Eye, Edit, Trash2, ToggleLeft, ToggleRight, Calendar, Target, Search } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import ConfirmDeleteModal from '@/Components/ConfirmDeleteModal';

export default function Index({ campaigns, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isToggling, setIsToggling] = useState(null);
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleTypeFilter = (type) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: filters.status,
            type: type
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.campaigns.index'), {
            search: filters.search,
            status: filters.status,
            type: filters.type,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const confirmDelete = (campaignId) => {
        setIsDeleting(campaignId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteCampaign = (campaignId) => {
        setIsLoading(true);
        router.delete(route('superadmin.affiliate.campaigns.destroy', campaignId), {
            onFinish: () => {
                setIsDeleting(null);
                setIsLoading(false);
            }
        });
    };

    const toggleStatus = (campaignId) => {
        setIsToggling(campaignId);
        router.post(route('superadmin.affiliate.campaigns.toggle-status', campaignId), {}, {
            onFinish: () => setIsToggling(null)
        });
    };





    const columns = [
        {
            field: 'name',
            label: __('affiliate.campaign_name'),
            sortable: true,
            render: (campaign) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-indigo-100 flex items-center justify-center">
                            <Target className="w-5 h-5 text-indigo-600" />
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {campaign.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {campaign.campaign_code}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'type',
            label: __('affiliate.campaign_type'),
            sortable: true,
            render: (campaign) => (
                <StatusBadge
                    status={campaign.type}
                    customStyles={{
                        product: 'bg-blue-100 text-blue-800',
                        service: 'bg-green-100 text-green-800',
                        event: 'bg-purple-100 text-purple-800',
                        general: 'bg-gray-100 text-gray-800'
                    }}
                    text={__(`affiliate.${campaign.type}`)}
                />
            )
        },
        {
            field: 'status',
            label: __('affiliate.status'),
            sortable: true,
            render: (campaign) => (
                <StatusBadge
                    status={campaign.status}
                    customStyles={{
                        active: 'bg-green-100 text-green-800',
                        paused: 'bg-yellow-100 text-yellow-800',
                        completed: 'bg-blue-100 text-blue-800',
                        cancelled: 'bg-red-100 text-red-800',
                        draft: 'bg-gray-100 text-gray-800'
                    }}
                    text={__(`affiliate.${campaign.status}`)}
                />
            )
        },
        {
            field: 'commission_rate',
            label: __('affiliate.commission_rate_column'),
            sortable: true,
            render: (campaign) => (
                <span className="text-sm text-gray-900">
                    {campaign.commission_rate}%
                </span>
            )
        },
        {
            field: 'budget',
            label: __('affiliate.budget'),
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {campaign.budget ? formatCurrency(campaign.budget) : __('affiliate.unlimited')}
                    </div>
                    {campaign.budget && (
                        <div className="text-gray-500">
                            {__('affiliate.used_budget')}: {formatCurrency(campaign.spent_budget || 0)}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'current_affiliates',
            label: __('affiliate.affiliates_count'),
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {campaign.current_affiliates || 0}
                    </div>
                    {campaign.max_affiliates && (
                        <div className="text-gray-500">
                            / {campaign.max_affiliates}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'performance',
            label: __('affiliate.performance'),
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900">
                        {campaign.total_clicks || 0} {__('affiliate.clicks_text')}
                    </div>
                    <div className="text-gray-500">
                        {campaign.total_conversions || 0} {__('affiliate.conversions_text')}
                    </div>
                </div>
            )
        },
        {
            field: 'start_date',
            label: __('affiliate.time_period'),
            sortable: true,
            render: (campaign) => (
                <div className="text-sm">
                    <div className="text-gray-900 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(campaign.start_date).toLocaleDateString('vi-VN')}
                    </div>
                    {campaign.end_date && (
                        <div className="text-gray-500">
                            đến {new Date(campaign.end_date).toLocaleDateString('vi-VN')}
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'actions',
            label: __('affiliate.actions'),
            render: (campaign) => (
                <div className="flex items-center space-x-2">
                    <Link
                        href={route('superadmin.affiliate.campaigns.show', campaign.id)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title={__('affiliate.view_details')}
                    >
                        <Eye className="w-4 h-4" />
                    </Link>
                    <Link
                        href={route('superadmin.affiliate.campaigns.edit', campaign.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title={__('affiliate.edit')}
                    >
                        <Edit className="w-4 h-4" />
                    </Link>
                    <button
                        onClick={() => toggleStatus(campaign.id)}
                        disabled={isToggling === campaign.id}
                        className={`${campaign.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={campaign.status === 'active' ? __('affiliate.pause') : __('affiliate.resume')}
                    >
                        {campaign.status === 'active' ? (
                            <ToggleRight className="w-4 h-4" />
                        ) : (
                            <ToggleLeft className="w-4 h-4" />
                        )}
                    </button>
                    <button
                        onClick={() => confirmDelete(campaign.id)}
                        className="text-red-600 hover:text-red-900"
                        title={__('affiliate.delete')}
                    >
                        <Trash2 className="w-4 h-4" />
                    </button>
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: __('affiliate.all_status') },
        { value: 'active', label: __('affiliate.active') },
        { value: 'paused', label: __('affiliate.paused') },
        { value: 'completed', label: __('affiliate.completed') },
        { value: 'cancelled', label: __('affiliate.cancelled') },
        { value: 'draft', label: __('affiliate.draft') }
    ];

    const typeOptions = [
        { value: '', label: __('affiliate.all_types') },
        { value: 'product', label: __('affiliate.product') },
        { value: 'service', label: __('affiliate.service') },
        { value: 'event', label: __('affiliate.event') },
        { value: 'general', label: __('affiliate.general') }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.manage_campaigns')}>
            <Head title={__('affiliate.manage_campaigns')} />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">

                            {__('affiliate.manage_campaigns')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.manage_campaigns_description')}
                        </p>
                    </div>
                    <Link
                        href={route('superadmin.affiliate.campaigns.create')}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                    >
                        <Plus className="w-4 h-4 mr-2" />
                        {__('affiliate.create_campaign')}
                    </Link>
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <TextInputWithLabel
                            label={__('affiliate.search_button')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
                            placeholder={__('affiliate.search_placeholder')}
                            icon={<Search className="w-4 h-4" />}
                        />
                    </div>

                    <SelectWithLabel
                        label={__('affiliate.status')}
                        value={filters.status || ''}
                        onChange={(e) => handleStatusFilter(e.target.value)}
                    >
                        {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <SelectWithLabel
                        label={__('affiliate.campaign_type')}
                        value={filters.type || ''}
                        onChange={(e) => handleTypeFilter(e.target.value)}
                    >
                        {typeOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <div>
                        <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                            {__('affiliate.search_button')}
                        </label>
                        <PrimaryButton
                            onClick={handleSearch}
                            className="w-full h-10"
                        >
                            <Search className="w-4 h-4 mr-2" />
                            {__('affiliate.search_button')}
                        </PrimaryButton>
                    </div>
                </div>

                <DataTable
                    data={campaigns}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Delete Confirmation Modal */}
            <ConfirmDeleteModal
                isOpen={isDeleting !== null}
                onClose={cancelDelete}
                onConfirm={() => deleteCampaign(isDeleting)}
                message={__('affiliate.confirm_delete_campaign')}
                isProcessing={isLoading}
            />
        </SuperAdminLayout>
    );
}
