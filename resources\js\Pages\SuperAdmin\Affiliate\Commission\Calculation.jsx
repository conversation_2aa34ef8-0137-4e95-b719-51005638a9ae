import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import DataTable from '@/Components/DataTable';
import StatusBadge from '@/Components/ui/StatusBadge';
import { useToast } from '@/Hooks/useToastContext';
import { __ } from '@/utils/lang';
import { Calculator as CalculatorIcon, DollarSign, TrendingUp, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';


export default function Calculation({
    pending_conversions = { data: [] },
    recent_commissions = [],
    summary = {}
}) {
    const [selectedConversions, setSelectedConversions] = useState([]);
    const { addAlert } = useToast();


    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    const handleSelectConversion = (conversionId) => {
        setSelectedConversions(prev =>
            prev.includes(conversionId)
                ? prev.filter(id => id !== conversionId)
                : [...prev, conversionId]
        );
    };

    const handleCalculateCommissions = () => {
        if (selectedConversions.length === 0) {
            addAlert('warning', __('affiliate.select_at_least_one'));
            return;
        }

        router.post(route('superadmin.affiliate.commission.calculate'), {
            conversion_ids: selectedConversions
        });
    };



    return (
        <SuperAdminLayout>
            <Head title={__('affiliate.commission_calculation')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{__('affiliate.commission_calculation')}</h1>
                        <p className="text-gray-600">{__('affiliate.commission_calculation_description')}</p>
                    </div>
                    <PrimaryButton
                        onClick={handleCalculateCommissions}
                        disabled={selectedConversions.length === 0}
                        className="flex items-center space-x-2"
                    >
                        <CalculatorIcon className="w-4 h-4" />
                        <span>{__('affiliate.calculate_selected')} ({selectedConversions.length})</span>
                    </PrimaryButton>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.pending_conversions')}</p>
                                <p className="text-2xl font-bold text-yellow-900 mt-1">{summary.pending_count || 0}</p>
                            </div>
                            <div className="bg-yellow-50 p-3 rounded-lg">
                                <Clock className="w-6 h-6 text-yellow-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.pending_value')}</p>
                                <p className="text-2xl font-bold text-green-900 mt-1">{formatCurrency(summary.pending_value)}</p>
                            </div>
                            <div className="bg-green-50 p-3 rounded-lg">
                                <DollarSign className="w-6 h-6 text-green-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.calculated_today')}</p>
                                <p className="text-2xl font-bold text-blue-900 mt-1">{summary.calculated_today || 0}</p>
                            </div>
                            <div className="bg-blue-50 p-3 rounded-lg">
                                <TrendingUp className="w-6 h-6 text-blue-600" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Pending Conversions */}
                <div className="mb-4">
                    <DataTable
                        title={__('affiliate.pending_conversions')}
                        icon={Clock}
                        data={pending_conversions.data || []}
                        columns={[
                            {
                                field: 'select',
                                label: (
                                    <input
                                        type="checkbox"
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                setSelectedConversions(pending_conversions.data.map(c => c.id));
                                            } else {
                                                setSelectedConversions([]);
                                            }
                                        }}
                                        className="rounded border-gray-300"
                                    />
                                ),
                                render: (conversion) => (
                                    <input
                                        type="checkbox"
                                        checked={selectedConversions.includes(conversion.id)}
                                        onChange={() => handleSelectConversion(conversion.id)}
                                        className="rounded border-gray-300"
                                    />
                                )
                            },
                            {
                                field: 'id',
                                label: __('affiliate.conversion'),
                                render: (conversion) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            #{conversion.id}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {conversion.order_id}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'affiliate',
                                label: __('affiliate.affiliate'),
                                render: (conversion) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {conversion.affiliate?.user?.name || 'N/A'}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {conversion.affiliate?.user?.email || 'N/A'}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'campaign',
                                label: __('affiliate.campaign'),
                                render: (conversion) => (
                                    <div className="text-sm text-gray-900">
                                        {conversion.campaign?.name || 'N/A'}
                                    </div>
                                )
                            },
                            {
                                field: 'order_value',
                                label: __('affiliate.order_value'),
                                tdClassName: 'text-right',
                                render: (conversion) => formatCurrency(conversion.order_value)
                            },
                            {
                                field: 'commission_rate',
                                label: __('affiliate.commission_rate_text'),
                                tdClassName: 'text-right',
                                render: (conversion) => `${conversion.commission_rate || 0}%`
                            },
                            {
                                field: 'estimated_commission',
                                label: __('affiliate.estimated_commission'),
                                tdClassName: 'text-right',
                                render: (conversion) => formatCurrency((conversion.order_value || 0) * ((conversion.commission_rate || 0) / 100))
                            },
                            {
                                field: 'converted_at',
                                label: __('affiliate.date'),
                                render: (conversion) => formatDate(conversion.converted_at)
                            }
                        ]}
                        emptyStateMessage={__('affiliate.no_pending_conversions')}
                        enableDefaultActions={false}
                    />
                </div>

                {/* Recent Commissions */}
                <div className="mb-4">
                    <DataTable
                        title={__('affiliate.recent_commissions')}
                        icon={DollarSign}
                        data={recent_commissions || []}
                        columns={[
                            {
                                field: 'id',
                                label: __('affiliate.commission'),
                                render: (commission) => (
                                    <div className="text-sm font-medium text-gray-900">
                                        #{commission.id}
                                    </div>
                                )
                            },
                            {
                                field: 'affiliate',
                                label: __('affiliate.affiliate'),
                                render: (commission) => (
                                    <div className="text-sm font-medium text-gray-900">
                                        {commission.affiliate?.user?.name || 'N/A'}
                                    </div>
                                )
                            },
                            {
                                field: 'amount',
                                label: __('affiliate.amount'),
                                tdClassName: 'text-right',
                                render: (commission) => formatCurrency(commission.amount)
                            },
                            {
                                field: 'status',
                                label: __('affiliate.status'),
                                render: (commission) => <StatusBadge status={commission.status} />
                            },
                            {
                                field: 'created_at',
                                label: __('affiliate.date'),
                                render: (commission) => formatDate(commission.created_at)
                            }
                        ]}
                        emptyStateMessage={__('affiliate.no_recent_commissions')}
                        enableDefaultActions={false}
                    />
                </div>
            </div>
        </SuperAdminLayout>
    );
}
