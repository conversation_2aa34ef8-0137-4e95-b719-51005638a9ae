import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import PrimaryButton from '@/Components/PrimaryButton';
import DataTable from '@/Components/DataTable';
import { Calculator as CalculatorIcon, DollarSign, TrendingUp, Users, Clock, CheckCircle, XCircle } from 'lucide-react';

export default function Calculator({ pending_conversions = { data: [] }, recent_commissions = [], stats = {} }) {
    const [selectedConversion, setSelectedConversion] = useState(null);
    const [calculating, setCalculating] = useState(false);
    const [bulkCalculating, setBulkCalculating] = useState(false);

    const handleCalculateCommission = async (conversionId) => {
        setCalculating(true);
        try {
            await router.post(route('superadmin.affiliate.commission.calculate', conversionId));
        } catch (error) {
            console.error('Error calculating commission:', error);
        } finally {
            setCalculating(false);
        }
    };

    const handleBulkCalculate = async () => {
        setBulkCalculating(true);
        try {
            await router.post(route('superadmin.affiliate.commission.bulk-calculate'));
        } catch (error) {
            console.error('Error bulk calculating commissions:', error);
        } finally {
            setBulkCalculating(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'yellow';
            case 'approved': return 'green';
            case 'rejected': return 'red';
            case 'cancelled': return 'gray';
            default: return 'gray';
        }
    };

    return (
        <SuperAdminLayout>
            <Head title="Commission Calculator" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Commission Calculator</h1>
                        <p className="text-gray-600">Calculate and manage affiliate commissions</p>
                    </div>
                    <PrimaryButton
                        onClick={handleBulkCalculate}
                        disabled={bulkCalculating || (pending_conversions.data && pending_conversions.data.length === 0)}
                    >
                        <CalculatorIcon className="w-4 h-4 mr-2" />
                        {bulkCalculating ? 'Calculating...' : 'Bulk Calculate'}
                    </PrimaryButton>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <Clock className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Pending Conversions</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.pending_conversions || 0}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Today's Commissions</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_commissions_today)}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Month's Commissions</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.total_commissions_month)}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Users className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Avg Commission Rate</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.avg_commission_rate ? `${Number(stats.avg_commission_rate).toFixed(2)}%` : '0%'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Pending Conversions */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Pending Conversions</h2>
                        <p className="text-sm text-gray-600">Conversions waiting for commission calculation</p>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Conversion
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Affiliate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Campaign
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Order Value
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {(!pending_conversions.data || pending_conversions.data.length === 0) ? (
                                    <tr>
                                        <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                                            No pending conversions found
                                        </td>
                                    </tr>
                                ) : (
                                    pending_conversions.data.map((conversion) => (
                                        <tr key={conversion.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    #{conversion.id}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {conversion.conversion_type}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {conversion.affiliate?.user?.name || 'N/A'}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {conversion.affiliate?.referral_code}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {conversion.campaign?.name || 'No Campaign'}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {formatCurrency(conversion.order_value)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {formatDate(conversion.created_at)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button
                                                    onClick={() => handleCalculateCommission(conversion.id)}
                                                    disabled={calculating}
                                                    className="text-blue-600 hover:text-blue-900 disabled:text-blue-400"
                                                >
                                                    {calculating ? 'Calculating...' : 'Calculate'}
                                                </button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    {pending_conversions.links && (
                        <div className="px-6 py-4 border-t border-gray-200">
                            <div className="flex justify-between items-center">
                                <div className="text-sm text-gray-700">
                                    Showing {pending_conversions.from || 0} to {pending_conversions.to || 0} of {pending_conversions.total || 0} results
                                </div>
                                <div className="flex space-x-2">
                                    {pending_conversions.links.map((link, index) => (
                                        <button
                                            key={index}
                                            onClick={() => link.url && router.visit(link.url)}
                                            disabled={!link.url}
                                            className={`px-3 py-1 rounded text-sm ${link.active
                                                    ? 'bg-blue-600 text-white'
                                                    : link.url
                                                        ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                }`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Recent Commissions */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Recent Commissions</h2>
                        <p className="text-sm text-gray-600">Recently calculated commissions</p>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Commission
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Affiliate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Rate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {recent_commissions.length === 0 ? (
                                    <tr>
                                        <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                                            No recent commissions found
                                        </td>
                                    </tr>
                                ) : (
                                    recent_commissions.map((commission) => (
                                        <tr key={commission.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    #{commission.id}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {commission.type}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {commission.affiliate?.user?.name || 'N/A'}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {commission.affiliate?.referral_code}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {formatCurrency(commission.amount)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {commission.rate}%
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getStatusColor(commission.status)}-100 text-${getStatusColor(commission.status)}-800`}>
                                                    {commission.status === 'approved' && <CheckCircle className="w-3 h-3 mr-1" />}
                                                    {commission.status === 'rejected' && <XCircle className="w-3 h-3 mr-1" />}
                                                    {commission.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {formatDate(commission.created_at)}
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
