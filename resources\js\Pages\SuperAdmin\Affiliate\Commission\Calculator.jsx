import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import TextareaWithLabel from '@/Components/TextareaWithLabel';
import DataTable from '@/Components/DataTable';
import StatusBadge from '@/Components/ui/StatusBadge';
import { useToast } from '@/Hooks/useToastContext';
import { __ } from '@/utils/lang';
import { Calculator as CalculatorIcon, DollarSign, TrendingUp, Users, Clock, CheckCircle, XCircle } from 'lucide-react';

export default function Calculator({ pending_conversions = { data: [] }, recent_commissions = [], stats = {} }) {
    const [selectedConversion, setSelectedConversion] = useState(null);
    const [calculating, setCalculating] = useState(false);
    const [bulkCalculating, setBulkCalculating] = useState(false);
    const { addAlert } = useToast();

    const handleCalculateCommission = async (conversionId) => {
        setSelectedConversion(conversionId);
        setCalculating(true);
        try {
            await router.post(route('superadmin.affiliate.commission.calculate', conversionId));
        } catch (error) {
            console.error('Error calculating commission:', error);
        } finally {
            setCalculating(false);
            setSelectedConversion(null);
        }
    };

    const handleBulkCalculate = async () => {
        setBulkCalculating(true);
        try {
            await router.post(route('superadmin.affiliate.commission.bulk-calculate'));
        } catch (error) {
            console.error('Error bulk calculating commissions:', error);
        } finally {
            setBulkCalculating(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    return (
        <SuperAdminLayout>
            <Head title={__('affiliate.commission_calculator')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{__('affiliate.commission_calculator')}</h1>
                        <p className="text-gray-600">{__('affiliate.commission_calculator_description')}</p>
                    </div>
                    <PrimaryButton
                        onClick={handleBulkCalculate}
                        disabled={bulkCalculating || (pending_conversions.data && pending_conversions.data.length === 0)}
                        className="flex items-center space-x-2"
                    >
                        <CalculatorIcon className="w-4 h-4" />
                        <span>{bulkCalculating ? __('affiliate.calculating') : __('affiliate.bulk_calculate')}</span>
                    </PrimaryButton>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.pending_conversions')}</p>
                                <p className="text-2xl font-bold text-yellow-900 mt-1">{stats.pending_conversions || 0}</p>
                            </div>
                            <div className="bg-yellow-50 p-3 rounded-lg">
                                <Clock className="w-6 h-6 text-yellow-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.todays_commissions')}</p>
                                <p className="text-2xl font-bold text-green-900 mt-1">{formatCurrency(stats.total_commissions_today)}</p>
                            </div>
                            <div className="bg-green-50 p-3 rounded-lg">
                                <DollarSign className="w-6 h-6 text-green-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.months_commissions')}</p>
                                <p className="text-2xl font-bold text-blue-900 mt-1">{formatCurrency(stats.total_commissions_month)}</p>
                            </div>
                            <div className="bg-blue-50 p-3 rounded-lg">
                                <TrendingUp className="w-6 h-6 text-blue-600" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <p className="text-sm font-medium text-gray-600 truncate">{__('affiliate.avg_commission_rate')}</p>
                                <p className="text-2xl font-bold text-purple-900 mt-1">{stats.avg_commission_rate ? `${Number(stats.avg_commission_rate).toFixed(2)}%` : '0%'}</p>
                            </div>
                            <div className="bg-purple-50 p-3 rounded-lg">
                                <Users className="w-6 h-6 text-purple-600" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Pending Conversions */}
                <div className="mb-4">
                    <DataTable
                        title={__('affiliate.pending_conversions')}
                        icon={Clock}
                        data={pending_conversions.data || []}
                        columns={[
                            {
                                field: 'id',
                                label: __('affiliate.conversion'),
                                render: (conversion) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            #{conversion.id}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {conversion.conversion_type}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'affiliate',
                                label: __('affiliate.affiliate'),
                                render: (conversion) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {conversion.affiliate?.user?.name || 'N/A'}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {conversion.affiliate?.referral_code}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'campaign',
                                label: __('affiliate.campaign'),
                                render: (conversion) => (
                                    <div className="text-sm text-gray-900">
                                        {conversion.campaign?.name || __('affiliate.no_campaign')}
                                    </div>
                                )
                            },
                            {
                                field: 'order_value',
                                label: __('affiliate.order_value'),
                                tdClassName: 'text-right',
                                render: (conversion) => formatCurrency(conversion.order_value)
                            },
                            {
                                field: 'created_at',
                                label: __('affiliate.date'),
                                render: (conversion) => formatDate(conversion.created_at)
                            }
                        ]}
                        actions={[
                            {
                                label: __('affiliate.calculate'),
                                onClick: (conversion) => handleCalculateCommission(conversion.id),
                                className: 'text-blue-600 hover:text-blue-900 disabled:text-blue-400',
                                isLoading: (conversion) => calculating && selectedConversion === conversion.id
                            }
                        ]}
                        emptyStateMessage={__('affiliate.no_pending_conversions_found')}
                    />

                    {/* Pagination */}
                    {pending_conversions.links && (
                        <div className="px-6 py-4 border-t border-gray-200">
                            <div className="flex justify-between items-center">
                                <div className="text-sm text-gray-700">
                                    {__('affiliate.showing_results', {
                                        from: pending_conversions.from || 0,
                                        to: pending_conversions.to || 0,
                                        total: pending_conversions.total || 0
                                    })}
                                </div>
                                <div className="flex space-x-2">
                                    {pending_conversions.links.map((link, index) => (
                                        <button
                                            key={index}
                                            onClick={() => link.url && router.visit(link.url)}
                                            disabled={!link.url}
                                            className={`px-3 py-1 rounded text-sm ${link.active
                                                ? 'bg-blue-600 text-white'
                                                : link.url
                                                    ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                }`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Recent Commissions */}
                <div className="mb-4">
                    <DataTable
                        title={__('affiliate.recent_commissions')}
                        icon={DollarSign}
                        data={recent_commissions || []}
                        columns={[
                            {
                                field: 'id',
                                label: __('affiliate.commission'),
                                render: (commission) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            #{commission.id}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {commission.type}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'affiliate',
                                label: __('affiliate.affiliate'),
                                render: (commission) => (
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {commission.affiliate?.user?.name || 'N/A'}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {commission.affiliate?.referral_code}
                                        </div>
                                    </div>
                                )
                            },
                            {
                                field: 'amount',
                                label: __('affiliate.amount'),
                                tdClassName: 'text-right',
                                render: (commission) => formatCurrency(commission.amount)
                            },
                            {
                                field: 'rate',
                                label: __('affiliate.rate'),
                                tdClassName: 'text-right',
                                render: (commission) => `${commission.rate}%`
                            },
                            {
                                field: 'status',
                                label: __('affiliate.status'),
                                render: (commission) => <StatusBadge status={commission.status} />
                            },
                            {
                                field: 'created_at',
                                label: __('affiliate.date'),
                                render: (commission) => formatDate(commission.created_at)
                            }
                        ]}
                        emptyStateMessage={__('affiliate.no_recent_commissions_found')}
                        enableDefaultActions={false}
                    />
                </div>
            </div>
        </SuperAdminLayout>
    );
}
