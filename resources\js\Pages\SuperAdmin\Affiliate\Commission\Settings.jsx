import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import { Save, Settings, Percent, DollarSign, Calendar, CreditCard } from 'lucide-react';

export default function Settings({ settings }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        default_commission_rate: settings.default_commission_rate || 5.0,
        tier_rates: settings.tier_rates || {
            bronze: 3.0,
            silver: 5.0,
            gold: 7.0,
            platinum: 10.0,
        },
        minimum_payout: settings.minimum_payout || 100000,
        payment_schedule: settings.payment_schedule || 'monthly',
        payment_method: settings.payment_method || 'bank_transfer',
        cookie_duration: settings.cookie_duration || 30,
        commission_structure: settings.commission_structure || 'percentage',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('superadmin.affiliate.commission.settings.update'), {
            onSuccess: () => {
                // Handle success
            }
        });
    };

    const handleReset = () => {
        reset();
    };

    return (
        <SuperAdminLayout title={__('affiliate.commission_settings')}>
            <Head title={__('affiliate.commission_settings')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <Settings className="w-6 h-6 mr-3" />
                            {__('affiliate.commission_settings')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.commission_settings_description')}
                        </p>
                    </div>
                </div>

                {/* Form */}
                <div className="bg-white shadow rounded-lg">
                    <form onSubmit={handleSubmit} className="space-y-8 p-6">
                        {/* Commission Rates */}
                        <div className="space-y-6">
                            <div className="flex items-center space-x-2 border-b pb-3">
                                <Percent className="w-5 h-5 text-indigo-600" />
                                <h3 className="text-lg font-medium text-gray-900">
                                    {__('affiliate.commission_rates')}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <TextInputWithLabel
                                    label={__('affiliate.default_commission_rate')}
                                    id="default_commission_rate"
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    value={data.default_commission_rate}
                                    onChange={(e) => setData('default_commission_rate', parseFloat(e.target.value))}
                                    error={errors.default_commission_rate}
                                    required
                                />

                                <SelectWithLabel
                                    label={__('affiliate.commission_structure')}
                                    value={data.commission_structure}
                                    onChange={(e) => setData('commission_structure', e.target.value)}
                                    error={errors.commission_structure}
                                    required
                                >
                                    <option value="percentage">{__('affiliate.percentage')}</option>
                                    <option value="fixed">{__('affiliate.fixed')}</option>
                                    <option value="tiered">{__('affiliate.tiered')}</option>
                                </SelectWithLabel>
                            </div>

                            {/* Tier Rates */}
                            <div className="space-y-4">
                                <h4 className="text-md font-medium text-gray-900">{__('affiliate.tier_rates')}</h4>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    {Object.entries(data.tier_rates).map(([tier, rate]) => (
                                        <div key={tier}>
                                            <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                                                {tier} (%)
                                            </label>
                                            <input
                                                type="number"
                                                min="0"
                                                max="100"
                                                step="0.01"
                                                value={rate}
                                                onChange={(e) => setData('tier_rates', {
                                                    ...data.tier_rates,
                                                    [tier]: parseFloat(e.target.value)
                                                })}
                                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                required
                                            />
                                            {errors[`tier_rates.${tier}`] && (
                                                <p className="mt-1 text-sm text-red-600">{errors[`tier_rates.${tier}`]}</p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Payment Settings */}
                        <div className="space-y-6">
                            <div className="flex items-center space-x-2 border-b pb-3">
                                <DollarSign className="w-5 h-5 text-green-600" />
                                <h3 className="text-lg font-medium text-gray-900">
                                    {__('affiliate.payment_settings')}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <TextInputWithLabel
                                    label={__('affiliate.minimum_payout')}
                                    id="minimum_payout"
                                    type="number"
                                    min="0"
                                    value={data.minimum_payout}
                                    onChange={(e) => setData('minimum_payout', parseInt(e.target.value))}
                                    error={errors.minimum_payout}
                                    required
                                />

                                <SelectWithLabel
                                    label={__('affiliate.payment_schedule')}
                                    value={data.payment_schedule}
                                    onChange={(e) => setData('payment_schedule', e.target.value)}
                                    error={errors.payment_schedule}
                                    required
                                >
                                    <option value="weekly">{__('affiliate.weekly')}</option>
                                    <option value="monthly">{__('affiliate.monthly')}</option>
                                    <option value="quarterly">{__('affiliate.quarterly')}</option>
                                </SelectWithLabel>

                                <SelectWithLabel
                                    label={__('affiliate.default_payment_method')}
                                    value={data.payment_method}
                                    onChange={(e) => setData('payment_method', e.target.value)}
                                    error={errors.payment_method}
                                    required
                                >
                                    <option value="bank_transfer">{__('affiliate.bank_transfer_label')}</option>
                                    <option value="paypal">{__('affiliate.paypal_label')}</option>
                                    <option value="crypto">{__('affiliate.crypto_label')}</option>
                                </SelectWithLabel>
                            </div>
                        </div>

                        {/* Tracking Settings */}
                        <div className="space-y-6">
                            <div className="flex items-center space-x-2 border-b pb-3">
                                <Calendar className="w-5 h-5 text-purple-600" />
                                <h3 className="text-lg font-medium text-gray-900">
                                    {__('affiliate.tracking_settings')}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <TextInputWithLabel
                                    label={__('affiliate.cookie_duration')}
                                    id="cookie_duration"
                                    type="number"
                                    min="1"
                                    max="365"
                                    value={data.cookie_duration}
                                    onChange={(e) => setData('cookie_duration', parseInt(e.target.value))}
                                    error={errors.cookie_duration}
                                    required
                                />
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                <div className="flex">
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-yellow-800">
                                            {__('affiliate.cookie_note')}
                                        </h3>
                                        <div className="mt-2 text-sm text-yellow-700">
                                            <p>
                                                {__('affiliate.cookie_note_description')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Form Actions */}
                        <div className="flex justify-end space-x-4 pt-6 border-t">
                            <SecondaryButton
                                type="button"
                                onClick={handleReset}
                                disabled={processing}
                            >
                                {__('affiliate.reset')}
                            </SecondaryButton>
                            <PrimaryButton
                                type="submit"
                                disabled={processing}
                                className="inline-flex items-center"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                {processing ? __('affiliate.saving') : __('affiliate.save_settings')}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
