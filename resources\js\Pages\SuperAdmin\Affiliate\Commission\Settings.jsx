import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import { __ } from '@/utils/lang';
import { Save, DollarSign, Users, Shield, Clock } from 'lucide-react';

export default function Settings({ settings = {} }) {
    const [formData, setFormData] = useState({
        default_commission_rate: settings.default_commission_rate || 5.0,
        tier_rates: settings.tier_rates || {
            bronze: 3.0,
            silver: 5.0,
            gold: 7.0,
            platinum: 10.0
        },
        minimum_payout: settings.minimum_payout || 100000,
        payment_schedule: settings.payment_schedule || 'monthly',
        payment_method: settings.payment_method || 'bank_transfer',
        cookie_duration: settings.cookie_duration || 30,
        commission_structure: settings.commission_structure || 'percentage',
        auto_approve_affiliates: settings.auto_approve_affiliates || false,
        require_tax_info: settings.require_tax_info || false,
        fraud_detection_enabled: settings.fraud_detection_enabled || true,
        email_notifications_enabled: settings.email_notifications_enabled || true,
        attribution_model: settings.attribution_model || 'last_click',
        max_commission_rate: settings.max_commission_rate || 50.0,
        min_commission_rate: settings.min_commission_rate || 1.0,
    });

    const [saving, setSaving] = useState(false);

    const handleInputChange = (key, value) => {
        if (key.startsWith('tier_rates.')) {
            const tier = key.split('.')[1];
            setFormData(prev => ({
                ...prev,
                tier_rates: {
                    ...prev.tier_rates,
                    [tier]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [key]: value
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        try {
            await router.post(route('superadmin.affiliate.commission.settings.update'), formData);
        } catch (error) {
            console.error('Error updating settings:', error);
        } finally {
            setSaving(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    return (
        <SuperAdminLayout>
            <Head title={__('affiliate.commission_settings')} />

            <div className="space-y-6">
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{__('affiliate.commission_settings')}</h1>
                        <p className="text-gray-600">{__('affiliate.commission_settings_description')}</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-white rounded-lg shadow mb-4">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">{__('affiliate.commission_rates')}</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <TextInputWithLabel
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    label={__('affiliate.default_commission_rate')}
                                    value={formData.default_commission_rate}
                                    onChange={(e) => handleInputChange('default_commission_rate', parseFloat(e.target.value))}
                                />

                                <TextInputWithLabel
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    label={__('affiliate.min_commission_rate')}
                                    value={formData.min_commission_rate}
                                    onChange={(e) => handleInputChange('min_commission_rate', parseFloat(e.target.value))}
                                />

                                <TextInputWithLabel
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    label={__('affiliate.max_commission_rate')}
                                    value={formData.max_commission_rate}
                                    onChange={(e) => handleInputChange('max_commission_rate', parseFloat(e.target.value))}
                                />
                            </div>

                            <div className="mb-4">
                                <h3 className="text-md font-medium text-gray-900 mb-3">{__('affiliate.tier_based_rates')}</h3>
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <TextInputWithLabel
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        label={`${__('affiliate.bronze')} (%)`}
                                        value={formData.tier_rates.bronze}
                                        onChange={(e) => handleInputChange('tier_rates.bronze', parseFloat(e.target.value))}
                                    />

                                    <TextInputWithLabel
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        label={`${__('affiliate.silver')} (%)`}
                                        value={formData.tier_rates.silver}
                                        onChange={(e) => handleInputChange('tier_rates.silver', parseFloat(e.target.value))}
                                    />

                                    <TextInputWithLabel
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        label={`${__('affiliate.gold')} (%)`}
                                        value={formData.tier_rates.gold}
                                        onChange={(e) => handleInputChange('tier_rates.gold', parseFloat(e.target.value))}
                                    />

                                    <TextInputWithLabel
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        label={`${__('affiliate.platinum')} (%)`}
                                        value={formData.tier_rates.platinum}
                                        onChange={(e) => handleInputChange('tier_rates.platinum', parseFloat(e.target.value))}
                                    />
                                </div>
                            </div>

                            <div className="mb-4">
                                <SelectWithLabel
                                    label={__('affiliate.commission_structure')}
                                    value={formData.commission_structure}
                                    onChange={(e) => handleInputChange('commission_structure', e.target.value)}
                                >
                                    <option value="percentage">{__('affiliate.percentage')}</option>
                                    <option value="fixed">{__('affiliate.fixed')}</option>
                                    <option value="tiered">{__('affiliate.tiered')}</option>
                                </SelectWithLabel>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow mb-4">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Users className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">{__('affiliate.payment_settings')}</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <TextInputWithLabel
                                        type="number"
                                        min="0"
                                        label={__('affiliate.minimum_payout')}
                                        value={formData.minimum_payout}
                                        onChange={(e) => handleInputChange('minimum_payout', parseInt(e.target.value))}
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        {__('affiliate.current')}: {formatCurrency(formData.minimum_payout)}
                                    </p>
                                </div>

                                <SelectWithLabel
                                    label={__('affiliate.payment_schedule')}
                                    value={formData.payment_schedule}
                                    onChange={(e) => handleInputChange('payment_schedule', e.target.value)}
                                >
                                    <option value="weekly">{__('affiliate.weekly')}</option>
                                    <option value="monthly">{__('affiliate.monthly')}</option>
                                    <option value="quarterly">{__('affiliate.quarterly')}</option>
                                </SelectWithLabel>

                                <SelectWithLabel
                                    label={__('affiliate.default_payment_method')}
                                    value={formData.payment_method}
                                    onChange={(e) => handleInputChange('payment_method', e.target.value)}
                                >
                                    <option value="bank_transfer">{__('affiliate.bank_transfer_label')}</option>
                                    <option value="paypal">{__('affiliate.paypal_label')}</option>
                                    <option value="momo">{__('affiliate.momo_label')}</option>
                                    <option value="zalopay">{__('affiliate.zalopay_label')}</option>
                                    <option value="vnpay">{__('affiliate.vnpay_label')}</option>
                                </SelectWithLabel>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end mt-6">
                        <PrimaryButton
                            type="submit"
                            disabled={saving}
                            className="flex items-center space-x-2"
                        >
                            <Save className="w-4 h-4" />
                            <span>{saving ? __('affiliate.saving') : __('affiliate.save_settings')}</span>
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
