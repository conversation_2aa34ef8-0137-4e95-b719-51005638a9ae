import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { Save, DollarSign, Users, Shield, Clock } from 'lucide-react';

export default function Settings({ settings = {} }) {
    const [formData, setFormData] = useState({
        default_commission_rate: settings.default_commission_rate || 5.0,
        tier_rates: settings.tier_rates || {
            bronze: 3.0,
            silver: 5.0,
            gold: 7.0,
            platinum: 10.0
        },
        minimum_payout: settings.minimum_payout || 100000,
        payment_schedule: settings.payment_schedule || 'monthly',
        payment_method: settings.payment_method || 'bank_transfer',
        cookie_duration: settings.cookie_duration || 30,
        commission_structure: settings.commission_structure || 'percentage',
        auto_approve_affiliates: settings.auto_approve_affiliates || false,
        require_tax_info: settings.require_tax_info || false,
        fraud_detection_enabled: settings.fraud_detection_enabled || true,
        email_notifications_enabled: settings.email_notifications_enabled || true,
        attribution_model: settings.attribution_model || 'last_click',
        max_commission_rate: settings.max_commission_rate || 50.0,
        min_commission_rate: settings.min_commission_rate || 1.0,
    });

    const [saving, setSaving] = useState(false);

    const handleInputChange = (key, value) => {
        if (key.startsWith('tier_rates.')) {
            const tier = key.split('.')[1];
            setFormData(prev => ({
                ...prev,
                tier_rates: {
                    ...prev.tier_rates,
                    [tier]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [key]: value
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        try {
            await router.post(route('superadmin.affiliate.commission.settings.update'), formData);
        } catch (error) {
            console.error('Error updating settings:', error);
        } finally {
            setSaving(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    return (
        <SuperAdminLayout>
            <Head title="Commission Settings" />

            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Commission Settings</h1>
                        <p className="text-gray-600">Configure affiliate commission and payment settings</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Commission Rates</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Default Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.default_commission_rate}
                                        onChange={(e) => handleInputChange('default_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Min Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.min_commission_rate}
                                        onChange={(e) => handleInputChange('min_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Max Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.max_commission_rate}
                                        onChange={(e) => handleInputChange('max_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                            </div>

                            <div>
                                <h3 className="text-md font-medium text-gray-900 mb-3">Tier-based Rates</h3>
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Bronze (%)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.tier_rates.bronze}
                                            onChange={(e) => handleInputChange('tier_rates.bronze', parseFloat(e.target.value))}
                                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Silver (%)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.tier_rates.silver}
                                            onChange={(e) => handleInputChange('tier_rates.silver', parseFloat(e.target.value))}
                                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Gold (%)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.tier_rates.gold}
                                            onChange={(e) => handleInputChange('tier_rates.gold', parseFloat(e.target.value))}
                                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Platinum (%)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.tier_rates.platinum}
                                            onChange={(e) => handleInputChange('tier_rates.platinum', parseFloat(e.target.value))}
                                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Commission Structure
                                </label>
                                <select
                                    value={formData.commission_structure}
                                    onChange={(e) => handleInputChange('commission_structure', e.target.value)}
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="percentage">Percentage</option>
                                    <option value="fixed">Fixed Amount</option>
                                    <option value="tiered">Tiered</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Users className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Payment Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Minimum Payout (VND)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        value={formData.minimum_payout}
                                        onChange={(e) => handleInputChange('minimum_payout', parseInt(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Current: {formatCurrency(formData.minimum_payout)}
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Payment Schedule
                                    </label>
                                    <select
                                        value={formData.payment_schedule}
                                        onChange={(e) => handleInputChange('payment_schedule', e.target.value)}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="quarterly">Quarterly</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Default Payment Method
                                    </label>
                                    <select
                                        value={formData.payment_method}
                                        onChange={(e) => handleInputChange('payment_method', e.target.value)}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        <option value="bank_transfer">Bank Transfer</option>
                                        <option value="paypal">PayPal</option>
                                        <option value="momo">MoMo</option>
                                        <option value="zalopay">ZaloPay</option>
                                        <option value="vnpay">VNPay</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            disabled={saving}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg flex items-center space-x-2"
                        >
                            <Save className="w-4 h-4" />
                            <span>{saving ? 'Saving...' : 'Save Settings'}</span>
                        </button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
