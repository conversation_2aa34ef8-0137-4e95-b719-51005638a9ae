import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { 
    Users, 
    TrendingUp, 
    DollarSign, 
    MousePointer, 
    ShoppingCart, 
    Percent,
    Activity,
    Clock,
    UserPlus,
    Award
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/utils/formatting';
import StatCards from '@/Components/Dashboard/StatCards';

export default function Dashboard({ stats, recentActivities, topAffiliates, recentRegistrations }) {
    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = () => {
        setRefreshing(true);
        router.reload({
            onFinish: () => setRefreshing(false)
        });
    };

    const statCardsData = [
        {
            title: 'Tổng Affiliate',
            value: stats.total_affiliates,
            icon: Users,
            color: 'blue',
            trend: null
        },
        {
            title: 'Affiliate Hoạt động',
            value: stats.active_affiliates,
            icon: Activity,
            color: 'green',
            trend: null
        },
        {
            title: 'Tổng Hoa hồng',
            value: formatCurrency(stats.total_commissions),
            icon: DollarSign,
            color: 'emerald',
            trend: null
        },
        {
            title: 'Yêu cầu Rút tiền',
            value: stats.pending_withdrawals,
            icon: Clock,
            color: 'yellow',
            trend: null
        },
        {
            title: 'Tổng Clicks',
            value: stats.total_clicks.toLocaleString(),
            icon: MousePointer,
            color: 'purple',
            trend: null
        },
        {
            title: 'Tổng Conversions',
            value: stats.total_conversions.toLocaleString(),
            icon: ShoppingCart,
            color: 'indigo',
            trend: null
        },
        {
            title: 'Tỷ lệ Chuyển đổi',
            value: `${stats.conversion_rate}%`,
            icon: Percent,
            color: 'pink',
            trend: null
        },
        {
            title: 'Tổng Doanh thu',
            value: formatCurrency(stats.total_revenue),
            icon: TrendingUp,
            color: 'orange',
            trend: null
        }
    ];

    const getActivityIcon = (type) => {
        switch (type) {
            case 'affiliate_registration':
                return UserPlus;
            case 'conversion':
                return ShoppingCart;
            default:
                return Activity;
        }
    };

    const getActivityColor = (status) => {
        switch (status) {
            case 'active':
            case 'approved':
                return 'text-green-600';
            case 'pending':
                return 'text-yellow-600';
            case 'inactive':
            case 'rejected':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const getTierColor = (tier) => {
        switch (tier) {
            case 'bronze':
                return 'bg-amber-100 text-amber-800';
            case 'silver':
                return 'bg-gray-100 text-gray-800';
            case 'gold':
                return 'bg-yellow-100 text-yellow-800';
            case 'platinum':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <SuperAdminLayout title="Affiliate Dashboard">
            <Head title="Affiliate Dashboard" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Affiliate Dashboard
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Tổng quan hệ thống affiliate marketing
                        </p>
                    </div>
                    <button
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                        <Activity className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Làm mới
                    </button>
                </div>

                {/* Stats Cards */}
                <StatCards stats={statCardsData} />

                {/* Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Activities */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Hoạt động Gần đây
                        </h3>
                        <div className="space-y-4">
                            {recentActivities.length > 0 ? (
                                recentActivities.map((activity, index) => {
                                    const IconComponent = getActivityIcon(activity.type);
                                    return (
                                        <div key={index} className="flex items-start space-x-3">
                                            <div className="flex-shrink-0">
                                                <IconComponent className="w-5 h-5 text-gray-400" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm text-gray-900">
                                                    {activity.message}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {formatRelativeTime(activity.time)}
                                                </p>
                                            </div>
                                            <div className={`text-xs font-medium ${getActivityColor(activity.status)}`}>
                                                {activity.status}
                                            </div>
                                        </div>
                                    );
                                })
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có hoạt động nào</p>
                            )}
                        </div>
                    </div>

                    {/* Top Affiliates */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Top Affiliate
                        </h3>
                        <div className="space-y-4">
                            {topAffiliates.length > 0 ? (
                                topAffiliates.map((affiliate, index) => (
                                    <div key={affiliate.id} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="flex-shrink-0">
                                                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                                    <span className="text-sm font-medium text-indigo-600">
                                                        #{index + 1}
                                                    </span>
                                                </div>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {affiliate.name}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {affiliate.email}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(affiliate.total_earnings)}
                                            </p>
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(affiliate.tier)}`}>
                                                {affiliate.tier}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có affiliate nào</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Recent Registrations */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">
                            Đăng ký Gần đây
                        </h3>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Affiliate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Trạng thái
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Tier
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Ngày đăng ký
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {recentRegistrations.length > 0 ? (
                                    recentRegistrations.map((registration) => (
                                        <tr key={registration.id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {registration.name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {registration.email}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityColor(registration.status)}`}>
                                                    {registration.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(registration.tier)}`}>
                                                    {registration.tier}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {formatDateTime(registration.created_at)}
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                                            Chưa có đăng ký nào
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
