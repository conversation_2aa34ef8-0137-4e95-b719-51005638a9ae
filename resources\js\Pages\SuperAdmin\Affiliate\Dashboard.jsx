import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    Users,
    TrendingUp,
    DollarSign,
    MousePointer,
    ShoppingCart,
    Percent,
    Activity,
    Clock,
    UserPlus,
    UserCheck
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/utils/formatting';
import StatusBadge from '@/Components/ui/StatusBadge';
import DataTable from '@/Components/DataTable';

export default function Dashboard({ stats, recentActivities, topAffiliates, recentRegistrations }) {
    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = () => {
        setRefreshing(true);
        router.reload({
            onFinish: () => setRefreshing(false)
        });
    };

    const statsConfig = {
        total_affiliates: {
            title: __('affiliate.total_affiliates'),
            icon: Users,
            bgColor: 'bg-blue-50',
            iconColor: 'text-blue-600',
            textColor: 'text-blue-900',
            formatter: (value) => value
        },
        active_affiliates: {
            title: __('affiliate.active_affiliates'),
            icon: Activity,
            bgColor: 'bg-green-50',
            iconColor: 'text-green-600',
            textColor: 'text-green-900',
            formatter: (value) => value
        },
        total_commissions: {
            title: __('affiliate.total_commissions'),
            icon: DollarSign,
            bgColor: 'bg-emerald-50',
            iconColor: 'text-emerald-600',
            textColor: 'text-emerald-900',
            formatter: (value) => formatCurrency(value)
        },
        pending_withdrawals: {
            title: __('affiliate.pending_withdrawals'),
            icon: Clock,
            bgColor: 'bg-yellow-50',
            iconColor: 'text-yellow-600',
            textColor: 'text-yellow-900',
            formatter: (value) => value
        },
        total_clicks: {
            title: __('affiliate.total_clicks'),
            icon: MousePointer,
            bgColor: 'bg-purple-50',
            iconColor: 'text-purple-600',
            textColor: 'text-purple-900',
            formatter: (value) => value.toLocaleString()
        },
        total_conversions: {
            title: __('affiliate.total_conversions'),
            icon: ShoppingCart,
            bgColor: 'bg-indigo-50',
            iconColor: 'text-indigo-600',
            textColor: 'text-indigo-900',
            formatter: (value) => value.toLocaleString()
        },
        conversion_rate: {
            title: __('affiliate.conversion_rate'),
            icon: Percent,
            bgColor: 'bg-pink-50',
            iconColor: 'text-pink-600',
            textColor: 'text-pink-900',
            formatter: (value) => `${value}%`
        },
        total_revenue: {
            title: __('affiliate.total_revenue'),
            icon: TrendingUp,
            bgColor: 'bg-orange-50',
            iconColor: 'text-orange-600',
            textColor: 'text-orange-900',
            formatter: (value) => formatCurrency(value)
        }
    };

    const getActivityIcon = (type) => {
        switch (type) {
            case 'affiliate_registration':
                return UserPlus;
            case 'conversion':
                return ShoppingCart;
            default:
                return Activity;
        }
    };

    const statusCustomStyles = {
        active: 'bg-green-50 text-green-700 border border-green-200',
        approved: 'bg-green-50 text-green-700 border border-green-200',
        pending: 'bg-yellow-50 text-yellow-700 border border-yellow-200',
        inactive: 'bg-red-50 text-red-700 border border-red-200',
        rejected: 'bg-red-50 text-red-700 border border-red-200',
    };

    const tierCustomStyles = {
        bronze: 'bg-amber-100 text-amber-800',
        silver: 'bg-gray-100 text-gray-800',
        gold: 'bg-yellow-100 text-yellow-800',
        platinum: 'bg-purple-100 text-purple-800',
        default: 'bg-gray-100 text-gray-800',
    };

    return (
        <SuperAdminLayout title={__('affiliate.dashboard')}>
            <Head title={__('affiliate.dashboard')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {__('affiliate.dashboard')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.dashboard_description')}
                        </p>
                    </div>
                    <button
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                        <Activity className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        {__('affiliate.refresh')}
                    </button>
                </div>

                {/* Custom Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                    {Object.entries(statsConfig).map(([key, config]) => {
                        const IconComponent = config.icon;
                        return (
                            <div key={key} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <p className="text-sm font-medium text-gray-600 truncate">
                                            {config.title}
                                        </p>
                                        <p className={`text-2xl font-bold ${config.textColor} mt-1`}>
                                            {config.formatter(stats[key])}
                                        </p>
                                    </div>
                                    <div className={`${config.bgColor} p-3 rounded-lg`}>
                                        <IconComponent className={`w-6 h-6 ${config.iconColor}`} />
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4 mb-6">
                    {/* Recent Activities */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.recent_activities')}
                        </h3>
                        <div className="space-y-4">
                            {recentActivities.length > 0 ? (
                                recentActivities.map((activity, index) => {
                                    const IconComponent = getActivityIcon(activity.type);
                                    return (
                                        <div key={index} className="flex items-start space-x-3">
                                            <div className="flex-shrink-0">
                                                <IconComponent className="w-5 h-5 text-gray-400" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm text-gray-900">
                                                    {activity.message}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {formatRelativeTime(activity.time)}
                                                </p>
                                            </div>
                                            <StatusBadge
                                                status={activity.status}
                                                customStyles={statusCustomStyles}
                                                className="text-xs"
                                            />
                                        </div>
                                    );
                                })
                            ) : (
                                <p className="text-gray-500 text-sm">{__('affiliate.no_activities')}</p>
                            )}
                        </div>
                    </div>

                    {/* Top Affiliates */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {__('affiliate.top_affiliates')}
                        </h3>
                        <div className="space-y-4">
                            {topAffiliates.length > 0 ? (
                                topAffiliates.map((affiliate, index) => (
                                    <div key={affiliate.id} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="flex-shrink-0">
                                                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                                    <span className="text-sm font-medium text-indigo-600">
                                                        #{index + 1}
                                                    </span>
                                                </div>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {affiliate.name}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {affiliate.email}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(affiliate.total_earnings)}
                                            </p>
                                            <StatusBadge
                                                status={__('affiliate.tier.' + affiliate.tier)}
                                                customStyles={tierCustomStyles}
                                                className="text-xs"
                                            />
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">{__('affiliate.no_affiliates')}</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Recent Registrations */}
                <DataTable
                    title={__('affiliate.recent_registrations')}
                    icon={UserCheck}
                    columns={[
                        {
                            field: 'name',
                            label: __('affiliate.name'),
                            render: (registration) => (
                                <div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {registration.name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {registration.email}
                                    </div>
                                </div>
                            )
                        },
                        {
                            field: 'status',
                            label: __('affiliate.status'),
                            render: (registration) => (
                                <StatusBadge
                                    status={registration.status}
                                    customStyles={statusCustomStyles}
                                />
                            )
                        },
                        {
                            field: 'tier',
                            label: __('affiliate.tier_column'),
                            render: (registration) => (
                                <StatusBadge
                                    status={registration.tier}
                                    customStyles={tierCustomStyles}
                                />
                            )
                        },
                        {
                            field: 'created_at',
                            label: __('affiliate.registration_date'),
                            render: (registration) => (
                                <span className="text-sm text-gray-500">
                                    {formatDateTime(registration.created_at)}
                                </span>
                            )
                        }
                    ]}
                    data={recentRegistrations}
                    emptyStateMessage={__('affiliate.no_registrations')}
                    enableDefaultActions={false}
                />
            </div>
        </SuperAdminLayout>
    );
}
