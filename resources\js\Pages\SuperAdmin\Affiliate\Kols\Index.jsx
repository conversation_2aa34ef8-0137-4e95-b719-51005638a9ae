import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { Users, Star, TrendingUp, Eye, Plus, Search, Filter } from 'lucide-react';

export default function Index({
    kols = { data: [] },
    filters = {},
    stats = {}
}) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedTier, setSelectedTier] = useState(filters.tier || '');
    const [selectedCategory, setSelectedCategory] = useState(filters.category || '');

    const formatNumber = (number) => {
        return new Intl.NumberFormat('vi-VN').format(number || 0);
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const handleSearch = () => {
        router.get(route('superadmin.affiliate.kols.index'), {
            search: searchTerm,
            tier: selectedTier,
            category: selectedCategory
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const getTierBadge = (tier) => {
        const tierConfig = {
            micro: { color: 'bg-green-100 text-green-800', text: 'Micro' },
            macro: { color: 'bg-blue-100 text-blue-800', text: 'Macro' },
            mega: { color: 'bg-purple-100 text-purple-800', text: 'Mega' }
        };

        const config = tierConfig[tier] || tierConfig.micro;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
                {config.text}
            </span>
        );
    };

    const getCategoryBadge = (category) => {
        const categoryConfig = {
            influencer: { color: 'bg-pink-100 text-pink-800', text: 'Influencer' },
            blogger: { color: 'bg-indigo-100 text-indigo-800', text: 'Blogger' },
            youtuber: { color: 'bg-red-100 text-red-800', text: 'YouTuber' },
            tiktoker: { color: 'bg-gray-100 text-gray-800', text: 'TikToker' },
            celebrity: { color: 'bg-yellow-100 text-yellow-800', text: 'Celebrity' },
            expert: { color: 'bg-emerald-100 text-emerald-800', text: 'Expert' }
        };

        const config = categoryConfig[category] || categoryConfig.influencer;
        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
                {config.text}
            </span>
        );
    };

    const getVerificationBadge = (isVerified) => {
        return isVerified ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <Star className="w-3 h-3 mr-1" />
                Verified
            </span>
        ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Pending
            </span>
        );
    };

    return (
        <SuperAdminLayout>
            <Head title="KOL Management" />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">KOL Management</h1>
                        <p className="text-gray-600">Manage Key Opinion Leaders and influencers</p>
                    </div>
                    <Link
                        href={route('superadmin.affiliate.kols.create')}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                    >
                        <Plus className="w-4 h-4" />
                        <span>Add KOL</span>
                    </Link>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total KOLs</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total_kols || 0}</p>
                            </div>
                            <Users className="h-8 w-8 text-blue-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Verified KOLs</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.verified_kols || 0}</p>
                            </div>
                            <Star className="h-8 w-8 text-yellow-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Followers</p>
                                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.total_followers)}</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-green-500" />
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Avg Engagement</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.avg_engagement || 0}%</p>
                            </div>
                            <Eye className="h-8 w-8 text-purple-500" />
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <input
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="Search KOLs..."
                                    className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Tier</label>
                            <select
                                value={selectedTier}
                                onChange={(e) => setSelectedTier(e.target.value)}
                                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="">All Tiers</option>
                                <option value="micro">Micro</option>
                                <option value="macro">Macro</option>
                                <option value="mega">Mega</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="">All Categories</option>
                                <option value="influencer">Influencer</option>
                                <option value="blogger">Blogger</option>
                                <option value="youtuber">YouTuber</option>
                                <option value="tiktoker">TikToker</option>
                                <option value="celebrity">Celebrity</option>
                                <option value="expert">Expert</option>
                            </select>
                        </div>
                        <div className="flex items-end">
                            <button
                                onClick={handleSearch}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center justify-center space-x-2"
                            >
                                <Filter className="w-4 h-4" />
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* KOLs Table */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">KOLs</h2>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        KOL
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Tier
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Followers
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Engagement
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Base Rate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {kols.data && kols.data.length > 0 ? (
                                    kols.data.map((kol) => (
                                        <tr key={kol.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <Users className="h-5 w-5 text-gray-600" />
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {kol.stage_name || 'N/A'}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {kol.affiliate?.user?.name || 'N/A'}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {getCategoryBadge(kol.category)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {getTierBadge(kol.tier)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {formatNumber(kol.followers_count)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {kol.engagement_rate || 0}%
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {formatCurrency(kol.base_rate)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {getVerificationBadge(kol.is_verified)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <Link
                                                    href={route('superadmin.affiliate.kols.show', kol.id)}
                                                    className="text-blue-600 hover:text-blue-900 mr-3"
                                                >
                                                    View
                                                </Link>
                                                <Link
                                                    href={route('superadmin.affiliate.kols.edit', kol.id)}
                                                    className="text-indigo-600 hover:text-indigo-900"
                                                >
                                                    Edit
                                                </Link>
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan="8" className="px-6 py-8 text-center text-gray-500">
                                            No KOLs found.
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    {kols.links && (
                        <div className="px-6 py-4 border-t border-gray-200">
                            <div className="flex justify-center">
                                <div className="flex space-x-1">
                                    {kols.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`px-3 py-2 text-sm rounded-md ${link.active
                                                    ? 'bg-blue-500 text-white'
                                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </SuperAdminLayout>
    );
}
