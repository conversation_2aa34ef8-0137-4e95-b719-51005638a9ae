import React from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import DataTable from '@/Components/DataTable';
import { ArrowLeft, Edit, Trash2, ExternalLink, Copy, BarChart3, Users, MousePointer, TrendingUp } from 'lucide-react';

export default function Show({ link, clickStats = [], conversionStats = [] }) {
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
        // You could add a toast notification here
    };

    const clickColumns = [
        {
            key: 'clicked_at',
            label: 'Date',
            render: (item) => formatDate(item.clicked_at)
        },
        {
            key: 'ip_address',
            label: 'IP Address'
        },
        {
            key: 'user_agent',
            label: 'User Agent',
            render: (item) => (
                <span className="truncate max-w-xs" title={item.user_agent}>
                    {item.user_agent}
                </span>
            )
        },
        {
            key: 'referrer',
            label: 'Referrer',
            render: (item) => item.referrer || 'Direct'
        },
        {
            key: 'is_unique',
            label: 'Unique',
            render: (item) => (
                <span className={`px-2 py-1 rounded-full text-xs ${item.is_unique ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                    {item.is_unique ? 'Yes' : 'No'}
                </span>
            )
        }
    ];

    const conversionColumns = [
        {
            key: 'converted_at',
            label: 'Date',
            render: (item) => formatDate(item.converted_at)
        },
        {
            key: 'order_id',
            label: 'Order ID'
        },
        {
            key: 'order_value',
            label: 'Order Value',
            render: (item) => formatCurrency(item.order_value)
        },
        {
            key: 'commission_amount',
            label: 'Commission',
            render: (item) => formatCurrency(item.commission_amount)
        },
        {
            key: 'status',
            label: 'Status',
            render: (item) => (
                <span className={`px-2 py-1 rounded-full text-xs ${item.status === 'approved' ? 'bg-green-100 text-green-800' :
                        item.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                    }`}>
                    {item.status}
                </span>
            )
        }
    ];

    return (
        <SuperAdminLayout>
            <Head title={`Link Details - ${link.title}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <SecondaryButton
                            onClick={() => router.visit(route('superadmin.affiliate.links.index'))}
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Links
                        </SecondaryButton>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">{link.title}</h1>
                            <p className="text-gray-600">Link Details & Performance</p>
                        </div>
                    </div>
                    <div className="flex space-x-3">
                        <SecondaryButton
                            onClick={() => router.visit(route('superadmin.affiliate.links.edit', link.id))}
                        >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                        </SecondaryButton>
                        <PrimaryButton
                            onClick={() => window.open(link.short_url, '_blank')}
                        >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Visit Link
                        </PrimaryButton>
                    </div>
                </div>

                {/* Link Information */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Link Information</h2>
                    </div>
                    <div className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Affiliate
                                </label>
                                <p className="text-gray-900">{link.affiliate?.user?.name || 'N/A'}</p>
                                <p className="text-sm text-gray-500">{link.affiliate?.user?.email}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Campaign
                                </label>
                                <p className="text-gray-900">{link.campaign?.name || 'No Campaign'}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Original URL
                                </label>
                                <div className="flex items-center space-x-2">
                                    <p className="text-gray-900 truncate flex-1">{link.original_url}</p>
                                    <button
                                        onClick={() => copyToClipboard(link.original_url)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <Copy className="w-4 h-4" />
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Short URL
                                </label>
                                <div className="flex items-center space-x-2">
                                    <p className="text-gray-900 truncate flex-1">{link.short_url}</p>
                                    <button
                                        onClick={() => copyToClipboard(link.short_url)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <Copy className="w-4 h-4" />
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Short Code
                                </label>
                                <p className="text-gray-900">{link.short_code}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Status
                                </label>
                                <span className={`px-2 py-1 rounded-full text-xs ${link.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                    {link.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Created
                                </label>
                                <p className="text-gray-900">{formatDate(link.created_at)}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Last Clicked
                                </label>
                                <p className="text-gray-900">{link.last_clicked_at ? formatDate(link.last_clicked_at) : 'Never'}</p>
                            </div>
                        </div>
                        {link.description && (
                            <div className="mt-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Description
                                </label>
                                <p className="text-gray-900">{link.description}</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Performance Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <MousePointer className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                                <p className="text-2xl font-bold text-gray-900">{link.clicks || 0}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Users className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Unique Clicks</p>
                                <p className="text-2xl font-bold text-gray-900">{link.unique_clicks || 0}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Conversions</p>
                                <p className="text-2xl font-bold text-gray-900">{link.conversions || 0}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <BarChart3 className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {(Number(link.clicks) || 0) > 0 ? (((Number(link.conversions) || 0) / (Number(link.clicks) || 1)) * 100).toFixed(2) : 0}%
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recent Clicks */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Recent Clicks</h2>
                    </div>
                    <DataTable
                        data={clickStats}
                        columns={clickColumns}
                        emptyStateMessage="No clicks recorded yet"
                    />
                </div>

                {/* Recent Conversions */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Recent Conversions</h2>
                    </div>
                    <DataTable
                        data={conversionStats}
                        columns={conversionColumns}
                        emptyStateMessage="No conversions recorded yet"
                    />
                </div>
            </div>
        </SuperAdminLayout>
    );
}
