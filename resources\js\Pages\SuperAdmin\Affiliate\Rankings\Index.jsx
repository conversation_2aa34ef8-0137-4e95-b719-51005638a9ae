import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import DataTable from '@/Components/DataTable';
import StatusBadge from '@/Components/ui/StatusBadge';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import { Trophy, TrendingUp, Users, DollarSign, Target, Eye, MousePointer, ShoppingCart, Search, RotateCcw } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { __ } from '@/utils/lang';


export default function Index({
    rankings = { data: [], links: [] },
    top_performers = {},
    tier_distribution = {},
    trends = [],
    filters = {}
}) {
    const [localFilters, setLocalFilters] = useState(filters || {});
    const [isSearching, setIsSearching] = useState(false);

    const handleFilterChange = (key, value) => {
        const newFilters = { ...localFilters, [key]: value };
        setLocalFilters(newFilters);
    };

    const handleSearch = () => {
        setIsSearching(true);
        router.get(route('superadmin.affiliate.rankings.index'), localFilters, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsSearching(false),
        });
    };

    const handleReset = () => {
        const resetFilters = {};
        setLocalFilters(resetFilters);
        setIsSearching(true);
        router.get(route('superadmin.affiliate.rankings.index'), resetFilters, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsSearching(false),
        });
    };


    const formatNumber = (number) => {
        return new Intl.NumberFormat('vi-VN').format(number || 0);
    };



    const getRankingIcon = (position) => {
        if (position === 1) return <Trophy className="h-5 w-5 text-yellow-500" />;
        if (position === 2) return <Trophy className="h-5 w-5 text-gray-400" />;
        if (position === 3) return <Trophy className="h-5 w-5 text-amber-600" />;
        return <span className="text-sm font-medium text-gray-500">#{position}</span>;
    };

    const columns = [
        {
            field: 'ranking_position',
            label: __('affiliate.rank'),
            render: (affiliate) => (
                <div className="flex items-center">
                    {getRankingIcon(affiliate.ranking_position || 0)}
                </div>
            )
        },
        {
            field: 'user',
            label: __('affiliate.affiliate'),
            render: (affiliate) => (
                <div>
                    <div className="font-medium text-gray-900">
                        {affiliate.user?.name || 'N/A'}
                    </div>
                    <div className="text-sm text-gray-500">
                        {affiliate.user?.email || 'N/A'}
                    </div>
                </div>
            )
        },
        {
            field: 'tier',
            label: __('affiliate.tier_label'),
            render: (affiliate) => (
                <StatusBadge
                    status={affiliate.tier || 'bronze'}
                    customStyles={{
                        bronze: 'bg-amber-100 text-amber-800',
                        silver: 'bg-gray-100 text-gray-800',
                        gold: 'bg-yellow-100 text-yellow-800',
                        platinum: 'bg-purple-100 text-purple-800'
                    }}
                    text={__(`affiliate.tier.${affiliate.tier || 'bronze'}`)}
                />
            )
        },
        {
            field: 'period_clicks',
            label: __('affiliate.clicks'),
            className: 'text-right',
            tdClassName: 'text-right',
            render: (affiliate) => formatNumber(affiliate.period_clicks)
        },
        {
            field: 'period_conversions',
            label: __('affiliate.conversions'),
            className: 'text-right',
            tdClassName: 'text-right',
            render: (affiliate) => formatNumber(affiliate.period_conversions)
        },
        {
            field: 'period_conversion_rate',
            label: __('affiliate.conversion_rate_short'),
            className: 'text-right',
            tdClassName: 'text-right',
            render: (affiliate) => `${affiliate.period_conversion_rate || 0}%`
        },
        {
            field: 'period_revenue',
            label: __('affiliate.revenue'),
            className: 'text-right',
            tdClassName: 'text-right',
            render: (affiliate) => formatCurrency(affiliate.period_revenue)
        },
        {
            field: 'period_earnings',
            label: __('affiliate.earnings'),
            className: 'text-right',
            tdClassName: 'text-right font-medium text-green-600',
            render: (affiliate) => formatCurrency(affiliate.period_earnings)
        },
        {
            field: 'period_epc',
            label: __('affiliate.epc'),
            className: 'text-right',
            tdClassName: 'text-right',
            render: (affiliate) => formatCurrency(affiliate.period_epc)
        }
    ];

    return (
        <SuperAdminLayout>
            <Head title={__('affiliate.affiliate_rankings')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{__('affiliate.affiliate_rankings')}</h1>
                        <p className="text-gray-600">{__('affiliate.track_and_compare')}</p>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.start_date')}
                            value={localFilters.start_date || ''}
                            onChange={(e) => handleFilterChange('start_date', e.target.value)}
                        />
                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.end_date')}
                            value={localFilters.end_date || ''}
                            onChange={(e) => handleFilterChange('end_date', e.target.value)}
                        />
                        <SelectWithLabel
                            label={__('affiliate.ranking_type')}
                            value={localFilters.ranking_type || 'earnings'}
                            onChange={(e) => handleFilterChange('ranking_type', e.target.value)}
                        >
                            <option value="earnings">{__('affiliate.earnings')}</option>
                            <option value="conversions">{__('affiliate.conversions')}</option>
                            <option value="clicks">{__('affiliate.clicks')}</option>
                            <option value="revenue">{__('affiliate.revenue')}</option>
                        </SelectWithLabel>
                        <SelectWithLabel
                            label={__('affiliate.period')}
                            value={localFilters.period || 'month'}
                            onChange={(e) => handleFilterChange('period', e.target.value)}
                        >
                            <option value="week">{__('affiliate.week')}</option>
                            <option value="month">{__('affiliate.month')}</option>
                            <option value="quarter">{__('affiliate.quarter')}</option>
                            <option value="year">{__('affiliate.year')}</option>
                        </SelectWithLabel>
                    </div>

                    {/* Filter Actions */}
                    <div className="flex justify-end space-x-3">
                        <SecondaryButton
                            onClick={handleReset}
                            disabled={isSearching}
                            className="flex items-center"
                        >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            {__('affiliate.reset')}
                        </SecondaryButton>
                        <PrimaryButton
                            onClick={handleSearch}
                            disabled={isSearching}
                            className="flex items-center"
                        >
                            <Search className="h-4 w-4 mr-2" />
                            {isSearching ? __('common.searching') + '...' : __('affiliate.search_button')}
                        </PrimaryButton>
                    </div>
                </div>

                {/* Top Performers */}
                {top_performers && Object.keys(top_performers).length > 0 && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                            {__('affiliate.top_performers')}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {top_performers.top_earner && (
                                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="text-sm font-medium text-green-700">{__('affiliate.top_earner')}</h3>
                                        <DollarSign className="h-5 w-5 text-green-500" />
                                    </div>
                                    <div className="text-2xl font-bold text-green-900 mb-2">
                                        {formatCurrency(top_performers.top_earner.period_earnings)}
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <p className="text-sm text-green-600 font-medium">
                                            {top_performers.top_earner.user?.name || 'N/A'}
                                        </p>
                                        <StatusBadge
                                            status={top_performers.top_earner.tier || 'bronze'}
                                            customStyles={{
                                                bronze: 'bg-amber-100 text-amber-800',
                                                silver: 'bg-gray-100 text-gray-800',
                                                gold: 'bg-yellow-100 text-yellow-800',
                                                platinum: 'bg-purple-100 text-purple-800'
                                            }}
                                            text={__(`affiliate.tier.${top_performers.top_earner.tier || 'bronze'}`)}
                                        />
                                    </div>
                                </div>
                            )}

                            {top_performers.top_converter && (
                                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="text-sm font-medium text-blue-700">{__('affiliate.top_converter')}</h3>
                                        <Target className="h-5 w-5 text-blue-500" />
                                    </div>
                                    <div className="text-2xl font-bold text-blue-900 mb-2">
                                        {formatNumber(top_performers.top_converter.period_conversions)}
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <p className="text-sm text-blue-600 font-medium">
                                            {top_performers.top_converter.user?.name || 'N/A'}
                                        </p>
                                        <StatusBadge
                                            status={top_performers.top_converter.tier || 'bronze'}
                                            customStyles={{
                                                bronze: 'bg-amber-100 text-amber-800',
                                                silver: 'bg-gray-100 text-gray-800',
                                                gold: 'bg-yellow-100 text-yellow-800',
                                                platinum: 'bg-purple-100 text-purple-800'
                                            }}
                                            text={__(`affiliate.tier.${top_performers.top_converter.tier || 'bronze'}`)}
                                        />
                                    </div>
                                </div>
                            )}

                            {top_performers.top_clicker && (
                                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200 p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="text-sm font-medium text-purple-700">{__('affiliate.top_clicker')}</h3>
                                        <MousePointer className="h-5 w-5 text-purple-500" />
                                    </div>
                                    <div className="text-2xl font-bold text-purple-900 mb-2">
                                        {formatNumber(top_performers.top_clicker.period_clicks)}
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <p className="text-sm text-purple-600 font-medium">
                                            {top_performers.top_clicker.user?.name || 'N/A'}
                                        </p>
                                        <StatusBadge
                                            status={top_performers.top_clicker.tier || 'bronze'}
                                            customStyles={{
                                                bronze: 'bg-amber-100 text-amber-800',
                                                silver: 'bg-gray-100 text-gray-800',
                                                gold: 'bg-yellow-100 text-yellow-800',
                                                platinum: 'bg-purple-100 text-purple-800'
                                            }}
                                            text={__(`affiliate.tier.${top_performers.top_clicker.tier || 'bronze'}`)}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Rankings Table */}
                <DataTable
                    data={rankings.data || []}
                    columns={columns}
                    title={__('affiliate.affiliate_rankings')}
                    icon={Trophy}
                    emptyStateMessage={__('affiliate.no_affiliate_data_period')}
                    enableDefaultActions={false}
                />
            </div>
        </SuperAdminLayout>
    );
}
