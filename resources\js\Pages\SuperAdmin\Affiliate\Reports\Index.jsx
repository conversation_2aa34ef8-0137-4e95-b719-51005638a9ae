import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { BarChart3, TrendingUp, Download, Calendar, Filter, FileText } from 'lucide-react';

export default function Index({
    reports = [],
    summary = {},
    filters = {}
}) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [reportType, setReportType] = useState(filters.report_type || 'performance');

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatNumber = (number) => {
        return new Intl.NumberFormat('vi-VN').format(number || 0);
    };

    const generateReport = () => {
        router.get(route('superadmin.affiliate.reports.index'), {
            ...dateRange,
            report_type: reportType
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const exportReport = (format) => {
        router.get(route('superadmin.affiliate.reports.export'), {
            ...dateRange,
            report_type: reportType,
            format: format
        });
    };

    const reportTypes = [
        { value: 'performance', label: 'Performance Report' },
        { value: 'commission', label: 'Commission Report' },
        { value: 'conversion', label: 'Conversion Report' },
        { value: 'affiliate', label: 'Affiliate Report' },
        { value: 'campaign', label: 'Campaign Report' }
    ];

    return (
        <SuperAdminLayout>
            <Head title="Affiliate Reports" />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Affiliate Reports</h1>
                        <p className="text-gray-600">Generate and analyze affiliate performance reports</p>
                    </div>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => exportReport('excel')}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                        >
                            <Download className="w-4 h-4" />
                            <span>Excel</span>
                        </button>
                        <button
                            onClick={() => exportReport('pdf')}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                        >
                            <Download className="w-4 h-4" />
                            <span>PDF</span>
                        </button>
                    </div>
                </div>

                {/* Report Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Report Configuration</h2>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                            <select
                                value={reportType}
                                onChange={(e) => setReportType(e.target.value)}
                                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                {reportTypes.map(type => (
                                    <option key={type.value} value={type.value}>
                                        {type.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                            <input
                                type="date"
                                value={dateRange.start_date}
                                onChange={(e) => setDateRange(prev => ({ ...prev, start_date: e.target.value }))}
                                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                            <input
                                type="date"
                                value={dateRange.end_date}
                                onChange={(e) => setDateRange(prev => ({ ...prev, end_date: e.target.value }))}
                                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                        <div className="flex items-end">
                            <button
                                onClick={generateReport}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center justify-center space-x-2"
                            >
                                <BarChart3 className="w-4 h-4" />
                                <span>Generate</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Summary Cards */}
                {summary && Object.keys(summary).length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.total_revenue)}</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-green-500" />
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.total_commissions)}</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-blue-500" />
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                                    <p className="text-2xl font-bold text-gray-900">{formatNumber(summary.total_clicks)}</p>
                                </div>
                                <BarChart3 className="h-8 w-8 text-purple-500" />
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                                    <p className="text-2xl font-bold text-gray-900">{summary.conversion_rate || 0}%</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-yellow-500" />
                            </div>
                        </div>
                    </div>
                )}

                {/* Report Data */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">
                            {reportTypes.find(t => t.value === reportType)?.label || 'Report Data'}
                        </h2>
                    </div>
                    <div className="overflow-x-auto">
                        {reports && reports.length > 0 ? (
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        {reportType === 'performance' && (
                                            <>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Affiliate
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Clicks
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Conversions
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Conv. Rate
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Revenue
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Commission
                                                </th>
                                            </>
                                        )}
                                        {reportType === 'commission' && (
                                            <>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Affiliate
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Total Earned
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Paid
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Pending
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Last Payment
                                                </th>
                                            </>
                                        )}
                                        {reportType === 'campaign' && (
                                            <>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Campaign
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Affiliates
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Clicks
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Conversions
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Revenue
                                                </th>
                                            </>
                                        )}
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {reports.map((item, index) => (
                                        <tr key={index} className="hover:bg-gray-50">
                                            {reportType === 'performance' && (
                                                <>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {item.affiliate_name || 'N/A'}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatNumber(item.clicks)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatNumber(item.conversions)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {item.conversion_rate || 0}%
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatCurrency(item.revenue)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                                                        {formatCurrency(item.commission)}
                                                    </td>
                                                </>
                                            )}
                                            {reportType === 'commission' && (
                                                <>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {item.affiliate_name || 'N/A'}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatCurrency(item.total_earned)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-green-600">
                                                        {formatCurrency(item.paid)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-yellow-600">
                                                        {formatCurrency(item.pending)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {item.last_payment || 'N/A'}
                                                    </td>
                                                </>
                                            )}
                                            {reportType === 'campaign' && (
                                                <>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {item.campaign_name || 'N/A'}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatNumber(item.affiliate_count)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatNumber(item.clicks)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatNumber(item.conversions)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                        {formatCurrency(item.revenue)}
                                                    </td>
                                                </>
                                            )}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        ) : (
                            <div className="px-6 py-8 text-center text-gray-500">
                                <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                                <p>No report data available for the selected criteria.</p>
                                <p className="text-sm">Try adjusting your filters and generate a new report.</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
