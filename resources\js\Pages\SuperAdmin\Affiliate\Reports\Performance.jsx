import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import DataTable from '@/Components/DataTable';
import { BarChart3, TrendingUp, Users, DollarSign, Download, Calendar, Filter } from 'lucide-react';

export default function Performance({
    performance_data = [],
    summary_stats = {},
    filters = {},
    date_range = { start: '', end: '' }
}) {
    const [dateRange, setDateRange] = useState({
        start: filters.start_date || '',
        end: filters.end_date || ''
    });
    const [selectedMetric, setSelectedMetric] = useState('revenue');
    const [loading, setLoading] = useState(false);

    const handleFilterChange = () => {
        setLoading(true);
        router.get(route('superadmin.affiliate.reports.performance'), {
            start_date: dateRange.start,
            end_date: dateRange.end,
            metric: selectedMetric
        }, {
            preserveState: true,
            onFinish: () => setLoading(false)
        });
    };

    const handleExport = () => {
        window.open(route('superadmin.affiliate.reports.performance.export', {
            start_date: dateRange.start,
            end_date: dateRange.end,
            format: 'excel'
        }));
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount || 0);
    };

    const formatNumber = (number) => {
        return new Intl.NumberFormat('vi-VN').format(number || 0);
    };

    const formatPercentage = (value) => {
        return `${Number(value || 0).toFixed(2)}%`;
    };

    const getPerformanceColor = (value, threshold = 0) => {
        if (value > threshold) return 'text-green-600';
        if (value < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    return (
        <SuperAdminLayout>
            <Head title="Performance Reports" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Performance Reports</h1>
                        <p className="text-gray-600">Analyze affiliate program performance and metrics</p>
                    </div>
                    <PrimaryButton
                        onClick={handleExport}
                        className="bg-green-600 hover:bg-green-700"
                    >
                        <Download className="w-4 h-4 mr-2" />
                        Export Report
                    </PrimaryButton>
                </div>

                {/* Filters */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <Filter className="w-5 h-5 text-gray-500" />
                            <span className="font-medium text-gray-700">Filters:</span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-gray-500" />
                            <input
                                type="date"
                                value={dateRange.start}
                                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                                className="border border-gray-300 rounded px-3 py-1 text-sm"
                            />
                            <span className="text-gray-500">to</span>
                            <input
                                type="date"
                                value={dateRange.end}
                                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                                className="border border-gray-300 rounded px-3 py-1 text-sm"
                            />
                        </div>
                        <select
                            value={selectedMetric}
                            onChange={(e) => setSelectedMetric(e.target.value)}
                            className="border border-gray-300 rounded px-3 py-1 text-sm"
                        >
                            <option value="revenue">Revenue</option>
                            <option value="conversions">Conversions</option>
                            <option value="clicks">Clicks</option>
                            <option value="commissions">Commissions</option>
                        </select>
                        <PrimaryButton
                            onClick={handleFilterChange}
                            disabled={loading}
                            className="text-sm px-4 py-1"
                        >
                            {loading ? 'Loading...' : 'Apply'}
                        </PrimaryButton>
                    </div>
                </div>

                {/* Summary Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <DollarSign className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary_stats.total_revenue)}</p>
                                <p className={`text-sm ${getPerformanceColor(summary_stats.revenue_growth)}`}>
                                    {summary_stats.revenue_growth > 0 ? '+' : ''}{formatPercentage(summary_stats.revenue_growth)} vs last period
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Conversions</p>
                                <p className="text-2xl font-bold text-gray-900">{formatNumber(summary_stats.total_conversions)}</p>
                                <p className={`text-sm ${getPerformanceColor(summary_stats.conversion_growth)}`}>
                                    {summary_stats.conversion_growth > 0 ? '+' : ''}{formatPercentage(summary_stats.conversion_growth)} vs last period
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Users className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Active Affiliates</p>
                                <p className="text-2xl font-bold text-gray-900">{formatNumber(summary_stats.active_affiliates)}</p>
                                <p className={`text-sm ${getPerformanceColor(summary_stats.affiliate_growth)}`}>
                                    {summary_stats.affiliate_growth > 0 ? '+' : ''}{formatPercentage(summary_stats.affiliate_growth)} vs last period
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <BarChart3 className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                                <p className="text-2xl font-bold text-gray-900">{formatPercentage(summary_stats.conversion_rate)}</p>
                                <p className={`text-sm ${getPerformanceColor(summary_stats.conversion_rate_change)}`}>
                                    {summary_stats.conversion_rate_change > 0 ? '+' : ''}{formatPercentage(summary_stats.conversion_rate_change)} vs last period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Performance Table */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Affiliate Performance</h2>
                        <p className="text-sm text-gray-600">Detailed performance metrics by affiliate</p>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Affiliate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Clicks
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Conversions
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Conversion Rate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Revenue
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Commission
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Performance
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {performance_data.length === 0 ? (
                                    <tr>
                                        <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                                            No performance data available for the selected period
                                        </td>
                                    </tr>
                                ) : (
                                    performance_data.map((affiliate, index) => (
                                        <tr key={affiliate.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <span className="text-sm font-medium text-gray-700">
                                                                {affiliate.user?.name?.charAt(0) || 'A'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {affiliate.user?.name || 'Unknown'}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {affiliate.referral_code}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatNumber(affiliate.total_clicks)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatNumber(affiliate.total_conversions)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatPercentage(affiliate.conversion_rate)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatCurrency(affiliate.total_revenue)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatCurrency(affiliate.total_commission)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{ width: `${Math.min(affiliate.performance_score || 0, 100)}%` }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-sm text-gray-600">
                                                        {Number(affiliate.performance_score || 0).toFixed(0)}%
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
