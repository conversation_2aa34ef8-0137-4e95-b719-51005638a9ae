import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    DollarSign,
    TrendingUp,
    Calendar,
    Download,
    Filter,
    BarChart3,
    Users,
    Target,
    MousePointer
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils/formatting';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import DataTable from '@/Components/DataTable';

export default function Report({ revenue, filters, affiliates, campaigns }) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [selectedAffiliate, setSelectedAffiliate] = useState(filters.affiliate_id || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');
    const [selectedPeriod, setSelectedPeriod] = useState(filters.period || 'daily');

    const handleFilterChange = () => {
        router.get(route('superadmin.affiliate.revenue.report'), {
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign,
            period: selectedPeriod
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const handleExport = () => {
        const params = new URLSearchParams({
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign,
            period: selectedPeriod,
            export: 'true'
        });
        
        window.open(`${route('superadmin.affiliate.revenue.report')}?${params.toString()}`, '_blank');
    };

    const summaryStats = [
        {
            title: 'Total Revenue',
            value: formatCurrency(revenue.summary?.total_revenue || 0),
            icon: DollarSign,
            color: 'green',
            change: revenue.summary?.revenue_change || 0
        },
        {
            title: 'Total Commissions',
            value: formatCurrency(revenue.summary?.total_commissions || 0),
            icon: TrendingUp,
            color: 'blue',
            change: revenue.summary?.commission_change || 0
        },
        {
            title: 'Active Affiliates',
            value: revenue.summary?.active_affiliates || 0,
            icon: Users,
            color: 'purple',
            change: revenue.summary?.affiliate_change || 0
        },
        {
            title: 'Conversion Rate',
            value: `${revenue.summary?.conversion_rate || 0}%`,
            icon: Target,
            color: 'orange',
            change: revenue.summary?.conversion_change || 0
        }
    ];

    return (
        <SuperAdminLayout title="Revenue Report">
            <Head title="Revenue Report" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <BarChart3 className="w-6 h-6 mr-3" />
                            Affiliate Revenue Report
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Detailed revenue analysis and performance metrics
                        </p>
                    </div>
                    <PrimaryButton onClick={handleExport} className="flex items-center">
                        <Download className="w-4 h-4 mr-2" />
                        Export Report
                    </PrimaryButton>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-gray-600" />
                        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <TextInputWithLabel
                            type="date"
                            label="From Date"
                            value={dateRange.start_date}
                            onChange={(e) => setDateRange({ ...dateRange, start_date: e.target.value })}
                        />

                        <TextInputWithLabel
                            type="date"
                            label="To Date"
                            value={dateRange.end_date}
                            onChange={(e) => setDateRange({ ...dateRange, end_date: e.target.value })}
                        />

                        <SelectWithLabel
                            label="Period"
                            value={selectedPeriod}
                            onChange={(e) => setSelectedPeriod(e.target.value)}
                        >
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </SelectWithLabel>

                        <SelectWithLabel
                            label="Affiliate"
                            value={selectedAffiliate}
                            onChange={(e) => setSelectedAffiliate(e.target.value)}
                        >
                            <option value="">All Affiliates</option>
                            {affiliates && affiliates.map((affiliate) => (
                                <option key={affiliate.id} value={affiliate.id}>
                                    {affiliate.user.name} ({affiliate.referral_code})
                                </option>
                            ))}
                        </SelectWithLabel>

                        <SelectWithLabel
                            label="Campaign"
                            value={selectedCampaign}
                            onChange={(e) => setSelectedCampaign(e.target.value)}
                        >
                            <option value="">All Campaigns</option>
                            {campaigns && campaigns.map((campaign) => (
                                <option key={campaign.id} value={campaign.id}>
                                    {campaign.name}
                                </option>
                            ))}
                        </SelectWithLabel>

                        <div className="flex items-end">
                            <PrimaryButton
                                onClick={handleFilterChange}
                                className="w-full h-10"
                            >
                                Apply Filters
                            </PrimaryButton>
                        </div>
                    </div>
                </div>

                {/* Summary Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {summaryStats.map((stat, index) => (
                        <div key={index} className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                    <p className="text-2xl font-bold text-gray-900 mt-2">
                                        {stat.value}
                                    </p>
                                    {stat.change !== 0 && (
                                        <p className={`text-sm mt-1 ${stat.change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {stat.change > 0 ? '+' : ''}{stat.change}% from last period
                                        </p>
                                    )}
                                </div>
                                <div className={`p-3 rounded-lg bg-${stat.color}-50 text-${stat.color}-600`}>
                                    <stat.icon className="w-6 h-6" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Revenue Data Table */}
                <DataTable
                    title="Revenue Breakdown"
                    icon={DollarSign}
                    columns={[
                        {
                            field: 'period',
                            label: 'Period',
                            render: (item) => (
                                <div className="text-sm font-medium text-gray-900">
                                    {formatDateTime(item.period)}
                                </div>
                            )
                        },
                        {
                            field: 'affiliate',
                            label: 'Affiliate',
                            render: (item) => (
                                <div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {item.affiliate_name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {item.affiliate_code}
                                    </div>
                                </div>
                            )
                        },
                        {
                            field: 'campaign',
                            label: 'Campaign',
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.campaign_name || 'N/A'}
                                </div>
                            )
                        },
                        {
                            field: 'clicks',
                            label: 'Clicks',
                            render: (item) => (
                                <div className="flex items-center text-sm text-gray-900">
                                    <MousePointer className="w-3 h-3 mr-1 text-gray-400" />
                                    {item.clicks?.toLocaleString()}
                                </div>
                            )
                        },
                        {
                            field: 'conversions',
                            label: 'Conversions',
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.conversions?.toLocaleString()}
                                </div>
                            )
                        },
                        {
                            field: 'conversion_rate',
                            label: 'CVR',
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.conversion_rate}%
                                </div>
                            )
                        },
                        {
                            field: 'revenue',
                            label: 'Revenue',
                            render: (item) => (
                                <div className="text-sm font-medium text-gray-900">
                                    {formatCurrency(item.revenue)}
                                </div>
                            )
                        },
                        {
                            field: 'commission',
                            label: 'Commission',
                            render: (item) => (
                                <div className="text-sm font-medium text-green-600">
                                    {formatCurrency(item.commission)}
                                </div>
                            )
                        }
                    ]}
                    data={revenue.data || []}
                    emptyStateMessage="No revenue data available for the selected period"
                    enableDefaultActions={false}
                />

                {/* Pagination */}
                {revenue.data && revenue.data.length > 0 && revenue.last_page > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
                        <div className="flex-1 flex justify-between sm:hidden">
                            {revenue.prev_page_url && (
                                <a
                                    href={revenue.prev_page_url}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Previous
                                </a>
                            )}
                            {revenue.next_page_url && (
                                <a
                                    href={revenue.next_page_url}
                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Next
                                </a>
                            )}
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{revenue.from}</span> to{' '}
                                    <span className="font-medium">{revenue.to}</span> of{' '}
                                    <span className="font-medium">{revenue.total}</span> results
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
