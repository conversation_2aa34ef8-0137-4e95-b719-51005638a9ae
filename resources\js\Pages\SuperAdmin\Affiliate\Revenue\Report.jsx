import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    DollarSign,
    TrendingUp,
    Calendar,
    Download,
    Filter,
    BarChart3,
    Users,
    Target,
    MousePointer
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatCurrency, formatDateTime } from '@/utils/formatting';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import DataTable from '@/Components/DataTable';

export default function Report({ revenue, filters, affiliates, campaigns }) {
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [selectedAffiliate, setSelectedAffiliate] = useState(filters.affiliate_id || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');
    const [selectedPeriod, setSelectedPeriod] = useState(filters.period || 'daily');

    const handleFilterChange = () => {
        router.get(route('superadmin.affiliate.revenue.report'), {
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign,
            period: selectedPeriod
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    const handleExport = () => {
        const params = new URLSearchParams({
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign,
            period: selectedPeriod,
            export: 'true'
        });

        window.open(`${route('superadmin.affiliate.revenue.report')}?${params.toString()}`, '_blank');
    };

    const summaryStats = [
        {
            title: __('affiliate.total_revenue'),
            value: formatCurrency(revenue.summary?.total_revenue || 0),
            icon: DollarSign,
            color: 'green',
            change: revenue.summary?.revenue_change || 0
        },
        {
            title: __('affiliate.total_commissions'),
            value: formatCurrency(revenue.summary?.total_commissions || 0),
            icon: TrendingUp,
            color: 'blue',
            change: revenue.summary?.commission_change || 0
        },
        {
            title: __('affiliate.active_affiliates'),
            value: revenue.summary?.active_affiliates || 0,
            icon: Users,
            color: 'purple',
            change: revenue.summary?.affiliate_change || 0
        },
        {
            title: __('affiliate.conversion_rate'),
            value: `${revenue.summary?.conversion_rate || 0}%`,
            icon: Target,
            color: 'orange',
            change: revenue.summary?.conversion_change || 0
        }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.revenue_report')}>
            <Head title={__('affiliate.revenue_report')} />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <BarChart3 className="w-6 h-6 mr-3" />
                            {__('affiliate.revenue_report')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.revenue_report_description')}
                        </p>
                    </div>
                    <PrimaryButton onClick={handleExport} className="flex items-center">
                        <Download className="w-4 h-4 mr-2" />
                        {__('affiliate.export_report')}
                    </PrimaryButton>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6 mt-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-gray-600" />
                        <h3 className="text-lg font-medium text-gray-900">{__('affiliate.filters')}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.from_date')}
                            value={dateRange.start_date}
                            onChange={(e) => setDateRange({ ...dateRange, start_date: e.target.value })}
                        />

                        <TextInputWithLabel
                            type="date"
                            label={__('affiliate.to_date')}
                            value={dateRange.end_date}
                            onChange={(e) => setDateRange({ ...dateRange, end_date: e.target.value })}
                        />

                        <SelectWithLabel
                            label={__('affiliate.period')}
                            value={selectedPeriod}
                            onChange={(e) => setSelectedPeriod(e.target.value)}
                        >
                            <option value="daily">{__('affiliate.daily')}</option>
                            <option value="weekly">{__('affiliate.weekly')}</option>
                            <option value="monthly">{__('affiliate.monthly')}</option>
                        </SelectWithLabel>

                        <SelectWithLabel
                            label={__('affiliate.affiliate')}
                            value={selectedAffiliate}
                            onChange={(e) => setSelectedAffiliate(e.target.value)}
                        >
                            <option value="">{__('affiliate.all_affiliates')}</option>
                            {affiliates && affiliates.map((affiliate) => (
                                <option key={affiliate.id} value={affiliate.id}>
                                    {affiliate.user.name} ({affiliate.referral_code})
                                </option>
                            ))}
                        </SelectWithLabel>

                        <SelectWithLabel
                            label={__('affiliate.campaign')}
                            value={selectedCampaign}
                            onChange={(e) => setSelectedCampaign(e.target.value)}
                        >
                            <option value="">{__('affiliate.all_campaigns')}</option>
                            {campaigns && campaigns.map((campaign) => (
                                <option key={campaign.id} value={campaign.id}>
                                    {campaign.name}
                                </option>
                            ))}
                        </SelectWithLabel>

                        <div>
                            <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                                {__('affiliate.apply_filters')}
                            </label>
                            <PrimaryButton
                                onClick={handleFilterChange}
                                className="w-full h-10"
                            >
                                {__('affiliate.apply_filters')}
                            </PrimaryButton>
                        </div>
                    </div>
                </div>

                {/* Summary Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6 mb-6">
                    {summaryStats.map((stat, index) => (
                        <div key={index} className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                    <p className="text-2xl font-bold text-gray-900 mt-2">
                                        {stat.value}
                                    </p>
                                    {stat.change !== 0 && (
                                        <p className={`text-sm mt-1 ${stat.change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            {stat.change > 0 ? '+' : ''}{stat.change}% {__('affiliate.from_last_period')}
                                        </p>
                                    )}
                                </div>
                                <div className={`p-3 rounded-lg bg-${stat.color}-50 text-${stat.color}-600`}>
                                    <stat.icon className="w-6 h-6" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Revenue Data Table */}
                <DataTable
                    title={__('affiliate.revenue_breakdown')}
                    icon={DollarSign}
                    columns={[
                        {
                            field: 'period',
                            label: __('affiliate.period_column'),
                            render: (item) => (
                                <div className="text-sm font-medium text-gray-900">
                                    {formatDateTime(item.period)}
                                </div>
                            )
                        },
                        {
                            field: 'affiliate',
                            label: __('affiliate.affiliate_column'),
                            render: (item) => (
                                <div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {item.affiliate_name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {item.affiliate_code}
                                    </div>
                                </div>
                            )
                        },
                        {
                            field: 'campaign',
                            label: __('affiliate.campaign_column'),
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.campaign_name || __('affiliate.na')}
                                </div>
                            )
                        },
                        {
                            field: 'clicks',
                            label: __('affiliate.clicks_column'),
                            render: (item) => (
                                <div className="flex items-center text-sm text-gray-900">
                                    <MousePointer className="w-3 h-3 mr-1 text-gray-400" />
                                    {item.clicks?.toLocaleString()}
                                </div>
                            )
                        },
                        {
                            field: 'conversions',
                            label: __('affiliate.conversions_column'),
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.conversions?.toLocaleString()}
                                </div>
                            )
                        },
                        {
                            field: 'conversion_rate',
                            label: __('affiliate.cvr_column'),
                            render: (item) => (
                                <div className="text-sm text-gray-900">
                                    {item.conversion_rate}%
                                </div>
                            )
                        },
                        {
                            field: 'revenue',
                            label: __('affiliate.revenue_column'),
                            render: (item) => (
                                <div className="text-sm font-medium text-gray-900">
                                    {formatCurrency(item.revenue)}
                                </div>
                            )
                        },
                        {
                            field: 'commission',
                            label: __('affiliate.commission_column'),
                            render: (item) => (
                                <div className="text-sm font-medium text-green-600">
                                    {formatCurrency(item.commission)}
                                </div>
                            )
                        }
                    ]}
                    data={revenue.data || []}
                    emptyStateMessage={__('affiliate.no_revenue_data_available')}
                    enableDefaultActions={false}
                />

                {/* Pagination */}
                {revenue.data && revenue.data.length > 0 && revenue.last_page > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow mt-6">
                        <div className="flex-1 flex justify-between sm:hidden">
                            {revenue.prev_page_url && (
                                <a
                                    href={revenue.prev_page_url}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    {__('affiliate.previous')}
                                </a>
                            )}
                            {revenue.next_page_url && (
                                <a
                                    href={revenue.next_page_url}
                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    {__('affiliate.next')}
                                </a>
                            )}
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    {__('affiliate.showing')} <span className="font-medium">{revenue.from}</span> {__('affiliate.to')}{' '}
                                    <span className="font-medium">{revenue.to}</span> {__('affiliate.of')}{' '}
                                    <span className="font-medium">{revenue.total}</span> {__('affiliate.results')}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
