import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import { Save, Settings, Globe, Shield, Mail, Clock, Users } from 'lucide-react';

export default function General({ settings = {} }) {
    const [formData, setFormData] = useState({
        program_enabled: settings.program_enabled || true,
        program_name: settings.program_name || 'Affiliate Program',
        program_description: settings.program_description || '',
        auto_approve_affiliates: settings.auto_approve_affiliates || false,
        require_tax_info: settings.require_tax_info || false,
        fraud_detection_enabled: settings.fraud_detection_enabled || true,
        email_notifications_enabled: settings.email_notifications_enabled || true,
        welcome_email_enabled: settings.welcome_email_enabled || true,
        commission_notification_enabled: settings.commission_notification_enabled || true,
        payment_notification_enabled: settings.payment_notification_enabled || true,
        cookie_duration: settings.cookie_duration || 30,
        conversion_window: settings.conversion_window || 7,
        max_links_per_affiliate: settings.max_links_per_affiliate || 100,
        link_expiry_days: settings.link_expiry_days || 365,
        commission_hold_days: settings.commission_hold_days || 30,
        ip_whitelist_enabled: settings.ip_whitelist_enabled || false,
        referrer_validation_enabled: settings.referrer_validation_enabled || false,
        click_fraud_threshold: settings.click_fraud_threshold || 10,
    });

    const [saving, setSaving] = useState(false);
    const [errors, setErrors] = useState({});

    const handleInputChange = (key, value) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));

        // Clear error when user starts typing
        if (errors[key]) {
            setErrors(prev => ({
                ...prev,
                [key]: null
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);
        setErrors({});

        try {
            await router.post(route('superadmin.affiliate.settings.general.update'), formData, {
                onError: (errors) => {
                    setErrors(errors);
                },
                onFinish: () => {
                    setSaving(false);
                }
            });
        } catch (error) {
            console.error('Error updating settings:', error);
            setSaving(false);
        }
    };

    return (
        <SuperAdminLayout>
            <Head title="General Settings" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">General Settings</h1>
                        <p className="text-gray-600">Configure general affiliate program settings</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Program Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Globe className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Program Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Enable Affiliate Program</label>
                                    <p className="text-sm text-gray-500">Turn the entire affiliate program on or off</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.program_enabled}
                                    onChange={(e) => handleInputChange('program_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <TextInputWithLabel
                                label="Program Name"
                                id="program_name"
                                type="text"
                                value={formData.program_name}
                                onChange={(e) => handleInputChange('program_name', e.target.value)}
                                placeholder="Enter program name"
                                errors={errors.program_name}
                            />

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Program Description
                                </label>
                                <textarea
                                    value={formData.program_description}
                                    onChange={(e) => handleInputChange('program_description', e.target.value)}
                                    rows={3}
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Describe your affiliate program"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Affiliate Management */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Users className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Affiliate Management</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Auto-approve Affiliates</label>
                                    <p className="text-sm text-gray-500">Automatically approve new affiliate applications</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.auto_approve_affiliates}
                                    onChange={(e) => handleInputChange('auto_approve_affiliates', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Require Tax Information</label>
                                    <p className="text-sm text-gray-500">Require affiliates to provide tax information</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.require_tax_info}
                                    onChange={(e) => handleInputChange('require_tax_info', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Security Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Shield className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Security Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Fraud Detection</label>
                                    <p className="text-sm text-gray-500">Enable automatic fraud detection</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.fraud_detection_enabled}
                                    onChange={(e) => handleInputChange('fraud_detection_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">IP Whitelist</label>
                                    <p className="text-sm text-gray-500">Enable IP whitelist validation</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.ip_whitelist_enabled}
                                    onChange={(e) => handleInputChange('ip_whitelist_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Referrer Validation</label>
                                    <p className="text-sm text-gray-500">Validate referrer headers</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.referrer_validation_enabled}
                                    onChange={(e) => handleInputChange('referrer_validation_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Click Fraud Threshold
                                </label>
                                <input
                                    type="number"
                                    min="1"
                                    value={formData.click_fraud_threshold}
                                    onChange={(e) => handleInputChange('click_fraud_threshold', parseInt(e.target.value))}
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <p className="text-xs text-gray-500 mt-1">Maximum clicks per IP per hour</p>
                            </div>
                        </div>
                    </div>

                    {/* Email Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Mail className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Email Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Email Notifications</label>
                                    <p className="text-sm text-gray-500">Send email notifications to affiliates</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.email_notifications_enabled}
                                    onChange={(e) => handleInputChange('email_notifications_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Welcome Email</label>
                                    <p className="text-sm text-gray-500">Send welcome email to new affiliates</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.welcome_email_enabled}
                                    onChange={(e) => handleInputChange('welcome_email_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Commission Notifications</label>
                                    <p className="text-sm text-gray-500">Notify affiliates about commission updates</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.commission_notification_enabled}
                                    onChange={(e) => handleInputChange('commission_notification_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Payment Notifications</label>
                                    <p className="text-sm text-gray-500">Notify affiliates about payments</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.payment_notification_enabled}
                                    onChange={(e) => handleInputChange('payment_notification_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Advanced Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Clock className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Advanced Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <TextInputWithLabel
                                    label="Cookie Duration (days)"
                                    id="cookie_duration"
                                    type="number"
                                    min="1"
                                    max="365"
                                    value={formData.cookie_duration}
                                    onChange={(e) => handleInputChange('cookie_duration', parseInt(e.target.value))}
                                />
                                <TextInputWithLabel
                                    label="Conversion Window (days)"
                                    id="conversion_window"
                                    type="number"
                                    min="1"
                                    value={formData.conversion_window}
                                    onChange={(e) => handleInputChange('conversion_window', parseInt(e.target.value))}
                                />
                                <TextInputWithLabel
                                    label="Max Links per Affiliate"
                                    id="max_links_per_affiliate"
                                    type="number"
                                    min="1"
                                    value={formData.max_links_per_affiliate}
                                    onChange={(e) => handleInputChange('max_links_per_affiliate', parseInt(e.target.value))}
                                />
                                <TextInputWithLabel
                                    label="Link Expiry (days)"
                                    id="link_expiry_days"
                                    type="number"
                                    min="1"
                                    value={formData.link_expiry_days}
                                    onChange={(e) => handleInputChange('link_expiry_days', parseInt(e.target.value))}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Commission Hold Period (days)
                                </label>
                                <input
                                    type="number"
                                    min="0"
                                    value={formData.commission_hold_days}
                                    onChange={(e) => handleInputChange('commission_hold_days', parseInt(e.target.value))}
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <p className="text-xs text-gray-500 mt-1">Days to hold commissions before making them available for withdrawal</p>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <PrimaryButton
                            type="submit"
                            disabled={saving}
                        >
                            <Save className="w-4 h-4 mr-2" />
                            {saving ? 'Saving...' : 'Save Settings'}
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
