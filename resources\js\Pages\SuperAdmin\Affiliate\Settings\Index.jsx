import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { Settings, Save, RefreshCw, Shield, DollarSign, Users, Mail } from 'lucide-react';

export default function Index({ settings = {} }) {
    const [formData, setFormData] = useState({
        // General Settings
        affiliate_program_enabled: settings.affiliate_program_enabled || false,
        auto_approve_affiliates: settings.auto_approve_affiliates || false,
        require_tax_info: settings.require_tax_info || false,

        // Commission Settings
        default_commission_rate: settings.default_commission_rate || 5,
        min_commission_rate: settings.min_commission_rate || 1,
        max_commission_rate: settings.max_commission_rate || 50,
        commission_calculation_method: settings.commission_calculation_method || 'percentage',

        // Payment Settings
        min_payout_amount: settings.min_payout_amount || 100000,
        payment_schedule: settings.payment_schedule || 'monthly',
        payment_methods: settings.payment_methods || ['bank_transfer'],

        // Tracking Settings
        cookie_duration: settings.cookie_duration || 30,
        attribution_model: settings.attribution_model || 'last_click',
        track_subids: settings.track_subids || false,

        // Email Settings
        welcome_email_enabled: settings.welcome_email_enabled || true,
        commission_notification_enabled: settings.commission_notification_enabled || true,
        payment_notification_enabled: settings.payment_notification_enabled || true,

        // Security Settings
        fraud_detection_enabled: settings.fraud_detection_enabled || true,
        ip_whitelist_enabled: settings.ip_whitelist_enabled || false,
        referrer_validation_enabled: settings.referrer_validation_enabled || false
    });

    const [saving, setSaving] = useState(false);

    const handleInputChange = (key, value) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        try {
            await router.post(route('superadmin.affiliate.settings.update'), formData);
            alert('Settings updated successfully!');
        } catch (error) {
            alert('Error updating settings. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    const paymentScheduleOptions = [
        { value: 'weekly', label: 'Weekly' },
        { value: 'monthly', label: 'Monthly' },
        { value: 'quarterly', label: 'Quarterly' }
    ];

    const attributionModelOptions = [
        { value: 'first_click', label: 'First Click' },
        { value: 'last_click', label: 'Last Click' },
        { value: 'linear', label: 'Linear' }
    ];

    const commissionMethodOptions = [
        { value: 'percentage', label: 'Percentage' },
        { value: 'fixed', label: 'Fixed Amount' },
        { value: 'tiered', label: 'Tiered' }
    ];

    return (
        <SuperAdminLayout>
            <Head title="Affiliate Settings" />

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Affiliate Settings</h1>
                        <p className="text-gray-600">Configure affiliate program settings</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* General Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Settings className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">General Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Enable Affiliate Program</label>
                                    <p className="text-sm text-gray-500">Allow users to join the affiliate program</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.affiliate_program_enabled}
                                    onChange={(e) => handleInputChange('affiliate_program_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Auto-approve Affiliates</label>
                                    <p className="text-sm text-gray-500">Automatically approve new affiliate applications</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.auto_approve_affiliates}
                                    onChange={(e) => handleInputChange('auto_approve_affiliates', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Require Tax Information</label>
                                    <p className="text-sm text-gray-500">Require affiliates to provide tax information</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.require_tax_info}
                                    onChange={(e) => handleInputChange('require_tax_info', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Commission Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Commission Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Default Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.default_commission_rate}
                                        onChange={(e) => handleInputChange('default_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Min Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.min_commission_rate}
                                        onChange={(e) => handleInputChange('min_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Max Commission Rate (%)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        value={formData.max_commission_rate}
                                        onChange={(e) => handleInputChange('max_commission_rate', parseFloat(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Commission Calculation Method
                                </label>
                                <select
                                    value={formData.commission_calculation_method}
                                    onChange={(e) => handleInputChange('commission_calculation_method', e.target.value)}
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    {commissionMethodOptions.map(option => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Payment Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Users className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Payment Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Minimum Payout Amount (VND)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        value={formData.min_payout_amount}
                                        onChange={(e) => handleInputChange('min_payout_amount', parseInt(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Payment Schedule
                                    </label>
                                    <select
                                        value={formData.payment_schedule}
                                        onChange={(e) => handleInputChange('payment_schedule', e.target.value)}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        {paymentScheduleOptions.map(option => (
                                            <option key={option.value} value={option.value}>
                                                {option.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Tracking Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <RefreshCw className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Tracking Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Cookie Duration (days)
                                    </label>
                                    <input
                                        type="number"
                                        min="1"
                                        max="365"
                                        value={formData.cookie_duration}
                                        onChange={(e) => handleInputChange('cookie_duration', parseInt(e.target.value))}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Attribution Model
                                    </label>
                                    <select
                                        value={formData.attribution_model}
                                        onChange={(e) => handleInputChange('attribution_model', e.target.value)}
                                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        {attributionModelOptions.map(option => (
                                            <option key={option.value} value={option.value}>
                                                {option.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Track Sub IDs</label>
                                    <p className="text-sm text-gray-500">Allow affiliates to use sub IDs for tracking</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.track_subids}
                                    onChange={(e) => handleInputChange('track_subids', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Email Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Mail className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Email Notifications</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Welcome Email</label>
                                    <p className="text-sm text-gray-500">Send welcome email to new affiliates</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.welcome_email_enabled}
                                    onChange={(e) => handleInputChange('welcome_email_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Commission Notifications</label>
                                    <p className="text-sm text-gray-500">Notify affiliates when commissions are earned</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.commission_notification_enabled}
                                    onChange={(e) => handleInputChange('commission_notification_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Payment Notifications</label>
                                    <p className="text-sm text-gray-500">Notify affiliates when payments are processed</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.payment_notification_enabled}
                                    onChange={(e) => handleInputChange('payment_notification_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Security Settings */}
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <Shield className="h-5 w-5 text-gray-500" />
                                <h2 className="text-lg font-medium text-gray-900">Security Settings</h2>
                            </div>
                        </div>
                        <div className="p-6 space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Fraud Detection</label>
                                    <p className="text-sm text-gray-500">Enable automatic fraud detection</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.fraud_detection_enabled}
                                    onChange={(e) => handleInputChange('fraud_detection_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">IP Whitelist</label>
                                    <p className="text-sm text-gray-500">Restrict access to whitelisted IPs only</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.ip_whitelist_enabled}
                                    onChange={(e) => handleInputChange('ip_whitelist_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="text-sm font-medium text-gray-900">Referrer Validation</label>
                                    <p className="text-sm text-gray-500">Validate referrer URLs for clicks</p>
                                </div>
                                <input
                                    type="checkbox"
                                    checked={formData.referrer_validation_enabled}
                                    onChange={(e) => handleInputChange('referrer_validation_enabled', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end">
                        <button
                            type="submit"
                            disabled={saving}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg flex items-center space-x-2"
                        >
                            <Save className="w-4 h-4" />
                            <span>{saving ? 'Saving...' : 'Save Settings'}</span>
                        </button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
