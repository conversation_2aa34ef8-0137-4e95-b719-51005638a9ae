import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    MousePointer,
    Globe,
    Smartphone,
    Monitor,
    Tablet,
    BarChart3,
    Filter,
    Calendar,
    MapPin,
    Eye
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatDateTime } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import StatCards from '@/Components/Dashboard/StatCards';

export default function Clicks({ clicks, stats, filters }) {
    const [isLoading, setIsLoading] = useState(false);
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [selectedAffiliate, setSelectedAffiliate] = useState(filters.affiliate_id || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');

    const handleFilterChange = () => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.tracking.clicks'), {
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const getDeviceIcon = (deviceType) => {
        switch (deviceType) {
            case 'desktop':
                return Monitor;
            case 'mobile':
                return Smartphone;
            case 'tablet':
                return Tablet;
            default:
                return Monitor;
        }
    };

    const getDeviceColor = (deviceType) => {
        switch (deviceType) {
            case 'desktop':
                return 'text-blue-600';
            case 'mobile':
                return 'text-green-600';
            case 'tablet':
                return 'text-purple-600';
            default:
                return 'text-gray-600';
        }
    };

    const statCardsData = [
        {
            title: 'Tổng Clicks',
            value: stats.total_clicks.toLocaleString(),
            icon: MousePointer,
            color: 'blue',
            trend: null
        },
        {
            title: 'Unique Clicks',
            value: stats.unique_clicks.toLocaleString(),
            icon: Eye,
            color: 'green',
            trend: null
        },
        {
            title: 'Click-through Rate',
            value: `${stats.click_through_rate}%`,
            icon: BarChart3,
            color: 'purple',
            trend: null
        },
        {
            title: 'Desktop',
            value: `${stats.device_breakdown.desktop}%`,
            icon: Monitor,
            color: 'indigo',
            trend: null
        },
        {
            title: 'Mobile',
            value: `${stats.device_breakdown.mobile}%`,
            icon: Smartphone,
            color: 'emerald',
            trend: null
        },
        {
            title: 'Tablet',
            value: `${stats.device_breakdown.tablet}%`,
            icon: Tablet,
            color: 'orange',
            trend: null
        }
    ];

    const columns = [
        {
            field: 'affiliate',
            label: 'Affiliate',
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {click.affiliate?.user?.name}
                    </div>
                    <div className="text-gray-500">
                        {click.affiliate?.referral_code}
                    </div>
                </div>
            )
        },
        {
            field: 'campaign',
            label: 'Chiến dịch',
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {click.campaign?.name || '-'}
                    </div>
                    <div className="text-gray-500">
                        {click.campaign?.campaign_code || '-'}
                    </div>
                </div>
            )
        },
        {
            field: 'ip_address',
            label: 'IP Address',
            render: (click) => (
                <span className="text-sm font-mono text-gray-900">
                    {click.ip_address}
                </span>
            )
        },
        {
            field: 'location',
            label: 'Vị trí',
            render: (click) => (
                <div className="flex items-center text-sm">
                    <MapPin className="w-3 h-3 mr-1 text-gray-400" />
                    <span className="text-gray-900">
                        {click.country_flag} {click.city || click.country || 'Unknown'}
                    </span>
                </div>
            )
        },
        {
            field: 'device_type',
            label: 'Thiết bị',
            render: (click) => {
                const DeviceIcon = getDeviceIcon(click.device_type);
                return (
                    <div className="flex items-center text-sm">
                        <DeviceIcon className={`w-4 h-4 mr-2 ${getDeviceColor(click.device_type)}`} />
                        <div>
                            <div className="text-gray-900 capitalize">
                                {click.device_type}
                            </div>
                            <div className="text-gray-500 text-xs">
                                {click.browser}
                            </div>
                        </div>
                    </div>
                );
            }
        },
        {
            field: 'referrer_url',
            label: 'Nguồn',
            render: (click) => (
                <div className="text-sm">
                    {click.referrer_url ? (
                        <a
                            href={click.referrer_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 truncate block max-w-xs"
                            title={click.referrer_url}
                        >
                            {new URL(click.referrer_url).hostname}
                        </a>
                    ) : (
                        <span className="text-gray-500">Direct</span>
                    )}
                </div>
            )
        },
        {
            field: 'is_unique',
            label: 'Unique',
            render: (click) => (
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${click.is_unique
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                    {click.is_unique ? 'Yes' : 'No'}
                </span>
            )
        },
        {
            field: 'clicked_at',
            label: 'Thời gian',
            sortable: true,
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900">
                        {formatDateTime(click.clicked_at)}
                    </div>
                </div>
            )
        }
    ];

    return (
        <SuperAdminLayout title="Theo dõi Clicks">
            <Head title="Theo dõi Clicks" />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <MousePointer className="w-6 h-6 mr-3" />
                            Theo dõi Clicks
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Thống kê chi tiết về clicks từ affiliate links
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-gray-600" />
                        <h3 className="text-lg font-medium text-gray-900">Bộ lọc</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Từ ngày
                            </label>
                            <input
                                type="date"
                                value={dateRange.start_date}
                                onChange={(e) => setDateRange({ ...dateRange, start_date: e.target.value })}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Đến ngày
                            </label>
                            <input
                                type="date"
                                value={dateRange.end_date}
                                onChange={(e) => setDateRange({ ...dateRange, end_date: e.target.value })}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Affiliate
                            </label>
                            <select
                                value={selectedAffiliate}
                                onChange={(e) => setSelectedAffiliate(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="">Tất cả affiliate</option>
                                {/* TODO: Add affiliate options */}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Chiến dịch
                            </label>
                            <select
                                value={selectedCampaign}
                                onChange={(e) => setSelectedCampaign(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="">Tất cả chiến dịch</option>
                                {/* TODO: Add campaign options */}
                            </select>
                        </div>

                        <div className="flex items-end">
                            <button
                                onClick={handleFilterChange}
                                className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Áp dụng
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <StatCards stats={statCardsData} />

                {/* Top Sources & Countries */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Top Sources */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Top Nguồn Traffic
                        </h3>
                        <div className="space-y-3">
                            {stats.top_sources && stats.top_sources.length > 0 ? (
                                stats.top_sources.map((source, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <Globe className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-900">
                                                {source.domain || 'Direct'}
                                            </span>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {source.clicks} clicks
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có dữ liệu</p>
                            )}
                        </div>
                    </div>

                    {/* Top Countries */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Top Quốc gia
                        </h3>
                        <div className="space-y-3">
                            {stats.top_countries && stats.top_countries.length > 0 ? (
                                stats.top_countries.map((country, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <span className="text-lg">{country.flag}</span>
                                            <span className="text-sm text-gray-900">
                                                {country.name}
                                            </span>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {country.clicks} clicks
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có dữ liệu</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Clicks Table */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">
                            Chi tiết Clicks
                        </h3>
                    </div>
                    <DataTable
                        data={clicks}
                        columns={columns}
                        onSort={(field, direction) => {
                            setIsLoading(true);
                            router.get(route('superadmin.affiliate.tracking.clicks'), {
                                ...filters,
                                sort: field,
                                direction: direction
                            }, {
                                preserveState: true,
                                preserveScroll: true,
                                onFinish: () => setIsLoading(false)
                            });
                        }}
                        currentSort={{ field: filters.sort, direction: filters.direction }}
                    />
                </div>
            </div>
        </SuperAdminLayout>
    );
}
