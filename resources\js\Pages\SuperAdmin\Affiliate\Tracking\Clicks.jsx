import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import {
    MousePointer,
    Globe,
    Smartphone,
    Monitor,
    Tablet,
    BarChart3,
    Filter,
    Calendar,
    MapPin,
    Eye,
    Search
} from 'lucide-react';
import { __ } from '@/utils/lang';
import { formatDateTime } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';

export default function Clicks({ clicks, stats, filters }) {
    const [isLoading, setIsLoading] = useState(false);
    const [dateRange, setDateRange] = useState({
        start_date: filters.start_date || '',
        end_date: filters.end_date || ''
    });
    const [selectedAffiliate, setSelectedAffiliate] = useState(filters.affiliate_id || '');
    const [selectedCampaign, setSelectedCampaign] = useState(filters.campaign_id || '');

    const handleFilterChange = () => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.tracking.clicks'), {
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            affiliate_id: selectedAffiliate,
            campaign_id: selectedCampaign
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const getDeviceIcon = (deviceType) => {
        switch (deviceType) {
            case 'desktop':
                return Monitor;
            case 'mobile':
                return Smartphone;
            case 'tablet':
                return Tablet;
            default:
                return Monitor;
        }
    };

    const getDeviceColor = (deviceType) => {
        switch (deviceType) {
            case 'desktop':
                return 'text-blue-600';
            case 'mobile':
                return 'text-green-600';
            case 'tablet':
                return 'text-purple-600';
            default:
                return 'text-gray-600';
        }
    };

    const columns = [
        {
            field: 'affiliate',
            label: __('affiliate.affiliate'),
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {click.affiliate?.user?.name}
                    </div>
                    <div className="text-gray-500">
                        {click.affiliate?.referral_code}
                    </div>
                </div>
            )
        },
        {
            field: 'campaign',
            label: __('affiliate.campaign'),
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {click.campaign?.name || '-'}
                    </div>
                    <div className="text-gray-500">
                        {click.campaign?.campaign_code || '-'}
                    </div>
                </div>
            )
        },
        {
            field: 'ip_address',
            label: __('affiliate.ip_address'),
            render: (click) => (
                <span className="text-sm font-mono text-gray-900">
                    {click.ip_address}
                </span>
            )
        },
        {
            field: 'location',
            label: __('affiliate.location'),
            render: (click) => (
                <div className="flex items-center text-sm">
                    <MapPin className="w-3 h-3 mr-1 text-gray-400" />
                    <span className="text-gray-900">
                        {click.country_flag} {click.city || click.country || 'Unknown'}
                    </span>
                </div>
            )
        },
        {
            field: 'device_type',
            label: __('affiliate.device'),
            render: (click) => {
                const DeviceIcon = getDeviceIcon(click.device_type);
                return (
                    <div className="flex items-center text-sm">
                        <DeviceIcon className={`w-4 h-4 mr-2 ${getDeviceColor(click.device_type)}`} />
                        <div>
                            <div className="text-gray-900 capitalize">
                                {click.device_type}
                            </div>
                            <div className="text-gray-500 text-xs">
                                {click.browser}
                            </div>
                        </div>
                    </div>
                );
            }
        },
        {
            field: 'referrer_url',
            label: __('affiliate.source'),
            render: (click) => (
                <div className="text-sm">
                    {click.referrer_url ? (
                        <a
                            href={click.referrer_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 truncate block max-w-xs"
                            title={click.referrer_url}
                        >
                            {new URL(click.referrer_url).hostname}
                        </a>
                    ) : (
                        <span className="text-gray-500">{__('affiliate.direct')}</span>
                    )}
                </div>
            )
        },
        {
            field: 'is_unique',
            label: __('affiliate.unique'),
            render: (click) => (
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${click.is_unique
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                    }`}>
                    {click.is_unique ? __('affiliate.yes') : __('affiliate.no')}
                </span>
            )
        },
        {
            field: 'clicked_at',
            label: __('affiliate.clicked_time'),
            sortable: true,
            render: (click) => (
                <div className="text-sm">
                    <div className="text-gray-900">
                        {formatDateTime(click.clicked_at)}
                    </div>
                </div>
            )
        }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.track_clicks')}>
            <Head title={__('affiliate.track_clicks')} />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <MousePointer className="w-6 h-6 mr-3" />
                            {__('affiliate.track_clicks')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.track_clicks_description')}
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <MousePointer className="w-8 h-8 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.total_clicks')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.total_clicks.toLocaleString()}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <Eye className="w-8 h-8 text-green-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.unique_clicks')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.unique_clicks.toLocaleString()}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <BarChart3 className="w-8 h-8 text-purple-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.click_through_rate')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.click_through_rate}%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <Monitor className="w-8 h-8 text-indigo-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.desktop')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.device_breakdown.desktop}%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <Smartphone className="w-8 h-8 text-emerald-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.mobile')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.device_breakdown.mobile}%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <Tablet className="w-8 h-8 text-orange-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        {__('affiliate.tablet')}
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {stats.device_breakdown.tablet}%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-lg shadow p-6 mb-6">
                    <div className="flex items-center space-x-2 mb-4">
                        <Filter className="w-5 h-5 text-gray-600" />
                        <h3 className="text-lg font-medium text-gray-900">{__('affiliate.filters')}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <TextInputWithLabel
                                label={__('affiliate.from_date')}
                                type="date"
                                value={dateRange.start_date}
                                onChange={(e) => setDateRange({ ...dateRange, start_date: e.target.value })}
                            />
                        </div>

                        <div>
                            <TextInputWithLabel
                                label={__('affiliate.to_date')}
                                type="date"
                                value={dateRange.end_date}
                                onChange={(e) => setDateRange({ ...dateRange, end_date: e.target.value })}
                            />
                        </div>

                        <div>
                            <SelectWithLabel
                                label={__('affiliate.affiliate')}
                                value={selectedAffiliate}
                                onChange={(e) => setSelectedAffiliate(e.target.value)}
                            >
                                <option value="">{__('affiliate.all_affiliates')}</option>
                                {/* TODO: Add affiliate options */}
                            </SelectWithLabel>
                        </div>

                        <div>
                            <SelectWithLabel
                                label={__('affiliate.campaign')}
                                value={selectedCampaign}
                                onChange={(e) => setSelectedCampaign(e.target.value)}
                            >
                                <option value="">{__('affiliate.all_campaigns')}</option>
                                {/* TODO: Add campaign options */}
                            </SelectWithLabel>
                        </div>

                        <div className="md:col-span-2">
                            <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                                {__('affiliate.apply_filters')}
                            </label>
                            <PrimaryButton
                                onClick={handleFilterChange}
                                className="w-full h-10"
                            >
                                <Search className="w-4 h-4 mr-2" />
                                {__('affiliate.apply_filters')}
                            </PrimaryButton>
                        </div>
                    </div>
                </div>

                {/* Top Sources & Countries */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Top Sources */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Top Nguồn Traffic
                        </h3>
                        <div className="space-y-3">
                            {stats.top_sources && stats.top_sources.length > 0 ? (
                                stats.top_sources.map((source, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <Globe className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-900">
                                                {source.domain || 'Direct'}
                                            </span>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {source.clicks} clicks
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có dữ liệu</p>
                            )}
                        </div>
                    </div>

                    {/* Top Countries */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Top Quốc gia
                        </h3>
                        <div className="space-y-3">
                            {stats.top_countries && stats.top_countries.length > 0 ? (
                                stats.top_countries.map((country, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <span className="text-lg">{country.flag}</span>
                                            <span className="text-sm text-gray-900">
                                                {country.name}
                                            </span>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {country.clicks} clicks
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-gray-500 text-sm">Chưa có dữ liệu</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Clicks Table */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">
                            Chi tiết Clicks
                        </h3>
                    </div>
                    <DataTable
                        data={clicks}
                        columns={columns}
                        onSort={(field, direction) => {
                            setIsLoading(true);
                            router.get(route('superadmin.affiliate.tracking.clicks'), {
                                ...filters,
                                sort: field,
                                direction: direction
                            }, {
                                preserveState: true,
                                preserveScroll: true,
                                onFinish: () => setIsLoading(false)
                            });
                        }}
                        currentSort={{ field: filters.sort, direction: filters.direction }}
                    />
                </div>
            </div>
        </SuperAdminLayout>
    );
}
