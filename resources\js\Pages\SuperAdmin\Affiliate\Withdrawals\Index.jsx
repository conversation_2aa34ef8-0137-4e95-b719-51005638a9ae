import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatDateTime, formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { 
    CreditCard, 
    Check, 
    X, 
    Clock, 
    DollarSign,
    User,
    Calendar,
    AlertCircle
} from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TableFilterHeader from '@/Components/TableFilterHeader';

export default function Index({ withdrawals, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isLoading, setIsLoading] = useState(false);
    const [actionModal, setActionModal] = useState(null);
    const [rejectionReason, setRejectionReason] = useState('');
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), {
            search: filters.search,
            status: filters.status,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const openActionModal = (action, withdrawalId) => {
        setActionModal({ action, withdrawalId });
        setRejectionReason('');
    };

    const closeActionModal = () => {
        setActionModal(null);
        setRejectionReason('');
    };

    const handleApprove = (withdrawalId) => {
        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.approve', withdrawalId), {}, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const handleReject = (withdrawalId) => {
        if (!rejectionReason.trim()) {
            alert('Vui lòng nhập lý do từ chối');
            return;
        }
        
        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.reject', withdrawalId), {
            reason: rejectionReason
        }, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const handleProcess = (withdrawalId) => {
        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.process', withdrawalId), {}, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
                return 'green';
            case 'processing':
                return 'blue';
            case 'approved':
                return 'cyan';
            case 'pending':
                return 'yellow';
            case 'rejected':
                return 'red';
            case 'cancelled':
                return 'gray';
            default:
                return 'gray';
        }
    };

    const getPaymentMethodColor = (method) => {
        switch (method) {
            case 'bank_transfer':
                return 'bg-blue-100 text-blue-800';
            case 'paypal':
                return 'bg-indigo-100 text-indigo-800';
            case 'crypto':
                return 'bg-purple-100 text-purple-800';
            case 'momo':
                return 'bg-pink-100 text-pink-800';
            case 'zalopay':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const columns = [
        {
            field: 'affiliate',
            label: 'Affiliate',
            render: (withdrawal) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <User className="w-5 h-5 text-indigo-600" />
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {withdrawal.affiliate?.user?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {withdrawal.affiliate?.user?.email}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'amount',
            label: 'Số tiền',
            sortable: true,
            render: (withdrawal) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {formatCurrency(withdrawal.amount)}
                    </div>
                    {withdrawal.fee > 0 && (
                        <div className="text-gray-500">
                            Phí: {formatCurrency(withdrawal.fee)}
                        </div>
                    )}
                    <div className="text-green-600 font-medium">
                        Thực nhận: {formatCurrency(withdrawal.net_amount)}
                    </div>
                </div>
            )
        },
        {
            field: 'payment_method',
            label: 'Phương thức',
            sortable: true,
            render: (withdrawal) => (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentMethodColor(withdrawal.payment_method)}`}>
                    {withdrawal.payment_method}
                </span>
            )
        },
        {
            field: 'status',
            label: 'Trạng thái',
            sortable: true,
            render: (withdrawal) => (
                <StatusBadge status={withdrawal.status} color={getStatusColor(withdrawal.status)} />
            )
        },
        {
            field: 'requested_at',
            label: 'Ngày yêu cầu',
            sortable: true,
            render: (withdrawal) => (
                <div className="text-sm">
                    <div className="text-gray-900 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDateTime(withdrawal.requested_at)}
                    </div>
                </div>
            )
        },
        {
            field: 'transaction_id',
            label: 'Mã giao dịch',
            render: (withdrawal) => (
                <span className="text-sm text-gray-900 font-mono">
                    {withdrawal.transaction_id || '-'}
                </span>
            )
        },
        {
            field: 'actions',
            label: 'Thao tác',
            render: (withdrawal) => (
                <div className="flex items-center space-x-2">
                    {withdrawal.status === 'pending' && (
                        <>
                            <button
                                onClick={() => openActionModal('approve', withdrawal.id)}
                                className="text-green-600 hover:text-green-900"
                                title="Duyệt"
                            >
                                <Check className="w-4 h-4" />
                            </button>
                            <button
                                onClick={() => openActionModal('reject', withdrawal.id)}
                                className="text-red-600 hover:text-red-900"
                                title="Từ chối"
                            >
                                <X className="w-4 h-4" />
                            </button>
                        </>
                    )}
                    {withdrawal.status === 'approved' && (
                        <button
                            onClick={() => openActionModal('process', withdrawal.id)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Xử lý"
                        >
                            <Clock className="w-4 h-4" />
                        </button>
                    )}
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: 'Tất cả trạng thái' },
        { value: 'pending', label: 'Chờ duyệt' },
        { value: 'approved', label: 'Đã duyệt' },
        { value: 'processing', label: 'Đang xử lý' },
        { value: 'completed', label: 'Hoàn thành' },
        { value: 'rejected', label: 'Từ chối' },
        { value: 'cancelled', label: 'Đã hủy' }
    ];

    return (
        <SuperAdminLayout title="Quản lý Rút tiền">
            <Head title="Quản lý Rút tiền" />
            
            {isLoading && <Loading />}
            
            <div className="space-y-6">
                <TableFilterHeader
                    title="Quản lý Yêu cầu Rút tiền"
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    onSearch={handleSearch}
                    showCreateButton={false}
                    filters={[
                        {
                            type: 'select',
                            value: filters.status || '',
                            onChange: handleStatusFilter,
                            options: statusOptions,
                            placeholder: 'Lọc theo trạng thái'
                        }
                    ]}
                />

                <DataTable
                    data={withdrawals}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Action Modal */}
            {actionModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex items-center mb-4">
                                {actionModal.action === 'approve' && <Check className="w-6 h-6 text-green-600 mr-2" />}
                                {actionModal.action === 'reject' && <X className="w-6 h-6 text-red-600 mr-2" />}
                                {actionModal.action === 'process' && <Clock className="w-6 h-6 text-blue-600 mr-2" />}
                                <h3 className="text-lg font-medium text-gray-900">
                                    {actionModal.action === 'approve' && 'Duyệt yêu cầu rút tiền'}
                                    {actionModal.action === 'reject' && 'Từ chối yêu cầu rút tiền'}
                                    {actionModal.action === 'process' && 'Xử lý yêu cầu rút tiền'}
                                </h3>
                            </div>
                            
                            <div className="mb-4">
                                <p className="text-sm text-gray-500">
                                    {actionModal.action === 'approve' && 'Bạn có chắc chắn muốn duyệt yêu cầu rút tiền này?'}
                                    {actionModal.action === 'reject' && 'Vui lòng nhập lý do từ chối:'}
                                    {actionModal.action === 'process' && 'Đánh dấu yêu cầu này là đang được xử lý?'}
                                </p>
                            </div>

                            {actionModal.action === 'reject' && (
                                <div className="mb-4">
                                    <textarea
                                        value={rejectionReason}
                                        onChange={(e) => setRejectionReason(e.target.value)}
                                        rows={3}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder="Nhập lý do từ chối..."
                                        required
                                    />
                                </div>
                            )}

                            <div className="flex justify-end space-x-4">
                                <button
                                    onClick={closeActionModal}
                                    className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                >
                                    Hủy
                                </button>
                                <button
                                    onClick={() => {
                                        if (actionModal.action === 'approve') {
                                            handleApprove(actionModal.withdrawalId);
                                        } else if (actionModal.action === 'reject') {
                                            handleReject(actionModal.withdrawalId);
                                        } else if (actionModal.action === 'process') {
                                            handleProcess(actionModal.withdrawalId);
                                        }
                                    }}
                                    className={`px-4 py-2 text-white text-base font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                                        actionModal.action === 'approve' 
                                            ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                                            : actionModal.action === 'reject'
                                            ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                                            : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                                    }`}
                                >
                                    {actionModal.action === 'approve' && 'Duyệt'}
                                    {actionModal.action === 'reject' && 'Từ chối'}
                                    {actionModal.action === 'process' && 'Xử lý'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </SuperAdminLayout>
    );
}
