import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatDateTime, formatCurrency } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import {
    CreditCard,
    Check,
    X,
    Clock,
    DollarSign,
    User,
    Calendar,
    AlertCircle,
    Search
} from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import SelectWithLabel from '@/Components/SelectWithLabel';
import PrimaryButton from '@/Components/PrimaryButton';

export default function Index({ withdrawals, filters }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isLoading, setIsLoading] = useState(false);
    const [actionModal, setActionModal] = useState(null);
    const [rejectionReason, setRejectionReason] = useState('');
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.affiliate.withdrawals.index'), {
            search: filters.search,
            status: filters.status,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const openActionModal = (action, withdrawalId) => {
        setActionModal({ action, withdrawalId });
        setRejectionReason('');
    };

    const closeActionModal = () => {
        setActionModal(null);
        setRejectionReason('');
    };

    const handleApprove = (withdrawalId) => {
        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.approve', withdrawalId), {}, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const handleReject = (withdrawalId) => {
        if (!rejectionReason.trim()) {
            alert(__('affiliate.enter_rejection_reason'));
            return;
        }

        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.reject', withdrawalId), {
            reason: rejectionReason
        }, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const handleProcess = (withdrawalId) => {
        setIsLoading(true);
        router.post(route('superadmin.affiliate.withdrawals.process', withdrawalId), {}, {
            onFinish: () => {
                setIsLoading(false);
                closeActionModal();
            }
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
                return 'green';
            case 'processing':
                return 'blue';
            case 'approved':
                return 'cyan';
            case 'pending':
                return 'yellow';
            case 'rejected':
                return 'red';
            case 'cancelled':
                return 'gray';
            default:
                return 'gray';
        }
    };

    const getPaymentMethodColor = (method) => {
        switch (method) {
            case 'bank_transfer':
                return 'bg-blue-100 text-blue-800';
            case 'paypal':
                return 'bg-indigo-100 text-indigo-800';
            case 'crypto':
                return 'bg-purple-100 text-purple-800';
            case 'momo':
                return 'bg-pink-100 text-pink-800';
            case 'zalopay':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const columns = [
        {
            field: 'affiliate',
            label: __('affiliate.affiliate'),
            render: (withdrawal) => (
                <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <User className="w-5 h-5 text-indigo-600" />
                        </div>
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                            {withdrawal.affiliate?.user?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                            {withdrawal.affiliate?.user?.email}
                        </div>
                    </div>
                </div>
            )
        },
        {
            field: 'amount',
            label: __('affiliate.amount'),
            sortable: true,
            render: (withdrawal) => (
                <div className="text-sm">
                    <div className="text-gray-900 font-medium">
                        {formatCurrency(withdrawal.amount)}
                    </div>
                    {withdrawal.fee > 0 && (
                        <div className="text-gray-500">
                            {__('affiliate.fee')}: {formatCurrency(withdrawal.fee)}
                        </div>
                    )}
                    <div className="text-green-600 font-medium">
                        {__('affiliate.net_amount')}: {formatCurrency(withdrawal.net_amount)}
                    </div>
                </div>
            )
        },
        {
            field: 'payment_method',
            label: __('affiliate.payment_method'),
            sortable: true,
            render: (withdrawal) => (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentMethodColor(withdrawal.payment_method)}`}>
                    {withdrawal.payment_method}
                </span>
            )
        },
        {
            field: 'status',
            label: __('affiliate.status'),
            sortable: true,
            render: (withdrawal) => (
                <StatusBadge status={withdrawal.status} color={getStatusColor(withdrawal.status)} />
            )
        },
        {
            field: 'requested_at',
            label: __('affiliate.request_date'),
            sortable: true,
            render: (withdrawal) => (
                <div className="text-sm">
                    <div className="text-gray-900 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDateTime(withdrawal.requested_at)}
                    </div>
                </div>
            )
        },
        {
            field: 'transaction_id',
            label: __('affiliate.transaction_id'),
            render: (withdrawal) => (
                <span className="text-sm text-gray-900 font-mono">
                    {withdrawal.transaction_id || '-'}
                </span>
            )
        },
        {
            field: 'actions',
            label: __('affiliate.actions'),
            render: (withdrawal) => (
                <div className="flex items-center space-x-2">
                    {withdrawal.status === 'pending' && (
                        <>
                            <button
                                onClick={() => openActionModal('approve', withdrawal.id)}
                                className="text-green-600 hover:text-green-900"
                                title={__('affiliate.approve_withdrawal')}
                            >
                                <Check className="w-4 h-4" />
                            </button>
                            <button
                                onClick={() => openActionModal('reject', withdrawal.id)}
                                className="text-red-600 hover:text-red-900"
                                title={__('affiliate.reject_withdrawal')}
                            >
                                <X className="w-4 h-4" />
                            </button>
                        </>
                    )}
                    {withdrawal.status === 'approved' && (
                        <button
                            onClick={() => openActionModal('process', withdrawal.id)}
                            className="text-blue-600 hover:text-blue-900"
                            title={__('affiliate.process_withdrawal')}
                        >
                            <Clock className="w-4 h-4" />
                        </button>
                    )}
                </div>
            )
        }
    ];

    const statusOptions = [
        { value: '', label: __('affiliate.all_status') },
        { value: 'pending', label: __('affiliate.pending') },
        { value: 'approved', label: __('affiliate.approved') },
        { value: 'processing', label: __('affiliate.processing') },
        { value: 'completed', label: __('affiliate.completed') },
        { value: 'rejected', label: __('affiliate.rejected') },
        { value: 'cancelled', label: __('affiliate.cancelled') }
    ];

    return (
        <SuperAdminLayout title={__('affiliate.manage_withdrawals')}>
            <Head title={__('affiliate.manage_withdrawals')} />

            {isLoading && <Loading />}

            <div className="p-4 bg-white rounded-lg shadow-md">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                            <CreditCard className="w-6 h-6 mr-3" />
                            {__('affiliate.manage_withdrawals')}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {__('affiliate.manage_withdrawals_description')}
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <TextInputWithLabel
                            label={__('affiliate.search_button')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
                            placeholder={__('affiliate.search_placeholder')}
                            icon={<Search className="w-4 h-4" />}
                        />
                    </div>

                    <SelectWithLabel
                        label={__('affiliate.status')}
                        value={filters.status || ''}
                        onChange={(e) => handleStatusFilter(e.target.value)}
                    >
                        {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </SelectWithLabel>

                    <div>
                        <label className="text-sm block mb-1 font-medium text-gray-700 opacity-0">
                            {__('affiliate.search_button')}
                        </label>
                        <PrimaryButton
                            onClick={handleSearch}
                            className="w-full h-10"
                        >
                            <Search className="w-4 h-4 mr-2" />
                            {__('affiliate.search_button')}
                        </PrimaryButton>
                    </div>
                </div>

                <DataTable
                    data={withdrawals}
                    columns={columns}
                    onSort={handleSort}
                    currentSort={{ field: filters.sort, direction: filters.direction }}
                />
            </div>

            {/* Action Modal */}
            {actionModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex items-center mb-4">
                                {actionModal.action === 'approve' && <Check className="w-6 h-6 text-green-600 mr-2" />}
                                {actionModal.action === 'reject' && <X className="w-6 h-6 text-red-600 mr-2" />}
                                {actionModal.action === 'process' && <Clock className="w-6 h-6 text-blue-600 mr-2" />}
                                <h3 className="text-lg font-medium text-gray-900">
                                    {actionModal.action === 'approve' && __('affiliate.approve_withdrawal_title')}
                                    {actionModal.action === 'reject' && __('affiliate.reject_withdrawal_title')}
                                    {actionModal.action === 'process' && __('affiliate.process_withdrawal_title')}
                                </h3>
                            </div>

                            <div className="mb-4">
                                <p className="text-sm text-gray-500">
                                    {actionModal.action === 'approve' && __('affiliate.approve_withdrawal_confirm')}
                                    {actionModal.action === 'reject' && __('affiliate.reject_withdrawal_prompt')}
                                    {actionModal.action === 'process' && __('affiliate.process_withdrawal_confirm')}
                                </p>
                            </div>

                            {actionModal.action === 'reject' && (
                                <div className="mb-4">
                                    <textarea
                                        value={rejectionReason}
                                        onChange={(e) => setRejectionReason(e.target.value)}
                                        rows={3}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder={__('affiliate.reject_reason_placeholder')}
                                        required
                                    />
                                </div>
                            )}

                            <div className="flex justify-end space-x-4">
                                <button
                                    onClick={closeActionModal}
                                    className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                                >
                                    {__('affiliate.cancel')}
                                </button>
                                <button
                                    onClick={() => {
                                        if (actionModal.action === 'approve') {
                                            handleApprove(actionModal.withdrawalId);
                                        } else if (actionModal.action === 'reject') {
                                            handleReject(actionModal.withdrawalId);
                                        } else if (actionModal.action === 'process') {
                                            handleProcess(actionModal.withdrawalId);
                                        }
                                    }}
                                    className={`px-4 py-2 text-white text-base font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${actionModal.action === 'approve'
                                        ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                                        : actionModal.action === 'reject'
                                            ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                                            : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                                        }`}
                                >
                                    {actionModal.action === 'approve' && __('affiliate.approve_withdrawal')}
                                    {actionModal.action === 'reject' && __('affiliate.reject_withdrawal')}
                                    {actionModal.action === 'process' && __('affiliate.process_withdrawal')}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </SuperAdminLayout>
    );
}
