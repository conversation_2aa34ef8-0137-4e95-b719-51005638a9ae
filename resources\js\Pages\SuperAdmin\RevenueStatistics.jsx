import React, { useState, useEffect } from 'react';
import { Line, Doughnut, Bar, Pie } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    BarElement,
} from 'chart.js';
import {
    DollarSign,
    TrendingUp,
    Calculator,
    Percent,
    PieChart,
    Crown,
    Filter,
    Download,
    RefreshCw,
    Search,
    ChevronLeft,
    ChevronRight,
    ArrowUp,
    ArrowDown,
    BarChart3,
    LineChart,
    Trophy,
    MapPin,
    FileText,
    Building,
    Users,
    AlertTriangle,
    CheckCircle,
    Calendar,
} from 'lucide-react';
import axios from 'axios';
import { formatCurrencyNoDecimals } from '@/utils/formatting';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import Loading from '@/Components/Loading';
import SelectWithLabel from '@/Components/SelectWithLabel';
import SecondaryButton from '@/Components/SecondaryButton';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInputWithLabel from '@/Components/TextInputWithLabel';
import Pagination from '@/Components/Pagination';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
    BarElement
);


const RevenueStatistics = ({ initialData, businesses, branches, dashboardStats }) => {
    console.log("🚀 ~ RevenueStatistics ~ initialData:", initialData)
    console.log("🚀 ~ RevenueStatistics ~ dashboardStats:", dashboardStats)
    const [loadingStates, setLoadingStates] = useState({
        overview: !initialData?.overview,
        trends: !initialData?.trends?.length,
        topBusinesses: !initialData?.top_businesses?.length,
        topBranches: false,
        hourlyAnalysis: true,
        detailedReport: true,
        tableLoading: false
    });
    const [isFiltering, setIsFiltering] = useState(false);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [revenueData, setRevenueData] = useState({
        overview: initialData?.overview || {},
        trends: initialData?.trends || [],
        topBusinesses: initialData?.top_businesses || [],
        topBranches: [],
        hourlyAnalysis: [],
        detailedReport: { data: [], pagination: {} }
    });

    const [filters, setFilters] = useState({
        timeRange: 'month',
        businessId: 'all',
        branchId: 'all',
        revenueType: 'all',
        paymentMethod: 'all',
        startDate: '',
        endDate: ''
    });

    const [chartPeriod, setChartPeriod] = useState('daily');
    const [searchTerm, setSearchTerm] = useState('');

    const fetchRevenueData = async (showRefreshing = false, sections = ['overview', 'trends', 'topBusinesses', 'topBranches', 'hourlyAnalysis', 'detailedReport']) => {
        try {
            if (showRefreshing) {
                setRefreshing(true);
                setIsFiltering(true); // Set global filtering state
            } else {
                // Only set loading state for the sections being fetched
                setLoadingStates(prev => {
                    const newState = { ...prev };
                    sections.forEach(section => {
                        newState[section] = true;
                    });
                    return newState;
                });
            }

            setError(null);

            const token = localStorage.getItem('auth_token');
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            const filterParams = showRefreshing ? appliedFilters : appliedFilters;

            // Create an array of API promises to fetch in parallel
            const apiRequests = [];

            if (sections.includes('overview')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/overview`, { headers, params: filterParams })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                overview: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, overview: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching overview data:', error);
                            setLoadingStates(prev => ({ ...prev, overview: false }));
                        })
                );
            }

            if (sections.includes('trends')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/trends`, { headers, params: { ...filterParams, chart_type: chartPeriod } })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                trends: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, trends: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching trends data:', error);
                            setLoadingStates(prev => ({ ...prev, trends: false }));
                        })
                );
            }

            if (sections.includes('topBusinesses')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/top-businesses`, { headers, params: { ...filterParams, limit: 5 } })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                topBusinesses: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, topBusinesses: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching top businesses data:', error);
                            setLoadingStates(prev => ({ ...prev, topBusinesses: false }));
                        })
                );
            }

            if (sections.includes('topBranches')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/top-branches`, { headers, params: { ...filterParams, limit: 5 } })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                topBranches: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, topBranches: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching top branches data:', error);
                            setLoadingStates(prev => ({ ...prev, topBranches: false }));
                        })
                );
            }

            if (sections.includes('hourlyAnalysis')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/hourly-analysis`, { headers, params: filterParams })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                hourlyAnalysis: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, hourlyAnalysis: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching hourly analysis data:', error);
                            setLoadingStates(prev => ({ ...prev, hourlyAnalysis: false }));
                        })
                );
            }

            if (sections.includes('detailedReport')) {
                apiRequests.push(
                    axios.get(`/api/revenue-statistics/detailed-report`, { headers, params: { ...filterParams, per_page: 10 } })
                        .then(response => {
                            setRevenueData(prev => ({
                                ...prev,
                                detailedReport: response.data.data
                            }));
                            setLoadingStates(prev => ({ ...prev, detailedReport: false }));
                        })
                        .catch(error => {
                            console.error('Error fetching detailed report data:', error);
                            setLoadingStates(prev => ({ ...prev, detailedReport: false }));
                        })
                );
            }

            // Execute all API requests in parallel
            await Promise.all(apiRequests);

        } catch (err) {
            console.error('Error fetching revenue data:', err);
            setError(err.response?.data?.message || 'Có lỗi xảy ra khi tải dữ liệu');
        } finally {
            if (showRefreshing) {
                setRefreshing(false);
                setIsFiltering(false); // Clear global filtering state
            }
        }
    };

    const [appliedFilters, setAppliedFilters] = useState({
        timeRange: 'month',
        businessId: 'all',
        branchId: 'all',
        revenueType: 'all',
        paymentMethod: 'all',
        startDate: '',
        endDate: ''
    });

    useEffect(() => {
        // Use initialData and dashboardStats for initial display
        if (initialData || dashboardStats) {
            // Set overview data from initialData or dashboardStats
            const overviewData = initialData?.overview || {
                total_revenue: dashboardStats?.month_revenue || 0,
                total_bookings: dashboardStats?.month_bookings || 0,
                active_businesses: dashboardStats?.active_businesses || 0,
                active_branches: dashboardStats?.active_branches || 0,
                total_courts: dashboardStats?.total_courts || 0,
                avg_utilization_rate: dashboardStats?.avg_utilization_rate || 0
            };

            // Update revenueData with all available initial data
            setRevenueData(prev => ({
                ...prev,
                overview: overviewData,
                trends: initialData?.trends || [],
                topBusinesses: initialData?.top_businesses || [],
                // Keep other data as is
            }));

            // Update loading states based on available data
            setLoadingStates(prev => ({
                ...prev,
                overview: !overviewData,
                trends: !initialData?.trends?.length,
                topBusinesses: !initialData?.top_businesses?.length,
            }));

            // Set appliedFilters to match the initial data
            setAppliedFilters(prev => ({
                ...prev,
                // Don't change filters here, just use defaults
            }));
        }
    }, [initialData, dashboardStats]);

    useEffect(() => {
        fetchRevenueData(false, ['trends']);
    }, [chartPeriod]);

    useEffect(() => {
        if (dashboardStats && !initialData?.overview) {
            setRevenueData(prev => ({
                ...prev,
                overview: {
                    ...prev.overview,
                    total_revenue: dashboardStats.month_revenue || 0,
                    total_bookings: dashboardStats.month_bookings || 0,
                    active_businesses: dashboardStats.active_businesses || 0,
                    active_branches: dashboardStats.active_branches || 0,
                    total_courts: dashboardStats.total_courts || 0,
                    avg_utilization_rate: dashboardStats.avg_utilization_rate || 0
                }
            }));

            // Update loading state for overview if we have dashboard stats
            if (dashboardStats.month_revenue) {
                setLoadingStates(prev => ({
                    ...prev,
                    overview: false
                }));
            }
        }
    }, [dashboardStats]);

    const applyFilters = () => {
        setAppliedFilters({ ...filters });
        // Set global loading state
        setIsFiltering(true);
        // Fetch data when filters are applied
        fetchRevenueData(true, ['overview', 'trends', 'topBusinesses', 'topBranches', 'hourlyAnalysis', 'detailedReport']);
    };

    const resetFilters = () => {
        const defaultFilters = {
            timeRange: 'month',
            businessId: 'all',
            branchId: 'all',
            revenueType: 'all',
            paymentMethod: 'all',
            startDate: '',
            endDate: ''
        };
        setFilters(defaultFilters);
        setAppliedFilters(defaultFilters);
    };

    const handleFilterChange = (filterName, value) => {
        if (filterName === 'businessId') {
            setFilters(prev => ({
                ...prev,
                [filterName]: value,
                branchId: 'all'
            }));
        } else if (filterName === 'startDate' || filterName === 'endDate') {
            setFilters(prev => {
                const updatedFilters = {
                    ...prev,
                    [filterName]: value,
                    // Always ensure timeRange is 'custom' when dates are being set
                    timeRange: 'custom'
                };

                // If both dates are cleared, we could optionally reset to default timeRange
                if (!value && !prev.startDate && !prev.endDate) {
                    updatedFilters.timeRange = 'month';
                }

                return updatedFilters;
            });
        } else {
            // For other filters
            setFilters(prev => ({
                ...prev,
                [filterName]: value
            }));
        }
    };
    const formatNumber = (num) => {
        if (!num) return '0';
        return new Intl.NumberFormat('vi-VN').format(num);
    };
    const formatCurrency = (amount) => {
        if (!amount) return '0 VNĐ';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    };

    const formatNumberWithSuffix = (num) => {
        if (!num) return '0';
        if (num >= 1000000000) {
            return (Number(num) / 1000000000).toFixed(1) + 'B';
        }
        if (num >= 1000000) {
            return (Number(num) / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (Number(num) / 1000).toFixed(1) + 'K';
        }
        return formatCurrencyNoDecimals(num);
    };

    const revenueChartData = {
        labels: revenueData.trends.map(item => {
            if (chartPeriod === 'daily') {
                return new Date(item.revenue_date).toLocaleDateString('vi-VN');
            } else if (chartPeriod === 'weekly') {
                return `Tuần ${item.week}/${item.year}`;
            } else {
                return `Tháng ${item.month}/${item.year}`;
            }
        }),
        datasets: [
            {
                label: 'Doanh thu (VNĐ)',
                data: revenueData.trends.map(item => item.revenue),
                borderColor: '#EF4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#EF4444',
                pointRadius: 5,
                pointHoverRadius: 8
            }
        ]
    };

    const businessDistributionData = {
        labels: revenueData.topBusinesses.map(business => business.name),
        datasets: [
            {
                data: revenueData.topBusinesses.map(business => business.total_revenue),
                backgroundColor: [
                    '#EF4444',
                    '#3B82F6',
                    '#10B981',
                    '#F59E0B',
                    '#8B5CF6'
                ],
                borderWidth: 0
            }
        ]
    };

    const hourlyRevenueData = {
        labels: revenueData.hourlyAnalysis.map(item => `${item.hour_slot}:00`),
        datasets: [
            {
                label: 'Doanh thu (VNĐ)',
                data: revenueData.hourlyAnalysis.map(item => item.hourly_revenue),
                backgroundColor: 'rgba(239, 68, 68, 0.8)',
                borderRadius: 4
            }
        ]
    };

    const exportReport = async (format) => {
        try {
            const token = localStorage.getItem('auth_token');
            const response = await axios.post(
                `/revenue-statistics/export-report`,
                { format, ...appliedFilters },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data.success) {
                const downloadUrl = response.data.data.download_url;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = response.data.data.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('Báo cáo đã được xuất thành công!');
            }
        } catch (err) {
            console.error('Error exporting report:', err);
            alert('Có lỗi xảy ra khi xuất báo cáo');
        }
    };

    const handlePageChange = (page) => {
        // Only set loading state for the table part
        setLoadingStates(prev => ({ ...prev, tableLoading: true }));

        const token = localStorage.getItem('auth_token');
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };

        axios.get(`/api/revenue-statistics/detailed-report`, {
            headers,
            params: {
                ...appliedFilters,
                per_page: 10,
                page: page
            }
        })
            .then(response => {
                setRevenueData(prev => ({
                    ...prev,
                    detailedReport: response.data.data
                }));
                setLoadingStates(prev => ({ ...prev, tableLoading: false }));
            })
            .catch(error => {
                console.error('Error fetching detailed report data:', error);
                setLoadingStates(prev => ({ ...prev, tableLoading: false }));
            });
    };

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center bg-white p-8 rounded-lg shadow-lg max-w-md">
                    <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Có lỗi xảy ra</h3>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <button
                        onClick={() => fetchRevenueData()}
                        className="inline-flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Thử lại
                    </button>
                </div>
            </div>
        );
    }

    return (
        <SuperAdminLayout>
            <div className="p-4 bg-white rounded-lg shadow-md relative">
                <div className="">
                    <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-6 text-white">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div className="mb-4 lg:mb-0 flex flex-col items-start">
                                <h1 className="text-2xl lg:text-3xl font-bold mb-2 text-left">Tổng doanh thu</h1>
                                <div className="text-4xl lg:text-5xl font-bold mb-2 text-left w-full">
                                    {loadingStates.overview || isFiltering ? (
                                        <div className="flex justify-start">
                                            <Loading size="sm" text="" />
                                        </div>
                                    ) : (
                                        formatCurrencyNoDecimals(revenueData.overview.total_revenue)
                                    )}
                                </div>
                                <p className="text-red-100 text-left">
                                    {appliedFilters.startDate && appliedFilters.endDate ?
                                        appliedFilters.startDate === appliedFilters.endDate ?
                                            `Ngày ${new Date(appliedFilters.startDate).toLocaleDateString('vi-VN')}` :
                                            `${new Date(appliedFilters.startDate).toLocaleDateString('vi-VN')} - ${new Date(appliedFilters.endDate).toLocaleDateString('vi-VN')}` :
                                        appliedFilters.startDate ?
                                            `Từ ngày ${new Date(appliedFilters.startDate).toLocaleDateString('vi-VN')}` :
                                            appliedFilters.endDate ?
                                                `Đến ngày ${new Date(appliedFilters.endDate).toLocaleDateString('vi-VN')}` :
                                                appliedFilters.timeRange === 'month' ? 'Tháng này' :
                                                    appliedFilters.timeRange === 'week' ? 'Tuần này' :
                                                        appliedFilters.timeRange === 'year' ? 'Năm này' :
                                                            appliedFilters.timeRange === 'yesterday' ? 'Hôm qua' :
                                                                appliedFilters.timeRange === 'quarter' ? '3 tháng qua' : 'Hôm nay'}
                                </p>
                            </div>
                            <div className="flex items-center bg-white/20 rounded-lg px-4 py-3">
                                {loadingStates.overview || isFiltering ? (
                                    <Loading size="sm" text="" />
                                ) : (
                                    <>
                                        {revenueData.overview.growth_rate >= 0 ? (
                                            <ArrowUp className="w-6 h-6 mr-2" />
                                        ) : (
                                            <ArrowDown className="w-6 h-6 mr-2" />
                                        )}
                                        <span className="text-xl font-semibold">
                                            {Math.abs(Number(revenueData.overview.growth_rate || 0)).toFixed(1)}% so với kỳ trước
                                        </span>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-xl p-4 sm:p-6 my-4 shadow-sm border border-gray-200">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
                            <div className="flex items-center">
                                <Filter className="w-5 h-5 text-red-500 mr-2" />
                                <h2 className="text-lg font-semibold text-gray-900">Bộ lọc thống kê</h2>
                            </div>
                            <div className="flex gap-2">
                                <SecondaryButton
                                    onClick={() => fetchRevenueData(true)}
                                    disabled={refreshing}
                                    className="flex items-center gap-2 text-xs sm:text-sm"
                                >
                                    <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
                                    <span className="hidden xs:inline">Làm mới</span>
                                </SecondaryButton>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                            <div className="h-full">
                                <SelectWithLabel
                                    id="timeRange"
                                    label="Khoảng thời gian"
                                    value={filters.timeRange}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (value !== 'custom') {
                                            setFilters(prev => ({
                                                ...prev,
                                                timeRange: value,
                                                startDate: '',
                                                endDate: ''
                                            }));
                                        } else {
                                            setFilters(prev => ({
                                                ...prev,
                                                timeRange: 'custom'
                                            }));
                                        }
                                    }}
                                >
                                    <option value="today">Hôm nay</option>
                                    <option value="yesterday">Hôm qua</option>
                                    <option value="week">7 ngày qua</option>
                                    <option value="month">30 ngày qua</option>
                                    <option value="quarter">3 tháng qua</option>
                                    <option value="year">12 tháng qua</option>
                                    <option value="custom">Tùy chỉnh</option>
                                </SelectWithLabel>
                            </div>

                            <div className="h-full">
                                <SelectWithLabel
                                    id="businessId"
                                    label="Doanh nghiệp"
                                    value={filters.businessId}
                                    onChange={(e) => handleFilterChange('businessId', e.target.value)}
                                >
                                    <option value="all">Tất cả doanh nghiệp</option>
                                    {businesses.map(business => (
                                        <option key={business.id} value={business.id}>{business.name}</option>
                                    ))}
                                </SelectWithLabel>
                            </div>

                            <div className="h-full">
                                <SelectWithLabel
                                    id="branchId"
                                    label="Chi nhánh"
                                    value={filters.branchId}
                                    onChange={(e) => handleFilterChange('branchId', e.target.value)}
                                    disabled={filters.businessId === 'all'}
                                >
                                    <option value="all">Tất cả chi nhánh</option>
                                    {filters.businessId !== 'all' && branches
                                        .filter(branch => branch.business_id.toString() === filters.businessId.toString())
                                        .map(branch => (
                                            <option key={branch.id} value={branch.id}>{branch.name}</option>
                                        ))
                                    }
                                </SelectWithLabel>
                            </div>

                            <div className="h-full">
                                <div className="flex flex-col gap-2">
                                    <div className="w-full">
                                        <TextInputWithLabel
                                            type="date"
                                            placeholder="Từ ngày"
                                            className={filters.timeRange !== 'custom' ? 'bg-gray-100 cursor-not-allowed opacity-60 w-full' : 'w-full'}
                                            value={filters.startDate || ''}
                                            onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                            disabled={filters.timeRange !== 'custom'}
                                            id="startDate"
                                            label="Bắt đầu"
                                        />
                                    </div>
                                    <div className="w-full">
                                        <TextInputWithLabel
                                            type="date"
                                            placeholder="Đến ngày"
                                            className={filters.timeRange !== 'custom' ? 'bg-gray-100 cursor-not-allowed opacity-60 w-full' : 'w-full'}
                                            value={filters.endDate || ''}
                                            onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                            disabled={filters.timeRange !== 'custom'}
                                            id="endDate"
                                            label="Kết thúc"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-end gap-2 mt-4">
                            <SecondaryButton
                                onClick={resetFilters}
                                className="flex items-center justify-center gap-2 border-gray-300"
                            >
                                <RefreshCw size={16} />
                                <span>Đặt lại bộ lọc</span>
                            </SecondaryButton>
                            <PrimaryButton
                                onClick={applyFilters}
                                className="flex items-center justify-center gap-2"
                            >
                                <Filter size={16} />
                                <span>Áp dụng bộ lọc</span>
                            </PrimaryButton>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mt-6">
                        <div className="cursor-pointer bg-white p-6 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl hover:transform hover:-translate-y-1 transition-all h-full flex flex-col">
                            <div className="flex items-start justify-between flex-1">
                                <div className="flex flex-col h-full">
                                    <div className="h-12 flex items-start">
                                        <p className="text-sm font-medium text-gray-500 line-clamp-2">Tổng Doanh Thu</p>
                                    </div>
                                    <p className="text-2xl font-bold mt-1">
                                        {loadingStates.overview || isFiltering ? (
                                            <Loading size="sm" text="" />
                                        ) : (
                                            formatNumberWithSuffix(revenueData.overview.total_revenue)
                                        )}
                                    </p>
                                    {!loadingStates.overview && !isFiltering && (
                                        <p className={`text-xs flex items-center mt-1 ${revenueData.overview.growth_rate >= 0 ? "text-green-500" : "text-red-500"}`}>
                                            {revenueData.overview.growth_rate >= 0 ? (
                                                <ArrowUp size={14} className="mr-1 flex-shrink-0" />
                                            ) : (
                                                <ArrowDown size={14} className="mr-1 flex-shrink-0" />
                                            )}
                                            <span>{Math.abs(revenueData.overview.growth_rate || 0).toFixed(1)}% {revenueData.overview.growth_rate >= 0 ? 'tăng' : 'giảm'}</span>
                                        </p>
                                    )}
                                </div>
                                <div className="p-3 bg-red-50 rounded-full flex-shrink-0">
                                    <DollarSign size={24} className="text-red-500" />
                                </div>
                            </div>
                        </div>


                        <div className="cursor-pointer bg-white p-6 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl hover:transform hover:-translate-y-1 transition-all h-full flex flex-col">
                            <div className="flex items-start justify-between flex-1">
                                <div className="flex flex-col h-full">
                                    <div className="h-12 flex items-start">
                                        <p className="text-sm font-medium text-gray-500 line-clamp-2">Tổng số booking</p>
                                    </div>
                                    <p className="text-2xl font-bold mt-1">
                                        {loadingStates.overview || isFiltering ? (
                                            <Loading size="sm" text="" />
                                        ) : (
                                            formatNumber(revenueData.overview.total_bookings)
                                        )}
                                    </p>
                                    {!loadingStates.overview && !isFiltering && (
                                        <p className="text-xs text-gray-500 mt-1">
                                            <span>Thành công: {revenueData.overview.success_rate}%</span>
                                        </p>
                                    )}
                                </div>
                                <div className="p-3 bg-green-50 rounded-full flex-shrink-0">
                                    <Users size={24} className="text-green-500" />
                                </div>
                            </div>
                        </div>

                        <div className="cursor-pointer bg-white p-6 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl hover:transform hover:-translate-y-1 transition-all h-full flex flex-col">
                            <div className="flex items-start justify-between flex-1">
                                <div className="flex flex-col h-full">
                                    <div className="h-12 flex items-start">
                                        <p className="text-sm font-medium text-gray-500 line-clamp-2">Doanh thu TB/Booking</p>
                                    </div>
                                    <p className="text-2xl font-bold mt-1">
                                        {loadingStates.overview || isFiltering ? (
                                            <Loading size="sm" text="" />
                                        ) : (
                                            formatNumberWithSuffix(revenueData.overview.avg_booking_value)
                                        )}
                                    </p>
                                </div>
                                <div className="p-3 bg-purple-50 rounded-full flex-shrink-0">
                                    <Calculator size={24} className="text-purple-500" />
                                </div>
                            </div>
                        </div>

                        <div className="cursor-pointer bg-white p-6 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl hover:transform hover:-translate-y-1 transition-all h-full flex flex-col">
                            <div className="flex items-start justify-between flex-1">
                                <div className="flex flex-col h-full">
                                    <div className="h-12 flex items-start">
                                        <p className="text-sm font-medium text-gray-500 line-clamp-2">Booking cao nhất</p>
                                    </div>
                                    <p className="text-2xl font-bold mt-1">
                                        {loadingStates.overview || isFiltering ? (
                                            <Loading size="sm" text="" />
                                        ) : (
                                            formatNumberWithSuffix(revenueData.overview.peak_day_revenue)
                                        )}
                                    </p>
                                    {!loadingStates.overview && !isFiltering && (
                                        <p className="text-xs flex items-center mt-1 text-blue-500">
                                            <Calendar size={14} className="mr-1 flex-shrink-0" />
                                            <span>{revenueData.overview.peak_day_date ? new Date(revenueData.overview.peak_day_date).toLocaleDateString('vi-VN') : 'N/A'}</span>
                                        </p>
                                    )}
                                </div>
                                <div className="p-3 bg-blue-50 rounded-full flex-shrink-0">
                                    <TrendingUp size={24} className="text-blue-500" />
                                </div>
                            </div>
                        </div>

                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="my-2 lg:my-4 lg:col-span-2 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                        <LineChart className="w-5 h-5 text-red-500 mr-2" />
                                        Xu Hướng Doanh Thu Theo Thời Gian
                                    </h3>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Doanh thu theo {chartPeriod === 'daily' ? 'ngày' : chartPeriod === 'weekly' ? 'tuần' : 'tháng'}
                                    </p>
                                </div>
                                <div className="flex bg-gray-100 rounded-lg p-1 mt-4 sm:mt-0">
                                    {['daily', 'weekly', 'monthly'].map((period) => (
                                        <button
                                            key={period}
                                            onClick={() => setChartPeriod(period)}
                                            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${chartPeriod === period ? "bg-white text-red-600 shadow-sm" : "text-gray-600 hover:text-gray-900"}`}
                                        >
                                            {period === 'daily' ? 'Ngày' : period === 'weekly' ? 'Tuần' : 'Tháng'}
                                        </button>
                                    ))}
                                </div>
                            </div>
                            <div className="h-80">
                                {loadingStates.trends || isFiltering ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="flex flex-col items-center">
                                            <div className="h-10 w-10 border-4 border-gray-200 border-t-red-600 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-sm text-gray-500">Đang tải dữ liệu...</p>
                                        </div>
                                    </div>
                                ) : (
                                    <Line
                                        data={revenueChartData}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            scales: {
                                                y: {
                                                    beginAtZero: true,
                                                    ticks: {
                                                        callback: function (value) {
                                                            return formatNumberWithSuffix(value);
                                                        }
                                                    }
                                                }
                                            },
                                            plugins: {
                                                legend: {
                                                    display: false
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        label: function (context) {
                                                            return 'Doanh thu: ' + formatCurrency(context.parsed.y);
                                                        }
                                                    }
                                                }
                                            }
                                        }}
                                    />
                                )}
                            </div>
                        </div>

                        <div className="my-2 lg:my-4  bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-6">
                                <PieChart className="w-5 h-5 text-red-500 mr-2" />
                                Phân Bổ Doanh Thu
                            </h3>
                            <div className="h-80">
                                {loadingStates.topBusinesses || isFiltering ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="flex flex-col items-center">
                                            <div className="h-10 w-10 border-4 border-gray-200 border-t-red-600 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-sm text-gray-500">Đang tải dữ liệu...</p>
                                        </div>
                                    </div>
                                ) : (
                                    <Doughnut
                                        data={businessDistributionData}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    position: 'bottom',
                                                    labels: {
                                                        padding: 20,
                                                        usePointStyle: true,
                                                        font: {
                                                            size: 12
                                                        }
                                                    }
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        label: function (context) {
                                                            return context.label + ': ' + formatCurrency(context.parsed);
                                                        }
                                                    }
                                                }
                                            }
                                        }}
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="my-2 lg:my-4  grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-6">
                                <BarChart3 className="w-5 h-5 text-red-500 mr-2" />
                                Doanh Thu Theo Khung Giờ
                            </h3>
                            <div className="h-80">
                                {loadingStates.hourlyAnalysis || isFiltering ? (
                                    <div className="flex items-center justify-center h-full">
                                        <div className="flex flex-col items-center">
                                            <div className="h-10 w-10 border-4 border-gray-200 border-t-red-600 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-sm text-gray-500">Đang tải dữ liệu...</p>
                                        </div>
                                    </div>
                                ) : (
                                    <Bar
                                        data={hourlyRevenueData}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            scales: {
                                                y: {
                                                    beginAtZero: true,
                                                    ticks: {
                                                        callback: function (value) {
                                                            return formatNumberWithSuffix(value);
                                                        }
                                                    }
                                                }
                                            },
                                            plugins: {
                                                legend: {
                                                    display: false
                                                }
                                            }
                                        }}
                                    />
                                )}
                            </div>
                        </div>

                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-6">
                                <CheckCircle className="w-5 h-5 text-red-500 mr-2" />
                                Tỷ Lệ Thành Công
                            </h3>
                            <div className="h-80 flex flex-col items-center justify-center">
                                <div className="text-6xl font-bold text-green-500 mb-4">
                                    {revenueData.overview.success_rate || 0}%
                                </div>
                                <div className="text-center text-gray-600">
                                    <p className="text-lg font-medium mb-2">Tỷ lệ booking thành công</p>
                                    <p className="text-sm">
                                        {formatNumber(revenueData.overview.confirmed_bookings)} / {formatNumber(revenueData.overview.total_bookings)} booking
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="my-2 lg:my-4  grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                    <Trophy className="w-5 h-5 text-red-500 mr-2" />
                                    Top Doanh Nghiệp Theo Doanh Thu
                                </h3>

                            </div>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Xếp hạng
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Doanh nghiệp
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Doanh thu
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Booking
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {loadingStates.topBusinesses || isFiltering ? (
                                            <tr>
                                                <td colSpan="4" className="px-6 py-4 text-center">
                                                    <Loading size="md" text="Đang tải dữ liệu..." />
                                                </td>
                                            </tr>
                                        ) : (
                                            revenueData.topBusinesses.map((business, index) => (
                                                <tr key={business.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <span className={`
                                                        inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold
                                                        ${index === 0 ? "bg-yellow-100 text-yellow-800" :
                                                                    index === 1 ? "bg-gray-100 text-gray-800" :
                                                                        index === 2 ? "bg-orange-100 text-orange-800" :
                                                                            "bg-blue-100 text-blue-800"
                                                                }`}>
                                                                {index + 1}
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                                                <Building className="w-4 h-4 text-white" />
                                                            </div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {business.name}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-semibold text-green-600">
                                                            {formatCurrency(business.total_revenue)}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {formatNumber(business.total_bookings)}
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* Top Branches */}
                        <div className=" bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                    <MapPin className="w-5 h-5 text-red-500 mr-2" />
                                    Top Chi Nhánh Theo Doanh Thu
                                </h3>

                            </div>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Xếp hạng
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Chi nhánh
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Doanh thu
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Hiệu suất
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {loadingStates.topBranches || isFiltering ? (
                                            <tr>
                                                <td colSpan="4" className="px-6 py-4 text-center">
                                                    <Loading size="md" text="Đang tải dữ liệu..." />
                                                </td>
                                            </tr>
                                        ) : (
                                            revenueData.topBranches.map((branch, index) => (
                                                <tr key={branch.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`
                                                    inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold
                                                    ${index === 0 ? "bg-yellow-100 text-yellow-800" :
                                                                index === 1 ? "bg-gray-100 text-gray-800" :
                                                                    index === 2 ? "bg-orange-100 text-orange-800" :
                                                                        "bg-blue-100 text-blue-800"
                                                            }`}>
                                                            {index + 1}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {branch.branch_name}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {branch.business_name}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-semibold text-green-600">
                                                            {formatCurrency(branch.total_revenue)}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`
                                                    inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    ${branch.performance_rating === 'Xuất sắc' ? "bg-green-100 text-green-800" :
                                                                branch.performance_rating === 'Tốt' ? "bg-blue-100 text-blue-800" :
                                                                    branch.performance_rating === 'Trung bình' ? "bg-yellow-100 text-yellow-800" :
                                                                        "bg-red-100 text-red-800"
                                                            }`}>
                                                            {branch.performance_rating}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {/* Detailed Revenue Report */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4 sm:mb-0">
                                    <FileText className="w-5 h-5 text-red-500 mr-2" />
                                    Báo Cáo Doanh Thu Chi Tiết
                                </h3>
                                <div className="flex flex-col sm:flex-row gap-3">

                                    <div className="flex gap-2">
                                        <SecondaryButton
                                            onClick={() => fetchRevenueData(true)}
                                            disabled={refreshing}
                                            className="flex items-center gap-2"
                                        >
                                            <RefreshCw size={18} className={refreshing ? "animate-spin" : ""} />
                                            <span>Làm mới</span>
                                        </SecondaryButton>
                                        <button
                                            onClick={() => exportReport('excel')}
                                            className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                                        >
                                            <Download className="w-4 h-4 mr-2" />
                                            Excel
                                        </button>
                                        <button
                                            onClick={() => exportReport('pdf')}
                                            className="inline-flex items-center px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                        >
                                            <FileText className="w-4 h-4 mr-2" />
                                            PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Ngày
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Doanh nghiệp
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Chi nhánh
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Booking
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Doanh thu thô
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Hoa hồng
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Doanh thu ròng
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {loadingStates.detailedReport || isFiltering || loadingStates.tableLoading ? (
                                        <tr>
                                            <td colSpan="7" className="px-6 py-4 text-center">
                                                <Loading size="md" text="Đang tải dữ liệu..." />
                                            </td>
                                        </tr>
                                    ) : (
                                        revenueData.detailedReport.data?.map((record, index) => (
                                            <tr key={index} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {new Date(record.revenue_date).toLocaleDateString('vi-VN')}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {record.business?.name || 'N/A'}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {record.branch?.name || 'N/A'}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <div className="flex items-center">
                                                        <Users className="w-4 h-4 text-gray-400 mr-2" />
                                                        {formatNumber(record.total_bookings)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-600">
                                                    {formatCurrency(record.gross_revenue)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {formatCurrency(record.commission_amount)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-600">
                                                    {formatCurrency(record.net_revenue)}
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        <div className="px-6 py-4 border-t border-gray-200">
                            <div className="text-sm text-gray-700 mb-4">
                                Hiển thị <span className="font-medium">{revenueData.detailedReport.from || 0}</span> đến{' '}
                                <span className="font-medium">{revenueData.detailedReport.to || 0}</span> trong tổng số{' '}
                                <span className="font-medium">{revenueData.detailedReport.total || 0}</span> bản ghi
                            </div>
                            {revenueData.detailedReport.links && (
                                <div className="flex justify-center">
                                    <div className="flex items-center space-x-2">
                                        <button
                                            onClick={() => {
                                                if (revenueData.detailedReport.prev_page_url) {
                                                    const prevPage = revenueData.detailedReport.current_page - 1;
                                                    handlePageChange(prevPage);
                                                }
                                            }}
                                            disabled={!revenueData.detailedReport.prev_page_url}
                                            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            <ChevronLeft className="w-4 h-4" />
                                        </button>
                                        <span className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white">
                                            {revenueData.detailedReport.current_page || 1}
                                        </span>
                                        <button
                                            onClick={() => {
                                                if (revenueData.detailedReport.next_page_url) {
                                                    const nextPage = revenueData.detailedReport.current_page + 1;
                                                    handlePageChange(nextPage);
                                                }
                                            }}
                                            disabled={!revenueData.detailedReport.next_page_url}
                                            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            <ChevronRight className="w-4 h-4" />
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </SuperAdminLayout>
    );
};

export default RevenueStatistics;

