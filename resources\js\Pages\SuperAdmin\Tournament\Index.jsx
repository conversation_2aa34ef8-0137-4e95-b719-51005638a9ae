import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import SuperAdminLayout from '@/Layouts/SuperAdminLayout';
import { __ } from '@/utils/lang';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/utils/formatting';
import Loading from '@/Components/Loading';
import DataTable from '@/Components/DataTable';
import { Button } from '@/Components/ui/button';
import { Plus } from 'lucide-react';
import StatusBadge from '@/Components/ui/StatusBadge';
import Pagination from '@/Components/Pagination';

export default function Index({ tournaments, businesses, filters, statuses }) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const { processing } = usePage().props;

    const handleSearch = (e) => {
        e.preventDefault();
        setIsLoading(true);
        router.get(route('superadmin.tournaments.index'), { search: searchQuery }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleStatusFilter = (status) => {
        setIsLoading(true);
        router.get(route('superadmin.tournaments.index'), {
            search: filters.search,
            status: status
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleBusinessFilter = (businessId) => {
        setIsLoading(true);
        router.get(route('superadmin.tournaments.index'), {
            search: filters.search,
            status: filters.status,
            business_id: businessId
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const handleSort = (field, direction) => {
        setIsLoading(true);
        router.get(route('superadmin.tournaments.index'), {
            search: filters.search,
            status: filters.status,
            business_id: filters.business_id,
            sort: field,
            direction: direction
        }, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => setIsLoading(false)
        });
    };

    const confirmDelete = (tournamentId) => {
        setIsDeleting(tournamentId);
    };

    const cancelDelete = () => {
        setIsDeleting(null);
    };

    const deleteTournament = (tournamentId) => {
        setIsLoading(true);
        router.delete(route('superadmin.tournaments.destroy', tournamentId), {
            onFinish: () => {
                setIsDeleting(null);
                setIsLoading(false);
            }
        });
    };

    const columns = [
        {
            field: 'title',
            label: 'Giải đấu',
            sortable: true,
            render: (tournament) => (
                <div className="flex items-center">
                    <Link
                        href={route('superadmin.tournaments.show', tournament.id)}
                        className="flex items-center hover:text-primary transition-colors"
                    >
                        <div className="flex-shrink-0 h-10 w-10">
                            {tournament.image_url ? (
                                <img
                                    src={tournament.formatted_image_url}
                                    alt={tournament.title}
                                    className="h-10 w-10 rounded-lg object-cover"
                                />
                            ) : (
                                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                    <i className="fas fa-trophy text-gray-400"></i>
                                </div>
                            )}
                        </div>
                        <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 group-hover:text-primary">
                                {tournament.title}
                            </div>
                            <div className="text-xs text-gray-500">
                                {tournament.location}
                            </div>
                        </div>
                    </Link>
                </div>
            )
        },
        {
            field: 'organizer',
            label: 'Đơn vị tổ chức',
            sortable: true,
            render: (tournament) => (
                <div>
                    <div className="text-sm text-gray-900">{tournament.organizer}</div>
                    {tournament.business ? (
                        <div className="text-xs text-purple-600">
                            {tournament.business.name}
                        </div>
                    ) : (
                        <div className="text-xs text-blue-600">
                            Giải đấu hệ thống
                        </div>
                    )}
                </div>
            )
        },
        {
            field: 'start_date',
            label: 'Thời gian',
            sortable: true,
            render: (tournament) => (
                <div>
                    <div className="text-sm text-gray-900">
                        {formatDateTime(tournament.start_date)} - {formatDateTime(tournament.end_date)}
                    </div>
                    <div className="text-xs text-gray-500">
                        Phí: {formatCurrency(tournament.entry_fee)}
                    </div>
                </div>
            )
        },
        {
            field: 'status',
            label: 'Trạng thái',
            render: (tournament) => (
                <StatusBadge status={tournament.status} />
            )
        },
        {
            field: 'featured',
            label: 'Nổi bật',
            render: (tournament) => (
                <StatusBadge
                    status={tournament.featured ? 'featured' : 'not_featured'}
                    text={tournament.featured ? 'Nổi bật' : 'Không nổi bật'}
                    color={tournament.featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}
                />
            )
        },
        {
            field: 'created_at',
            label: 'Ngày tạo',
            sortable: true,
            render: (tournament) => (
                <div>
                    <div className="text-sm text-gray-500">{formatDateTime(tournament.created_at)}</div>
                    <div className="text-xs text-gray-400">{formatRelativeTime(tournament.created_at)}</div>
                </div>
            )
        }
    ];

    const customActions = [
        {
            label: 'Xem',
            onClick: (tournament) => window.location.href = route('superadmin.tournaments.show', tournament.id)
        },
        {
            label: 'Sửa',
            onClick: (tournament) => window.location.href = route('superadmin.tournaments.edit', tournament.id)
        },
        {
            label: 'Xóa',
            onClick: (tournament) => confirmDelete(tournament.id)
        }
    ];

    return (
        <SuperAdminLayout>
            <Head title="Quản lý Giải đấu" />

            <div className="bg-white rounded-lg shadow-md relative">
                {(isLoading || processing) && <Loading overlay text="Đang tải..." />}
                <div className="border-gray-200 p-4">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-semibold text-gray-900">Quản lý Giải đấu</h1>
                        <Button asChild variant="primary">
                            <Link href={route('superadmin.tournaments.create')} className="flex items-center gap-2">
                                <Plus className="w-4 h-4" />
                                Thêm Giải đấu
                            </Link>
                        </Button>
                    </div>

                    <div className="mt-4 flex flex-col md:flex-row md:items-center gap-4">
                        <form onSubmit={handleSearch} className="flex-1">
                            <div className="relative">
                                <input
                                    type="text"
                                    className="w-full pl-10 pr-4 py-2 border rounded-md focus:ring-primary focus:border-primary"
                                    placeholder="Tìm kiếm giải đấu..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i className="fas fa-search text-gray-400"></i>
                                </div>
                                <button type="submit" className="hidden">Search</button>
                            </div>
                        </form>

                        <div className="flex items-center gap-2">
                            <Button
                                onClick={() => handleStatusFilter('')}
                                variant={!filters.status ? "secondary" : "outline"}
                                size="sm"
                            >
                                Tất cả
                            </Button>
                            {Object.entries(statuses).map(([key, label]) => (
                                <Button
                                    key={key}
                                    onClick={() => handleStatusFilter(key)}
                                    variant={filters.status === key ? "default" : "outline"}
                                    size="sm"
                                >
                                    {label}
                                </Button>
                            ))}
                        </div>
                    </div>
                </div>

                <DataTable
                    data={tournaments.data}
                    columns={columns}
                    actions={customActions}
                    enableIcon={true}
                    onSort={handleSort}
                    sortField={filters.sort || 'created_at'}
                    sortDirection={filters.direction || 'desc'}
                    emptyStateMessage="Không có giải đấu nào"
                    primaryKey="id"
                    deleteCallback={deleteTournament}
                    confirmingDeletionId={isDeleting}
                    cancelDeletion={cancelDelete}
                    highlightColor="primary"
                    enableDefaultActions={false}
                    actionIcon={true}
                />

                {tournaments.links && tournaments.links.length > 3 && (
                    <div className="px-6 py-4 bg-white border-t border-gray-200 sm:px-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-700"
                                    dangerouslySetInnerHTML={{
                                        __html: `Hiển thị ${tournaments.from} đến ${tournaments.to} trong tổng số ${tournaments.total} kết quả`
                                    }}
                                />
                            </div>
                            <Pagination links={tournaments.links} />
                        </div>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
