import axios from "axios";

export const AddressService = {
    CACHE_TTL: 24 * 60 * 60 * 1000,

    getFromCache(key) {
        try {
            const cachedData = localStorage.getItem(key);
            if (!cachedData) return null;

            const { data, timestamp } = JSON.parse(cachedData);
            const isExpired = Date.now() - timestamp > this.CACHE_TTL;

            return isExpired ? null : data;
        } catch (error) {
            console.error("Cache error:", error);
            return null;
        }
    },

    saveToCache(key, data) {
        try {
            const item = {
                data,
                timestamp: Date.now(),
            };
            localStorage.setItem(key, JSON.stringify(item));
        } catch (error) {
            console.error("Cache save error:", error);
        }
    },

    getProvinces: async () => {
        try {
            const cacheKey = "provinces_list";
            const cachedData = AddressService.getFromCache(cacheKey);

            if (cachedData) {
                return { success: true, data: cachedData };
            }
            const response = await axios.get(route("api.address.provinces"));
            if (response.data.success) {
                AddressService.saveToCache(cacheKey, response.data.data);
            }

            return response.data;
        } catch (error) {
            console.error("Error fetching provinces:", error);
            throw error;
        }
    },

    getWardsByProvince: async (provinceCode) => {
        try {
            const cacheKey = `wards_province_${provinceCode}`;
            const cachedData = AddressService.getFromCache(cacheKey);

            if (cachedData) {
                return { success: true, data: cachedData };
            }
            const response = await axios.get(
                route("api.address.wards", provinceCode)
            );
            if (response.data.success) {
                AddressService.saveToCache(cacheKey, response.data.data);
            }

            return response.data;
        } catch (error) {
            console.error("Error fetching wards:", error);
            throw error;
        }
    },

    getFullAddress: async (provinceCode, wardCode) => {
        try {
            const response = await axios.get(route("api.address.full"), {
                params: {
                    province_code: provinceCode,
                    ward_code: wardCode,
                },
            });
            return response.data;
        } catch (error) {
            console.error("Error fetching full address:", error);
            throw error;
        }
    },

    getProvinceNameById: async (provinceId) => {
        try {
            if (!provinceId) return null;
            const cacheKey = `province_name_${provinceId}`;
            const cachedName = AddressService.getFromCache(cacheKey);

            if (cachedName) return cachedName;
            const provinces = await AddressService.getProvinces();
            const province = provinces.data.find(
                (p) => p.id === parseInt(provinceId)
            );

            if (province) {
                AddressService.saveToCache(cacheKey, province.name);
                return province.name;
            }

            return null;
        } catch (error) {
            console.error("Error fetching province name:", error);
            return null;
        }
    },

    getProvinceNameByCode: async (provinceCode) => {
        try {
            if (!provinceCode) return null;
            const cacheKey = `province_name_code_${provinceCode}`;
            const cachedName = AddressService.getFromCache(cacheKey);

            if (cachedName) return cachedName;
            const provinces = await AddressService.getProvinces();
            const province = provinces.data.find(
                (p) => p.province_code === provinceCode
            );

            if (province) {
                AddressService.saveToCache(cacheKey, province.name);
                return province.name;
            }

            return null;
        } catch (error) {
            console.error("Error fetching province name:", error);
            return null;
        }
    },

    getWardNameById: async (wardId, provinceCode) => {
        try {
            if (!wardId || !provinceCode) return null;
            const cacheKey = `ward_name_${wardId}`;
            const cachedName = AddressService.getFromCache(cacheKey);

            if (cachedName) return cachedName;

            const wards = await AddressService.getWardsByProvince(provinceCode);
            const ward = wards.data.find((w) => w.id === parseInt(wardId));

            if (ward) {
                AddressService.saveToCache(cacheKey, ward.name);
                return ward.name;
            }

            return null;
        } catch (error) {
            console.error("Error fetching ward name:", error);
            return null;
        }
    },

    getWardNameByCode: async (wardCode) => {
        try {
            if (!wardCode) return null;
            const cacheKey = `ward_name_code_${wardCode}`;
            const cachedName = AddressService.getFromCache(cacheKey);

            if (cachedName) return cachedName;

            // If we don't have a cached name, we might need to make a specific API call
            // or iterate through provinces to find the ward
            // For now, return null - this could be optimized based on requirements
            return null;
        } catch (error) {
            console.error("Error fetching ward name:", error);
            return null;
        }
    },
};

export default AddressService;
