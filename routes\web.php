<?php

use App\Http\Controllers\Api\CronJobController;
use App\Http\Controllers\Api\RevenueController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ErrorController;
use App\Http\Controllers\Customer\CourtSearchController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Http\Controllers\Customer\BranchController;
use App\Http\Controllers\Auth\SocialLoginController;
use App\Models\SystemSetting;

Route::middleware(['web'])->group(function () {
    Inertia::share('siteSettings', function () {
        $settings = SystemSetting::whereIn('group', ['general', 'ui'])->get();
        $siteSettings = [];

        foreach ($settings as $setting) {
            $siteSettings[$setting->key_setting] = $setting->setting_value;
        }

        return $siteSettings;
    });
});

Route::get('/unauthorized', [ErrorController::class, 'unauthorized'])->name('unauthorized');
Route::post('/redirect-to-unauthorized', [ErrorController::class, 'redirectToUnauthorized'])->name('redirect.to.unauthorized');

Route::controller(App\Http\Controllers\NotificationController::class)->prefix('notifications')->name('notifications.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/count', 'getUnreadCount')->name('count');
    Route::get('/dropdown', 'getDropdownNotifications')->name('dropdown');
    Route::post('/{notification}/read', 'markAsRead')->name('read');
    Route::post('/mark-all-read', 'markAllAsRead')->name('mark-all-read');
    Route::delete('/{notification}', 'destroy')->name('destroy');
    Route::get('/booking/new', 'getNewBookingNoti')->name('booking.new');
});


Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('welcome');

Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

foreach (['/superadmin', '/business', '/branch'] as $prefix) {
    Route::get($prefix, function () {
        if (!Auth::check()) {
            return redirect()->route('login');
        }
        return redirect()->route('dashboard');
    });
}

Route::prefix('superadmin')->name('superadmin.')->middleware(['auth', 'verified', 'check.super.admin'])->group(function () {
    Route::get('/settings', [App\Http\Controllers\SuperAdmin\SettingController::class, 'index'])->name('settings.index');
    Route::post('/settings', [App\Http\Controllers\SuperAdmin\SettingController::class, 'update'])->name('settings.update');
    Route::post('/settings/upload-image', [App\Http\Controllers\SuperAdmin\SettingController::class, 'uploadImage'])->name('settings.upload-image');
    Route::delete('/settings/delete-image', [App\Http\Controllers\SuperAdmin\SettingController::class, 'deleteImage'])->name('settings.delete-image');

    Route::get('/bookings', [App\Http\Controllers\BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/list', [App\Http\Controllers\BookingController::class, 'list'])->name('bookings.list');
    Route::prefix('booking')->name('bookings.')->group(function () {
        Route::get('/create', [App\Http\Controllers\BookingController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\BookingController::class, 'store'])->name('store');
        Route::get('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'show'])->name('show');
        Route::get('/{referenceNumber}/edit', [App\Http\Controllers\BookingController::class, 'edit'])->name('edit');
        Route::put('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'update'])->name('update');
        Route::delete('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'destroy'])->name('destroy');
        Route::post('/{referenceNumber}/cancel', [App\Http\Controllers\BookingController::class, 'cancel'])->name('cancel');
        Route::post('/{referenceNumber}/complete', [App\Http\Controllers\BookingController::class, 'complete'])->name('complete');
        Route::post('/{referenceNumber}/confirm-payment', [App\Http\Controllers\BookingController::class, 'confirmPayment'])->name('confirm-payment');
        Route::post('/{referenceNumber}/send-confirmation-email', [App\Http\Controllers\BookingController::class, 'sendConfirmationEmail'])->name('send-confirmation-email');
    });

    // Online booking routes
    Route::get('/bookings/online', [App\Http\Controllers\BookingOnlineController::class, 'index'])->name('bookings.online');
    Route::get('/bookings/online/{id}', [App\Http\Controllers\BookingOnlineController::class, 'show'])->name('bookings.online.show');
    Route::post('/bookings/online/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approve'])->name('bookings.online.approve');
    Route::post('/bookings/online/reference/{reference_number}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approveByReference'])->name('bookings.online.approve.reference');
    Route::post('/bookings/online/{id}/reject', [App\Http\Controllers\BookingOnlineController::class, 'reject'])->name('bookings.online.reject');
    Route::get('/bookings/online/{id}/payment-info', [App\Http\Controllers\BookingOnlineController::class, 'getPaymentInfo'])->name('bookings.online.payment-info');
    Route::post('/payments/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approvePayment'])->name('payments.approve');

    // Review management routes
    Route::get('/reviews', [App\Http\Controllers\Customer\ReviewController::class, 'index'])->name('reviews');

    Route::get('/business', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'index'])->name('business.index');
    Route::get('/business/create', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'create'])->name('business.create');
    Route::post('/business', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'store'])->name('business.store');
    Route::get('/business/{business}', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'show'])->name('business.show');
    Route::get('/business/{business}/edit', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'edit'])->name('business.edit');
    Route::put('/business/{business}', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'update'])->name('business.update');
    Route::delete('/business/{business}', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'destroy'])->name('business.destroy');
    Route::post('/business/{business}/admins', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'addAdmin'])->name('business.add-admin');
    Route::post('/business/{business}/settings', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'updateSettings'])->name('business.update-settings');
    Route::post('/business/{business}/images', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'updateImages'])->name('business.update-images');
    Route::get('/business/{business}/get-admins', [App\Http\Controllers\SuperAdmin\BusinessController::class, 'getAdmins'])->name('business.get-admins');
    Route::get('/analytics', [App\Http\Controllers\SuperAdmin\StatisticController::class, 'analytics'])->name('analytics');
    Route::get('/export-statistics', [App\Http\Controllers\SuperAdmin\StatisticController::class, 'exportStatistics'])->name('export-statistics');
    Route::resource('branch', \App\Http\Controllers\BranchController::class);
    Route::resource('users', \App\Http\Controllers\UserController::class);


    // Service management routes
    Route::get('/services', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'index'])->name('services.index');
    Route::get('/services/create', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'create'])->name('services.create');
    Route::post('/services', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'store'])->name('services.store');
    Route::get('/services/{service}/edit', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'edit'])->name('services.edit');
    Route::put('/services/{service}', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'update'])->name('services.update');
    Route::delete('/services/{service}', [App\Http\Controllers\SuperAdmin\ServiceController::class, 'destroy'])->name('services.destroy');

    // Staff assignment routes
    Route::get('/{business_id}/staff/assign', [App\Http\Controllers\StaffController::class, 'assignPage'])->name('staff.assign');
    Route::get('/staff/get-list-staff', [App\Http\Controllers\StaffController::class, 'getListStaff'])->name('staff.get-list-staff');
    Route::post('/staff/assign', [App\Http\Controllers\StaffController::class, 'assign'])->name('staff.assign.post');
    Route::post('/staff/unassign/{user_id}', [App\Http\Controllers\StaffController::class, 'unassign'])->name('staff.unassign.post');
    // Service Assignment routes
    Route::get('/services/assignments', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'index'])->name('services.assignments.index');
    Route::get('/services/{service}/assign', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'assign'])->name('services.assignments.assign');
    Route::post('/services/{service}/assignments', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'storeAssignment'])->name('services.assignments.store');
    Route::put('/services/{service}/assignments/{branch}', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'updateAssignment'])->name('services.assignments.update');
    Route::delete('/services/{service}/assignments/{branch}', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'destroyAssignment'])->name('services.assignments.destroy');
    Route::get('/branches/business/{business}', [App\Http\Controllers\SuperAdmin\ServiceAssignmentController::class, 'getBranchesByBusiness'])->name('branches.by-business');
    Route::get('/permissions', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'index'])->name('permissions.index');
    Route::get('/permissions/create', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'create'])->name('permissions.create');
    Route::post('/permissions', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'store'])->name('permissions.store');
    Route::get('/permissions/{permission}/edit', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'edit'])->name('permissions.edit');
    Route::put('/permissions/{permission}', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'update'])->name('permissions.update');
    Route::delete('/permissions/{permission}', [App\Http\Controllers\SuperAdmin\PermissionController::class, 'destroy'])->name('permissions.destroy');

    Route::get('/roles', [App\Http\Controllers\SuperAdmin\RoleController::class, 'index'])->name('roles.index');
    Route::get('/roles/create', [App\Http\Controllers\SuperAdmin\RoleController::class, 'create'])->name('roles.create');
    Route::post('/roles', [App\Http\Controllers\SuperAdmin\RoleController::class, 'store'])->name('roles.store');
    Route::get('/roles/{role}/edit', [App\Http\Controllers\SuperAdmin\RoleController::class, 'edit'])->name('roles.edit');
    Route::put('/roles/{role}', [App\Http\Controllers\SuperAdmin\RoleController::class, 'update'])->name('roles.update');
    Route::delete('/roles/{role}', [App\Http\Controllers\SuperAdmin\RoleController::class, 'destroy'])->name('roles.destroy');

    Route::get('/role-permissions', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'index'])->name('role-permissions.index');
    Route::post('/role-permissions/sync', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'syncPermissions'])->name('role-permissions.sync');

    Route::get('/user-roles', [App\Http\Controllers\SuperAdmin\UserRoleController::class, 'index'])->name('user-roles.index');
    Route::post('/user-roles/sync', [App\Http\Controllers\SuperAdmin\UserRoleController::class, 'syncRoles'])->name('user-roles.sync');

    // User-Permission assignment routes
    // Route::get('/user-permissions/{user}/edit', [App\Http\Controllers\UserPermissionController::class, 'edit'])->name('user-permissions.edit');
    // Route::put('/user-permissions/{user}', [App\Http\Controllers\UserPermissionController::class, 'update'])->name('user-permissions.update');

    Route::get('/payment-history', [App\Http\Controllers\SuperAdmin\PaymentHistoryController::class, 'index'])->name('payment.history');
    Route::get('/payment-history/export', [App\Http\Controllers\SuperAdmin\PaymentHistoryController::class, 'export'])->name('payment.history.export');
    Route::get('booking-history', [App\Http\Controllers\BookingHistoryController::class, 'index'])->name('booking.history');
    Route::get('booking-history/export', [App\Http\Controllers\BookingHistoryController::class, 'export'])->name('booking.export');
    Route::get('booking-history/details', [App\Http\Controllers\BookingHistoryController::class, 'getBookingDetails'])->name('booking.details');

    // Update check-in/check-out routes to use the consolidated controller
    Route::get('/bookings/checkin-checkout', [App\Http\Controllers\BookingCheckController::class, 'index'])->name('bookings.checkin-checkout');
    Route::post('/bookings/{id}/checkin', [App\Http\Controllers\BookingCheckController::class, 'checkIn'])->name('bookings.checkin');
    Route::post('/bookings/{id}/checkout', [App\Http\Controllers\BookingCheckController::class, 'checkOut'])->name('bookings.checkout');
    Route::post('/bookings/payment/overtime', [App\Http\Controllers\BookingCheckController::class, 'processOvertimePayment'])->name('bookings.payment.overtime');

    // Marketplace routes
    Route::prefix('marketplace')->name('marketplace.')->group(function () {
        // Products
        Route::get('/products', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'index'])->name('products.index');
        Route::get('/products/api', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'apiIndex'])->name('products.api');
        Route::get('/products/create', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'create'])->name('products.create');
        Route::post('/products', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'store'])->name('products.store');
        Route::get('/products/{product}', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'show'])->name('products.show');
        Route::get('/products/{product}/edit', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'edit'])->name('products.edit');
        Route::put('/products/{product}', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'update'])->name('products.update');
        Route::delete('/products/{product}', [App\Http\Controllers\Marketplace\SuperAdmin\ProductController::class, 'destroy'])->name('products.destroy');

        // Categories
        Route::get('/categories', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'index'])->name('categories.index');
        Route::get('/categories/create', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'create'])->name('categories.create');
        Route::post('/categories', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'store'])->name('categories.store');
        Route::get('/categories/{category}', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'show'])->name('categories.show');
        Route::get('/categories/{category}/edit', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'edit'])->name('categories.edit');
        Route::put('/categories/{category}', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'update'])->name('categories.update');
        Route::delete('/categories/{category}', [App\Http\Controllers\Marketplace\SuperAdmin\CategoryController::class, 'destroy'])->name('categories.destroy');

        // Product Bundles
        Route::get('/bundles', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'index'])->name('bundles.index');
        Route::get('/bundles/create', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'create'])->name('bundles.create');
        Route::post('/bundles', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'store'])->name('bundles.store');
        Route::get('/bundles/{bundle}', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'show'])->name('bundles.show');
        Route::get('/bundles/{bundle}/edit', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'edit'])->name('bundles.edit');
        Route::put('/bundles/{bundle}', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'update'])->name('bundles.update');
        Route::delete('/bundles/{bundle}', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'destroy'])->name('bundles.destroy');
        Route::post('/bundles/{bundle}/toggle-active', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'toggleActive'])->name('bundles.toggle-active');
        Route::post('/bundles/{bundle}/toggle-featured', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'toggleFeatured'])->name('bundles.toggle-featured');
        Route::post('/bundles/{bundle}/refresh-pricing', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'refreshPricing'])->name('bundles.refresh-pricing');
        Route::post('/bundles/update-sort-order', [App\Http\Controllers\Marketplace\SuperAdmin\BundleController::class, 'updateSortOrder'])->name('bundles.update-sort-order');

        // Coupons
        Route::get('/coupons', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'index'])->name('coupons.index');
        Route::get('/coupons/create', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'create'])->name('coupons.create');
        Route::post('/coupons', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'store'])->name('coupons.store');
        Route::get('/coupons/{coupon}', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'show'])->name('coupons.show');
        Route::get('/coupons/{coupon}/edit', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'edit'])->name('coupons.edit');
        Route::put('/coupons/{coupon}', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'update'])->name('coupons.update');
        Route::delete('/coupons/{coupon}', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'destroy'])->name('coupons.destroy');
        Route::post('/coupons/{coupon}/toggle-status', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'toggleStatus'])->name('coupons.toggle-status');
        Route::post('/coupons/{coupon}/apply', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'applyCoupon'])->name('coupons.apply');
        Route::post('/coupons/validate', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'validateCoupon'])->name('coupons.validate');
        Route::get('/coupons/{orderId}/applied', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'listAppliedCoupons'])->name('coupons.applied');
        Route::post('/coupons/{orderId}/remove', [App\Http\Controllers\Marketplace\SuperAdmin\CouponController::class, 'removeAppliedCoupon'])->name('coupons.remove');

        // Orders
        Route::get('/orders', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'index'])->name('orders.index');
        Route::get('/orders/export', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'export'])->name('orders.export');
        Route::get('/orders/{order}', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'show'])->name('orders.show');
        Route::post('/orders/{order}/update-status', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::get('/orders/{order}/details', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'getOrderDetails'])->name('orders.details');
        Route::delete('/orders/{order}', [App\Http\Controllers\Marketplace\SuperAdmin\OrderController::class, 'destroy'])->name('orders.destroy');

        // Payments
        Route::get('/payments', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'index'])->name('payments.index');
        Route::get('/payments/export', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'export'])->name('payments.export');
        Route::get('/payments/{payment}', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'show'])->name('payments.show');
        Route::post('/payments/{payment}/update-status', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'updateStatus'])->name('payments.update-status');
        Route::post('/payments/{payment}/generate-transaction-id', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'generateTransactionId'])->name('payments.generate-transaction-id');
        Route::post('/payments/generate-all-transaction-ids', [App\Http\Controllers\Marketplace\SuperAdmin\PaymentController::class, 'generateAllTransactionIds'])->name('payments.generate-all-transaction-ids');

        // Settings
        Route::get('/settings', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'index'])->name('settings.index');
        Route::post('/settings/general', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'updateGeneral'])->name('settings.update-general');
        Route::post('/settings/email', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'updateEmail'])->name('settings.update-email');
        Route::post('/settings/payment', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'updatePayment'])->name('settings.update-payment');
        Route::post('/settings/shipping', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'updateShipping'])->name('settings.update-shipping');
        Route::get('/settings/payment-config/{provider}', [App\Http\Controllers\Marketplace\SuperAdmin\SettingController::class, 'getPaymentConfig'])->name('settings.payment-config');
    });

    // Edu routes
    Route::prefix('edu')->name('edu.')->group(function () {
        Route::get('lecturers/api', [App\Http\Controllers\Edu\SuperAdmin\LecturerController::class, 'apiIndex'])->name('lecturers.api');
        Route::resource('lecturers', App\Http\Controllers\Edu\SuperAdmin\LecturerController::class);

        Route::get('courses/api', [App\Http\Controllers\Edu\SuperAdmin\CourseController::class, 'apiIndex'])->name('courses.api');
        Route::resource('courses', App\Http\Controllers\Edu\SuperAdmin\CourseController::class);

        // Review routes
        Route::get('reviews/api', [App\Http\Controllers\Edu\SuperAdmin\ReviewController::class, 'apiIndex'])->name('reviews.api');
        Route::resource('reviews', App\Http\Controllers\Edu\SuperAdmin\ReviewController::class)->except(['create', 'store']);
        Route::post('reviews/{review}/toggle-published', [App\Http\Controllers\Edu\SuperAdmin\ReviewController::class, 'togglePublished'])->name('reviews.toggle-published');
        Route::post('reviews/bulk-update', [App\Http\Controllers\Edu\SuperAdmin\ReviewController::class, 'bulkUpdate'])->name('reviews.bulk-update');
    });

    Route::get('/revenue-statistics', [App\Http\Controllers\Api\RevenueController::class, 'showRevenueStatisticsPage'])->name('revenue.statistics');

    // Affiliate Marketing routes
    Route::prefix('affiliate')->name('affiliate.')->group(function () {
        // Dashboard & Analytics
        Route::get('/dashboard', [App\Http\Controllers\Affiliate\SuperAdmin\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/analytics', [App\Http\Controllers\Affiliate\SuperAdmin\AnalyticsController::class, 'index'])->name('analytics');
        Route::get('/revenue/report', [App\Http\Controllers\Affiliate\SuperAdmin\RevenueController::class, 'report'])->name('revenue.report');

        // Affiliate Management
        Route::get('/affiliates', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'index'])->name('affiliates.index');
        Route::get('/affiliates/create', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'create'])->name('affiliates.create');
        Route::post('/affiliates', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'store'])->name('affiliates.store');
        Route::get('/affiliates/{affiliate}', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'show'])->name('affiliates.show');
        Route::get('/affiliates/{affiliate}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'edit'])->name('affiliates.edit');
        Route::put('/affiliates/{affiliate}', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'update'])->name('affiliates.update');
        Route::delete('/affiliates/{affiliate}', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'destroy'])->name('affiliates.destroy');
        Route::post('/affiliates/{affiliate}/toggle-status', [App\Http\Controllers\Affiliate\SuperAdmin\AffiliateController::class, 'toggleStatus'])->name('affiliates.toggle-status');

        // Affiliate Applications
        Route::get('/applications', [App\Http\Controllers\Affiliate\SuperAdmin\ApplicationController::class, 'index'])->name('applications.index');
        Route::post('/applications/{application}/approve', [App\Http\Controllers\Affiliate\SuperAdmin\ApplicationController::class, 'approve'])->name('applications.approve');
        Route::post('/applications/{application}/reject', [App\Http\Controllers\Affiliate\SuperAdmin\ApplicationController::class, 'reject'])->name('applications.reject');

        // Affiliate Rankings
        Route::get('/rankings', [App\Http\Controllers\Affiliate\SuperAdmin\RankingController::class, 'index'])->name('rankings.index');
        Route::post('/rankings/update', [App\Http\Controllers\Affiliate\SuperAdmin\RankingController::class, 'update'])->name('rankings.update');

        // KOL Management
        Route::get('/kols', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'index'])->name('kols.index');
        Route::get('/kols/create', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'create'])->name('kols.create');
        Route::post('/kols', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'store'])->name('kols.store');
        Route::get('/kols/{kol}', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'show'])->name('kols.show');
        Route::get('/kols/{kol}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'edit'])->name('kols.edit');
        Route::put('/kols/{kol}', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'update'])->name('kols.update');
        Route::delete('/kols/{kol}', [App\Http\Controllers\Affiliate\SuperAdmin\KolController::class, 'destroy'])->name('kols.destroy');

        // KOL Contracts
        Route::get('/kol/contracts', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'index'])->name('kol.contracts.index');
        Route::get('/kol/contracts/create', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'create'])->name('kol.contracts.create');
        Route::post('/kol/contracts', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'store'])->name('kol.contracts.store');
        Route::get('/kol/contracts/{contract}', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'show'])->name('kol.contracts.show');
        Route::get('/kol/contracts/{contract}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'edit'])->name('kol.contracts.edit');
        Route::put('/kol/contracts/{contract}', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'update'])->name('kol.contracts.update');
        Route::delete('/kol/contracts/{contract}', [App\Http\Controllers\Affiliate\SuperAdmin\KolContractController::class, 'destroy'])->name('kol.contracts.destroy');

        // Campaign Management
        Route::get('/campaigns', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'index'])->name('campaigns.index');
        Route::get('/campaigns/create', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'create'])->name('campaigns.create');
        Route::post('/campaigns', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'store'])->name('campaigns.store');
        Route::get('/campaigns/{campaign}', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'show'])->name('campaigns.show');
        Route::get('/campaigns/{campaign}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'edit'])->name('campaigns.edit');
        Route::put('/campaigns/{campaign}', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'update'])->name('campaigns.update');
        Route::delete('/campaigns/{campaign}', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'destroy'])->name('campaigns.destroy');
        Route::post('/campaigns/{campaign}/toggle-status', [App\Http\Controllers\Affiliate\SuperAdmin\CampaignController::class, 'toggleStatus'])->name('campaigns.toggle-status');

        // Link Management
        Route::get('/links', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'index'])->name('links.index');
        Route::get('/links/create', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'create'])->name('links.create');
        Route::post('/links', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'store'])->name('links.store');
        Route::get('/links/{link}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'edit'])->name('links.edit');
        Route::put('/links/{link}', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'update'])->name('links.update');
        Route::delete('/links/{link}', [App\Http\Controllers\Affiliate\SuperAdmin\LinkController::class, 'destroy'])->name('links.destroy');

        // Referral Codes
        Route::get('/referral/codes', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'index'])->name('referral.codes.index');
        Route::get('/referral/codes/create', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'create'])->name('referral.codes.create');
        Route::post('/referral/codes', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'store'])->name('referral.codes.store');
        Route::get('/referral/codes/{code}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'edit'])->name('referral.codes.edit');
        Route::put('/referral/codes/{code}', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'update'])->name('referral.codes.update');
        Route::delete('/referral/codes/{code}', [App\Http\Controllers\Affiliate\SuperAdmin\ReferralCodeController::class, 'destroy'])->name('referral.codes.destroy');

        // Commission Settings
        Route::get('/commission/settings', [App\Http\Controllers\Affiliate\SuperAdmin\CommissionSettingController::class, 'index'])->name('commission.settings.index');
        Route::post('/commission/settings', [App\Http\Controllers\Affiliate\SuperAdmin\CommissionSettingController::class, 'update'])->name('commission.settings.update');

        // Commission Calculations
        Route::get('/commission/calculations', [App\Http\Controllers\Affiliate\SuperAdmin\CommissionCalculationController::class, 'index'])->name('commission.calculations.index');
        Route::post('/commission/calculations/recalculate', [App\Http\Controllers\Affiliate\SuperAdmin\CommissionCalculationController::class, 'recalculate'])->name('commission.calculations.recalculate');

        // Payment History
        Route::get('/payments/history', [App\Http\Controllers\Affiliate\SuperAdmin\PaymentHistoryController::class, 'index'])->name('payments.history');
        Route::get('/payments/history/export', [App\Http\Controllers\Affiliate\SuperAdmin\PaymentHistoryController::class, 'export'])->name('payments.history.export');

        // Withdrawal Requests
        Route::get('/withdrawals', [App\Http\Controllers\Affiliate\SuperAdmin\WithdrawalController::class, 'index'])->name('withdrawals.index');
        Route::post('/withdrawals/{withdrawal}/approve', [App\Http\Controllers\Affiliate\SuperAdmin\WithdrawalController::class, 'approve'])->name('withdrawals.approve');
        Route::post('/withdrawals/{withdrawal}/reject', [App\Http\Controllers\Affiliate\SuperAdmin\WithdrawalController::class, 'reject'])->name('withdrawals.reject');
        Route::post('/withdrawals/{withdrawal}/process', [App\Http\Controllers\Affiliate\SuperAdmin\WithdrawalController::class, 'process'])->name('withdrawals.process');

        // Reports & Analytics
        Route::get('/reports/performance', [App\Http\Controllers\Affiliate\SuperAdmin\ReportController::class, 'performance'])->name('reports.performance');
        Route::get('/reports/export', [App\Http\Controllers\Affiliate\SuperAdmin\ReportController::class, 'export'])->name('reports.export');

        // Tracking
        Route::get('/tracking/clicks', [App\Http\Controllers\Affiliate\SuperAdmin\TrackingController::class, 'clicks'])->name('tracking.clicks');
        Route::get('/tracking/conversions', [App\Http\Controllers\Affiliate\SuperAdmin\TrackingController::class, 'conversions'])->name('tracking.conversions');

        // Settings
        Route::get('/settings/general', [App\Http\Controllers\Affiliate\SuperAdmin\SettingController::class, 'general'])->name('settings.general');
        Route::post('/settings/general', [App\Http\Controllers\Affiliate\SuperAdmin\SettingController::class, 'updateGeneral'])->name('settings.general.update');

        // Email Templates
        Route::get('/email/templates', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'index'])->name('email.templates.index');
        Route::get('/email/templates/create', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'create'])->name('email.templates.create');
        Route::post('/email/templates', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'store'])->name('email.templates.store');
        Route::get('/email/templates/{template}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'edit'])->name('email.templates.edit');
        Route::put('/email/templates/{template}', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'update'])->name('email.templates.update');
        Route::delete('/email/templates/{template}', [App\Http\Controllers\Affiliate\SuperAdmin\EmailTemplateController::class, 'destroy'])->name('email.templates.destroy');

        // Policies
        Route::get('/policies', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'index'])->name('policies.index');
        Route::get('/policies/create', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'create'])->name('policies.create');
        Route::post('/policies', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'store'])->name('policies.store');
        Route::get('/policies/{policy}/edit', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'edit'])->name('policies.edit');
        Route::put('/policies/{policy}', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'update'])->name('policies.update');
        Route::delete('/policies/{policy}', [App\Http\Controllers\Affiliate\SuperAdmin\PolicyController::class, 'destroy'])->name('policies.destroy');
    });

    // Tournament management routes
    Route::resource('tournaments', App\Http\Controllers\SuperAdmin\TournamentController::class);

});

Route::prefix('business')->name('business.')->middleware(['auth', 'verified', 'check.business.admin'])->group(function () {
    // Dashboard routes
    Route::controller(App\Http\Controllers\Business\DashboardController::class)->group(function () {
        Route::get('/dashboard', 'index')->name('dashboard');
        Route::get('/dashboard/reports', 'reports')->name('dashboard.reports');
        Route::get('/dashboard/export-statistics', 'exportBusinessStatistics')->name('export-business-statistics');
    });

    // Booking management routes


    Route::get('/reviews', [App\Http\Controllers\Customer\ReviewController::class, 'index'])->name('reviews');

    // Branch routes
    Route::resource('branch', App\Http\Controllers\BranchController::class);
    Route::post('/branch/{branch}/update', [App\Http\Controllers\BranchController::class, 'update'])->name('branch.update');

    // User routes
    Route::resource('users', App\Http\Controllers\UserController::class);

    // Court routes
    Route::resource('courts', App\Http\Controllers\Business\CourtController::class);

    // Customer routes
    Route::controller(App\Http\Controllers\Business\CustomerController::class)->prefix('customers')->name('customers.')->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('/{customer}', 'show')->name('show');
    });

    // Price routes
    Route::controller(App\Http\Controllers\Business\CourtPriceController::class)->prefix('prices')->name('prices.')->group(function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::post('/bulk-save', 'bulkSave')->name('bulk-save');
        Route::get('/{price}/edit', 'edit')->name('edit');
        Route::put('/{price}', 'update')->name('update');
        Route::delete('/{price}', 'destroy')->name('destroy');
    });

    // Statistics routes
    Route::controller(App\Http\Controllers\Business\StatisticController::class)->prefix('statistics')->name('statistics.')->group(function () {
        Route::get('/overview', 'overview')->name('overview');
        Route::get('/revenue', 'revenue')->name('revenue');
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{statistic}', 'show')->name('show');
        Route::get('/{statistic}/edit', 'edit')->name('edit');
        Route::put('/{statistic}', 'update')->name('update');
        Route::delete('/{statistic}', 'destroy')->name('destroy');
    });

    // Trash routes
    Route::controller(App\Http\Controllers\Business\TrashController::class)->prefix('trash/courts')->name('trash.')->group(function () {
        Route::get('/', 'index')->name('index');
        Route::post('/{id}/restore', 'restore')->name('restore');
        Route::delete('/{id}/force-delete', 'forceDelete')->name('force-delete');
        Route::post('/restore-all', 'restoreAll')->name('restore-all');
        Route::delete('/empty', 'emptyTrash')->name('empty');
    });

    // Notification routes - handled by the unified NotificationController

    // Settings routes
    Route::prefix('settings')->name('settings.')->controller(App\Http\Controllers\Business\SettingController::class)->group(function () {
        Route::get('general', 'index')->name('general');
        Route::post('general', 'update')->name('update');
        Route::post('payment', 'updatePayment')->name('update.payment');
        Route::post('email', 'updateEmail')->name('update.email');
    });
    // Service Management Routes
    Route::get('/services', [App\Http\Controllers\Business\ServiceManagementController::class, 'index'])->name('services.index');
    Route::get('/services/create', [App\Http\Controllers\Business\ServiceManagementController::class, 'create'])->name('services.create');
    Route::post('/services', [App\Http\Controllers\Business\ServiceManagementController::class, 'store'])->name('services.store');
    Route::get('/services/{service}/manage', [App\Http\Controllers\Business\ServiceManagementController::class, 'manage'])->name('services.manage');
    Route::put('/services/{service}', [App\Http\Controllers\Business\ServiceManagementController::class, 'update'])->name('services.update');
    Route::post('/services/{service}/apply-to-all', [App\Http\Controllers\Business\ServiceManagementController::class, 'applyToAllBranches'])->name('services.apply-to-all');
    Route::delete('/services/{service}/branch/{branch}', [App\Http\Controllers\Business\ServiceManagementController::class, 'removeFromBranch'])->name('services.remove-from-branch');
    Route::post('/services/{service}/assign-to-branch', [App\Http\Controllers\Business\ServiceManagementController::class, 'assignToBranch'])->name('services.assign-to-branch');

    // Court Service Assignment Routes
    Route::get('/service-assignments', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'index'])->name('service-assignments.index');
    Route::get('/service-assignments/create', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'create'])->name('service-assignments.create');
    Route::post('/service-assignments', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'store'])->name('service-assignments.store');
    Route::get('/service-assignments/{serviceAssign}/edit', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'edit'])->name('service-assignments.edit');
    Route::put('/service-assignments/{serviceAssign}', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'update'])->name('service-assignments.update');
    Route::delete('/service-assignments/{serviceAssign}', [App\Http\Controllers\Business\CourtServiceAssignController::class, 'destroy'])->name('service-assignments.destroy');

    // Update check-in/check-out routes to use the consolidated controller
    Route::get('/bookings/checkin-checkout', [App\Http\Controllers\BookingCheckController::class, 'index'])->name('bookings.checkin-checkout');
    Route::post('/bookings/{id}/checkin', [App\Http\Controllers\BookingCheckController::class, 'checkIn'])->name('bookings.checkin');
    Route::post('/bookings/{id}/checkout', [App\Http\Controllers\BookingCheckController::class, 'checkOut'])->name('bookings.checkout');
    Route::post('/bookings/payment/overtime', [App\Http\Controllers\BookingCheckController::class, 'processOvertimePayment'])->name('bookings.payment.overtime');


    Route::get('/bookings/online/list', [App\Http\Controllers\BookingOnlineController::class, 'index'])->name('bookings.online');
    Route::get('/bookings/online/{id}', [App\Http\Controllers\BookingOnlineController::class, 'show'])->name('bookings.online.show');
    Route::post('/bookings/online/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approve'])->name('bookings.online.approve');
    Route::post('/bookings/online/reference/{reference_number}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approveByReference'])->name('bookings.online.approve.reference');
    Route::post('/bookings/online/{id}/reject', [App\Http\Controllers\BookingOnlineController::class, 'reject'])->name('bookings.online.reject');
    Route::get('/bookings/online/{id}/payment-info', [App\Http\Controllers\BookingOnlineController::class, 'getPaymentInfo'])->name('bookings.online.payment-info');
    Route::post('/payments/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approvePayment'])->name('payments.approve');


    Route::get('/booking/history', [App\Http\Controllers\BookingHistoryController::class, 'index'])->name('booking.history');
    Route::get('booking-history/export', [App\Http\Controllers\BookingHistoryController::class, 'export'])->name('booking.export');
    Route::get('booking-history/details', [App\Http\Controllers\BookingHistoryController::class, 'getBookingDetails'])->name('booking.details');


    Route::get('/bookings', [App\Http\Controllers\BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/list', [App\Http\Controllers\BookingController::class, 'index'])->name('bookings.list');
    Route::prefix('booking')->name('booking.')->group(function () {
        Route::get('/', [App\Http\Controllers\BookingController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\BookingController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\BookingController::class, 'store'])->name('store');
        Route::get('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'show'])->name('show');
        Route::get('/{referenceNumber}/edit', [App\Http\Controllers\BookingController::class, 'edit'])->name('edit');
        Route::put('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'update'])->name('update');
        Route::delete('/{referenceNumber}', [App\Http\Controllers\BookingController::class, 'destroy'])->name('destroy');
        Route::post('/{referenceNumber}/cancel', [App\Http\Controllers\BookingController::class, 'cancel'])->name('cancel');
        Route::post('/{referenceNumber}/complete', [App\Http\Controllers\BookingController::class, 'complete'])->name('complete');
        Route::post('/{referenceNumber}/confirm-payment', [App\Http\Controllers\BookingController::class, 'confirmPayment'])->name('confirm-payment');
        Route::post('/{referenceNumber}/send-confirmation-email', [App\Http\Controllers\BookingController::class, 'sendConfirmationEmail'])->name('send-confirmation-email');
        Route::post('/{referenceNumber}/status', [App\Http\Controllers\BookingController::class, 'updateStatus'])->name('status');
        Route::post('/{referenceNumber}/payment', [App\Http\Controllers\BookingController::class, 'addPayment'])->name('payment');
    });
});

Route::prefix('branch')->name('branch.')->middleware(['auth', 'verified', \App\Http\Middleware\CheckBranchAdminRole::class])->group(function () {
    // Booking Routes
    Route::get('/dashboard', [App\Http\Controllers\Branch\DashboardController::class, 'index'])->name('dashboard');

    // Review management routes
    Route::get('/reviews', [App\Http\Controllers\Customer\ReviewController::class, 'index'])->name('reviews');

    // Branch Service Management Routes
    Route::get('/services', [App\Http\Controllers\Branch\BranchServiceController::class, 'index'])->name('services.index');
    Route::get('/services/{service}', [App\Http\Controllers\Branch\BranchServiceController::class, 'show'])->name('services.show');
    Route::get('/services/{service}/edit', [App\Http\Controllers\Branch\BranchServiceController::class, 'edit'])->name('services.edit');
    Route::put('/services/{service}', [App\Http\Controllers\Branch\BranchServiceController::class, 'update'])->name('services.update');
    Route::post('/services/bulk-update-status', [App\Http\Controllers\Branch\BranchServiceController::class, 'bulkUpdateStatus'])->name('services.bulk-update-status');
    Route::get('/services/api/active', [App\Http\Controllers\Branch\BranchServiceController::class, 'getActiveServices'])->name('services.active');

    // Notifications routes - handled by the unified NotificationController

    // Branch Settings routes
    Route::get('/settings/profile', [App\Http\Controllers\Branch\ProfileController::class, 'index'])->name('settings.profile');
    Route::put('/settings/profile', [App\Http\Controllers\Branch\ProfileController::class, 'update'])->name('settings.profile.update');
    Route::put('/settings/hours', [App\Http\Controllers\Branch\ProfileController::class, 'updateHours'])->name('settings.hours.update');

    // Customer routes
    Route::get('/customers', [App\Http\Controllers\Branch\CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/search', [App\Http\Controllers\Branch\CustomerController::class, 'search'])->name('customers.search');
    Route::get('/customers/{id}', [App\Http\Controllers\Branch\CustomerController::class, 'show'])->name('customers.show');

    // Add booking reports routes
    Route::get('/reports/bookings', [App\Http\Controllers\Branch\BookingReportController::class, 'index'])->name('reports.bookings');
    Route::get('/reports/bookings/export', [App\Http\Controllers\Branch\BookingReportController::class, 'export'])->name('reports.bookings.export');
    Route::get('/reports/bookings/details', [App\Http\Controllers\Branch\BookingReportController::class, 'getBookingDetails'])->name('reports.bookings.details');

    // Add customer reports routes
    Route::get('/reports/customers', [App\Http\Controllers\Branch\CustomerReportController::class, 'index'])->name('reports.customers');
    Route::get('/reports/customers/export', [App\Http\Controllers\Branch\CustomerReportController::class, 'export'])->name('reports.customers.export');
    Route::get('/reports/customers/details', [App\Http\Controllers\Branch\CustomerReportController::class, 'getCustomerDetails'])->name('reports.customers.details');

    // Add finance reports routes
    Route::get('/finance/reports', [App\Http\Controllers\Branch\FinanceReportController::class, 'index'])->name('finance.reports');
    Route::get('/finance/reports/export', [App\Http\Controllers\Branch\FinanceReportController::class, 'export'])->name('finance.reports.export');

    // Add finance transactions routes
    Route::get('/finance/transactions', [App\Http\Controllers\Branch\PaymentController::class, 'index'])->name('finance.transactions');
    Route::get('/finance/transactions/details', [App\Http\Controllers\Branch\PaymentController::class, 'getPaymentDetails'])->name('finance.transactions.details');
    Route::get('/finance/transactions/export', [App\Http\Controllers\Branch\PaymentController::class, 'export'])->name('finance.transactions.export');

    // Add statistics routes
    Route::get('/statistics/revenue', [App\Http\Controllers\Branch\RevenueController::class, 'getRevenueStats'])->name('statistics.revenue');
    Route::get('/statistics/revenue/export', [App\Http\Controllers\Branch\RevenueController::class, 'exportRevenue'])->name('statistics.revenue.export');
    Route::get('/statistics/transactions', [App\Http\Controllers\Branch\StatisticController::class, 'getRecentTransactions'])->name('statistics.transactions');

    Route::get('/bookings/online', [App\Http\Controllers\BookingOnlineController::class, 'index'])->name('bookings.online');
    Route::get('/bookings/online/{id}', [App\Http\Controllers\BookingOnlineController::class, 'show'])->name('bookings.online.show');
    Route::post('/bookings/online/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approve'])->name('bookings.online.approve');
    Route::post('/bookings/online/reference/{reference_number}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approveByReference'])->name('bookings.online.approve.reference');
    Route::post('/bookings/online/{id}/reject', [App\Http\Controllers\BookingOnlineController::class, 'reject'])->name('bookings.online.reject');
    Route::get('/bookings/online/{id}/payment-info', [App\Http\Controllers\BookingOnlineController::class, 'getPaymentInfo'])->name('bookings.online.payment-info');
    Route::post('/payments/{id}/approve', [App\Http\Controllers\BookingOnlineController::class, 'approvePayment'])->name('payments.approve');

    Route::get('/bookings/offline', [App\Http\Controllers\Branch\BookingOfflineController::class, 'index'])->name('bookings.offline');
    Route::post('/bookings/offline', [App\Http\Controllers\Branch\BookingOfflineController::class, 'store'])->name('bookings.store.offline');
    Route::get('/bookings/offline/{id}', [App\Http\Controllers\Branch\BookingOfflineController::class, 'show'])->name('bookings.offline.show');
    Route::get('/bookings/offline/{id}/edit', [App\Http\Controllers\Branch\BookingOfflineController::class, 'edit'])->name('bookings.offline.edit');
    Route::put('/bookings/offline/{id}', [App\Http\Controllers\Branch\BookingOfflineController::class, 'update'])->name('bookings.offline.update');
    Route::post('/bookings/offline/{id}/cancel', [App\Http\Controllers\Branch\BookingOfflineController::class, 'cancel'])->name('bookings.offline.cancel');

    // Update check-in/check-out routes to use the consolidated controller

    Route::get('/bookings/checkin-checkout', [App\Http\Controllers\BookingCheckController::class, 'index'])->name('bookings.checkin-checkout');
    Route::post('/bookings/{id}/checkin', [App\Http\Controllers\BookingCheckController::class, 'checkIn'])->name('bookings.checkin');
    Route::post('/bookings/{id}/checkout', [App\Http\Controllers\BookingCheckController::class, 'checkOut'])->name('bookings.checkout');
    Route::post('/bookings/payment/overtime', [App\Http\Controllers\BookingCheckController::class, 'processOvertimePayment'])->name('bookings.payment.overtime');

    Route::get('/bookings/schedule', [App\Http\Controllers\Branch\BookingController::class, 'schedule'])->name('schedule');
    Route::get('/bookings/schedule/data', [App\Http\Controllers\Branch\BookingController::class, 'getScheduleData'])->name('schedule.data');

    Route::get('/bookings/stats', [App\Http\Controllers\Branch\BookingStatsController::class, 'getStatistics'])->name('bookings.stats');
    Route::get('/bookings/stats/refresh', [App\Http\Controllers\Branch\BookingStatsController::class, 'refreshStatistics'])->name('bookings.stats.refresh');


    Route::get('/checkin', [App\Http\Controllers\Branch\BookingCheckController::class, 'show'])
        ->name('checkin.show');
    Route::get('/checkin/{reference_number}', [App\Http\Controllers\Branch\BookingCheckController::class, 'show'])
        ->name('checkin.show');
    Route::post('/checkin/{id}', [App\Http\Controllers\Branch\BookingCheckController::class, 'checkIn'])
        ->name('checkin.check_in');
    Route::post('/checkout/{id}', [App\Http\Controllers\Branch\BookingCheckController::class, 'checkOut'])
        ->name('checkin.check_out');
    Route::post('/booking/complete/{reference_number}', [App\Http\Controllers\Branch\BookingCheckController::class, 'completeBooking'])
        ->name('booking.complete');

    Route::get('/finance/revenue', function () {
        return redirect()->route('branch.statistics.revenue');
    })->name('finance.revenue');


    Route::post('/staff', [App\Http\Controllers\BranchStaffController::class, 'store'])->name('staff.store');
    Route::put('/staff/{user}', [App\Http\Controllers\BranchStaffController::class, 'update'])->name('staff.update');
    Route::delete('/staff/{user}', [App\Http\Controllers\BranchStaffController::class, 'unassignStaff'])->name('staff.unassign');
    Route::get('/staff/get-list-staff', [App\Http\Controllers\BranchStaffController::class, 'getBusinessStaff'])->name('staff.get-business-staff');
    Route::post('/staff/assign', [App\Http\Controllers\BranchStaffController::class, 'assignStaff'])->name('staff.assign');


    Route::get('/bookings', [App\Http\Controllers\BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/list', [App\Http\Controllers\BookingController::class, 'index'])->name('bookings.list');

    Route::prefix('booking')->name('branch.bookings.')->group(function () {
        Route::get('/', [App\Http\Controllers\Branch\BookingController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Branch\BookingController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Branch\BookingController::class, 'store'])->name('store');
        Route::get('/{referenceNumber}', [App\Http\Controllers\Branch\BookingController::class, 'show'])->name('show');
        Route::get('/{referenceNumber}/edit', [App\Http\Controllers\Branch\BookingController::class, 'edit'])->name('edit');
        Route::put('/{referenceNumber}', [App\Http\Controllers\Branch\BookingController::class, 'update'])->name('update');
        Route::delete('/{referenceNumber}', [App\Http\Controllers\Branch\BookingController::class, 'destroy'])->name('destroy');
        Route::post('/{referenceNumber}/cancel', [App\Http\Controllers\Branch\BookingController::class, 'cancel'])->name('cancel');
        Route::post('/{referenceNumber}/complete', [App\Http\Controllers\Branch\BookingController::class, 'complete'])->name('complete');
        Route::post('/{referenceNumber}/confirm-payment', [App\Http\Controllers\Branch\BookingController::class, 'confirmPayment'])->name('confirm-payment');
        Route::post('/{referenceNumber}/send-confirmation-email', [App\Http\Controllers\Branch\BookingController::class, 'sendConfirmationEmail'])->name('send-confirmation-email');
        Route::post('/{referenceNumber}/status', [App\Http\Controllers\Branch\BookingController::class, 'updateStatus'])->name('status');
        Route::post('/{referenceNumber}/payment', [App\Http\Controllers\Branch\BookingController::class, 'addPayment'])->name('payment');
    });
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::prefix('user')->name('user.')->middleware(['auth'])->group(function () {
        Route::get('/bookings', [App\Http\Controllers\User\BookingController::class, 'index'])->name('bookings');
        Route::get('/bookings/{referenceNumber}', [App\Http\Controllers\User\BookingController::class, 'show'])->name('bookings.show');
        Route::post('/bookings/{referenceNumber}/cancel', [App\Http\Controllers\User\BookingController::class, 'cancel'])->name('bookings.cancel');
        Route::get('/profile', [App\Http\Controllers\User\ProfileController::class, 'show'])->name('profile');
        Route::post('/profile', [App\Http\Controllers\User\ProfileController::class, 'update'])->name('profile.update');
        Route::post('/profile/password', [App\Http\Controllers\User\ProfileController::class, 'updatePassword'])->name('profile.password');
        Route::post('/profile/avatar', [App\Http\Controllers\User\ProfileController::class, 'updateAvatar'])->name('profile.avatar');
        Route::post('/profile/use-provider-avatar', [App\Http\Controllers\User\ProfileController::class, 'useProviderAvatar'])->name('profile.use-provider-avatar');
    });
});

Route::prefix('marketplace')->name('marketplace.')->group(function () {
    Route::get('/', [App\Http\Controllers\Marketplace\Customer\HomeController::class, 'index'])->name('home');

    Route::get('/categories', [App\Http\Controllers\Marketplace\Customer\CategoryController::class, 'publicIndex'])->name('categories');

    Route::get('/search', [App\Http\Controllers\Marketplace\Customer\ListProductController::class, 'search'])->name('search');
    Route::get('/featured', [App\Http\Controllers\Marketplace\Customer\ListProductController::class, 'featured'])->name('featured');
    Route::get('/new-arrivals', [App\Http\Controllers\Marketplace\Customer\ListProductController::class, 'newArrivals'])->name('new-arrivals');
    Route::get('/special-offers', [App\Http\Controllers\Marketplace\Customer\SpecialOfferController::class, 'index'])->name('special-offers');

    Route::get('category/{slug}', [App\Http\Controllers\Marketplace\Customer\ListProductController::class, 'index'])->name('category');
    Route::get('product/{slug}', [App\Http\Controllers\Marketplace\Customer\ProductDetailController::class, 'index'])->name('product.detail');

    // Bundle routes
    Route::get('/bundles', [App\Http\Controllers\Marketplace\Customer\BundleController::class, 'index'])->name('bundles.index');
    Route::get('/bundles/featured', [App\Http\Controllers\Marketplace\Customer\BundleController::class, 'featured'])->name('bundles.featured');
    Route::get('/bundles/search', [App\Http\Controllers\Marketplace\Customer\BundleController::class, 'search'])->name('bundles.search');
    Route::get('/bundles/{slug}', [App\Http\Controllers\Marketplace\Customer\BundleController::class, 'show'])->name('bundle.detail');
    Route::get('/api/bundles/{id}', [App\Http\Controllers\Marketplace\Customer\BundleController::class, 'getBundleData'])->name('bundles.data');

    Route::get('/cart', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'index'])->name('cart');

    Route::post('/cart/add', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'add'])->name('cart.add');
    Route::put('/cart/{id}', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'update'])->name('cart.update');
    Route::delete('/cart/{id}', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'remove'])->name('cart.remove');
    Route::get('/cart/count', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'getCount'])->name('cart.count');
    Route::post('/cart/sync', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'syncFromStorage'])->name('cart.sync');
    Route::post('/cart/auto-apply-first-time-discount', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'autoApplyFirstTimeDiscount'])->name('cart.auto-apply-first-time-discount');
    Route::post('/cart/calculate-shipping', [App\Http\Controllers\Marketplace\Customer\CartController::class, 'calculateShipping'])->name('cart.calculate-shipping');

    Route::get('/wishlist', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'index'])->name('wishlist');
    Route::post('/wishlist/add', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'add'])->name('wishlist.add');
    Route::post('/wishlist/items', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'getItems'])->name('wishlist.items');
    Route::delete('/wishlist/{id}', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'remove'])->name('wishlist.remove');
    Route::get('/wishlist/count', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'getCount'])->name('wishlist.count');
    Route::post('/wishlist/sync', [App\Http\Controllers\Marketplace\Customer\WishlistController::class, 'syncFromStorage'])->name('wishlist.sync');

    // my order
    Route::get('/my-orders', [App\Http\Controllers\Marketplace\Customer\OrderController::class, 'index'])->name('my-orders.index');
    Route::get('/my-orders/{id}', [App\Http\Controllers\Marketplace\Customer\OrderController::class, 'show'])->name('my-orders.show');
    Route::post('/my-orders/{id}/cancel', [App\Http\Controllers\Marketplace\Customer\OrderController::class, 'cancel'])->name('my-orders.cancel');
    Route::get('/my-orders/{id}/invoice', [App\Http\Controllers\Marketplace\Customer\OrderController::class, 'invoice'])->name('my-orders.invoice');

    Route::post('/reviews/{reviewId}/vote', [App\Http\Controllers\Marketplace\Customer\ProductReviewVoteController::class, 'vote'])->name('reviews.vote');
    Route::get('/reviews/{reviewId}/vote-status', [App\Http\Controllers\Marketplace\Customer\ProductReviewVoteController::class, 'getVoteStatus'])->name('reviews.vote-status');
    Route::post('/products/review', [App\Http\Controllers\Marketplace\Customer\ProductReviewController::class, 'store'])->name('products.review.store');
});

// Customer Order Routes
Route::group(['prefix' => 'marketplace/orders', 'as' => 'marketplace.orders.'], function () {
    Route::get('/', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'index'])->name('index');
    Route::get('/{order}', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'show'])->name('show');
    Route::post('/checkout', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'checkout'])->name('checkout');
    Route::post('/validate-coupon', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'validateCoupon'])->name('validate-coupon');
    Route::post('/{order}/cancel', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'cancel'])->name('cancel');
    Route::post('/review', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'submitReview'])->name('review.submit');
    Route::get('/{order}/reviewable-items', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'getReviewableItems'])->name('reviewable-items');
});

// VNPay Payment Routes
Route::group(['prefix' => 'marketplace/payment/vnpay', 'as' => 'marketplace.payment.vnpay.'], function () {
    Route::post('/create', [\App\Http\Controllers\Marketplace\Payment\VNPayController::class, 'createPayment'])->name('create');
    Route::get('/return', [\App\Http\Controllers\Marketplace\Payment\VNPayController::class, 'handleReturn'])->name('return');
    Route::post('/ipn', [\App\Http\Controllers\Marketplace\Payment\VNPayController::class, 'handleIPN'])->name('ipn');
    Route::post('/refund', [\App\Http\Controllers\Marketplace\Payment\VNPayController::class, 'refund'])->name('refund');
});



// Checkout Success/Failure Routes
Route::get('/marketplace/checkout/success', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'checkoutSuccess'])->name('marketplace.checkout.success');
Route::get('/marketplace/checkout/failure', [\App\Http\Controllers\Marketplace\Customer\OrderController::class, 'checkoutFailure'])->name('marketplace.checkout.failure');

// Education routes
Route::prefix('edu')->name('edu.')->group(function () {
    Route::prefix('lecturers')->name('lecturers.')->group(function () {
        Route::get('/{id}', [App\Http\Controllers\Edu\Public\LecturerProfileController::class, 'show'])->name('profile');
        Route::get('/{id}/courses', [App\Http\Controllers\Edu\Public\LecturerProfileController::class, 'courses'])->name('courses');
        Route::get('/{id}/reviews', [App\Http\Controllers\Edu\Public\LecturerProfileController::class, 'reviews'])->name('reviews');
    });

    Route::prefix('courses')->name('courses.')->group(function () {
        Route::get('/', [App\Http\Controllers\Edu\Public\CoursesController::class, 'index'])->name('index');
        Route::get('/{course}', [App\Http\Controllers\Edu\Public\CoursesController::class, 'show'])->name('show');
    });
});


Route::get('/search', [BranchController::class, 'index'])->name('branch.search');


Route::prefix('customer')->name('customer.')->group(function () {
    Route::get('/court/{id}/available-slots', [CourtSearchController::class, 'getAvailableSlots'])->name('court.available-slots');
    Route::get('/courts/{id}', [App\Http\Controllers\Customer\CourtBookingController::class, 'show'])->name('courts.show');
    Route::get('/courts/{id}/book', [App\Http\Controllers\Customer\CourtBookingController::class, 'show'])->name('courts.book');
    Route::get('/branchs', [BranchController::class, 'index'])->name('customer.branchs.index');
    Route::get('/branch/{branch}', [BranchController::class, 'show'])->name('customer.branchs.show');
    Route::get('/branch/{id}/booking', [BranchController::class, 'booking'])->name('branch.booking');
    Route::get('/branch/{id}/bookings-by-date', [BranchController::class, 'getBookingsByDate'])->name('branch.bookings-by-date');
    Route::get('/branch/{id}/prices-by-date', [App\Http\Controllers\Business\CourtPriceController::class, 'getPricesByDate'])->name('branch.prices-by-date');

    Route::post('/payments', [App\Http\Controllers\Customer\PaymentController::class, 'processPayment'])->name('payments.process');
    Route::get('/payments/{referenceNumber}', [App\Http\Controllers\Customer\PaymentController::class, 'getPaymentStatus'])->name('payments.status');
    Route::get('/payments/confirmation/{referenceNumber}', [App\Http\Controllers\Customer\PaymentController::class, 'confirmation'])->name('payments.confirmation');
    Route::get('/payments/select/{referenceNumber}', [App\Http\Controllers\Customer\PaymentController::class, 'selectPaymentMethod'])->name('payments.select');
    Route::get('/payments/remaining/{referenceNumber}', [App\Http\Controllers\Customer\PaymentController::class, 'selectRemainingPaymentMethod'])->name('payments.remaining');
    Route::post('/payments/process-remaining', [App\Http\Controllers\Customer\PaymentController::class, 'processRemainingPayment'])->name('payments.process.remaining');
    Route::get('/payments/failed', [App\Http\Controllers\Customer\PaymentController::class, 'paymentFailed'])->name('payments.failed');

    Route::post('/payments/create-momo-url', [App\Http\Controllers\Customer\PaymentController::class, 'createMomoUrl'])->name('payments.create-momo-url');
    Route::post('/payments/momo-ipn', [App\Http\Controllers\Customer\PaymentController::class, 'momoIpn'])->name('payments.momo-ipn');
    Route::post('/payments/create-vnpay-url', [App\Http\Controllers\Customer\PaymentController::class, 'createVnpayUrl'])->name('payments.create-vnpay-url');
    Route::post('/payments/vnpay-ipn', [App\Http\Controllers\Customer\PaymentController::class, 'vnpayIpn'])->name('payments.vnpay-ipn');
    Route::get('/booking/verify', [App\Http\Controllers\Customer\PaymentController::class, 'verifyBookingForm'])->name('bookings.verify-form');
    Route::post('/booking/verify', [App\Http\Controllers\Customer\PaymentController::class, 'verifyBooking'])->name('bookings.verify');
    Route::get('/booking/details/{referenceNumber}', [App\Http\Controllers\Customer\PaymentController::class, 'bookingDetails'])->name('bookings.details');
    Route::post('/bookings', [App\Http\Controllers\Customer\BookingController::class, 'store'])->name('bookings.store');
    Route::get('/bookings/reference/{referenceNumber}', [App\Http\Controllers\Customer\BookingController::class, 'getByReference'])->name('bookings.reference');
    Route::post('/bookings/cancel/{referenceNumber}', [App\Http\Controllers\Customer\BookingController::class, 'cancel'])->name('bookings.cancel');

    Route::get('/invoice/{referenceNumber}', [App\Http\Controllers\Customer\InvoiceController::class, 'viewInvoice'])->name('invoice.view');
    Route::get('/review/{referenceNumber}', [App\Http\Controllers\Customer\ReviewController::class, 'index'])->name('review.index');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::prefix('user')->name('user.')->middleware(['auth'])->group(function () {
        Route::get('/bookings', [App\Http\Controllers\User\BookingController::class, 'index'])->name('bookings');
        Route::get('/bookings/{referenceNumber}', [App\Http\Controllers\User\BookingController::class, 'show'])->name('bookings.show');
        Route::post('/bookings/{referenceNumber}/cancel', [App\Http\Controllers\User\BookingController::class, 'cancel'])->name('bookings.cancel');

        // Profile routes
        Route::get('/profile', [App\Http\Controllers\User\ProfileController::class, 'show'])->name('profile');
        Route::post('/profile', [App\Http\Controllers\User\ProfileController::class, 'update'])->name('profile.update');
        Route::post('/profile/password', [App\Http\Controllers\User\ProfileController::class, 'updatePassword'])->name('profile.password');
        Route::post('/profile/avatar', [App\Http\Controllers\User\ProfileController::class, 'updateAvatar'])->name('profile.avatar');
        Route::post('/profile/use-provider-avatar', [App\Http\Controllers\User\ProfileController::class, 'useProviderAvatar'])->name('profile.use-provider-avatar');

        // New routes for separate pages
        Route::get('/password', [App\Http\Controllers\User\PasswordController::class, 'show'])->name('password');
        Route::get('/favorites', [App\Http\Controllers\User\FavoritesController::class, 'show'])->name('favorites');
        Route::get('/booking-history', [App\Http\Controllers\User\BookingHistoryController::class, 'index'])->name('booking.history');
    });
});


Route::get('/search', [App\Http\Controllers\Customer\BranchController::class, 'index'])->name('branch.search');



Route::prefix('api/address')->group(function () {
    Route::get('/provinces', [App\Http\Controllers\AddressController::class, 'provinces'])->name('api.address.provinces');
    Route::get('/districts/{provinceId}', [App\Http\Controllers\AddressController::class, 'districts'])->name('api.address.districts');
    Route::get('/wards/{districtId}', [App\Http\Controllers\AddressController::class, 'wards'])->name('api.address.wards');
});


Route::get('/payment-methods', [App\Http\Controllers\Customer\PaymentController::class, 'getPaymentMethods'])->name('payment-methods');

Route::get('/terms-of-service', function () {
    $termsSettings = \App\Models\SystemSetting::getByGroup('terms');

    $terms = [];
    foreach ($termsSettings as $setting) {
        $terms[$setting->key_setting] = $setting->setting_value;
    }

    return Inertia::render('TermsOfService', [
        'terms' => $terms
    ]);
})->name('terms-of-service');

Route::get('/about', function () {
    return Inertia::render('About');
})->name('about');

Route::get('/tournaments', [App\Http\Controllers\Customer\TournamentController::class, 'index'])->name('tournaments');
Route::get('/tournaments/{tournament}', [App\Http\Controllers\Customer\TournamentController::class, 'show'])->name('tournaments.show');

Route::get('/under-development', function () {
    return Inertia::render('UnderDevelopment');
})->name('under-development');

Route::middleware('guest')->group(function () {
    Route::get('/api/auth/{provider}', [SocialLoginController::class, 'redirectToProvider']);
    Route::get('/api/auth/{provider}/callback', [SocialLoginController::class, 'handleProviderCallback']);

    Route::post('/api/auth/facebook/sdk', [App\Http\Controllers\Auth\FacebookAuthController::class, 'authenticate'])
        ->name('auth.facebook.sdk');
});



Route::prefix('api')->group(function () {
    Route::post('/users/create', [App\Http\Controllers\UserController::class, 'store'])->name('api.users.create');
    Route::get('/users/business', [App\Http\Controllers\UserController::class, 'getUsersByBusiness'])->name('api.users.by-business');
    Route::get('/booking/timeline/{referenceNumber}', [App\Http\Controllers\Customer\BookingController::class, 'timeline'])->name('bookings.timeline');

    Route::get('/likelists', [App\Http\Controllers\Customer\LikelistController::class, 'index'])->name('api.likelists.index');
    Route::post('/likelists', [App\Http\Controllers\Customer\LikelistController::class, 'store'])->name('api.likelists.store');
    Route::get('/likelists/{id}', [App\Http\Controllers\Customer\LikelistController::class, 'show'])->name('api.likelists.show');
    Route::put('/likelists/{id}', [App\Http\Controllers\Customer\LikelistController::class, 'update'])->name('api.likelists.update');
    Route::delete('/likelists/{id}', [App\Http\Controllers\Customer\LikelistController::class, 'destroy'])->name('api.likelists.destroy');
    Route::get('/likelists/{id}/check', [App\Http\Controllers\Customer\LikelistController::class, 'check'])->name('api.likelists.check');

    // Booking API routes
    Route::get('/bookings', [App\Http\Controllers\User\BookingController::class, 'getUserBookings'])->name('api.bookings.index');
    Route::get('/bookings/{referenceNumber}', [App\Http\Controllers\User\BookingController::class, 'show'])->name('api.bookings.show');

    // Branch API routes
    Route::get('/branch/{branch}', [App\Http\Controllers\Customer\BranchController::class, 'show'])->name('api.branch.show');
    Route::get('/price/{branch}', [App\Http\Controllers\Customer\BranchController::class, 'getPrice'])->name('api.branch.price');

    // Tournament API routes
    Route::get('/tournaments', [App\Http\Controllers\Customer\TournamentController::class, 'apiIndex'])->name('api.tournaments.index');

    // Review API routes
    Route::post('/reviews', [App\Http\Controllers\Customer\ReviewController::class, 'store'])->name('api.reviews.store');
    Route::patch('/reviews/{id}', [App\Http\Controllers\Customer\ReviewController::class, 'update'])->name('api.reviews.update');
    Route::delete('/reviews/{id}', [App\Http\Controllers\Customer\ReviewController::class, 'destroy'])->name('api.reviews.destroy');
    Route::get('/reviews', [App\Http\Controllers\Customer\ReviewController::class, 'getList'])->name('api.reviews.index');
    Route::post('/reviews/{id}/status', [App\Http\Controllers\Customer\ReviewController::class, 'approveReview'])->name('api.reviews.update.status');

    Route::get('/list-bookings/checkin-checkout', [App\Http\Controllers\BookingCheckController::class, 'index'])->name('api.bookings.checkin-checkout');
    Route::post('/bookings/{id}/checkin', [App\Http\Controllers\BookingCheckController::class, 'checkIn'])->name('api.bookings.checkin');
    Route::post('/bookings/{id}/checkout', [App\Http\Controllers\BookingCheckController::class, 'checkOut'])->name('api.bookings.checkout');
    Route::post('/bookings/payment/overtime', [App\Http\Controllers\BookingCheckController::class, 'processOvertimePayment'])->name('api.bookings.payment.overtime');


    Route::get('/cron-jobs', [App\Http\Controllers\Api\CronJobController::class, 'index']);
    Route::post('/cron-jobs', [App\Http\Controllers\Api\CronJobController::class, 'store']);
    Route::get('/cron-jobs/{id}', [App\Http\Controllers\Api\CronJobController::class, 'show']);
    Route::put('/cron-jobs/{id}', [App\Http\Controllers\Api\CronJobController::class, 'update']);
    Route::delete('/cron-jobs/{id}', [App\Http\Controllers\Api\CronJobController::class, 'destroy']);
    Route::post('/cron-jobs/{id}/run', [App\Http\Controllers\Api\CronJobController::class, 'runNow']);
    Route::get('/cron-jobs/{id}/logs', [App\Http\Controllers\Api\CronJobController::class, 'getLogs']);
    Route::get('/cron-jobs/status/overview', [App\Http\Controllers\Api\CronJobController::class, 'getOverview']);

    Route::prefix('revenue-statistics')->group(function () {
        Route::get('overview', [App\Http\Controllers\Api\RevenueController::class, 'getRevenueOverview']);
        Route::get('summary', [App\Http\Controllers\Api\RevenueController::class, 'getRevenueSummary']);

        Route::get('trends', [App\Http\Controllers\Api\RevenueController::class, 'getRevenueTrends']);
        Route::get('comparison', [App\Http\Controllers\Api\RevenueController::class, 'getRevenueComparison']);
        Route::get('hourly-analysis', [App\Http\Controllers\Api\RevenueController::class, 'getHourlyAnalysis']);
        Route::get('daily-analysis', [App\Http\Controllers\Api\RevenueController::class, 'getDailyAnalysis']);
        Route::get('monthly-analysis', [App\Http\Controllers\Api\RevenueController::class, 'getMonthlyAnalysis']);

        Route::get('top-businesses', [App\Http\Controllers\Api\RevenueController::class, 'getTopBusinesses']);
        Route::get('top-branches', [App\Http\Controllers\Api\RevenueController::class, 'getTopBranches']);
        Route::get('top-courts', [App\Http\Controllers\Api\RevenueController::class, 'getTopCourts']);

        Route::get('detailed-report', [App\Http\Controllers\Api\RevenueController::class, 'getDetailedReport']);
        Route::post('export-report', [App\Http\Controllers\Api\RevenueController::class, 'exportRevenueReport']);

        Route::get('real-time', [App\Http\Controllers\Api\RevenueController::class, 'getRealTimeRevenue']);
        Route::get('live-bookings', [App\Http\Controllers\Api\RevenueController::class, 'getLiveBookings']);
    });
});

require __DIR__ . '/auth.php';
require __DIR__ . '/modules/marketplace/marketplace.php';
require __DIR__ . '/modules/booking/branch.php';
require __DIR__ . '/modules/booking/business.php';
require __DIR__ . '/api.php';