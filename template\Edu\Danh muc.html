<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - PickleAcademy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #03786C;
            --secondary-color: #E5B63D;
            --tertiary-color: #05A493;
            --white: #ffffff;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #333333;
            --text-gray: #666666;
            --border-color: #dddddd;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            width: 1440px;
            margin: 0 auto;
            overflow-x: hidden;
        }

        /* Header Styles */
        header {
            background-color: var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            width: 1440px;
            z-index: 1000;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 50px;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            height: 40px;
            margin-right: 10px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo-text span {
            color: var(--secondary-color);
        }

        .nav-menu {
            display: flex;
            list-style: none;
        }

        .nav-menu li {
            margin: 0 15px;
        }

        .nav-menu a {
            text-decoration: none;
            color: var(--dark-gray);
            font-weight: 500;
            transition: color 0.3s;
            font-size: 16px;
            padding: 8px 0;
            position: relative;
        }

        .nav-menu a:hover, .nav-menu a.active {
            color: var(--primary-color);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s;
        }

        .nav-menu a:hover::after, .nav-menu a.active::after {
            width: 100%;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 15px;
            text-decoration: none;
        }

        .btn-outline {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            margin-right: 10px;
        }

        .btn-outline:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
        }

        .btn-primary:hover {
            background-color: var(--tertiary-color);
        }

        /* Main Content Styles */
        .main-content {
            padding: 120px 50px 50px;
        }

        .page-title {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
        }

        .page-title h1 {
            font-size: 36px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .page-title p {
            font-size: 18px;
            color: var(--text-gray);
            max-width: 700px;
            text-align: center;
            line-height: 1.6;
        }

        /* Search and Filter Styles */
        .search-filter-container {
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 30px;
        }

        .search-bar {
            display: flex;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px 0 0 5px;
            font-size: 16px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--tertiary-color);
        }

        .search-btn {
            padding: 12px 25px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .search-btn:hover {
            background-color: var(--tertiary-color);
        }

        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        .filter-select {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 15px;
            background-color: var(--white);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 15px center;
            padding-right: 30px;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--tertiary-color);
        }

        .filter-price {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .price-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 15px;
        }

        .price-input:focus {
            outline: none;
            border-color: var(--tertiary-color);
        }

        .reset-filters {
            padding: 10px 20px;
            background-color: transparent;
            color: var(--tertiary-color);
            border: 1px solid var(--tertiary-color);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .reset-filters:hover {
            background-color: var(--tertiary-color);
            color: var(--white);
        }

        /* Category Tabs */
        .category-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 25px;
        }

        .category-tab {
            padding: 10px 20px;
            background-color: var(--white);
            border: 1px solid var(--border-color);
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .category-tab:hover {
            border-color: var(--tertiary-color);
            color: var(--tertiary-color);
        }

        .category-tab.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        /* Results Info */
        .results-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-count {
            font-size: 16px;
            color: var(--text-gray);
        }

        .results-count span {
            font-weight: 600;
            color: var(--primary-color);
        }

        .sort-by {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sort-label {
            font-size: 16px;
            color: var(--text-gray);
        }

        .sort-select {
            padding: 8px 30px 8px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 15px;
            background-color: var(--white);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
        }

        .sort-select:focus {
            outline: none;
            border-color: var(--tertiary-color);
        }

        /* Course Cards */
        .courses-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .course-card {
            background-color: var(--white);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .course-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .course-card:hover .course-image img {
            transform: scale(1.1);
        }

        .course-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: var(--secondary-color);
            color: var(--dark-gray);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .course-content {
            padding: 25px;
        }

        .course-level {
            color: var(--tertiary-color);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            display: block;
        }

        .course-title {
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: 600;
            color: var(--dark-gray);
            line-height: 1.4;
        }

        .course-details {
            display: flex;
            margin-bottom: 15px;
        }

        .course-detail {
            display: flex;
            align-items: center;
            margin-right: 20px;
            color: var(--text-gray);
            font-size: 14px;
        }

        .course-detail i {
            margin-right: 6px;
            color: var(--tertiary-color);
        }

        .course-instructor {
            display: flex;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--medium-gray);
        }

        .instructor-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
        }

        .instructor-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .instructor-name {
            font-weight: 500;
            color: var(--dark-gray);
        }

        .course-price {
            margin-left: auto;
            font-weight: 700;
            color: var(--primary-color);
            font-size: 18px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 50px;
        }

        .pagination-item {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            border-radius: 5px;
        }

        .pagination-item:hover {
            border-color: var(--tertiary-color);
            color: var(--tertiary-color);
        }

        .pagination-item.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .pagination-arrow {
            color: var(--text-gray);
        }

        .pagination-arrow:hover {
            color: var(--primary-color);
        }

        /* No Results */
        .no-results {
            text-align: center;
            padding: 50px 0;
        }

        .no-results-icon {
            font-size: 50px;
            color: var(--medium-gray);
            margin-bottom: a:hover20px;
        }

        .no-results-text {
            font-size: 20px;
            color: var(--text-gray);
            margin-bottom: 20px;
        }

        /* Footer */
        footer {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 60px 50px 20px;
        }

        .footer-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }

        .footer-column {
            flex: 1;
            padding-right: 30px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .footer-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .footer-logo-text {
            font-size: 22px;
            font-weight: 700;
            color: var(--white);
        }

        .footer-logo-text span {
            color: var(--secondary-color);
        }

        .footer-about {
            font-size: 14px;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .footer-social {
            display: flex;
            gap: 10px;
        }

        .footer-social-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            transition: all 0.3s;
        }

        .footer-social-icon:hover {
            background-color: var(--secondary-color);
            color: var(--dark-gray);
        }

        .footer-title {
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 600;
            color: var(--white);
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            text-decoration: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--secondary-color);
        }

        .footer-form input {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: none;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .footer-form input:focus {
            outline: none;
        }

        .footer-form button {
            width: 100%;
            padding: 12px;
            border-radius: 5px;
            background-color: var(--secondary-color);
            color: var(--dark-gray);
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.3s;
        }

        .footer-form button:hover {
            background-color: #d3a736;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            text-align: center;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Responsive Styles */
        @media screen and (max-width: 1440px) {
            body {
                width: 100%;
            }

            header {
                width: 100%;
            }
        }

        @media screen and (max-width: 1200px) {
            .courses-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (max-width: 992px) {
            .header-container {
                padding: 15px 20px;
            }

            .main-content {
                padding: 100px 20px 40px;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .filter-group {
                width: 100%;
            }

            .footer-container {
                flex-wrap: wrap;
            }

            .footer-column {
                flex: 1 0 50%;
                margin-bottom: 30px;
            }
        }

        @media screen and (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .courses-container {
                grid-template-columns: 1fr;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .page-title p {
                font-size: 16px;
            }

            .search-bar {
                flex-direction: column;
            }

            .search-input {
                border-radius: 5px;
                margin-bottom: 10px;
            }

            .search-btn {
                border-radius: 5px;
                width: 100%;
            }
        }

        @media screen and (max-width: 576px) {
            .filter-price {
                flex-direction: column;
                gap: 10px;
            }

            .results-info {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .footer-column {
                flex: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <a href="index.html" class="logo">
                <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgPGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDUiIHN0cm9rZT0iIzAzNzg2QyIgc3Ryb2tlLXdpZHRoPSI2IiBmaWxsPSJ0cmFuc3BhcmVudCIgLz4KICA8cmVjdCB4PSIyNSIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0U1QjYzRCIgcng9IjUiIHJ5PSI1IiAvPgogIDxjaXJjbGUgY3g9IjM1IiBjeT0iNTAiIHI9IjgiIGZpbGw9IiMwNUE0OTMiIC8+CiAgPGNpcmNsZSBjeD0iNjUiIGN5PSI1MCIgcj0iOCIgZmlsbD0iIzA1QTQ5MyIgLz4KPC9zdmc+" alt="PickleAcademy Logo">
                <div class="logo-text">Pickle<span>Academy</span></div>
            </a>

            <ul class="nav-menu">
                <li><a href="index.html">Trang chủ</a></li>
                <li><a href="#" class="active">Khóa học</a></li>
                <li><a href="#">Huấn luyện viên</a></li>
                <li><a href="#">Lịch học</a></li>
                <li><a href="#">Học liệu</a></li>
                <li><a href="#">Liên hệ</a></li>
            </ul>

            <div class="auth-buttons">
                <a href="#" class="btn btn-outline">Đăng nhập</a>
                <a href="#" class="btn btn-primary">Đăng ký</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      
        <!-- Search and Filter Section -->
        <div class="search-filter-container">
            <form class="search-bar">
                <input type="text" class="search-input" placeholder="Tìm kiếm khóa học..." id="search-input">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
            </form>

            <div class="filters">
                <div class="filter-group">
                    <label for="level-filter" class="filter-label">Cấp độ</label>
                    <select id="level-filter" class="filter-select">
                        <option value="">Tất cả cấp độ</option>
                        <option value="beginner">Người mới bắt đầu</option>
                        <option value="intermediate">Trung cấp</option>
                        <option value="advanced">Nâng cao</option>
                        <option value="professional">Chuyên nghiệp</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="instructor-filter" class="filter-label">Huấn luyện viên</label>
                    <select id="instructor-filter" class="filter-select">
                        <option value="">Tất cả huấn luyện viên</option>
                        <option value="nguyen-a">Nguyễn Văn A</option>
                        <option value="tran-b">Trần Thị B</option>
                        <option value="le-c">Lê Văn C</option>
                        <option value="pham-d">Phạm Thị D</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Giá (VNĐ)</label>
                    <div class="filter-price">
                        <input type="number" class="price-input" placeholder="Tối thiểu" id="min-price">
                        <input type="number" class="price-input" placeholder="Tối đa" id="max-price">
                    </div>
                </div>

                <div class="filter-group" style="display: flex; align-items: flex-end;">
                    <button type="button" class="reset-filters" id="reset-filters">
                        <i class="fas fa-redo-alt"></i> Đặt lại bộ lọc
                    </button>
                </div>
            </div>

            <div class="category-tabs">
                <div class="category-tab active" data-category="all">Tất cả khóa học</div>
                <div class="category-tab" data-category="basic">Kỹ thuật cơ bản</div>
                <div class="category-tab" data-category="advanced">Kỹ thuật nâng cao</div>
                <div class="category-tab" data-category="strategy">Chiến thuật</div>
                <div class="category-tab" data-category="fitness">Thể lực</div>
                <div class="category-tab" data-category="mental">Tâm lý thi đấu</div>
            </div>
        </div>

        <!-- Results Info -->
        <div class="results-info">
            <div class="results-count">Hiển thị <span>15</span> khóa học từ tổng số <span>24</span></div>
            <div class="sort-by">
                <span class="sort-label">Sắp xếp theo:</span>
                <select class="sort-select" id="sort-select">
                    <option value="popular">Phổ biến nhất</option>
                    <option value="newest">Mới nhất</option>
                    <option value="price-asc">Giá tăng dần</option>
                    <option value="price-desc">Giá giảm dần</option>
                </select>
            </div>
        </div>

        <!-- Course Cards -->
        <div class="courses-container" id="courses-container">
            <!-- Course 1 -->
            <div class="course-card" data-category="basic" data-level="beginner" data-instructor="nguyen-a" data-price="790000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Pickleball dành cho người mới bắt đầu">
                    <div class="course-badge">Bán chạy</div>
                </div>
                <div class="course-content">
                    <span class="course-level">Người mới bắt đầu</span>
                    <h3 class="course-title">Pickleball dành cho người mới bắt đầu</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 15 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 5 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Nguyễn Văn A">
                        </div>
                        <span class="instructor-name">Nguyễn Văn A</span>
                        <span class="course-price">790.000đ</span>
                    </div>
                </div>
            </div>

            <!-- Course 2 -->
            <div class="course-card" data-category="advanced" data-level="intermediate" data-instructor="tran-b" data-price="890000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Kỹ thuật đánh giao bóng nâng cao">
                    <div class="course-badge">Mới</div>
                </div>
                <div class="course-content">
                    <span class="course-level">Trung cấp</span>
                    <h3 class="course-title">Kỹ thuật đánh giao bóng nâng cao</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 12 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 4 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Trần Thị B">
                        </div>
                        <span class="instructor-name">Trần Thị B</span>
                        <span class="course-price">890.000đ</span>
                    </div>
                </div>
            </div>

            <!-- Course 3 -->
            <div class="course-card" data-category="strategy" data-level="advanced" data-instructor="le-c" data-price="1290000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Chiến thuật thi đấu chuyên nghiệp">
                </div>
                <div class="course-content">
                    <span class="course-level">Nâng cao</span>
                    <h3 class="course-title">Chiến thuật thi đấu chuyên nghiệp</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 20 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 8 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/men/62.jpg" alt="Lê Văn C">
                        </div>
                        <span class="instructor-name">Lê Văn C</span>
                        <span class="course-price">1.290.000đ</span>
                    </div>
                </div>
            </div>

            <!-- Course 4 -->
            <div class="course-card" data-category="fitness" data-level="beginner" data-instructor="pham-d" data-price="690000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Luyện tập thể lực cho Pickleball">
                </div>
                <div class="course-content">
                    <span class="course-level">Người mới bắt đầu</span>
                    <h3 class="course-title">Luyện tập thể lực cho Pickleball</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 10 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 3 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Phạm Thị D">
                        </div>
                        <span class="instructor-name">Phạm Thị D</span>
                        <span class="course-price">690.000đ</span>
                    </div>
                </div>
            </div>

            <!-- Course 5 -->
            <div class="course-card" data-category="mental" data-level="intermediate" data-instructor="nguyen-a" data-price="890000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Tâm lý thi đấu Pickleball">
                </div>
                <div class="course-content">
                    <span class="course-level">Trung cấp</span>
                    <h3 class="course-title">Tâm lý thi đấu Pickleball</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 8 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 4 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Nguyễn Văn A">
                        </div>
                        <span class="instructor-name">Nguyễn Văn A</span>
                        <span class="course-price">890.000đ</span>
                    </div>
                </div>
            </div>

            <!-- Course 6 -->
            <div class="course-card" data-category="basic" data-level="beginner" data-instructor="tran-b" data-price="790000">
                <div class="course-image">
                    <img src="https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Luật chơi và quy tắc Pickleball">
                </div>
                <div class="course-content">
                    <span class="course-level">Người mới bắt đầu</span>
                    <h3 class="course-title">Luật chơi và quy tắc Pickleball</h3>
                    <div class="course-details">
                        <span class="course-detail">
                            <i class="fas fa-video"></i> 6 bài học
                        </span>
                        <span class="course-detail">
                            <i class="fas fa-clock"></i> 2 giờ
                        </span>
                    </div>
                    <div class="course-instructor">
                        <div class="instructor-avatar">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Trần Thị B">
                        </div>
                        <span class="instructor-name">Trần Thị B</span>
                        <span class="course-price">790.000đ</span>
                    </div>
                </div>
            </div>

            <!-- More courses would be added here in a real implementation -->
        </div>

        <!-- No Results Template (hidden by default) -->
        <div class="no-results" style="display: none;" id="no-results">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <div class="no-results-text">Không tìm thấy khóa học phù hợp với tiêu chí tìm kiếm</div>
            <button class="btn btn-primary" id="clear-filters">Xóa bộ lọc</button>
        </div>

        <!-- Pagination -->
        <div class="pagination">
            <div class="pagination-item pagination-arrow" disabled>
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item">3</div>
            <div class="pagination-item pagination-arrow">
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-container">
            <div class="footer-column">
                <div class="footer-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+CiAgPGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDUiIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSI2IiBmaWxsPSJ0cmFuc3BhcmVudCIgLz4KICA8cmVjdCB4PSIyNSIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0U1QjYzRCIgcng9IjUiIHJ5PSI1IiAvPgogIDxjaXJjbGUgY3g9IjM1IiBjeT0iNTAiIHI9IjgiIGZpbGw9IiMwNUE0OTMiIC8+CiAgPGNpcmNsZSBjeD0iNjUiIGN5PSI1MCIgcj0iOCIgZmlsbD0iIzA1QTQ5MyIgLz4KPC9zdmc+" alt="PickleAcademy Logo">
                    <div class="footer-logo-text">Pickle<span>Academy</span></div>
                </div>
                <p class="footer-about">PickleAcademy là nền tảng học tập và huấn luyện Pickleball hàng đầu, cung cấp các khóa học chất lượng cao từ các huấn luyện viên chuyên nghiệp.</p>
                <div class="footer-social">
                    <a href="#" class="footer-social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="footer-social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="footer-social-icon"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="footer-social-icon"><i class="fab fa-youtube"></i></a>
                </div>
            </div>

            <div class="footer-column">
                <h3 class="footer-title">Khóa học</h3>
                <ul class="footer-links">
                    <li><a href="#">Pickleball cho người mới</a></li>
                    <li><a href="#">Kỹ thuật đánh giao bóng</a></li>
                    <li><a href="#">Chiến thuật thi đấu</a></li>
                    <li><a href="#">Luyện tập thể lực</a></li>
                    <li><a href="#">Phân tích video</a></li>
                </ul>
            </div>

            <div class="footer-column">
                <h3 class="footer-title">Liên kết hữu ích</h3>
                <ul class="footer-links">
                    <li><a href="#">Về chúng tôi</a></li>
                    <li><a href="#">Đội ngũ huấn luyện viên</a></li>
                    <li><a href="#">Blog</a></li>
                    <li><a href="#">Câu hỏi thường gặp</a></li>
                    <li><a href="#">Hỗ trợ</a></li>
                </ul>
            </div>

            <div class="footer-column">
                <h3 class="footer-title">Đăng ký nhận tin</h3>
                <p class="footer-about">Đăng ký nhận thông báo về khóa học mới và mẹo chơi Pickleball hàng tuần.</p>
                <form class="footer-form">
                    <input type="email" placeholder="Email của bạn">
                    <button type="submit">Đăng ký</button>
                </form>
            </div>
        </div>

        <div class="footer-bottom">
            <p>© 2025 PickleAcademy. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter variables
            let activeCategory = 'all';
            let searchTerm = '';

            // Elements
            const categoryTabs = document.querySelectorAll('.category-tab');
            const searchInput = document.getElementById('search-input');
            const searchForm = document.querySelector('.search-bar');
            const levelFilter = document.getElementById('level-filter');
            const instructorFilter = document.getElementById('instructor-filter');
            const minPriceFilter = document.getElementById('min-price');
            const maxPriceFilter = document.getElementById('max-price');
            const resetFiltersBtn = document.getElementById('reset-filters');
            const clearFiltersBtn = document.getElementById('clear-filters');
            const sortSelect = document.getElementById('sort-select');
            const coursesContainer = document.getElementById('courses-container');
            const noResults = document.getElementById('no-results');
            const resultsCount = document.querySelector('.results-count span:first-child');
            const totalCount = document.querySelector('.results-count span:last-child');

            // Set total count
            const totalCourses = document.querySelectorAll('.course-card').length;
            totalCount.textContent = totalCourses;

            // Category tab click handler
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    categoryTabs.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Update active category
                    activeCategory = this.getAttribute('data-category');

                    // Apply filters
                    applyFilters();
                });
            });

            // Search form submit handler
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                searchTerm = searchInput.value.toLowerCase().trim();
                applyFilters();
            });

            // Filter change handlers
            levelFilter.addEventListener('change', applyFilters);
            instructorFilter.addEventListener('change', applyFilters);
            minPriceFilter.addEventListener('input', applyFilters);
            maxPriceFilter.addEventListener('input', applyFilters);
            sortSelect.addEventListener('change', applyFilters);

            // Reset filters button
            resetFiltersBtn.addEventListener('click', function() {
                // Reset category
                categoryTabs.forEach(tab => {
                    tab.classList.remove('active');
                    if(tab.getAttribute('data-category') === 'all') {
                        tab.classList.add('active');
                    }
                });
                activeCategory = 'all';

                // Reset search and filters
                searchInput.value = '';
                searchTerm = '';
                levelFilter.value = '';
                instructorFilter.value = '';
                minPriceFilter.value = '';
                maxPriceFilter.value = '';
                sortSelect.value = 'popular';

                // Apply filters
                applyFilters();
            });

            // Clear filters button (from no results)
            clearFiltersBtn.addEventListener('click', function() {
                resetFiltersBtn.click();
            });

            // Apply all filters function
            function applyFilters() {
                const courses = document.querySelectorAll('.course-card');
                let visibleCount = 0;

                courses.forEach(course => {
                    // Get course data attributes
                    const category = course.getAttribute('data-category');
                    const level = course.getAttribute('data-level');
                    const instructor = course.getAttribute('data-instructor');
                    const price = parseFloat(course.getAttribute('data-price'));
                    const title = course.querySelector('.course-title').textContent.toLowerCase();

                    // Category filter
                    const passCategory = activeCategory === 'all' || category === activeCategory;

                    // Search filter
                    const passSearch = searchTerm === '' || title.includes(searchTerm);

                    // Level filter
                    const passLevel = levelFilter.value === '' || level === levelFilter.value;

                    // Instructor filter
                    const passInstructor = instructorFilter.value === '' || instructor === instructorFilter.value;

                    // Price range filter
                    const minPrice = minPriceFilter.value ? parseFloat(minPriceFilter.value) : 0;
                    const maxPrice = maxPriceFilter.value ? parseFloat(maxPriceFilter.value) : Infinity;
                    const passPrice = price >= minPrice && price <= maxPrice;

                    // Show or hide course
                    if (passCategory && passSearch && passLevel && passInstructor && passPrice) {
                        course.style.display = 'block';
                        visibleCount++;
                    } else {
                        course.style.display = 'none';
                    }
                });

                // Update visible count
                resultsCount.textContent = visibleCount;

                // Show/hide no results message
                if (visibleCount === 0) {
                    noResults.style.display = 'block';
                    document.querySelector('.pagination').style.display = 'none';
                } else {
                    noResults.style.display = 'none';
                    document.querySelector('.pagination').style.display = 'flex';
                }

                // Apply sorting
                applySorting(sortSelect.value);
            }

            // Apply sorting function
            function applySorting(sortBy) {
                const
